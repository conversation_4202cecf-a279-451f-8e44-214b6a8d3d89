#!/bin/sh

# 案件信息修改，数据同步
# shellcheck disable=SC2046
kill -9 $(ps -ef|grep sync_bpw_case_info.py |grep -v grep|awk '{print $2}')
nohup python3 -u sync_bpw_case_info.py "${1}" >> /dev/null 2>&1 &
# 案件日志修改，数据同步
kill -9 $(ps -ef|grep sync_bpw_case_log.py |grep -v grep|awk '{print $2}')
nohup python3 -u sync_bpw_case_log.py "${1}" >> /dev/null 2>&1 &
# 案件催记修改，数据同步
kill -9 $(ps -ef|grep sync_bpw_case_operation.py |grep -v grep|awk '{print $2}')
nohup python3 -u sync_bpw_case_operation.py "${1}" >> /dev/null 2>&1 &
# 案件催记冷数据修改，数据同步
kill -9 $(ps -ef|grep sync_bpw_case_operation_use_less.py |grep -v grep|awk '{print $2}')
nohup python3 -u sync_bpw_case_operation_use_less.py "${1}" >> /dev/null 2>&1 &
# 其他修改，数据同步
kill -9 $(ps -ef|grep sync_bpw_other.py |grep -v grep|awk '{print $2}')
nohup python3 -u sync_bpw_other.py "${1}" >> /dev/null 2>&1 &