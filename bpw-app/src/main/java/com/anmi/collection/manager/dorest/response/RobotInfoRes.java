
package com.anmi.collection.manager.dorest.response;

import lombok.Getter;
import lombok.Setter;

/**
 * 机器人信息
 *
 * <AUTHOR>
 * @date 2022/11/17
 */
@Getter
@Setter
public class RobotInfoRes {

    /**
     * 机器人数量
     */
    private Long amount;
    /**
     * 收费模式 1（包年）；2（包月）
     */
    private Long charge_mode;
    /**
     * 生效日期
     */
    private Long start_date;
    /**
     * 失效日期
     */
    private Long end_date;
    /**
     * 状态 OFFICIAL（正式）；EXPIRED（失效）
     */
    private String status;
    /**
     * 话费是否免费 true（免费）；false（收费
     */
    private Boolean is_call_free;
}
