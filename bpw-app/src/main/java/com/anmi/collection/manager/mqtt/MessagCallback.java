package com.anmi.collection.manager.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;

@Slf4j
public class MessagCallback implements MqttCallback {
    private MqttClient client;
    private MqttConnectOptions options;

    public MessagCallback() {
    }

    public MessagCallback(MqttClient client, MqttConnectOptions options) {
        this.client = client;
        this.options = options;
    }

    /**
     * 丢失对服务端的连接后触发该方法回调，此处可以做一些特殊处理，比如重连
     */
    @Override
    public void connectionLost(Throwable throwable) {
        // 连接丢失后，一般在这里面进行重连
        log.info("连接断开，可以做重连");
        try {
            if (null != client && !client.isConnected()) {
                //清除信息
                client.reconnect();
                log.error("尝试重新连接");
            } else {
                //清除信息
                client.connect(options);
                log.error("尝试建立新连接");
            }
        } catch (Exception e) {
            log.error("重连异常:{}", e.getMessage());
        }
    }

    @Override
    public void messageArrived(String s, MqttMessage mqttMessage) throws Exception {

    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {

    }
}
