package com.anmi.collection.manager.dorest;

import cn.duyan.dorest.RequestMethod;
import cn.duyan.dorest.anno.HttpClient;
import cn.duyan.dorest.anno.req.Dorest;
import cn.duyan.dorest.anno.req.Form;
import cn.duyan.dorest.anno.req.Param;
import cn.duyan.dorest.anno.req.Url;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.manager.dorest.request.CreateFifoReq;
import com.anmi.collection.manager.dorest.request.CreateRobotPointReq;
import com.anmi.collection.manager.dorest.response.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@HttpClient(maxTotal = 60, defaultMaxPerRoute = 30)
public interface NewDuyanInvoker {

    /**
     * 创建团队预测式计划
     *
     * @param url 请求地址
     * @param contents 团队预测式计划创建请求
     * @return {@link DoRestResult}<{@link CreateFifoRes}>
     */
    @Dorest(method = RequestMethod.POST,enableLog = true,enableLogException = true,ignoreAllKeys = true,timeout = 5000,connectTimeout = 5000,requestTimeout = 5000)
    DoRestResult<CreateFifoRes> createFifo(@Url String url, List<Map<String, Object>> contents);

    /**
     * 创建个人预测式计划
     *
     * @param url 请求地址
     * @param contents 个人预测式计划创建请求
     * @return {@link DoRestResult}<{@link CreateFifoRes}>
     */
    @Dorest(method = RequestMethod.POST,enableLog = true,enableLogException = true,ignoreAllKeys = true,timeout = 5000,connectTimeout = 5000,requestTimeout = 5000)
    DoRestResult<CreatePersonalFifoRes> createPersonalFifo(@Url String url, List<Map<String, Object>> contents);

    /**
     * 获取计划基本信息列表
     * 此接口用于获取指定计划的基本信息
     *
     * @param url    请求地址
     * @param apikey 唯一接口密钥
     * @param ids    度言计划id
     * @return {@link DoRestResult}<{@link List}<{@link GetPlanBaseInfoRes}>>
     */
    @Dorest(method = RequestMethod.GET,enableLog = true,enableLogException = true,timeout = 5000,connectTimeout=5000,requestTimeout=5000)
    DoRestResult<List<GetPlanBaseInfoRes>> getPlanListByIds(@Url String url, String apikey, String ids);

    /**
     * 获取计划统计数据
     *
     * @param url     请求地址
     * @param apikey  唯一接口密钥
     * @param round   计划执行轮次，不传默认返回最近一次执行结果
     * @return
     */
    @Dorest(method = RequestMethod.GET,enableLog = true,enableLogException = true,timeout = 5000,connectTimeout=5000,requestTimeout=5000)
    DoRestResult<StatisticsPlanRes> getPlanDetail(@Url String url, String apikey, Integer round);

    /**
     * 获取计划执行结果
     * 此接口用于获取指定计划的执行结果，即计划内所有客户的呼叫详情
     *
     * @param url              请求地址
     * @return {@link DoRestResult}<{@link GetPlanResultRes}>
     */
    @Dorest(method = RequestMethod.GET,enableLog = true,enableLogException = true,timeout = 5000,connectTimeout=5000,requestTimeout=5000)
    DoRestResult<GetPlanResultRes> getPlanResults(@Url String url);

    /**
     * 切换计划状态
     * 此接口用于切换指定计划的状态
     *
     * @param url             请求地址
     * @param apikey          唯一接口密钥
     * @param campaign_status 计划状态。1：未开始，2：执行中，3：已完成，4：已暂停，5：已取消
     * @return {@link DoRestResult}<{@link String}>
     */
    @Form
    @Dorest(method = RequestMethod.POST,enableLog = true,enableLogException = true,timeout = 5000,connectTimeout=5000,requestTimeout=5000)
    DoRestResult<String> changePlanStatus(@Url String url, String apikey, Long campaign_status);

    /**
     * 获取话术列表
     * 此接口用于获取公司的所有话术，话术需在管理后台（CFG）创建
     *
     * @param url    请求地址
     * @param apikey 唯一接口密钥
     * @return {@link DoRestResult}<{@link List}<{@link VoiceSiteListRes}>>
     */
    @Dorest(method = RequestMethod.GET,enableLog = true,enableLogException = true,timeout = 5000,connectTimeout=5000,requestTimeout=5000)
    DoRestResult<List<VoiceSiteListRes>> voiceSiteList(@Url String url, String apikey);

    /**
     * 获取计划基本信息
     * 此接口用于获取指定计划的基本信息
     *
     * @param url         请求地址
     * @param apikey      唯一接口密钥
     * @return {@link DoRestResult}<{@link GetPlanBaseInfoRes}>
     */
    @Dorest(method = RequestMethod.GET,enableLog = true,enableLogException = true,timeout = 5000,connectTimeout=5000,requestTimeout=5000)
    DoRestResult<GetPlanBaseInfoRes> casePlanBaseInfo(@Url String url, String apikey);

    /**
     * 获取话术变量
     * 此接口用于获取话术变量
     *
     * @param url           请求地址
     * @param apikey        唯一接口密钥
     * @param voice_site_id 话术id
     * @return {@link DoRestResult}<{@link List}<{@link String}>>
     */
    @Dorest(method = RequestMethod.GET,enableLog = true,enableLogException = true,timeout = 5000,connectTimeout=5000,requestTimeout=5000)
    DoRestResult<List<String>> siteVars(@Url String url, String apikey, Long voice_site_id);

    /**
     * 批量添加号码
     * 此接口用于在批量外呼计划（预测式计划、机器人计划、智能计划）中批量添加客户数据
     *
     * @param url         请求地址
     * @param batchAddReqContent 批量添加请求
     * @return {@link DoRestResult}<{@link String}>
     */
    @Dorest(method = RequestMethod.POST,enableLog = true,enableLogException = true,ignoreAllKeys = true,timeout = 5000,connectTimeout=5000,requestTimeout=5000)
    DoRestResult<String> batchAdd(@Url String url, List<Map<String,Object>> batchAddReqContent);

    /**
     * 获取短信回复记录
     * 此接口用于对应公司一天内的短信回复记录
     *
     * @param url           请求地址
     * @return {@link DoRestResult}<{@link List}<{@link String}>>
     */
    @Dorest(method = RequestMethod.GET,enableLog = true,enableLogException = true,timeout = 5000,connectTimeout=5000,requestTimeout=5000)
    DoRestResult<GetSmsReplyRes> getReolylogResult(@Url String url);

    /**
     * 创建机器人点呼
     *
     * @param url                 url
     * @param createRobotPointReq createRobotPointReq
     * @return {@link DoRestResult}<{@link CreateRobotPointRes}>
     */
    @Dorest(method = RequestMethod.POST,enableLog = true,enableLogException = true,timeout = 5000,connectTimeout = 5000,requestTimeout = 5000)
    DoRestResult<CreateRobotPointRes> createRobotPoint(@Url String url,CreateRobotPointReq createRobotPointReq);

    /**
     * 获取客户标签列表
     *
     * @param url                 url
     * @param apikey              apikey
     * @return {@link DoRestResult}<{@link CreateRobotPointRes}>
     */
    @Dorest(method = RequestMethod.GET,enableLog = true,enableLogException = true,timeout = 5000,connectTimeout = 5000,requestTimeout = 5000)
    DoRestResult<List<GetLabelInfoRes>> getLabelInfo(@Url String url, String apikey);


    /**
     * 获取号码池，这个接口度言的的code返回值是OK(其他接口是数字),所以不能用DoRestResult承载，只能用JSONObject！！！蛋疼的接口
     *
     * @param url                 url
     * @param apikey              apikey
     */
    @Dorest(method = RequestMethod.GET,enableLog = true,enableLogException = true,timeout = 5000,connectTimeout = 5000,requestTimeout = 5000)
    JSONObject getPhoneNumberPool(@Url String url, String apikey);
}
