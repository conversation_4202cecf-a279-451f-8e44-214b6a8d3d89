package com.anmi.collection.manager;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.common.enums.AccountLogEnums;
import com.anmi.collection.common.enums.CasePlanEnums;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.dto.*;
import com.anmi.collection.entity.requset.QosParam;
import com.anmi.collection.entity.requset.cases.Campaigns;
import com.anmi.collection.entity.requset.cases.casePlan.CallLogDetail;
import com.anmi.collection.entity.requset.cases.casePlan.CasePlanTmp;
import com.anmi.collection.entity.requset.cases.casePlan.ChangePlan;
import com.anmi.collection.entity.requset.cases.casePlan.VoiceSite;
import com.anmi.collection.entity.requset.duyan.PlanDetailQuery;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.CallDetailVO;
import com.anmi.collection.entity.response.duyan.*;
import com.anmi.collection.entity.response.repair.Content;
import com.anmi.collection.entity.response.repair.RepairInfoVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.dorest.DoRestResult;
import com.anmi.collection.manager.dorest.NewDuyanConstant;
import com.anmi.collection.manager.dorest.NewDuyanInvoker;
import com.anmi.collection.manager.dorest.NewRobotConstant;
import com.anmi.collection.manager.dorest.request.*;
import com.anmi.collection.manager.dorest.response.*;
import com.anmi.collection.service.*;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.HttpUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.CasePlan;
import com.anmi.domain.user.Company;
import com.anmi.domain.user.User;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.anmi.collection.utils.dict.CommonUtils.isEmpty;

/** <AUTHOR> 度言调用管理类 */
@Component
@Slf4j
public class DuyanManager extends BaseManager {
  private static final Logger LOGGER = LoggerFactory.getLogger(DuyanManager.class);
  @Autowired SystemConfig systemConfig;
  @Autowired CompanyService companyService;
  @Autowired CaseService caseService;
  @Autowired FileStoreService fileStoreService;

  @Autowired ContactsService contactsService;
  @Autowired DeltService deltService;
  @Autowired ProductService productService;
  @Autowired AccountLogService accountLogService;
  @Autowired NewRobotManager newRobotManager;
  @Autowired NewDuyanInvoker newDuyanInvoker;
  @Autowired YunpianManager yunpianManager;

  /**
   * 创建度言公司
   *
   * @param company
   * @param alias
   */
  public Long createOrg(Company company, String alias) {
    accountLogService.createAccountLog(AccountLogEnums.Type.ADD_DUYAN_ORG,
        String.format("创建度言公司[%s,%s]",company.getId(),alias),JSON.toJSONString(company));
    JSONObject org = new JSONObject();
    log.info("创建度言公司参数apikey：{}", systemConfig.getDuyanApikey(company.getDuyanServiceKey()));
    org.put("apikey", systemConfig.getDuyanApikey(company.getDuyanServiceKey()));
    org.put("name", company.getName());
    org.put("alias", alias);
    org.put("register_id", company.getName());
    org.put("wrap_up", "NEVER");
    org.put("org_phone_protection", "PUBLIC");
    org.put("callback", systemConfig.getDuyanCallbackUrl());
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(),company.getDuyanServiceKey()) + "org",
            HttpUtils.CONTENT_TYPE_FORM,
            org);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getLong("org_id")) {
        log.info("总公司：{} 度言别名：{} 同步度言成功！！", company.getName(), alias);
        return dataJson.getLongValue("org_id");
      }
    }
    return 0L;
  }

  /**
   * 设置总公司的活跃状态
   *
   * @return
   */
  public void setOrgStatus(Long duyanReferId, String isActive) {
    JSONObject param = new JSONObject();
    String apiKey = getOrgApiKeyRealTime(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    param.put("apikey", apiKey);
    param.put("isActive", isActive);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(),getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "org/active",
            HttpUtils.CONTENT_TYPE_FORM,
            param);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      if (!jsonResult.isEmpty() && jsonResult.getIntValue("status") != 1) {
        throw new ApiException("设置总公司活跃状态失败！");
      }
    }
  }

  /**
   * 设置总公司的活跃状态
   *
   * @return
   */
  public void setOrgCallback(Long duyanReferId, String url) {
    JSONObject param = new JSONObject();
    String apiKey = getOrgApiKeyRealTime(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    param.put("apikey", apiKey);
    param.put("callback", url);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(),getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "org/setCallback",
            HttpUtils.CONTENT_TYPE_FORM,
            param);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      if (!jsonResult.isEmpty() && jsonResult.getIntValue("status") != 1) {
        throw new ApiException("设置回调地址失败！");
      }
    }
  }

  /**
   * 设置呼入指定坐席地址配置
   *
   * @param duyanReferId
   * @param url
   */
  public void setOrgAgentUrl(Long duyanReferId, String url) {
    JSONObject param = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    param.put("apikey", apiKey);
    param.put("url", url);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(),getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "org/agent/url",
            HttpUtils.CONTENT_TYPE_FORM,
            param);
    if (isEmpty(referResponse)) {
      throw new ApiException("调用设置呼入指定坐席接口失败");
    }
    JSONObject jsonResult = JSONObject.parseObject(referResponse);
    valiResponse(jsonResult);
  }

  /**
   * 设置号码隐藏属性
   *
   * @return
   */
  public void setPhoneProtection(Long duyanReferId, String phoneProtection) {
    JSONObject param = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    param.put("apikey", apiKey);
    param.put("org_id", duyanReferId);
    param.put("org_phone_protection", phoneProtection);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(),getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "org/org_phone_protection",
            HttpUtils.CONTENT_TYPE_FORM,
            param);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      int status = jsonResult.getInteger("status");
      if (status == 1) {
        LOGGER.info("修改号码可见属性：结果：{}", "成功");
      } else {
        LOGGER.info("修改号码可见属性：结果：{}", "失败");
      }
    }
  }

  /**
   * 预先注册完整号码
   *
   * @return
   */
  public void callRegister(Long duyanReferId, Long accountId, String mobile) {
    JSONObject param = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    param.put("apikey", apiKey);
    param.put("account_id", accountId);
    param.put("target", mobile);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(),getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "call/register",
            HttpUtils.CONTENT_TYPE_FORM,
            param);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      int status = jsonResult.getInteger("status");
      if (status == 1) {
        LOGGER.info("预先注册完整号码：结果：{}", "成功");
      } else {
        LOGGER.info("预先注册完整号码：结果：{}", "失败");
      }
    }
  }

  /** 设置团队（度言）的活跃状态 */
  public void setDepStatus(Long duyanReferId, Long teamReferId, String isActive) {
    JSONObject param = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    param.put("apikey", apiKey);
    param.put("teamId", teamReferId);
    param.put("isActive", isActive);
    HttpUtils.requset(
        HttpUtils.METHOD_POST,
        String.format(systemConfig.getDuyanUrlPrefix(),getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "team/active/" + duyanReferId,
        HttpUtils.CONTENT_TYPE_FORM,
        param);
  }

  /**
   * 添加单个员工
   *
   * @param userName
   * @param userTitle
   * @param mobile
   * @return
   */
  public Long addUser(String userName, String userTitle, String mobile, Long duyanReferId) {
    JSONObject user = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    user.put("apikey", apiKey);
    user.put("name", userName);
    user.put("title", userTitle);
    user.put("mobile", mobile);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account",
            HttpUtils.CONTENT_TYPE_FORM,
            user);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getLong("account_id")) {
        return dataJson.getLong("account_id");
      }
    }
    return 0L;
  }

  /**
   * 删除单个员工
   *
   * @return
   */
  public void removeUser(Long accountId, Long duyanReferId) {
    JSONObject user = new JSONObject();
    String apiKey = getOrgApiKeyRealTime(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    user.put("apikey", apiKey);
    user.put("account_id", accountId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DELETE,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId,
            HttpUtils.CONTENT_TYPE_FORM,
            user);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      int status = jsonResult.getInteger("status");
      if (status == 1) {
        LOGGER.info("删除员工:{},结果：{}", accountId, true);
      } else {
        LOGGER.info("删除员工:{},结果：{}", accountId, false);
      }
    } else {
      throw new ApiException("删除度言员工失败");
    }
  }

  /** 设置度言管理员 */
  public boolean setAdmin(Long accountId, Long duyanReferId) {
    JSONObject user = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    user.put("apikey", apiKey);
    user.put("account_id", accountId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId + "/admin",
            HttpUtils.CONTENT_TYPE_FORM,
            user);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getBoolean("is_admin")) {
        return dataJson.getBoolean("is_admin");
      }
    } else {
      throw new ApiException("调用度言接口失败");
    }
    return false;
  }

  /** 取消度言管理员角色 */
  public boolean removeAdmin(Long accountId, Long duyanReferId) {
    JSONObject user = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    user.put("apikey", apiKey);
    user.put("account_id", accountId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DELETE,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId + "/admin",
            HttpUtils.CONTENT_TYPE_FORM,
            user);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getBoolean("is_admin")) {
        return dataJson.getBoolean("is_admin");
      }
    } else {
      throw new ApiException("调用度言接口失败");
    }
    return false;
  }

  /** 设置质检员角色 */
  public boolean setInspector(
      Long accountId, Long duyanReferId, Boolean isInspectorSup, String inspectorTeamIds) {
    JSONObject user = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    user.put("apikey", apiKey);
    user.put("is_inspector_supervisor", isInspectorSup);
    user.put("inspector_team_ids", inspectorTeamIds);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId + "/inspector",
            HttpUtils.CONTENT_TYPE_FORM,
            user);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getBoolean("is_inspector")) {
        return dataJson.getBoolean("is_inspector");
      }
    } else {
      throw new ApiException("调用度言接口失败");
    }
    return false;
  }

  /** 取消质检员角色 */
  public boolean delInspector(Long accountId, Long duyanReferId) {
    JSONObject user = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    user.put("apikey", apiKey);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DELETE,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId + "/inspector",
            HttpUtils.CONTENT_TYPE_FORM,
            user);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getBoolean("is_inspector")) {
        return dataJson.getBoolean("is_inspector");
      }
    }
    return true;
  }

  /** 智能质检设置 */
  public void setInspection(
      Long duyanReferId, Integer type, Integer durationStart, Integer durationEnd, Map targetMap) {
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject targetMapJson = new JSONObject();
    for (Object key : targetMap.keySet()) {
      targetMapJson.put(key.toString(), targetMap.get(key));
    }
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(),getDuyanServiceKeyByDuyanReferId(duyanReferId))
                + "inspect/rule?apikey="
                + apiKey
                + "&type="
                + type
                + "&durationStart="
                + durationStart
                + "&durationEnd="
                + durationEnd,
            HttpUtils.CONTENT_TYPE_JSON,
            targetMapJson);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
    }
  }

  /** 智能质检设置 */
  public JSONObject getInspection(Long duyanReferId) {
    JSONObject inspection = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    inspection.put("apikey", apiKey);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "inspect/rule",
            HttpUtils.CONTENT_TYPE_FORM,
            inspection);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (dataJson != null && !dataJson.isEmpty()) {
        return dataJson;
      }
    }
    return null;
  }

  /**
   * 创建团队
   *
   * @param duyanReferId
   * @param name
   * @param parentId
   */
  public Long createTeam(Long duyanReferId, String name, Long parentId, Boolean isHiddenMobile) {
    // 子公司
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    // 团队
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("apikey", orgApikey);
    jsonObject.put("name", name);
    jsonObject.put("phone_visibility", isHiddenMobile ? "PRIVATE" : "PUBLIC");
    jsonObject.put("remark", name);
    if (null != parentId) {
      jsonObject.put("parent_id", parentId);
    }
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "team",
            HttpUtils.CONTENT_TYPE_FORM,
            jsonObject);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getLong("team_id")) {
        log.info("组织架构成员：{} 同步度言成功！！", name);
        return dataJson.getLongValue("team_id");
      }
    }
    return 0L;
  }

  /**
   * 删除团队(分公司)
   *
   * @param duyanReferId
   */
  public void cancelTeam(Long duyanReferId, Long teamId) {
    // 子公司
    String orgApikey = getOrgApiKeyRealTime(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    // 团队
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("apikey", orgApikey);
    jsonObject.put("team_id", teamId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DELETE,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "team/" + teamId,
            HttpUtils.CONTENT_TYPE_FORM,
            jsonObject);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      int status = jsonResult.getInteger("status");
      if (status == 1) {
        LOGGER.info("删除团队:{},结果：{}", teamId, true);
      } else {
        LOGGER.info("删除团队:{},结果：{}", teamId, false);
      }
    }
  }

  /**
   * 修改团队信息(分公司)
   *
   * @param duyanReferId
   */
  public void updateTeam(
      Long duyanReferId,
      Long teamId,
      String name,
      Long parentId,
      String remark,
      Boolean isHiddenMobile) {
    // 子公司
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    // 团队
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("team_id", teamId);
    jsonObject.put("apikey", orgApikey);
    jsonObject.put("name", name);
    jsonObject.put("phone_visibility", isHiddenMobile ? "PRIVATE" : "PUBLIC");
    jsonObject.put("parent_id", parentId);
    jsonObject.put("remark", remark);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "team/" + teamId,
            HttpUtils.CONTENT_TYPE_FORM,
            jsonObject);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      int status = jsonResult.getInteger("status");
      if (status == 1) {
        LOGGER.info("修改团队:{},结果：{}", teamId, true);
      } else {
        LOGGER.info("修改团队:{},结果：{}", teamId, false);
      }
    }
  }

  /**
   * 修改坐席所属团队
   *
   * @param duyanReferId 总公司度言id
   * @param newTeamId 小组的度言id
   * @param accountId 员工的accountId
   * @param
   * @return
   */
  public Long resetTeam(Long duyanReferId, Long newTeamId, Long accountId, Boolean isSupervisor) {
    // 子公司
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    // 团队
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("apikey", orgApikey);
    jsonObject.put("account_id", accountId);
    jsonObject.put("team_id", newTeamId);
    jsonObject.put("is_supervisor", isSupervisor);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId + "/team",
            HttpUtils.CONTENT_TYPE_FORM,
            jsonObject);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getLong("team_id")) {
        return dataJson.getLongValue("team_id");
      }
    }
    return 0L;
  }

  /** 修改度言员工信息 */
  public void updateUser(
      Long duyanReferId, Long accountId, String name, String title, String mobile) {
    // 子公司
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    // 团队
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("apikey", orgApikey);
    jsonObject.put("account_id", accountId);
    jsonObject.put("name", name);
    jsonObject.put("title", title);
    jsonObject.put("mobile", mobile);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId,
            HttpUtils.CONTENT_TYPE_FORM,
            jsonObject);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
    }
  }

  /**
   * 移除团队坐席
   *
   * @param duyanReferId
   * @param oldTeamId
   * @param duyanAccountId
   */
  public void removeTeam(Long duyanReferId, Long oldTeamId, Long duyanAccountId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    // 团队
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("apikey", orgApikey);
    jsonObject.put("account_id", duyanAccountId);
    jsonObject.put("team_id", oldTeamId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DELETE,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + duyanAccountId + "/team",
            HttpUtils.CONTENT_TYPE_FORM,
            jsonObject);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getLong("team_id")) {}
    }
  }

  /**
   * 创建度言坐席
   *
   * @param duyanReferId
   * @param name
   * @param mobile
   * @param teamId
   * @return
   */
  public Long createOrgAgent(Long duyanReferId, String name, String mobile, Long teamId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    // 坐席
    JSONObject agent = new JSONObject();
    agent.put("apikey", orgApikey);
    agent.put("title", "坐席");
    agent.put("name", name);
    agent.put("mobile", mobile);
    agent.put("team_id", teamId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/agent",
            HttpUtils.CONTENT_TYPE_FORM,
            agent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getLong("account_id")) {
        return dataJson.getLongValue("account_id");
      }
    }
    return 0L;
  }

  /**
   * 获取总公司的购买坐席数
   *
   * @param duyanReferId
   * @return
   */
  public Map<String, Integer> getSeatNum(Long duyanReferId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    // 坐席
    JSONObject agent = new JSONObject();
    agent.put("apikey", orgApikey);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "org/agent_limit",
            HttpUtils.CONTENT_TYPE_FORM,
            agent);
    Map<String, Integer> resultMap = new HashMap<>();
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty()) {
        Integer agentLimit = dataJson.getIntValue("agent_limit");
        Integer fifoAgentLimit = dataJson.getIntValue("fifo_agent_limit");
        resultMap.put("agentLimit", agentLimit == null ? 0 : agentLimit);
        resultMap.put("fifoAgentLimit", fifoAgentLimit == null ? 0 : fifoAgentLimit);
      }
    }
    return resultMap;
  }

  /**
   * 取消坐席角色
   *
   * @param duyanReferId
   * @param accountId
   */
  public Boolean cancelAgent(Long duyanReferId, Long accountId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject agent = new JSONObject();
    agent.put("apikey", orgApikey);
    agent.put("account_id", accountId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DELETE,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId + "/agent",
            HttpUtils.CONTENT_TYPE_FORM,
            agent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.get("is_agent")) {
        LOGGER.info("删除坐席:{},结果：{}", accountId, dataJson.getBoolean("is_agent"));
      }
      return dataJson.getBoolean("is_agent");
    }
    return false;
  }

  /**
   * 设置坐席角色
   *
   * @param duyanReferId
   * @param accountId
   */
  public boolean setAgent(Long duyanReferId, Long accountId, Long teamId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject agent = new JSONObject();
    agent.put("apikey", orgApikey);
    agent.put("account_id", accountId);
    agent.put("team_id", teamId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId + "/agent",
            HttpUtils.CONTENT_TYPE_FORM,
            agent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getBoolean("is_agent")) {
        return dataJson.getBoolean("is_agent");
      }
    } else {
      throw new ApiException("调用度言接口失败");
    }
    return false;
  }

  /**
   * 设置度言团队主管角色
   *
   * @param duyanReferId
   * @param accountId
   */
  public boolean setTeamLeader(Long duyanReferId, Long accountId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject agent = new JSONObject();
    agent.put("apikey", orgApikey);
    agent.put("account_id", accountId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId + "/supervisor",
            HttpUtils.CONTENT_TYPE_FORM,
            agent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getBoolean("is_supervisor")) {
        return dataJson.getBoolean("is_supervisor");
      }
    } else {
      throw new ApiException("调用度言接口失败");
    }
    return false;
  }

  /**
   * 取消度言团队主管角色
   *
   * @param duyanReferId
   * @param accountId
   */
  public boolean removeTeamLeader(Long duyanReferId, Long accountId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject agent = new JSONObject();
    agent.put("apikey", orgApikey);
    agent.put("account_id", accountId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DELETE,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId + "/supervisor",
            HttpUtils.CONTENT_TYPE_FORM,
            agent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getBoolean("is_supervisor")) {
        return dataJson.getBoolean("is_supervisor");
      }
    }
    return false;
  }

  public boolean setFifoAgent(Long duyanReferId, Long accountId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject agent = new JSONObject();
    agent.put("apikey", orgApikey);
    agent.put("account_id", accountId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId + "/fifo",
            HttpUtils.CONTENT_TYPE_FORM,
            agent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getBoolean("is_fifo_agent")) {
        return dataJson.getBoolean("is_fifo_agent");
      }
    } else {
      throw new ApiException("调用度言接口失败");
    }
    return false;
  }

  public boolean removeFifoAgent(Long duyanReferId, Long accountId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject agent = new JSONObject();
    agent.put("apikey", orgApikey);
    agent.put("account_id", accountId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DELETE,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/" + accountId + "/fifo",
            HttpUtils.CONTENT_TYPE_FORM,
            agent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getBoolean("is_fifo_agent")) {
        return dataJson.getBoolean("is_fifo_agent");
      }
    }
    return false;
  }

  /**
   * 登录坐席
   *
   * @param duyanReferId
   * @param accountId
   * @return
   */
  public String getCtiToken(Long duyanReferId, Long accountId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject agent = new JSONObject();
    agent.put("apikey", orgApikey);
    agent.put("account_id", accountId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/login/cti",
            HttpUtils.CONTENT_TYPE_FORM,
            agent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getString("token")) {
        return dataJson.getString("token");
      }
    }
    return "";
  }

  /**
   * 登录管理员后台
   *
   * @param duyanReferId
   * @param accountId
   * @return
   */
  public String getCfgToken(Long duyanReferId, Long accountId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject agent = new JSONObject();
    agent.put("apikey", orgApikey);
    agent.put("account_id", accountId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "account/login/cfg",
            HttpUtils.CONTENT_TYPE_FORM,
            agent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty() && null != dataJson.getString("token")) {
        return dataJson.getString("token");
      }
    }
    return "";
  }

  /**
   * 创建信修任务
   *
   * @param duyanReferId
   * @return
   */
  public RepairInfoVO addInfoRepair(Long duyanReferId, String taskName, List<Content> contents) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject param = new JSONObject();
    param.put("apikey", orgApikey);
    param.put("name", taskName);
    param.put("content", JSONObject.toJSONString(contents));
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "repair/batch",
            HttpUtils.CONTENT_TYPE_FORM,
            param);
    RepairInfoVO repairInfo = new RepairInfoVO();
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty()) {
        if (null != dataJson.getJSONArray("content")) {
          JSONArray array = dataJson.getJSONArray("content");
          repairInfo.setContent(array.toJavaList(Content.class));
        }
        if (null != dataJson.getString("task_id")) {
          repairInfo.setTaskId(dataJson.getString("task_id"));
        }
        return repairInfo;
      }
    }
    return null;
  }

  /**
   * 获取信修任务结果
   *
   * @param duyanReferId
   * @return
   */
  public RepairInfoVO getRepairInfo(Long duyanReferId, String taskId) {
    String orgApikey = getOrgApiKey(duyanReferId);
    if (isEmpty(orgApikey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject param = new JSONObject();
    param.put("apikey", orgApikey);
    param.put("task_id", taskId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "repair",
            HttpUtils.CONTENT_TYPE_FORM,
            param);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty()) {
        RepairInfoVO repairInfo = dataJson.toJavaObject(RepairInfoVO.class);
        return repairInfo;
      }
    }
    return null;
  }

  private PageOutput getPageOut(JSONObject data, Integer limit, Integer page, Class clas) {
    PageOutput pageOutput = new PageOutput();
    Integer total = 0;
    Integer pageSize = 0;
    List list = new ArrayList();
    if (data != null) {
      total = data.getIntValue("total_elements");
      pageSize = data.getIntValue("total_pages");
      JSONArray campaigns = data.getJSONArray("campaigns");
      campaigns.stream()
          .forEach(
              l -> {
                JSONObject object = (JSONObject) l;
                list.add(object.toJavaObject(clas));
              });
    }
    pageOutput.setList(list);
    pageOutput.setTotal(total);
    pageOutput.setPages(pageSize);
    pageOutput.setPageSize(list.size());
    pageOutput.setLimit(limit);
    pageOutput.setPageNum(page);
    return pageOutput;
  }

  private CasePlanTmp convertTmp(Object o) {
    CasePlanTmp tmp = new CasePlanTmp();
    JSONObject json = (JSONObject) o;
    tmp.setCaller(json.getString("caller"));
    tmp.setDuyanPlanId(json.getLongValue("id"));
    long endTime = json.getLongValue("end_time");
    long startTime = json.getLongValue("start_time");
    if (endTime != 0) {
      tmp.setPlanEndTime(new Date(json.getLongValue("end_time")));
    }
    if (startTime != 0) {
      tmp.setPlanStartTime(new Date(json.getLongValue("start_time")));
    }
    tmp.setFinishedCount(json.getLongValue("finished_count"));
    tmp.setTotalCount(json.getLongValue("total_count"));
    tmp.setPlanStatus(json.getString("status"));
    tmp.setPlanCreatedDate(json.getLongValue("created_time"));
    return tmp;
  }

  // 创建智能计划
  public CasePlan createPlan(List<CaseContact> param, CasePlan plan) {
    JSONObject planJson = getPlanJsonParam(plan);
    JSONObject jsonContent = new JSONObject();
    JSONArray jsonArray = new JSONArray();
    if (!CollectionUtils.isEmpty(param)) {
      for (CaseContact temp : param) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("U_phone", temp.getContactMobile());
        jsonObject.put("U_name", temp.getContactName());
        jsonObject.put("U_caseId", temp.getCaseId());
        jsonObject.put("U_products", temp.getProductName());
        jsonObject.put("U_orgId", UserUtils.getTokenUser().getOrgId());
        jsonObject.put("U_idcard", temp.getIdCard());
        jsonObject.put("U_amount", temp.getAmount() / 1000.00);
        jsonObject.put("U_entrustendtime", DateUtils.formatDate(temp.getEntrustEndTime(), DateUtils.SIMPLE_DATE_FORMAT));
        jsonObject.put("U_overduedays", temp.getOverdueDays());
        jsonObject.put("U_deltname", deltService.getNames().get(temp.getOrgDeltId()));
        jsonArray.add(jsonObject);
      }
    }
    planJson.put("is_extra", true);
    planJson.put("is_distinct", true);
    planJson.put("is_early_media_rec_enabled",CasePlanEnums.CheckPhoneNo.YES.getCode().equals(plan.getCheckPhoneNo()));
    jsonContent.put("content", jsonArray.toJSONString());
    String path = "?";
    Iterator<String> iterator = planJson.keySet().iterator();
    while (iterator.hasNext()) {
      String next = iterator.next();
      if (iterator.hasNext()) {
        path = path + next + "=" + planJson.get(next) + "&";
      } else {
        path = path + next + "=" + planJson.get(next);
      }
    }

    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DUYAN,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(UserUtils.getTokenUser().getOrgId())) + "call/campaign/robot" + path,
            HttpUtils.CONTENT_TYPE_JSON,
            jsonContent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject data = jsonResult.getJSONObject("data");
      if (!data.isEmpty()) {
        plan.setCaseTotal(data.getInteger("total_count"));
        plan.setDuyanPlanId(data.getLong("id"));
      }
    } else {
      throw new ApiException("调用创建智能计划接口失败");
    }
    return plan;
  }

  /**
   * 创建机器人计划
   *
   * @param param       参数
   * @param plan        计划
   * @param userSession 用户会话
   * @param contents    新机器人计划的body
   * @return {@link CasePlan}
   */
  public CasePlan createPlan(List<CaseContact> param, CasePlan plan, UserSession userSession, List<Map<String,Object>> contents) {
    if (ObjectUtil.equals(plan.getType(), CasePlanEnums.Type.NEWROBOT.getCode())){
      return newRobotManager.createPlan(plan,userSession, contents);
    }

    JSONObject planJson = getPlanJsonParam(plan,userSession.getOrgId());
    JSONObject jsonContent = new JSONObject();
    JSONArray jsonArray = new JSONArray();
    if (!CollectionUtils.isEmpty(param)) {
      //过滤掉不符合条件的手机号码
      param = param.stream()
              .filter(caseContact -> PatternUtils.isPlanPhone(caseContact.getContactMobile()))
              .collect(Collectors.toList());
      for (CaseContact temp : param) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("U_phone", temp.getContactMobile());
        jsonObject.put("U_name", temp.getContactName());
        jsonObject.put("U_caseId", temp.getCaseId());
        jsonObject.put("U_products", temp.getProductName());
        jsonObject.put("U_orgId", userSession.getOrgId());
        jsonObject.put("U_idcard", temp.getIdCard());
        jsonObject.put("U_amount", temp.getAmount() / 1000.00);
        jsonObject.put("U_entrustendtime", DateUtils.formatDate(temp.getEntrustEndTime(), DateUtils.SIMPLE_DATE_FORMAT));
        jsonObject.put("U_overduedays", temp.getOverdueDays());
        jsonObject.put("U_deltname", deltService.getNames().get(temp.getOrgDeltId()));
        jsonArray.add(jsonObject);
      }
    }
    planJson.put("is_extra", true);
    planJson.put("is_distinct", true);
    planJson.put("is_early_media_rec_enabled",CasePlanEnums.CheckPhoneNo.YES.getCode().equals(plan.getCheckPhoneNo()));
    jsonContent.put("content", jsonArray.toJSONString());
    String path = "?";
    Iterator<String> iterator = planJson.keySet().iterator();
    while (iterator.hasNext()) {
      String next = iterator.next();
      if (iterator.hasNext()) {
        path = path + next + "=" + planJson.get(next) + "&";
      } else {
        path = path + next + "=" + planJson.get(next);
      }
    }

    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DUYAN,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(userSession.getOrgId())) + "call/campaign/robot" + path,
            HttpUtils.CONTENT_TYPE_JSON,
            jsonContent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject data = jsonResult.getJSONObject("data");
      if (!data.isEmpty()) {
        plan.setCaseTotal(data.getInteger("total_count"));
        plan.setDuyanPlanId(data.getLong("id"));
      }
    } else {
      throw new ApiException("调用创建智能计划接口失败");
    }
    return plan;
  }

  private JSONObject getPlanJsonParam(CasePlan plan) {
    JSONObject commonJson = getCommonJson();
    commonJson.put("name", plan.getName());
    commonJson.put("site_id", plan.getSiteId());
    if (Objects.equals(plan.getIsStartNow(), 1)) {
      commonJson.put("is_start_now", true);
    } else {
      // 自动模式需要传时间
      if (plan.getStartType() == 1) {
        if (plan.getPlanStartTime() == null) {
          throw new ApiException("定时模式需要传计划开始时间");
        } else {
          commonJson.put("start_time", plan.getPlanStartTime().getTime());
        }
        if (plan.getPlanEndTime() != null) {
          commonJson.put("end_time", plan.getPlanEndTime().getTime());
        }
        if (plan.getPlanStartTime() != null && plan.getPlanEndTime() != null) {
          if (plan.getPlanStartTime().getTime() > plan.getPlanEndTime().getTime()) {
            throw new ApiException("开始时间应该小于结束时间");
          }
        }
      }
    }
    //如果是团队线路池那么传poolId即可，此时caller是团队线路池名称，不用传，但是保存在安米本地方便前端展示
    if (plan.getPoolId() != null) {
      commonJson.put("pool_id", plan.getPoolId());
    } else {
      if (!StringUtils.isEmpty(plan.getCaller())) {
        commonJson.put("caller", plan.getCaller());
      }
    }
    commonJson.put("priority", plan.getPriority());
    commonJson.put("is_early_media_rec_enabled",CasePlanEnums.CheckPhoneNo.YES.getCode().equals(plan.getCheckPhoneNo()));
    return commonJson;
  }

  private JSONObject getPlanJsonParam(CasePlan plan,Long orgId) {
    JSONObject commonJson = getCommonJson(orgId);
    commonJson.put("name", plan.getName());
    commonJson.put("site_id", plan.getSiteId());
    if (Objects.equals(plan.getIsStartNow(), 1)) {
      commonJson.put("is_start_now", true);
    } else {
      // 自动模式需要传时间
      if (plan.getStartType() == 1) {
        if (plan.getPlanStartTime() == null) {
          throw new ApiException("定时模式需要传计划开始时间");
        } else {
          commonJson.put("start_time", plan.getPlanStartTime().getTime());
        }
        if (plan.getPlanEndTime() != null) {
          commonJson.put("end_time", plan.getPlanEndTime().getTime());
        }
        if (plan.getPlanStartTime() != null && plan.getPlanEndTime() != null) {
          if (plan.getPlanStartTime().getTime() > plan.getPlanEndTime().getTime()) {
            throw new ApiException("开始时间应该小于结束时间");
          }
        }
      }
    }
    //如果是团队线路池那么传poolId即可，此时caller是团队线路池名称，不用传，但是保存在安米本地方便前端展示
    if (plan.getPoolId() != null) {
      commonJson.put("pool_id", plan.getPoolId());
    } else {
      if (!StringUtils.isEmpty(plan.getCaller())) {
        commonJson.put("caller", plan.getCaller());
      }
    }
    commonJson.put("priority", plan.getPriority());
    commonJson.put("is_early_media_rec_enabled",CasePlanEnums.CheckPhoneNo.YES.getCode().equals(plan.getCheckPhoneNo()));
    return commonJson;
  }

  private JSONObject getCommonJson() {
    Long duyanId = UserUtils.getDuyanId();
    String apiKey = getOrgApiKey(duyanId);
    if (StrUtil.isBlank(apiKey)){
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("apikey", apiKey);
    return jsonObject;
  }

  private JSONObject getCommonJson(long orgId) {
    Long duyanId = UserUtils.getDuyanId(orgId);
    String apiKey = getOrgApiKey(duyanId);
    if (StrUtil.isBlank(apiKey)){
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("apikey", apiKey);
    return jsonObject;
  }

  // https://open.duyansoft.com/api/v1/call/campaign_item 获取单条计划详情
  public String getCallDetail(String callUUid, long orgId) {
    String tagName = "";
    JSONObject commonJson = getCommonJson(orgId);
    commonJson.put("call_uuid", callUUid);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId)) + "call/campaign_item",
            HttpUtils.CONTENT_TYPE_FORM,
            commonJson);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject data = jsonResult.getJSONObject("data");
      if (data != null) {
        tagName = data.getString("tag");
      }
    }
    return tagName;
  }

  /**
   * 获取话术
   *
   * @param module 单元
   * @return {@link List}<{@link VoiceSite}>
   */
  public List<VoiceSite> getSites(String module) {
    if (ObjectUtil.equals(module, "NEWROBOT")){
      return newRobotManager.getSites();
    }
    if (ObjectUtil.equals(module, "PERSONALFIFO")) {
      return getPersonalSites();
    }
    JSONObject site = getCommonJson();
    List<VoiceSite> voiceSites = new ArrayList<>();
    if (!StringUtils.isEmpty(module)) {
      site.put("module", module);
    }
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefixV3(),getDuyanServiceKeyByDuyanReferId(UserUtils.getDuyanId())) + "call/voice_site",
            HttpUtils.CONTENT_TYPE_FORM,
            site);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONArray data = jsonResult.getJSONArray("data");
      if (data != null) {
        voiceSites = convertSite(data);
      }
    }
    return voiceSites;
  }

  public List<VoiceSite> getRobotSitesWithoutToken(Long orgId) {
    return newRobotManager.getSitesByOrgId(orgId);
  }

  private List<VoiceSite> convertSite(JSONArray data) {
    List<VoiceSite> list = new ArrayList<>();
    try {
      data.stream()
          .forEach(
              l -> {
                VoiceSite site = new VoiceSite();
                JSONObject object = (JSONObject) l;
                if (object.containsKey("id")) {
                  site.setId(object.getLong("id"));
                }
                if (object.containsKey("name")) {
                  site.setName(object.getString("name"));
                }
                if (object.containsKey("module")) {
                  site.setModule(object.getString("module"));
                }
                if (object.containsKey("status")) {
                  site.setStatus(object.getString("status"));
                }
                if (object.containsKey("created_time")) {
                  site.setCreatedTime(new Date(object.getLong("created_time")));
                }
                if (object.containsKey("last_updated_time")) {
                  site.setLastUpdatedTime(new Date(object.getLong("last_updated_time")));
                }
                if (object.containsKey("ttsType")) {
                  TtsTypeDetailVO detailVO = new TtsTypeDetailVO();
                  detailVO.setSpeechRate(object.getJSONObject("ttsType").getIntValue("speechRate"));
                  detailVO.setPitchRate(object.getJSONObject("ttsType").getIntValue("pitchRate"));
                  detailVO.setVolume(object.getJSONObject("ttsType").getIntValue("volume"));
                  detailVO.setShowName(
                      object
                          .getJSONObject("ttsType")
                          .getJSONObject("ttsType")
                          .getString("showName"));
                  detailVO.setCnName(
                      object.getJSONObject("ttsType").getJSONObject("ttsType").getString("cnName"));
                  detailVO.setGender(
                      object
                          .getJSONObject("ttsType")
                          .getJSONObject("ttsType")
                          .getIntValue("gender"));
                  site.setDetailVO(detailVO);
                }
                list.add(site);
              });
    } catch (Exception e) {
      throw new ApiException("json 解析异常！");
    }
    return list;
  }

  /**
   * 获取计划执行结果
   *
   * @param query 查询
   * @param type  类型
   * @return {@link PageOutput}<{@link PlanDetailVO}>
   */
  public PageOutput<PlanDetailVO> getPlanDetails(PlanDetailQuery query, Integer type) {
    if (ObjectUtil.equals(type, CasePlanEnums.Type.NEWROBOT.getCode())){
      return newRobotManager.getPlanDetails(query);
    }
    // 查询团队、个人预测式计划详细信息
    if (ObjectUtil.equals(type, CasePlanEnums.Type.FIFO.getCode()) || ObjectUtil.equals(type, CasePlanEnums.Type.PERSONALFIFO.getCode())) {
      UserSession user = UserUtils.getTokenUser();
      Long orgId = user.getOrgId();
      return getFifoPlanDetail(query, orgId);
    }
    JSONObject json = (JSONObject) JSONObject.toJSON(query);
    json.remove("campaign_id");
    Long duyanId = UserUtils.getDuyanId();
    String apiKey = getOrgApiKey(duyanId);
    json.put("apikey", apiKey);
    PageOutput pageOutput = new PageOutput();
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanId)) + "call/campaign/" + query.getCampaign_id() + "/item",
            HttpUtils.CONTENT_TYPE_FORM,
            json);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject data = jsonResult.getJSONObject("data");
      pageOutput = getPageOut(data, query.getPage_size(), query.getPage_num(), PlanDetailVO.class);
    }
    return pageOutput;
  }

  /**
   * 获取计划基本信息
   * @param orgId 公司id
   * @param campaignId 计划id
   * @return
   */
  public CasePlanTmp getPlanInfo(Long orgId, Long campaignId) {
    JSONObject commonJson = getCommonJson(orgId);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId)) + "call/campaign/"
                + campaignId + "?apikey="
                + commonJson.getString("apikey"),
            HttpUtils.CONTENT_TYPE_JSON,
            null);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      long status = jsonResult.getLongValue("status");
      if (status == 1) {
        JSONObject data = jsonResult.getJSONObject("data");
        if (!data.isEmpty()) {
          return this.convertTmp(data);
        }
      }
    }
    CasePlanTmp casePlanTmp = new CasePlanTmp();
    casePlanTmp.setTotalCount(0L);
    return casePlanTmp;
  }

  /**
   * 切换计划状态
   *
   * @param change change
   * @return int
   */
  public int change(ChangePlan change) {
    if (ObjectUtil.equals(change.getRobotType(), CasePlanEnums.Type.NEWROBOT.getCode())){
      return newRobotManager.changePlanStatus(change);
    }
    // 团体、个人预测式外呼计划变更
    if (ObjectUtil.equals(change.getRobotType(), CasePlanEnums.Type.PERSONALFIFO.getCode())
    || ObjectUtil.equals(change.getRobotType(), CasePlanEnums.Type.FIFO.getCode())) {
      return changeFifoPlanStatus(change);
    }
    JSONObject commonJson = getCommonJson();
    commonJson.put("campaign_status", change.getPlanStatus());
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(UserUtils.getDuyanId()))
                + "call/campaign/"
                + change.getDuyanPlanId()
                + "/status",
            HttpUtils.CONTENT_TYPE_FORM,
            commonJson);

    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      try {
        valiResponse(jsonResult);
      } catch (ApiException e) {
        if ("无效的status[IN_PROGRESS]，status必须是NOT_STARTED或者PAUSED才可开始."
            .equalsIgnoreCase(jsonResult.getString("message"))) {
          throw new ApiException("计划状态已更改，请刷新页面");
        }
      }
      return jsonResult.getIntValue("status");
    }
    return 0;
  }

  public OrgInfoTemp getOrgInfo() {
    JSONObject commonJson = getCommonJson();
    OrgInfoTemp orgInfoTemp = new OrgInfoTemp();
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(UserUtils.getDuyanId())) + "org/info",
            HttpUtils.CONTENT_TYPE_FORM,
            commonJson);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject dataJson = jsonResult.getJSONObject("data");
      if (!dataJson.isEmpty()) {
        orgInfoTemp = dataJson.toJavaObject(OrgInfoTemp.class);
      }
    }
    return orgInfoTemp;
  }

  public List<RobotInfoTemp> getRobotInfo() {
    List<RobotInfoTemp> robotInfoTemps = Lists.newArrayList();
    JSONObject commonJson = getCommonJson();
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(UserUtils.getDuyanId())) + "robot",
            HttpUtils.CONTENT_TYPE_FORM,
            commonJson);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONArray data = jsonResult.getJSONArray("data");
      if (!data.isEmpty()) {
        robotInfoTemps = data.toJavaList(RobotInfoTemp.class);
      }
    }
    return robotInfoTemps;
  }

  public boolean orgTtsTypeModify(
      Long siteId, String ttsType, String name, String description, String module) {
    JSONObject commonJson = getCommonJson();
    JSONObject content = new JSONObject();
    content.put("name", name);
    content.put("description", description);
    content.put("module", module);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(UserUtils.getDuyanId()))
                + "site/update/"
                + siteId
                + "?apikey="
                + commonJson.getString("apikey")
                + "&ttsTypeShowName="
                + ttsType,
            HttpUtils.CONTENT_TYPE_JSON,
            content);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      int status = jsonResult.getIntValue("status");
      if (status == 1) {
        return true;
      }
    }
    return false;
  }

  public JSONArray ttsTypeList() {
    JSONObject commonJson = getCommonJson();
    JSONObject jsonObject = new JSONObject();
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(UserUtils.getDuyanId())) + "tts_type?apikey=" + commonJson.getString("apikey"),
            HttpUtils.CONTENT_TYPE_FORM,
            jsonObject);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONArray jsonArray = jsonResult.getJSONArray("data");
      return jsonArray;
    }
    return null;
  }

  public JSONObject ttsTypeFreeDemo(
      String showName, String volume, Integer speechRate, Integer pitchRate) {
    JSONObject commonJson = getCommonJson();
    JSONObject jsonObject = new JSONObject();
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(UserUtils.getDuyanId()))
                + "tts_type/free_demo?"
                + "apikey="
                + commonJson.getString("apikey")
                + "&showName="
                + showName
                + "&volume="
                + volume
                + "&speechRate="
                + speechRate
                + "&pitchRate="
                + pitchRate,
            HttpUtils.CONTENT_TYPE_FORM,
            jsonObject);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      return jsonResult;
    }
    return null;
  }

  public JSONObject chartInfo(Long campaignId) {
    JSONObject result = new JSONObject();
    JSONObject commonJson = getCommonJson();
    JSONObject content = new JSONObject();
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(UserUtils.getDuyanId()))
                + "campaign/"
                + campaignId
                + "/robot/report?apikey="
                + commonJson.getString("apikey"),
            HttpUtils.CONTENT_TYPE_FORM,
            content);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      result = jsonResult.getJSONObject("data");
    }
    return result;
  }

  public JSONArray robotText(String callUUid) {
    JSONArray result = new JSONArray();
    JSONObject commonJson = getCommonJson();
    commonJson.put("call_uuid", callUUid);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefixV2(),getDuyanServiceKeyByDuyanReferId(UserUtils.getDuyanId())) + "asrSession",
            HttpUtils.CONTENT_TYPE_FORM,
            commonJson);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      result = jsonResult.getJSONArray("data");
    }
    return result;
  }

  /**
   * 获取单条计划详情
   *
   * @param callUUid  呼叫uuid
   * @param robotType 机器人类型
   * @return {@link CallLogDetail}
   */
  public CallLogDetail getCallLogDetail(String callUUid, Integer robotType) {
    if (ObjectUtil.equals(robotType, CasePlanEnums.Type.NEWROBOT.getCode())){
      return newRobotManager.campaignItem(callUUid);
    }
    CallLogDetail callLogDetail = null;
    JSONObject commonJson = getCommonJson();
    commonJson.put("call_uuid", callUUid);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(UserUtils.getDuyanId())) + "call/campaign_item",
            HttpUtils.CONTENT_TYPE_FORM,
            commonJson);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject data = jsonResult.getJSONObject("data");
      if (data != null) {
        callLogDetail = assembleCallLogDetail(data);
      }
    }
    return callLogDetail;
  }

  /**
   * 批量添加号码
   *
   * @param orgId      组织id
   * @param campaignId 活动id
   * @param list       列表
   * @param type       类型
   * @param casePlan   案例计划
   * @param contents 新机器人计划的body
   */
  public void batchAdd(long orgId, long campaignId, List<CaseContact> list, Integer type, CasePlan casePlan, List<Map<String,Object>> contents) {
    if (ObjectUtil.equals(type, CasePlanEnums.Type.NEWROBOT.getCode())){
      newRobotManager.batchAdd(orgId, campaignId, list, contents);
      return;
    }
    if (ObjectUtil.equals(type, CasePlanEnums.Type.PERSONALFIFO.getCode())
    || ObjectUtil.equals(type, CasePlanEnums.Type.FIFO.getCode())) {
      newFifoAppend(orgId, campaignId, list, contents);
      return;
    }

    JSONObject commonJson = getCommonJson(orgId);
    List<CaseContact> param = Lists.newArrayList();
    JSONArray jsonArray = new JSONArray();
    if (!CollectionUtils.isEmpty(list)) {
      list.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile)).forEach((k, v) -> param.add(v.get(0)));
    }
    if (!CollectionUtils.isEmpty(param)) {
      for (CaseContact temp : param) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("U_phone", temp.getContactMobile());
        if (type.equals(CasePlanEnums.Type.ROBOT.getCode())) {
          jsonObject.put("U_name", temp.getContactName());
          jsonObject.put("U_caseId", temp.getCaseId());
          jsonObject.put("U_products", temp.getProductName());
          jsonObject.put("U_orgId", orgId);
          jsonObject.put("U_idcard", temp.getIdCard());
          jsonObject.put("U_amount", temp.getAmount() / 1000.00);
          jsonObject.put("U_entrustendtime", DateUtils.formatDate(temp.getEntrustEndTime(), DateUtils.SIMPLE_DATE_FORMAT));
          jsonObject.put("U_overduedays", temp.getOverdueDays());
          jsonObject.put("U_deltname", deltService.getNames().get(temp.getOrgDeltId()));
        } else if (type.equals(CasePlanEnums.Type.PERSONALFIFO.getCode())) {
          // 度言只支持中文逗号分隔，个人预测式外呼加-1用以区分
          jsonObject.put("U_TAG", temp.getCaseId() + "，" + CasePlanEnums.AllotAgent.PERSONAL.getCode());
        } else {
          // 度言只支持中文逗号分隔
          jsonObject.put("U_TAG", temp.getCaseId() + "，" + casePlan.getAllotAgent());
        }
        jsonArray.add(jsonObject);
      }
    }
    commonJson.put("content", jsonArray.toJSONString());
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DUYAN,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId))
                + campaignId
                + "/batch_add?apikey="
                + commonJson.getString("apikey"),
            HttpUtils.CONTENT_TYPE_JSON,
            commonJson);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      long status = jsonResult.getLongValue("status");
      if (status == 1) {
        log.info("批量添加号码成功");
      }
    }
  }

  public boolean fifoAppend(long orgId, long campaignId, Long caseId, String mobile, Integer type, CasePlan casePlan) {
    JSONObject commonJson = getCommonJson(orgId);
    JSONArray jsonArray = new JSONArray();
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("U_phone", mobile);
    if (type.equals(CasePlanEnums.Type.PERSONALFIFO.getCode())) {
      // 度言只支持中文逗号分隔，个人预测式外呼加-1用以区分
      jsonObject.put("U_TAG", caseId + "，" + CasePlanEnums.AllotAgent.PERSONAL.getCode());
    } else {
      // 度言只支持中文逗号分隔
      jsonObject.put("U_TAG", caseId + "，" + casePlan.getAllotAgent());
    }
    jsonArray.add(jsonObject);
    commonJson.put("content", jsonArray.toJSONString());
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DUYAN,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId))
                + campaignId
                + "/batch_add?apikey="
                + commonJson.getString("apikey"),
            HttpUtils.CONTENT_TYPE_JSON,
            commonJson);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      long status = jsonResult.getLongValue("status");
      if (status == 1) {
        log.info("号码追加成功");
        return true;
      }
    }
    return false;
  }

  /**
   * 批量添加号码
   *
   * @return
   */
  public void newFifoAppend(Long orgId, Long campaignId, List<CaseContact> caseContactList,List<Map<String, Object>> contents) {
    List<CaseContact> caseContacts = Lists.newArrayList();
    if (!CollectionUtils.isEmpty(caseContactList)) {
      caseContactList.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile)).forEach((k, v) -> caseContacts.add(v.get(0)));
    }

    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + String.format(NewDuyanConstant.BATCH_ADD, campaignId);

    BatchAddReq batchAddReq = new BatchAddReq();
    Long duyanId = UserUtils.getDuyanId(orgId);
    batchAddReq.setApikey(getOrgApiKey(duyanId));

    url = url + batchAddReq.params();
    DoRestResult<String> doRestResult;
    try {
      doRestResult = newDuyanInvoker.batchAdd(url, contents);
    } catch (Exception e){
      log.error("调用创建智能计划接口异常", e);
      throw new ApiException("调用创建智能计划接口异常");
    }
    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("调用创建智能计划接口失败");
    }
    valiResponse(doRestResult);

  }

  /**
   * 创建机器人计划
   *
   * @param list  列表
   * @param plan  计划
   * @param orgId 组织id
   * @param contents 新机器人计划的body
   * @return {@link Long}
   */
  public Long createPlan(List<CaseContact> list, CasePlan plan, long orgId, List<Map<String,Object>> contents) {
    if (ObjectUtil.equals(plan.getType(), CasePlanEnums.Type.NEWROBOT.getCode())){
      return newRobotManager.createPlan(plan,orgId, contents);
    }

    JSONObject planJson = getPlanJsonParam(plan, orgId);
    JSONObject jsonContent = new JSONObject();
    JSONArray jsonArray = new JSONArray();
//    List<CaseContact> param = Lists.newArrayList();
//    if (!CollectionUtils.isEmpty(list)) {
//      list.stream().collect(Collectors.groupingBy(CaseContact::getIdCard)).forEach((k, v) -> param.add(v.get(0)));
//    }
    if (!CollectionUtils.isEmpty(list)) {
      for (CaseContact temp : list) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("U_phone", temp.getContactMobile());
        jsonObject.put("U_name", temp.getContactName());
        jsonObject.put("U_caseId", temp.getCaseId());
        jsonObject.put("U_products", temp.getProductName());
        jsonObject.put("U_orgId", orgId);
        jsonObject.put("U_idcard", temp.getIdCard());
        jsonObject.put("U_amount", temp.getAmount() / 1000.00);
        jsonObject.put("U_entrustendtime", DateUtils.formatDate(temp.getEntrustEndTime(), DateUtils.SIMPLE_DATE_FORMAT));
        jsonObject.put("U_overduedays", temp.getOverdueDays());
        jsonObject.put("U_deltname", deltService.getNames().get(temp.getOrgDeltId()));
        jsonArray.add(jsonObject);
      }
    }
    planJson.put("is_extra", true);
    planJson.put("is_distinct", true);
    planJson.put("is_early_media_rec_enabled",CasePlanEnums.CheckPhoneNo.YES.getCode().equals(plan.getCheckPhoneNo()));
    jsonContent.put("content", jsonArray.toJSONString());
    String path = "?";
    Iterator<String> iterator = planJson.keySet().iterator();
    while (iterator.hasNext()) {
      String next = iterator.next();
      if (iterator.hasNext()) {
        path = path + next + "=" + planJson.get(next) + "&";
      } else {
        path = path + next + "=" + planJson.get(next);
      }
    }

    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DUYAN,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId)) + "call/campaign/robot" + path,
            HttpUtils.CONTENT_TYPE_JSON,
            jsonContent);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject data = jsonResult.getJSONObject("data");
      if (!data.isEmpty()) {
        plan.setCaseTotal(data.getInteger("total_count"));
        return data.getLong("id");
      }
    } else {
      throw new ApiException("调用创建智能计划接口失败");
    }
    return null;
  }

  private CallLogDetail assembleCallLogDetail(JSONObject data) {
    CallLogDetail callLogDetail = new CallLogDetail();
    callLogDetail.setCallCount(data.getLong("call_count"));
    callLogDetail.setCaller(data.getString("caller"));
    callLogDetail.setCallTime(new Date(data.getLongValue("call_time")));
    callLogDetail.setCallUUid(data.getString("call_uuid"));
    callLogDetail.setCampaignId(data.getLong("campaign_id"));
    callLogDetail.setDuration(data.getLong("duration"));
    callLogDetail.setOutcome(data.getString("outcome"));
    callLogDetail.setPhone(data.getString("phone"));
    callLogDetail.setTag(data.getString("tag"));
    callLogDetail.setVars(data.getJSONObject("variables").toJSONString());
    return callLogDetail;
  }

  // 创建预测式计划
  public Long createFifo(List<CaseContact> param, CasePlan plan, Long fifoDuyanTeamId) {
    // content客户数据
    JSONObject jsonContent = new JSONObject();
    JSONArray jsonArray = new JSONArray();
    if (!CollectionUtils.isEmpty(param)) {
      for (CaseContact temp : param) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("U_phone", temp.getContactMobile());
        // 度言只支持中文逗号分隔
        jsonObject.put("U_TAG", temp.getCaseId() + "，" + plan.getAllotAgent());
        jsonArray.add(jsonObject);
      }
    }
    jsonContent.put("content", jsonArray.toJSONString());
    // 其他参数
    JSONObject planJson = getCommonJson(plan.getOrgId());
    planJson.put("is_extra", true);
    planJson.put("name", plan.getName());
    planJson.put("teamId", fifoDuyanTeamId);
    //如果是团队线路池那么传poolId即可，此时caller是团队线路池名称，不用传，但是保存在安米本地方便前端展示
    if (plan.getPoolId() != null) {
      planJson.put("pool_id", plan.getPoolId());
    } else {
      if (!StringUtils.isEmpty(plan.getCaller())) {
        planJson.put("caller", plan.getCaller());
      }
    }
    planJson.put("priority", plan.getPriority());
    planJson.put("is_distinct", true);
    planJson.put("is_early_media_rec_enabled",CasePlanEnums.CheckPhoneNo.YES.getCode().equals(plan.getCheckPhoneNo()));
    String path = "?";
    Iterator<String> iterator = planJson.keySet().iterator();
    while (iterator.hasNext()) {
      String next = iterator.next();
      if (iterator.hasNext()) {
        path = path + next + "=" + planJson.get(next) + "&";
      } else {
        path = path + next + "=" + planJson.get(next);
      }
    }

    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DUYAN,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(plan.getOrgId())) + "call/campaign/fifo" + path,
            HttpUtils.CONTENT_TYPE_JSON,
            jsonContent);
    Long duyanPlanId = null;
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject data = jsonResult.getJSONObject("data");
      if (!data.isEmpty()) {
        plan.setCaseTotal(data.getInteger("total_count"));
        duyanPlanId = data.getLong("id");
      }
    } else {
      throw new ApiException("调用创建预测式计划接口失败");
    }
    return duyanPlanId;
  }

  /**
   * 创建团队预测式计划
   *
   * @param param    联系人信息
   * @param plan     计划
   * @param teamId   团队id
   * @param contents 机器人body
   * @return
   */
  public Long createNewFifo(List<CaseContact> param, CasePlan plan, Long teamId, List<Map<String, Object>> contents) {
    AssertUtil.notNull(plan.getOrgId(),"orgId不可为空！");
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(plan.getOrgId()));
    String url = duyanUrlPrefix + NewDuyanConstant.FIFO_CREATE;

    // content客户数据
    CreateFifoReq createFifoReq = new CreateFifoReq();
    Long duyanId = UserUtils.getDuyanId(plan.getOrgId());
    createFifoReq.setApikey(getOrgApiKey(duyanId));
    createFifoReq.setName(plan.getName())
            .setTeam_Id(teamId);
    // 计划是否立即执行
    if (ObjectUtil.equals(plan.getStartType(), 1)) {
      createFifoReq.setIs_start_now(Boolean.TRUE);
    }else {
      createFifoReq.setIs_start_now(Boolean.FALSE);
    }
    // 号码校验、优先级、外呼比例、机器花束
    createFifoReq.setIs_early_media_rec_enabled(CasePlanEnums.CheckPhoneNo.YES.getCode().equals(plan.getCheckPhoneNo()))
            .setPriority(CasePlanEnums.Priority.getCodeByOldCode(plan.getPriority()))
            .setChannel_limit(plan.getChannelLimit());
    // 如果是团队线路池那么传poolId即可，此时caller是团队线路池名称，不用传，但是保存在安米本地方便前端展示
    if (ObjectUtil.isNotNull(plan.getPoolId())) {
      createFifoReq.setPool_id(plan.getPoolId());
    } else if (StrUtil.isNotBlank(plan.getCaller())){
      createFifoReq.setCaller(plan.getCaller());
    }
    // 失败重试
    if (ObjectUtil.equals(CasePlanEnums.FailRetry.OPEN.getCode(), plan.getFailRetry())){
      createFifoReq.setRetry_count(ObjectUtil.isNull(plan.getRetryCount())?null: plan.getRetryCount())
              .setRetry_interval(ObjectUtil.isNull(plan.getRetryInterval())?null: plan.getRetryInterval())
              .setRetry_conditions(StrUtil.isBlank(plan.getRetryConditions())?null: plan.getRetryConditions());
    }
    // 团队预测式外呼转机器人 未开启fifo转robot功能时，字段传入无效
    if (ObjectUtil.equals(CasePlanEnums.FifoToRobotEnabled.OPEN.getCode(), plan.getFifoToRobotEnabled())) {
      createFifoReq.setSite_id(plan.getSiteId());
    } else {
      if (!CollectionUtils.isEmpty(param)) {
        for (CaseContact temp : param) {
          Map<String, Object> content = new HashMap<>();
          content.put("U_phone", temp.getContactMobile());
          // 度言只支持中文逗号分隔
          content.put("U_TAG", temp.getCaseId() + "，" + plan.getAllotAgent());
          contents.add(content);
        }
      }
    }

//    List<CreateFifoReq.Content> contents = new ArrayList<>();
//    if (!CollectionUtils.isEmpty(param)) {
//      for (CaseContact temp : param) {
//        CreateFifoReq.Content content= new CreateFifoReq.Content();
//        content.setU_phone(temp.getContactMobile());
//        // 度言只支持中文逗号分隔
//        content.setU_TAG(temp.getCaseId() + "，" + plan.getAllotAgent());
//        contents.add(content);
//      }
//    }

    // 调用度言创建接口
    DoRestResult<CreateFifoRes> doRestResult;
    url = url + createFifoReq.params();
    try {
      doRestResult = newDuyanInvoker.createFifo(url,contents);
    } catch (Exception e) {
      log.error("调用创建团队预测式计划接口异常", e);
      throw new ApiException("调用创建团队预测式计划接口异常");
    }
    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("调用创建团队预测式计划接口异常");
    }
    valiResponse(doRestResult);
    CreateFifoRes resData = doRestResult.getData();
    if (resData != null) {
      plan.setCaseTotal(resData.getTotal_count().intValue());
      plan.setChannelLimit(resData.getChannel_limit().doubleValue());
      return resData.getId();
    }
    return null;
  }

  // 创建个人预测式计划
  public Long createPersonalFifo(List<CaseContact> param, CasePlan plan, Long duyanAccountId) {
    // content客户数据
    JSONObject jsonContent = new JSONObject();
    JSONArray jsonArray = new JSONArray();
    if (!CollectionUtils.isEmpty(param)) {
      for (CaseContact temp : param) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("U_phone", temp.getContactMobile());
        // 度言只支持中文逗号分隔，个人预测式外呼加-1用以区分
        jsonObject.put("U_TAG", temp.getCaseId() + "，" + CasePlanEnums.AllotAgent.PERSONAL.getCode());
        jsonArray.add(jsonObject);
      }
    }
    jsonContent.put("content", jsonArray.toJSONString());
    // 其他参数
    JSONObject planJson = getCommonJson(plan.getOrgId());
    planJson.put("is_extra", true);
    planJson.put("name", plan.getName());
    // 度言用户id
    planJson.put("account_id", duyanAccountId);
    //如果是团队线路池那么传poolId即可，此时caller是团队线路池名称，不用传，但是保存在安米本地方便前端展示
    if (plan.getPoolId() != null) {
      planJson.put("pool_id", plan.getPoolId());
    } else {
      if (!StringUtils.isEmpty(plan.getCaller())) {
        planJson.put("caller", plan.getCaller());
      }
    }
    planJson.put("priority", plan.getPriority());
    planJson.put("is_distinct", true);
    planJson.put("is_early_media_rec_enabled",CasePlanEnums.CheckPhoneNo.YES.getCode().equals(plan.getCheckPhoneNo()));
    String path = "?";
    Iterator<String> iterator = planJson.keySet().iterator();
    while (iterator.hasNext()) {
      String next = iterator.next();
      if (iterator.hasNext()) {
        path = path + next + "=" + planJson.get(next) + "&";
      } else {
        path = path + next + "=" + planJson.get(next);
      }
    }

    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_DUYAN,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(plan.getOrgId())) + "call/campaign/personalFifo" + path,
            HttpUtils.CONTENT_TYPE_JSON,
            jsonContent);
    Long duyanPlanId = null;
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject data = jsonResult.getJSONObject("data");
      if (!data.isEmpty()) {
        plan.setCaseTotal(data.getInteger("total_count"));
        duyanPlanId = data.getLong("id");
      }
    } else {
      throw new ApiException("调用创建预测式计划接口失败");
    }
    return duyanPlanId;
  }

  /**
   * 创建个人预测式外呼
   *
   * @param param
   * @param plan
   * @param duyanAccountId
   * @return
   */
  public Long createNewPersonalFifo(List<CaseContact> param, CasePlan plan, Long duyanAccountId, List<Map<String, Object>> contents) {
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(plan.getOrgId()));
    String url = duyanUrlPrefix + NewDuyanConstant.PERSONAL_FIFO_CREATE;

    CreatePersonalFifoReq createPersonalFifoReq = new CreatePersonalFifoReq();

    Long duyanId = UserUtils.getDuyanId(plan.getOrgId());
    createPersonalFifoReq.setApikey(getOrgApiKey(duyanId));
    createPersonalFifoReq.setName(plan.getName())
            .setAccount_id(duyanAccountId);
    // 计划是否立即执行
    if (ObjectUtil.equals(plan.getStartType(), 1)) {
      createPersonalFifoReq.setIs_start_now(Boolean.TRUE);
    }else {
      createPersonalFifoReq.setIs_start_now(Boolean.FALSE);
    }
    // 号码校验、优先级、外呼比例
    createPersonalFifoReq.setIs_early_media_rec_enabled(CasePlanEnums.CheckPhoneNo.YES.getCode().equals(plan.getCheckPhoneNo()))
            .setPriority(CasePlanEnums.Priority.getCodeByOldCode(plan.getPriority()))
            .setChannel_limit(plan.getChannelLimit());

    // 如果是团队线路池那么传poolId即可，此时caller是团队线路池名称，不用传，但是保存在安米本地方便前端展示
    if (ObjectUtil.isNotNull(plan.getPoolId())) {
      createPersonalFifoReq.setPool_id(plan.getPoolId());
    } else if (StrUtil.isNotBlank(plan.getCaller())){
      createPersonalFifoReq.setCaller(plan.getCaller());
    }
    // 失败重试
    if (ObjectUtil.equals(CasePlanEnums.FailRetry.OPEN.getCode(), plan.getFailRetry())){
      createPersonalFifoReq.setRetry_count(ObjectUtil.isNull(plan.getRetryCount())?null: plan.getRetryCount())
              .setRetry_interval(ObjectUtil.isNull(plan.getRetryInterval())?null: plan.getRetryInterval())
              .setRetry_conditions(StrUtil.isBlank(plan.getRetryConditions())?null: plan.getRetryConditions());
    }
    // 机器人话术编号，等待客户接入机器人时需要接入话术的编号（须公司先开启等待客户接入机器人功能）
    if (ObjectUtil.equals(plan.getFifoToRobotEnabled(), 1)) {
      createPersonalFifoReq.setSite_id(plan.getSiteId());
    } else {
      if (!CollectionUtils.isEmpty(param)) {
        for (CaseContact temp : param) {
          Map<String, Object> content = new HashMap<>();
          content.put("U_phone" , temp.getContactMobile());
          // 度言只支持中文逗号分隔
          content.put("U_TAG" , temp.getCaseId() + "，" + CasePlanEnums.AllotAgent.PERSONAL.getCode());
          contents.add(content);
        }
      }
    }

    /*List<CreatePersonalFifoReq.Content> contents = new ArrayList<>();
    if (!CollectionUtils.isEmpty(param)) {
      for (CaseContact temp : param) {
        CreatePersonalFifoReq.Content content = new CreatePersonalFifoReq.Content();
        // 度言只支持中文逗号分隔，个人预测式外呼加-1用以区分
        content.setU_phone(temp.getContactMobile())
                .setU_TAG(temp.getCaseId() + "，" + CasePlanEnums.AllotAgent.PERSONAL.getCode());
        contents.add(content);
      }
    }
    createPersonalFifoReq.setContent(contents);*/

    // 调用度言创建接口
    DoRestResult<CreatePersonalFifoRes> doRestResult;
    url = url + createPersonalFifoReq.params();
    try {
      doRestResult = newDuyanInvoker.createPersonalFifo(url,contents);
    } catch (Exception e) {
      log.error("调用创建个人预测式计划接口异常", e);
      throw new ApiException("调用创建个人预测式计划接口异常");
    }
    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("调用创建个人预测式计划接口异常");
    }
    valiResponse(doRestResult);
    CreatePersonalFifoRes resData = doRestResult.getData();
    if (resData != null) {
      plan.setCaseTotal(resData.getTotal_count().intValue());
      plan.setChannelLimit(resData.getChannel_limit().doubleValue());
      return resData.getId();
    }
    return null;
  }

  /**
   * 获取计划列表(通过外呼计划编号)
   *
   * @param duyanPlanIds 度言计划id
   * @param planType     计划类型
   * @return {@link List}<{@link CasePlanTmp}>
   */
  public List<CasePlanTmp> getPlanListByIds(List<Long> duyanPlanIds, Integer planType) {
    if (ObjectUtil.equals(planType, CasePlanEnums.Type.NEWROBOT.getCode())){
      return newRobotManager.getPlanListByIds(duyanPlanIds);
    }
    if (ObjectUtil.equals(planType, CasePlanEnums.Type.FIFO.getCode()) || ObjectUtil.equals(planType, CasePlanEnums.Type.PERSONALFIFO.getCode())) {
      return getNewPlanListByIds(duyanPlanIds);
    }
    return getPlanListByIds(duyanPlanIds);
  }

  /**
   * 获取预测式计划列表
   * 度言接口因分页设置最多一次查询最多20个，由于前端分页可能会一次性查询200个，此处做截断后再查询
   *
   * @param duyanPlanIds 度言ids
   * @return
   */
  public List<CasePlanTmp> getNewPlanListByIds(List<Long> duyanPlanIds) {
    Long orgId = UserUtils.getTokenUser().getOrgId();
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + NewRobotConstant.PLAN_LIST_BY_IDS;

    List<GetPlanBaseInfoRes> resDataList = new ArrayList<>();
    List<List<Long>> groupList = CmUtil.splitList(duyanPlanIds, 20);
    DoRestResult<List<GetPlanBaseInfoRes>> doRestResult;
    for (List<Long> subIds : groupList) {
      try {
        doRestResult = newDuyanInvoker.getPlanListByIds(url,getOrgApiKey(), org.apache.commons.lang3.StringUtils.join(subIds, ","));
      }catch (Exception e){
        log.error("调用获取计划列表接口异常", e);
        throw new ApiException("调用获取计划列表接口异常");
      }
      if (ObjectUtil.isNull(doRestResult)) {
        throw new ApiException("调用获取计划列表接口失败");
      }
      valiResponse(doRestResult);
      List<GetPlanBaseInfoRes> resDatas = Optional.ofNullable(doRestResult.getData()).orElse(new ArrayList<>());
      resDataList.addAll(resDatas);
    }
    List<CasePlanTmp> casePlanTmps = resDataList.stream().map(planListRes -> {
      CasePlanTmp casePlanTmp = new CasePlanTmp();
      casePlanTmp.setCaller(planListRes.getCaller());
      casePlanTmp.setDuyanPlanId(planListRes.getId());
      casePlanTmp.setPlanStartTime(ObjectUtil.isNull(planListRes.getStart_time())?null:new Date(planListRes.getStart_time()));
      casePlanTmp.setPlanEndTime(ObjectUtil.isNull(planListRes.getEnd_time())?null:new Date(planListRes.getEnd_time()));
      casePlanTmp.setFinishedCount(planListRes.getFinished_count());
      casePlanTmp.setTotalCount(planListRes.getTotal_count());
      casePlanTmp.setPlanStatus(CasePlanEnums.DuyanPlanStatus.getOldCodeByCode(planListRes.getStatus()));
      casePlanTmp.setPlanCreatedDate(planListRes.getCreated_time());
      casePlanTmp.setNowRounds(planListRes.getNow_rounds());
      casePlanTmp.setRetryCount(planListRes.getRetry_count());
      casePlanTmp.setNowRoundCount(planListRes.getNow_round_count());
      return casePlanTmp;
    }).collect(Collectors.toList());
    return casePlanTmps;
  }

  /**
   * 获取计划列表(通过外呼计划编号)
   *
   * @param caseIdList 案例id列表
   * @return {@link List}<{@link CasePlanTmp}>
   */
  public List<CasePlanTmp> getPlanListByIds(List<Long> caseIdList) {
    if (CollectionUtils.isEmpty(caseIdList)) {
      return new ArrayList<>();
    }
    List<CasePlanTmp> tmps = new ArrayList<>();
    JSONObject commonJson = getCommonJson();
    commonJson.put("id_list", StringUtils.join(caseIdList, ","));
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_GET,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(UserUtils.getDuyanId())) + "campaign/list",
            HttpUtils.CONTENT_TYPE_FORM,
            commonJson);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONArray data = jsonResult.getJSONArray("data");
      if (data != null) {
        tmps = data.stream().map(this::convertTmp).collect(Collectors.toList());
      }
    }
    return tmps;
  }

  // 创建预测式计划
  public void removePlanItem(Long orgId, Long campaignId, String mobile, Long updateBy) {
    // 请求参数
    JSONObject pathJson = getCommonJson(orgId);
    pathJson.put("agent_id", updateBy);
    String path = "?";
    Iterator<String> iterator = pathJson.keySet().iterator();
    while (iterator.hasNext()) {
      String next = iterator.next();
      if (iterator.hasNext()) {
        path = path + next + "=" + pathJson.get(next) + "&";
      } else {
        path = path + next + "=" + pathJson.get(next);
      }
    }
    JSONObject body = new JSONObject();
    body.put("campaign_id", campaignId);
    body.put("phone_number", mobile);

    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId)) + "campaign/removeItem" + path,
            HttpUtils.CONTENT_TYPE_JSON,
            body);
    if (!isEmpty(referResponse)) {
      JSONObject jsonResult = JSONObject.parseObject(referResponse);
      valiResponse(jsonResult);
      JSONObject data = jsonResult.getJSONObject("data");
      if (data.isEmpty()
          || data.getJSONArray("success_id_list") == null
          || data.getJSONArray("success_id_list").isEmpty()) {
        throw new ApiException("调用度言接口失败，手机号移除计划失败,mobile:" + mobile + "duyanPlanId:" + campaignId);
      }
    } else {
      throw new ApiException("调用度言接口未返回，手机号移除计划失败,mobile:" + mobile + "duyanPlanId:" + campaignId);
    }
  }

  /**
   * 发送短信
   * 发送短信不加分区
   * @param mobile
   * @param content
   * @param isVoice
   * @return
   */
  public int sendSms(String mobile, String content, Boolean isVoice) {
    JSONObject json = new JSONObject();
    json.put("apikey", AESUtil.decrypt(systemConfig.getDuyanMessageApiKey()));
    json.put("mobile", mobile);
    json.put("content", content);
    json.put("is_voice", isVoice);
    String referResponse =
        HttpUtils.requestBase(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), "") + "sms/token",
            HttpUtils.CONTENT_TYPE_JSON,
            json,
            true);
    if (!isEmpty(referResponse)) {
      JSONObject object = JSONObject.parseObject(referResponse);
      validateDuYanResponse(object);
    }
    log.info("短信发送成功");
    return 1;
  }

  private void validateDuYanResponse(JSONObject jsonResult) {
    Integer code = jsonResult.getInteger("status");
    if (code != null && code.intValue() != 1) {
      JSONObject jsondata = JSON.parseObject(jsonResult.getString("data"));
      if (jsondata != null) {
        log.error("调用发送短信接口失败", jsondata.getString("msg") + " " + jsondata.getString("detail"));
        throw new ApiException(jsondata.getString("msg") + " " + jsondata.getString("detail"));
      } else {
        if (jsonResult.getString("message") != null) {
          throw new ApiException(jsonResult.getString("message"));
        }
      }
    }
  }

  /**
   * 返回md5
   *
   * @param text
   * @param mobile
   * @param salt
   * @return
   */
  public String generatePwdAndSendSms(String text, String mobile, String salt) {
    // 发送短信
    String randomPwd = UserUtils.generateRandomPwd();
    yunpianManager.sendSms(mobile, String.format(text, randomPwd), false);
    String pwd = UserUtils.generateMd5(salt, randomPwd);
    return pwd;
  }

  public Long createDuyanUserAndRole(
      User user, Long duyanTeamId, List<Long> inspectorTeamIds, Long updateBy) {
    // 请求参数
    JSONObject pathJson = getCommonJson(user.getOrgId());
    pathJson.put("agent_id", updateBy);
    String path = "?";
    Iterator<String> iterator = pathJson.keySet().iterator();
    while (iterator.hasNext()) {
      String next = iterator.next();
      if (iterator.hasNext()) {
        path = path + next + "=" + pathJson.get(next) + "&";
      } else {
        path = path + next + "=" + pathJson.get(next);
      }
    }
    JSONObject body = new JSONObject();
    body.put("name", user.getName());
    body.put("mobile", user.getMobile());
    body.put("title", "");
    body.put("isAdmin", user.getDuyanAdmin() == 1 ? true : false);
    body.put("isAgent", user.getDuyanSeat() == 1 ? true : false);
    body.put("isSupervisor", user.getDuyanTeamLeader() == 1 ? true : false);
    body.put("isInspector", user.getDuyanInspector() == 1 ? true : false);
    body.put("isInspectorSup", user.getDuyanInspectorSup() == 1 ? true : false);
    body.put("isFifoAgent", user.getDuyanFifoAgent() == 1 ? true : false);
    if (duyanTeamId != null) {
      body.put("teamId", duyanTeamId);
    }
    if (inspectorTeamIds != null) {
      body.put("inspectorTeamIds", inspectorTeamIds);
    }

    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(user.getOrgId())) + "agent/org" + path,
            HttpUtils.CONTENT_TYPE_JSON,
            body);
    if (isEmpty(referResponse)) {
      throw new ApiException("调用度言接口未返回，创建用户失败");
    }
    JSONObject jsonResult = JSONObject.parseObject(referResponse);
    valiResponse(jsonResult);
    JSONObject data = jsonResult.getJSONObject("data");
    if (data.isEmpty() || data.getLong("account_id") == null) {
      throw new ApiException("调用度言接口返回错误，创建用户失败");
    }
    return data.getLong("account_id");
  }

  public void updateDuyanUserAndRole(
      User user, Long duyanTeamId, List<Long> inspectorTeamIds, Long updateBy) {
    // 请求参数
    JSONObject pathJson = getCommonJson(user.getOrgId());
    pathJson.put("agent_id", updateBy);
    String path = "?";
    Iterator<String> iterator = pathJson.keySet().iterator();
    while (iterator.hasNext()) {
      String next = iterator.next();
      if (iterator.hasNext()) {
        path = path + next + "=" + pathJson.get(next) + "&";
      } else {
        path = path + next + "=" + pathJson.get(next);
      }
    }
    JSONObject body = new JSONObject();
    body.put("name", user.getName());
    body.put("mobile", user.getMobile());
    body.put("title", "");
    body.put("isAdmin", user.getDuyanAdmin() == 1 ? true : false);
    body.put("isAgent", user.getDuyanSeat() == 1 ? true : false);
    body.put("isSupervisor", user.getDuyanTeamLeader() == 1 ? true : false);
    body.put("isInspector", user.getDuyanInspector() == 1 ? true : false);
    body.put("isInspectorSup", user.getDuyanInspectorSup() == 1 ? true : false);
    body.put("isFifoAgent", user.getDuyanFifoAgent() == 1 ? true : false);
    if (duyanTeamId != null) {
      body.put("teamId", duyanTeamId);
    }
    if (inspectorTeamIds != null) {
      body.put("inspectorTeamIds", inspectorTeamIds);
    }

    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(user.getOrgId()))
                + "agent/org/modify/"
                + user.getDuyanAccountId()
                + path,
            HttpUtils.CONTENT_TYPE_JSON,
            body);
    if (isEmpty(referResponse)) {
      throw new ApiException("调用度言接口未返回，修改用户失败");
    }
    JSONObject jsonResult = JSONObject.parseObject(referResponse);
    valiResponse(jsonResult);
  }

  public void mobileOnlineSwitch(Long duyanReferId, Long duyanAccountId, Boolean isMobileOnline) {
    JSONObject param = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    param.put("apikey", apiKey);
    param.put("account_id", duyanAccountId);
    param.put("is_mobile_online", isMobileOnline);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "agent/mobile_online/switch",
            HttpUtils.CONTENT_TYPE_FORM,
            param);
    if (isEmpty(referResponse)) {
      throw new ApiException("调用度言接口未返回");
    }
    JSONObject jsonResult = JSONObject.parseObject(referResponse);
    if (-1 == jsonResult.getIntValue("status")) {
      throw new ApiException("合作方密钥不合法，请联系管理员处理");
    }
    if (0 == jsonResult.getIntValue("status")) {
      throw new ApiException(jsonResult.getString("message"));
    }
  }

  public String registerMobile(Long duyanReferId, Long duyanAccountId, String mobile, String tag) {
    JSONObject param = new JSONObject();
    String apiKey = getOrgApiKey(duyanReferId);
    if (isEmpty(apiKey)) {
      throw new ApiException("合作方密钥不存在,请联系管理员");
    }
    param.put("apikey", apiKey);
    param.put("account_id", duyanAccountId);
    param.put("target", mobile);
    param.put("tag", tag);
    String referResponse =
        HttpUtils.requset(
            HttpUtils.METHOD_POST,
            String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "call/register/mobile",
            HttpUtils.CONTENT_TYPE_FORM,
            param);

    if (isEmpty(referResponse)) {
      throw new ApiException("调用度言接口未返回");
    }
    JSONObject jsonResult = JSONObject.parseObject(referResponse);
    valiResponse(jsonResult);
    JSONObject data = jsonResult.getJSONObject("data");
    if (data.isEmpty() || data.getString("mobile_target") == null) {
      throw new ApiException("调用度言接口返回错误");
    }
    return data.getString("mobile_target");
  }


  /**
   * 查询度言销售人员信息
   */
  public List<SalesDTO> getDuYanSales() {
    String duyanApikey = systemConfig.getDuyanApikey(null);//默认分区的key
    JSONObject param = new JSONObject();
    param.put("apikey", duyanApikey);
    String url = systemConfig.getDuyanUrlPrefix();
    url = String.format(url, "") + "sales";
    String result =
            HttpUtils.requset(
                    HttpUtils.METHOD_GET,
                    url,
                    HttpUtils.CONTENT_TYPE_FORM,
                    param);
    if (StringUtils.isBlank(result)) {
      throw new ApiException("调用度言获取销售信息接口失败");
    }
    ResultMessage resultMessage = JSONObject.parseObject(result, ResultMessage.class);
    if (Objects.equals(resultMessage.getStatus(), 1)) {
        String data = resultMessage.getData().toString();
        List<SalesDTO> sales = JSONObject.parseArray(data, SalesDTO.class);
        return sales;
    }
    throw new ApiException("调用度言获取销售信息接口返回错误");
  }

  public DuyanOrg getDuYanOrgInfo(Long orgId, String duyanServiceKey) {
    if (StringUtils.isBlank(duyanServiceKey)) {
      duyanServiceKey = "";
    }
    JSONObject param = new JSONObject();
    param.put("apikey", systemConfig.getDuyanApikey(duyanServiceKey));
    param.put("org_id", orgId);
    param.put("call_log_receive_url", systemConfig.getDuyanCallbackUrl());
    param.put("campaign_notify_tag_url", systemConfig.getDuyanTagCallbackUrl());
    String url = systemConfig.getDuyanUrlPrefix();
    url = String.format(url, duyanServiceKey) + "org/anmi/init?";
    String result =
            HttpUtils.requset(
                    HttpUtils.METHOD_POST,
                    url,
                    HttpUtils.CONTENT_TYPE_FORM,
                    param);
    if (isEmpty(result)) {
      throw new ApiException("调用度言获取公司信息接口失败");
    }
    ResultMessage resultMessage = JSONObject.parseObject(result, ResultMessage.class);
    if (Objects.equals(resultMessage.getStatus(), 1)) {
      String data = resultMessage.getData().toString();
      DuyanOrg duyanOrg = JSONObject.parseObject(data, DuyanOrg.class);
      return duyanOrg;
    }
    throw new ApiException("调用度言获取公司信息接口返回数据错误");
  }

  /**
   * 查询度言公司的电话线路池
   * @param duyanReferId
   */
  public DuYanPhoneLineDTO getOrgPhoneLine(long duyanReferId) {
    String orgApiKey = getOrgApiKey(duyanReferId);
    String url = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "phoneline";
    JSONObject param = new JSONObject();
    param.put("apikey", orgApiKey);
    String result =
            HttpUtils.requset(
                    HttpUtils.METHOD_GET,
                    url,
                    HttpUtils.CONTENT_TYPE_FORM,
                    param);
    if (StringUtils.isBlank(result)) {
      throw new ApiException("调用度言获取公司电话线路池接口失败");
    }
    ResultMessage resultMessage = JSONObject.parseObject(result, ResultMessage.class);
    if (Objects.equals(resultMessage.getStatus(), 1)) {
      String data = resultMessage.getData().toString();
      DuYanPhoneLineDTO duYanPhoneLineDTO = JSON.parseObject(data, DuYanPhoneLineDTO.class);
      return duYanPhoneLineDTO;
    }
    throw new ApiException("调用度言获取公司电话线路池信息接口返回数据错误");
  }


  public Campaigns getCallInfo(String callUuid, Long duyanReferId) {
    String orgApiKey = getOrgApiKey(duyanReferId);
    JSONObject param = new JSONObject();
    param.put("apikey", orgApiKey);
    param.put("call_uuid", callUuid);
    String url = systemConfig.getDuyanUrlPrefix();
    url = String.format(url, "") + "call_log";
    String result =
            HttpUtils.requset(
                    HttpUtils.METHOD_GET,
                    url,
                    HttpUtils.CONTENT_TYPE_FORM,
                    param);
    if (StringUtils.isBlank(result)) {
      throw new ApiException("调用度言获取电话录音信息接口失败");
    }
    ResultMessage resultMessage = JSONObject.parseObject(result, ResultMessage.class);
    if (Objects.equals(resultMessage.getStatus(), 1)) {
      String data = resultMessage.getData().toString();
      Campaigns campaigns = JSONObject.parseObject(data, Campaigns.class);
      return campaigns;
    }
    throw new ApiException("调用度言获取电话录音信息接口返回错误");
  }

  public OrgKeyDTO getOrgConfig(Long duyanReferId) {
    JSONObject org = new JSONObject();
    org.put("apikey", systemConfig.getDuyanApikey(getDuyanServiceKeyByDuyanReferId(duyanReferId)));
    org.put("org_id", duyanReferId);
    String result =
            HttpUtils.requset(
                    HttpUtils.METHOD_GET,
                    String.format(systemConfig.getDuyanUrlPrefix(),getDuyanServiceKeyByDuyanReferId(duyanReferId)) + "org/apikey",
                    HttpUtils.CONTENT_TYPE_FORM,
                    org);
    if (StringUtils.isBlank(result)) {
      throw new ApiException("调用度言获取api配置接口失败");
    }
    ResultMessage<OrgKeyDTO> resultMessage = JSONObject.parseObject(result, new TypeReference<ResultMessage<OrgKeyDTO>>(){});
    if (Objects.equals(resultMessage.getStatus(), 1)) {
      return resultMessage.getData();
    }
    throw new ApiException("调用度言获取api配置接口返回错误");
  }

  public PageOutput<CallRecordDTO> getQosCallRecord(Long duyanReferId, QosParam qosParam) {
    OrgKeyDTO orgKeyDTO = getOrgConfig(duyanReferId);
    String apikey = orgKeyDTO.getApikey();
    Object appSecret = orgKeyDTO.getAppSecret();
    // 下面点qos质检平台接口
    JSONObject param = new JSONObject();
    param.put("appkey", apikey);
    String nonce = UUID.randomUUID().toString();
    long curTime = System.currentTimeMillis();
    List<String> sortList = new ArrayList<>(3);
    sortList.add(String.valueOf(curTime));
    sortList.add(nonce);
    sortList.add(appSecret.toString());
    Collections.sort(sortList);
    String str = String.join("", sortList);
    String sign = MD5Util.digest(str);
    param.put("nonce", nonce);
    param.put("t", curTime);
    param.put("sign", sign);
    if (qosParam.getStartTime() != null) {
      param.put("start_time", qosParam.getStartTime());
    }
    if (qosParam.getEndTime() != null) {
      param.put("end_time", qosParam.getEndTime());
    }
    if (qosParam.getLimit() != null) {
      param.put("page_size", qosParam.getLimit());
    }
    if (qosParam.getPage() != null) {
      param.put("page_num",qosParam.getPage());
    }
    if (qosParam.getMinDuration() != null) {
      param.put("min_duration", qosParam.getMinDuration());
    }
    if (qosParam.getMaxDuration() != null) {
      param.put("max_duration", qosParam.getMaxDuration());
    }
    if (qosParam.getMinScore() != null) {
      param.put("min_score", qosParam.getMinScore());
    }
    if (qosParam.getMaxScore() != null) {
      param.put("max_score", qosParam.getMaxScore());
    }
    if (StringUtils.isNotBlank(qosParam.getTargetNumber())) {
      param.put("target_number", qosParam.getTargetNumber());
    }
    if (qosParam.getTeamId() != null) {
      param.put("team_id", qosParam.getTeamId());
    }
    if (qosParam.getAgentId() != null) {
      param.put("agent_id", qosParam.getAgentId());
    }
    if (qosParam.getQosStatus() != null) {
      param.put("qos_status", qosParam.getQosStatus());
    }
    if (qosParam.getIsEarlyWarning() != null) {
      param.put("is_early_warning", qosParam.getIsEarlyWarning());
    }
    if (qosParam.getIsHangUp() != null) {
      param.put("is_hang_up", qosParam.getIsHangUp());
    }
    String result = HttpUtils.requset(
            HttpUtils.METHOD_GET,
            systemConfig.getQosUrlPrefix() + "callRecord/queryByParam",
            HttpUtils.CONTENT_TYPE_FORM,
            param);
    if (StringUtils.isBlank(result)) {
      throw new ApiException("调用质检平台获取通话记录接口失败");
    }
    ResultMessage<PageOutput<CallRecordDTO>> resultMessage = JSONObject.parseObject(result, new TypeReference<ResultMessage<PageOutput<CallRecordDTO>>>() {});
    if (Objects.equals(resultMessage.getStatus(), 200)) {
      return resultMessage.getData();
    }
    throw new ApiException("调用质检平台获取通话记录接口返回错误");
  }

  public CallDetailVO getCallDetail(Long duyanReferId, String callUuid) {
    OrgKeyDTO orgKeyDTO = getOrgConfig(duyanReferId);
    String apikey = orgKeyDTO.getApikey();
    Object appSecret = orgKeyDTO.getAppSecret();

    // 下面点qos质检平台接口
    JSONObject param = new JSONObject();
    param.put("appkey", apikey);
    String nonce = UUID.randomUUID().toString();
    long curTime = System.currentTimeMillis();
    List<String> sortList = new ArrayList<>(3);
    sortList.add(String.valueOf(curTime));
    sortList.add(nonce);
    sortList.add(appSecret.toString());
    Collections.sort(sortList);
    String str = String.join("", sortList);
    String sign = MD5Util.digest(str);
    param.put("nonce", nonce);
    param.put("t", curTime);
    param.put("sign", sign);
    param.put("uuid", callUuid);
    String result = HttpUtils.requset(
            HttpUtils.METHOD_GET,
            systemConfig.getQosUrlPrefix() + "callLog/query",
            HttpUtils.CONTENT_TYPE_FORM,
            param);
    if (StringUtils.isBlank(result)) {
      throw new ApiException("调用质检平台获取通话详情接口失败");
    }
    ResultMessage<CallDetailVO> resultMessage = JSONObject.parseObject(result, new TypeReference<ResultMessage<CallDetailVO>>() {});
    if (Objects.equals(resultMessage.getStatus(), 200)) {
      return resultMessage.getData();
    } else {
      JSONObject rst = JSON.parseObject(result);
      Object code = rst.get("code");
      Integer value = code == null ? 0 : Integer.valueOf(code.toString());
      // 如果返回数据报错，该uuid通话记录没有质检过，则返回null给前端，50001是质检平台对应没有质检过的异常code码
      if (Objects.equals(value, 50001)) {
        return null;
      }
    }
    throw new ApiException("调用质检平台获取通话详情接口返回错误");

  }


  /**
   * 批量移除计划中的手机号
   * @param orgId
   * @param duyanPlanId
   * @param mobiles
   * @param updateBy
   */
  public void removePlanItemBatch(Long orgId, Long duyanPlanId, List<String> mobiles, Long updateBy) {
      // 请求参数
      JSONObject pathJson = getCommonJson(orgId);
      pathJson.put("agent_id", updateBy);
      String path = "?";
      Iterator<String> iterator = pathJson.keySet().iterator();
      while (iterator.hasNext()) {
        String next = iterator.next();
        if (iterator.hasNext()) {
          path = path + next + "=" + pathJson.get(next) + "&";
        } else {
          path = path + next + "=" + pathJson.get(next);
        }
      }
      JSONObject body = new JSONObject();
      body.put("campaign_id", duyanPlanId);
      body.put("phone_number_list", mobiles);

      String referResponse =
        HttpUtils.requset(
          HttpUtils.METHOD_POST,
          String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId)) + "open/dispatch/batch/del" + path,
          HttpUtils.CONTENT_TYPE_JSON,
          body);
      if (!isEmpty(referResponse)) {
        JSONObject jsonResult = JSONObject.parseObject(referResponse);
        valiResponse(jsonResult);
        JSONObject data = jsonResult.getJSONObject("data");
        if (data.isEmpty()
          || data.getJSONArray("success_id_list") == null
          || data.getJSONArray("success_id_list").isEmpty()) {
          throw new ApiException("调用度言接口失败，手机号移除计划失败");
        }
      } else {
        throw new ApiException("调用度言接口未返回，手机号移除计划失败");
      }
    }

  /**
   * 获取计划统计数据
   * 计划执行轮次，不传默认返回最近一次执行结果
   *
   * @param duyanPlanId 度言计划id
   */
  public StatisticsPlanRes getFifoStatistics(Long duyanPlanId) {
    Long orgId = UserUtils.getTokenUser().getOrgId();
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + String.format(NewDuyanConstant.FIFO_STATISTICS, duyanPlanId);
    DoRestResult<StatisticsPlanRes> doRestResult;
    try {
      doRestResult = newDuyanInvoker.getPlanDetail(url, getOrgApiKey(), null);
    } catch (Exception e) {
      log.error("调用获取计划统计数据接口异常", e);
      throw new ApiException("调用获取计划统计数据接口异常");
    }
    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("调用获取计划统计接口异常");
    }
    valiResponse(doRestResult);
    StatisticsPlanRes resData = doRestResult.getData();
    return resData;
  }

  public StatisticsPlanRes statisticsPlan(Long duyanPlanId, Integer round) {
    Long orgId = UserUtils.getTokenUser().getOrgId();
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + String.format(NewDuyanConstant.FIFO_STATISTICS, duyanPlanId);
    DoRestResult<StatisticsPlanRes> doRestResult;
    try {
      doRestResult = newDuyanInvoker.getPlanDetail(url, getOrgApiKey(), round);
    } catch (Exception e) {
      log.error("调用获取计划统计数据接口异常", e);
      throw new ApiException("调用获取计划统计数据接口异常");
    }
    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("调用获取计划统计接口异常");
    }
    valiResponse(doRestResult);
    StatisticsPlanRes resData = doRestResult.getData();
    return resData;
  }

  /**
   * 查询团队、个人预测式外呼详情信息
   *
   * @param query 查询条件
   * @return
   */
  public PageOutput<PlanDetailVO> getFifoPlanDetail(PlanDetailQuery query, Long orgId) {
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + String.format(NewDuyanConstant.PLAN_RESULT, query.getCampaign_id());
    GetPlanResultReq getPlanResultReq = new GetPlanResultReq();
    Long duyanId = UserUtils.getDuyanId(orgId);
    getPlanResultReq.setApikey(getOrgApiKey(duyanId));

    getPlanResultReq.setPage_num(query.getPage_num().longValue())
            .setPage_size(query.getPage_size().longValue())
            .setCampaign_id(query.getCampaign_id())
            .setPhone(query.getPhone())
            .setOutcome(query.getOutcome())
            .setCall_start_time(query.getCall_start_time())
            .setCall_end_time(query.getCall_end_time())
            .setRound(query.getRound());
    url = url + getPlanResultReq.params();
    DoRestResult<GetPlanResultRes> doRestResult;
    try {
      doRestResult = newDuyanInvoker.getPlanResults(url);
    }catch (Exception e){
      log.error("调用获取计划执行结果接口异常", e);
      throw new ApiException("调用获取计划执行结果接口异常");
    }
    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("调用获取计划执行结果接口失败");
    }
    valiResponse(doRestResult);
    GetPlanResultRes resData = doRestResult.getData();

    PageOutput<PlanDetailVO> pageOutput = new PageOutput<>();
    pageOutput.setLimit(query.getPage_size());
    pageOutput.setPageNum(query.getPage_num());

    if (ObjectUtil.isNull(resData)){
      pageOutput.setTotal(0);
      pageOutput.setPages(0);
      pageOutput.setPageSize(0);
      pageOutput.setList(new ArrayList<>());
      return pageOutput;
    }

    pageOutput.setTotal(resData.getTotal_elements().intValue());
    pageOutput.setPages(resData.getTotal_pages().intValue());

    List<GetPlanResultRes.Campaign> campaigns = Optional.ofNullable(resData.getCampaigns()).orElse(new ArrayList<>());
    List<PlanDetailVO> planDetailVOS = campaigns.stream().map(campaign -> {
      PlanDetailVO planDetailVO = new PlanDetailVO();
      planDetailVO.setCampaignId(campaign.getCampaign_id());
      planDetailVO.setPhone(campaign.getPhone());
      planDetailVO.setCall_count(ObjectUtil.isNull(campaign.getCall_count())?null:campaign.getCall_count().intValue());
      planDetailVO.setCall_time(campaign.getCall_time());
      planDetailVO.setDuration(ObjectUtil.isNull(campaign.getDuration())?null:campaign.getDuration().intValue());
      planDetailVO.setCaller(campaign.getCaller());
      planDetailVO.setOutcome(campaign.getOutcome());
      planDetailVO.setCall_uuid(campaign.getCall_uuid());
      planDetailVO.setVariables(campaign.getVariables());
      // 机器人客户标签 仅个人预测式外呼计划详情 当等待客户处理方式为接入机器人时，会存在命中度言客户标签数据
      planDetailVO.setVoice_site_labels(campaign.getVoice_site_labels());
      planDetailVO.setConnect_type(campaign.getConnect_type());
      planDetailVO.setVoice_site_id(campaign.getVoice_site_id());
      return planDetailVO;
    }).collect(Collectors.toList());

    pageOutput.setPageSize(planDetailVOS.size());
    pageOutput.setList(planDetailVOS);

    return pageOutput;
  }

  /**
   * 切换计划状态
   * 此接口用于指定计划的状态
   *
   * @param change 修改计划
   * @return
   */
  public int changeFifoPlanStatus(ChangePlan change) {
    Long orgId = UserUtils.getTokenUser().getOrgId();
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + String.format(NewDuyanConstant.PLAN_STATUS_CHANGE, change.getDuyanPlanId());
    String orgApiKey = getOrgApiKey();
    Long planStatus = CasePlanEnums.DuyanPlanStatus.getCodeByOldCode(change.getPlanStatus());

    DoRestResult<String> doRestResult;
    try {
      doRestResult = newDuyanInvoker.changePlanStatus(url, orgApiKey, planStatus);
    } catch (Exception e) {
      log.error("调用切换计划状态接口异常", e);
      throw new ApiException("调用切换计划状态接口异常");
    }
    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("调用切换计划状态接口失败");
    }

    try {
      valiResponse(doRestResult);
    } catch (ApiException e) {
      if ("无效的status[IN_PROGRESS]，status必须是NOT_STARTED或者PAUSED才可开始.".equalsIgnoreCase(doRestResult.getMessage())) {
        throw new ApiException("计划状态已更改，请刷新页面");
      }
    }
    return doRestResult.getStatus().intValue();
  }

  /**
   * 获取话术
   * 此接口用于获取公司的所有话术，话术需在管理后台（CFG）创建
   *
   * @return
   */
  public List<VoiceSite> getPersonalSites() {
    Long orgId = UserUtils.getTokenUser().getOrgId();
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + NewRobotConstant.VOICE_SITE_IST;
    String orgApiKey = getOrgApiKey();
    DoRestResult<List<VoiceSiteListRes>> doRestResult;
    try {
      doRestResult = newDuyanInvoker.voiceSiteList(url,orgApiKey);
    } catch (Exception e){
      log.error("调用获取话术接口异常", e);
      throw new ApiException("调用获取话术接口异常");
    }

    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("调用获取话术接口失败");
    }
    valiResponse(doRestResult);
    List<VoiceSiteListRes> resDatas = doRestResult.getData();
    if (ObjectUtil.isNull(resDatas)) {
      throw new ApiException("未获取到话术");
    }
    List<VoiceSite> voiceSites = resDatas.stream().map(resData -> {
      VoiceSite voiceSite = new VoiceSite();
      voiceSite.setId(resData.getId());
      voiceSite.setName(resData.getName());
      voiceSite.setDescription(resData.getRemark());
      voiceSite.setIsPublish(resData.getIs_publish());
      voiceSite.setCreatedTime(ObjectUtil.isNull(resData.getCreated_time())?null:new Date(resData.getCreated_time()));
      voiceSite.setModule(resData.getModule());
      voiceSite.setLastUpdatedTime(ObjectUtil.isNull(resData.getUpdated_time())?null:new Date(resData.getUpdated_time()));
      voiceSite.setBusinessName(resData.getBusiness_name());
      voiceSite.setPublicName(resData.getPublic_name());
      voiceSite.setPublishTime(ObjectUtil.isNull(resData.getPublish_time())?null:new Date(resData.getPublish_time()));
      return voiceSite;
    }).collect(Collectors.toList());
    return voiceSites;
  }

  /**
   * 获取预测式外呼计划基本信息
   *
   * @return
   */
  public CasePlanBaseInfo getFifoPlanInfo(Long campaignId) {
    Long orgId = UserUtils.getTokenUser().getOrgId();
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + String.format(NewDuyanConstant.PLAN_INFO, campaignId);
    DoRestResult<GetPlanBaseInfoRes> doRestResult;
    try {
      doRestResult = newDuyanInvoker.casePlanBaseInfo(url,getOrgApiKey());
    } catch (Exception e){
      log.error("获取预测式外呼计划基本信息接口异常", e);
      throw new ApiException("获取预测式外呼计划基本信息接口异常");
    }

    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("获取预测式外呼计划基本信息接口失败");
    }
    valiResponse(doRestResult);
    GetPlanBaseInfoRes resData = doRestResult.getData();
    if (ObjectUtil.isNull(resData)) {
      throw null;
    }
    CasePlanBaseInfo casePlanBaseInfo = new CasePlanBaseInfo();
    casePlanBaseInfo.setDuyanPlanId(resData.getId())
            .setName(resData.getName())
            .setCreatedTime(ObjectUtil.isNull(resData.getCreated_time())?null:new Date(resData.getCreated_time()))
            .setCaller(resData.getCaller())
            .setPoolId(resData.getPool_id())
            .setStartTime(ObjectUtil.isNull(resData.getStart_time())?null:new Date(resData.getStart_time()))
            .setEndTime(ObjectUtil.isNull(resData.getEnd_time())?null:new Date(resData.getEnd_time()))
            .setPlanStatus(CasePlanEnums.DuyanPlanStatus.getOldCodeByCode(resData.getStatus()))
            .setTotalCount(resData.getTotal_count())
            .setFinishedCount(resData.getFinished_count())
            .setSiteId(resData.getSite_id())
            .setChannelLimit(resData.getChannel_limit())
            .setTeamId(resData.getTeam_id())
            .setAccountId(resData.getAccount_id())
            .setRetryCount(resData.getRetry_count())
            .setRetryInterval(resData.getRetry_interval())
            .setRetryConditions(resData.getRetry_conditions())
            .setPriority(resData.getPriority())
            .setNowRounds(resData.getNow_rounds())
            .setNowRoundCount(resData.getNow_round_count())
            .setIsEarlyMediaRecEnabled(resData.getIs_early_media_rec_enabled());

    return casePlanBaseInfo;
  }

  /**
   * 获取度言话术变量
   *
   * @param siteId 话术id
   * @param orgId  公司id
   * @return {@link List}<{@link String}>
   */
  public List<String> getSiteVars(Long siteId,Long orgId) {
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + NewDuyanConstant.SITE_VAR_LIST;
    Long duyanId = UserUtils.getDuyanId(orgId);
    DoRestResult<List<String>> doRestResult;
    try {
      doRestResult = newDuyanInvoker.siteVars(url,getOrgApiKey(duyanId),siteId);
    }catch (Exception e){
      log.error("调用获取获取话术变量接口异常", e);
      throw new ApiException("调用获取获取话术变量接口异常");
    }

    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("调用获取获取话术变量接口失败");
    }
    valiResponse(doRestResult);
    return Optional.ofNullable(doRestResult.getData()).orElse(new ArrayList<>());
  }

  /**
   * 创建机器人点
   * 创建机器人点呼
   *
   * @param createRobotPointReq createRobotPointReq
   * @param orgId               组织id
   * @param caseId              案例id
   * @return {@link CreateRobotPointRes}
   */
  public DoRestResult<CreateRobotPointRes> createRobotPoint(CreateRobotPointReq createRobotPointReq,Long orgId,Long caseId) {
    AssertUtil.notNull(orgId,"公司id不能为空");
    AssertUtil.notNull(caseId,"案件id不能为空");

    Map<String, String> vars = createRobotPointReq.getVars();
    vars.put("U_orgId",String.valueOf(orgId));
    vars.put("U_caseId",String.valueOf(caseId));

    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + NewDuyanConstant.ROBOT_POINT;
    Long duyanId = UserUtils.getDuyanId(orgId);

    DoRestResult<CreateRobotPointRes> doRestResult;
    try {
      String apiKey = getOrgApiKey(duyanId);
      url=url+"?apikey="+apiKey;
      doRestResult = newDuyanInvoker.createRobotPoint(url,createRobotPointReq);
    }catch (Exception e){
      log.error("创建机器人点呼接口异常", e);
      throw new ApiException("创建机器人点呼接口异常");
    }

    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("创建机器人点呼接口失败");
    }
    return doRestResult;
  }

  /**
   * 获取客户标签列表
   *
   * @param orgId               组织id
   * @return {@link CreateRobotPointRes}
   */
  public List<GetLabelInfoRes> getLabelInfo(Long orgId) {
    AssertUtil.notNull(orgId,"公司id不能为空");
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + NewDuyanConstant.LABEL_INDO;
    Long duyanId = UserUtils.getDuyanId(orgId);
    DoRestResult<List<GetLabelInfoRes>> doRestResult;
    try {
      doRestResult = newDuyanInvoker.getLabelInfo(url,getOrgApiKey(duyanId));
    }catch (Exception e){
      log.error("获取标签异常", e);
      throw new ApiException("获取标签异常");
    }
    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("获取标签异常");
    }
    valiResponse(doRestResult);
    return doRestResult.getData();
  }

  /**
   * 获取号码池
   *
   * @param orgId               组织id
   * @return {@link CreateRobotPointRes}
   */
  public List<PhoneNumberPoolRes> getPhoneNumberPool(Long orgId) {
    AssertUtil.notNull(orgId,"公司id不能为空");
    String duyanUrlPrefix = String.format(systemConfig.getDuyanUrlPrefix(), getDuyanServiceKey(orgId));
    String url = duyanUrlPrefix + NewDuyanConstant.PHONE_NUMBER_POOL;
    Long duyanId = UserUtils.getDuyanId(orgId);
    JSONObject doRestResult;
    try {
      doRestResult = newDuyanInvoker.getPhoneNumberPool(url,getOrgApiKey(duyanId));
    }catch (Exception e){
      log.error("获取号码池异常", e);
      throw new ApiException("获取号码池异常");
    }
    if (ObjectUtil.isNull(doRestResult)) {
      throw new ApiException("获取号码池异常");
    }
    valiResponse(doRestResult);
    JSONArray jsonArray = doRestResult.getJSONArray("data");
    List<PhoneNumberPoolRes> result = JSON.parseArray(JSON.toJSONString(jsonArray),PhoneNumberPoolRes.class);
    return result;
  }

}
