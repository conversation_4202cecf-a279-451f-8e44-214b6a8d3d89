package com.anmi.collection.configs;

import com.anmi.collection.common.enums.QosEnum;
import com.anmi.collection.manager.mqtt.EmqClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/8/9 下午2:30
 */
@Component
@Slf4j
public class MqttConfig implements ApplicationRunner {

    @Resource private SystemConfig systemConfig;
    @Resource private EmqClient emqClient;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        emqClient.connect();
        emqClient.subscribe(systemConfig.getMqttDefaultTopic(), QosEnum.Qos0);
    }
}
