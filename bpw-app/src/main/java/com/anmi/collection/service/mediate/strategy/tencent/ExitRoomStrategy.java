package com.anmi.collection.service.mediate.strategy.tencent;

import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.redis.RedisLock;
import com.anmi.collection.service.mediate.MediationVideoRoomService;
import com.anmi.collection.service.mediate.strategy.CallBackStrategy;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.DateUtils;
import com.anmi.domain.mediate.MediationVideoRoom;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 退出房间
 *
 * <AUTHOR>
 */
@Component("ExitRoomStrategy")
@Slf4j
public class ExitRoomStrategy implements CallBackStrategy {
    @Resource
    private MediationVideoRoomService mediationVideoRoomService;
    @Resource
    private RedisLock redisLock;


    @Override
    public String getAction() {
        return "EVENT_TYPE_EXIT_ROOM";
    }

    /**
     * {"CallbackTs":1684820598516,
     * "EventInfo":{
     *          "RoomId":899989,"EventTs":1684820598,
     *          "EventMsTs":1684820598425,"UserId":"741436",
     *          "Role":20,"Reason":1
     *          },
     * "EventType":104,"EventGroupId":1}
     * @param jsonObject 回调信息
     */
    @Override
    public void dealCallBack(JSONObject jsonObject) {
        log.info("用户退出房间回调通知！{}", jsonObject);
        JSONObject eventInfo = jsonObject.getJSONObject("EventInfo");
        String roomId = eventInfo.getString("RoomId");
        MediationVideoRoom room = mediationVideoRoomService.queryRoomByUuid(roomId);
        AssertUtil.notNull(room, "不存在该房间的信息");
        Long userId = eventInfo.getLong("UserId");
        Long eventTs = eventInfo.getLong("EventTs");
        Date exitTime = DateUtils.parseDateFull(DateUtils.timeStampToStr(eventTs * 1000));

        if (!Objects.equals(room.getCreateBy(), userId)) {
            log.info("用户{}于{}退出房间", userId, exitTime);
            return;
        }
        log.info("催员{}于{}退出房间", userId, exitTime);
        String lockKey = KeyCache.ROOM_UPDATE_LOCK + roomId;
        try {
            if (!redisLock.tryLock(lockKey)) {
                log.error("锁超时，key:{}", lockKey);
                throw new ApiException("操作过于繁忙，请稍后再试");
            }
            // 修改房间发起状态为未发起
            mediationVideoRoomService.updateStatusByUuid(roomId, 0);
        } catch (Exception e) {
            log.error("获取锁发生错误：", e);
            throw new ApiException("获取锁发生错误");
        } finally {
            redisLock.unlock(lockKey);
        }
    }
}
