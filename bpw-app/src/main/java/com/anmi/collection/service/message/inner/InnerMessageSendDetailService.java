package com.anmi.collection.service.message.inner;

import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.message.InnerMessageSendDetailEnums;
import com.anmi.collection.entity.requset.message.InnerMessageSendParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.message.InnerMessageDetailVO;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.message.InnerMessageSend;
import com.anmi.domain.message.InnerMessageSendDetail;
import com.github.pagehelper.Page;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 站内信发送详情
 *
 * <AUTHOR>
 * @date 2023/11/27
 */
@Service
public class InnerMessageSendDetailService extends BaseService<InnerMessageSendDetail> {

    @Resource private InnerMessageSendService innerMessageSendService;

    /**
     * 添加站内信发送详情
     *
     * @param orgId   公司id
     * @param toUsers 接收人
     * @param sendId  站内信发送id
     */
    public void addSendDetail(Long orgId, List<InnerMessageSendParam.ToUser> toUsers, Long sendId) {
        List<InnerMessageSendDetail> sendDetails = toUsers.parallelStream().map(toUser -> {
            InnerMessageSendDetail sendDetail = new InnerMessageSendDetail();
            sendDetail.setOrgId(orgId);
            sendDetail.setToUserId(toUser.getUserId());
            sendDetail.setSendId(sendId);
            sendDetail.setReaded(InnerMessageSendDetailEnums.Readed.NO.getCode());
            sendDetail.setDeleted(InnerMessageSendDetailEnums.Deleted.NO.getCode());
            Date now = new Date();
            sendDetail.setCreateTime(now);
            sendDetail.setUpdateTime(now);
            return sendDetail;
        }).collect(Collectors.toList());
        insertBatch(sendDetails);
    }

    /**
     * 收件列表
     *
     * @param param 参数
     * @return {@link PageOutput}<{@link InnerMessageDetailVO}>
     */
    public PageOutput<InnerMessageDetailVO> receiveList(PageParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        Long orgId = userSession.getOrgId();
        Long toUserId = userSession.getId();

        PageParam pageParam = new PageParam(param.getPage(), param.getLimit());
        Page page = PageUtils.setPage(pageParam);
        Example example = new Example(InnerMessageSendDetail.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId",orgId)
                .andEqualTo("toUserId",toUserId)
                .andEqualTo("deleted", InnerMessageSendDetailEnums.Deleted.NO.getCode());
        List<InnerMessageSendDetail> sendDetails = selectByExample(example);
        if (ObjectUtil.isEmpty(sendDetails)){
            return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), new ArrayList<>());
        }

        Set<Long> sendIds = sendDetails.parallelStream().map(InnerMessageSendDetail::getSendId).collect(Collectors.toSet());
        List<InnerMessageSend> sends = innerMessageSendService.getList(sendIds);

        List<InnerMessageDetailVO> messageDetailVOS = sendDetails.parallelStream().map(sendDetail -> {
            InnerMessageDetailVO detailVO = new InnerMessageDetailVO();
            detailVO.setSendDetailId(sendDetail.getId());
            detailVO.setSendId(sendDetail.getSendId());
            detailVO.setReaded(sendDetail.getReaded());
            Optional<InnerMessageSend> first = sends.parallelStream().filter(send -> ObjectUtil.equals(sendDetail.getSendId(), send.getId())).findFirst();
            if (first.isPresent()) {
                InnerMessageSend sendRecord = first.get();
                detailVO.setFromUserName(sendRecord.getFromUserName());
                detailVO.setSubject(sendRecord.getSubject());
                detailVO.setContent(sendRecord.getContent());
                detailVO.setCreateTime(sendRecord.getCreateTime());
            }
            return detailVO;
        }).collect(Collectors.toList());

        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), messageDetailVOS);
    }

    /**
     * 已读 站内信
     *
     * @param sendDetailId 站内信发送详情id
     */
    public void read(Long sendDetailId) {
        InnerMessageSendDetail sendDetail = new InnerMessageSendDetail();
        sendDetail.setId(sendDetailId);
        sendDetail.setReaded(InnerMessageSendDetailEnums.Readed.YES.getCode());
        updateByPrimaryKeySelective(sendDetail);
    }

    /**
     * 全部已读 站内信
     */
    public void readAll() {
        UserSession userSession = UserUtils.getTokenUser();
        Long orgId = userSession.getOrgId();
        Long toUserId = userSession.getId();

        Example example = new Example(InnerMessageSendDetail.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId",orgId)
                .andEqualTo("toUserId",toUserId)
                .andEqualTo("readed", InnerMessageSendDetailEnums.Readed.NO.getCode());

        InnerMessageSendDetail sendDetail = new InnerMessageSendDetail();
        sendDetail.setReaded(InnerMessageSendDetailEnums.Readed.YES.getCode());
        sendDetail.setUpdateTime(new Date());

        updateByExampleSelective(sendDetail,example);
    }

    /**
     * 获取列表
     *
     * @param sendId 发送id
     * @return {@link List}<{@link InnerMessageSendDetail}>
     */
    public List<InnerMessageSendDetail> getList(Long sendId) {
        Example example = new Example(InnerMessageSendDetail.class);
        example.selectProperties("id","toUserId");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sendId",sendId);

        return selectByExample(example);
    }
}
