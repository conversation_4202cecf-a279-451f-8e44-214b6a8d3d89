package com.anmi.collection.service;


import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.enums.RoleEnums;
import com.anmi.collection.mapper.InspectorsMapper;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.domain.user.Inspectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class InspectorsService extends BaseService<Inspectors> {
    @Resource
    private InspectorsMapper inspectorsMapper;

    public void createInspectorsRels(Long userId, List<Long> teamIds) {
        if (CollectionUtils.isEmpty(teamIds)) {
            return;
        }
        List<Inspectors> inspectors = new ArrayList<>();
        for (Long teamId : teamIds) {
            // 需要添加的对应关系
            Inspectors inspector = new Inspectors();
            inspector.setTargetId(teamId);
            inspector.setInspectorId(userId);
            inspector.setType(RoleEnums.DepType.TEAM.getCode());
            inspectors.add(inspector);
        }
        super.insertBatch(inspectors);
    }

    public void updateInspectorsRels(Long userId, List<Long> teamIds) {
        //删除旧关系
        Example example = new Example(Inspectors.class);
        example.createCriteria().andEqualTo("inspectorId", userId);
        super.deleteByExample(example);
        //创建新关系
        createInspectorsRels(userId, teamIds);
    }

    /**
     * 删除没有度言账号的质检关系信息
     */
    public void deleteByDuyanAccount(Long orgId) {
        inspectorsMapper.deleteByDuyanAccount(orgId);
    }
}
