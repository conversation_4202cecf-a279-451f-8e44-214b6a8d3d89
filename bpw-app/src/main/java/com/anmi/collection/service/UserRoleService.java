package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.mapper.UserRoleMapper;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.domain.user.Role;
import com.anmi.domain.user.UserRole;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class UserRoleService extends BaseService<UserRole> {
    @Resource
    private UserRoleMapper userRoleMapper;

    public void createUserRoleRels(Long userId, List<Role> roleList) {
        if (CollectionUtils.isEmpty(roleList)) {
            return;
        }
        List<UserRole> userRoles = new ArrayList<>();
        for (Role role : roleList) {
            UserRole userRole = new UserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(role.getId());
            userRoles.add(userRole);
        }
        super.insertBatch(userRoles);
    }

    public void updateUserRoleRels(Long userId, List<Role> roleList) {
        //删除旧关系
        Example example = new Example(UserRole.class);
        example.createCriteria().andEqualTo("userId", userId);
        super.deleteByExample(example);
        //插入新关系
        createUserRoleRels(userId, roleList);
    }

    public void deleteByDuyanAccount(Long orgId) {
        userRoleMapper.deleteByDuyanAccount(orgId);
    }
}
