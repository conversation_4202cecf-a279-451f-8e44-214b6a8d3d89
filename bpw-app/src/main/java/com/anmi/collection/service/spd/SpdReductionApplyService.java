package com.anmi.collection.service.spd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.entity.requset.spd.ReduceApplyListParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.spd.SpdReductionApplyVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.service.*;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.spd.SpdReductionApply;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SpdReductionApplyService extends BaseService<SpdReductionApply> {

    @Resource private CaseService caseService;
    @Resource private UserService userService;
    @Resource private DeltService deltService;
    @Resource private ProductService productService;
    @Resource private DepTeamService depTeamService;

    /**
     * 减免申请列表
     *
     * @param param 参数
     * @return {@link PageOutput}<{@link SpdReductionApplyVO}>
     */
    public PageOutput<SpdReductionApplyVO> reduceApplyList(ReduceApplyListParam param) {
        if (ObjectUtil.equals("detail", param.getSource()) && ObjectUtil.isNull(param.getCaseId())) {
            throw new ApiException("详情页来源，案件id不能为空");
        }

        Example example = new Example(SpdReductionApply.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();

        UserSession session = UserUtils.getTokenUser();

        // 公司隔离
        criteria.andEqualTo("orgId", session.getOrgId());
        if (ObjectUtil.equals("detail", param.getSource())) {
            criteria.andEqualTo("caseId", param.getCaseId());
        } else {
            if (UserUtils.likeAdmin()) {
                criteria.andEqualTo("orgId", session.getOrgId());
            } else if (UserUtils.likeBranchAdmin()) {
                criteria.andEqualTo("depId", session.getDepId());
            } else if (UserUtils.likeTeamLeader()) {
                criteria.andEqualTo("teamId", session.getTeamId());
            } else {
                throw new ApiException("无权访问");
            }
        }

        Page page = super.setPage(param);
        List<SpdReductionApply> applyList = selectByExample(example);
        if (ObjectUtil.isEmpty(applyList)){
            return new PageOutput(page.getPageNum(), page.getPageSize(),(int) page.getTotal(),new ArrayList());
        }

        // 案件
        List<Long> caseIds = applyList.stream().filter(p-> ObjectUtil.isNotNull(p.getCaseId())).map(SpdReductionApply::getCaseId).distinct().collect(Collectors.toList());
        List<Case> caseList = caseService.getCaseListByCaseIds(caseIds);
        // 委案公司
        List<Long> deltIds = caseList.stream().filter(p-> ObjectUtil.isNotNull(p.getOrgDeltId())).map(Case::getOrgDeltId).distinct().collect(Collectors.toList());
        Map<Long, String> deltMap = deltService.getDeltMap(deltIds);
        // 委案产品
        List<Long> productIds = caseList.stream().filter(p-> ObjectUtil.isNotNull(p.getProductId())).map(Case::getProductId).distinct().collect(Collectors.toList());
        Map<Long, String> productMap = productService.getProductMap(productIds);

        List<Long> userIds = applyList.stream().filter(p-> ObjectUtil.isNotNull(p.getUserId())).map(SpdReductionApply::getUserId).distinct().collect(Collectors.toList());
        List<Long> caseUserIds = caseList.stream().filter(p-> ObjectUtil.isNotNull(p.getUserId())).map(Case::getUserId).distinct().collect(Collectors.toList());
        userIds = CollUtil.addAllIfNotContains(userIds,caseUserIds);
        Map<Long, String> userMap = userService.getUserMap(userIds);

        List<Long> depIds = applyList.stream().filter(p-> ObjectUtil.isNotNull(p.getDepId())).map(SpdReductionApply::getDepId).distinct().collect(Collectors.toList());
        List<Long> teamIds = applyList.stream().filter(p-> ObjectUtil.isNotNull(p.getTeamId())).map(SpdReductionApply::getTeamId).distinct().collect(Collectors.toList());
        depIds = CollUtil.addAllIfNotContains(depIds,teamIds);
        Map<Long, String> depTeamMap = depTeamService.getDepTeamMap(depIds);

        List<SpdReductionApplyVO> spdReductionApplyVOS = applyList.stream().map(apply -> {
            SpdReductionApplyVO applyVO = new SpdReductionApplyVO();
            BeanUtils.copyProperties(apply, applyVO);
            Optional.ofNullable(userMap.get(apply.getUserId())).ifPresent(s->applyVO.setUserName(s));
            caseList.stream().filter(caseInfo -> ObjectUtil.equals(apply.getCaseId(),caseInfo.getId())).findFirst().ifPresent(caseInfo->{
                applyVO.setOutSerialTemp(caseInfo.getOutSerialTemp()).setAmount(caseInfo.getAmount());
                Optional.ofNullable(deltMap.get(caseInfo.getOrgDeltId())).ifPresent(s->applyVO.setDeltName(s));
                Optional.ofNullable(productMap.get(caseInfo.getProductId())).ifPresent(s->applyVO.setProductName(s));
                Optional.ofNullable(userMap.get(caseInfo.getUserId())).ifPresent(s->applyVO.setCaseUserName(s));
                Optional.ofNullable(depTeamMap.get(caseInfo.getDepId())).ifPresent(s->applyVO.setBranchCompany(s));
                Optional.ofNullable(depTeamMap.get(caseInfo.getTeamId())).ifPresent(s->applyVO.setTeamName(s));
            });
            return applyVO;
        }).collect(Collectors.toList());

        PageOutput pageOutput = new PageOutput(page.getPageNum(), page.getPageSize(),(int) page.getTotal(),spdReductionApplyVOS);

        return pageOutput;
    }
}
