package com.anmi.collection.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.AllotCaseEnums;
import com.anmi.collection.common.enums.CaseAllotTemplateEnums;
import com.anmi.collection.common.enums.StrategyEnums;
import com.anmi.collection.dto.TemplateStrategyCnt;
import com.anmi.collection.entity.requset.cases.AllotTemplateParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.AllotTemplateVO;
import com.anmi.collection.entity.response.cases.PlanTmplStrategy;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.StrategyInfoMapper;
import com.anmi.collection.mapper.StrategyTmplRelMapper;
import com.anmi.collection.utils.AnmiBeanutils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.CaseAllotTemplate;
import com.anmi.domain.decision.StrategyInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CaseAllotTemplateService extends BaseService<CaseAllotTemplate> {

    @Resource
    private StrategyTmplRelMapper strategyTmplRelMapper;
    @Resource
    private StrategyInfoMapper strategyInfoMapper;
    @Resource
    private UserService userService;
    @Resource
    private DepTeamService depTeamService;

    public Long add(AllotTemplateParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        CaseAllotTemplate template = new CaseAllotTemplate();
        convert(param, userSession, template, true);
        this.insert(template);
        return template.getId();
    }

    public void update(AllotTemplateParam param) {
        if (Objects.isNull(param.getId())) {
            throw new ApiException("id不能为空");
        }
        UserSession userSession = UserUtils.getTokenUser();
        CaseAllotTemplate template = this.selectByPrimaryKey(param.getId());
        if (Objects.isNull(template) || !Objects.equals(userSession.getOrgId(), template.getOrgId())) {
            throw new ApiException("找不到id为{0}的模板", param.getId());
        }
        convert(param, userSession, template, false);
        this.updateByPrimaryKey(template);
    }

    public PageOutput<AllotTemplateVO> list(PageParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        CaseAllotTemplate query = new CaseAllotTemplate();
        query.setOrgId(userSession.getOrgId());
        query.setRecovery(0);
        PageOutput<CaseAllotTemplate> pageInfo = this.selectByPage(query, param);
        if(CollectionUtils.isEmpty(pageInfo.getList())){
            return new PageOutput<>();
        }
        return convertVOS(pageInfo);
    }

    public Map<Long, List<AllotTemplateVO>> getLinkAllotTempByRuleId(List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyMap();
        }
        Example example = new Example(CaseAllotTemplate.class);
        example.and().andEqualTo("recovery", 0).andIn("ruleId", ruleIds);
        List<CaseAllotTemplate> caseAllotTemplates = this.selectByExample(example);
        List<AllotTemplateVO> templateVOS = new ArrayList<>();
        caseAllotTemplates.forEach(caseAllotTemplate -> {
            AllotTemplateVO templateVO = AnmiBeanutils.copy(caseAllotTemplate, AllotTemplateVO.class);
            templateVOS.add(templateVO);
        });

        Map<Long, List<AllotTemplateVO>> map = templateVOS.parallelStream().collect(Collectors.groupingBy(AllotTemplateVO::getRuleId));
        return map;

    }

    public AllotTemplateVO detail(Long id) {
        UserSession userSession = UserUtils.getTokenUser();
        CaseAllotTemplate template = this.selectByPrimaryKey(id);
        if (Objects.isNull(template) || !Objects.equals(userSession.getOrgId(), template.getOrgId())) {
            throw new ApiException("找不到id为{0}的模板", id);
        }
        AllotTemplateVO vo = new AllotTemplateVO();
        convert(template, vo, Collections.emptyMap());
        Map<Long, String> allotProportionsName = new HashMap<>();
        Map<Long, String> userOrDepTeamNameMaps = null;
        List<Long> ids = null;
        if (AllotCaseEnums.Target.USER.getCode().equals(template.getAllotObject())) {
            userOrDepTeamNameMaps = userService.getNames(userSession.getOrgId());
            ids = vo.getUserIdList();
        } else if (AllotCaseEnums.Target.TEAM.getCode().equals(template.getAllotObject())) {
            ids = vo.getTeamIdList();
            userOrDepTeamNameMaps = depTeamService.getNames();
        } else {
            userOrDepTeamNameMaps = depTeamService.getNames();
            ids = vo.getDepIdList();
        }
        if (!Objects.isNull(ids)) {
            for (Long targetId : ids) {
                String value = userOrDepTeamNameMaps.get(targetId);
                allotProportionsName.put(targetId, value);
            }
        }
        vo.setAllotProportionsName(allotProportionsName);
        return vo;
    }

    public void delete(Long id) {
        UserSession userSession = UserUtils.getTokenUser();
        CaseAllotTemplate template = this.selectByPrimaryKey(id);
        if (Objects.isNull(template) || !Objects.equals(userSession.getOrgId(), template.getOrgId())) {
            throw new ApiException("找不到id为{0}的模板", id);
        }
        template.setRecovery(-1);
        template.setUpdateBy(userSession.getId());
        template.setUpdateTime(new Date());
        this.updateByPrimaryKey(template);
    }

    private void convert(AllotTemplateParam param,
                         UserSession session,
                         CaseAllotTemplate template,
                         Boolean create) {
        template.setType(param.getType());
        template.setAllotObject(param.getAllotObject());
        template.setAllotProportions(JSON.toJSONString(param.getAllotProportions()));
        template.setDepIds(JSON.toJSONString(param.getDepIdList()));
        template.setTeamIds(JSON.toJSONString(param.getTeamIdList()));
        template.setUserIds(JSON.toJSONString(param.getUserIdList()));
        template.setName(param.getName());
        template.setOrgId(session.getOrgId());
        if (!Objects.isNull(param.getIsAllAgents())) {
            template.setAllAgents(param.getIsAllAgents() ? 1 : 0);
        }
        if (!Objects.isNull(param.getAutoRecovery())) {
            template.setAutoRecovery(param.getAutoRecovery() ? 1 : 0);
        }
        if (!Objects.isNull(param.getIsConjoint())) {
            template.setIsConjoint(param.getIsConjoint() ? 1 : 0);
        }
        if (!Objects.isNull(param.getAllotToTeamMembers())) {
            template.setToMembers(param.getAllotToTeamMembers() ? 1 : 0);
        }
        if (!Objects.isNull(param.getAutoRecoveryDate())) {
            template.setAutoRecoveryDate(new Date(param.getAutoRecoveryDate()));
        }
        if (create) {
            template.setRecovery(0);
            template.setCreateBy(session.getId());
            template.setCreateTime(new Date());
            template.setStatus(0);
        }
        template.setUpdateBy(session.getId());
        template.setUpdateTime(new Date());
        template.setDesc(param.getDesc());
        template.setOperationWay(param.getOperationWay());
        template.setIsRule(param.getIsRule());
        template.setRuleId(param.getRuleId());
        template.setAutoRecoveryDay(param.getAutoRecoveryDay());
    }

    private void convert(CaseAllotTemplate template,
                         AllotTemplateVO vo,
                         Map<Long, Integer> strategyCntMap) {
        vo.setId(template.getId());
        vo.setType(template.getType());
        vo.setAllotObject(template.getAllotObject());
        vo.setRelStrategyCnt(strategyCntMap.get(template.getId()));
        vo.setUpdateTime(template.getUpdateTime().getTime());
        vo.setCreateTime(template.getCreateTime().getTime());
        TypeReference<Map<Long, Integer>> typeRef = new TypeReference<Map<Long, Integer>>() {
        };
        vo.setDesc(template.getDesc());
        vo.setAllotProportions(JSON.parseObject(template.getAllotProportions(), typeRef));
        if (!Objects.isNull(template.getDepIds())) {
            vo.setDepIdList(JSON.parseArray(template.getDepIds(),Long.class));
        }
        if (!Objects.isNull(template.getTeamIds())) {
            vo.setTeamIdList(JSON.parseArray(template.getTeamIds(),Long.class));
        }
        if (!Objects.isNull(template.getUserIds())) {
            vo.setUserIdList(JSON.parseArray(template.getUserIds(),Long.class));
        }
        vo.setName(template.getName());
        if (!Objects.isNull(template.getAllAgents())) {
            vo.setIsAllAgents(Objects.equals(template.getAllAgents(), 1));
        }
        if (!Objects.isNull(template.getAutoRecovery())) {
            vo.setAutoRecovery(Objects.equals(template.getAutoRecovery(), 1));
        }
        if (!Objects.isNull(template.getIsConjoint())) {
            vo.setIsConjoint(Objects.equals(template.getIsConjoint(), 1));
        }
        if (!Objects.isNull(template.getToMembers())) {
            vo.setAllotToTeamMembers(Objects.equals(template.getToMembers(), 1));
        }
        if (!Objects.isNull(template.getAutoRecoveryDate())) {
            vo.setAutoRecoveryDate(template.getAutoRecoveryDate().getTime());
        }
        vo.setOperationWay(template.getOperationWay());
        vo.setIsRule(template.getIsRule());
        vo.setRuleId(template.getRuleId());
        vo.setAutoRecoveryDay(template.getAutoRecoveryDay());
    }

    private PageOutput<AllotTemplateVO> convertVOS(PageOutput<CaseAllotTemplate> pageInfo) {
        PageOutput<AllotTemplateVO> vos = new PageOutput<>();
        vos.setPages(pageInfo.getPages());
        vos.setLimit(pageInfo.getLimit());
        vos.setPageNum(pageInfo.getPageNum());
        vos.setTotal(pageInfo.getTotal());
        vos.setPageSize(pageInfo.getPageSize());
        List<Long> ids = pageInfo.getList().stream().map(CaseAllotTemplate::getId).collect(Collectors.toList());
        List<TemplateStrategyCnt> strategyCnt = strategyTmplRelMapper.selectStrategyCnt(ids, Collections.singletonList("allot"));
        Map<Long, Integer> strategyCntMap = strategyCnt.stream()
            .collect(Collectors.toMap(TemplateStrategyCnt::getTemplateId, TemplateStrategyCnt::getCnt));
        List<AllotTemplateVO> list = new ArrayList<>();
        for (CaseAllotTemplate caseAllotTemplate : pageInfo.getList()) {
            AllotTemplateVO vo = new AllotTemplateVO();
            convert(caseAllotTemplate, vo, strategyCntMap);
            list.add(vo);
        }
        vos.setList(list);
        return vos;
    }

    public List<PlanTmplStrategy> relList(Long tmplId) {
        CaseAllotTemplate template = this.selectByPrimaryKey(tmplId);
        UserSession session = UserUtils.getTokenUser();
        if (template == null
          || CaseAllotTemplateEnums.Recovery.DELETE.getCode().equals(template.getRecovery())
          || !Objects.equals(session.getOrgId(), template.getOrgId())) {
            throw new ApiException("找不到编号为{0}的模板", tmplId);
        }
        List<StrategyInfo> list = strategyInfoMapper.selectByTemlId(tmplId,Collections.singletonList("allot"));
        return convert(list);
    }

    private List<PlanTmplStrategy> convert(List<StrategyInfo> list) {
        List<PlanTmplStrategy> result = new ArrayList<>();
        for (StrategyInfo strategyInfo : list) {
            PlanTmplStrategy planTmplStrategy = new PlanTmplStrategy();
            planTmplStrategy.setStrategyId(strategyInfo.getId());
            planTmplStrategy.setName(strategyInfo.getName());
            if (StrategyEnums.Draft.YES.getCode().equals(strategyInfo.getIsDraft())) {
                planTmplStrategy.setStatus("草稿箱");
            } else {
                if (StrategyEnums.Status.ENABLE.getCode().equals(strategyInfo.getStatus())) {
                    planTmplStrategy.setStatus("已开启");
                } else {
                    planTmplStrategy.setStatus("已关闭");
                }
            }
            result.add(planTmplStrategy);
        }
        return result;
    }
}
