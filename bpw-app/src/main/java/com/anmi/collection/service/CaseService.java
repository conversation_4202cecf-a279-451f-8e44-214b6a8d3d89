package com.anmi.collection.service;

import cn.duyan.thread.DuyanThreadExecutor;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anmi.alfred.dto.CaseMultiQueryDto;
import com.anmi.alfred.entity.TransferCaseOperationResult;
import com.anmi.alfred.response.concrete.CaseInfoResponse;
import com.anmi.alfred.response.concrete.CaseStatisticsResponse;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.constant.SystemConst;
import com.anmi.collection.dto.CaseAmount;
import com.anmi.collection.dto.CaseDTO;
import com.anmi.collection.dto.CaseTagRelDTO;
import com.anmi.collection.dto.EndInfoDTO;
import com.anmi.collection.dto.fileStorage.UploadFileInfo;
import com.anmi.collection.encrypt.EncryptProperties;
import com.anmi.collection.encrypt.EncryptService;
import com.anmi.collection.entity.requset.amc.*;
import com.anmi.collection.entity.requset.cases.*;
import com.anmi.collection.entity.requset.lawsuit.CaseToLawsuitParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.*;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.*;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.redis.RedisLock;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.service.lawsuit.LawsuitApplyService;
import com.anmi.collection.service.lawsuit.LawsuitService;
import com.anmi.collection.service.letter.LetterApplyService;
import com.anmi.collection.service.letter.LetterService;
import com.anmi.collection.service.mediate.LetterMediateService;
import com.anmi.collection.service.mediate.MediateAuditService;
import com.anmi.collection.service.mediate.MediationVideoRoomService;
import com.anmi.collection.service.remote.RemoteAlfredService;
import com.anmi.collection.service.workorder.WorkOrderService;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.*;
import com.anmi.domain.cases.*;
import com.anmi.domain.dto.CaseOperationParamDto;
import com.anmi.domain.letter.Letter;
import com.anmi.domain.principal.Product;
import com.anmi.domain.sys.CustomField;
import com.anmi.domain.sys.CustomSearchField;
import com.anmi.domain.user.*;
import com.github.pagehelper.Page;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/** Created by dongwang on 2018-12-07. */
@Service
@Slf4j
public class CaseService extends BaseService<Case> {

  @Autowired private CaseMapper caseMapper;
  @Autowired private DeltService deltService;
  @Autowired private ProductService productService;
  @Autowired private DepTeamService depTeamService;
  @Autowired private UserService userService;
  @Autowired private RepaymentService repaymentService;
  @Autowired private ApplicationContext applicationContext;
  @Autowired private CompanyService companyService;
  @Autowired private CaseLogService caseLogService;
  @Autowired private CaseOperationMapper caseOperationMapper;
  @Autowired private ContactsService contactsService;
  @Autowired private RepaymentMapper repaymentMapper;
  @Autowired private StringRedisTemplate stringRedisTemplate;
  @Autowired private FileStoreService fileStoreService;
  @Autowired private AsyncTaskService asyncTaskService;
  @Autowired private SystemConfig systemConfig;
  @Autowired private CaseFileMapper caseFileMapper;
  @Autowired protected CustomFieldService customFieldService;
  @Autowired private OutBatchService outBatchService;
  @Autowired private InnerBatchService innerBatchService;
  @Autowired private CaseDebtorService caseDebtorService;
  @Autowired private ReductionService reductionService;
  @Autowired private CustomOperationStatusService customOperationStatusService;
  @Autowired private CasePlanService casePlanService;
  @Autowired private ContactsMapper contactsMapper;
  @Autowired private CaseTagMapper caseTagMapper;
  @Autowired private CaseCooperationService caseCooperationService;
  @Autowired private CaseCooperationApplyService caseCooperationApplyService;
  @Autowired private RedisLock redisLock;
  @Autowired private CustomSearchFieldService customSearchFieldService;
  @Autowired private RemoteAlfredService remoteAlfredService;
  @Autowired private OrgConfigService orgConfigService;
  @Autowired private OrgSwitchMapper orgSwitchMapper;
  @Autowired private CaseTagRelMapper caseTagRelMapper;
  @Resource private LetterService letterService;
  @Resource private AssistApplyService assistApplyService;
  @Resource private CaseApplyService caseApplyService;
  @Resource private VisitService visitService;
  @Resource private CaseHistoryMapper caseHistoryMapper;
  @Resource private CaseLogMapper caseLogMapper;
  @Resource private LetterMediateService letterMediateService;
  @Resource private MediateAuditService mediateAuditService;
  @Autowired private CaseOperationUseLessService caseOperationUseLessService;
  @Autowired private AsyncTaskMapper asyncTaskMapper;
  @Autowired private RedisUtil redisUtil;
  @Autowired private CaseEndConfigService caseEndConfigService;
  @Resource private LawsuitService lawsuitService;
  @Resource private LawsuitApplyService lawsuitApplyService;
  @Resource private CaseCtrlService caseCtrlService;
  @Resource private CaseNoteService caseNoteService;
  @Autowired private EncryptProperties encryptProperties;
  @Autowired private EncryptService encryptService;
  @Resource private CaseOperationWayRelService caseOperationWayRelService;
  @Autowired private FileStorageStrategyFactory fileStorageStrategyFactory;
  @Autowired private MediationVideoRoomService mediationVideoRoomService;
  @Autowired private WorkOrderService workOrderService;
  @Autowired private StrategyExecCaseDetailService strategyExecCaseDetailService;
  @Autowired private StrategyTestDetailService strategyTestDetailService;
  @Autowired private RobotQueueService robotQueueService;
  @Autowired private RobotQueueFailService robotQueueFailService;
  @Autowired private AntiFraudService antiFraudService;
  @Autowired private AntiFraudRemarkService antiFraudRemarkService;
  @Autowired private VerifyApplyService verifyApplyService;
  @Autowired private LetterApplyService letterApplyService;
  @Autowired private RepayPlanService repayPlanService;
  @Autowired private PromissoryNoteService promissoryNoteService;
  @Autowired private CaseTagRelService caseTagRelService;

  private static final String ASYNC_PREFIX = "async-";

  private DuyanThreadExecutor fixedThreadPool = new DuyanThreadExecutor("case-pool");

  private static final List<String> CASE_BASE_MONEY_FIELD = Lists.newArrayList("capital", "rest_capital", "overdue_rate",
          "overdue_punish_rate", "penalty", "overdue_management_fee", "late_fee", "settled_amt", "amount_per_period",
          "repay_min_amt", "amount_paid", "amount_unpaid", "pro_price", "down_payment", "total_amount_paid", "load_amount",
          "rate");

  /**
   * 案件列表
   *
   * @param query 查询
   * @return {@link CaseListInfoVO}
   * @throws Exception 例外
   */
  public CaseListInfoVO listCase(CaseMultiQuery query) throws Exception {
    if (query.getBeAmc()){
      return listCaseAmc(query);
    }
    return listCaseAnmi(query);
  }

  /**
   * 安米案件列表
   *
   * @param query 查询
   * @return {@link CaseListInfoVO}
   * @throws Exception 例外
   */
  private CaseListInfoVO listCaseAnmi(CaseMultiQuery query) throws Exception {
    listCaseAnmiParamDeal(query);
    return listCases(query);
  }

  /**
   * 甲方案件列表
   *
   * @param query 查询
   * @return {@link CaseListInfoVO}
   * @throws Exception 例外
   */
  private CaseListInfoVO listCaseAmc(CaseMultiQuery query) throws Exception {
    listCaseAmcParamDeal(query);
    return listCases(query);
  }

  /**
   * 甲方案件列表查询参数处理
   *
   * @param query 查询
   */
  public void listCaseAmcParamDeal(CaseMultiQuery query) {
    UserSession userSession = UserUtils.getTokenUser();

    // 数据权限
    query.setOrgIds(userSession.getOrgId().toString());
    if (UserUtils.likeBranchAdmin()) {
      query.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader()) {
      query.setTeamIds(userSession.getTeamId().toString());
    }
    else if (!UserUtils.likeAdmin()) {
      throw new ApiException("非法角色，拒绝访问，请联系管理员");
    }

    // 催收手段
    if (ObjectUtil.isNotNull(query.getAllotAgent())) {
      query.setAllotAgentState(CaseEnums.AllotAgentState.YES.getCode());
    }
  }

  /**
   * 获取 案件列表
   *
   * @param query
   * @return
   */
  public CaseListInfoVO listCases(CaseMultiQuery query) throws Exception {
    // Long startAll = System.currentTimeMillis();
    if (query.getOrderBy() == null) {
      query.setOrderBy(0);
    }
    if (query.getSortRule() == null) {
      query.setSortRule(0);
    }
    // 参数转化（添加筛选条件时，分页案件查询、不分页案件查询(留案、批量调整等异步操作)，案件统计、案件导出方法需同时添加筛选条件）
    // 日期状态参数调整
    convertDate(query);
    // 快捷搜索调整
    coverSearchLabel(query);
    // 案件联系人条件转化成caseIds
    coverContactMobiles(query);

    // 分页
    PageParam pageParam = new PageParam();
    pageParam.setPage(query.getPage());
    pageParam.setLimit(query.getLimit());
    Page page = super.setPage(pageParam);
    List<CaseQueryResult> list = caseMapper.queryResultForMulti(query);
    List<CaseVO> vos = convertVO(list);
    // 案件共债个数
    List<Long> debtorList =
        list.stream()
            .filter(c -> c.getDebtId() != null)
            .map(CaseQueryResult::getDebtId)
            .collect(Collectors.toList());
    Map<Long, Integer> conjointCountMap = selectConjointMapByDebtor(getOrgId(), debtorList);
    List<Long> caseIdList = list.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
    Map<Long, Long> reductionTotalMap = reductionService.getReductionTotalMap(caseIdList);
    // 查询对应的最新时间案件催收小结
    List<CaseNote> caseNotes = new ArrayList<>();
    List<CaseOperation> caseOperations = new ArrayList<>();
    if (!CollectionUtils.isEmpty(caseIdList)) {
      caseNotes = caseNoteService.selectNoteByCaseIds(caseIdList, CaseNoteEnums.Type.NOTE.getCode());
      caseOperations = caseOperationMapper.selectDescByCaseIds(caseIdList);
    }
    Map<Long, CaseNote> caseNoteMap = new HashMap<>();
    if (!CollectionUtils.isEmpty(caseNotes)){
      caseNoteMap = caseNotes.stream().collect(Collectors.toMap(CaseNote::getCaseId, f -> f));
    }
    // 查询对应的最新时间催收备注
    Map<Long, CaseOperation> operationMap = new HashMap<>();
    if (!CollectionUtils.isEmpty(caseOperations)){
      operationMap = caseOperations.stream().collect(Collectors.toMap(CaseOperation::getCaseId, f -> f));
    }
    for (CaseVO vo : vos) {
      Integer conjointCount = conjointCountMap.get(vo.getDebtId());
      if (conjointCount != null) {
        conjointCount--;
      } else if (vo.getDebtId() != null) {
        conjointCount = 0;
      } else {
        conjointCount = null;
      }
      // 设置共债案件个数
      vo.setConjointCount(conjointCount);
      // 设置已减免金额
      Long reductionTotal = reductionTotalMap.get(vo.getId());
      vo.setReductionTotal(reductionTotal == null ? 0L : reductionTotal);
      // 催收小结
      if (caseNoteMap.containsKey(vo.getId())) {
        vo.setNote(caseNoteMap.get(vo.getId()).getContent());
      }
      // 催收备注
      if (operationMap.containsKey(vo.getId())) {
        vo.setOperationDesc(operationMap.get(vo.getId()).getDesc());
      }
    }

    // 当前是否为管理员端分配催员或者是催员端-已结案案件页面，是则列表显示结案类型信息
    if (query.getAction() != null && (query.getAction() == 3 || query.getAction() == 5)) {
      List<Long> endConfigIds = vos.stream().map(CaseVO::getEndConfigId)
              .filter(Objects::nonNull).distinct()
              .collect(Collectors.toList());
      Map<Long, String> endConfigMap = caseEndConfigService.queryEndConfigByIds(endConfigIds);
      if (!CollectionUtils.isEmpty(endConfigMap)) {
        for (CaseVO vo : vos) {
          if (vo.getEndConfigId() != null && endConfigMap.containsKey(vo.getEndConfigId())) {
            vo.setAttachedRes(endConfigMap.get(vo.getEndConfigId()));
          }
        }
      }
    }

    // 当前是否为分配催员或者是催员页面，是则列表显示管控信息
    if (query.getAction() != null && (query.getAction() == 3 || query.getAction() == 1)) {
      for (CaseVO vo : vos) {
        if (vo.getCtrlId() == null) {
          continue;
        }
        Integer caution = caseCtrlService.queryCaseCtrlCaution(vo.getCtrlId(), vo.getId());
        vo.setCaseCtrlCaution(caution);
      }
    }

    CaseListInfoVO pageOutput =
        new CaseListInfoVO(
            page.getPageNum(),
            page.getPageSize(),
          (int) page.getTotal(),
            vos);
    // Long endAll = System.currentTimeMillis();
    // log.info("总体耗时: " + (endAll - startAll));
    return pageOutput;
  }

  /**
   * 与案件列表接口参数一致，用作够选全部筛选结果时查询数据
   *
   * @param query
   * @return
   */
  public List<CaseQueryResult> queryResultForMulti(CaseMultiQuery query) {
    encryptQueryData(query);
    // 日期状态参数调整
    convertDate(query);
    // 快捷搜索调整
    coverSearchLabel(query);
    // 案件联系人条件转化成caseIds
    coverContactMobiles(query);
    // 若查询总数超过1W条则分页查询
    query.setOrderBy(12);
    query.setSortRule(1);
    Integer pageNum = 1;
    Integer limit = 5000;
    Integer totalPage = 1;
    PageParam pageParam = new PageParam();
    pageParam.setLimit(limit);
    List<CaseQueryResult> resultList = new ArrayList<>();
    while (true) {
      pageParam.setPage(pageNum);
      Page page = super.setPage(pageParam);
      List<CaseQueryResult> list = caseMapper.queryResultForMulti(query);
      resultList.addAll(list);
      if (pageNum == 1) {
        Integer total = page == null ? list.size() : (int) page.getTotal();
        int i = (total % limit) > 0 ? 1 : 0;
        totalPage = total / limit + i;
      }
      if (pageNum >= totalPage) {
        break;
      }
      pageNum++;
    }
    return resultList;
  }

  /**
   * 与案件列表接口参数一致，用作够选全部筛选结果时查询数据
   *
   * @param query
   * @return
   */
  public Long queryResultForMulti(CaseMultiQuery query,Consumer<PageOutput<CaseQueryResult>> callBack) {
    // 日期状态参数调整
    convertDate(query);
    // 快捷搜索调整
    coverSearchLabel(query);
    // 案件联系人条件转化成caseIds
    coverContactMobiles(query);
    // 若查询总数超过1W条则分页查询
    query.setOrderBy(12);
    query.setSortRule(1);
    Integer pageNum = 1;
    Integer limit = 5000;
    Integer totalPage = 1;
    PageParam pageParam = new PageParam();
    pageParam.setLimit(limit);
    long totalNum = 0L;
    while (true) {
      pageParam.setPage(pageNum);
      Page page = super.setPage(pageParam);
      List<CaseQueryResult> list = caseMapper.queryResultForMulti(query);
      PageOutput<CaseQueryResult> pageInfo=new PageOutput<>();
      pageInfo.setTotal((int)page.getTotal());
      pageInfo.setPageNum(page.getPageNum());
      pageInfo.setPageSize(page.getPageSize());
      pageInfo.setLimit(page.getPageSize());
      pageInfo.setPages(page.getPages());
      pageInfo.setList(list);
      callBack.accept(pageInfo);
      totalNum += list.size();
      if (pageNum == 1) {
        Integer total = page == null ? list.size() : (int) page.getTotal();
        int i = (total % limit) > 0 ? 1 : 0;
        totalPage = total / limit + i;
      }
      if (pageNum >= totalPage) {
        break;
      }
      pageNum++;
    }
    return totalNum;
  }

  public void coverSearchLabel(CaseMultiQuery query) {
    Integer searchLabel = query.getSearchLabel();
    if (searchLabel != null && searchLabel != 0) {
      Date todayStartTime = DateUtils.getTodayDateFormat();
      Date yesterdayStartTime = DateUtils.addDays(todayStartTime, -1);
      Date yesterdayEndTime = DateUtils.getEndTimeOfDay(yesterdayStartTime);

      if (CaseEnums.SearchLabelEnum.JRWGC.getCode() == searchLabel) {
        // 今日未跟催(上次跟进时间为今日之前)
        query.setLastFollowTimeStart(null);
        query.setLastFollowTimeEnd(DateUtils.formatDateFull(yesterdayEndTime));
      } else if (CaseEnums.SearchLabelEnum.JRYGC.getCode() == searchLabel) {
        // 今日已跟催(上次跟进时间为今天)
        query.setLastFollowTimeStart(DateUtils.formatDateFull(todayStartTime));
        query.setLastFollowTimeEnd(null);
      } else if (CaseEnums.SearchLabelEnum.THREEWGC.getCode() == searchLabel) {
        // 1-3天未跟催(上次跟进时间为1-3天之前)
        Date threeDaysAgoStartTime = DateUtils.addDays(todayStartTime, -3);
        query.setLastFollowTimeStart(DateUtils.formatDateFull(threeDaysAgoStartTime));
        query.setLastFollowTimeEnd(DateUtils.formatDateFull(yesterdayEndTime));
      } else if (CaseEnums.SearchLabelEnum.CWGC.getCode() == searchLabel) {
        // 从未跟催(催收结果为未处理案件)
        query.setLastFollowTimeStart(null);
        query.setLastFollowTimeEnd(null);
        query.setOperStatus(String.valueOf(CaseOperationEnums.ActionType.UNTREATED.getCode()));
      } else if (CaseEnums.SearchLabelEnum.THREEJTA.getCode() == searchLabel) {
        // 3天内将退案(委案截止时间小于等于3天)
        Date afterThreeDaysStartTime = DateUtils.addDays(todayStartTime, 3);
        query.setDeltEndStrOn(DateUtils.formatDateFull(todayStartTime));
        query.setDeltEndStrOff(DateUtils.formatDateFull(afterThreeDaysStartTime));
      } else if (CaseEnums.SearchLabelEnum.SEVENJTA.getCode() == searchLabel) {
        // 7天内将退案(委案截止时间小于等于7天)
        Date afterSevenDaysStartTime = DateUtils.addDays(todayStartTime, 7);
        query.setDeltEndStrOn(DateUtils.formatDateFull(todayStartTime));
        query.setDeltEndStrOff(DateUtils.formatDateFull(afterSevenDaysStartTime));
      }
      // 今日未跟催、1~3天未跟催仅含分案完成和留案状态的案件，3天内和7天内退案仅含留案、分案完成和停催状态的案件
      if (query.getAction() != null && query.getAction() == 3) {
        // 今日未跟催、1~3天未跟催
        if (CaseEnums.SearchLabelEnum.JRWGC.getCode() == searchLabel || CaseEnums.SearchLabelEnum.THREEWGC.getCode() == searchLabel) {
          // 仅含分案完成和留案状态的案件
          List<Integer> allotStatues = Lists.newArrayList(CaseEnums.AllotStatus.ALLOT_USER.getCode());
          List<Integer> caseStatues = Lists.newArrayList(CaseEnums.CaseStatus.DELAY.getCode());
          query.setAllotStatues(StringUtils.join(allotStatues, ","));
          query.setCallStatuses(StringUtils.join(caseStatues, ","));
        }
      }
    }
    Integer days = query.getCustomSearchLabel();
    if (days != null && days != 0) {
      Date xDayBefore = DateUtils.getDayBeforeXDay(new Date(), - days);
      query.setLastFollowTimeStart(null);
      query.setLastFollowTimeEnd(DateUtils.formatDateFull(xDayBefore));
    }
  }

  public void coverContactMobiles(CaseMultiQuery query) {
    List<String> contactMobiles = query.getContactMobiles();
    if (CollectionUtils.isEmpty(contactMobiles)) {
      return;
    }
    Long orgId = Long.valueOf(query.getOrgIds());
    // 非共债案件
    List<Long> caseIds = contactsMapper.selectRelIdsByContacts(orgId, contactMobiles, 0);
    // 共债案件
    List<Long> debtIds = contactsMapper.selectRelIdsByContacts(orgId, contactMobiles, 1);
    if (!CollectionUtils.isEmpty(debtIds)) {
      List<Long> conjointCaseIds = caseMapper.selectCaseIdsByDebtIds(orgId, debtIds);
      caseIds.addAll(conjointCaseIds);
    }
    List<Long> allCaseIds = Lists.newArrayList(Sets.newHashSet(caseIds));
    if (!CollectionUtils.isEmpty(query.getCaseIds())) {
      // 取交集
      List<Long> queryCaseIds = query.getCaseIds();
      allCaseIds.retainAll(queryCaseIds);
    }
    // 如果联系人查不到数据，则返回空
    if (CollectionUtils.isEmpty(allCaseIds)) {
      allCaseIds = Lists.newArrayList(-1L);
    }
    query.setCaseIds(allCaseIds);
  }

  public List<CaseQueryResult> queryResult(Map map) {
    return caseMapper.queryResult(handleSpecialParams(map));
  }

  public StatisCasesVO selectRepaymentCount(CaseMultiQuery query) {
    if (query.getBeAmc()){
      listCaseAmcParamDeal(query);
    } else {
      listCaseAnmiParamDeal(query);
    }
    // 日期状态参数调整
    convertDate(query);
    // 快捷搜索调整
    coverSearchLabel(query);
    // 案件联系人条件转化成caseIds
    coverContactMobiles(query);

    if(systemConfig.getESSwitch()){
      CaseMultiQueryDto dto = new CaseMultiQueryDto();
      BeanUtils.copyProperties(query, dto);
      if(!CollectionUtils.isEmpty(query.getFieldSearch())){
        dto.setFieldSearchMap(new HashMap<>());
        query.getFieldSearch().forEach(t -> dto.getFieldSearchMap().put(t.getKey(), t.getValues()));
      }
      CaseStatisticsResponse response = remoteAlfredService.fetchStatisticFromCaseInfoRemote(dto);
      RepaymentStatis repaymentStatis = new RepaymentStatis();
      repaymentStatis.setCaseTotal(response.getTotalCount().intValue());
      repaymentStatis.setTotalAmount(response.getTotalAmount());
      repaymentStatis.setTotalRepayAmount(response.getRepayAmount());
      StatisCasesVO statisCasesVO = new StatisCasesVO();
      // 还款金额
      statisCasesVO.setTotalRepayAmount(repaymentStatis.getTotalRepayAmount());
      // 列表金额
      statisCasesVO.setTotalAmount(repaymentStatis.getTotalAmount());
      // 还款户数
      statisCasesVO.setAccounts(repaymentStatis.getAccounts());
      // 案件总数
      statisCasesVO.setCaseTotal(repaymentStatis.getCaseTotal().intValue());

      return statisCasesVO;
    } else {
      RepaymentStatis repaymentStatis = repaymentMapper.selectRepaymentStatis(query);

      StatisCasesVO statisCasesVO = new StatisCasesVO();
      // 还款金额
      statisCasesVO.setTotalRepayAmount(repaymentStatis.getTotalRepayAmount());
      // 列表金额
      statisCasesVO.setTotalAmount(repaymentStatis.getTotalAmount());
      // 还款户数
      statisCasesVO.setAccounts(repaymentStatis.getAccounts());
      // 案件总数
      statisCasesVO.setCaseTotal(repaymentStatis.getCaseTotal().intValue());

      return statisCasesVO;
    }
  }

  public int updateStatusByIds(List<Long> list, Integer allotStatus) {
    if (CommonUtils.isEmpty(list)) {
      throw new ApiException("请选择案件！");
    }
    Map<String,Object> param = new HashMap<>();
    param.put("list", list);
    param.put("allotStatus", allotStatus);
    // 更新分案日期
    param.put("divisionTime", new Date());
    return caseMapper.updateBatchByCaseIds(param);
  }


  @Transactional(rollbackFor = Exception.class)
  public ResultMessage changeStatus(CaseSwitchStatusParam param) throws Exception {
    Integer changeStatus = param.getTargetCaseStatus();
    Date delayTime = null;
    Date restartTime = param.getRestartTime();
    String reason = param.getReason();
    // 判断留案状态必须传递时间
    if (CaseEnums.ChangeStatus.DELAY.getCode() == changeStatus) {
      if (param.getDelayTime() == null) {
        throw new ApiException("请选择留案时间");
      } else {
        delayTime = DateUtils.getStartTimeOfDate(param.getDelayTime());
      }
      if (restartTime != null && restartTime.after(delayTime)) {
        throw new ApiException("留案时间必须大于案件委案开始时间");
      }
    }
    // 查询状态是否可执行案件操作
//    validChangeStatus(param.getState(), changeStatus);
    if (!param.getAllSelect()) {
      // 同步执行
      doChangeStatus(param, changeStatus, delayTime, reason, restartTime);
    } else {
      // 创建异步任务
      asyncChangeStatus(param, changeStatus, delayTime, reason, restartTime);
    }
    return ResultMessage.success();
  }

  /**
   * 提交异步案件操作任务
   *
   * @param param
   * @throws Exception
   */
  private void asyncChangeStatus(CaseSwitchStatusParam param, Integer changeStatus, Date delayTime, String reason, Date restartTime) throws Exception {
    UserSession userSession = getTokenUser();

    //创建异步任务任务之前判断3分钟之前是否有相同操作
    CaseSwitchStatusParam caseSwitchStatusParam = AuthBeanUtils.copy(param, CaseSwitchStatusParam.class);
    caseSwitchStatusParam.setOrgIds(userSession.getOrgId().toString());
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(caseSwitchStatusParam).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }

    // 只获取id
    param.setSelectField("ca.id");
    List<CaseQueryResult> caseList =
        selectChangeStatusList(param, changeStatus, delayTime, restartTime);
    // 加锁保护
    caseList = protectCaseStatus(userSession.getOrgId(), caseList);
    if (CommonUtils.isEmpty(caseList)) {
      throw new ApiException("没有可操作案件，请重新确定案件状态！");
    }

    try {
      EndInfoDTO endInfoDTO = new EndInfoDTO();
      endInfoDTO.setEndConfigId(param.getChangeEndConfigId());
      endInfoDTO.setEndType(param.getChangeEndType());
      // 创建异步任务
      Long taskId =
          asyncTaskService.createChangeStatusTask(
              userSession,
              (long) caseList.size(),
              changeStatus,
              delayTime,
              reason,
              restartTime,
              AsyncTaskEnums.Status.ING.getCode(),
              endInfoDTO,
              param.getAutoRestartDate());
      // 任务添加到任务列表
      stringRedisTemplate
          .opsForSet()
          .add(
              KeyCache.CASE_CHANGE_STATUS_TASK_CASES + taskId,
              caseList.stream().map(c -> c.getId().toString()).toArray(String[]::new));
      stringRedisTemplate
          .opsForList()
          .leftPush(KeyCache.CASE_CHANGE_STATUS_TASK_ID_LIST, taskId.toString());
    } catch (Exception e) {
      removeProtectCases(userSession.getOrgId(), caseList);
      throw new ApiException(e.getMessage());
    }
  }

  private void doChangeStatus(CaseSwitchStatusParam param, Integer changeStatus, Date delayTime, String reason, Date restartTime) throws Exception {
    UserSession userSession = getTokenUser();
    // 筛选符合要求的案件，判断案件是否可执行更新状态
    List<CaseQueryResult> caseList =       selectChangeStatusList(param, changeStatus, delayTime, restartTime);
    caseList = protectCaseStatus(userSession.getOrgId(), caseList);
    try {
      if (CommonUtils.isEmpty(caseList) || caseList.size() != param.getCaseIds().size()) {
        if (CaseEnums.ChangeStatus.DELAY.getCode() == changeStatus) {
          throw new ApiException("留案时间必须大于案件委案开始时间");
        }
        throw new ApiException("请确定所选案件的状态");
      }
      EndInfoDTO endInfoDTO = new EndInfoDTO();
      endInfoDTO.setEndConfigId(param.getChangeEndConfigId());
      endInfoDTO.setEndType(param.getChangeEndType());
      // 创建任务
      Long taskId = asyncTaskService.createChangeStatusTask(getTokenUser(),(long) caseList.size(),
              changeStatus,delayTime,reason,restartTime,
              AsyncTaskEnums.Status.SUCCESS.getCode(),endInfoDTO,param.getAutoRestartDate());
      changeStatusUpdate(caseList, changeStatus, reason, delayTime, null, getTokenUser(), restartTime, taskId, endInfoDTO, param.getAutoRestartDate());
    } finally {
      removeProtectCases(userSession.getOrgId(), caseList);
    }
  }


  public List<CaseQueryResult> selectChangeStatusList(CaseSwitchStatusParam param, Integer changeStatus, Date delayTime, Date restartTime) throws IllegalAccessException, InvocationTargetException, InstantiationException {
    CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
    BeanUtil.copyProperties(param, caseMultiQuery);
    if (!param.getAllSelect()) {
      AssertUtil.notEmpty(caseMultiQuery.getCaseIds(),"请选择案件");
    } else {
      caseMultiQuery.setFields(Lists.newArrayList("id"));
      caseMultiQuery.setSelectField("ca.id");
      caseMultiQuery.setCaseIds(null);
    }

    if (caseMultiQuery.getBeAmc()){
      listCaseAmcParamDeal(caseMultiQuery);
    } else {
      listCaseAnmiParamDeal(caseMultiQuery);
    }

    coverChangeStatus(caseMultiQuery, changeStatus, delayTime, restartTime);
    List<CaseQueryResult> caseList;
    if (systemConfig.getESSwitch()) {
      caseList = getAllCasesUsingEs(caseMultiQuery);
    }else{
      caseList = queryResultForMulti(caseMultiQuery);
    }
    AssertUtil.notEmpty(caseList,"所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
    return caseList;
  }

  private void coverChangeStatus(CaseMultiQuery caseMultiQuery, Integer changeStatus, Date delayTime, Date restartTime) {
    // 留案时间必须大于委案开始时间(若同时传入开始时间则不需要判断)
    if (CaseEnums.ChangeStatus.DELAY.getCode() == changeStatus && restartTime == null) {
      caseMultiQuery.setDeltStartStrOff(DateUtils.formatDate(DateUtils.addDays(delayTime, 1)));
    }
  }

  /**
   * 修改案件状态等信息
   *
   * @param caseList 案件列表
   * @param changeStatus 修改状态
   * @param reason 原因
   * @param delayTime 留案时间
   * @param returnTime 实际退案时间
   * @param userSession session 可能为空
   * @param restartTime 委案开始时间
   * @param taskId 任务id
   */
  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public void changeStatusUpdate(
          List<CaseQueryResult> caseList,
          Integer changeStatus,
          String reason,
          Date delayTime,
          Date returnTime,
          UserSession userSession,
          Date restartTime,
          Long taskId,
          EndInfoDTO endInfo,
          Date autoRestartDate) {
    if (CollectionUtils.isEmpty(caseList)) {
      return;
    }
    List<Long> caseIds = caseList.stream().map(Case::getId).collect(Collectors.toList());
    Map<String,Object> updateParams = new HashMap<>();
    updateParams.put("list", caseIds);
    updateParams.put("desc", reason);
    updateParams.put("returnTime", returnTime);
    updateParams.put("autoRestartDate", autoRestartDate);
    updateParams.put("autoRestart",autoRestartDate!=null);
    updateParams.put("caseStatus", changeStatus);
    if (CaseEnums.ChangeStatus.DELAY.getCode() == changeStatus) {
      updateParams.put("entrustEndTime", delayTime);
      updateParams.put("entrustStartTime", restartTime);
    }
    if (changeStatus == CaseEnums.ChangeStatus.END.getCode()) {
      updateParams.put("endTime",new Date());
      if (endInfo != null) {
        updateParams.put("endType", endInfo.getEndType());
        updateParams.put("endConfigId", endInfo.getEndConfigId());
      }
    }
    if (changeStatus == CaseEnums.ChangeStatus.STOP.getCode()) {
      updateParams.put("stopDate",new Date());
    }
    // 待审批协催申请自动拒绝，待协催自动完成
    if (CaseEnums.ChangeStatus.DELAY.getCode() != changeStatus) {
      List<Long> caseIdList = caseList.stream().map(Case::getId).collect(Collectors.toList());
      caseCooperationApplyService.batchRefuseApply(caseIdList, userSession, "案件状态变更，需重新申请");
      caseCooperationService.batchFinishCooperation(caseIdList, userSession, "案件状态变更，协催自动结束");
      updateParams.put("cooperationStatus", CaseEnums.CooperationStatus.NO.getCode());
    }
    if (changeStatus == CaseEnums.ChangeStatus.END.getCode()) {
      OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(userSession.getOrgId());
      if (OrgSwitchEnums.CaseNoSwitch.OPEN.getCode().equals(orgSwitch.getCaseNoSwitch())) {
        Map<Long, DepTeam> depMap = depTeamService.getDepHavingNumberMap(userSession.getOrgId());
        if (!CollectionUtils.isEmpty(depMap)) {
          Map<Long, List<CaseQueryResult>> caseMap = caseList.stream().filter(c -> c.getDepId() != null).collect(Collectors.groupingBy(CaseQueryResult::getDepId));
          for (Map.Entry<Long, List<CaseQueryResult>> entry: caseMap.entrySet()){
            if (depMap.containsKey(entry.getKey())) {
              DepTeam depTeam = depMap.get(entry.getKey());
              String teamNo = depTeam.getTeamNo();
              int endSeq = depTeam.getEndSeq();
              List<CaseQueryResult> caseSubList = entry.getValue();
              for(CaseQueryResult item : caseSubList) {
                endSeq = endSeq + 1;
                Map<String, Object> caseUpdateParam = new HashMap<>();
                caseUpdateParam.put("caseId", item.getId());
                String  endSeqStr = depTeamService.fillSeqZero(endSeq);
                String endNo = "J" + teamNo +  Calendar.getInstance().get(Calendar.YEAR) + endSeqStr;
                caseUpdateParam.put("endNo", endNo);
                caseMapper.updateNumberByCaseId(caseUpdateParam);
              }
              depTeam.setEndSeq(endSeq);
              depTeamService.updateEndSeq(depTeam);
            }
          }
        }
      }

    }
    // 更新状态
    caseMapper.updateBatchByCaseIds(updateParams);

    // 案件退案、作废、停催、结案 → 案件关联的待外访、外访中的外访状态更新为：已取消， 外访申请在审核中更新为：已取消
    if (Objects.equals(changeStatus,CaseEnums.ChangeStatus.TOVOID.getCode()) ||
            Objects.equals(changeStatus,CaseEnums.ChangeStatus.END.getCode()) ||
            Objects.equals(changeStatus,CaseEnums.ChangeStatus.STOP.getCode())) {
      visitService.cancelVisitApply(caseIds);
      visitService.cancelVisit(caseIds);
    }

    // log记录
    changeStatusEvent(caseList, updateParams, changeStatus, userSession, taskId);
    // 从计划里面移除案件
    if (CaseEnums.ChangeStatus.TOVOID.getCode() == changeStatus
            || CaseEnums.ChangeStatus.END.getCode() == changeStatus
            || CaseEnums.ChangeStatus.STOP.getCode() == changeStatus) {
      casePlanService.removeCase(caseList, userSession);
    }
  }

  private void changeStatusEvent(List<CaseQueryResult> caseList,  Map<String,Object> updateParams, Integer changeStatus, UserSession userSession, Long taskId) {
    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setCases(JsonUtils.toList(JsonUtils.toJson(caseList), Case.class));
    caseBatchUpdateEvent.setTaskId(taskId);
    Map<Long, Map<String, String>> diffMap = new HashMap<>();
    Object updateCaseStatus = updateParams.get("caseStatus");
    Object entrustEndTime = updateParams.get("entrustEndTime");
    Object returnTime = updateParams.get("returnTime");
    Object entrustStartTime = updateParams.get("entrustStartTime");
    caseList.forEach(
        aCase -> {
          Diff diff = new Diff();
          if (Objects.equals(updateCaseStatus, aCase.getCaseStatus())) {
            diff.addDiff("caseStatus", updateCaseStatus, aCase.getCaseStatus());
          }
          if (entrustEndTime != null) {
            diff.addDiff("entrust_end_time", aCase.getEntrustEndTime(), entrustEndTime);
          }
          if (entrustStartTime != null) {
            diff.addDiff("entrust_start_time", aCase.getEntrustStartTime(), entrustStartTime);
          }
          if (aCase.getReturnTime() != null || returnTime != null) {
            diff.addDiff("return_time", aCase.getReturnTime() != null ? aCase.getReturnTime() : "null", returnTime != null ? returnTime : "null");
          }
          diff.addDiff("desc", null, updateParams.get("desc"));
          diffMap.put(aCase.getId(), diff.toStringMap());
        });
    // 组装事件信息
    caseBatchUpdateEvent.setDiffMap(diffMap);
    caseBatchUpdateEvent.setUserSession(userSession);
    if (CaseEnums.ChangeStatus.TOVOID.getCode() == changeStatus) {
      caseBatchUpdateEvent.setType(CaseLogEnums.Type.TOVOID.getCode());
    }
    if (CaseEnums.ChangeStatus.END.getCode() == changeStatus) {
      caseBatchUpdateEvent.setType(CaseLogEnums.Type.END.getCode());
    }
    if (CaseEnums.ChangeStatus.STOP.getCode() == changeStatus) {
      caseBatchUpdateEvent.setType(CaseLogEnums.Type.STOP.getCode());
    }
    if (CaseEnums.ChangeStatus.DELAY.getCode() == changeStatus) {
      caseBatchUpdateEvent.setType(CaseLogEnums.Type.DELAY.getCode());
    }
    if (CaseEnums.ChangeStatus.RESTART.getCode() == changeStatus) { // 恢复
      caseBatchUpdateEvent.setType(CaseLogEnums.Type.RESTART.getCode());
    }
    applicationContext.publishEvent(caseBatchUpdateEvent);
  }

  public HomePageVO homePage() {
    UserSession userSession = UserUtils.getTokenUser();
    Role role = RoleSessionUtils.getRole();
    if (role == null) {
      throw new ApiException("用户角色状态异常！");
    }
    List<Long> userIds = new ArrayList<>();
    // 坐席
    if (role.getType().equals(SystemConst.ROLE_TYPE_CHARIMAN)) {
      userIds.add(userSession.getId());
      // 管理员
    } else {
      // 查询管理员所属公司下所有催员编号
      userIds = UserUtils.getUserIds();
    }
    HomePageVO vo = getHomePageValues(userIds);
    return vo;
  }

  /**
   * @param ids
   * @return
   */
  private HomePageVO getHomePageValues(List<Long> ids) {
    HomePageVO vo = new HomePageVO();
    Integer caseTotal = 0;
    Integer collectionTotal = 0;
    Map<String, Long> products = new HashMap<>();
    List<MapBean> list = new ArrayList<>();
    // 查询当天新增量
    Map<String, Object> map = new HashMap<>();
    String start = DateUtils.formatDateFull(DateUtils.getStartTimeOfDate(new Date()));
    map.put("beginDate", start);
    map.put("userIds", ids);
    map.put("orderByTime", "orderByTime");
    caseTotal = caseMapper.queryResult(map).size();
    // 查询再催案件，和在催案件占比
    map.clear();
    map.put("allotStatus", CaseEnums.AllotStatus.ALLOT_USER.getCode());
    map.put("userIds", ids);
    map.put("orderByTime", "orderByTime");
    List<CaseQueryResult> cases = caseMapper.queryResult(map);
    collectionTotal = cases.size();
    Map<Long, Long> collect =
        cases.stream()
            .collect(Collectors.groupingBy(CaseQueryResult::getProductId, Collectors.counting()));
    products = covertName(collect);
    // 查询一周还款数据
    String beginDate = DateUtils.formatDateFull(DateUtils.getStartTimeOfWeek(new Date()));
    map.clear();
    map.put("userIds", ids);
    map.put("status", 0);
    map.put("start", beginDate);
    Map<String, List<RepaymentQuery>> datas =
        repaymentService.queryResult(map).stream()
            .collect(Collectors.groupingBy(l -> DateUtils.formatDate(l.getCreateTime())));
    Iterator<String> iterator = datas.keySet().iterator();

    while (iterator.hasNext()) {
      String next = iterator.next();
      MapBean mapBean = new MapBean();
      mapBean.setDay(next);
      List<RepaymentQuery> querys = datas.get(next);
      Map<Long, Long> maps =
          querys.stream()
              .collect(
                  Collectors.groupingBy(
                      RepaymentQuery::getProductId,
                      Collectors.summingLong(RepaymentQuery::getRepaymentAmount)));
      mapBean.setMap(covertName(maps));
      list.add(mapBean);
    }
    vo.setCollectionTotal(collectionTotal);
    vo.setNewTotal(caseTotal);
    vo.setRepayment(list);
    vo.setProCount(products);
    return vo;
  }

  // 产品id 转换成 name
  private Map<String, Long> covertName(Map<Long, Long> products) {
    Map<String, Long> map = new HashMap<>();
    Iterator<Long> iterator = products.keySet().iterator();
    while (iterator.hasNext()) {
      Long next = iterator.next();
      map.put(productService.getNames().get(next), products.get(next));
    }
    return map;
  }

  /**
   * 根据map中的状态设置进程，把map中的string类型的string转为date型
   *
   * @param map
   * @return
   */
  private Map<String,Object> handleSpecialParams(Map<String,Object> map) {
    // 把所有string类型的时间都转为date
    Object start = map.get("start");
    if (null != start) {
      map.put("start", new Date(Long.valueOf(map.get("start").toString())));
    }
    Object end = map.get("end");
    if (null != end) {
      map.put("end", new Date(Long.valueOf(map.get("end").toString())));
    }
    Object deltStart = map.get("detlStart");
    if (null != deltStart) {
      map.put("detlStart", new Date(Long.valueOf(map.get("detlStart").toString())));
    }
    Object deltEnd = map.get("deltEnd");
    if (null != deltEnd) {
      map.put("deltEnd", new Date(Long.valueOf(map.get("deltEnd").toString())));
    }
    Object delStartRange = map.get("deltStartStr");
    if (null != delStartRange) {
      String[] range = String.valueOf(delStartRange).split(",");
      if (range.length == 2) {
        map.put("deltStartOn", new Date(Long.valueOf(range[0])));
        map.put("deltStartOff", new Date(Long.valueOf(range[1])));
      }
    }
    Object delEndRange = map.get("deltEndStr");
    if (null != delEndRange) {
      String[] range = String.valueOf(delEndRange).split(",");
      if (range.length == 2) {
        map.put("deltEndOn", new Date(Long.valueOf(range[0])));
        map.put("deltEndOff", new Date(Long.valueOf(range[1])));
      }
    }
    return map;
  }
  /**
   * 判断员工是否有案件
   *
   * @param user
   * @return
   */
  public boolean haveCases(User user) {
    Map<String,Object> queryMap = new HashMap<>();
    queryMap.put("userId", user.getId());
    queryMap.put("allotStatues",ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode()));
    return caseMapper.queryCount(queryMap) > 0;
  }

  /**
   * 查询机构下是否有留案案件
   *
   * @param depId 机构id
   * @return 是否
   */
  public boolean haveDepCases(Long depId) {
    Map<String,Object> queryMap = new HashMap<>();
    queryMap.put("depId", depId);
    queryMap.put("allotStatues",ImmutableList.of(CaseEnums.AllotStatus.ALLOT_BRANCH.getCode()));
    return caseMapper.queryCount(queryMap) > 0;
  }

  @Transactional
  public void updateBatch(List<Case> newCas) {
    if (CommonUtil.isEmpty(newCas)) {
      return;
    }
    caseMapper.updateBatch(newCas);
  }

  @Transactional(rollbackFor = Exception.class)
  public void delCases(CaseMultiQuery deleteParam) throws Exception {
    // 彻底删除和导入不能同时进行
    UserSession userSession = getTokenUser();
    if (fileStoreService.isCaseImport(userSession.getOrgId())) {
      throw new ApiException("案件导入和彻底删除不能同时进行！");
    }
    if (deleteParam.getAllSelect()) {
      // 异步
      asyncDelCases(deleteParam);
    } else {
      // 同步
      doAsyncDelCases(deleteParam);
    }
  }

  public void doAsyncDelCases(CaseMultiQuery deleteParam) throws Exception {
    UserSession userSession = getTokenUser();
    List<CaseQueryResult> caseQueryResultList = selectDelList(deleteParam, getOrgId());
    // 状态保护
    caseQueryResultList = protectCaseStatus(userSession.getOrgId(), caseQueryResultList);
    try {
      // 创建任务
      Long taskId =
          asyncTaskService.createDeleteTask(
              getTokenUser(),
              Long.valueOf(caseQueryResultList.size()),
              deleteParam.getAllSelect(),
              AsyncTaskEnums.Status.SUCCESS.getCode());
      batchDelCase(caseQueryResultList, getTokenUser(), taskId);

      // 判断共债案件，删除联系人
      List<Long> debtorIdList = caseQueryResultList.stream().filter(c -> c.getDebtId() != null)
                  .map(CaseQueryResult::getDebtId).collect(Collectors.toList());
      List<Long> notConjointCaseIds = caseQueryResultList.stream().filter(c -> c.getDebtId() == null).map(Case::getId).collect(Collectors.toList());
      // 查出所有删除当前案件之后，还存在的共债案件的idcard
      List<Long> existList = selectExistConjointList(userSession.getOrgId(), debtorIdList);
      // 移除后，说明剩下的都是一个idCard内所有共债案件都被删除了，也就是可以去删除contacts、还有债务人的信息表了
      debtorIdList = debtorIdList.stream().filter(d -> !existList.contains(d)).collect(Collectors.toList());
      // 删除共债通讯录
      contactsService.deleteContactsByRelIds(userSession.getOrgId(), debtorIdList, Contacts.IsConjoint.YES.getCode());
      // 删除非共债通讯录
      contactsService.deleteContactsByRelIds(
          userSession.getOrgId(),
          Lists.newArrayList(notConjointCaseIds),
          Contacts.IsConjoint.NO.getCode());
      // 删除债务人信息
      caseDebtorService.deleteDebtorsByDebtId(debtorIdList);
    } finally {
      removeProtectCases(userSession.getOrgId(), caseQueryResultList);
    }
  }

  /**
   * 提交异步删除案件任务
   *
   * @param param 删除案件条件
   * @throws Exception
   */
  private void asyncDelCases(CaseMultiQuery param) throws Exception {
    UserSession userSession = getTokenUser();
    //添加异步任务之前判断5分钟之内相同任务有没有创建过
    param.setOrgIds(userSession.getOrgId().toString());
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(param).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }
    // 公司隔离
    if (param.getBeAmc()) {
      listCaseAmcParamDeal(param);
    } else {
      listCaseAnmiParamDeal(param);
    }
    List<CaseQueryResult> caseList = new ArrayList<>();
    param.setFields(Lists.newArrayList("id", "case_status", "allot_status", "recovery"));
    if (systemConfig.getESSwitch()) {
      caseList = getAllCasesUsingEs(param);
    } else {
      caseList = queryResultForMulti(param);
    }
    // 加锁，状态保护
    caseList = protectCaseStatus(userSession.getOrgId(), caseList);
    if (CommonUtils.isEmpty(caseList)) {
      throw new ApiException("没有可操作案件，请重新确定案件状态！");
    }
    try {
      List<String> caseIdList =
          caseList.stream().map(c -> c.getId().toString()).collect(Collectors.toList());
      // 创建异步任务
      Long taskId =
          asyncTaskService.createDeleteTask(
              userSession,
              Long.valueOf(caseIdList.size()),
              param.getAllSelect(),
              AsyncTaskEnums.Status.ING.getCode());
      // 任务添加到任务列表
      stringRedisTemplate
          .opsForSet()
          .add(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + taskId,
              caseIdList.toArray(new String[0]));
      stringRedisTemplate
          .opsForList()
          .leftPush(KeyCache.CASE_CHANGE_STATUS_TASK_ID_LIST, taskId.toString());
    } catch (Exception e) {
      removeProtectCases(userSession.getOrgId(), caseList);
      throw new ApiException(e.getMessage());
    }
  }

  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public void batchDelCase(
      List<CaseQueryResult> caseQueryResultList,
      UserSession userSession,
      Long taskId)
      throws Exception {
    List<Case> cases = BeanUtil.copyPropertiesFromList(caseQueryResultList, Case.class);
    if (CommonUtils.isEmpty(cases)) {
      return;
    }
    List<Long> caseIds = cases.stream().map(Case::getId).collect(Collectors.toList());
    // 彻底删除
    for (Case ca : cases) {
      ca.setRecovery((byte) CaseEnums.Recovery.DELETE_COMPLETELY.getCode());
      // 每个案件的案件编号需要加时间戳
      String newOutSerialNo = ca.getOutSerialNo() + "-" + new Date().getTime();
      ca.setOutSerialNo(newOutSerialNo);
      ca.setUpdateBy(userSession.getId());
    }
    delJoinDataByCaseIds(caseIds);
    // 移除计划中的案件
    casePlanService.removeCase(caseQueryResultList, userSession);

    updateBatch(cases);
    // 生成log
    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setCaseInfoResultList(
        JsonUtils.toList(JsonUtils.toJson(caseQueryResultList), CaseInfoResult.class));
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.DELETE_COMPLETE.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);

  }

  private List<CaseQueryResult> selectDelList(
          CaseMultiQuery deleteParam, Long orgId) {
    deleteParam.setOrgIds(orgId.toString());
    if (deleteParam.getBeAmc()) {
      listCaseAmcParamDeal(deleteParam);
    } else {
      listCaseAnmiParamDeal(deleteParam);
    }
    List<CaseQueryResult> caseQueryResultList = queryResultForMulti(deleteParam);
    if (CommonUtils.isEmpty(caseQueryResultList)) {
      throw new ApiException("没有可操作案件，请重新确定案件状态！");
    }
    return caseQueryResultList;
  }


  /**
   * 回收站列表查询
   *
   * @param pageParam
   * @return
   */
  public CaseListInfoVO garbageList(PageParam pageParam) throws Exception {
    UserSession userSession = getTokenUser();
    // 查询操作有所属团队用户编号
    Map<String,Object> map = new HashMap<>();
    map.put("orgId", getOrgId());
    if (UserUtils.likeBranchAdmin()) {
      map.put("depId", getTokenUser().getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      map.put("teamId", userSession.getTeamId());
    }
    map.put("recovery", CaseEnums.Recovery.DELETE.getCode());
    map.put("garbage", "garbage");
    map.put("orderByTime", "orderByTime");
    Page page = super.setPage(pageParam);
    List<CaseQueryResult> delCases = caseMapper.queryResult(map);

    List<CaseVO> vos = convertVO(delCases);
    CaseListInfoVO caseListInfoVO =
        new CaseListInfoVO(
            page.getPageNum(),
            page.getPageSize(),
            page != null ? (int) page.getTotal() : vos.size(),
            vos);
    return caseListInfoVO;
  }

  /**
   * 恢复案件
   *
   * @param caseIds
   */
  @Transactional(rollbackFor = Exception.class)
  public void recoverCases(List<Long> caseIds) {
    if (CommonUtils.isEmpty(caseIds)) {
      throw new ApiException("请选择案件！");
    }
    Example caseExp = new Example(Case.class);
    Example.Criteria caseCrt = caseExp.createCriteria();
    caseCrt.andIn("id", caseIds);
    List<Case> cases = selectByExample(caseExp);
    for (Case c : cases) {
      c.setRecovery((byte) CaseEnums.Recovery.NORMAL.getCode());
    }
    updateBatch(cases);
    // 生成log
    UserSession userSession = getTokenUser();
    Long taskId = asyncTaskService.createRecoverTask(userSession, Long.valueOf(cases.size()));
    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setCaseIds(caseIds);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.RECOVERY.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);
    // 恢复计划中的案件
//    List<Integer> caseStatues = Lists.newArrayList(CaseEnums.CaseStatus.DELAY.getCode(), CaseEnums.CaseStatus.NORMAL.getCode());
//    cases =  cases.stream().filter(c -> caseStatues.contains(c.getCaseStatus())).collect(Collectors.toList());
//    if (CollectionUtils.isEmpty(cases)) {
//      return;
//    }
//    List<Long> caseIdList = cases.stream().map(c -> c.getId()).collect(Collectors.toList());
//    List<CaseDuyanPlanRel> relList = casePlanService.selectPlanCaseIds(caseIdList, CasePlanRelEnums.Status.REMOVE.getCode());
//    if (CollectionUtils.isEmpty(relList)) {
//      return;
//    }
    // 查询案件详情
//    List<Long> filterCaseIds = relList.stream().map(c -> c.getCaseId()).collect(Collectors.toList());
//    CaseMultiQuery query = new CaseMultiQuery();
//    query.setCaseIds(filterCaseIds);
//    List<CaseQueryResult> caseQueryResultList = queryResultForMulti(query);
//    List<CaseContact> caseContacts=new ArrayList<>();
//    caseQueryResultList.forEach(obj->{
//      CaseContact caseContact=new CaseContact();
//      caseContact.setCaseId(obj.getId());
//      caseContact.setContactMobile(obj.getOwnMobile());
//      caseContact.setContactName(obj.getName());
//      BeanUtils.copyProperties(obj,caseContact);
//      caseContacts.add(caseContact);
//    });
    // 移除后再添加
//    casePlanService.addByPlanRel(caseContacts, relList, UserUtils.getTokenUser());
  }

  public NearestCaseVO nearestCase(String mobile) {
    if (encryptProperties.getEnable()) {
      mobile = encryptService.encrypt(mobile);
    }
    UserSession userSession = getTokenUser();
    NearestCaseVO vo = new NearestCaseVO();
    Long caseId;
    if(systemConfig.getESSwitch()){
      CaseOperationParamDto dto = new CaseOperationParamDto();
      //设置分页
      dto.setLimit(1);
      dto.setPage(1);
      //根据创建时间排序，生产环境es催记表id不是数字类型，排序有问题、草！！！草！草！草！
      dto.setSortField("createTime");
      dto.setSortAsc(false);
      dto.setOrgId(userSession.getOrgId());
      dto.setCreateType(new Byte("0"));
      dto.setUserIds(userSession.getId().toString());
      List<String> list = new ArrayList<>();
      list.add(mobile);
      dto.setConMobiles(list);
      //es查找最近催记
      TransferCaseOperationResult res = remoteAlfredService.fetchFromNestedCaseOperationRemote(dto);
      if (res == null || res.getCaseId() == null) {
        return null;
      }
      caseId = res.getCaseId();
    }else {
      Map<String,Object> map = new HashMap<>();
      map.put("mobile", mobile);
      map.put("orgId", userSession.getOrgId());
      map.put("userId", userSession.getId());
      // 找最近的一条催记
      caseId = caseOperationMapper.queryNearestByCaseOpe(map);
    }
    if (caseId != null) {
      Case ca = caseMapper.selectByPrimaryKey(caseId);
      getContactInfo(ca, vo, mobile);
      vo.setName(ca.getName());
      vo.setIsDel(ca.getRecovery() == CaseEnums.Recovery.DELETE.getCode() || ca.getRecovery() == CaseEnums.Recovery.DELETE_COMPLETELY.getCode());
    }
    vo.setCaseId(caseId);
    return vo;
  }

  /**
   * 设置联系人、联系人关系、联系人电话
   *
   * @param ca
   * @param vo
   * @param mobile
   */
  private void getContactInfo(Case ca, NearestCaseVO vo, String mobile) {
    Long relId = ca.getDebtId() == null ? ca.getId() : ca.getDebtId();
    Integer isConjoint = ca.getDebtId() == null ? 0 : 1;
    Example contactExp = new Example(Contacts.class);
    Example.Criteria contactCrt = contactExp.createCriteria();
    contactCrt.andEqualTo("relId", relId);
    contactCrt.andEqualTo("isConjoint", isConjoint);
    contactCrt.andEqualTo("mobile", mobile);
    List<Contacts> contacts = contactsService.selectByExample(contactExp);
    if (!CommonUtils.isEmpty(contacts)) {
      Contacts c = contacts.get(0);
      vo.setContactsId(c.getId());
      vo.setRelationType(c.getRelationType());
      vo.setContactName(c.getName());
    }
  }

  /**
   * 格式化时间，支持 Long时间戳和String格式化时间
   *
   * @param query query
   * @return 返回yyyy-MM-dd HH:mm:ss格式时间
   */
  public CaseMultiQuery convertDate(CaseMultiQuery query) {
    query.setDeltEnd(convertDate(query.getDeltEnd()));
    query.setDeltStart(convertDate(query.getDeltStart()));
    String delStartRange = query.getDeltStartStr();
    if (null != delStartRange) {
      String[] range = delStartRange.split(",");
      if (range.length == 2) {
        query.setDeltStartStrOn(convertDate(range[0]));
        query.setDeltStartStrOff(convertDate(range[1]));
      }
    }
    String delEndRange = query.getDeltEndStr();
    if (null != delEndRange) {
      String[] range = delEndRange.split(",");
      if (range.length == 2) {
        query.setDeltEndStrOn(convertDate(range[0]));
        query.setDeltEndStrOff(convertDate(range[1]));
      }
    }
    // 逾期日期的范围
    if (query.getOverdueDate() != null && query.getOverdueDate().split(",").length == 2) {
      query.setOverdueDateStart(convertDate(query.getOverdueDate().split(",")[0]));
      query.setOverdueDateEnd(convertDate(query.getOverdueDate().split(",")[1]));
    }
    // 上次跟进时间的范围
    if (query.getLastFollowTime() != null && query.getLastFollowTime().split(",").length == 2) {
      query.setLastFollowTimeStart(convertDate(query.getLastFollowTime().split(",")[0]));
      query.setLastFollowTimeEnd(convertDate(query.getLastFollowTime().split(",")[1]));
    }
    // 最近智能协催时间
    if (query.getAutoAssistDate() != null && query.getAutoAssistDate().split(",").length == 2) {
      query.setAutoAssistDateStart(convertDate(query.getAutoAssistDate().split(",")[0]));
      query.setAutoAssistDateEnd(convertDate(query.getAutoAssistDate().split(",")[1]));
    }
    // 进行中、调整委案开始时间范围终点
    if (query.getAction() != null) {
      if (1 == query.getAction() || 9 == query.getAction()) {
        Date today = DateUtils.getTodayDateFormat();
        String deltStartStrOff = query.getDeltStartStrOff();
        if (StringUtils.isBlank(deltStartStrOff)) {
          query.setDeltStartStrOff(DateUtils.formatDate(today));
        } else {
          query.setDeltStartStrOff(
              DateUtils.parseDate(deltStartStrOff).after(today)
                  ? DateUtils.formatDate(today)
                  : deltStartStrOff);
        }
      }
      // 未开始 调整委案时间搜索
      if (6 == query.getAction()) {
        Date tomorrow = DateUtils.addDays(DateUtils.getTodayDateFormat(), 1);
        String deltStartStrOn = query.getDeltStartStrOn();
        if (StringUtils.isBlank(deltStartStrOn)) {
          query.setDeltStartStrOn(DateUtils.formatDate(tomorrow));
        } else {
          query.setDeltStartStrOn(
              DateUtils.parseDate(deltStartStrOn).before(tomorrow)
                  ? DateUtils.formatDate(tomorrow)
                  : deltStartStrOn);
        }
      }
    }
    // 下次跟进时间
    if (query.getOperationNextTime() != null
        && query.getOperationNextTime().split(",").length == 2) {
      query.setOperationNextTimeStart(convertDate(query.getOperationNextTime().split(",")[0]));
      query.setOperationNextTimeEnd(convertDate(query.getOperationNextTime().split(",")[1]));
    }
    // 分案日期
    if (query.getDivisionTime() != null && query.getDivisionTime().split(",").length == 2) {
      query.setDivisionTimeStart(convertDate(query.getDivisionTime().split(",")[0]));
      query.setDivisionTimeEnd(convertDate(query.getDivisionTime().split(",")[1]));
    }

    // 结案日期
    if (query.getEndTime() != null && query.getEndTime().split(",").length == 2) {
      query.setEndTimeStart(convertDate(query.getEndTime().split(",")[0]));
      query.setEndTimeEnd(convertDate(query.getEndTime().split(",")[1]));
    }

    // 案件列表增加贷款金额筛选搜索，包括“待分配、分配至组、分配至催员”三个模块
    query.setMinAmount(ObjectUtil.isNull(query.getMinAmount()) ? null : query.getMinAmount().multiply(new BigDecimal(1000)));
    query.setMaxAmount(ObjectUtil.isNull(query.getMaxAmount()) ? null : query.getMaxAmount().multiply(new BigDecimal(1000)));

    // 承诺还款日期的范围
    if (StrUtil.isNotBlank(query.getPtpTimeStr()) && query.getPtpTimeStr().split(",").length == 2) {
      query.setPtpTimeStart(convertDate(query.getPtpTimeStr().split(",")[0]));
      query.setPtpTimeEnd(convertDate(query.getPtpTimeStr().split(",")[1]));
    }
    // 承诺还款金额
    query.setMinPtpAmount(ObjectUtil.isNull(query.getMinPtpAmount()) ? null : query.getMinPtpAmount().multiply(new BigDecimal(1000)));
    query.setMaxPtpAmount(ObjectUtil.isNull(query.getMaxPtpAmount()) ? null : query.getMaxPtpAmount().multiply(new BigDecimal(1000)));

    // 催收手段更新日期的范围
    if (StrUtil.isNotBlank(query.getWayUpdateDateStr()) && query.getWayUpdateDateStr().split(",").length == 2) {
      query.setWayUpdateDateStart(convertDate(query.getWayUpdateDateStr().split(",")[0]));
      query.setWayUpdateDateEnd(convertDate(query.getWayUpdateDateStr().split(",")[1]));
    }
    // 停催日期的范围
    if (StrUtil.isNotBlank(query.getStopDateStr()) && query.getStopDateStr().split(",").length == 2) {
      query.setStopDateStart(convertDate(query.getStopDateStr().split(",")[0]));
      query.setStopDateEnd(convertDate(query.getStopDateStr().split(",")[1]));
    }
    // 实际回收日期的范围
    if (StrUtil.isNotBlank(query.getRecycleDateStr()) && query.getRecycleDateStr().split(",").length == 2) {
      query.setRecycleDateStart(convertDate(query.getRecycleDateStr().split(",")[0]));
      query.setRecycleDateEnd(convertDate(query.getRecycleDateStr().split(",")[1]));
    }

    return query;
  }

  private String convertDate(String date) {
    if (!CommonUtil.isEmpty(date) && CommonUtil.isInteger(date)) {
      return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.valueOf(date)));
    }
    // 是否是标准时间格式，否则返回空
    if (!CommonUtil.isValidDate(date)) {
      return null;
    }
    return date;
  }

  public List<String> getExistOutSerialNoList(List<String> outSerialNoList) {
    if (CollectionUtils.isEmpty(outSerialNoList)) {
      return new ArrayList<>();
    }
    Example example = new Example(Case.class);
    example.selectProperties("outSerialNo");
    example.createCriteria().andIn("outSerialNo", outSerialNoList);
    List<Case> list = selectByExample(example);
    List existList = list.stream().map(Case::getOutSerialNo).collect(Collectors.toList());
    return existList;
  }

  /**
   * 查找列表（仅搜索出部分字段，用于校验）
   *
   * @param outSerialNoList
   * @return
   */
  public Map<String, Long> getOutSerialNoAndIdMap(List<String> outSerialNoList) {
    if (CollectionUtils.isEmpty(outSerialNoList)) {
      return new HashMap<>();
    }
    Example example = new Example(Case.class);
    example.selectProperties("outSerialNo");
    example.selectProperties("id");
    example.createCriteria().andIn("outSerialNo", outSerialNoList);
    List<Case> list = selectByExample(example);
    Map<String, Long> map =
        list.stream().collect(Collectors.toMap(Case::getOutSerialNo, Case::getId));
    return map;
  }

  /**
   * 查找列表
   *
   * @param outSerialNoList
   * @return
   */
  public List<Case> selectCaseByOutSerialNos(List<String> outSerialNoList) {
    if (CollectionUtils.isEmpty(outSerialNoList)) {
      return new ArrayList<>();
    }
    Example example = new Example(Case.class);
    example.createCriteria().andIn("outSerialNo", outSerialNoList);
    return selectByExample(example);
  }

  public Map<Long, Integer> selectConjointMapByDebtor(Long orgId, List<Long> debtorIdList) {
    if (CollectionUtils.isEmpty(debtorIdList)) {
      return new HashMap<>();
    }
    Map params = new HashMap();
    params.put("orgId", orgId);
    params.put("list", debtorIdList);
    List<Map<String, Object>> mapList = caseMapper.selectConjointMapByDebtor(params);
    Map<Long, Integer> result = new HashMap<>();
    for (Map<String, Object> map : mapList) {
      result.put(
          Long.valueOf(map.get("debtId").toString()),
          Integer.valueOf(map.get("conjointCount").toString()));
    }
    return result;
  }

  private Map<String, Object> covertState(Integer state) {
    Map<String, Object> map = new HashMap<>(2);
    if (null != state) {
        switch (state) {
          // 未分案
          case 0:
            map.put("states", "0");
            break;
          // 分案完成
          case 1:
            map.put("states", "1");
            map.put("statuses", "3");
            break;
          // 留案
          case 2:
            map.put("states", "1");
            map.put("statuses", "4");
            break;
          // 暂停
          case 3:
            map.put("states", "2");
            map.put("notInStatuses", "-1");
            break;
          // 结案
          case 4:
            map.put("states", "3");
            map.put("notInStatuses", "-1");
            break;
          // 作废
          case 5:
            map.put("statuses", "-1");
            break;
          // 分案至组
          case 6:
            map.put("states", "5");
            map.put("statuses", "3");
            break;
          // 分配至分公司
          case 7:
            map.put("states", "6");
            map.put("statuses", "3");
            break;
          // 退案
          case 8:
            map.put("states", "4");
            map.put("notInStatuses", "-1");
            break;
          default:
            break;
        }
      }
      return map;
  }

  public CaseListInfoVO conjointList(Long caseId, String tag, String caseStatues,
                                     Long productId, PageParam pageParam)
      throws Exception {
    tag = tag == null ? null : tag.toUpperCase();
    if (!"ON".equals(tag) && !"PAST".equals(tag)) {
      throw new ApiException("请输入正确tag");
    }
    Case caseInfo = super.selectByPrimaryKey(caseId);
    if (caseInfo == null) {
      throw new ApiException("案件不存在");
    }
    if (caseInfo.getDebtId() == null) {
      return new CaseListInfoVO();
    }
    //状态调整
//    Map<String, Object> map = covertState(state);
    Map<String, Object> map = new HashMap<>();
    map.put("tag", tag);
    map.put("caseId", caseId);
    //map.put("state", state);
    map.put("productId", productId);
    map.put("debtId", caseInfo.getDebtId());
    map.put("caseStatues",caseStatues);
    Page page = super.setPage(pageParam);
    List<CaseQueryResult> list = caseMapper.selectConjointList(map);
    List<Long> caseIdList = list.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
    Map<Long, Long> reductionTotalMap = reductionService.getReductionTotalMap(caseIdList);
    List<CaseVO> vos = convertVO(list);
    // 已减免金额
    for (CaseVO vo : vos) {
      Long reductionTotal = reductionTotalMap.get(vo.getId());
      vo.setReductionTotal(reductionTotal == null ? 0L : reductionTotal);
    }
    CaseListInfoVO pageOutput =
        new CaseListInfoVO(
            page.getPageNum(),
            page.getPageSize(),
            page != null ? (int) page.getTotal() : vos.size(),
            vos);
    //计算共债案件贷款金额
    Long totalConjointAmount = caseMapper.getConjointAmountSum(map);
    Long currentAmount = caseInfo.getAmount() == null ? 0 : caseInfo.getAmount();
    pageOutput.setCurrentAmount(currentAmount);
    pageOutput.setTotalConjointAmount(totalConjointAmount == null ? currentAmount : totalConjointAmount + currentAmount);
    return pageOutput;
  }

  /**
   * 获取共债案件对应的用户，<debtId,userId>
   *
   * @param orgId
   * @param debtIdList
   * @return
   */
  public Map<Long, Long> selectConjointUserMap(
      Long orgId, List<Long> debtIdList, List<Long> selectUserIdList) {
    if (CollectionUtils.isEmpty(debtIdList)) {
      return new HashMap<>();
    }
    Map params = new HashMap();
    params.put("orgId", orgId);

    if (!CollectionUtils.isEmpty(selectUserIdList)) {
      params.put("selectUserIdList", selectUserIdList);
    }
    List<Map<String, Object>> mapList = new ArrayList<>();
    List<List<Long>> debtIdGroupList = CmUtil.splitList(debtIdList, SystemConst.GROUP_SIZE);
    for (List<Long> subDebtIdList : debtIdGroupList) {
      params.put("list", subDebtIdList);
      List<Map<String, Object>> subList = caseMapper.selectConjointUserMap(params);
      mapList.addAll(subList);
    }
    Map<Long, Long> result = new HashMap<>();
    for (Map<String, Object> map : mapList) {
      result.put(
          Long.valueOf(map.get("debtId").toString()), Long.valueOf(map.get("userId").toString()));
    }
    return result;
  }

  public List<CaseVO> convertVO(List<CaseQueryResult> list) throws Exception {
    List<CaseVO> caseVOList = new ArrayList<>();
    if (CollectionUtils.isEmpty(list)) {
      return caseVOList;
    }

    Map<Long, String> deltMap = deltService.getDeltMap(list.stream()
            .filter(p->ObjectUtil.isNotNull(p.getOrgDeltId()))
            .map(CaseQueryResult::getOrgDeltId).distinct().collect(Collectors.toList()));
    Map<Long, String> innerBatchMap = innerBatchService.getInnerBatchMap(list.stream()
            .filter(p->ObjectUtil.isNotNull(p.getInnerBatchId()))
            .map(CaseQueryResult::getInnerBatchId).distinct().collect(Collectors.toList()));
    Map<Long, String> outBatchMap = outBatchService.getOutBatchMap(list.stream()
            .filter(p->ObjectUtil.isNotNull(p.getOutBatchId()))
            .map(CaseQueryResult::getOutBatchId).distinct().collect(Collectors.toList()));
    Map<Long, String> productMap = productService.getProductMap(list.stream()
            .filter(p->ObjectUtil.isNotNull(p.getProductId()))
            .map(CaseQueryResult::getProductId).distinct().collect(Collectors.toList()));

    List<Long> depIds = list.stream().filter(p->ObjectUtil.isNotNull(p.getDepId())).map(CaseQueryResult::getDepId).distinct().collect(Collectors.toList());
    List<Long> preDepIds = list.stream().filter(p->ObjectUtil.isNotNull(p.getPreDepId())).map(CaseQueryResult::getPreDepId).distinct().collect(Collectors.toList());
    List<Long> teamIds = list.stream().filter(p->ObjectUtil.isNotNull(p.getTeamId())).map(CaseQueryResult::getTeamId).distinct().collect(Collectors.toList());
    depIds = CollUtil.addAllIfNotContains(depIds,preDepIds);
    depIds = CollUtil.addAllIfNotContains(depIds,teamIds);
    Map<Long, String> depTeamMap = depTeamService.getDepTeamMap(depIds);

    List<Long> userIds = list.stream().filter(p->ObjectUtil.isNotNull(p.getUserId())).map(CaseQueryResult::getUserId).distinct().collect(Collectors.toList());
    List<Long> createByIds = list.stream().filter(p->ObjectUtil.isNotNull(p.getCreateBy())).map(CaseQueryResult::getCreateBy).distinct().collect(Collectors.toList());
    List<Long> updatByIds = list.stream().filter(p->ObjectUtil.isNotNull(p.getUpdateBy())).map(CaseQueryResult::getUpdateBy).distinct().collect(Collectors.toList());
    userIds = CollUtil.addAllIfNotContains(userIds,createByIds);
    userIds = CollUtil.addAllIfNotContains(userIds,updatByIds);
    Map<Long, String> userMap = userService.getUserMap(userIds);

    UserSession userSession = getTokenUser();
    Map<Integer, String> operationStatusNameMap = customOperationStatusService.getNames(userSession.getOrgId());
    Map<Integer, String> operationStateNamesMap =orgConfigService.getAllNames(userSession.getOrgId(),OrgConfigEnums.Type.OPERATION_STATE.getCode());
    Map<Integer, String> callTypeNamesMap =orgConfigService.getAllNames(userSession.getOrgId(),OrgConfigEnums.Type.CALL_TYPE.getCode());

    // 案件标签
    List<Long> caseIds=list.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
    List<CaseTagRelDTO> caseTagDtoList = caseTagRelMapper.selectByCaseIds(caseIds);
    Map<Long, List<CaseTagRelDTO>> caseTagRelMap = caseTagDtoList.stream().collect(Collectors.groupingBy(CaseTagRelDTO::getCaseId));

    // 催收手段
    List<CaseOperationWayRel> caseOperationWayRels = caseOperationWayRelService.selectByCaseIds(caseIds);
    for (CaseQueryResult ca : list) {
      CaseVO vo = new CaseVO();
      BeanUtils.copyProperties(ca, vo);
      // 自定义字段key转为驼峰形式
      if (!CollectionUtils.isEmpty(ca.getFieldJson())){
        Map<String, String> fieldJsonMap = new HashMap<>();
        ca.getFieldJson().forEach((key, value) -> {
          fieldJsonMap.put(toCamelCase(key), value);
        });
        vo.setFieldJson(fieldJsonMap);
      }

      // 自定义字段数据
      if (!CollectionUtils.isEmpty(ca.getFieldSearch())) {
        Map<String, String> fieldJsonMap = new HashMap<>();
        for (String item : ca.getFieldSearch()) {
          int index = item.indexOf("#");
          if (index != -1) {
            String key = toCamelCase(item.substring(0, index));
            String value = item.substring(index + 1);
            fieldJsonMap.put(key, value);
          }
        }
        vo.setFieldJson(fieldJsonMap);
      }
      vo.setOutSerialNo(ca.getOutSerialNo().substring(0, ca.getOutSerialNo().lastIndexOf("#")));
      vo.setOrgDeltName(deltMap.get(ca.getOrgDeltId()));
      vo.setBatchNo(innerBatchMap.get(ca.getInnerBatchId()));
      vo.setOutBatchNo(outBatchMap.get(ca.getOutBatchId()));
      vo.setProductName(productMap.get(ca.getProductId()));
      vo.setDepName(depTeamMap.get(ca.getDepId()));
      vo.setTeamId(ca.getTeamId());
      vo.setTeamName(depTeamMap.get(ca.getTeamId()));
      vo.setRepTotal(ca.getPayAmount() == null ? 0 : ca.getPayAmount());
      vo.setCreateBy(userMap.get(ca.getCreateBy()));
      vo.setUpdateBy(userMap.get(ca.getUpdateBy()));
      vo.setUserName(userMap.get(ca.getUserId()));
      vo.setMobile(ca.getOwnMobile());
      vo.setActionType(ca.getOperStatus()); // 设置催收结果
      vo.setOrgDeltId(ca.getOrgDeltId());
      // 度言电话结果
      vo.setOutCome(ca.getCallStatus());
      // 电话结果
      vo.setCallType(ca.getCallType());
      // 特殊处理状态 增加state 需要兼容老的状态
      vo.setAutoAssistDate(ca.getAutoAssistDate());
      vo.setAutoAssistResult(ca.getAutoAssistResult());
      vo.setTag(ca.getTag());
      vo.setAutoAssistRecord(ca.getAutoAssistRecord());
      // 判断是否在分配中
      Boolean isAllot = redisUtil.sHasKey(KeyCache.CASE_PROTECT_EXIST_IDS + ca.getOrgId(), String.valueOf(ca.getId()));
      vo.setHasAllot(isAllot);
      vo.setActionTypeName(operationStatusNameMap.get(vo.getActionType()));
      vo.setOperationStateName(operationStateNamesMap.get(vo.getOperationState()));
      vo.setCallTypeName(callTypeNamesMap.get(vo.getCallType()));
      vo.setConjointType(ca.getConjointType() != null ? ca.getConjointType() : 0);

      vo.setCaseTags(convert(caseTagRelMap.get(ca.getId())));
      vo.setPtpAmount(ObjectUtil.isNull(ca.getPtpAmount())?null: NumberUtil.div((Number)ca.getPtpAmount(),1000,2));
      vo.setPreDepName(ObjectUtil.isNull(ca.getPreDepId())?null:depTeamMap.get(ca.getPreDepId()));
      vo.setEndTime(ObjectUtil.isNull(ca.getEndTime())?null:ca.getEndTime().getTime());

      List<Integer> operationWays = caseOperationWayRels.stream()
              .filter(p -> ObjectUtil.equals(ca.getId(), p.getCaseId()))
              .map(CaseOperationWayRel::getOperationWay)
              .collect(Collectors.toList());
      vo.setOperationWays(operationWays);

      caseVOList.add(vo);
    }
    return caseVOList;
  }

  private String toCamelCase(String key) {
    String[] words = key.split("_");
    StringBuilder camelCase = new StringBuilder(words[0]);

    for (int i = 1; i < words.length; i++) {
      String word = words[i];
      String capitalizedWord = StrUtil.EMPTY;
      if (PatternUtils.isDigit(word.substring(0, 1))) {
        capitalizedWord = "_" + word;
      } else {
        capitalizedWord = word.substring(0, 1).toUpperCase() + word.substring(1);
      }
      camelCase.append(capitalizedWord);
    }

    String camelCaseString = camelCase.toString();
    return camelCaseString;
  }

  private List<CaseTagVO> convert(List<CaseTagRelDTO> caseTagRelDTOList){
    if(caseTagRelDTOList==null){
      return Lists.newArrayList();
    }
    List<CaseTagVO> caseTagVOS=new ArrayList<>(caseTagRelDTOList.size());
    for(CaseTagRelDTO dto:caseTagRelDTOList){
      CaseTagVO vo=new CaseTagVO();
      vo.setColor(dto.getColor().intValue());
      vo.setName(dto.getName());
      vo.setId(dto.getTagId());
      vo.setState(dto.getState().intValue());
      caseTagVOS.add(vo);
    }
    return caseTagVOS;
  }

  public List<CaseQueryResult> selectChangeListByIds(
      List<String> caseIdList, Integer changeStatus, Date delayTime, Date restartTime) {
    CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
    List<Long> idList = caseIdList.stream().map(i -> Long.valueOf(i)).collect(Collectors.toList());
    caseMultiQuery.setCaseIds(idList);
    // 状态委案时间校验
    coverChangeStatus(caseMultiQuery, changeStatus, delayTime, restartTime);
    List<CaseQueryResult> caseList = caseMapper.queryResultForMulti(caseMultiQuery);
    return caseList;
  }

  /**
   * 上传案件文件
   *
   * @param caseFileParam
   * @param multipartFile
   */
  @Transactional
  public void uploadFiles(CaseFileParam caseFileParam, MultipartFile multipartFile) {
    UserSession loginUser = UserUtils.getTokenUser();
    Date currentDate = new Date();
    Date expireDate = DateUtils.addDays(currentDate, 10 * 365);
    String fileName = multipartFile.getOriginalFilename();
    fileName =
        fileName.split("\\.")[0]
            + "_"
            + StringUtils.getRandomNumberBIT6()
            + "."
            + fileName.split("\\.")[1];

    UploadFileInfo uploadFileInfo = new UploadFileInfo();
    uploadFileInfo.setFile(multipartFile)
            .setFileName(fileName)
            .setExpireDate(expireDate)
            .setBucket(systemConfig.getCaseFilesBucket())
            .setLocalUrl(systemConfig.getCaseFilePath() + File.separator + fileName);
    FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
    String caseFilesUrl = fileStorageStrategy.uploadFile(uploadFileInfo);

    // 插入
    CaseFile caseFile = new CaseFile();
    caseFile.setCaseId(caseFileParam.getCaseId());
    caseFile.setUploadBy(loginUser.getId());
    caseFile.setUploadTime(currentDate);
    caseFile.setFileName(multipartFile.getOriginalFilename());
    caseFile.setFileUrl(caseFilesUrl);
    caseFileMapper.insertSelective(caseFile);
  }

  /**
   * 案件文件列表
   *
   * @param caseId
   * @param pageParam
   * @return
   */
  public CaseFileListVO listCaseFiles(Long caseId, PageParam pageParam) {
    Example caFileExp = new Example(CaseFile.class);
    caFileExp.createCriteria().andEqualTo("caseId", caseId);
    caFileExp.setOrderByClause("upload_time desc");
    Page page = super.setPage(pageParam);
    List<CaseFile> caseFiles = caseFileMapper.selectByExample(caFileExp);
    List<CaseFileVO> caseFileVOS = Lists.newArrayList();
    for (CaseFile caseFile : caseFiles) {
      CaseFileVO vo = convertFileVO(caseFile);
      caseFileVOS.add(vo);
    }
    CaseFileListVO pageOutput =
        new CaseFileListVO(
            page.getPageNum(),
            page.getPageSize(),
            page != null ? (int) page.getTotal() : caseFileVOS.size(),
            caseFileVOS);
    return pageOutput;
  }

  private CaseFileVO convertFileVO(CaseFile caseFile) {
    CaseFileVO vo = new CaseFileVO();
    vo.setCaseId(caseFile.getCaseId());
    vo.setFileName(caseFile.getFileName());
    vo.setFileUrl(caseFile.getFileUrl());
    vo.setUploadTime(caseFile.getUploadTime());
    User uploadBy = userService.selectByPrimaryKey(caseFile.getUploadBy());
    if (uploadBy != null) {
      vo.setUploadBy(uploadBy.getName());
    }
    vo.setId(caseFile.getId());
    return vo;
  }

  /**
   * 删除案件文件，物理删除
   *
   * @param caseId
   * @param caseFileId
   */
  @Transactional
  public void delCaseFile(Long caseId, Long caseFileId) {
    CaseFile delQuery = new CaseFile();
    delQuery.setCaseId(caseId);
    delQuery.setId(caseFileId);
    caseFileMapper.delete(delQuery);
  }

  public String getInvalidReason(Long caseId) {
    List<Integer> logTypeList =
        Lists.newArrayList(
            CaseLogEnums.Type.STOP.getCode(),
            CaseLogEnums.Type.UPDATE.getCode(),
            CaseLogEnums.Type.END.getCode(),
            CaseLogEnums.Type.TOVOID.getCode(),
            CaseLogEnums.Type.RETURN.getCode());
    List<CaseLog> caseLogs = caseLogService.selectByCaseId(caseId, logTypeList);
    if (CommonUtils.isEmpty(caseLogs)) {
      return "";
    }
    caseLogs =
        caseLogs.stream()
            .sorted(Comparator.comparing(CaseLog::getCreateTime).reversed())
            .collect(Collectors.toList());
    String desc = "";
    for (CaseLog caseLog : caseLogs) {
      Map<String, String> map = caseLog.getFieldJson();
      if (map == null) {
        continue;
      }
      Object list = map.get("desc");
      if (list != null) {
        List<String> change = (List<String>) list;
        desc = CollectionUtils.isEmpty(change) ? "" : change.get(change.size() - 1);
        break;
      }
    }
    return desc;
  }

  @Transactional(rollbackFor = Exception.class)
  public ResultMessage allotResetStatus(CaseAllotResetParam param) throws Exception {
   Integer revokeTo=CaseEnums.RevokeTarget.ORG.getCode();
    if (UserUtils.likeTeamLeader()) {
      revokeTo = CaseEnums.RevokeTarget.TEAM.getCode();
    } else if (UserUtils.likeBranchAdmin()) {
      revokeTo = CaseEnums.RevokeTarget.DEP.getCode();
    }
    if (!param.getAllSelect()) {
      // 同步执行
      doSyncAllotReset(param, revokeTo);
    } else {
      // 异步
      asyncAllotReset(param, revokeTo);
    }
    return ResultMessage.success();
  }

  private void doSyncAllotReset(CaseAllotResetParam param, Integer revokeTo)
      throws Exception {
    UserSession userSession = getTokenUser();
    List<CaseQueryResult> caseList = selectAllotResetList(param, userSession);
    caseList = protectCaseStatus(userSession.getOrgId(), caseList);
    try {
      if (CommonUtils.isEmpty(caseList) || caseList.size() != param.getCaseIds().size()) {
        throw new ApiException("请确定所选案件的状态");
      }
      // 创建任务
      Long taskId =
          asyncTaskService.createAllotResetTask(
              getTokenUser(),
              Long.valueOf(caseList.size()),
              AsyncTaskEnums.Type.ALLOT_RESET.getCode(),
              revokeTo,
              AsyncTaskEnums.Status.SUCCESS.getCode());
      allotResetUpdate(caseList, getTokenUser(), revokeTo, taskId);
    } finally {
      removeProtectCases(userSession.getOrgId(), caseList);
    }
  }

  private void asyncAllotReset(CaseAllotResetParam param,Integer revokeTo)
      throws Exception {
    UserSession userSession = userService.getTokenUser();

    //创建异步任务任务之前判断3分钟之前是否有相同操作
    CaseAllotResetParam caseAllotResetParam = AuthBeanUtils.copy(param, CaseAllotResetParam.class);
    caseAllotResetParam.setOrgIds(userSession.getOrgId().toString());
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(caseAllotResetParam).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }
    // 只获取id
    param.setSelectField("ca.id");
    List<CaseQueryResult> caseList = selectAllotResetList(param, userSession);
    // 案件状态保护
    caseList = protectCaseStatus(userSession.getOrgId(), caseList);


    try {
      List<String> caseIdList =
          caseList.stream().map(c -> c.getId().toString()).collect(Collectors.toList());
      // 创建异步任务
      Long taskId =
          asyncTaskService.createAllotResetTask(
              userSession,
              Long.valueOf(caseIdList.size()),
              AsyncTaskEnums.Type.ALLOT_RESET.getCode(),
              revokeTo,
              AsyncTaskEnums.Status.ING.getCode());
      // 任务添加到任务列表
      stringRedisTemplate
          .opsForSet()
          .add(
              KeyCache.CASE_CHANGE_STATUS_TASK_CASES + taskId,
              caseIdList.toArray(new String[caseIdList.size()]));
      stringRedisTemplate
          .opsForList()
          .leftPush(KeyCache.CASE_CHANGE_STATUS_TASK_ID_LIST, taskId.toString());
    } catch (Exception e) {
      removeProtectCases(userSession.getOrgId(), caseList);
      throw new ApiException(e.getMessage());
    }
  }

  private List<CaseQueryResult> selectAllotResetList(
      CaseAllotResetParam param, UserSession userSession) throws Exception {
    CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
    if (!param.getAllSelect()) {
      //  去除加锁中的数据
      List<Long> idList = param.getCaseIds();
      if (CommonUtils.isEmpty(idList)) {
        throw new ApiException("请选择案件！");
      }
      caseMultiQuery.setCaseIds(idList);
    } else {
      // 选择全部
      BeanUtil.copyProperties(param, caseMultiQuery);
      // 公司隔离
      caseMultiQuery.setOrgIds(UserUtils.getTokenUser().getOrgId().toString());
      if (UserUtils.likeBranchAdmin()) {
        // 如果是分公司管理员，进行分公司隔离
        caseMultiQuery.setDepId(UserUtils.getTokenUser().getDepId());
      } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
        caseMultiQuery.setTeamIds(userSession.getTeamId().toString());
      }
      caseMultiQuery.setCaseIds(null);
    }
    List<Integer> allotStatues;
    // 只有分案完成、留案、分案至组可以重新分配
    // 如果当前action是7，代表只有分案至组状态的案件
    if (param.getAction() == 7) {
      allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_TEAM.getCode());
    } else if (param.getAction() == 8) {
      allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_BRANCH.getCode());
    } else {
      allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode());
    }
    caseMultiQuery.setAllotStatues(StringUtils.join(allotStatues, ","));
    List<CaseQueryResult> caseList;
    if (param.getAllSelect() && systemConfig.getESSwitch()) {
      caseMultiQuery.setFields(Lists.newArrayList("id"));
      caseList = getAllCasesUsingEs(caseMultiQuery);
    } else {
      caseList = queryResultForMulti(caseMultiQuery);
    }
    if (CommonUtils.isEmpty(caseList)) {
      throw new ApiException("没有可操作案件，请重新确定案件状态！");
    }
    return caseList;
  }

  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public void allotResetUpdate(
      List<CaseQueryResult> caseList,
      UserSession userSession,
      Integer revokeTo,
      Long taskId) {
    List<Long> caseIds = caseList.stream().map(Case::getId).collect(Collectors.toList());
    Map<String,Object> updateParams = assembleCaseResetInfo(revokeTo, caseIds);
    // 更新状态
    caseMapper.updateAllotReset(updateParams);
    // 待审批协催申请自动拒绝，待协催自动完成
    caseCooperationApplyService.batchRefuseApply(caseIds, userSession, "案件重新分配，需重新申请");
    caseCooperationService.batchFinishCooperation(caseIds, userSession, "案件重新分配，协催自动结束");
    // log记录
    allotResetEvent(caseList, updateParams, userSession, taskId);
  }

  /**
   * 组装案件重分配信息
   *
   * @param revokeTo 回收级别
   * @param caseIds 案件ids
   * @return
   */
  private Map<String,Object> assembleCaseResetInfo(Integer revokeTo, List<Long> caseIds) {
    Map<String,Object> updateParams = new HashMap<>();
    updateParams.put("list", caseIds);
    // 重新分配标为颜色无状态
    updateParams.put("color", CaseEnums.Color.NOCOLOR.getCode());
    updateParams.put("ignorePlan", CaseEnums.IgnorePlan.NO.getCode());
    updateParams.put("revokeTo",revokeTo);
    if(CaseEnums.RevokeTarget.ORG.getCode().equals(revokeTo)){
      //回收至总公司
      //总公司管理员重新分配案件，设置案件状态为未分配
      updateParams.put("allotStatus", CaseEnums.AllotStatus.NOT_ALLOT.getCode());
    }else if(CaseEnums.RevokeTarget.DEP.getCode().equals(revokeTo)){
      //回收至分公司
      //分公司管理员重新分配案件
      updateParams.put("allotStatus", CaseEnums.AllotStatus.ALLOT_BRANCH.getCode());
    }else if(CaseEnums.RevokeTarget.TEAM.getCode().equals(revokeTo)){
      //回收至小组
      updateParams.put("allotStatus", CaseEnums.AllotStatus.ALLOT_TEAM.getCode());
    }else {
      throw new ApiException("重新分配错误，需指定重新分配的归属");
    }
    updateParams.put("cooperationStatus", CaseEnums.CooperationStatus.NO.getCode());
    return updateParams;
  }

  private void allotResetEvent(
      List<CaseQueryResult> caseList, Map updateParams, UserSession userSession, Long taskId) {
    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setCaseInfoResultList(
        JsonUtils.toList(JsonUtils.toJson(caseList), CaseInfoResult.class));
    Map<Long, Map<String, String>> diffMap = new HashMap<>();
    Object updateAllotStatus = updateParams.get("allotStatus");
    Object revokeTo = updateParams.get("revokeTo");
    caseList.forEach(
        aCase -> {
          Diff diff = new Diff();
          diff.addDiff("allotStatus", aCase.getAllotStatus(), updateAllotStatus);
          diff.addDiff("user_id", aCase.getUserId(), null);
          if(CaseEnums.RevokeTarget.DEP.getCode().equals(revokeTo)){
            diff.addDiff("team_id", aCase.getTeamId(), null);
          }
          if(CaseEnums.RevokeTarget.ORG.getCode().equals(revokeTo)){
            diff.addDiff("team_id", aCase.getTeamId(), null);
            diff.addDiff("dep_id", aCase.getDepId(), null);
          }
          diffMap.put(aCase.getId(), diff.toStringMap());
        });
    // 组装事件信息
    caseBatchUpdateEvent.setDiffMap(diffMap);
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.ALLOT_RESET.getCode());
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);
  }

  public List<Long> selectExistConjointList(Long orgId, List<Long> debtorIdList) {
    List<Long> existList = new ArrayList<>();
    if (debtorIdList.isEmpty()) {
      return existList;
    }
    Map<String, Object> map = new HashMap<>();
    map.put("orgId", orgId);
    List<List<Long>> groupList = CmUtil.splitList(debtorIdList, 1000);
    for (List<Long> list : groupList) {
      map.put("list", list);
      List<Long> result = caseMapper.selectExistConjointList(map);
      existList.addAll(result);
    }
    return existList;
  }

  public StatisAgentCasesVO statisAgentCases(Long userId) {
    StatisAgentCasesVO vo = new StatisAgentCasesVO();
    // 案件总数、案件总金额、已还款金额一条sql
    Map map = caseMapper.queryCaseTotalExtra(userId); // 已还款金额，还款金额大于0的案件
    Long caseTotal = (Long) map.get("caseTotal");
    Double totalAmount = (Double) map.get("totalAmount");
    Double totalRepayAmount = (Double) map.get("totalRepayAmount");
    // 已还款案件
    Long repaiedCaseTotal = caseMapper.queryRepaiedCaseTotal(userId); // 已还款案件 审核通过和导入成功
    //
    Long caseOpedTotal = caseMapper.queryCaseOpedTotal(userId); // 在催案件 有催记的案件
    Long undoCaseTotal = caseMapper.queryUndoCaseTotal(userId); // 未处理案件 催收结果为未处理
    Long casePtpTotal = caseMapper.queryCasePtpTotal(userId); // 承诺还款金额
    vo.setCaseTotal(caseTotal == null ? 0L : caseTotal);
    vo.setTotalAmount(totalAmount == null ? 0D : totalAmount);
    vo.setTotalRepayAmount(totalRepayAmount == null ? 0D : totalRepayAmount);
    vo.setRepaiedCaseTotal(repaiedCaseTotal == null ? 0L : repaiedCaseTotal);
    vo.setCaseOpedTotal(caseOpedTotal == null ? 0L : caseOpedTotal);
    vo.setUndoCaseTotal(undoCaseTotal == null ? 0L : undoCaseTotal);
    vo.setCasePtpTotal(casePtpTotal == null ? 0L : casePtpTotal);
    return vo;
  }

  public Case selectByOutSeriaNo(String outSerialNo) {
    Case domain = new Case();
    domain.setOutSerialNo(outSerialNo);
    List<Case> caseList = select(domain);
    if (CollectionUtils.isEmpty(caseList)) {
      return null;
    }
    return caseList.get(0);
  }

  @Transactional
  public void caseColoring(CaseColorParam param) {
    param.setUpdateBy(getTokenUser().getId());
    caseMapper.updateCaseColor(param);
  }

  public CaseDTO getCaseForLetter(Long caseId) {
    if (caseId == null) {
      throw new ApiException("案件编号不能为空");
    }
    CaseMultiQuery query = new CaseMultiQuery();
    query.setCaseIds(Lists.newArrayList(caseId));
    List<CaseQueryResult> list = caseMapper.queryResultForMulti(query);
    if (CollectionUtils.isEmpty(list)) {
      throw new ApiException("案件不存在");
    }
    CaseQueryResult caseQueryResult = list.get(0);
    CaseDTO caseDTO = AuthBeanUtils.copy(caseQueryResult, CaseDTO.class);
    caseDTO.setOrgDeltName(deltService.getNames().get(caseQueryResult.getOrgDeltId()));
    caseDTO.setProductName(productService.getNames().get(caseQueryResult.getProductId()));
    return caseDTO;
  }

  public List<CaseDTO> getCasesForLetter(CaseMultiQuery query) {
    List<CaseQueryResult> list = caseMapper.queryResultForMulti(query);
    if (CollectionUtils.isEmpty(list)) {
      throw new ApiException("案件不存在");
    }
    List<CaseDTO> caseDTOS = cn.hutool.core.bean.BeanUtil.copyToList(list, CaseDTO.class);
    caseDTOS.forEach(dto->{
      dto.setOrgDeltName(deltService.getNames().get(dto.getOrgDeltId()));
      dto.setProductName(productService.getNames().get(dto.getProductId()));
    });
    return caseDTOS;
  }

  public CaseInfoVO getCaseInfo(Long caseId) throws Exception {
    if (caseId == null) {
      throw new ApiException("案件编号不能为空");
    }
    UserSession userSession = getTokenUser();
    CaseMultiQuery query = new CaseMultiQuery();
    query.setCaseIds(Lists.newArrayList(caseId));
    query.setOrgIds(userSession.getOrgId().toString());
    List<CaseQueryResult> list = caseMapper.queryResultForMulti(query);
    if (CollectionUtils.isEmpty(list)) {
      throw new ApiException("案件已被删除");
    }

    CaseQueryResult caseQueryResult = list.get(0);
    // 包装数据
    CaseInfoVO caseInfoVO = coverToCaseInfoVO(caseQueryResult);
    Map<Integer, String> operationStateName = orgConfigService.getAllNames(userSession.getOrgId(), OrgConfigEnums.Type.OPERATION_STATE.getCode());
    caseInfoVO.setOperationStateName(operationStateName.get(caseInfoVO.getOperationState()));

    // 案件共债个数
    Map<Long, Integer> conjointCountMap =
        selectConjointMapByDebtor(
            userSession.getOrgId(), Lists.newArrayList(caseQueryResult.getDebtId()));
    Integer conjointCount = conjointCountMap.get(caseQueryResult.getDebtId());
    caseInfoVO.setConjointCount(conjointCount == null ? 0 : conjointCount - 1);

    List<Long> caseIdList = list.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
    Map<Long, Long> reductionTotalMap = reductionService.getReductionTotalMap(caseIdList);
    // 已减免金额
    Long reductionTotal = reductionTotalMap.get(caseInfoVO.getId());
    caseInfoVO.setReductionTotal(reductionTotal == null ? 0L : reductionTotal);

    // 案件承诺还款金额
    Long ptpAmountTotal = caseMapper.selectCasePtpTotal(caseId);
    caseInfoVO.setPtpAmount(ptpAmountTotal == null ? 0L : ptpAmountTotal);

    // 剩余金额
    Long remainAmount = caseInfoVO.getAmount() - caseInfoVO.getReductionTotal() - caseInfoVO.getPayAmount();
    caseInfoVO.setRemainAmount(remainAmount);

    // 案件导入模版字段
    Map<Long, Map<String, String>> tplMap = new HashMap<>();
    tplMap = fileStoreService.selectTplSeqRecordMap(Sets.newHashSet(caseInfoVO.getStoreId()));
    caseInfoVO.setTplFields(tplMap.get(caseInfoVO.getStoreId()));

    // 案件基础内置字段金额由后端除以1000。。。。哎
    Map<String, String> fields = caseInfoVO.getFields();
    if (Objects.nonNull(fields)) {
      fields.forEach((k,v) ->{
        if (CASE_BASE_MONEY_FIELD.contains(k)) {
          String value = new BigDecimal(v).divide(new BigDecimal("1000")).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
          fields.put(k, value);
        }
      });
    }
    return caseInfoVO;
  }

  public CaseInfoVO coverToCaseInfoVO(CaseQueryResult caseQueryResult) throws Exception {
    CaseInfoVO caseInfoVO = new CaseInfoVO();
    BeanUtils.copyProperties(caseQueryResult, caseInfoVO);
    caseInfoVO.setFields(caseQueryResult.getFieldJson());
    caseInfoVO.setOutSerialNo(
        caseQueryResult
            .getOutSerialNo()
            .substring(0, caseQueryResult.getOutSerialNo().lastIndexOf("#")));
    caseInfoVO.setOrgDeltName(deltService.getNames().get(caseQueryResult.getOrgDeltId()));
    caseInfoVO.setBatchNo(innerBatchService.getNames().get(caseQueryResult.getInnerBatchId()));
    caseInfoVO.setOutBatchNo(outBatchService.getNames().get(caseQueryResult.getOutBatchId()));
    caseInfoVO.setProductName(productService.getNames().get(caseQueryResult.getProductId()));
    caseInfoVO.setDepName(depTeamService.getNames().get(caseQueryResult.getDepId()));
    caseInfoVO.setTeamName(depTeamService.getNames().get(caseQueryResult.getTeamId()));
    caseInfoVO.setRepTotal(caseQueryResult.getPayAmount());
    if (caseQueryResult.getUserId() != null
        && caseInfoVO.getUserStatus() != null
        && User.Status.NORMAL.getCode() != caseInfoVO.getUserStatus()) {
      caseInfoVO.setUserName(null);
    }
    caseInfoVO.setCreateBy(userService.getNames(userService.getOrgId()).get(caseQueryResult.getCreateBy()));
    caseInfoVO.setUpdateBy(userService.getNames(userService.getOrgId()).get(caseQueryResult.getUpdateBy()));
    caseInfoVO.setMobile(caseQueryResult.getOwnMobile());
    caseInfoVO.setActionType(caseQueryResult.getOperStatus()); // 设置催收结果
    caseInfoVO.setCallType(caseQueryResult.getCallType());

    List<CaseTagRelDTO> caseTagDtoList = this.caseTagRelMapper.selectByCaseIds(Collections.singletonList(caseQueryResult.getId()));
    caseInfoVO.setCaseTags(convert(caseTagDtoList));
    CaseCooperation cooperation =
        caseCooperationService.getCooperationByCaseId(caseQueryResult.getId());

    if (null != cooperation) {
      caseInfoVO.setCooperationId(cooperation.getId());
      caseInfoVO.setCooperatorId(cooperation.getCooperatorId());
      caseInfoVO.setCooperatorName(cooperation.getCooperatorName());
      caseInfoVO.setCooperatorState(cooperation.getState().intValue());
    }
    // 判断是否在分配中
    Boolean isAllot = stringRedisTemplate.opsForSet().isMember(KeyCache.CASE_PROTECT_EXIST_IDS + caseQueryResult.getOrgId(), String.valueOf(caseQueryResult.getId()));
    caseInfoVO.setHasAllot(isAllot!=null && isAllot);
    caseInfoVO.setRepTotal(caseInfoVO.getRepTotal() == null ? 0 : caseInfoVO.getRepTotal());
    // 自定义字段和中文名拼接
    concatFields(caseInfoVO);
    return caseInfoVO;
  }

  /**
   * 自定义字段名个字段名值拼接
   *
   * @param caseInfoVO
   */
  private void concatFields(CaseInfoVO caseInfoVO) {
    Long orgId = caseInfoVO.getOrgId();
    // 自定义字段
    List<CustomField> fieldList = customFieldService.selectByOrgId(orgId);
    Map<String, CustomField> fieldMap =
        fieldList.stream().collect(Collectors.toMap(CustomField::getValue, c -> c));
    // 自定义搜索标签
    List<CustomSearchField> customSearchFieldList = customSearchFieldService.selectByOrgId(orgId);
    Map<String, CustomSearchField> searchMap =
        customSearchFieldList.stream()
            .collect(Collectors.toMap(CustomSearchField::getSearchKey, c -> c));
    Map<String, String> fieldsJson = caseInfoVO.getFields();
    Map<String, String> newJson = new HashMap<>();
    Iterator<Map.Entry<String, String>> it = fieldsJson.entrySet().iterator();
    while (it.hasNext()) {
      Map.Entry<String, String> entry = it.next();
      String name = entry.getKey();
      String value = entry.getValue(); // 字段的值
      CustomField customField = fieldMap.get(name);
      if (customField != null) {
        // 是自定义字段,拼接中文
        newJson.put(name + "|" + customField.getName(), value);
      } else if (searchMap.get(name) != null) {
        newJson.put(name + "||" + searchMap.get(name).getName(), value);
      } else {
        newJson.put(name, value);
      }
    }
    caseInfoVO.setFields(newJson);
  }

  public Long getCaserByMobile(String mobile, Long orgId) {
    if (encryptProperties.getEnable()) {
      mobile = encryptService.encrypt(mobile);
    }
    //根据传递的关联id查询委派公司的id
    List<Company> companys = companyService.select(new Company(orgId));
    if (CommonUtil.isEmpty(companys)) {
      return null;
    }
    OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(companys.get(0).getId());
    // 开启专属坐席路线开关，就不需要再查询最近催收坐席，直接返回null即可
    if (Objects.equals(orgSwitch.getCallInSeatExclusiveLineSwitch(), 1)) {
      return null;
    }
    Map<String,Object> map = new HashMap<>();
    map.put("mobile", mobile);
    map.put("orgId", companys.get(0).getId());
    // 找最近的一条催记
    Long caseId = caseOperationMapper.queryNearestByCaseOpe(map);
    if (caseId == null) {
      return null;
    }
    Case caseInfo = caseMapper.selectByPrimaryKey(caseId);
    if (caseInfo == null || caseInfo.getUserId() == null) {
      return null;
    }
    //返回关联度言id
    User user = userService.selectByPrimaryKey(caseInfo.getUserId());
    if (null == user || user.getStatus() != 0) {
      return null;
    }
    return user.getDuyanAccountId();
  }

  public Long getCaserByMobileUsingES(String mobile, Long orgId) {
    //1.根据传递id查询对应公司id
    List<Company> companys = companyService.select(new Company(orgId));
    if (CommonUtil.isEmpty(companys)) {
      return null;
    }
    OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(companys.get(0).getId());
    // 开启专属坐席路线开关，就不需要再查询最近催收坐席，直接返回null即可
    if (Objects.equals(orgSwitch.getCallInSeatExclusiveLineSwitch(), 1)) {
      return null;
    }
    CaseOperationParamDto dto = new CaseOperationParamDto();
    //设置分页
    dto.setLimit(1);
    dto.setPage(1);
    //根据排序字段
    dto.setSortField("createTime");
    dto.setSortAsc(false);
    dto.setOrgId(companys.get(0).getId());
    dto.setCreateType(new Byte("0"));
    List<String> list = new ArrayList<>();
    list.add(mobile);
    dto.setConMobiles(list);
    //es查找最近催记
    TransferCaseOperationResult res = remoteAlfredService.fetchFromNestedCaseOperationRemote(dto);
    if (res == null || res.getCaseId() == null) {
      return null;
    }
    Long caseId = res.getCaseId();
    Case caseInfo = caseMapper.selectByPrimaryKey(caseId);
    if (caseInfo == null || caseInfo.getUserId() == null) {
      return null;
    }
    //返回关联度言id
    User user = userService.selectByPrimaryKey(caseInfo.getUserId());
    if (null == user || user.getStatus() != 0) {
      return null;
    }
    return user.getDuyanAccountId();
  }

  public List<CasePlanCallResult> casePlanDetailList(Map param) {
    return caseMapper.casePlanDetailList(param);
  }

  public QosCaseOperationInfoVO getInfoByCallUuid(String callUuid) throws Exception {
    if (StringUtils.isBlank(callUuid)) {
      throw new ApiException("根据callUuid查询案件信息，callUuid不能为空");
    }

    CaseOperation cope = getCaseOperationByCallUuid(callUuid);
    if (ObjectUtil.isNull(cope)) {
      throw new ApiException("callUuid找不到对应的案件信息！");
    }

    Long caseId = cope.getCaseId();
    UserSession userSession = getTokenUser();
    CaseMultiQuery query = new CaseMultiQuery();
    query.setCaseIds(Lists.newArrayList(caseId));
    query.setOrgIds(userSession.getOrgId().toString());
    List<CaseQueryResult> list = caseMapper.queryResultForMulti(query);
    if (CollectionUtils.isEmpty(list)) {
      throw new ApiException("案件不存在");
    }
    // 包装数据
    CaseInfoVO caseInfoVO = coverToCaseInfoVO(list.get(0));
    // 案件共债个数
    Map<Long, Integer> conjointCountMap =
        selectConjointMapByDebtor(
            userSession.getOrgId(), Lists.newArrayList(list.get(0).getDebtId()));
    Integer conjointCount = conjointCountMap.get(caseInfoVO.getIdCard());
    caseInfoVO.setConjointCount(conjointCount == null ? 0 : conjointCount - 1);

    List<Long> caseIdList = list.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
    Map<Long, Long> reductionTotalMap = reductionService.getReductionTotalMap(caseIdList);
    // 已减免金额
    Long reductionTotal = reductionTotalMap.get(caseInfoVO.getId());
    caseInfoVO.setReductionTotal(reductionTotal == null ? 0L : reductionTotal);

    // 案件承诺还款金额
    Long ptpAmountTotal = caseMapper.selectCasePtpTotal(caseId);
    caseInfoVO.setPtpAmount(ptpAmountTotal == null ? 0L : ptpAmountTotal);

    // 案件导入模版字段
    Map<Long, Map<String, String>> tplMap = new HashMap<>();
    tplMap = fileStoreService.selectTplSeqRecordMap(Sets.newHashSet(caseInfoVO.getStoreId()));
    caseInfoVO.setTplFields(tplMap.get(caseInfoVO.getStoreId()));
    QosCaseOperationInfoVO qosVO = new QosCaseOperationInfoVO();
    BeanUtils.copyProperties(caseInfoVO, qosVO);
    // 为什么tplFields copy不进来
    qosVO.setTplFields(caseInfoVO.getTplFields());
    qosVO.setFields(caseInfoVO.getFields());
    String contactName = cope.getConName();
    String contactMobile = cope.getConMobile();
    String relationType = cope.getRelationType();
    qosVO.setContactMobile(contactMobile);
    qosVO.setContactName(contactName);
    qosVO.setRelationType(relationType);

    Map<Integer, String> operationStateNamesMap =orgConfigService
            .getAllNames(userSession.getOrgId(),OrgConfigEnums.Type.OPERATION_STATE.getCode());
    qosVO.setOperationStateName(operationStateNamesMap.get(qosVO.getOperationState()));
    return qosVO;
  }

  /**
   * 通过callUuid获催记
   *
   * @param callUuid 呼叫uuid
   * @return {@link CaseOperation}
   */
  private CaseOperation getCaseOperationByCallUuid(String callUuid) {
    if (StrUtil.isBlank(callUuid)){
      return null;
    }
    // 根据callUuid查询caseId
    CaseOperation param = new CaseOperation();
    param.setCallUuid(callUuid);
    CaseOperation caseOperation = caseOperationMapper.selectOne(param);
    if (ObjectUtil.isNotNull(caseOperation)){
      return caseOperation;
    }

    CaseOperationUseLess useLessParam = new CaseOperationUseLess();
    useLessParam.setCallUuid(callUuid);
    CaseOperationUseLess caseOperationUseLess = caseOperationUseLessService.selectOne(useLessParam);
    return BeanUtils.copyProperties(caseOperationUseLess, CaseOperation.class);
  }

  @Transactional(rollbackFor = Exception.class)
  public void caseIgnorePlan(CaseIgnorePlanParam caseIgnorePlanParam) {
    List<Long> caseIdList = caseIgnorePlanParam.getCaseIdList();
    if (CollectionUtils.isEmpty(caseIdList)) {
      throw new ApiException("请至少选择一个案件");
    }
    Boolean isIgnore = caseIgnorePlanParam.getIsIgnore();
    Case acase = new Case();
    if (isIgnore) {
      acase.setIgnorePlan(CaseEnums.IgnorePlan.YES.getCode());
    } else {
      acase.setIgnorePlan(CaseEnums.IgnorePlan.NO.getCode());
    }
    Example example = new Example(Case.class);
    example.createCriteria().andIn("id", caseIdList);
    super.updateByExampleSelective(acase, example);
    UserSession userSession = UserUtils.getTokenUser();
    //添加对应的任务记录，方便根据任务id查询任务操作的案件信息
    Integer type = isIgnore == true ? AsyncTaskEnums.Type.CASE_IGNORE_PLAN.getCode() : AsyncTaskEnums.Type.CASE_NOT_IGNORE_PLAN.getCode();
    Long taskId = asyncTaskService.createCaseIgnoreTask(
            caseIgnorePlanParam,
            userSession,
            (long)caseIdList.size(),
            AsyncTaskEnums.Status.SUCCESS.getCode(),
            type);
    Case caseInfo = new Case();
    caseInfo.setUpdateBy(userSession.getId());
    caseInfo.setUpdateTime(new Date());
    Example e = new Example(Case.class);
    Example.Criteria criteria = e.createCriteria();
    criteria.andIn("id", caseIdList);
    this.updateByExampleSelective(caseInfo, e);
    //添加案件操作日志记录
    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setType(isIgnore == true ? CaseLogEnums.Type.CASE_IGNORE_PLAN.getCode() : CaseLogEnums.Type.CASE_NOT_IGNORE_PLAN.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setCaseIds(caseIdList);
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);
  }

  public Map<Long, Long> getCaseUserId(List<Long> caseIdList) {
    if (com.anmi.collection.utils.CollectionUtils.isEmpty(caseIdList)) {
      return new HashMap<>();
    }
    Example example = new Example(Case.class);
    example.selectProperties("id");
    example.selectProperties("userId");
    example.createCriteria().andIn("id", caseIdList);
    List<Case> caseList = selectByExample(example);
    Map<Long, Long> caseUserMap =
        caseList.stream()
            .filter(c -> c.getUserId() != null)
            .collect(Collectors.toMap(Case::getId, Case::getUserId));
    return caseUserMap;
  }

  public void updateCaseInfo(CaseUpdateParam param) {
    UserSession userSession = userService.getTokenUser();
    Case caseInfo = super.selectByPrimaryKey(param.getCaseId());
    if (caseInfo == null) {
      throw new ApiException("案件不存在");
    }


    try {
      if (Objects.nonNull(param.getFieldJson())) {
        Map<String, String> fieldJson = caseInfo.getFieldJson();
        if (fieldJson != null) {
          String contactsUpdateTime = fieldJson.get("contacts_update_time");
          String contacts = fieldJson.get("contacts");
          if (StringUtils.isNotBlank(contactsUpdateTime)) {
            param.getFieldJson().put("contacts_update_time", contactsUpdateTime);
          }
          if (StringUtils.isNotBlank(contacts)) {
            param.getFieldJson().put("contacts", contacts);
          }
          // 脱敏的自定义字段不能编辑，得保存原有的，所以这里需根据原始field来更新可以编辑的
          fieldJson.forEach((k, v) -> {
            if (StringUtils.isNotBlank(param.getFieldJson().get(k))) {
              fieldJson.put(k, param.getFieldJson().get(k));
            }
          });
          param.getFieldJson().putAll(fieldJson);
        }
        param.getFieldJson().put("is_sync_address", "false");
      }
      caseInfo.setFieldJson(param.getFieldJson());
      caseInfo.setName(param.getName());
      caseInfo.setAmount(param.getAmount());
      caseInfo.setOverdueDays(
          param.getOverdueDays() == null ? null : Integer.valueOf(param.getOverdueDays()));
      caseInfo.setOverdueDate(
          param.getOverdueDate() == null ? null : new Date(param.getOverdueDate()));
      caseInfo.setUpdateTime(new Date());
      caseInfo.setUpdateBy(userSession.getId());
      super.updateByPrimaryKeySelective(caseInfo);
      if (systemConfig.getLocalDeploy() && Objects.nonNull(param.getFieldJson()) && !param.getFieldJson().isEmpty()) {
        caseMapper.insertOrUpdateCaseInfoField(caseInfo.getId(), param.getFieldJson());
      }
    } catch (Exception e) {
      throw new ApiException("数据填写错误，请检查数据格式！");
    }
  }

  public List<CaseTagVO> getCaseTagList() throws Exception {
    UserSession userSession = userService.getTokenUser();
    List<CaseTagVO> list = new ArrayList<>();
    List<CaseTag> caseTagList = caseTagMapper.selectCaseTagByOrgId(userSession.getOrgId());
    for (CaseTag caseTag : caseTagList) {
      CaseTagVO caseTagVO = BeanUtil.copyProperties(caseTag, CaseTagVO.class);
      caseTagVO.setColor(caseTag.getColor().intValue());
      caseTagVO.setState(caseTag.getState().intValue());
      list.add(caseTagVO);
    }
    return list;
  }

  @Transactional(rollbackFor = Exception.class)
  public void updateCaseTag(List<CaseTagUpdateParam> paramList) {
    UserSession userSession = userService.getTokenUser();
    for (CaseTagUpdateParam param : paramList) {
      CaseTag caseTag = caseTagMapper.selectByPrimaryKey(param.getId());
      Assert.notNull(caseTag, "id为" + param.getId() + "标签不存在");
      caseTag.setName(param.getName());
      caseTag.setColor(param.getColor().byteValue());
      caseTag.setState(param.getState().byteValue());
      caseTag.setUpdateTime(new Date());
      caseTagMapper.updateByPrimaryKey(caseTag);
      // 案件标签为关闭时，state=0
      if (CaseEnums.TagState.CLOSE.getCode().equals(param.getState())) {
        caseMapper.updateByTag(userSession.getOrgId(),param.getId(),userSession.getId());
        Example example=new Example(CaseTagRel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId",userSession.getOrgId());
        criteria.andEqualTo("tagId",param.getId());
        caseTagRelMapper.deleteByExample(example);
      }
    }

  }

  @Transactional(rollbackFor = Exception.class)
  public Long addTag(TagAddParam param){
    UserSession userSession = userService.getTokenUser();
    List<CaseTag> tags=this.caseTagMapper.selectCaseTagByOrgId(userSession.getOrgId());
    if(tags.size()>=20){
      throw new ApiException("自定义标签不能超过20个");
    }
    CaseTag caseTag=new CaseTag();
    caseTag.setState(CaseEnums.TagState.OPEN.getCode().byteValue());
    caseTag.setColor(param.getColor().byteValue());
    caseTag.setName(StringUtils.trimAnyBlank(param.getName()));
    caseTag.setOrgId(userSession.getOrgId());
    caseTag.setCreateTime(new Date());
    caseTag.setUpdateTime(new Date());
    this.caseTagMapper.insert(caseTag);
    return caseTag.getId();
  }

  @Transactional(rollbackFor = Exception.class)
  public void addCaseTag(CaseAddTagParam param) {
    if (!CollectionUtils.isEmpty(param.getAddTagIds()) && param.getAddTagIds().size() > 10) {
      throw new ApiException("案件最多只能添加十个标签");
    }
    if (param.getAllSelect()) {
      asyncAddCaseTagTask(param);
    } else {
      if (param.getCaseIds() == null || param.getCaseIds().size() == 0) {
        throw new ApiException("未选择任何案件信息");
      }
      UserSession userSession = UserUtils.getTokenUser();

      if (Objects.equals(param.getDelCaseTag(), Boolean.TRUE)) {
        Example exampleDelCasTagRel = new Example(CaseTagRel.class);
        Example.Criteria criteriaDelCasTagRel = exampleDelCasTagRel.createCriteria();
        criteriaDelCasTagRel.andEqualTo("orgId", userSession.getOrgId());
        criteriaDelCasTagRel.andIn("caseId", param.getCaseIds());
        caseTagRelMapper.deleteByExample(exampleDelCasTagRel);
      }

      if (!CollectionUtil.isEmpty(param.getAddTagIds())) {
        Example caseValidTagQuery = new Example(CaseTag.class);
        caseValidTagQuery.and().andEqualTo("state", CaseEnums.TagState.OPEN.getCode());
        caseValidTagQuery.and().andIn("id", param.getAddTagIds());
        List<CaseTag> validTag = this.caseTagMapper.selectByExample(caseValidTagQuery);
        if (CollectionUtils.isEmpty(validTag)) {
          throw new ApiException("所选标签均已禁用，请刷新后再操作");
        }
        List<CaseTagRel> caseTagRels = new ArrayList<>();
        for (Long caseId : param.getCaseIds()) {
          for (CaseTag tag : validTag) {
            CaseTagRel caseTagRel = new CaseTagRel();
            caseTagRel.setCaseId(caseId);
            caseTagRel.setTagId(tag.getId());
            caseTagRel.setOrgId(userSession.getOrgId());
            caseTagRels.add(caseTagRel);
          }
        }
        Example existExample = new Example(CaseTagRel.class);
        existExample.and().andEqualTo("orgId", userSession.getOrgId());
        existExample.and().andIn("caseId", param.getCaseIds());
        List<CaseTagRel> existCaseTagRel = caseTagRelMapper.selectByExample(existExample);
        caseTagRels = caseTagRels.stream().filter(t -> {
          for (CaseTagRel tmp : existCaseTagRel) {
            if (Objects.equals(tmp.getCaseId(), t.getCaseId()) &&
              Objects.equals(tmp.getTagId(), t.getTagId())) {
              return false;
            }
          }
          return true;
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(caseTagRels)) {
          this.caseTagRelMapper.insertList(caseTagRels);
        }
      }
      Long taskId = asyncTaskService.createAddCaseTagTask(
        param,
        userSession,
        (long) param.getCaseIds().size(),
        AsyncTaskEnums.Status.SUCCESS.getCode());
      Case caseInfo = new Case();
      caseInfo.setUpdateBy(userSession.getId());
      caseInfo.setUpdateTime(new Date());
      Example example = new Example(Case.class);
      Example.Criteria criteria = example.createCriteria();
      criteria.andIn("id", param.getCaseIds());
      this.updateByExampleSelective(caseInfo, example);

      CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
      caseBatchUpdateEvent.setType(CaseLogEnums.Type.CASE_ADD_TAG.getCode());
      caseBatchUpdateEvent.setUserSession(userSession);
      caseBatchUpdateEvent.setCaseIds(param.getCaseIds());
      caseBatchUpdateEvent.setTaskId(taskId);
      applicationContext.publishEvent(caseBatchUpdateEvent);
    }
  }

  public void asyncAddCaseTagTask(CaseAddTagParam param) {
    UserSession userSession = getTokenUser();
    if (param.getBeAmc()){
      listCaseAmcParamDeal(param);
    } else {
      listCaseAnmiParamDeal(param);
    }
    param.setCaseIds(null);
    //创建异步任务任务之前判断3分钟之前是否有相同操作
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(param).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }
    Long taskId = asyncTaskService.createAddCaseTagTask(param, userSession, 0L, AsyncTaskEnums.Status.ING.getCode());
    stringRedisTemplate.opsForList().leftPush(KeyCache.CASE_ADD_TAG_TASK_ID_LIST, taskId.toString());
  }


  @Transactional(rollbackFor = Exception.class)
  public void addCaseToLetter(CaseToLetterParam param) {
    //操作全部查询结果走异步
    if (param.getAllSelect()) {
      asyncAddCaseToLetter(param);
      return;
    }
    UserSession userSession = userService.getTokenUser();
    List<Long> caseIds = param.getCaseIds();
    if (CollectionUtils.isEmpty(caseIds)) {
      throw new ApiException("案件caseIds不能为空");
    }

    //1.查询案件信息
    List<Case> caseList = getCaseListByCaseIds(caseIds);
    //2.添加案件到函件管理
    List<Letter> letterList = new ArrayList<>();
    caseList.forEach(caseInfo -> {
      Letter letter = AuthBeanUtils.copy(caseInfo, Letter.class);
      letter.setId(null);
      letter.setCaseId(caseInfo.getId());
      letter.setOrgId(userSession.getOrgId());
      letter.setOutSerialNo(caseInfo.getOutSerialTemp());
      letter.setDepId(caseInfo.getDepId());
      letter.setTeamId(caseInfo.getTeamId());
      letter.setAllotAgent(caseInfo.getAllotAgent());
      letterList.add(letter);
    });
    letterService.addLetterBatch(letterList);
    //3.添加对应的任务记录，方便根据任务id查询任务操作的案件信息
    Long taskId = asyncTaskService.createAddCaseToLetterTask(
            param,
            userSession,
            (long)caseIds.size(),
            AsyncTaskEnums.Status.SUCCESS.getCode());
    Case caseInfo = new Case();
    caseInfo.setUpdateBy(userSession.getId());
    caseInfo.setUpdateTime(new Date());
    Example example = new Example(Case.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andIn("id", param.getCaseIds());
    this.updateByExampleSelective(caseInfo, example);
    //4.添加案件操作日志记录
    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.CASE_TO_LETTER.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setCaseIds(param.getCaseIds());
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);
  }

  @Transactional(rollbackFor = Exception.class)
  public void addCaseToLawsuit(CaseToLawsuitParam param) {
    //操作全部查询结果走异步
    if (param.getAllSelect()) {
      asyncAddCaseToLawsuit(param);
      return;
    }
    List<Long> caseIds = param.getCaseIds();
    if (CollectionUtils.isEmpty(caseIds)) {
      throw new ApiException("案件caseIds不能为空");
    }
    UserSession userSession = userService.getTokenUser();
    //1.查询案件信息
    List<Case> caseList = getCaseListByCaseIds(caseIds);
    //2.添加案件到诉讼管理
    lawsuitService.addLawsuit(caseList, param.getHandleType());
    //3.添加对应的任务记录，方便根据任务id查询任务操作的案件信息
    Long taskId = asyncTaskService.createAddCaseToLawsuitTask(param, userSession,
            (long)caseIds.size(), AsyncTaskEnums.Status.SUCCESS.getCode());
    //4.添加案件操作日志记录
    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.CASE_TO_LAWSUIT.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setCaseIds(param.getCaseIds());
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);
  }

  public List<Case> getCaseListByCaseIds(List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return new ArrayList<>();
    }
    Example example = new Example(Case.class);
    example.and().andIn("id", caseIds);
    final List<Case> cases = caseMapper.selectByExample(example);
    return cases;
  }

  public void asyncAddCaseToLetter(CaseToLetterParam param) {
    if (!param.getAllSelect()) {
      AssertUtil.notEmpty(param.getCaseIds(),"请选择案件");
    } else {
      param.setCaseIds(null);
    }

    if (param.getBeAmc()){
      listCaseAmcParamDeal(param);
    } else {
      listCaseAnmiParamDeal(param);
    }

    //创建异步任务任务之前判断3分钟之前是否有相同操作
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(param).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }

    UserSession userSession = getTokenUser();
    //使用线程池异步创建任务：这里不在使用后台起一个线程睡眠监听的方式
    Long taskId = asyncTaskService.createAddCaseToLetterTask(param, userSession, 0L, AsyncTaskEnums.Status.ING.getCode());
    fixedThreadPool.submit(()->{
      try {
        List<CaseQueryResult> caseList;
        if (systemConfig.getESSwitch()) {
          caseList = getAllCasesUsingEs(param);
        }else{
          caseList = queryResultForMulti(param);
        }

        if (CollectionUtils.isEmpty(caseList)) {
          log.info("批量添加案件到函件列表，案件列表不能为空");
          return;
        }
        List<Long> caseIds = caseList.stream().map(Case::getId).collect(Collectors.toList());
        List<Letter> letterList = new ArrayList<>();
        caseList.forEach(caseInfo -> {
          Letter letter = AuthBeanUtils.copy(caseInfo, Letter.class);
          letter.setId(null);
          letter.setCaseId(caseInfo.getId());
          letter.setOutSerialNo(caseInfo.getOutSerialTemp());
          letter.setDepId(caseInfo.getDepId());
          letter.setTeamId(caseInfo.getTeamId());
          letter.setOrgId(userSession.getOrgId());
          letter.setAllotAgent(caseInfo.getAllotAgent());
          letterList.add(letter);
        });
        //添加案件到函件管理
        letterService.addLetterBatch(letterList);
        Case caseInfo = new Case();
        caseInfo.setUpdateBy(userSession.getId());
        caseInfo.setUpdateTime(new Date());
        Example example = new Example(Case.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", caseIds);
        this.updateByExampleSelective(caseInfo, example);

        AsyncTask task = new AsyncTask();
        task.setId(taskId);
        task.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
        task.setTotal((long) caseIds.size());
        task.setSuccessAmt((long) caseIds.size());
        asyncTaskService.updateByPrimaryKeySelective(task);

        //添加案件操作日志记录
        CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
        caseBatchUpdateEvent.setType(CaseLogEnums.Type.CASE_TO_LETTER.getCode());
        caseBatchUpdateEvent.setUserSession(userSession);
        caseBatchUpdateEvent.setCaseIds(caseIds);
        caseBatchUpdateEvent.setTaskId(taskId);
        applicationContext.publishEvent(caseBatchUpdateEvent);
      } catch (Exception e) {
        log.error("添加案件到函件列表,taskId:{},失败！", taskId, e);
        AsyncTask task = asyncTaskService.selectByPrimaryKey(taskId);
        asyncTaskService.updateTaskFinish(task, e.getMessage());
      }
    });
  }

  public void asyncAddCaseToLawsuit(CaseToLawsuitParam param) {
    UserSession userSession = getTokenUser();
    param.setOrgIds(userSession.getOrgId().toString());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      // 如果是分公司管理员，进行分公司隔离
      param.setDepId(userSession.getDepId());
    }
    //创建异步任务任务之前判断3分钟之前是否有相同操作
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(param).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }
    //使用线程池异步创建任务：这里不在使用后台起一个线程睡眠监听的方式
    Long taskId = asyncTaskService.createAddCaseToLawsuitTask(param, userSession, 0L, AsyncTaskEnums.Status.ING.getCode());
    fixedThreadPool.submit(()->{
      try {
        List<CaseQueryResult> caseList;
        if (systemConfig.getESSwitch()) {
          caseList = getAllCasesUsingEs(param);
        } else {
          caseList = queryResultForMulti(param);
        }
        if (CollectionUtils.isEmpty(caseList)) {
          log.info("批量案件加入诉讼，案件列表不能为空");
          return;
        }
        List<Long> caseIds = caseList.stream().map(Case::getId).collect(Collectors.toList());
        List<Case> cases = new ArrayList<>();
        caseList.forEach(caseQueryResult -> {
          cases.add(AuthBeanUtils.copy(caseQueryResult, Case.class));
        });
        //添加案件到函件管理
        lawsuitService.addLawsuit(cases, param.getHandleType());

        AsyncTask task = new AsyncTask();
        task.setId(taskId);
        task.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
        task.setTotal((long) caseIds.size());
        task.setSuccessAmt((long) caseIds.size());
        asyncTaskService.updateByPrimaryKeySelective(task);

        //添加案件操作日志记录
        CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
        caseBatchUpdateEvent.setType(CaseLogEnums.Type.CASE_TO_LAWSUIT.getCode());
        caseBatchUpdateEvent.setUserSession(userSession);
        caseBatchUpdateEvent.setCaseIds(caseIds);
        caseBatchUpdateEvent.setTaskId(taskId);
        applicationContext.publishEvent(caseBatchUpdateEvent);
      } catch (Exception e) {
        log.error("批量案件加入诉讼任务,taskId:{},失败！", taskId, e);
        // 任务失败立即删除任务限制key
        stringRedisTemplate.delete(ASYNC_PREFIX + md5);
        AsyncTask task = asyncTaskService.selectByPrimaryKey(taskId);
        asyncTaskService.updateTaskFinish(task, e.getMessage());
      }
    });
  }

  @Transactional(rollbackFor = Exception.class)
  public void caseAutoEnd(Long orgId) throws InterruptedException {
    UserSession userSession = new UserSession();
    userSession.setOrgId(orgId);
    userSession.setId(0L);
    // 分案完成、留案、暂停可结案，分批操作
    while (true) {
      List<CaseQueryResult> caseList = caseMapper.selectAutoEndCaseList(orgId, 500, null);
      if (CollectionUtils.isEmpty(caseList)) {
        break;
      }
      // 自动结案 结案类型为成功
      EndInfoDTO endInfoDTO = new EndInfoDTO();
      endInfoDTO.setEndType(CaseEndConfigEnums.EndType.SUCCESS.getCode());
      // 批量执行修改案件信息、log记录
      changeStatusUpdate(
          caseList,
          CaseEnums.ChangeStatus.END.getCode(),
          "还款金额大于等于贷款金额，自动结案",
          null,
          null,
          userSession,
          null,
          null,
           endInfoDTO,
              null);
      // 睡眠一下
      Thread.sleep(1000);
    }
  }

  public Boolean isCaseOngoing(Case caseInfo) {
    return caseInfo.getRecovery() == CaseEnums.Recovery.NORMAL.getCode();
  }

  public List<CaseQueryResult> protectCaseStatus(Long orgId, List<CaseQueryResult> caseList) throws Exception{
    if (CollectionUtils.isEmpty(caseList)) {
      return null;
    }
    String lockKey = KeyCache.CASE_STATUS_LOCK + orgId;
    try {
      if (!redisLock.tryLock(lockKey)) {
        log.error("锁超时,key:{}", lockKey);
        throw new ApiException("操作过于繁忙，请稍后再试");
      }
      // 取出保护中的案件
      Set<String> idLockList =
          stringRedisTemplate.opsForSet().members(KeyCache.CASE_PROTECT_EXIST_IDS + orgId);
      if (!CollectionUtils.isEmpty(idLockList)) {
        // 过滤
        caseList =
            caseList.stream()
                .filter(c -> !idLockList.contains(c.getId().toString()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(caseList)) {
          throw new ApiException("搜索可执行案件为空，请确定案件状态");
        }
      }
      // 案件批量加锁，状态保护
      stringRedisTemplate
          .opsForSet()
          .add(KeyCache.CASE_PROTECT_EXIST_IDS + orgId,
                  caseList.stream().map(c -> c.getId().toString()).toArray(String[]::new));
      // 防止案件一直被锁
      stringRedisTemplate.expire(KeyCache.CASE_PROTECT_EXIST_IDS + orgId, 2L, TimeUnit.HOURS);
      return caseList;
    } finally {
      redisLock.unlock(lockKey);
    }
  }

  public void removeProtectCases(Long orgId, List<CaseQueryResult> caseList) {
    if (CollectionUtils.isEmpty(caseList)) {
      return;
    }
    List<String> caseIdStrList =
        caseList.stream().map(c -> c.getId().toString()).collect(Collectors.toList());
    // 解除状态保护
    stringRedisTemplate
        .opsForSet()
        .remove(KeyCache.CASE_PROTECT_EXIST_IDS + orgId, caseIdStrList.toArray());
  }

  public void allotCooperator(CaseCooperationAllotParam param) throws Exception {
    if (param.getCooperatorId() == null) {
      throw new ApiException("请选择协催员");
    }
    User cooperator = userService.selectByPrimaryKey(param.getCooperatorId());
    if (cooperator == null || User.Status.NORMAL.getCode() != cooperator.getStatus()) {
      throw new ApiException("协催员不存在");
    }
    if (!param.getAllSelect()
        && com.anmi.collection.utils.CollectionUtils.isEmpty(param.getCaseIds())) {
      throw new ApiException("请选择案件");
    }
    UserSession userSession = userService.getTokenUser();
    // 加锁保护案件协催
    List<CaseQueryResult> caseList = selectCooperationCaseList(param, userSession);
    caseList = protectCaseStatus(userSession.getOrgId(), caseList);
    if (CollectionUtils.isEmpty(caseList)) {
      throw new ApiException("搜索可执行案件为空，请确定案件状态");
    }
    if (!param.getAllSelect()) {
      // 同步
      caseCooperationService.doAllotCooperator(caseList, cooperator, userSession, null);
    } else {
      // 异步
      caseCooperationService.asyncAllotCooperator(caseList, cooperator, userSession);
    }
  }

  public List<CaseQueryResult> selectCooperationCaseList(
      CaseCooperationAllotParam param, UserSession userSession) throws Exception {
    CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
    if (!param.getAllSelect()) {
      if (CommonUtils.isEmpty(param.getCaseIds())) {
        throw new ApiException("没有可操作案件，请重新确定案件状态！");
      }
      caseMultiQuery.setCaseIds(param.getCaseIds());
    } else {
      // 选择全部
      BeanUtil.copyProperties(param, caseMultiQuery);
      caseMultiQuery.setSelectField("ca.id");
      // 公司隔离
      caseMultiQuery.setOrgIds(UserUtils.getTokenUser().getOrgId().toString());
      if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
        // 如果是分公司管理员，进行分公司隔离
        caseMultiQuery.setDepId(userSession.getDepId());
      } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
        caseMultiQuery.setTeamIds(userSession.getTeamId().toString());
      }
      caseMultiQuery.setCaseIds(null);
    }
    List<Integer> allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode());
    caseMultiQuery.setAllotStatues(StringUtils.join(allotStatues, ","));
    List<CaseQueryResult> caseList = new ArrayList<>();
    if (param.getAllSelect() && systemConfig.getESSwitch()) {
      //如果有es就走es查询，解决慢sql问题
      caseMultiQuery.setFields(Lists.newArrayList("id"));
      caseList = getAllCasesUsingEs(caseMultiQuery);
    } else {
      caseList = queryResultForMulti(caseMultiQuery);
    }
    return caseList;
  }

  @Transactional(rollbackFor = Exception.class)
  public void delContacts(Long orgId, Set<Long> debtorIdSet, List<Long> notConjointCaseIds) {
    // 判断共债案件，删除联系人
    List<Long> existList = selectExistConjointList(orgId, Lists.newArrayList(debtorIdSet));
    debtorIdSet.removeAll(existList);
    // 删除共债通讯录
    contactsService.deleteContactsByRelIds(
        orgId, Lists.newArrayList(debtorIdSet), Contacts.IsConjoint.YES.getCode());
    // 删除非共债通讯录
    contactsService.deleteContactsByRelIds(
        orgId, notConjointCaseIds, Contacts.IsConjoint.NO.getCode());
    // 删除债务人信息
    caseDebtorService.deleteDebtorsByDebtId(Lists.newArrayList(debtorIdSet));
  }

  public void checkStatusAndUserId(Long caseId,UserSession userSession) {
    Case caseInfo = caseMapper.selectByPrimaryKey(caseId);
    if (caseInfo.getUserId() == null || !userSession.getId().equals(caseInfo.getUserId())) {
      // 非催员则校验是否是协催员
      CaseCooperation caseCooperation = caseCooperationService.getCooperationByCaseId(caseId);
      if (caseCooperation == null || !userSession.getId().equals(caseCooperation.getCooperatorId())) {
        //非协催员判断是否开启了团队催收
        OrgSwitch orgSwitch = this.orgSwitchMapper.selectOrgSwitchByOrgId(userSession.getOrgId());
        Assert.notNull(orgSwitch, "找不到OrgSwitch");
        if (!OrgSwitchEnums.TeamUrgeSwitch.OPEN.getCode().equals(orgSwitch.getTeamUrgeSwitch())
            || orgSwitch.getTeamUrgeIds() == null
            || userSession.getTeamId() == null
            || !userSession.getTeamId().equals(caseInfo.getTeamId())
            || !JSON.parseArray(orgSwitch.getTeamUrgeIds()).toJavaList(Long.class).contains(userSession.getTeamId())) {
          throw new ApiException("您已不是该案件负责人，无法执行此操作");
        }
      }
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void autoEnd(UserSession userSession, List<Long> caseIdList) {
    List<CaseQueryResult> endCaseList =
        caseMapper.selectAutoEndCaseList(userSession.getOrgId(), caseIdList.size(), caseIdList);
    if (CollectionUtils.isEmpty(endCaseList)) {
      return;
    }
    // 自动结案默认结案类型为调解成功
    EndInfoDTO endInfoDTO = new EndInfoDTO();
    endInfoDTO.setEndType(CaseEndConfigEnums.EndType.SUCCESS.getCode());
    changeStatusUpdate(
        endCaseList,
        CaseEnums.ChangeStatus.END.getCode(),
        "还款金额大于等于贷款金额，自动结案",
        null,
        null,
        userSession,
        null,
        null,
        endInfoDTO,
            null);
  }

  /**
   * 案件列表（es）
   *
   * @param query 查询
   * @return {@link CaseListInfoVO}
   * @throws Exception 例外
   */
  public CaseListInfoVO listCaseUsingEs(CaseMultiQuery query) throws Exception {
    if (query.getBeAmc()){
      return listCaseUsingEsAmc(query);
    }
    return listCaseUsingEsAnmi(query);
  }

  /**
   * 安米案件列表(es)
   *
   * @param query 查询
   * @return {@link CaseListInfoVO}
   * @throws Exception 例外
   */
  private CaseListInfoVO listCaseUsingEsAnmi(CaseMultiQuery query) throws Exception {
    listCaseAnmiParamDeal(query);
    return listCasesUsingEs(query);
  }

  /**
   * 安米案件列表查询参数处理
   *
   * @param query 查询
   */
  public void listCaseAnmiParamDeal(CaseMultiQuery query) {
    UserSession session = UserUtils.getTokenUser();
    // 强制设置公司
    query.setOrgIds(session.getOrgId().toString());
    if (null != query.getAction()) {
      if (1 == query.getAction() || 6 == query.getAction() || 4 == query.getAction() || 5 == query.getAction() || 9 ==query.getAction() || 10 == query.getAction()) {
        if(query.getAction()!=null && query.getAction()==9){
          OrgSwitch orgSwitch = this.orgSwitchMapper.selectOrgSwitchByOrgId(session.getOrgId());
          Assert.notNull(orgSwitch,"找不到OrgSwitch");
          if(!OrgSwitchEnums.TeamUrgeSwitch.OPEN.getCode().equals(orgSwitch.getTeamUrgeSwitch())){
            throw new ApiException("公司不支持团队催收，查询失败");
          }
          if(orgSwitch.getTeamUrgeIds()==null){
            throw new ApiException("您所属的团队未开启团队催收，查询失败");
          }
          if(!JSON.parseArray(orgSwitch.getTeamUrgeIds()).toJavaList(Long.class).contains(session.getTeamId())){
            throw new ApiException("您所属的团队未开启团队催收，查询失败");
          }
          query.setTeamIds(String.valueOf(session.getTeamId()));
        } else if (query.getAction() != null && query.getAction() == 10) {
          DepTeam team = depTeamService.selectByPrimaryKey(session.getTeamId());
          if (Objects.equals(team.getUnderTeam(), DepTeamEnums.UnderTeam.YES.getCode())) {
            query.setTeamIds(String.valueOf(session.getTeamId()));
          } else {
            query.setDepId(session.getDepId());
          }
        } else {
          query.setUserIds(session.getId().toString());
        }
      } else if (2 == query.getAction() || 3 == query.getAction() || 7 == query.getAction() || 8 == query.getAction() || 0 == query.getAction()) {
        if (UserUtils.likeBranchAdmin()) {
          // 如果是分公司管理员，进行分公司隔离
          query.setDepId(session.getDepId());
        } else if (UserUtils.likeTeamLeader()) {
          if (query.getAction() != null && query.getAction() == 0) {
            DepTeam team = depTeamService.selectByPrimaryKey(session.getTeamId());
            if (Objects.equals(team.getUnderTeam(), DepTeamEnums.UnderTeam.YES.getCode())) {
              query.setTeamIds(String.valueOf(session.getTeamId()));
            } else {
              query.setDepId(session.getDepId());
            }
          } else {
            query.setTeamIds(String.valueOf(session.getTeamId()));
          }
        }
      } else {
        throw new ApiException("Action 非法！");
      }
    } else if(ObjectUtil.isNull(query.getId()) && ObjectUtil.isEmpty(query.getCaseIds())){
      query.setRecovery(-1);
      query.setIsAgent(null);
      query.setSortRule(2);
      query.setOrderBy(0);

      if (UserUtils.likeBranchAdmin()) {
        // 如果是分公司管理员，进行分公司隔离
        query.setDepId(session.getDepId());
      } else if (UserUtils.likeTeamLeader()) {
        if (query.getAction() != null && query.getAction() == 0) {
          DepTeam team = depTeamService.selectByPrimaryKey(session.getTeamId());
          if (Objects.equals(team.getUnderTeam(), DepTeamEnums.UnderTeam.YES.getCode())) {
            query.setTeamIds(String.valueOf(session.getTeamId()));
          } else {
            query.setDepId(session.getDepId());
          }
        } else {
          query.setTeamIds(String.valueOf(session.getTeamId()));
        }
      }
    } else {
      if (UserUtils.likeBranchAdmin()) {
        query.setDepId(session.getDepId());
      } else if (UserUtils.likeTeamLeader()) {
        query.setTeamIds(session.getTeamId().toString());
      } else if (!UserUtils.likeAdmin()) {
        throw new ApiException("非法角色，拒绝访问，请联系管理员");
      }
    }
  }

  /**
   * 甲方案件列表(es)
   *
   * @param query 查询
   * @return {@link CaseListInfoVO}
   * @throws Exception 例外
   */
  private CaseListInfoVO listCaseUsingEsAmc(CaseMultiQuery query) throws Exception {
    listCaseAmcParamDeal(query);
    return listCasesUsingEs(query);
  }

  /**
   * 案件列表(es)
   *
   * @param query 查询
   * @return {@link CaseListInfoVO}
   * @throws Exception 例外
   */
  public CaseListInfoVO listCasesUsingEs(CaseMultiQuery query) throws Exception {
    // Long startAll = System.currentTimeMillis();
    if (query.getOrderBy() == null) {
      query.setOrderBy(0);
    }
    if (query.getSortRule() == null) {
      query.setSortRule(0);
    }
    // 参数转化（添加筛选条件时，分页案件查询、不分页案件查询(留案、批量调整等异步操作)，案件统计、案件导出方法需同时添加筛选条件）
    // 日期状态参数调整
    convertDate(query);
    // 快捷搜索调整
    coverSearchLabel(query);
    // 案件联系人条件转化成caseIds
    coverContactMobiles(query);

    List<CaseQueryResult> list;
    CaseMultiQueryDto dto = new CaseMultiQueryDto();
    BeanUtils.copyProperties(query, dto);
    if(!CollectionUtils.isEmpty(query.getFieldSearch())){
      dto.setFieldSearchMap(new HashMap<>());
      query.getFieldSearch().forEach(t -> dto.getFieldSearchMap().put(t.getKey(), t.getValues()));
    }
    CaseInfoResponse response = remoteAlfredService.fetchFromCaseInfoRemote(dto);
    list = BeanUtil.copyPropertiesFromList(response.getList(), CaseQueryResult.class);
    list.forEach(caseQueryResult -> {
      decryptDataFromES(caseQueryResult);
    });
    Long total = response.getTotalNum();
    //endregion


    List<CaseVO> vos = convertVO(list);
    // 案件共债个数
    List<Long> debtorList =
            list.stream()
                    .filter(c -> c.getDebtId() != null)
                    .map(CaseQueryResult::getDebtId)
                    .collect(Collectors.toList());
    Map<Long, Integer> conjointCountMap = selectConjointMapByDebtor(getOrgId(), debtorList);
    //减免金额
    List<Long> caseIdList = list.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
    Map<Long, Long> reductionTotalMap = reductionService.getReductionTotalMap(caseIdList);
    // 查询对应的最新时间案件催收小结
    List<CaseNote> caseNotes = new ArrayList<>();
    List<CaseOperation> caseOperations = new ArrayList<>();
    if (!CollectionUtils.isEmpty(caseIdList)) {
      caseNotes = caseNoteService.selectNoteByCaseIds(caseIdList, CaseNoteEnums.Type.NOTE.getCode());
      caseOperations = caseOperationMapper.selectDescByCaseIds(caseIdList);
    }
    Map<Long, CaseNote> caseNoteMap = new HashMap<>();
    if (!CollectionUtils.isEmpty(caseNotes)){
      caseNoteMap = caseNotes.stream().collect(Collectors.toMap(CaseNote::getCaseId, f -> f));
    }
    // 查询对应的最新时间催收备注
    Map<Long, CaseOperation> operationMap = new HashMap<>();
    if (!CollectionUtils.isEmpty(caseOperations)){
      operationMap = caseOperations.stream().collect(Collectors.toMap(CaseOperation::getCaseId, f -> f));
    }
    for (CaseVO vo : vos) {
      Integer conjointCount = conjointCountMap.get(vo.getDebtId());
      if (conjointCount != null) {
        conjointCount--;
      } else if (vo.getDebtId() != null) {
        conjointCount = 0;
      } else {
        conjointCount = null;
      }
      //设置共债案件个数
      vo.setConjointCount(conjointCount);
      //设置已减免金额
      Long reductionTotal = reductionTotalMap.get(vo.getId());
      vo.setReductionTotal(reductionTotal == null ? 0L : reductionTotal);
      //设置催收小结
      if (caseNoteMap.containsKey(vo.getId())) {
        vo.setNote(caseNoteMap.get(vo.getId()).getContent());
      }
      // 催收备注
      if (operationMap.containsKey(vo.getId())) {
        vo.setOperationDesc(operationMap.get(vo.getId()).getDesc());
      }
    }

    // 当前是否为管理员端分配催员或者是催员端-已结案案件页面，是则列表显示结案类型信息
    if (query.getAction() != null && (query.getAction() == 3 || query.getAction() == 5)) {
      List<Long> endConfigIds = vos.stream().map(CaseVO::getEndConfigId)
              .filter(Objects::nonNull).distinct()
              .collect(Collectors.toList());
      Map<Long, String> endConfigMap = caseEndConfigService.queryEndConfigByIds(endConfigIds);
      if (!CollectionUtils.isEmpty(endConfigMap)) {
        for (CaseVO vo : vos) {
          if (vo.getEndConfigId() != null && endConfigMap.containsKey(vo.getEndConfigId())) {
            vo.setAttachedRes(endConfigMap.get(vo.getEndConfigId()));
          }
        }
      }
    }


    // 当前是否为分配催员或者是催员页面，是则列表显示管控信息
    if (query.getAction() != null && (query.getAction() == 3 || query.getAction() == 1)) {
      for (CaseVO vo : vos) {
        if (vo.getCtrlId() == null) {
          continue;
        }
        Integer caution = caseCtrlService.queryCaseCtrlCaution(vo.getCtrlId(), vo.getId());
        vo.setCaseCtrlCaution(caution);
      }
    }

    CaseListInfoVO pageOutput;
    pageOutput = new CaseListInfoVO(query.getPage(), query.getLimit(), total.intValue(), vos);
    return pageOutput;
  }

  /**
   * 查询所有的案件数据，分页循环查询(es)
   *
   * @param query 查询
   * @return {@link List}<{@link CaseQueryResult}>
   */
  public List<CaseQueryResult> getAllCasesUsingEs(CaseMultiQuery query) {
    try {
      query.setOrderBy(12);
      if (query.getOrderBy() == null) {
        query.setOrderBy(0);
      }
      if (query.getSortRule() == null) {
        query.setSortRule(0);
      }
      encryptQueryData(query);
      // 参数转化（添加筛选条件时，分页案件查询、不分页案件查询(留案、批量调整等异步操作)，案件统计、案件导出方法需同时添加筛选条件）
      // 日期状态参数调整
      convertDate(query);
      // 快捷搜索调整
      coverSearchLabel(query);
      // 案件联系人条件转化成caseIds
      coverContactMobiles(query);

      CaseMultiQueryDto dto = AuthBeanUtils.copy(query, CaseMultiQueryDto.class);
      if (StringUtils.isNotBlank(query.getIsAgent())) {
        dto.setIsAgent(Integer.valueOf(query.getIsAgent()));
      }
      if(!CollectionUtils.isEmpty(query.getFieldSearch())){
        dto.setFieldSearchMap(new HashMap<>());
        query.getFieldSearch().forEach(t -> dto.getFieldSearchMap().put(t.getKey(), t.getValues()));
      }
      log.info("执行es查询的条件为：{}", JSONUtil.toJsonStr(dto));
      int page = 1;
      int totalPage = 1;
      int pageSize = 2000;
      List<CaseQueryResult> result = new ArrayList<>();
      while (page <= totalPage) {
        dto.setPage(page);
        dto.setLimit(pageSize);
        CaseInfoResponse response = remoteAlfredService.fetchFromCaseInfoRemote(dto);
        List<CaseQueryResult> list = BeanUtil.copyPropertiesFromList(response.getList(), CaseQueryResult.class);
        list.forEach(caseQueryResult -> {
          decryptDataFromES(caseQueryResult);
        });
        result.addAll(list);
        if (page == 1) {
          totalPage = (int) (response.getTotalNum() % pageSize == 0 ? response.getTotalNum() / pageSize : response.getTotalNum() / pageSize + 1);
        }
        page++;
      }
      return result;
    } catch (Exception ex) {
      throw new RuntimeException(ex);
    }
  }

  //查询所有的案件数据，分页循环查询
  public Long getAllCasesUsingEs(CaseMultiQuery query, Consumer<PageOutput<CaseQueryResult>> callBack) {
    try {
      query.setOrderBy(12);
      if (query.getOrderBy() == null) {
        query.setOrderBy(0);
      }
      if (query.getSortRule() == null) {
        query.setSortRule(0);
      }
      encryptQueryData(query);
      // 参数转化（添加筛选条件时，分页案件查询、不分页案件查询(留案、批量调整等异步操作)，案件统计、案件导出方法需同时添加筛选条件）
      // 日期状态参数调整
      convertDate(query);
      // 快捷搜索调整
      coverSearchLabel(query);
      // 案件联系人条件转化成caseIds
      coverContactMobiles(query);
      CaseMultiQueryDto dto = AuthBeanUtils.copy(query, CaseMultiQueryDto.class);
      if (StringUtils.isNotBlank(query.getIsAgent())) {
        dto.setIsAgent(Integer.valueOf(query.getIsAgent()));
      }
      if(!CollectionUtils.isEmpty(query.getFieldSearch())){
        dto.setFieldSearchMap(new HashMap<>());
        query.getFieldSearch().forEach(t -> dto.getFieldSearchMap().put(t.getKey(), t.getValues()));
      }
      log.info("执行es查询的条件为：{}", JSONUtil.toJsonStr(dto));
      int page = 1;
      int totalPage = 1;
      int pageSize = 2000;
      long total = 0L;
      while (page <= totalPage) {
        dto.setPage(page);
        dto.setLimit(pageSize);
        CaseInfoResponse response = remoteAlfredService.fetchFromCaseInfoRemote(dto);
        total += CollectionUtil.size(response.getList());
        List<CaseQueryResult> list = BeanUtil.copyPropertiesFromList(response.getList(), CaseQueryResult.class);
        list.forEach(caseQueryResult -> {
          decryptDataFromES(caseQueryResult);
        });
        if (page == 1) {
          totalPage = (int) (response.getTotalNum() % pageSize == 0 ? response.getTotalNum() / pageSize : response.getTotalNum() / pageSize + 1);
        }
        PageOutput<CaseQueryResult> pageInfo=new PageOutput<>();
        pageInfo.setTotal((int)response.getTotalNum());
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(pageSize);
        pageInfo.setList(list);
        pageInfo.setPages(totalPage);
        callBack.accept(pageInfo);
        page++;
      }
      return total;
    } catch (Exception ex) {
      throw new ApiException("es查询出错",ex);
    }
  }

  @Transactional(rollbackFor = Throwable.class)
  public void changeOperationState(ChangeOperationStateParam param) {
    if (param.getAllSelect()) {
      asyncChangeOperationStateTask(param);
    } else {
      if(param.getCaseIds()==null||param.getCaseIds().size()==0){
        throw new ApiException("未选择任何案件信息");
      }
      UserSession userSession = getTokenUser();
      User creator = userService.selectByPrimaryKey(userSession.getId());
      List<Long> allCaseList=new ArrayList<>();
      //同步同一委案公司下的共债案件的催收进程
      if(param.getSyncConjoint()!=null&&param.getSyncConjoint()){
        allCaseList.addAll(caseMapper.selectConjointByCaseIds(param.getCaseIds()));
      }
      param.getCaseIds().forEach(caseId ->{
        if (!allCaseList.contains(caseId)) {
          allCaseList.add(caseId);
        }
      });
      Long taskId =
              asyncTaskService.createOperationStateTask(
                      param,
                      userSession,
                      Long.valueOf(allCaseList.size()),
                      AsyncTaskEnums.Status.SUCCESS.getCode());
      Case caseInfo=new Case();
      caseInfo.setOperationState(param.getOperationState());
      caseInfo.setUpdateBy(userSession.getId());
      caseInfo.setUpdateTime(new Date());
      Example example=new Example(Case.class);
      Example.Criteria criteria = example.createCriteria();
      criteria.andIn("id",allCaseList);
      this.updateByExampleSelective(caseInfo,example);

      CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
      caseBatchUpdateEvent.setType(CaseLogEnums.Type.CHANGE_OPERATION_STATE.getCode());
      caseBatchUpdateEvent.setUserSession(userSession);
      caseBatchUpdateEvent.setCaseIds(allCaseList);
      caseBatchUpdateEvent.setTaskId(taskId);
      applicationContext.publishEvent(caseBatchUpdateEvent);
    }
  }

  public void asyncChangeOperationStateTask(ChangeOperationStateParam param) {
    UserSession userSession = getTokenUser();
    param.setOrgIds(userSession.getOrgId().toString());
    if (UserUtils.likeAdmin(userSession.getRoleId())) {
      param.setOrgIds(userSession.getOrgId().toString());
    } else if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      param.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      param.setTeamIds(userSession.getTeamId().toString());
    } else {
      throw new ApiException("当前账号没有操作权限");
    }
    param.setCaseIds(null);
    //创建异步任务任务之前判断3分钟之前是否有相同操作
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(param).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }
    Long taskId = asyncTaskService.createOperationStateTask(param, userSession, 0L, AsyncTaskEnums.Status.ING.getCode());
    stringRedisTemplate.opsForList().leftPush(KeyCache.CASE_CHANGE_OPERATION_STATE_TASK_ID_LIST, taskId.toString());
  }

  @Transactional(rollbackFor = Exception.class)
  public void delCase(List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return;
    }
    Example example = new Example(Case.class);
    example.and().andIn("id", caseIds);
    int i = caseMapper.deleteByExample(example);
    // 级联删除案件关联数据
    delJoinDataByCaseIds(caseIds);
  }

  @Transactional(rollbackFor = Exception.class)
  public int backupCaseAndLog(List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return 0;
    }

    //备份案件
    caseHistoryMapper.backupCaseInfo(caseIds);
    //物理删除案件
    Example example = new Example(Case.class);
    example.and().andIn("id", caseIds);
    int cnt = caseMapper.deleteByExample(example);

    //备份案件日志
    caseLogMapper.backupCaseLogInfo(caseIds);

    //删除原表日志
    Example logExample = new Example(CaseLog.class);
    logExample.and().andIn("caseId", caseIds);
    caseLogMapper.deleteByExample(logExample);
    return cnt;
  }

  @Transactional(rollbackFor = Exception.class)
  public void backupAndDelCase(List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return;
    }
    // 备份案件日志，然后删除
    caseLogService.backupAndDelCaseLog(caseIds);
    List<Case> caseList = getCaseListByCaseIds(caseIds);
    if (CollectionUtils.isEmpty(caseList)) {
      return;
    }
    List<CaseHistory> caseHistoryList = new ArrayList<>();
    caseList.forEach(aCase -> {
      CaseHistory caseHistory = AuthBeanUtils.copy(aCase, CaseHistory.class);
      caseHistoryList.add(caseHistory);
    });
    caseHistoryMapper.insertList(caseHistoryList);
    delCase(caseIds);
  }

  @Transactional(rollbackFor = Exception.class)
  public void delJoinDataByCaseIds(List<Long> caseIds) {
    // 删除调解文书待发起状态和待审核，审核拒绝，只保留已发起签署和签署完成的
    letterMediateService.deleteLetterMediateByCaseId(caseIds);
    // 删除人脸认证结果为"未认证"在线视频记录删除
    mediationVideoRoomService.deleteMediationNotAuthRoom(caseIds);
    // 删除创建函件
    letterService.delIsNotCreateLetter(caseIds);
    // 删除待发送函件删除
    letterService.delLetter(caseIds);
    // 策略中心
    // 删除策略监控-执行明细 strategy_exec_case_detail
    strategyExecCaseDetailService.delDetail(caseIds);
    // 删除策略测试明细表
    strategyTestDetailService.delDetail(caseIds);
    // 删除机器人队列-待执行队列/推送失败队列
    robotQueueService.delQueue(caseIds);
    robotQueueFailService.delQueue(caseIds);

    // 删除待审核函件
    letterApplyService.delLetterApply(caseIds);
    // 删除待审核/协催待分配的协催记录
    caseCooperationApplyService.delCaseCooperationApply(caseIds);
    // 删除审核中的还款和划扣记录
    repaymentService.delRepayment(caseIds);
    // 删除待审核的减免记录
    reductionService.delReduction(caseIds);
    // 删除未审核的状态申请记录
    caseApplyService.delCaseApply(caseIds);
    // 删除待审核/已审核待分配的外访审核记录
    visitService.delVisitAudit(caseIds);
    // 删除待外访和外访中的外访记录
    visitService.delVisit(caseIds);
    // 删除待审批的诉讼案件
    lawsuitApplyService.delLawsuitApply(caseIds);
    // 删除案件催收手段
    caseOperationWayRelService.deleteByCaseIds(caseIds);
    // 删除调解审核
    mediateAuditService.deleteMediateAuditByCaseId(caseIds);
    // 删除核销申请/结清证明申请 type 申请类型 0：核销，1：结清
    verifyApplyService.delVerifyApplyByCaseIds(caseIds);
    // 其他申请-未审核、未回复删除
    assistApplyService.delAssistApply(caseIds);
    // 删除待接单、受理中工单，删除工单操作记录、关联信息、附件
    workOrderService.delWorkOrder(caseIds);
    // 删除还款计划
    repayPlanService.delRepayPlan(caseIds);
    // 删除借据
    promissoryNoteService.delPromissoryNote(caseIds);
    // 删除案件标签关联表
    caseTagRelService.delCaseTagRelByCaseIds(caseIds);
    // 删除反欺诈案件
    antiFraudService.delAntiFraud(caseIds);
    // 删除反欺诈案件备注
    antiFraudRemarkService.delRemarkByCaseIds(caseIds);
    // 删除案件小结
    caseNoteService.delNoteByCaseIds(caseIds);
  }

  public void warning(Long caseId, Integer warning) {
    Assert.notNull(warning, "参数不正确");
    Assert.notNull(caseId, "参数不正确");
    UserSession userSession = UserUtils.getTokenUser();
    if (!UserUtils.likeAdmin(userSession.getRoleId())
      && !UserUtils.likeBranchAdmin(userSession.getRoleId())
      && !UserUtils.likeTeamLeader(userSession.getRoleId())) {
      throw new ApiException("只有管理员才能操作");
    }
    Case ca = selectByPrimaryKey(caseId);
    if (ca == null || !Objects.equals(ca.getOrgId(), userSession.getOrgId())) {
      throw new ApiException("找不到对应案件数据");
    }
    Case update = new Case();
    update.setId(caseId);
    update.setWarning(warning);
    update.setUpdateBy(userSession.getId());
    update.setUpdateTime(new Date());
    updateByPrimaryKeySelective(update);
  }

    /**
   * 案件附加导入
   *
   * @param caseAttachImportParam case附加导入参数
   * @param multipartFile         多部件文件
   */
  public void attachImport(CaseAttachImportParam caseAttachImportParam, MultipartFile multipartFile) {

    String fileName = multipartFile.getOriginalFilename();

    byte[] bytes;
    try {
      bytes = multipartFile.getBytes();
    }catch (Exception e){
      throw new ApiException("案件附加导入异常");
    }
    if (ObjectUtil.notEqual(StringUtils.substringAfterLast(fileName, "."), "zip")||!ZipUtil.isZipFile(bytes)){
      throw new ApiException("非zip格式压缩文件");
    }

    UserSession userSession = UserUtils.getTokenUser();
    Date expireDate = DateUtils.addDays(new Date(), 3);
    fileName = StringUtils.substringBeforeLast(fileName, ".")
                    + "_"
                    + AnmiSnowFlowUtil.getIdStr()
                    + "."
                    + StringUtils.substringAfterLast(fileName, ".");
    UploadFileInfo uploadFileInfo = new UploadFileInfo();
    uploadFileInfo.setFile(multipartFile)
            .setFileName(fileName)
            .setExpireDate(expireDate)
            .setBucket(systemConfig.getCaseFilesBucket())
            .setLocalUrl(systemConfig.getCaseAttachPath() + File.separator + fileName);
    FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
    String caseAttachUrl = fileStorageStrategy.uploadFile(uploadFileInfo);
    // 设置异步任务
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    asyncTask.setDepId(userSession.getDepId());
    asyncTask.setTeamId(userSession.getTeamId());
    asyncTask.setType(AsyncTaskEnums.Type.CASE_ATTACH_IMPORT.getCode());
    asyncTask.setStatus(AsyncTaskEnums.Status.ING.getCode());
    asyncTask.setTotal(0L);
    asyncTask.setSuccessAmt(0L);
    asyncTask.setIgnoreCount(0L);
    asyncTask.setFileName(fileName);
    asyncTask.setFileUrl(caseAttachUrl);
    asyncTaskMapper.insertSelective(asyncTask);

    Map<String,String> param = new HashMap<>();
    param.put("orgDeltId",String.valueOf(caseAttachImportParam.getOrgDeltId()));
    String fieldJson = JSON.toJSONString(param);
    asyncTaskMapper.updateFieldJson(asyncTask.getId(), fieldJson);

    // 任务添加到任务列表
    redisUtil.lSetFromLeft(KeyCache.CASE_ATTACH_IMPORT_LIST, String.valueOf(asyncTask.getId()), 24*60*60);
  }

  /**
   * 查询当前催员名下在催案件(不包括停催)
   *
   * @param userIds 催员ids
   * @return
   */
  public List<CaseQueryResult> queryCaseByUserId(String userIds) {
    CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
    caseMultiQuery.setUserIds(userIds);
    caseMultiQuery.setRecovery(0);
    List<Integer> allotStatues = Lists.newArrayList(CaseEnums.AllotStatus.ALLOT_USER.getCode());
    caseMultiQuery.setAllotStatues(StringUtils.join(allotStatues,","));
    List<Integer> notInCaseStatues = Lists.newArrayList(CaseEnums.CaseStatus.STOP.getCode());
    caseMultiQuery.setNotInCaseStatues(StringUtils.join(notInCaseStatues,","));
    return caseMapper.queryResultForMulti(caseMultiQuery);
  }

  /**
   * 因删除用户，重新分配案件到待分配案件
   *
   * @param caseList 需要重新分配的案件
   * @throws InterruptedException
   */
  @Transactional(rollbackFor = Throwable.class)
  public void allotResetForDelUser(List<CaseQueryResult> caseList) throws InterruptedException {
    List<Long> caseIds = caseList.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
    UserSession userSession = UserUtils.getTokenUser();
    // 设置分配维度 一律分配到待分配案件下
    Integer revokerTo = CaseEnums.RevokeTarget.ORG.getCode();
    // 创建任务
    Long taskId =
            asyncTaskService.createAllotResetTask(
                    getTokenUser(),
                    Long.valueOf(caseList.size()),
                    AsyncTaskEnums.Type.ALLOT_RESET.getCode(),
                    revokerTo,
                    AsyncTaskEnums.Status.SUCCESS.getCode());
    // 加锁
    caseIds = protectCaseForDelUser(userSession.getOrgId(), caseIds);
    try {
      if (CollectionUtils.isEmpty(caseIds)) {
        throw new ApiException("该用户下案件发生改变，请稍后执行");
      }
      Map<String, Object> updateParams = assembleCaseResetInfo(revokerTo, caseIds);
      // 待审批协催申请自动拒绝，待协催自动完成
      caseCooperationApplyService.batchRefuseApply(caseIds, userSession, "案件重新分配，需重新申请");
      caseCooperationService.batchFinishCooperation(caseIds, userSession, "案件重新分配，协催自动结束");
      // 更新状态
      caseMapper.updateAllotReset(updateParams);
      // log记录
      allotResetEvent(caseList, updateParams, userSession, taskId);
    } finally {
      removeProtectCasesForDelUser(userSession.getOrgId(), caseIds);
    }
  }

  /**
   * 对因为删除用户而改变状态的案件进行加锁
   *
   * @param orgId 当前公司id
   * @param caseIds 改变状态的案件
   * @return
   * @throws InterruptedException
   */
  public List<Long> protectCaseForDelUser(Long orgId, List<Long> caseIds) throws InterruptedException {
    if (CollectionUtils.isEmpty(caseIds)) {
      return Collections.emptyList();
    }
    String lockKey = KeyCache.CASE_STATUS_LOCK + orgId;
    try {
      if (!redisLock.tryLock(lockKey)){
        log.error("锁超时，key{}", lockKey);
        throw new ApiException("操作过于繁忙，请稍后再试");
      }
      // 取出保护中的案件
      Set<String> idLockList = stringRedisTemplate.opsForSet().members(KeyCache.CASE_PROTECT_EXIST_IDS + orgId);
      if (!CollectionUtils.isEmpty(idLockList)) {
        caseIds = caseIds.stream().filter(c -> !idLockList.contains(String.valueOf(c))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(caseIds)) {
          throw new ApiException("搜索可执行案件为空，请确定该用户下的案件状态");
        }
      }
      List<String> caseIdStrList = caseIds.stream().map(String::valueOf).collect(Collectors.toList());
      // 案件批量加锁，状态保护
      stringRedisTemplate
              .opsForSet()
              .add(KeyCache.CASE_PROTECT_EXIST_IDS + orgId,
                      caseIdStrList.toArray(new String[caseIds.size()]));
      // 防止案件一直被锁
      stringRedisTemplate.expire(KeyCache.CASE_PROTECT_EXIST_IDS + orgId, 2L, TimeUnit.HOURS);
      return caseIds;
    } finally {
      redisLock.unlock(lockKey);
    }
  }

  /**
   * 解除锁定的案件
   *
   * @param orgId 公司id
   * @param caseIds 案件ids
   */
  public void removeProtectCasesForDelUser(Long orgId, List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return;
    }
    // 解除状态保护
    stringRedisTemplate.opsForSet().remove(KeyCache.CASE_PROTECT_EXIST_IDS + orgId,
            caseIds.stream().map(String::valueOf).toArray());
  }

  /**
   * 改变案件表中对应数据协催状态
   * @param caseIds 案件列表
   */
  public void finishCooperationByCaseId(List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return;
    }
    Map<String, Object> conCaseMap = new HashMap<>();
    conCaseMap.put("list", caseIds);
    conCaseMap.put("cooperationStatus", CaseEnums.CooperationStatus.NO.getCode());
    caseMapper.updateBatchByCaseIds(conCaseMap);
  }

  /**
   * 设置留案号
   *
   * @return
   */
  public String addDelayNo(Long depId) {
    AssertUtil.notNull(depId, "depId不可为空");
    DepTeam depTeam = depTeamService.selectByPrimaryKey(depId);
    String delayNo = StringUtils.EMPTY;
    if (StringUtils.isNotBlank(depTeam.getTeamNo())) {
      int delaySeq = depTeam.getDelaySeq();
      delaySeq = delaySeq + 1;
      String delaySeqStr = depTeamService.fillSeqZero(delaySeq);
      delayNo = "L" + depTeam.getTeamNo() + Calendar.getInstance().get(Calendar.YEAR) + delaySeqStr;
      depTeam.setDelaySeq(delaySeq);
      depTeamService.updateDelaySeq(depTeam);
    }
    return delayNo;
  }

  /**
   * 根据呼入手机号查询关联案件
   * @param mobile
   * @return
   */
  public CaseInfoVO getCaseInfoByMobile(String mobile) throws Exception {
    if (encryptProperties.getEnable()) {
      mobile = encryptService.encrypt(mobile);
    }
    // 1.先查询当前手机号是否有催收跟进记录，有的话返回最近跟进的案件即可
    UserSession userSession = getTokenUser();
    CaseInfoVO caseInfoVO = null;
    Long caseId = null;
    Date startTime = DateUtils.addDays(new Date(), -3);
    if (systemConfig.getESSwitch()) {
      CaseOperationParamDto dto = new CaseOperationParamDto();
      //设置分页
      dto.setLimit(1);
      dto.setPage(1);
      //根据创建时间排序，生产环境es催记表id不是数字类型，排序有问题、草！！！草！草！草！
      dto.setSortField("createTime");
      dto.setSortAsc(false);
      dto.setOrgId(userSession.getOrgId());
      dto.setCreateType(new Byte("0"));
      dto.setStartTime(startTime);
      List<String> list = new ArrayList<>();
      list.add(mobile);
      dto.setConMobiles(list);
      //es查找最近催记
      TransferCaseOperationResult res = remoteAlfredService.fetchFromNestedCaseOperationRemote(dto);
      if (Objects.nonNull(res)) {
        caseId = res.getCaseId();
      }
    } else {
      Map<String,Object> map = new HashMap<>();
      map.put("mobile", mobile);
      map.put("orgId", userSession.getOrgId());
      map.put("startTime", startTime);
      // 找最近的一条催记
      caseId = caseOperationMapper.latest72HourOperationCase(map);
    }
    if (Objects.nonNull(caseId)) {
      caseInfoVO = getCaseInfo(caseId);
      getContactRelation(caseInfoVO, mobile);
      return caseInfoVO;
    }
    // 2.找不到最近跟进的案件，就根据手机号联系人关联查询案件，多个案件随机选一个返回即可。
    // 非共债案件
    List<Long> caseIds = contactsMapper.selectRelIdsByContacts(userSession.getOrgId(), Lists.newArrayList(mobile), 0);
    // 共债案件
    List<Long> debtIds = contactsMapper.selectRelIdsByContacts(userSession.getOrgId(), Lists.newArrayList(mobile), 1);
    if (!CollectionUtils.isEmpty(debtIds)) {
      List<Long> conjointCaseIds = caseMapper.selectCaseIdsByDebtIds(userSession.getOrgId(), debtIds);
      caseIds.addAll(conjointCaseIds);
    }
    List<Long> allCaseIds = Lists.newArrayList(Sets.newHashSet(caseIds));
    if (CollectionUtils.isEmpty(allCaseIds)) {
      throw new ApiException("来电主叫号码无匹配案件");
    }
    int size = allCaseIds.size();
    Integer index = RandomUtil.getRandomNumber(0, size-1);
    caseId = allCaseIds.get(index);
    caseInfoVO = getCaseInfo(caseId);
    getContactRelation(caseInfoVO, mobile);
    return caseInfoVO;
  }

  public void getContactRelation(CaseInfoVO caseInfoVO, String mobile) {
    Long relId = caseInfoVO.getDebtId() == null ? caseInfoVO.getId() : caseInfoVO.getDebtId();
    Integer isConjoint = caseInfoVO.getDebtId() == null ? 0 : 1;
    Example contactExp = new Example(Contacts.class);
    Example.Criteria contactCrt = contactExp.createCriteria();
    contactCrt.andEqualTo("relId", relId);
    contactCrt.andEqualTo("isConjoint", isConjoint);
    contactCrt.andEqualTo("mobile", mobile);
    List<Contacts> contacts = contactsService.selectByExample(contactExp);
    if (!CommonUtils.isEmpty(contacts)) {
      Contacts c = contacts.get(0);
      caseInfoVO.setContactsId(c.getId());
      caseInfoVO.setContactName(c.getName());
      caseInfoVO.setRelationType(c.getRelationType());
      if (Objects.equals(c.getOwnSign(), 1)) {
        caseInfoVO.setIsSelf(true);
      } else {
        caseInfoVO.setIsSelf(false);
      }
    }
  }

  public CaseVO getCaseByOutSerialNo(String outSerialNo) {
    UserSession session = UserUtils.getTokenUser();
    Company company = companyService.selectByPrimaryKey(session.getOrgId());
    outSerialNo = outSerialNo.trim() + "#" + company.getDefaultDelt();
    Case query = new Case();
    query.setOrgId(session.getOrgId());
    query.setOutSerialNo(outSerialNo);
    query.setRecovery((byte)CaseEnums.Recovery.NORMAL.getCode());
    Case ca = caseMapper.selectOne(query);
    if (Objects.isNull(ca)) {
      return null;
    }
    CaseVO caseVO = AnmiBeanutils.copy(ca, CaseVO.class);
    if (Objects.nonNull(ca.getOverdueDate())) {
      caseVO.setOverdueDate(ca.getOverdueDate().getTime());
    }
    Product product = productService.selectByPrimaryKey(ca.getProductId());
    caseVO.setProductName(product.getName());
    Map<Long, String> userMap = userService.getNames(session.getOrgId());
    caseVO.setUserName(userMap.get(caseVO.getUserId()));
    Map<Long, String> depTeamMap = depTeamService.getNames();
    caseVO.setTeamName(depTeamMap.get(ca.getTeamId()));
    caseVO.setDepName(depTeamMap.get(ca.getDepId()));
    return caseVO;
  }

  public List<Long> getConjointCaseIds(Long caseId, Long orgId) {
    Case ca = caseMapper.selectByPrimaryKey(caseId);
    Long debtId = ca.getDebtId();
    if (Objects.isNull(debtId)) {
      // 无共债案件
      return new ArrayList<>();
    }
    Example example = new Example(Case.class);
    example.and().andEqualTo("debtId", debtId).andEqualTo("orgId", orgId).andNotEqualTo("id", caseId);
    example.selectProperties("id");
    List<Case> caseList = caseMapper.selectByExample(example);
    return caseList.stream().map(Case::getId).collect(Collectors.toList());
  }


  /**
   * 案件手段流转
   *
   * @param param 参数
   */
  @Transactional(rollbackFor = Exception.class)
  public void switchWay(CaseSwitchWayParam param) throws Exception{
    AssertUtil.notNull(param,"手段流转参数校验不通过");
    AssertUtil.notNull(param.getAllSelect(),"未指明是否全选");
    AssertUtil.notNull(param.getSwitchType(),"未指明流转类型");
    AssertUtil.notEmpty(param.getResultOperationWays(),"未指明手段");

    param.setAllotAgentState(CaseEnums.AllotAgentState.YES.getCode());

    List<Case> canSwitchWayCases = canRunCases(param,param.getAllSelect(),KeyCache.CASE_PROTECT_EXIST_IDS);
    AssertUtil.notEmpty(canSwitchWayCases,"没有符合条件，可以切换手段的案件");

    if (param.getAllSelect()) {
      // 全选走异步逻辑
      switchWayAsync(canSwitchWayCases,param);
      return;
    }
    // 同步
    switchWaySync(canSwitchWayCases,param);
  }

  /**
   * 手段流转(异步)
   *
   * @param canRunCases 可以手段流转的案件
   * @param param 条件
   */
  private void switchWayAsync(List<Case> canRunCases, CaseSwitchWayParam param) {
    String[] caseIds = canRunCases.stream().map(p->String.valueOf(p.getId())).distinct().toArray(String[]::new);
    UserSession userSession = getTokenUser();
    redisUtil.sSet(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(), 24*60*60, caseIds);
    try {
      Long taskId = asyncTaskService.createAsyncTask(param, userSession, (long) caseIds.length, AsyncTaskEnums.Status.ING.getCode(),AsyncTaskEnums.Type.SWITCH_WAY.getCode());
      redisUtil.lSetFromLeft(KeyCache.CASE_WAY_TASK_ID_LIST, taskId.toString(),24*60*60);
      redisUtil.sSet(KeyCache.CASE_WAY_TASK_CASES + taskId, 24*60*60, caseIds);
    } catch (Exception ex) {
      redisUtil.sRemove(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(), caseIds);
      throw ex;
    }
  }

  /**
   * 手段流转(同步)
   *
   * @param canRunCases  可以手段流转的案件
   * @param param 条件
   */
  private void switchWaySync(List<Case> canRunCases, CaseSwitchWayParam param) {
    Set<Long> canSwitchWayCaseIds = canRunCases.stream().map(Case::getId).collect(Collectors.toSet());

    UserSession userSession = getTokenUser();
    Long taskId = asyncTaskService.createAsyncTask(param,userSession,(long)canSwitchWayCaseIds.size(),AsyncTaskEnums.Status.SUCCESS.getCode(),AsyncTaskEnums.Type.SWITCH_WAY.getCode());

    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.SWITCH_WAY.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setCaseIds(new ArrayList<>(canSwitchWayCaseIds));
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);

    caseOperationWayRelService.switchWay(canSwitchWayCaseIds,param.getSwitchType(),param.getResultOperationWays());
  }

  /**
   * 可以执行的案件
   *
   * @param caseMultiQuery  案例多查询
   * @param allSelect       全部选择
   * @param redisProtectKey redis保护密钥
   * @return {@link List}<{@link Case}>
   * @throws Exception 例外
   */
  private List<Case> canRunCases(CaseMultiQuery caseMultiQuery, Boolean allSelect, String redisProtectKey) throws Exception{

    List<Case> caseList;
    UserSession userSession = getTokenUser();
    Set<String> idLockList = redisUtil.sGet(redisProtectKey + userSession.getOrgId());

    caseMultiQuery.setFields(Lists.newArrayList("id"));
    if (!allSelect) {
      AssertUtil.notEmpty(caseMultiQuery.getCaseIds(),"请选择案件");
    } else {
      caseMultiQuery.setCaseIds(null);
    }

    if (caseMultiQuery.getBeAmc()){
      listCaseAmcParamDeal(caseMultiQuery);
    }else {
      listCaseAnmiParamDeal(caseMultiQuery);
    }
    if (systemConfig.getESSwitch()) {
      List<CaseQueryResult> caseQueryResults = getAllCasesUsingEs(caseMultiQuery);
      caseList = BeanUtil.copyPropertiesFromList(caseQueryResults, Case.class);
    } else {
      List<CaseQueryResult> list = queryResultForMulti(caseMultiQuery);
      caseList = BeanUtil.copyPropertiesFromList(list, Case.class);
    }

    // 过滤加锁的案件
    if (ObjectUtil.isNotEmpty(idLockList)) {
      caseList = caseList.stream().filter(c -> !idLockList.contains(c.getId().toString())).collect(Collectors.toList());
    }
    return caseList;
  }

  /**
   * 移入待分配
   *
   * @param param 参数
   */
  @Transactional(rollbackFor = Exception.class)
  public void toWaitAllot(CaseToWaitAllotParam param) throws Exception{
    AssertUtil.notNull(param,"移入待分配参数校验不通过");
    AssertUtil.notNull(param.getAllSelect(),"未指明是否全选");
    AssertUtil.notNull(param.getBeRecycle(),"未指明是否回收案件");
    AssertUtil.notNull(param.getToType(),"未指明移入类型");

    param.setAllotAgentState(CaseEnums.AllotAgentState.YES.getCode());

    List<Case> canRunCases = canRunCases(param,param.getAllSelect(),KeyCache.CASE_PROTECT_EXIST_IDS);

    AssertUtil.notEmpty(canRunCases,"没有符合条件，可以移入待分配的案件");

    if (param.getAllSelect()) {
      // 全选走异步逻辑
      toWaitAllotAsync(canRunCases,param);
      return;
    }
    // 同步
    toWaitAllotSync(canRunCases,param);
  }

  /**
   * 移入待分配(同步)
   *
   * @param canRunCases 可以执行的案件
   * @param param       参数
   */
  private void toWaitAllotSync(List<Case> canRunCases,CaseToWaitAllotParam param) {
    Set<Long> canRunCaseIds = canRunCases.stream().map(Case::getId).collect(Collectors.toSet());

    AsyncTaskEnums.Type asyncTaskType = null;
    CaseLogEnums.Type caseLogType = null;
    if (ObjectUtil.equals(param.getToType(),1)){
      asyncTaskType = AsyncTaskEnums.Type.TO_WAIT_ALLOT;
      caseLogType = CaseLogEnums.Type.TO_WAIT_ALLOT;
    } else if (ObjectUtil.equals(param.getToType(),2)) {
      asyncTaskType = AsyncTaskEnums.Type.TO_WAIT_ALLOT_ELECTRIC;
      caseLogType = CaseLogEnums.Type.TO_WAIT_ALLOT_ELECTRIC;
    } else if (ObjectUtil.equals(param.getToType(),3)) {
      asyncTaskType = AsyncTaskEnums.Type.TO_WAIT_ALLOT_AGENT;
      caseLogType = CaseLogEnums.Type.TO_WAIT_ALLOT_AGENT;
    }

    UserSession userSession = getTokenUser();
    Long taskId = asyncTaskService.createAsyncTask(param,userSession,(long)canRunCaseIds.size(),AsyncTaskEnums.Status.SUCCESS.getCode(),asyncTaskType.getCode());

    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setType(caseLogType.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setCaseIds(new ArrayList<>(canRunCaseIds));
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);

    if (ObjectUtil.equals(param.getToType(),1)){
      doToWaitAllot(canRunCaseIds,param.getBeRecycle());
    } else if (ObjectUtil.equals(param.getToType(),2)) {
      caseMapper.toWaitAllotElectric(canRunCaseIds);
    } else if (ObjectUtil.equals(param.getToType(),3)) {
      caseMapper.toWaitAllotAgent(canRunCaseIds);
    }
  }

  /**
   * 执行 移入待分配
   *
   * @param caseIds   案例ID
   * @param beRecycle 被回收
   */
  @Transactional(rollbackFor = Exception.class)
  public void doToWaitAllot(Collection<Long> caseIds,Boolean beRecycle){
    caseOperationWayRelService.deleteByCaseIds(caseIds);
    caseMapper.toWaitAllot(caseIds,beRecycle);
  }

  /**
   * 移入待分配(异步)
   *
   * @param canRunCases 可以执行的案件
   * @param param       参数
   */
  private void toWaitAllotAsync(List<Case> canRunCases,CaseToWaitAllotParam param) {
    String[] caseIds = canRunCases.stream().map(p->String.valueOf(p.getId())).toArray(String[]::new);
    UserSession userSession = getTokenUser();

    AsyncTaskEnums.Type asyncTaskType = null;
    String protectKey = KeyCache.CASE_PROTECT_EXIST_IDS;
    String taskKey = null;
    String taskCaseKey = null;
    if (ObjectUtil.equals(param.getToType(),1)){
      asyncTaskType = AsyncTaskEnums.Type.TO_WAIT_ALLOT;
      taskKey = KeyCache.CASE_TO_WAIT_ALLOT_TASK_ID_LIST;
      taskCaseKey = KeyCache.CASE_TO_WAIT_ALLOT_TASK_CASES;
    } else if (ObjectUtil.equals(param.getToType(),2)) {
      asyncTaskType = AsyncTaskEnums.Type.TO_WAIT_ALLOT_ELECTRIC;
      taskKey = KeyCache.CASE_TO_WAIT_ALLOT_ELECTRIC_TASK_ID_LIST;
      taskCaseKey = KeyCache.CASE_TO_WAIT_ALLOT_ELECTRIC_TASK_CASES;
    } else if (ObjectUtil.equals(param.getToType(),3)) {
      asyncTaskType = AsyncTaskEnums.Type.TO_WAIT_ALLOT_AGENT;
      taskKey = KeyCache.CASE_TO_WAIT_ALLOT_AGENT_TASK_ID_LIST;
      taskCaseKey = KeyCache.CASE_TO_WAIT_ALLOT_AGENT_TASK_CASES;
    }

    redisUtil.sSet(protectKey + userSession.getOrgId(), 24*60*60, caseIds);
    try {
      Long taskId = asyncTaskService.createAsyncTask(param, userSession, (long) caseIds.length, AsyncTaskEnums.Status.ING.getCode(),asyncTaskType.getCode());
      redisUtil.lSetFromLeft(taskKey, taskId.toString(),24*60*60);
      redisUtil.sSet(taskCaseKey + taskId, 24*60*60, caseIds);
    } catch (Exception ex) {
      redisUtil.sRemove(protectKey + userSession.getOrgId(),caseIds);
      throw ex;
    }
  }

  /**
   * 设置重点案件
   *
   * @param param 参数
   * @throws Exception 例外
   */
  public void setImportant(CaseSetImportantParam param) throws Exception{
    AssertUtil.notNull(param,"设置重点案件参数校验不通过");
    AssertUtil.notNull(param.getAllSelect(),"未指明是否全选");

    param.setImportant(CaseEnums.Important.NO.getCode());

    List<Case> canRunCases = canRunCases(param,param.getAllSelect(),KeyCache.CASE_PROTECT_EXIST_IDS);
    AssertUtil.notEmpty(canRunCases,"没有符合条件，可以设置重点的案件");

    if (param.getAllSelect()) {
      // 全选走异步逻辑
      setImportantAsync(canRunCases,param);
      return;
    }
    // 同步
    setImportantSync(canRunCases,param);
  }

  /**
   * 设置重点案件(异步)
   *
   * @param canRunCases 可以运行案例
   * @param param       参数
   */
  private void setImportantAsync(List<Case> canRunCases, CaseSetImportantParam param) {
    String[] caseIds = canRunCases.stream().map(p->String.valueOf(p.getId())).toArray(String[]::new);
    UserSession userSession = getTokenUser();
    redisUtil.sSet(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(), 24*60*60, caseIds);
    try {
      Long taskId = asyncTaskService.createAsyncTask(param, userSession, (long) caseIds.length, AsyncTaskEnums.Status.ING.getCode(),AsyncTaskEnums.Type.SET_IMPORTANT.getCode());
      redisUtil.lSetFromLeft(KeyCache.CASE_SET_IMPORTANT_TASK_ID_LIST, taskId.toString(),24*60*60);
      redisUtil.sSet(KeyCache.CASE_SET_IMPORTANT_TASK_CASES + taskId, 24*60*60, caseIds);
    } catch (Exception ex) {
      redisUtil.sRemove(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(),caseIds);
      throw ex;
    }
  }

  /**
   * 设置重点案件(同步)
   *
   * @param canRunCases 可以运行案例
   * @param param       参数
   */
  private void setImportantSync(List<Case> canRunCases, CaseSetImportantParam param) {
    Set<Long> canRunCaseIds = canRunCases.stream().map(Case::getId).collect(Collectors.toSet());

    UserSession userSession = getTokenUser();
    Long taskId = asyncTaskService.createAsyncTask(param,userSession,(long)canRunCases.size(),AsyncTaskEnums.Status.SUCCESS.getCode(),AsyncTaskEnums.Type.SET_IMPORTANT.getCode());

    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.SET_IMPORTANT.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setCaseIds(new ArrayList<>(canRunCaseIds));
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);

    Case caseInfo = new Case();
    caseInfo.setImportant(CaseEnums.Important.YES.getCode());
    Example example = new Example(Case.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andIn("id", canRunCaseIds);
    updateByExampleSelective(caseInfo, example);
  }

  /**
   * 移出重点案件
   *
   * @param param 参数
   * @throws Exception 例外
   */
  public void clearImportant(CaseClearImportantParam param) throws Exception{
    AssertUtil.notNull(param,"移出重点案件参数校验不通过");
    AssertUtil.notNull(param.getAllSelect(),"未指明是否全选");

    param.setImportant(CaseEnums.Important.YES.getCode());

    List<Case> canRunCases = canRunCases(param,param.getAllSelect(),KeyCache.CASE_PROTECT_EXIST_IDS);
    AssertUtil.notEmpty(canRunCases,"没有符合条件，可以移出重点的案件");

    if (param.getAllSelect()) {
      // 全选走异步逻辑
      clearImportantAsync(canRunCases,param);
      return;
    }
    // 同步
    clearImportantSync(canRunCases,param);
  }

  /**
   * 移出重点案件(异步)
   *
   * @param canRunCases 可以运行案例
   * @param param       参数
   */
  private void clearImportantAsync(List<Case> canRunCases, CaseClearImportantParam param) {
    String[] caseIds = canRunCases.stream().map(p->String.valueOf(p.getId())).toArray(String[]::new);
    UserSession userSession = getTokenUser();
    redisUtil.sSet(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(), 24*60*60, caseIds);
    try {
      Long taskId = asyncTaskService.createAsyncTask(param, userSession, (long) caseIds.length, AsyncTaskEnums.Status.ING.getCode(),AsyncTaskEnums.Type.CLEAR_IMPORTANT.getCode());
      redisUtil.lSetFromLeft(KeyCache.CASE_CLEAR_IMPORTANT_TASK_ID_LIST, taskId.toString(),24*60*60);
      redisUtil.sSet(KeyCache.CASE_CLEAR_IMPORTANT_TASK_CASES + taskId, 24*60*60, caseIds);
    } catch (Exception ex) {
      redisUtil.sRemove(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(),caseIds);
      throw ex;
    }
  }

  /**
   * 移出重点案件(同步)
   *
   * @param canRunCases 可以运行案例
   * @param param       参数
   */
  private void clearImportantSync(List<Case> canRunCases, CaseClearImportantParam param) {
    Set<Long> canRunCaseIds = canRunCases.stream().map(Case::getId).collect(Collectors.toSet());

    UserSession userSession = getTokenUser();
    Long taskId = asyncTaskService.createAsyncTask(param,userSession,(long)canRunCases.size(),AsyncTaskEnums.Status.SUCCESS.getCode(),AsyncTaskEnums.Type.CLEAR_IMPORTANT.getCode());

    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.CLEAR_IMPORTANT.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setCaseIds(new ArrayList<>(canRunCaseIds));
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);

    Case caseInfo = new Case();
    caseInfo.setImportant(CaseEnums.Important.NO.getCode());
    Example example = new Example(Case.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andIn("id", canRunCaseIds);
    updateByExampleSelective(caseInfo, example);
  }

  /**
   * 回收案件
   *
   * @param param 参数
   * @throws Exception 例外
   */
  public void recycle(CaseRecycleParam param) throws Exception {
    AssertUtil.notNull(param,"回收案件参数校验不通过");
    AssertUtil.notNull(param.getAllSelect(),"未指明是否全选");

    param.setAllotAgentState(CaseEnums.AllotAgentState.YES.getCode());
    param.setRecycleFlag(CaseEnums.RecycleFlag.NO.getCode());

    List<Case> canRunCases = canRunCases(param,param.getAllSelect(),KeyCache.CASE_PROTECT_EXIST_IDS);
    AssertUtil.notEmpty(canRunCases,"没有符合条件，可以回收的案件");

    if (param.getAllSelect()) {
      // 全选走异步逻辑
      recycleCaseAsync(canRunCases,param);
      return;
    }
    // 同步
    recycleCaseSync(canRunCases,param);
  }

  /**
   * 回收案件(异步)
   *
   * @param canRunCases 可以运行案例
   * @param param       参数
   */
  private void recycleCaseAsync(List<Case> canRunCases, CaseRecycleParam param) {
    String[] caseIds = canRunCases.stream().map(p->String.valueOf(p.getId())).toArray(String[]::new);
    UserSession userSession = getTokenUser();
    redisUtil.sSet(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(), 24*60*60, caseIds);
    try {
      Long taskId = asyncTaskService.createAsyncTask(param, userSession, (long) caseIds.length, AsyncTaskEnums.Status.ING.getCode(),AsyncTaskEnums.Type.RECYCLE.getCode());
      redisUtil.lSetFromLeft(KeyCache.CASE_RECYCLE_TASK_ID_LIST, taskId.toString(),24*60*60);
      redisUtil.sSet(KeyCache.CASE_RECYCLE_TASK_CASES + taskId, 24*60*60, caseIds);
    } catch (Exception ex) {
      redisUtil.sRemove(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(),caseIds);
      throw ex;
    }
  }

  /**
   * 回收案件(同步)
   *
   * @param canRunCases 可以运行案例
   * @param param       参数
   */
  private void recycleCaseSync(List<Case> canRunCases, CaseRecycleParam param) {
    Set<Long> canRunCaseIds = canRunCases.stream().map(Case::getId).collect(Collectors.toSet());

    UserSession userSession = getTokenUser();
    Long taskId = asyncTaskService.createAsyncTask(param,userSession,(long)canRunCases.size(),AsyncTaskEnums.Status.SUCCESS.getCode(),AsyncTaskEnums.Type.RECYCLE.getCode());

    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.RECYCLE.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setCaseIds(new ArrayList<>(canRunCaseIds));
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);

    caseMapper.recycleCase(canRunCaseIds);
  }

  /**
   * 手动分案(催收机构类型、催收手段)
   *
   * @param param 参数
   */
  @Transactional(rollbackFor = Exception.class)
  public void manualAllot(CaseManualAllotParam param) throws Exception {
    AssertUtil.notNull(param,"手动分案参数校验不通过");
    AssertUtil.notNull(param.getAllSelect(),"未指明是否全选");
    AssertUtil.notNull(param.getSwitchType(),"未指明流转类型");
    AssertUtil.notEmpty(param.getResultOperationWays(),"未指明手段");

    param.setAllotAgentState(CaseEnums.AllotAgentState.NO.getCode());

    List<Case> canRunCases = canRunCases(param,param.getAllSelect(),KeyCache.CASE_PROTECT_EXIST_IDS);
    AssertUtil.notEmpty(canRunCases,"没有符合条件，可以手动分案的案件");

    if (param.getAllSelect()) {
      // 全选走异步逻辑
      manualAllotAsync(canRunCases,param);
      return;
    }
    // 同步
    manualAllotSync(canRunCases,param);
  }

  /**
   * 手动分案(同步)(催收机构类型、催收手段)
   *
   * @param canRunCases 可以运行案例
   * @param param       参数
   */
  private void manualAllotSync(List<Case> canRunCases, CaseManualAllotParam param) {
    Set<Long> canRunCaseIds = canRunCases.stream().map(Case::getId).collect(Collectors.toSet());

    UserSession userSession = getTokenUser();
    Long taskId = asyncTaskService.createAsyncTask(param,userSession,(long)canRunCases.size(),AsyncTaskEnums.Status.SUCCESS.getCode(),AsyncTaskEnums.Type.MANUAL_ALLOT.getCode());

    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.MANUAL_ALLOT.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setCaseIds(new ArrayList<>(canRunCaseIds));
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);

    caseOperationWayRelService.manualAllot(canRunCaseIds,param.getResultAllotAgent(),param.getSwitchType(), param.getResultOperationWays());
  }

  /**
   * 手动分案(异步)(催收机构类型、催收手段)
   *
   * @param canRunCases 可以运行案例
   * @param param       参数
   */
  private void manualAllotAsync(List<Case> canRunCases, CaseManualAllotParam param) {
    String[] caseIds = canRunCases.stream().map(p->String.valueOf(p.getId())).toArray(String[]::new);
    UserSession userSession = getTokenUser();
    redisUtil.sSet(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(), 24*60*60, caseIds);
    try {
      Long taskId = asyncTaskService.createAsyncTask(param, userSession, (long) caseIds.length, AsyncTaskEnums.Status.ING.getCode(),AsyncTaskEnums.Type.MANUAL_ALLOT.getCode());
      redisUtil.lSetFromLeft(KeyCache.CASE_MANUAL_ALLOT_TASK_ID_LIST, taskId.toString(),24*60*60);
      redisUtil.sSet(KeyCache.CASE_MANUAL_ALLOT_TASK_CASES + taskId, 24*60*60, caseIds);
    } catch (Exception ex) {
      redisUtil.sRemove(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(),caseIds);
      throw ex;
    }
  }

  /**
   * 根据分案所选对象（机构、部门、催员）不同维度统计已分配金额
   * @param ids
   * @param allotObject
   * @param orgId
   * @return
   */
  public List<CaseAmount> getCaseAmount(List<Long> ids, Integer allotObject, Long orgId) {
    CaseMultiQuery query = new CaseMultiQuery();
    query.setOrgId(orgId);
    String idStr = StringUtils.join(ids, ",");
    String dimension = "";
    if (Objects.equals(AllotCaseEnums.Target.USER.getCode(), allotObject)) {
      query.setUserIds(idStr);
      dimension = "user_id";
      List<Integer> allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode());
      query.setAllotStatues(StringUtils.join(allotStatues, ","));
    }
    if (Objects.equals(AllotCaseEnums.Target.TEAM.getCode(), allotObject)) {
      query.setTeamIds(idStr);
      dimension = "team_id";
      List<Integer> allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_TEAM.getCode());
      query.setAllotStatues(StringUtils.join(allotStatues, ","));
    }
    if (Objects.equals(AllotCaseEnums.Target.DEPT.getCode(), allotObject)) {
      query.setDepIds(idStr);
      dimension = "dep_id";
      List<Integer> allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_BRANCH.getCode());
      query.setAllotStatues(StringUtils.join(allotStatues, ","));
    }
    if (Objects.equals(AllotCaseEnums.Target.AGENT.getCode(), allotObject)) {
      query.setDepIds(idStr);
      dimension = "dep_id";
      List<Integer> allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_BRANCH.getCode());
      query.setAllotStatues(StringUtils.join(allotStatues, ","));
    }
    List<CaseAmount> amountList = caseMapper.getCaseAmount(query, dimension);
    List<CaseAmount> result = new ArrayList<>();
    ids.forEach(id -> {
      CaseAmount ca = amountList.parallelStream().filter(caseAmount -> Objects.equals(caseAmount.getSourceId(), id))
              .findFirst().orElse(null);
      if (Objects.nonNull(ca)) {
        ca.setAmount(ca.getAmount().divide(new BigDecimal("1000"), 2, BigDecimal.ROUND_HALF_UP));
      } else {
        ca = new CaseAmount();
        ca.setSourceId(id);
        ca.setAmount(BigDecimal.ZERO);
      }
      result.add(ca);
    });
    return result;
  }

  public List<CaseAmount> getCaseAmount(List<Long> ids, Integer allotObject, Long orgId, String metricField) {
    CaseMultiQuery query = new CaseMultiQuery();
    query.setSelectField("ca.id,ca.amount,ca.user_id,ca.team_id,ca.dep_id,ca.field_json");
    query.setOrgId(orgId);
    String idStr = StringUtils.join(ids, ",");
    if (Objects.equals(AllotCaseEnums.Target.USER.getCode(), allotObject)) {
      query.setUserIds(idStr);
      List<Integer> allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode());
      query.setAllotStatues(StringUtils.join(allotStatues, ","));
    }
    if (Objects.equals(AllotCaseEnums.Target.TEAM.getCode(), allotObject)) {
      query.setTeamIds(idStr);
      List<Integer> allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_TEAM.getCode());
      query.setAllotStatues(StringUtils.join(allotStatues, ","));
    }
    if (Objects.equals(AllotCaseEnums.Target.DEPT.getCode(), allotObject)) {
      query.setDepIds(idStr);
      List<Integer> allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_BRANCH.getCode());
      query.setAllotStatues(StringUtils.join(allotStatues, ","));
    }
    if (Objects.equals(AllotCaseEnums.Target.AGENT.getCode(), allotObject)) {
      query.setDepIds(idStr);
      List<Integer> allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_BRANCH.getCode());
      query.setAllotStatues(StringUtils.join(allotStatues, ","));
    }
    List<CaseQueryResult> caseQueryResults = caseMapper.queryResultForMulti(query);
    caseQueryResults.forEach(caseQueryResult -> {
      BigDecimal metric = getMetricFieldValue(metricField, caseQueryResult);
      caseQueryResult.setMetric(metric);
    });
    Map<Long, BigDecimal> map = new HashMap<>();
    if (Objects.equals(AllotCaseEnums.Target.USER.getCode(), allotObject)) {
      map = caseQueryResults.parallelStream().filter(caseQueryResult -> Objects.nonNull(caseQueryResult.getMetric()))
              .collect(Collectors.groupingBy(Case::getUserId, Collectors.mapping(CaseQueryResult::getMetric,
                      Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }
    if (Objects.equals(AllotCaseEnums.Target.TEAM.getCode(), allotObject)) {
      map = caseQueryResults.parallelStream().filter(caseQueryResult -> Objects.nonNull(caseQueryResult.getMetric()))
              .collect(Collectors.groupingBy(Case::getTeamId, Collectors.mapping(CaseQueryResult::getMetric,
                      Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }
    if (Objects.equals(AllotCaseEnums.Target.DEPT.getCode(), allotObject)) {
      map = caseQueryResults.parallelStream().filter(caseQueryResult -> Objects.nonNull(caseQueryResult.getMetric()))
              .collect(Collectors.groupingBy(Case::getDepId, Collectors.mapping(CaseQueryResult::getMetric,
                      Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }
    if (Objects.equals(AllotCaseEnums.Target.AGENT.getCode(), allotObject)) {
      map = caseQueryResults.parallelStream().filter(caseQueryResult -> Objects.nonNull(caseQueryResult.getMetric()))
              .collect(Collectors.groupingBy(Case::getDepId, Collectors.mapping(CaseQueryResult::getMetric,
                      Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
    }
    List<CaseAmount> result = new ArrayList<>();
    for (Long id : ids) {
      BigDecimal metric = map.get(id);
      if (Objects.isNull(metric)) {
        metric = BigDecimal.ZERO;
      }
      CaseAmount ca = new CaseAmount();
      ca.setSourceId(id);
      ca.setAmount(metric);
      result.add(ca);
    }
    return result;
  }

  /**
   * 根据分案算法金额字段获取值，系统字段只有贷款金额amount几个字段，其他的类似本金字段一律存储在json字段里
   * 这里系统字段直接枚举取值，没有用反射尽量提高性能
   * 这里捕获异常，因为配置的字段取值可能为空或者非数字什么的转bigDecimal失败
   * @param metricField 为空等于贷款金额amount
   * @param ca
   * @return
   */
  public BigDecimal getMetricFieldValue(String metricField, Case ca) {
    try {
      if (Objects.equals(metricField, "amount") || StringUtils.isBlank(metricField)) {
        return BigDecimal.valueOf(ca.getAmount());
      }
      if (Objects.equals(metricField, "outsourceCount")) {
        return BigDecimal.valueOf(ca.getOutsourceCount());
      }
      if (Objects.equals(metricField, "overdueDays")) {
        return BigDecimal.valueOf(ca.getOverdueDays());
      }
      if (Objects.equals(metricField, "debtFollowCount")) {
        return BigDecimal.valueOf(ca.getFollowCount());
      }
      // 根据字段在fieldJson中查找
      Map<String, String> fieldJson = ca.getFieldJson();
      String value = fieldJson.get(metricField);
      return new BigDecimal(value);
    } catch (Exception e) {
      // 这里就输出错误日志了，因为案子很多转换失败日志会很多；
    }
    // 返回null该案子不参与分案，因为分案算法指定字段取值指标无效。
    return null;
  }

  private void decryptDataFromES(CaseQueryResult caseQueryResult) {
    if (!encryptProperties.getEnable()) {
      return;
    }
    caseQueryResult.setOwnMobile(encryptService.decrypt(caseQueryResult.getOwnMobile()));
    caseQueryResult.setName(encryptService.decrypt(caseQueryResult.getName()));
    caseQueryResult.setIdCard(encryptService.decrypt(caseQueryResult.getIdCard()));
  }

  public void encryptQueryData(CaseMultiQuery caseMultiQuery) {
    if (!encryptProperties.getEnable()) {
      return;
    }
    List<String> idCards = caseMultiQuery.getIdCards();
    if (!com.anmi.collection.utils.CollectionUtils.isEmpty(idCards)) {
      caseMultiQuery.setIdCards(idCards.stream().map(s->encryptService.encrypt(s)).collect(Collectors.toList()));
    }
    List<String> names = caseMultiQuery.getNames();
    if (!com.anmi.collection.utils.CollectionUtils.isEmpty(names)) {
      caseMultiQuery.setNames(names.stream().map(s->encryptService.encrypt(s)).collect(Collectors.toList()));
    }
    List<String> mobiles = caseMultiQuery.getMobiles();
    if (!com.anmi.collection.utils.CollectionUtils.isEmpty(mobiles)) {
      caseMultiQuery.setMobiles(mobiles.stream().map(s->encryptService.encrypt(s)).collect(Collectors.toList()));
    }
    List<String> contactMobiles = caseMultiQuery.getContactMobiles();
    if (!com.anmi.collection.utils.CollectionUtils.isEmpty(contactMobiles)) {
      caseMultiQuery.setContactMobiles(contactMobiles.stream().map(s->encryptService.encrypt(s)).collect(Collectors.toList()));
    }
  }


  public void checkPlanOnStatus(CaseMultiQuery query) {
    if (query.getState() == null) {
      return; // 代表没有进行状态筛选
    }
    Integer state = query.getState();
    if (state == 3 || state == 4 || state == 8 || state == 5) {
      throw new ApiException("创建案件智能协催计划的状态不合法！");
    }
  }
}

