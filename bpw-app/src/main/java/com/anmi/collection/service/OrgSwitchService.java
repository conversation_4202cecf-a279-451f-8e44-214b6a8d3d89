package com.anmi.collection.service;

import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.entity.requset.sys.org.OrgSwitchParam;
import com.anmi.collection.mapper.OrgSwitchMapper;
import com.anmi.domain.cases.OrgSwitch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class OrgSwitchService extends BaseService<OrgSwitch> {
    @Autowired
    private OrgSwitchMapper orgSwitchMapper;

    public OrgSwitch selectOrgSwitchByOrgId(Long orgId){
        return orgSwitchMapper.selectOrgSwitchByOrgId(orgId);
    }

    /**
     * 保存或更新
     *
     * @param orgId      组织id
     * @param orgEnables org使
     * @return {@link Boolean}
     */
    public Boolean saveOrUpdate(Long orgId, Map<String, String> orgEnables) {
        OrgSwitch switchSaveOrUpdate = new OrgSwitch();
        for (String fieldName : orgEnables.keySet()) {
            String fieldValue = orgEnables.get(fieldName);
            switch (fieldName) {
                case "connect_duyan":
                    switchSaveOrUpdate.setConnectDuyan(fieldValue);
                    break;
                case "hidden_phone":
                    switchSaveOrUpdate.setHiddenPhone(fieldValue);
                    break;
                case "hidden_id_card":
                    switchSaveOrUpdate.setHiddenIdCard(fieldValue);
                    break;
                case "hidden_bank_code":
                    switchSaveOrUpdate.setHiddenBankCode(fieldValue);
                    break;
                case "auto_update_overdue_days":
                    switchSaveOrUpdate.setAutoUpdateOverdueDays(fieldValue);
                    break;
                case "desensitization_rule":
                    switchSaveOrUpdate.setDesensitizationRule(fieldValue);
                    break;
                case "conjoint_type":
                    switchSaveOrUpdate.setConjointType(fieldValue);
                    break;
                case "ip_access_agent":
                    switchSaveOrUpdate.setIpAccessAgent(fieldValue);
                    break;
                case "ip_access_admin":
                    switchSaveOrUpdate.setIpAccessAdmin(fieldValue);
                    break;
                case "own_phone_auto_switch":
                    switchSaveOrUpdate.setOwnPhoneAutoSwitch(fieldValue);
                    break;
                default:
                    break;
            }
        }

        OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(orgId);

        if (ObjectUtil.isNull(orgSwitch)){
            switchSaveOrUpdate.setOrgId(orgId);
            orgSwitchMapper.insertSelective(switchSaveOrUpdate);
            return true;
        }
        switchSaveOrUpdate.setId(orgSwitch.getId());
        orgSwitchMapper.updateByPrimaryKeySelective(switchSaveOrUpdate);
        return true;
    }

    /**
     * 按组织id查询开关
     *
     * @param orgIds 组织ID
     * @return {@link List}<{@link OrgSwitch}>
     */
    public List<OrgSwitch> selectOrgSwitchByOrgIds(Set<Long> orgIds) {
        Example example = new Example(OrgSwitch.class);
        example.createCriteria().andIn("id", orgIds);
        return orgSwitchMapper.selectByExample(example);
    }

    public List<OrgSwitch> selectOrgSwitchAll() {
        Example example = new Example(OrgSwitch.class);
        example.createCriteria().andIsNotNull("id");
        return orgSwitchMapper.selectByExample(example);
    }

    /**
     * 设置ai分析开关
     *
     * @param param 开关信息
     */
    public void setAiAnalysisSwitch(OrgSwitchParam param) {
        Integer aiAnalysisSwitch = param.getAiAnalysisSwitch();
        OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(param.getOrgId());
        Assert.notNull(orgSwitch, "找不到系统开关");
        Assert.notNull(aiAnalysisSwitch, "开关不能为空");
        OrgSwitch updateSwitch = new OrgSwitch();
        updateSwitch.setId(orgSwitch.getId());
        updateSwitch.setAiAnalysisSwitch(aiAnalysisSwitch);
        updateSwitch.setUpdateTime(new Date());
        orgSwitchMapper.updateByPrimaryKeySelective(updateSwitch);
    }

    /**
     * 是否保存机器人下催记的短信
     *
     * @param param 开关信息
     */
    public void setSaveRobotSmsSaveSwitch(OrgSwitchParam param) {
        Integer robotSmsSaveSwitch = param.getRobotSmsSaveSwitch();
        OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(param.getOrgId());
        Assert.notNull(orgSwitch, "找不到系统开关");
        Assert.notNull(robotSmsSaveSwitch, "开关不能为空");
        OrgSwitch updateSwitch = new OrgSwitch();
        updateSwitch.setId(orgSwitch.getId());
        updateSwitch.setRobotSmsSaveSwitch(robotSmsSaveSwitch);
        updateSwitch.setUpdateTime(new Date());
        orgSwitchMapper.updateByPrimaryKeySelective(updateSwitch);
    }

    /**
     * 机器人回调pageNode保存开关 0关 1开
     *
     * @param param 开关信息
     */
    public void setSaveRobotPageNodesSaveSwitch(OrgSwitchParam param) {
        Integer robotPageNodesSaveSwitch = param.getRobotPageNodesSaveSwitch();
        OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(param.getOrgId());
        Assert.notNull(orgSwitch, "找不到系统开关");
        Assert.notNull(robotPageNodesSaveSwitch, "开关不能为空");
        OrgSwitch updateSwitch = new OrgSwitch();
        updateSwitch.setId(orgSwitch.getId());
        updateSwitch.setRobotPageNodesSaveSwitch(robotPageNodesSaveSwitch);
        updateSwitch.setUpdateTime(new Date());
        orgSwitchMapper.updateByPrimaryKeySelective(updateSwitch);
    }
}
