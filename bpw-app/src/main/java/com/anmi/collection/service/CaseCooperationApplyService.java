package com.anmi.collection.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.smallbun.screw.core.util.Assert;
import com.alibaba.fastjson.JSON;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.CaseCooperationApplyEnums;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.DepTeamEnums;
import com.anmi.collection.common.enums.OrgSwitchEnums;
import com.anmi.collection.common.enums.flow.ApplyEnums;
import com.anmi.collection.common.enums.flow.FlowEnums;
import com.anmi.collection.common.enums.flow.FlowHandleRecordEnums;
import com.anmi.collection.entity.requset.cases.*;
import com.anmi.collection.entity.requset.flow.ApproveListParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.CooperationApplyListVO;
import com.anmi.collection.entity.response.cases.CooperationApplyStatisticsVO;
import com.anmi.collection.entity.response.cases.CooperationAwaitListVO;
import com.anmi.collection.entity.response.flow.ApproveListVO;
import com.anmi.collection.entity.response.flow.FlowNodeVO;
import com.anmi.collection.entity.response.flow.FlowVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.CaseCooperationApplyMapper;
import com.anmi.collection.mapper.OrgSwitchMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.redis.RedisLock;
import com.anmi.collection.service.flow.FlowService;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseCooperationApply;
import com.anmi.domain.cases.CaseQueryResult;
import com.anmi.domain.cases.OrgSwitch;
import com.anmi.domain.user.DepTeam;
import com.anmi.domain.user.User;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CaseCooperationApplyService extends BaseService<CaseCooperationApply> {
  @Resource private CaseCooperationApplyMapper caseCooperationApplyMapper;
  @Resource private CaseCooperationService caseCooperationService;
  @Resource private UserService userService;
  @Resource private CaseService caseService;
  @Resource private OutBatchService outBatchService;
  @Resource private ProductService productService;
  @Resource private DeltService deltService;
  @Resource private CompanyService companyService;
  @Resource private DepTeamService depTeamService;
  @Resource private RedisLock redisLock;
  @Resource private OrgSwitchMapper orgSwitchMapper;
  @Resource private FlowService flowService;

  /**
   * 协催申请
   *
   * @param param 参数
   * @return {@link Map}<{@link String}, {@link Object}>
   */
  @Transactional(rollbackFor = Exception.class)
  public Map<String, Object> applyAdd(CaseCooperationApplyAddParam param){
    List<Long> caseIdList = param.getCaseIdList();
    Byte type = param.getType();
    Long cooperatorId = param.getCooperatorId();
    if (CollectionUtils.isEmpty(caseIdList) || type == null) {
      throw new ApiException("参数错误");
    }
    UserSession userSession = UserUtils.getTokenUser();
    if (CaseCooperationApplyEnums.Type.SELECTED_DUNNER.getCode().equals(type)) {
      if (cooperatorId == null) {
        throw new ApiException("协催员不能为空");
      }
      if (cooperatorId.equals(userSession.getId())) {
        throw new ApiException("协催员不能是本人");
      }
    } else {
      param.setCooperatorId(null);
    }

    List<FlowVO> flowVOS = flowService.flowChoice(userSession.getOrgId(), FlowEnums.BusinessType.XC);

    Integer successNum = 0;
    for (Long caseId : caseIdList) {
      try {
        createApply(caseId, param, userSession, flowVOS);
        successNum++;
      } catch (Exception e) {
        log.error("申请协催失败,caseId{}", caseId, e);
      }
    }
    Map<String, Object> map = new HashMap<>();
    map.put("successNum", successNum);
    map.put("failNum", caseIdList.size() - successNum);
    return map;
  }

  /**
   * 创建协催申请
   *
   * @param caseId      案例id
   * @param param       参数
   * @param userSession 用户会话
   * @param flowVOS     流量vos
   * @throws Exception 例外
   */
  private void createApply(Long caseId, CaseCooperationApplyAddParam param, UserSession userSession, List<FlowVO> flowVOS) throws Exception{
    Case caseInfo = caseService.selectByPrimaryKey(caseId);
    if (!caseService.isCaseOngoing(caseInfo)) {
      throw new ApiException("案件状态不能发起申请");
    }
    if (!caseInfo.getUserId().equals(userSession.getId())) {
      //判断公司是否开放团队催收
      OrgSwitch orgSwitch = this.orgSwitchMapper.selectOrgSwitchByOrgId(userSession.getOrgId());
      Assert.notNull(orgSwitch, "找不到OrgSwitch");
      if (!OrgSwitchEnums.TeamUrgeSwitch.OPEN.getCode().equals(orgSwitch.getTeamUrgeSwitch())
          || orgSwitch.getTeamUrgeIds() == null
          || userSession.getTeamId() == null
          || !userSession.getTeamId().equals(caseInfo.getTeamId())
          || !JSON.parseArray(orgSwitch.getTeamUrgeIds()).toJavaList(Long.class).contains(userSession.getTeamId())) {
        throw new ApiException("非案件负责人不能发起协催");
      }
    }
    if (isApplyExist(caseId)
        || caseInfo.getCooperationStatus().equals(CaseEnums.CooperationStatus.YES.getCode())) {
      throw new ApiException("案件协催中，无法申请");
    }

    FlowEnums.AgentType agentType = ObjectUtil.equals(caseInfo.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?FlowEnums.AgentType.OUTER:FlowEnums.AgentType.INNER;
    // 获取审批流
    Optional<FlowVO> first = flowVOS.stream().filter(p -> ObjectUtil.equals(agentType.getCode(), p.getAgentType())).findFirst();
    AssertUtil.isTrue(first.isPresent(),"没有匹配的审批流配置");
    FlowVO flowVO = first.get();
    // 申请是否自动通过
    Boolean autoPassApply = flowService.autoPassApply(userSession,flowVO);

    Long cooperatorId = param.getCooperatorId();

    CaseCooperationApply caseCooperationApply = cn.hutool.core.bean.BeanUtil.copyProperties(param,CaseCooperationApply.class);
    caseCooperationApply.setCaseId(caseId);
    caseCooperationApply.setState(CaseCooperationApplyEnums.State.ING.getCode());
    caseCooperationApply.setCooperatorName(
        cooperatorId == null ? null : userService.getNames(userSession.getOrgId()).get(cooperatorId));
    caseCooperationApply.setApplyBy(userSession.getId());
    caseCooperationApply.setApplyName(userService.getNames(userSession.getOrgId()).get(userSession.getId()));
    caseCooperationApply.setApplyTime(new Date());
    caseCooperationApply.setDunnerDepId(caseInfo.getDepId());
    caseCooperationApply.setDunnerTeamId(caseInfo.getTeamId());
    caseCooperationApply.setAllotAgent(caseInfo.getAllotAgent());
    caseCooperationApply.setOrgDeltId(caseInfo.getOrgDeltId());
    caseCooperationApply.setOrgId(caseInfo.getOrgId());
    caseCooperationApply.setOutBatchId(caseInfo.getOutBatchId());
    caseCooperationApply.setOutSerialTemp(caseInfo.getOutSerialTemp());
    caseCooperationApply.setProductId(caseInfo.getProductId());
    caseCooperationApply.setCreateBy(userSession.getId());
    caseCooperationApply.setCreateTime(new Date());
    caseCooperationApply.setUpdateBy(userSession.getId());
    caseCooperationApply.setUpdateTime(new Date());

    caseCooperationApply.setAllot(CaseCooperationApplyEnums.Allot.NO.getCode());
    caseCooperationApply.setAllotAgent(ObjectUtil.equals(caseInfo.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?CaseEnums.AllotAgent.WW.getCode():CaseEnums.AllotAgent.NC.getCode());
    if (ObjectUtil.equals(param.getType(),CaseCooperationApplyEnums.Type.ADMIN_AUDIT.getCode())){
      caseCooperationApply.setFlowId(ObjectUtil.isNull(flowVO)?null:flowVO.getId());
      if (autoPassApply){
        caseCooperationApply.setState(CaseCooperationApplyEnums.State.PASS.getCode());
        caseCooperationApply.setAuditTime(new Date());
        caseCooperationApply.setAuditDesc("自动通过");
      }
      Integer timeLimitSwitch = flowVO.getTimeLimitSwitch();
      if (ObjectUtil.equals(timeLimitSwitch, FlowEnums.TimeLimitSwitch.YES.getCode())){
        String timeLimit = flowVO.getTimeLimit();
        String hour = StrUtil.subBefore(timeLimit, ":", true);
        String minute = StrUtil.subAfter(timeLimit, ":", true);
        Date outTime = DateUtil.offsetMinute(DateUtil.offsetHour(new Date(),Integer.valueOf(hour)),Integer.valueOf(minute));
        caseCooperationApply.setOutTime(outTime);
      }
    }
    caseCooperationApplyMapper.insertSelective(caseCooperationApply);
    if (ObjectUtil.equals(param.getType(),CaseCooperationApplyEnums.Type.ADMIN_AUDIT.getCode())){
      List<FlowNodeVO> nodes = flowVO.getNodes();
      Map<String,Object> applyMap = cn.hutool.core.bean.BeanUtil.beanToMap(caseCooperationApply);
      String parentUniqueCode = flowService.getApplyOrRuleNodeUniqueCode(nodes,applyMap,FlowEnums.BusinessType.XC,agentType,userSession);
      if (autoPassApply){
        flowService.findNextCopyNodeAndExecute(parentUniqueCode,flowVO, caseCooperationApply.getId(),userSession);
      } else {
        flowService.findNextNodeAndExecute(parentUniqueCode,flowVO, caseCooperationApply.getId(),userSession, userSession.getId());
      }
    }
  }

  /**
   * 案件是否协催中
   *
   * @param caseId 案例id
   * @return {@link Boolean}
   */
  private Boolean isApplyExist(Long caseId) {
    Integer count =
        caseCooperationApplyMapper.selectCountByCaseId(
            caseId, CaseCooperationApplyEnums.State.ING.getCode());
    if (count > 0) {
      return true;
    }
    return false;
  }

  public void applyHandle(CaseCooperationApplyHandleParam param) throws Exception{

    UserSession userSession = UserUtils.getTokenUser();

    Long applyId = param.getId();
    AssertUtil.notNull(applyId,"请选择申请");

    Byte state = param.getState();
    if (param.getId() == null
            || state == null
            || (state != CaseCooperationApplyEnums.State.PASS.getCode()
            && state != CaseCooperationApplyEnums.State.REFUSE.getCode()
            && state != CaseCooperationApplyEnums.State.CANCEL.getCode())) {
      throw new ApiException("参数错误");
    }

    CaseCooperationApply applyRecord = selectByPrimaryKey(applyId);
    AssertUtil.notNull(applyRecord, "未发现申请");
    AssertUtil.isTrue(ObjectUtil.equals(applyRecord.getState(), CaseCooperationApplyEnums.State.ING.getCode()), "申请非审批中");

    if (ObjectUtil.equals(param.getState(),CaseCooperationApplyEnums.State.CANCEL.getCode())&&ObjectUtil.equals(applyRecord.getType(),CaseCooperationApplyEnums.Type.ADMIN_AUDIT.getCode())){
      AssertUtil.notNull(applyRecord.getFlowId(), "审批流异常");
      Long flowId = applyRecord.getFlowId();
      flowService.revokeCheck(applyId, flowId);
      CaseCooperationApply caseCooperationApply = new CaseCooperationApply();

      caseCooperationApply.setId(applyId);
      caseCooperationApply.setState(CaseCooperationApplyEnums.State.CANCEL.getCode());
      caseCooperationApply.setUpdateBy(userSession.getId());
      caseCooperationApply.setUpdateTime(new Date());
      updateByPrimaryKeySelective(caseCooperationApply);
      return;
    }

    String auditDesc = param.getAuditDesc();
    if (param.getId() == null
            || state == null
            || (state != CaseCooperationApplyEnums.State.PASS.getCode()
            && state != CaseCooperationApplyEnums.State.REFUSE.getCode()
            && state != CaseCooperationApplyEnums.State.CANCEL.getCode())) {
      throw new ApiException("参数错误");
    }
    // 加锁保护申请
    String lockKey = KeyCache.COOPERATION_APPLY_LOCK + param.getId();
    if (!redisLock.tryLock(lockKey)) {
      log.error("锁超时,key:{}", lockKey);
      throw new ApiException("操作过于繁忙，请稍后再试");
    }
    try {
      User cooperator = null;
      if (CaseCooperationApplyEnums.State.PASS.getCode().equals(state)) {
        cooperator = userService.selectByPrimaryKey(userSession.getId());
      }
      caseCooperationService.applyChangeStatus(param.getId(), state, auditDesc, cooperator, userSession, false);
    } finally {
      redisLock.unlock(lockKey);
    }
  }

  /**
   * 协催申请(审批)
   *
   * @param param 参数
   * @return {@link Map}<{@link String}, {@link Object}>
   * @throws Exception 例外
   */
  @Transactional(rollbackFor = Exception.class)
  public Map<String, Object> applyAudit(CaseCooperationApplyAuditParam param) throws Exception {
    List<Long> applyIds = param.getIdList();
    AssertUtil.notEmpty(applyIds,"请选择申请");
    AssertUtil.isTrue(applyIds.size()==1,"暂不支持批量审批");
    AssertUtil.isTrue(ObjectUtil.equals(param.getState(),CaseCooperationApplyEnums.State.PASS.getCode())||ObjectUtil.equals(param.getState(),CaseCooperationApplyEnums.State.REFUSE.getCode()),"审批状态错误");

    Long applyId = applyIds.get(0);
    CaseCooperationApply applyRecord = selectByPrimaryKey(applyId);
    AssertUtil.notNull(applyRecord, "未发现申请");
    AssertUtil.isTrue(ObjectUtil.equals(applyRecord.getState(), CaseCooperationApplyEnums.State.ING.getCode()), "申请非审批中");
    AssertUtil.notNull(applyRecord.getFlowId(), "审批流异常");

    UserSession userSession = UserUtils.getTokenUser();

    if (ObjectUtil.equals(applyRecord.getType(),CaseCooperationApplyEnums.Type.SELECTED_DUNNER.getCode()) && ObjectUtil.notEqual(userSession.getId(),applyRecord.getCooperatorId())) {
      throw new ApiException("非指定协催员不可接受申请");
    }
    if (ObjectUtil.notEqual(applyRecord.getType(),CaseCooperationApplyEnums.Type.ADMIN_AUDIT.getCode())) {
      throw new ApiException("非管理员审核申请");
    }

    Integer successNum = 0;
    // 加锁保护申请
    String lockKey = KeyCache.COOPERATION_APPLY_LOCK + applyId;
    if (!redisLock.tryLock(lockKey)) {
      log.error("锁超时,key:{}", lockKey);
    }

    try {
      Long flowId = applyRecord.getFlowId();
      Long applyUserId = applyRecord.getApplyBy();
      FlowHandleRecordEnums.HandleStatus approveStatus = ObjectUtil.equals(param.getState(),CaseCooperationApplyEnums.State.PASS.getCode())?FlowHandleRecordEnums.HandleStatus.PASS:FlowHandleRecordEnums.HandleStatus.REFUSE;
      flowService.execApprove(applyId,flowId,approveStatus,param.getAuditDesc(),userSession,applyUserId);
      successNum++;
    } catch (Exception e) {
      log.error("协催申请审批失败,applyId:{}", applyId, e);
    } finally {
      redisLock.unlock(lockKey);
    }
    Map<String, Object> map = new HashMap<>();
    map.put("successNum", successNum);
    map.put("failNum", applyIds.size() - successNum);
    return map;
  }

  /**
   * 更新申请 处理状态
   *
   * @param applyId       申请id
   * @param approveStatus 审批状态
   * @param approveUserId 审批人
   */
  public void updateHandleStatus(Long applyId, FlowHandleRecordEnums.HandleStatus approveStatus, Long approveUserId) {
    Byte status = ObjectUtil.equals(approveStatus,FlowHandleRecordEnums.HandleStatus.PASS)?CaseCooperationApplyEnums.State.PASS.getCode():CaseCooperationApplyEnums.State.REFUSE.getCode();
    CaseCooperationApply applyRecord = new CaseCooperationApply();
    applyRecord.setId(applyId);
    applyRecord.setState(status);
    applyRecord.setUpdateBy(approveUserId);
    applyRecord.setUpdateTime(new Date());
    applyRecord.setAuditTime(new Date());
    applyRecord.setAuditBy(approveUserId);
    updateByPrimaryKeySelective(applyRecord);
  }

  public PageOutput cooperationApplyList(CaseCooperationApplyListParam param) throws Exception {
    UserSession userSession = userService.getTokenUser();
    if (param.getApplyTime() != null && param.getApplyTime().split(",").length == 2) {
      param.setApplyTimeStart(DateUtils.convertDate(param.getApplyTime().split(",")[0]));
      param.setApplyTimeEnd(DateUtils.convertDate(param.getApplyTime().split(",")[1]));
    }
    if (param.getAuditTime() != null && param.getAuditTime().split(",").length == 2) {
      param.setAuditTimeStart(DateUtils.convertDate(param.getAuditTime().split(",")[0]));
      param.setAuditTimeEnd(DateUtils.convertDate(param.getAuditTime().split(",")[1]));
    }

    if (UserUtils.likeAdmin()) {
      param.setOrgId(userSession.getOrgId());
    } else if (UserUtils.likeBranchAdmin()) {
      param.setOrgId(userSession.getOrgId());
      param.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader()) {
      param.setOrgId(userSession.getOrgId());
      param.setTeamId(userSession.getTeamId());
    } else {
      throw new ApiException("角色错误");
    }

    // 分页
    PageParam pageParam = new PageParam();
    pageParam.setPage(param.getPage());
    pageParam.setLimit(param.getLimit());
    Page page = PageUtils.setPage(pageParam);

    List<CaseCooperationApply> list = caseCooperationApplyMapper.selectCooperationApplyList(param);

    List<CooperationApplyListVO> cooperationApplyList = new ArrayList<>();
    Map<Long,String> depTeamMap = depTeamService.getNames();
    Map<Long,String> deltMap = deltService.getNames();
    Map<Long,String> outBatchMap = outBatchService.getNames();
    Map<Long,String> productMap = productService.getNames();
    Map<Long,DepTeam> depMap = new HashMap<>();
    Map<Long,Case> caseMap = new HashMap<>();
    List<Long> depIds = list.stream().map(CaseCooperationApply::getDunnerDepId).collect(Collectors.toList());
    List<DepTeam> depList = depTeamService.selectByIdList(depIds);
    if (!CollectionUtils.isEmpty(depList)) {
      depMap = depList.stream().collect(Collectors.toMap(DepTeam::getId, Function.identity()));
    }
    List<Long> caseIds = list.stream().map(CaseCooperationApply::getCaseId).collect(Collectors.toList());
    List<Case> caseList = caseService.selectByIdList(caseIds);
    if (!CollectionUtils.isEmpty(caseList)) {
      caseMap = caseList.stream().collect(Collectors.toMap(Case::getId,Function.identity()));
    }

    for (CaseCooperationApply apply : list) {
      CooperationApplyListVO applyVO = BeanUtil.copyProperties(apply, CooperationApplyListVO.class);

      applyVO.setState(apply.getState().intValue());
      applyVO.setType(apply.getType().intValue());
      // 所属公司：分公司没有用总公司
      if (null == apply.getDunnerDepId()) {
        applyVO.setDunnerCompany(companyService.getNames().get(apply.getOrgId()));
      } else {
        applyVO.setDunnerCompany(depTeamService.getNames().get(apply.getDunnerDepId()));
      }
      applyVO.setDunnerTeam(depTeamMap.get(apply.getDunnerTeamId()));
      applyVO.setOrgDeltName(deltMap.get(apply.getOrgDeltId()));
      applyVO.setOutBatchNo(outBatchMap.get(apply.getOutBatchId()));
      applyVO.setProductName(productMap.get(apply.getProductId()));

      // 案件负责人，申请表中申请人为案件负责人
      applyVO.setDunnerName(apply.getApplyName());

      applyVO.setApplyTime(apply.getApplyTime() == null ? null : apply.getApplyTime().getTime());
      applyVO.setAuditTime(apply.getAuditTime() == null ? null : apply.getAuditTime().getTime());

      applyVO.setAmount(caseMap.get(apply.getCaseId()) == null ? null : caseMap.get(apply.getCaseId()).getAmount());
      applyVO.setOwnMobile(caseMap.get(apply.getCaseId()) == null ? null : caseMap.get(apply.getCaseId()).getOwnMobile());
      applyVO.setIdCard(caseMap.get(apply.getCaseId()) == null ? null : caseMap.get(apply.getCaseId()).getIdCard());
      applyVO.setDepName(depTeamMap.get(apply.getDunnerDepId()) == null ? null : depTeamMap.get(apply.getDunnerDepId()));
      applyVO.setTeamName(depTeamMap.get(apply.getDunnerTeamId()) == null ? null : depTeamMap.get(apply.getDunnerTeamId()));

      Case caseInfo = caseService.selectByPrimaryKey(apply.getCaseId());
      if (null != caseInfo) {
        applyVO.setName(caseInfo.getName());
      }
      if (ObjectUtil.isNotNull(applyVO.getAllotAgent())){
        applyVO.setAgentType(ObjectUtil.equals(applyVO.getAllotAgent(), 1) ? ApplyEnums.AgentType.INNER.getCode():ApplyEnums.AgentType.OUTER.getCode());
      }

      cooperationApplyList.add(applyVO);
    }

    PageOutput pageOutput =
        new PageOutput(
            page.getPageNum(),
            page.getPageSize(),
            page != null ? (int) page.getTotal() : cooperationApplyList.size(),
            cooperationApplyList);
    return pageOutput;
  }

  public CooperationApplyStatisticsVO cooperationApplyStatistics(
      CaseCooperationApplyListParam param) {
    UserSession userSession = userService.getTokenUser();
    if (param.getApplyTime() != null && param.getApplyTime().split(",").length == 2) {
      param.setApplyTimeStart(DateUtils.convertDate(param.getApplyTime().split(",")[0]));
      param.setApplyTimeEnd(DateUtils.convertDate(param.getApplyTime().split(",")[1]));
    }
    if (param.getAuditTime() != null && param.getAuditTime().split(",").length == 2) {
      param.setAuditTimeStart(DateUtils.convertDate(param.getAuditTime().split(",")[0]));
      param.setAuditTimeEnd(DateUtils.convertDate(param.getAuditTime().split(",")[1]));
    }

    if (UserUtils.likeAdmin()) {
      param.setOrgId(userSession.getOrgId());
    } else if (UserUtils.likeBranchAdmin()) {
      param.setOrgId(userSession.getOrgId());
      param.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader()) {
      param.setOrgId(userSession.getOrgId());
      param.setTeamId(userSession.getTeamId());
    } else {
      throw new ApiException("角色错误");
    }

    List<CaseCooperationApply> list = caseCooperationApplyMapper.selectCooperationApplyList(param);

    Map<Byte, Long> result =
        list.stream()
            .collect(Collectors.groupingBy(CaseCooperationApply::getState, Collectors.counting()));

    CooperationApplyStatisticsVO vo = new CooperationApplyStatisticsVO();
    vo.setTotal(list.size());
    vo.setIngCount(
        result.get(CaseCooperationApplyEnums.State.ING.getCode()) == null
            ? 0L
            : result.get(CaseCooperationApplyEnums.State.ING.getCode()));
    vo.setPassCount(
        result.get(CaseCooperationApplyEnums.State.PASS.getCode()) == null
            ? 0L
            : result.get(CaseCooperationApplyEnums.State.PASS.getCode()));
    vo.setRefuseCount(
        result.get(CaseCooperationApplyEnums.State.REFUSE.getCode()) == null
            ? 0L
            : result.get(CaseCooperationApplyEnums.State.REFUSE.getCode()));
    vo.setCancelCount(
        result.get(CaseCooperationApplyEnums.State.CANCEL.getCode()) == null
            ? 0L
            : result.get(CaseCooperationApplyEnums.State.CANCEL.getCode()));

    return vo;
  }

  public void batchRefuseApply(List<Long> caseIdList, UserSession userSession, String desc) {
    if (CollectionUtils.isEmpty(caseIdList)) {
      return;
    }
    Map<String, Object> params = new HashMap<>();
    params.put("state", CaseCooperationApplyEnums.State.REFUSE.getCode());
    params.put("auditBy", userSession == null ? 0 : userSession.getId());
    params.put(
        "auditName", userSession == null ? "系统" : userService.getNames(userSession.getOrgId()).get(userSession.getId()));
    params.put("auditDesc", desc);
    params.put("caseIdList", caseIdList);
    caseCooperationApplyMapper.batchUpdateApplyState(params);
  }

  public PageOutput cooperationAwaitList(CaseCooperationApplyListParam param) throws Exception {
    UserSession userSession = userService.getTokenUser();
    if (param.getApplyTime() != null && param.getApplyTime().split(",").length == 2) {
      param.setApplyTimeStart(DateUtils.convertDate(param.getApplyTime().split(",")[0]));
      param.setApplyTimeEnd(DateUtils.convertDate(param.getApplyTime().split(",")[1]));
    }
    if (param.getAuditTime() != null && param.getAuditTime().split(",").length == 2) {
      param.setAuditTimeStart(DateUtils.convertDate(param.getAuditTime().split(",")[0]));
      param.setAuditTimeEnd(DateUtils.convertDate(param.getAuditTime().split(",")[1]));
    }

    DepTeam depTeam = depTeamService.selectByPrimaryKey(userSession.getTeamId());
    if (null == depTeam) {
      throw new ApiException("该催员没有团队！");
    }

    // 指定给我
    if (param.getType() == CaseCooperationApplyEnums.Type.SELECTED_DUNNER.getCode().intValue()) {
      param.setUserId(userSession.getId());
    }

    // 催员为非直属小组，协催范围为分公司
    if (depTeam.getUnderTeam().equals(DepTeamEnums.UnderTeam.NOT.getCode())) {
      param.setOrgId(userSession.getOrgId());
      param.setDepId(userSession.getDepId());
    } else {
      // 催员为直属小组时，范围为所有直属小组
      param.setDepTeamIdList(depTeamService.getDepTeamIdByUnderTeam());
    }

    // 分页
    PageParam pageParam = new PageParam();
    pageParam.setPage(param.getPage());
    pageParam.setLimit(param.getLimit());
    Page page = PageUtils.setPage(pageParam);

    List<CaseCooperationApply> list = caseCooperationApplyMapper.selectCooperationAwaitList(param);
    // 自己申请的案件不展示在自己的抢单列表中，过滤掉自己申请的协催
    list =
        list.stream()
            .filter(r -> !r.getApplyBy().equals(userSession.getId()))
            .collect(Collectors.toList());

    List<CooperationAwaitListVO> cooperationApplyList = new ArrayList<>();
    for (CaseCooperationApply apply : list) {
      CooperationAwaitListVO applyVO = BeanUtil.copyProperties(apply, CooperationAwaitListVO.class);

      applyVO.setState(apply.getState().intValue());
      applyVO.setType(apply.getType().intValue());
      // 所属公司：分公司没有用总公司
      if (null == apply.getDunnerDepId()) {
        applyVO.setDunnerCompany(companyService.getNames().get(apply.getOrgId()));
      } else {
        applyVO.setDunnerCompany(depTeamService.getNames().get(apply.getDunnerDepId()));
      }
      applyVO.setDunnerTeam(depTeamService.getNames().get(apply.getDunnerTeamId()));
      applyVO.setOrgDeltName(deltService.getNames().get(apply.getOrgDeltId()));
      applyVO.setOutBatchNo(outBatchService.getNames().get(apply.getOutBatchId()));
      applyVO.setProductName(productService.getNames().get(apply.getProductId()));

      // 案件负责人，申请表中申请人为案件负责人
      applyVO.setDunnerName(apply.getApplyName());

      applyVO.setApplyTime(apply.getApplyTime() == null ? null : apply.getApplyTime().getTime());

      Case caseInfo = caseService.selectByPrimaryKey(apply.getCaseId());
      if (null != caseInfo) {
        applyVO.setName(caseInfo.getName());
      }

      cooperationApplyList.add(applyVO);
    }

    PageOutput pageOutput =
        new PageOutput(
            page.getPageNum(),
            page.getPageSize(),
            page != null ? (int) page.getTotal() : cooperationApplyList.size(),
            cooperationApplyList);
    return pageOutput;
  }

  /**
   * 案件彻底物理删除，级联删除待审核/待分配的协催记录
   *
   * @param caseIds 案件ids
   * @return
   */
  @Transactional(rollbackFor = Exception.class)
  public Integer delCaseCooperationApply(List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return 0;
    }
    Integer i = caseCooperationApplyMapper.delCaseCooperationApply(caseIds
            ,Lists.newArrayList(CaseCooperationApplyEnums.State.ING.getCode().intValue(),CaseCooperationApplyEnums.State.PASS.getCode().intValue()));
    return i;
  }

  public void allotCooperator(AllotCooperatorParam param) throws Exception{
    User cooperator = userService.selectByPrimaryKey(param.getCooperatorId());
    if (ObjectUtil.isNull(cooperator) || ObjectUtil.notEqual(User.Status.NORMAL.getCode(),cooperator.getStatus())) {
      throw new ApiException("协催员不存在");
    }

    UserSession userSession = userService.getTokenUser();

    List<Long> applyIdList = param.getApplyIdList();
    List<CaseCooperationApply> applyList = selectByIdList(applyIdList);
    boolean allMatchState = applyList.stream().allMatch(p -> ObjectUtil.equals(p.getState(), CaseCooperationApplyEnums.State.PASS.getCode()));
    AssertUtil.isTrue(allMatchState,"请检查审批状态，此操作仅支持审批通过的申请");
    boolean allMatchAllot = applyList.stream().allMatch(p -> ObjectUtil.equals(p.getAllot(), CaseCooperationApplyEnums.Allot.NO.getCode()));
    AssertUtil.isTrue(allMatchAllot,"请确认是否已分配，此操作仅支持未分配的申请");
    Map<Long, List<CaseCooperationApply>> applyListGroup = applyList.stream().collect(Collectors.groupingBy(CaseCooperationApply::getCaseId));
    boolean onlyOne = applyListGroup.entrySet().stream().allMatch(e -> ObjectUtil.equals(e.getValue().size(), 1));
    AssertUtil.isTrue(onlyOne,"请确认是否存在相同案件多次申请，暂不支持");

    List<Long> caseIds = applyList.stream().map(CaseCooperationApply::getCaseId).collect(Collectors.toList());
    CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
    caseMultiQuery.setCaseIds(caseIds);
    List<CaseQueryResult> caseList = caseService.queryResultForMulti(caseMultiQuery);

    caseList = caseService.protectCaseStatus(userSession.getOrgId(), caseList);
    if (ObjectUtil.isEmpty(caseList)) {
      throw new ApiException("搜索可执行案件为空，请确定案件状态");
    }

    // 同步
    caseCooperationService.doAllotCooperator(caseList, cooperator, userSession, applyList);
 }

  /**
   * 更新分配分配状态
   *
   * @param applyIdList 应用id列表
   * @param userSession 用户会话
   */
  public void updateAllot(List<Long> applyIdList, UserSession userSession) {
    if (ObjectUtil.isEmpty(applyIdList)){
      return;
    }
    Example example = new Example(CaseCooperationApply.class);
    example.createCriteria().andIn("id",applyIdList);

    CaseCooperationApply apply = new CaseCooperationApply();
    apply.setAllot(CaseCooperationApplyEnums.Allot.YES.getCode());
    apply.setUpdateBy(userSession.getId());
    apply.setUpdateTime(new Date());

    updateByExampleSelective(apply,example);
  }

  /**
   * 我的审批列表
   *
   * @param param      参数
   * @param orgId      组织id
   * @param userId     用户id
   * @param formFields 表单字段
   * @return {@link List}<{@link ApproveListVO}>
   */
    public List<ApproveListVO> approveList(ApproveListParam param, Long orgId, Long userId, String formFields) {
      String applyTimeStartStr = null;
      String applyTimeEndStr = null;
      String applyTime = param.getApplyTime();
      if (StrUtil.isNotBlank(applyTime)) {
        String[] range = applyTime.split(",");
        if (range.length == 2) {
          applyTimeStartStr = DateUtils.convertDate(range[0]);
          applyTimeEndStr = DateUtils.convertDate(range[1]);
        }
      }
      return caseCooperationApplyMapper.approveList(param, orgId, userId, applyTimeStartStr, applyTimeEndStr, formFields);
    }

  /**
   * 审批待处理统计
   *
   * @param orgId        组织id
   * @param userId       用户id
   * @param businessType 业务类型
   * @return {@link Integer}
   */
  public Integer todoStatistics(Long orgId, Long userId, FlowEnums.BusinessType businessType) {
    return caseCooperationApplyMapper.todoStatistics(orgId,userId,businessType.getCode());
  }

  /**
   * 超时记录ID
   *
   * @param startTime 开始时间
   * @param endTime   结束时间
   * @return {@link List}<{@link Long}>
   */
  public List<Long> timeOutRecordIds(Date startTime, Date endTime) {
    Example example = new Example(CaseCooperationApply.class);
    example.selectProperties("id");
    Example.Criteria criteria = example.and();
    criteria.andEqualTo("state", CaseCooperationApplyEnums.State.ING.getCode())
            .andIsNotNull("outTime")
            .andGreaterThanOrEqualTo("outTime",startTime)
            .andLessThan("outTime",endTime);

    List<CaseCooperationApply> recordes = selectByExample(example);
    List<Long> timeOutRecordIds = recordes.stream().map(CaseCooperationApply::getId).collect(Collectors.toList());
    return timeOutRecordIds;
  }

  /**
   * 状态更新为超时
   *
   * @param applyIds 申请id
   */
  public void updateStateWithTimeOut(List<Long> applyIds) {
    if (ObjectUtil.isEmpty(applyIds)){
      return;
    }
    Example example = new Example(CaseCooperationApply.class);
    Example.Criteria criteria = example.and();
    criteria.andIn("id", applyIds)
            .andEqualTo("state", CaseCooperationApplyEnums.State.ING.getCode());

    CaseCooperationApply update = new CaseCooperationApply();
    update.setState(CaseCooperationApplyEnums.State.TIMEOUT.getCode());
    update.setUpdateTime(new Date());

    updateByExampleSelective(update,example);
  }
}
