package com.anmi.collection.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.enums.VisitAuditEnums;
import com.anmi.collection.common.enums.flow.FlowEnums;
import com.anmi.collection.entity.requset.flow.ApproveListParam;
import com.anmi.collection.entity.response.flow.ApproveListVO;
import com.anmi.collection.mapper.VisitAuditMapper;
import com.anmi.collection.utils.DateUtils;
import com.anmi.domain.visit.VisitAudit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VisitAuditService extends BaseService<VisitAudit>{

    @Resource private VisitAuditMapper visitAuditMapper;

    /**
     * 我的审批列表
     *
     * @param param      参数
     * @param orgId      组织id
     * @param userId     用户id
     * @param formFields 表单字段
     * @return {@link List}<{@link ApproveListVO}>
     */
    public List<ApproveListVO> approveList(ApproveListParam param, Long orgId, Long userId, String formFields) {
        String applyTimeStartStr = null;
        String applyTimeEndStr = null;
        String applyTime = param.getApplyTime();
        if (StrUtil.isNotBlank(applyTime)) {
            String[] range = applyTime.split(",");
            if (range.length == 2) {
                applyTimeStartStr = DateUtils.convertDate(range[0]);
                applyTimeEndStr = DateUtils.convertDate(range[1]);
            }
        }
        return visitAuditMapper.approveList(param, orgId, userId, applyTimeStartStr, applyTimeEndStr, formFields);
    }

    /**
     * 审批待处理统计
     *
     * @param orgId        组织id
     * @param userId       用户id
     * @param businessType 业务类型
     * @return {@link Integer}
     */
    public Integer todoStatistics(Long orgId, Long userId, FlowEnums.BusinessType businessType) {
        return visitAuditMapper.todoStatistics(orgId,userId,businessType.getCode());
    }

    /**
     * 超时记录ID
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link Long}>
     */
    public List<Long> timeOutRecordIds(Date startTime, Date endTime) {
        Example example = new Example(VisitAudit.class);
        example.selectProperties("id");
        Example.Criteria criteria = example.and();
        criteria.andEqualTo("state", VisitAuditEnums.State.ING.getCode())
                .andIsNotNull("outTime")
                .andGreaterThanOrEqualTo("outTime",startTime)
                .andLessThan("outTime",endTime);

        List<VisitAudit> recordes = selectByExample(example);
        List<Long> timeOutRecordIds = recordes.stream().map(VisitAudit::getId).collect(Collectors.toList());
        return timeOutRecordIds;
    }

    /**
     * 状态更新为超时
     *
     * @param applyIds 申请id
     */
    public void updateStateWithTimeOut(List<Long> applyIds) {
        if (ObjectUtil.isEmpty(applyIds)){
            return;
        }
        Example example = new Example(VisitAudit.class);
        Example.Criteria criteria = example.and();
        criteria.andIn("id", applyIds)
                .andEqualTo("state", VisitAuditEnums.State.ING.getCode());

        VisitAudit update = new VisitAudit();
        update.setState(VisitAuditEnums.State.TIMEOUT.getCode());
        update.setUpdateTime(new Date());

        updateByExampleSelective(update,example);
    }
}
