package com.anmi.collection.service.fileStorage.impl;

import cn.hutool.core.io.FileUtil;
import com.anmi.collection.common.enums.FileStorageEnums;
import com.anmi.collection.dto.fileStorage.*;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.utils.dict.ZipUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;

import java.io.*;

/**
 * 本地文件管理实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LocalFileStorageStrategy implements FileStorageStrategy {
    @Override
    public String getChannelCode() {
        return FileStorageEnums.Type.LOCAL.getCode();
    }

    @Override
    public String uploadFile(UploadFileInfo uploadFileInfo) {
        String localUrl = uploadFileInfo.getLocalUrl();
        File localFile = new File(localUrl);
        if (!localFile.getParentFile().exists()) {
            localFile.getParentFile().mkdirs();
        }
        try {
            InputStream inputStream = uploadFileInfo.getFile().getInputStream();
            FileUtils.copyInputStreamToFile(inputStream, localFile);
            inputStream.close();
        } catch (Exception e) {
            log.error("上传本地服务器失败，", e);
        }
        int index = localUrl.indexOf("/ng/download");
        if (index != -1) {
            localUrl = localUrl.substring(index);
        }
        log.info("文件已上传至本地服务器====>文件名为：{} 地址为：{}", uploadFileInfo.getFileName(), localUrl);
        return localUrl;
    }

    @Override
    public String uploadFilesToZip(UploadZipFileInfo uploadZipFileInfo) {
        String localUrl = uploadZipFileInfo.getLocalUrl();
        try {
            File fileTmp = new File(uploadZipFileInfo.getLocalUrl());
            if (!fileTmp.getParentFile().exists()) {
                fileTmp.getParentFile().mkdirs();
            }
            // 本地zip存储的位置
            FileOutputStream fos = new FileOutputStream(fileTmp);
            ZipUtil.toZip(uploadZipFileInfo.getFiles(), fos, true);
            int index = localUrl.indexOf("/ng/download");
            if (index != -1) {
                localUrl = localUrl.substring(index);
            }
            log.info("文件已上传至本地服务器====>文件名为：{} 地址为：{}", uploadZipFileInfo.getFileName(),localUrl);
        } catch (Exception e) {
            throw new ApiException("上传zip文件失败！", e);
        }
        return localUrl;
    }

    @Override
    public String uploadCreatedFile(UploadCreatedFileInfo fileInfo) {
        String url = fileInfo.getLocalUrl();
        File dest = new File(url);
        FileUtil.copy(fileInfo.getFile(), dest, true);
        int index = url.indexOf("/ng/download");
        if (index != -1) {
            url = url.substring(index);
        }
        FileUtils.deleteQuietly(fileInfo.getFile());
        log.info("文件已上传至本地服务器====>文件名为：{} 地址为：{}", fileInfo.getFileName(), url);
        return url;
    }

    @Override
    public String uploadCreatedFileByStream(UploadCreatedFileStream fileInfo) {
        String url = fileInfo.getLocalUrl();
        File dest = new File(url);
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }
        int index = url.indexOf("/ng/download");
        if (index != -1) {
            url = url.substring(index);
        }
        try {
            FileUtils.copyInputStreamToFile(fileInfo.getInputStream(), dest);
        } catch (Exception e) {
            throw new ApiException("上传zip文件失败！", e);
        }
        log.info("文件已上传至本地服务器====>文件名为：{} 地址为：{}", fileInfo.getFileName(), url);
        return url;
    }

    @Override
    public Boolean delFile(String fileName, String url) {
        boolean flag = false;
        File file = new File("/usr/local/duyansoft" + url);
        if (file.isFile() && file.exists()) {
            file.delete();
            flag = true;
        }
        log.info("本地的文件已删除====>文件名为:{} 地址为:{}", fileName, url);
        return flag;
    }

    @Override
    public InputStream readFileStream(ReadFileInfo readFileInfo) {
        File localFile = new File("/usr/local/duyansoft" + readFileInfo.getUrl());
        FileInputStream fileInputStream = null;
        try {
            fileInputStream = new FileInputStream(localFile);
        } catch (FileNotFoundException e) {
            throw new ApiException("文件存储管理读取本地文件流出现异常！", e);
        }
        return fileInputStream;
    }

}
