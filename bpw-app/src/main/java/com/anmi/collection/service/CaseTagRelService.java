package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.mapper.CaseTagRelMapper;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.domain.cases.CaseTagRel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CaseTagRelService extends BaseService<CaseTagRel> {
    @Resource
    private CaseTagRelMapper caseTagRelMapper;

    /**
     * 案件删除，级联删除案件标签关联信息
     *
     * @param caseIds 案件ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void delCaseTagRelByCaseIds(List<Long> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }
        Example example = new Example(CaseTagRel.class);
        example.and().andIn("caseId", caseIds);
        caseTagRelMapper.deleteByExample(example);
    }
}
