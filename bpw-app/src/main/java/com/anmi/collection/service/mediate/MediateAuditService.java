package com.anmi.collection.service.mediate;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.common.enums.flow.ApplyEnums;
import com.anmi.collection.common.enums.flow.FlowEnums;
import com.anmi.collection.common.enums.flow.FlowHandleRecordEnums;
import com.anmi.collection.constant.DictionaryConstant;
import com.anmi.collection.constant.UserConstant;
import com.anmi.collection.dto.CaseDTO;
import com.anmi.collection.encrypt.EncryptProperties;
import com.anmi.collection.encrypt.EncryptService;
import com.anmi.collection.entity.requset.cases.CaseMultiQuery;
import com.anmi.collection.entity.requset.flow.ApproveListParam;
import com.anmi.collection.entity.requset.mediate.*;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.flow.ApproveListVO;
import com.anmi.collection.entity.response.flow.FlowNodeVO;
import com.anmi.collection.entity.response.flow.FlowVO;
import com.anmi.collection.entity.response.mediate.ApplyMediateFailVO;
import com.anmi.collection.entity.response.mediate.MediateAuditStatVO;
import com.anmi.collection.entity.response.mediate.MediateAuditVO;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.*;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.*;
import com.anmi.collection.service.flow.FlowService;
import com.anmi.collection.service.letter.LetterSignerService;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.base.DictionaryEntity;
import com.anmi.domain.cases.OrgSwitch;
import com.anmi.domain.letter.LetterSigner;
import com.anmi.domain.letter.LetterTemplate;
import com.anmi.domain.mediate.LetterMediate;
import com.anmi.domain.mediate.MediateAudit;
import com.anmi.domain.sys.CustomField;
import com.anmi.domain.user.User;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Service
@Slf4j
public class MediateAuditService extends BaseService<MediateAudit> {
    @Resource private UserMapper userMapper;
    @Resource private LetterTemplateMapper letterTemplateMapper;
    @Resource private LetterSignerMapper letterSignerMapper;
    @Resource private MediateAuditMapper mediateAuditMapper;
    @Resource private LetterMediateMapper letterMediateMapper;
    @Resource private LetterMediateService letterMediateService;
    @Resource private CaseService caseService;
    @Resource private CustomFieldService customFieldService;
    @Resource private I18nService i18nService;
    @Resource private OrgSwitchService orgSwitchService;
    @Resource private LetterSignerService letterSignerService;
    @Resource private StringRedisTemplate stringRedisTemplate;
    @Resource private AsyncTaskService asyncTaskService;
    @Resource private ApplicationContext applicationContext;
    @Resource private EncryptProperties encryptProperties;
    @Resource private EncryptService encryptService;
    @Resource private FlowService flowService;

    private static final String SEND_DAY = "${sendDay}";
    private static final Map<String, String> TEMPLATE_VAR_MAP;
    private static final String ASYNC_PREFIX = "async-";

    static {
        TEMPLATE_VAR_MAP = new HashMap<>();
        TEMPLATE_VAR_MAP.put("name", "${name}");
        TEMPLATE_VAR_MAP.put("idCard", "${idCard}");
        TEMPLATE_VAR_MAP.put("amount", "${amount}");
        TEMPLATE_VAR_MAP.put("payAmount", "${payAmount}");
        TEMPLATE_VAR_MAP.put("entrustEndTime", "${entrustEndTime}");
        TEMPLATE_VAR_MAP.put("overdueDays", "${overdueDays}");
        TEMPLATE_VAR_MAP.put("orgDeltName", "${orgDeltName}");
        TEMPLATE_VAR_MAP.put("productName", "${productName}");
        TEMPLATE_VAR_MAP.put("overdueDate", "${overdueDate}");
        TEMPLATE_VAR_MAP.put("ownMobile", "${ownMobile}");
    }

    public PageOutput<MediateAuditVO> getMediateAuditList(ApplyMediateParam param) {
        encryptQueryData(param);
        UserSession userSession = UserUtils.getTokenUser();
        // 权限
        if (UserUtils.likeAdmin(userSession.getRoleId())) {
            param.setOrgId(userSession.getOrgId());
        } else if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
            param.setOrgId(userSession.getOrgId());
            param.setDepId(userSession.getDepId());
        } else {
            param.setOrgId(userSession.getOrgId());
            param.setUserId(userSession.getId());
        }
        param = this.setParam(param);
        // 分页
        PageParam pageParam = new PageParam();
        pageParam.setPage(param.getPage());
        pageParam.setLimit(param.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<MediateAuditVO> mediateAudits = mediateAuditMapper.selectMediateAuditList(param);
        mediateAudits.forEach(mediateAuditVO -> {
            if (encryptProperties.getEnable()) {
                mediateAuditVO.setName(encryptService.decrypt(mediateAuditVO.getName()));
                mediateAuditVO.setIdCard(encryptService.decrypt(mediateAuditVO.getIdCard()));
                mediateAuditVO.setMobile(encryptService.decrypt(mediateAuditVO.getMobile()));
            }
            mediateAuditVO.setAllotAgent(mediateAuditVO.getIsBankAgent() == null ? null : (Objects.equals(DepTeamEnums.IsBankAgent.YES.getCode(), mediateAuditVO.getIsBankAgent()) ? 2 : 1));
        });
        PageOutput<MediateAuditVO> pageOutput =
                new PageOutput<>(
                        page.getPageNum(),
                        page.getPageSize(),
                        (int)page.getTotal(),
                        mediateAudits
                );
        return pageOutput;
    }
    /**
     * 统计审核文书状态
     *
     * @param param
     * @return
     */
    public MediateAuditStatVO getMediateAuditStat(ApplyMediateParam param) {
        encryptQueryData(param);
        UserSession userSession = UserUtils.getTokenUser();
        // 权限
        if (UserUtils.likeAdmin(userSession.getRoleId())) {
            param.setOrgId(userSession.getOrgId());
        } else if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
            param.setOrgId(userSession.getOrgId());
            param.setDepId(userSession.getDepId());
        } else {
            param.setOrgId(userSession.getOrgId());
            param.setUserId(userSession.getId());
        }
        param = this.setParam(param);
        List<MediateAuditVO> mediateAuditVOS = mediateAuditMapper.selectMediateAuditList(param);
        MediateAuditStatVO mediateAuditStatVO = new MediateAuditStatVO();
        Map<Integer, Long> result = mediateAuditVOS.stream().collect(Collectors.groupingBy(MediateAuditVO::getState, Collectors.counting()));
        mediateAuditStatVO.setTotal(mediateAuditVOS.size());
        // 待审批 0
        mediateAuditStatVO.setWaitCount(
                result.get(MediateAuditEnums.State.ING.getCode()) == null
                        ? 0L
                        : result.get(MediateAuditEnums.State.ING.getCode()));
        // 已通过 1
        mediateAuditStatVO.setPassCount(
                result.get(MediateAuditEnums.State.PASS.getCode()) == null
                        ? 0L
                        : result.get(MediateAuditEnums.State.PASS.getCode()));
        // 已拒绝 -1
        mediateAuditStatVO.setRefuseCount(
                result.get(MediateAuditEnums.State.REFUSE.getCode()) == null
                        ? 0L
                        : result.get(MediateAuditEnums.State.REFUSE.getCode()));
        // 已取消 -2
        mediateAuditStatVO.setCancelCount(
                result.get(MediateAuditEnums.State.CANCEL.getCode()) == null
                        ? 0L
                        : result.get(MediateAuditEnums.State.CANCEL.getCode()));
        return mediateAuditStatVO;
    }

    private ApplyMediateParam setParam(ApplyMediateParam param) {
        // 申请时间
        if (param.getApplyTime() != null && param.getApplyTime().split(",").length == 2) {
            param.setApplyTimeStart(convertDate(param.getApplyTime().split(",")[0]));
            param.setApplyTimeEnd(convertDate(param.getApplyTime().split(",")[1]));
        }
        // 审核时间
        if (param.getAuditTime() != null && param.getAuditTime().split(",").length == 2) {
            param.setAuditTimeStart(convertDate(param.getAuditTime().split(",")[0]));
            param.setAuditTimeEnd(convertDate(param.getAuditTime().split(",")[1]));
        }
        return param;
    }

    private String convertDate(String date) {
        if (!CommonUtil.isEmpty(date) && CommonUtil.isInteger(date)) {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.valueOf(date)));
        }
        // 是否是标准时间格式，否则返回空
        if (!CommonUtil.isValidDate(date)) {
            return null;
        }
        return date;
    }

    /**
     * 文书申请
     *
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void addMediateLetter(MediateAuditParam param) throws Exception{
        UserSession userSession = UserUtils.getTokenUser();
        // 查询案件状态
        CaseDTO aCase = caseService.getCaseForLetter(param.getCaseId());
        if (aCase.getRecovery() != 0) {
            throw new ApiException("当前案件已删除！");
        }
        checkUserAndTemplate(userSession, param.getLetterTemplateId());
        FlowEnums.AgentType agentType = ObjectUtil.equals(aCase.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?FlowEnums.AgentType.OUTER:FlowEnums.AgentType.INNER;
        // 获取审批流
        FlowVO flowVO = flowService.flowChoice(userSession.getOrgId(), agentType, FlowEnums.BusinessType.TJWS);
        // 申请是否自动通过
        Boolean autoPassApply = flowService.autoPassApply(userSession,flowVO);

        LetterMediate letterMediate = setMediateLetter(aCase, param, userSession);
        letterMediateMapper.insertSelective(letterMediate);
        // 设置调解文书申请
        MediateAudit mediateAudit = BeanUtil.copyProperties(param, MediateAudit.class);
        mediateAudit.setApplyBy(userSession.getId());
        mediateAudit.setApplyByName(userSession.getName());
        // 审批状态设置为待审批
        mediateAudit.setState(0);
        // 文书id
        mediateAudit.setLetterId(letterMediate.getId());
        mediateAudit.setApplyTime(new Date());
        mediateAudit.setFlowId(flowVO.getId());
        mediateAudit.setAgentType(ObjectUtil.equals(aCase.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?ApplyEnums.AgentType.OUTER.getCode():ApplyEnums.AgentType.INNER.getCode());
        if (autoPassApply){
            mediateAudit.setState(MediateAuditEnums.State.PASS.getCode());
            mediateAudit.setAuditTime(new Date());
            mediateAudit.setAuditReason("自动通过");
        }
        Integer timeLimitSwitch = flowVO.getTimeLimitSwitch();
        if (ObjectUtil.equals(timeLimitSwitch, FlowEnums.TimeLimitSwitch.YES.getCode())){
            String timeLimit = flowVO.getTimeLimit();
            String hour = StrUtil.subBefore(timeLimit, ":", true);
            String minute = StrUtil.subAfter(timeLimit, ":", true);
            Date outTime = DateUtil.offsetMinute(DateUtil.offsetHour(new Date(),Integer.valueOf(hour)),Integer.valueOf(minute));
            mediateAudit.setOutTime(outTime);
        }
        mediateAuditMapper.insertSelective(mediateAudit);
        List<FlowNodeVO> nodes = flowVO.getNodes();
        Map<String,Object> applyMap = cn.hutool.core.bean.BeanUtil.beanToMap(mediateAudit);
        applyMap.put("templateId",letterMediate.getTemplateId());
        String parentUniqueCode = flowService.getApplyOrRuleNodeUniqueCode(nodes,applyMap,FlowEnums.BusinessType.TJWS,agentType,userSession);
        if (autoPassApply) {
            flowService.findNextCopyNodeAndExecute(parentUniqueCode,flowVO, mediateAudit.getId(),userSession);
        } else {
            flowService.findNextNodeAndExecute(parentUniqueCode,flowVO, mediateAudit.getId(),userSession, userSession.getId());
        }
    }

    /**
     * 设置文书审批参数
     *
     * @param param
     * @param userSession
     * @return
     */
    public LetterMediate setMediateLetter(CaseDTO aCase, MediateAuditParam param, UserSession userSession){
        LetterTemplate letterTemplate = letterTemplateMapper.selectByPrimaryKey(param.getLetterTemplateId());
        // 设置文书信息
        LetterMediate letterMediate = new LetterMediate();
        letterMediate.setCreateTime(new Date());
        // 由于兼容甲方权限功能，文书跟着案件权限走
        letterMediate.setOrgId(aCase.getOrgId());
        letterMediate.setDepId(aCase.getDepId());
        letterMediate.setTeamId(aCase.getTeamId());
        letterMediate.setTemplateId(param.getLetterTemplateId());
        letterMediate.setShowSignTime(letterTemplate.getShowSignTime());
        letterMediate.setSignOrder(letterTemplate.getSignOrder());
        letterMediate.setName(letterTemplate.getName());
        letterMediate.setCaseId(param.getCaseId());
        letterMediate.setUpdateTime(new Date());
        letterMediate.setCreateTime(new Date());
        letterMediate.setSignFlowStatus(0);
        letterMediate.setStartStatus(0);
        letterMediate.setNoticeType(0);
        // 填充案件变量
        String content = letterTemplate.getContent();
        try {
            content = fillCaseInfoContent(aCase, content);
        }catch (Exception e) {
            log.error("填充案件变量时出现异常{}",e.getStackTrace());
            throw new ApiException("填充案件变量时出现异常");
        }
        // 填充自定义变量
        content = handleContentCustomField(userSession.getOrgId(),content,  aCase.getFieldJson(), userSession.getLanguage());
        letterMediate.setTemplateContent(content);
        // 设置调解员
        letterMediate.setMediator(userSession.getId());
        // 根据template查询签署方信息
        Example example = new Example(LetterSigner.class);
        example.and().andEqualTo("letterTemplateId", param.getLetterTemplateId());
        example.orderBy("sort").asc();
        List<LetterSigner> signerList = letterSignerMapper.selectByExample(example);
        // 设置签署方信息
        Map<String, String> signerFieldJson = new HashMap<>();
        for (LetterSigner signer : signerList) {
            signerFieldJson.put(signer.getName(), "false");
        }
        letterMediate.setSignerFieldJson(signerFieldJson);
        return letterMediate;
    }

    /**
     * 校验参数
     *
     * @param userSession
     * @param templateId
     */
    private void checkUserAndTemplate(UserSession userSession, Long templateId) {
        // 查询是否存在该模板信息
        LetterTemplate letterTemplate = letterTemplateMapper.selectByPrimaryKey(templateId);
        if (letterTemplate == null) {
            throw new ApiException("申请文书选中调解模板已经被删除!");
        }
        // 查询此次申请催员是否为调解员，是否启用
        User user = userMapper.selectByPrimaryKey(userSession.getId());
        if (UserConstant.MEDIATOR.compareTo(user.getMediator()) != 0) {
            throw new ApiException("当前用户非调解员！");
        }
        if (UserConstant.ISCANMEDIATE.compareTo(user.getIsCanMediate()) != 0) {
            throw new ApiException("当前调解员申请文书功能已被禁用！");
        }
    }

    /**
     * 取消文书审批
     *
     * @param applyId 申请文书id
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelMediateLetter(Long applyId) {
        MediateAudit applyRecord = selectByPrimaryKey(applyId);
        AssertUtil.notNull(applyRecord, "未发现申请");
        AssertUtil.isTrue(ObjectUtil.equals(applyRecord.getState(), MediateAuditEnums.State.ING.getCode()), "申请非审批中");
        AssertUtil.notNull(applyRecord.getFlowId(), "审批流异常");
        Long flowId = applyRecord.getFlowId();
        flowService.revokeCheck(applyId, flowId);

        MediateAudit mediateAuditUpdate = new MediateAudit();
        mediateAuditUpdate.setId(applyId);
        // 查询审批文书信息
        mediateAuditUpdate.setState(MediateAuditEnums.State.CANCEL.getCode());
        mediateAuditUpdate.setUpdateTime(new Date());
        mediateAuditMapper.updateByPrimaryKeySelective(mediateAuditUpdate);
    }

    /**
     * 审核拒绝
     *
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public ApplyMediateFailVO refuseAudit(RefuseMediateParam param) throws Exception{
        UserSession userSession = UserUtils.getTokenUser();
        List<Long> applyIds = Arrays.stream(param.getAuditIds().split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        AssertUtil.notEmpty(applyIds,"请选择申请");
        AssertUtil.isTrue(applyIds.size()==1,"暂不支持批量审批");

        Long applyId = applyIds.get(0);
        MediateAudit applyRecord = selectByPrimaryKey(applyId);
        AssertUtil.notNull(applyRecord, "未发现申请");
        AssertUtil.isTrue(ObjectUtil.equals(applyRecord.getState(), MediateAuditEnums.State.ING.getCode()), "申请非审批中");
        AssertUtil.notNull(applyRecord.getFlowId(), "审批流异常");

        Long flowId = applyRecord.getFlowId();
        Long applyUserId = applyRecord.getApplyBy();

        FlowHandleRecordEnums.HandleStatus approveStatus = FlowHandleRecordEnums.HandleStatus.REFUSE;
        flowService.execApprove(applyId,flowId,approveStatus,param.getRejectReason(),userSession,applyUserId);

        ApplyMediateFailVO applyMediateFailVO = new ApplyMediateFailVO();
        applyMediateFailVO.setSuccessCount(1);
        applyMediateFailVO.setFailCount(0);
        return applyMediateFailVO;
    }

    /**
     * 批量通过审批
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ApplyMediateFailVO passAudit(PassMediateParam param) throws Exception{
        UserSession userSession = UserUtils.getTokenUser();
        List<Long> applyIds = Arrays.stream(param.getAuditIds().split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        AssertUtil.notEmpty(applyIds,"请选择申请");
        AssertUtil.isTrue(applyIds.size()==1,"暂不支持批量审批");

        Long applyId = applyIds.get(0);
        MediateAudit applyRecord = selectByPrimaryKey(applyId);
        AssertUtil.notNull(applyRecord, "未发现申请");
        AssertUtil.isTrue(ObjectUtil.equals(applyRecord.getState(), MediateAuditEnums.State.ING.getCode()), "申请非审批中");
        AssertUtil.notNull(applyRecord.getFlowId(), "审批流异常");

        Long flowId = applyRecord.getFlowId();
        Long applyUserId = applyRecord.getApplyBy();

        FlowHandleRecordEnums.HandleStatus approveStatus = FlowHandleRecordEnums.HandleStatus.PASS;
        flowService.execApprove(applyId,flowId,approveStatus,param.getPassReason(),userSession,applyUserId);

        ApplyMediateFailVO applyMediateFailVO = new ApplyMediateFailVO();
        applyMediateFailVO.setSuccessCount(1);
        applyMediateFailVO.setFailCount(0);
        return applyMediateFailVO;
    }

    /**
     * 更新申请 处理状态
     *
     * @param applyId       申请id
     * @param approveStatus 审批状态
     * @param approveUserId 更新人
     */
    public void updateHandleStatus(Long applyId, FlowHandleRecordEnums.HandleStatus approveStatus,Long approveUserId) {
        Integer status = ObjectUtil.equals(approveStatus,FlowHandleRecordEnums.HandleStatus.PASS)?1:-1;
        MediateAudit applyRecord = new MediateAudit();
        applyRecord.setId(applyId);
        applyRecord.setState(status);
        applyRecord.setAuditBy(approveUserId);
        applyRecord.setUpdateTime(new Date());
        applyRecord.setAuditTime(new Date());
        updateByPrimaryKeySelective(applyRecord);

        if (ObjectUtil.equals(approveStatus,FlowHandleRecordEnums.HandleStatus.PASS)){
            try {
                MediateAudit mediateAudit = selectByPrimaryKey(applyId);
                // 审核通过则发起文书
                letterMediateService.uploadFile(mediateAudit.getLetterId());
            } catch (Exception e) {
                log.error("发起文书时出现异常！");
            }
        }
    }

    /**
     * 案件彻底删除，删除审核表中待审核，审核拒绝状态的审核记录
     * @param caseIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteMediateAuditByCaseId(List<Long> caseIds) {
        if (CommonUtils.isEmpty(caseIds)) {
            return;
        }
        Example letterExample = new Example(LetterMediate.class);
        letterExample.and().andIn("caseId", caseIds);
        List<LetterMediate> letterMediates = letterMediateService.selectByExample(letterExample);
        if (CollectionUtils.isEmpty(letterMediates)) {
            return;
        }
        // 案件彻底删除之后，审核状态 待审核、审核拒绝、取消的、已审核（未发起）都删除 待发起状态 只保留已发起签署和签署完成的
        List<Long> letterMediateIds = letterMediates.stream().filter(item -> item.getStartStatus() != 1)
                .map(LetterMediate::getId).collect(Collectors.toList());
        Map<Long, LetterMediate> letterMediateMap = letterMediates.stream().collect(Collectors.toMap(LetterMediate::getId, f -> f));
        List<MediateAudit> mediateAudits = new ArrayList<>();
        // 查询对应审核 过滤掉已通过审核的数据
        if (!CollectionUtils.isEmpty(letterMediateIds)) {
            Example auditExample = new Example(MediateAudit.class);
            auditExample.and().andIn("letterId", letterMediateIds);
            mediateAudits = mediateAuditMapper.selectByExample(auditExample);
        }
        // 审核状态不为已通过
        List<Long> auditIds = mediateAudits.stream().filter(item -> item.getState() != 1)
                .map(MediateAudit::getId).collect(Collectors.toList());
        // 审批状态为已通过且未发起
        List<Long> noStartIds = mediateAudits.stream().filter(item -> item.getState() == 1 && (letterMediateMap.get(item.getLetterId()).getStartStatus() == 0))
                .map(MediateAudit::getId).collect(Collectors.toList());
        List<Long> ids = new ArrayList<>();
        ids.addAll(auditIds);
        ids.addAll(noStartIds);
        if (!CollectionUtils.isEmpty(ids)) {
            List<List<Long>> groupList = CmUtil.splitList(ids, 500);
            Example example = new Example(MediateAudit.class);
            for (List<Long> subIds : groupList) {
                if (CommonUtils.isEmpty(subIds)) {
                    continue;
                }
                example.clear();
                example.createCriteria().andIn("id", subIds);
                super.deleteByExample(example);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchCreateMediateLetter(CreateMediateLetterParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        caseService.encryptQueryData(param.getCaseMultiQuery());
        CaseMultiQuery caseMultiQuery = param.getCaseMultiQuery();
        checkRoleAndTemplate(userSession, param.getTemplateId());
        if (caseMultiQuery.getBeAmc()) {
            caseService.listCaseAmcParamDeal(caseMultiQuery);
        } else {
            caseService.listCaseAnmiParamDeal(caseMultiQuery);
        }
        if (caseMultiQuery.getAllSelect()) {
            asyncCreateMediateLetter(param);
        } else {
            if (CollectionUtils.isEmpty(caseMultiQuery.getCaseIds())) {
                throw new ApiException("未选择任何案件信息");
            }
            // 查询案件状态
            List<CaseDTO> cases = caseService.getCasesForLetter(param.getCaseMultiQuery());
            String msg = null;
            Example example = new Example(LetterSigner.class);
            example.and().andEqualTo("letterTemplateId", param.getTemplateId());
            List<LetterSigner> signerList = letterSignerService.selectByExample(example);
            List<String> signerNames = signerList.stream().map(LetterSigner::getName).collect(Collectors.toList());
            List<CaseDTO> failedCase = new ArrayList<>();
            if (signerNames.contains(SignerEnums.Type.MEDIATOR.getMessage())) {
                // 不存在催员的案件不可生成带有调解员签名的文书
                failedCase = cases.stream().filter(caseDTO -> !Objects.isNull(caseDTO.getUserId())).collect(Collectors.toList());
                cases.removeAll(failedCase);
                if (!CollectionUtils.isEmpty(failedCase)) {
                    msg = "文书需要调解员签署，该案件未分配到指定调解员，不支持生成文书";
                }
            }
            if (!CollectionUtils.isEmpty(cases)) {
                // 校验模板参数、催员/调解员 文书功能状态（部门启用如何处理）   设置调解文书信息，填充案件变量、自定义变量
                List<LetterMediate> letterMediates = cases.stream().map(dto -> {
                    MediateAuditParam mediateAuditParam = new MediateAuditParam();
                    mediateAuditParam.setLetterTemplateId(param.getTemplateId());
                    mediateAuditParam.setCaseId(dto.getId());
                    return setMediateLetter(dto, mediateAuditParam, userSession);
                }).collect(Collectors.toList());
                letterMediateService.insertBatch(letterMediates);
                //  设置调解文书申请  默认申请通过 mediate_audit
                Date now = new Date();
                Map<Long, CaseDTO> caseMap = cases.stream().collect(Collectors.toMap(CaseDTO::getId, f -> f));
                List<MediateAudit> mediateAudits = letterMediates.stream().map(lm -> {
                    MediateAudit mediateAudit = new MediateAudit();
                    mediateAudit.setAuditBy(userSession.getId());
                    mediateAudit.setApplyBy(userSession.getId());
                    mediateAudit.setApplyByName(userSession.getName());
                    mediateAudit.setAuditByName(userSession.getName());
                    mediateAudit.setUpdateTime(now);
                    mediateAudit.setCreateTime(now);
                    mediateAudit.setAuditTime(now);
                    mediateAudit.setApplyTime(now);
                    mediateAudit.setState(MediateAuditEnums.State.PASS.getCode());
                    mediateAudit.setLetterId(lm.getId());
                    CaseDTO caseDTO = caseMap.get(lm.getCaseId());
                    mediateAudit.setAgentType(ObjectUtil.equals(caseDTO.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?ApplyEnums.AgentType.OUTER.getCode():ApplyEnums.AgentType.INNER.getCode());
                    return mediateAudit;
                }).collect(Collectors.toList());
                mediateAuditMapper.insertList(mediateAudits);

                // 文书申请成功，上传文书至e签宝
                mediateAudits.forEach(ma -> {
                    try {
                        letterMediateService.uploadFile(ma.getLetterId());
                    } catch (Exception e) {
                        log.error("上传文书至e签宝时出现异常");
                    }
                });
            }

            Integer taskStatus = AsyncTaskEnums.Status.SUCCESS.getCode();
            if (CollectionUtils.isEmpty(cases)) {
                taskStatus = AsyncTaskEnums.Status.FAIL.getCode();
            } else if (CollectionUtils.isEmpty(failedCase)){
                taskStatus = AsyncTaskEnums.Status.PART_FAIL.getCode();
            }
            Long taskId = asyncTaskService.createMediateFileTask(
                    param,
                    userSession,
                    caseMultiQuery.getCaseIds().size(),
                    taskStatus,
                    cases.size(),
                    failedCase.size(),
                    msg);

            // 添加案件操作日志
            CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
            caseBatchUpdateEvent.setType(CaseLogEnums.Type.CASE_TO_MEDIATE_FILE.getCode());
            caseBatchUpdateEvent.setUserSession(userSession);
            caseBatchUpdateEvent.setCaseIds(caseMultiQuery.getCaseIds());
            caseBatchUpdateEvent.setTaskId(taskId);
            applicationContext.publishEvent(caseBatchUpdateEvent);
        }
    }

    public void asyncCreateMediateLetter(CreateMediateLetterParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        CaseMultiQuery caseMultiQuery = param.getCaseMultiQuery();
        caseMultiQuery.setCaseIds(null);
        String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(param).getBytes());
        String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
        if (StringUtils.isNotBlank(s)) {
            throw new ApiException("当前已有相同的任务进行中，请稍后操作");
        } else {
            stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
        }
        Long taskId = asyncTaskService.createMediateFileTask(param, userSession, 0L, 0L, 0L, AsyncTaskEnums.Status.ING.getCode(), null);
        stringRedisTemplate.opsForList().leftPush(KeyCache.CASE_ADD_TO_MEDIATE_TASK_ID_LIST, taskId.toString());
    }

    private void checkRoleAndTemplate(UserSession userSession, Long templateId) {
        LetterTemplate letterTemplate = letterTemplateMapper.selectByPrimaryKey(templateId);
        AssertUtil.notNull(letterTemplate, "申请文书选中的调解模板不存在！");
        if (!UserUtils.likeAdmin(userSession.getRoleId()) && !UserUtils.likeBranchAdmin(userSession.getRoleId())) {
            throw new ApiException("当前用户非管理员，不可生成调解文书模板");
        }
    }

    /**
     * 填充案件数据至模板
     *
     * @return
     */
    private String fillCaseInfoContent(CaseDTO caseInfo, String content) throws Exception {
        DecimalFormat df = new DecimalFormat("#.00");
        Class<? extends CaseDTO> c = caseInfo.getClass();
        for(Map.Entry<String, String> entry : TEMPLATE_VAR_MAP.entrySet()) {
            String col = entry.getKey();
            String var = entry.getValue();
            Field field = c.getDeclaredField(col);
            field.setAccessible(true);
            Object value = field.get(caseInfo);
            if (col.toLowerCase().contains("amount")) {
                long money = (long) value;
                if (money == 0L) {
                    value = "0.00";
                } else {
                    value = (double) money / 1000;
                    value = df.format(value);
                }

            }
            if (value instanceof Date) {
                value = DateUtils.formatDate((Date)value);
            }
            // 逾期天数实时计算，根据当前日期减去逾期日期
            if (Objects.equals(col, "overdueDays")) {
                value = calculateOverDays(caseInfo.getOverdueDate(), caseInfo.getOverdueDays(), caseInfo.getOrgId());
            }
            if (value != null) {
                content = content.replace(var, value.toString());
            } else {
                content = content.replace(var, "&nbsp; &nbsp; &nbsp;");
            }

        }
        content = content.replace(SEND_DAY, DateUtils.formatDate(new Date()));
        return content;
    }

    private Integer calculateOverDays(Date overdueDate, Integer overdueDays, Long orgId) {
        OrgSwitch orgSwitch = orgSwitchService.selectOrgSwitchByOrgId(orgId);
        Boolean b = ObjectUtil.isNull(orgSwitch)?false:Boolean.valueOf(orgSwitch.getAutoUpdateOverdueDays());
        if (!b) {
            return overdueDays;
        }
        if (overdueDate == null) {
            return null;
        }
        Long days = DateUtils.countDays(overdueDate, new Date());
        if (days < 0) {
            days = 0L;
        }
        return days.intValue();
    }

    private String handleContentCustomField(Long orgId, String content, Map<String, String>fieldJson,String language) {
        if (fieldJson == null || fieldJson.size()==0) {
            return content;
        }
        Pattern pattern = Pattern.compile("#\\{.+?\\}");
        Matcher matcher = pattern.matcher(content);
        List<String> customFields = new ArrayList<>();
        while (matcher.find()) {
            String group = matcher.group();
            if (!customFields.contains(group.substring(2, group.length()-1))) {
                customFields.add(group.substring(2, group.length()-1));
            }
        }
        if (org.springframework.util.CollectionUtils.isEmpty(customFields)) {
            return content;
        }
        // 处理自定义字段
        Map<String, List<CustomField>> listMap = customFieldService.selectByNames(orgId, customFields);
        for (String field : customFields) {
            if (!listMap.containsKey(field)) {
                continue;
            }
            List<CustomField> customFieldList = listMap.get(field);
            for (CustomField customField : customFieldList) {
                String key = customField.getValue();
                if (fieldJson.containsKey(key)) {
                    String value = fieldJson.get(key);
                    if (StringUtils.isNotBlank(value)) {
                        content = content.replace("#{"+field+"}", value);
                    }
                    break;
                }
            }
        }
        // 处理系统添加的字段
        List<DictionaryEntity> caseBaseFields = i18nService.getListByKey(language, DictionaryConstant.CASE_BASE_FIELDS_KEY);
        for (String field : customFields) {
            for (DictionaryEntity entity : caseBaseFields) {
                if (Objects.equals(entity.getName(), field)) {
                    String key = entity.getValue();
                    if (fieldJson.containsKey(key)) {
                        String value = fieldJson.get(key);
                        if (StringUtils.isNotBlank(value)) {
                            if (StringUtils.isNotBlank(entity.getType()) && "Money".equals(entity.getType())) {
                                BigDecimal val = new BigDecimal(value).divide(new BigDecimal(1000));
                                content = content.replace("#{"+field+"}", val.toString());
                            } else {
                                content = content.replace("#{"+field+"}", value);
                            }
                        }
                        break;
                    }
                }
            }
        }

        List<DictionaryEntity> caseUserBaseFields = i18nService.getListByKey(language,DictionaryConstant.CASE_USER_BASE_FIELDS_KEY);
        for (String field : customFields) {
            for (DictionaryEntity entity : caseUserBaseFields) {
                if (Objects.equals(entity.getName(), field)) {
                    String key = entity.getValue();
                    if (fieldJson.containsKey(key)) {
                        String value = fieldJson.get(key);
                        if (StringUtils.isNotBlank(value)) {
                            if (StringUtils.isNotBlank(entity.getType()) && "Money".equals(entity.getType())) {
                                BigDecimal val = new BigDecimal(value).divide(new BigDecimal(1000));
                                content = content.replace("#{"+field+"}", val.toString());
                            } else {
                                content = content.replace("#{"+field+"}", value);
                            }
                        }
                        break;
                    }
                }
            }
        }
        List<DictionaryEntity> caseUpdateFields = i18nService.getListByKey(language,DictionaryConstant.CASE_UPDATE_FIELDS_KEY);
        for (String field : customFields) {
            for (DictionaryEntity entity : caseUpdateFields) {
                if (Objects.equals(entity.getName(), field)) {
                    String key = entity.getValue();
                    if (fieldJson.containsKey(key)) {
                        String value = fieldJson.get(key);
                        if (StringUtils.isNotBlank(value)) {
                            if (StringUtils.isNotBlank(entity.getType()) && "Money".equals(entity.getType())) {
                                BigDecimal val = new BigDecimal(value).divide(new BigDecimal(1000));
                                content = content.replace("#{"+field+"}", val.toString());
                            } else {
                                content = content.replace("#{"+field+"}", value);
                            }
                        }
                        break;
                    }
                }
            }
        }
        return content;
    }

    void encryptQueryData(ApplyMediateParam query) {
        if (!encryptProperties.getEnable()) {
            return;
        }
        List<String> names = query.getNames();
        if (!CollectionUtils.isEmpty(names)) {
            query.setNames(names.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
        }
        List<String> idCards = query.getIdCards();
        if (!CollectionUtils.isEmpty(idCards)) {
            query.setIdCards(idCards.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
        }
        List<String> mobiles = query.getMobiles();
        if (!CollectionUtils.isEmpty(mobiles)) {
            query.setMobiles(mobiles.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
        }
    }

    /**
     * 我的审批列表
     *
     * @param param      参数
     * @param orgId      组织id
     * @param userId     用户id
     * @param formFields 表单字段
     * @return {@link List}<{@link ApproveListVO}>
     */
    public List<ApproveListVO> approveList(ApproveListParam param, Long orgId, Long userId, String formFields) {
        String applyTimeStartStr = null;
        String applyTimeEndStr = null;
        String applyTime = param.getApplyTime();
        if (StrUtil.isNotBlank(applyTime)) {
            String[] range = applyTime.split(",");
            if (range.length == 2) {
                applyTimeStartStr = DateUtils.convertDate(range[0]);
                applyTimeEndStr = DateUtils.convertDate(range[1]);
            }
        }
        return mediateAuditMapper.approveList(param, orgId, userId, applyTimeStartStr, applyTimeEndStr, formFields);
    }

    /**
     * 审批待处理统计
     *
     * @param orgId        组织id
     * @param userId       用户id
     * @param businessType 业务类型
     * @return {@link Integer}
     */
    public Integer todoStatistics(Long orgId, Long userId, FlowEnums.BusinessType businessType) {
        return mediateAuditMapper.todoStatistics(orgId,userId,businessType.getCode());
    }

    /**
     * 超时记录ID
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link Long}>
     */
    public List<Long> timeOutRecordIds(Date startTime, Date endTime) {
        Example example = new Example(MediateAudit.class);
        example.selectProperties("id");
        Example.Criteria criteria = example.and();
        criteria.andEqualTo("state", MediateAuditEnums.State.ING.getCode())
                .andIsNotNull("outTime")
                .andGreaterThanOrEqualTo("outTime",startTime)
                .andLessThan("outTime",endTime);

        List<MediateAudit> recordes = selectByExample(example);
        List<Long> timeOutRecordIds = recordes.stream().map(MediateAudit::getId).collect(Collectors.toList());
        return timeOutRecordIds;
    }

    /**
     * 状态更新为超时
     *
     * @param applyIds 申请id
     */
    public void updateStateWithTimeOut(List<Long> applyIds) {
        if (ObjectUtil.isEmpty(applyIds)){
            return;
        }
        Example example = new Example(MediateAudit.class);
        Example.Criteria criteria = example.and();
        criteria.andIn("id", applyIds)
                .andEqualTo("state", MediateAuditEnums.State.ING.getCode());

        MediateAudit update = new MediateAudit();
        update.setState(MediateAuditEnums.State.TIMEOUT.getCode());
        update.setUpdateTime(new Date());

        updateByExampleSelective(update,example);
    }
}
