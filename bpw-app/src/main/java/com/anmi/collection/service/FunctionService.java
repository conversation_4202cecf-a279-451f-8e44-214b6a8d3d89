package com.anmi.collection.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.enums.CompanyEnums;
import com.anmi.collection.deploy.DeployStrategyHolder;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.duyan.OrgInfoTemp;
import com.anmi.collection.entity.response.sys.role.FunctionVO;
import com.anmi.collection.entity.response.sys.user.UserLoginVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.mapper.FunctionMapper;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.user.Function;
import com.anmi.domain.user.RoleFunction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by dongwang on 2018-12-03.
 */

@Service
public class FunctionService extends BaseService<Function> {
    @Autowired
    private I18nService i18nService;
    @Autowired
    private FunctionMapper functionMapper;
    @Autowired
    private UserService userService;
    @Autowired private DuyanManager duyanManager;
    @Autowired private RoleFunctionService roleFunctionService;
    @Autowired private DeployStrategyHolder deployStrategyHolder;

    public List<FunctionVO> getFunctionVO(List<Function> functions) {
        return functionTreeBuild(functions);
    }

    public List<Function> selectByIds(List<Long> ids) {
        Example example = new Example(Function.class);
        example.createCriteria().andIn("id", ids);
        List<Function> list = functionMapper.selectByExample(example);
        UserSession userSession = UserUtils.getTokenUser();
        i18nService.convertFunction(list, userSession.getLanguage());
        return list;
    }

    public List<FunctionVO> getAllFunctionVO(Integer roleLevel) {
        if (roleLevel == null) {
            throw new ApiException("需要传角色层级！");
        }
        UserSession userSession = UserUtils.getTokenUser();
        Function query = new Function();
        query.setStatus(0);
        if (userSession.getIsAmc() == null || userSession.getIsAmc() == 0) {
            query.setIsAmc(0);
        }
        if (Objects.equals(userSession.getIsBank(), CompanyEnums.BankAmc.YES.getCode())) {
            query.setIsBank(CompanyEnums.BankAmc.YES.getCode());
        } else {
            query.setIsBank(CompanyEnums.BankAmc.NO.getCode());
        }
        if (roleLevel == 2) {
            //总公司管理员层级
            query.setHeadLeaderOptional(Function.IsOptional.YES.getCode());
        } else if (roleLevel == 1) {
            //分公司管理员层级
            query.setBranchLeaderOptional(Function.IsOptional.YES.getCode());
        } else if (roleLevel == 0) {
            //小组管理员层级
            query.setTeamLeaderOptional(Function.IsOptional.YES.getCode());
        } else {
            // 未知
            throw new ApiException("未知的角色层级！");
        }
        List<Function> functions = super.select(query);

        Boolean isConnectDuyan = userService.isConnectDuyan(getOrgId());
        OrgInfoTemp orgInfo = null;
        try {
            orgInfo = duyanManager.getOrgInfo();
        }catch (Exception e){
            e.printStackTrace();
        }
        List<Long> excludeFunctionIds = excludeRobotFunctionIds(isConnectDuyan, orgInfo);
        UserLoginVO userVo = deployStrategyHolder.select().buildOrgSwitch(userSession.getOrgId(), new UserLoginVO());
        // 若工作手机未配置 话术配置菜单不显示
        if (ObjectUtil.notEqual(userVo.getWorkPhoneSwitch(), 1)){
            excludeFunctionIds.add(11082L);
        }
        if (ObjectUtil.isNotEmpty(excludeFunctionIds)){
            functions =  functions.stream().filter(function -> excludeFunctionIds.stream().noneMatch(exclude -> ObjectUtil.equals(function.getId(),exclude))).collect(Collectors.toList());
        }

        i18nService.convertFunction(functions, userSession.getLanguage());

        // 此处逻辑有点别扭，是为了复用 Mode.filterFunctionId 方法，所做的牺牲
        List<Long> functionIds = deployStrategyHolder.select().filterFunctionId(userSession.getOrgId(),functions.stream().map(Function::getId).collect(Collectors.toList()));

        functions = functions.stream().filter(function->functionIds.stream().anyMatch(functionId->ObjectUtil.equals(functionId,function.getId()))).collect(Collectors.toList());

        List<FunctionVO> functionVOS = functionTreeBuild(functions);
        return functionVOS;
    }

    public List<FunctionVO> agentFunctionList() {
        UserSession userSession = UserUtils.getTokenUser();
        Function query = new Function();
        query.setStatus(0);
        query.setIsAmc(0);
        query.setIsBank(CompanyEnums.BankAmc.NO.getCode());
        query.setBranchLeaderOptional(Function.IsOptional.YES.getCode());

        List<Function> functions = super.select(query);
        Boolean isConnectDuyan = userService.isConnectDuyan(getOrgId());
        OrgInfoTemp orgInfo = null;
        try {
            orgInfo = duyanManager.getOrgInfo();
        }catch (Exception e){
            e.printStackTrace();
        }
        Example example = new Example(Function.class);
        example.and().andEqualTo("agentVisible",1);
        List<Function> agentVisibleFunctions = functionMapper.selectByExample(example);
        List<Long> agentVisibleFunctionIds = agentVisibleFunctions.stream().map(Function::getId).collect(Collectors.toList());
        functions = functions.stream().filter(t -> agentVisibleFunctionIds.contains(t.getId())).collect(Collectors.toList());

        List<Long> excludeFunctionIds = excludeRobotFunctionIds(isConnectDuyan, orgInfo);
        if (ObjectUtil.isNotEmpty(excludeFunctionIds)){
            functions =  functions.stream().filter(function -> excludeFunctionIds.stream().noneMatch(exclude -> ObjectUtil.equals(function.getId(),exclude))).collect(Collectors.toList());
        }

        i18nService.convertFunction(functions, userSession.getLanguage());

        // 此处逻辑有点别扭，是为了复用 Mode.filterFunctionId 方法，所做的牺牲
        List<Long> functionIds = deployStrategyHolder.select().filterFunctionId(userSession.getOrgId(),functions.stream().map(Function::getId).collect(Collectors.toList()));

        functions = functions.stream().filter(function->functionIds.stream().anyMatch(functionId->ObjectUtil.equals(functionId,function.getId()))).collect(Collectors.toList());

        List<FunctionVO> functionVOS = functionTreeBuild(functions);
        return functionVOS;
    }

    /**
     * 组装function 树形结构
     *
     * @param funcs
     * @return
     */
    public List<FunctionVO> functionTreeBuild(List<Function> funcs) {
        Map<Long,String> userMap=userService.getNames(userService.getOrgId());
        //删选出子对象,按照parentId 分组
        Map<Long, List<FunctionVO>> children = funcs.stream().
                filter(l -> l.getParentId().intValue() != 0).map(l -> {
            return convertFunctionToVO(l,userMap);
        }).collect(Collectors.groupingBy(FunctionVO::getParentId));
        for (Map.Entry<Long, List<FunctionVO>> child : children.entrySet()) {
            List<FunctionVO> vos = child.getValue();
            vos = vos.stream().sorted(Comparator.comparing(FunctionVO::getSort)).collect(Collectors.toList());
            child.setValue(vos);
        }
        //删选出父对象
        List<FunctionVO> parents =
                funcs.stream().filter(l -> l.getParentId() == 0).map(l -> {
                    return convertFunctionToVO(l,userMap);
                }).collect(Collectors.toList());
        parents = parents.stream().sorted(Comparator.comparing(FunctionVO::getSort)).collect(Collectors.toList());
        //包装子节点
        build(children, parents);
        return parents;
    }

    private void build(Map<Long, List<FunctionVO>> children, List<FunctionVO> parents) {
        List<FunctionVO> childParents = new ArrayList<>();
        for (FunctionVO functionVO : parents) {
            List<FunctionVO> childFus = children.get(functionVO.getId());
            if (childFus == null) {
                continue;
            }
            functionVO.setChildren(childFus);
            childParents.addAll(childFus);
        }
        if (CollectionUtils.isEmpty(childParents)) {
            return;
        }
        build(children, childParents);
    }

    private FunctionVO convertFunctionToVO(Function function,Map<Long,String> userMap) {
        FunctionVO functionVO = new FunctionVO();
        BeanUtils.copyProperties(function, functionVO);
        functionVO.setCreateBy(userMap.get(function.getCreateBy()));
        functionVO.setUpdateBy(userMap.get(function.getUpdateBy()));
        return functionVO;
    }

    /**
     * 处理机器人功能菜单
     * 1、未开通度言 或 未开通老机器人 【显示 新机器人协催菜单项；剔除 老机器人协催菜单项】
     * 2、开通度言，开通老机器人，未开通新机器人 【显示 老机器人协催菜单项；剔除 新机器人协催菜单项】
     * 3、开通度言，开通老机器人，开通新机器人 【显示 新、老机器人协催菜单项；不做剔除】
     *
     * @param enableConnectDuyan 启用duyan
     * @param orgInfo            组织信息
     * @return {@link List}<{@link FunctionVO}>
     */
    public List<Long> excludeRobotFunctionIds(Boolean enableConnectDuyan, OrgInfoTemp orgInfo) {
        // 未开通度言 或 未开通老机器人 【显示 新机器人协催菜单项；剔除 老机器人协催菜单项】
        if (!enableConnectDuyan || (ObjectUtil.isNotNull(orgInfo) && ObjectUtil.notEqual(orgInfo.getIs_robot_enabled(), Boolean.TRUE))){
            return CollUtil.newArrayList(126L,26L,87L,88L);
        }

        // 开通度言，开通老机器人，未开通新机器人 【显示 老机器人协催菜单项；剔除 新机器人协催菜单项】
        if (enableConnectDuyan && ObjectUtil.isNotNull(orgInfo) && ObjectUtil.equals(orgInfo.getIs_robot_enabled(), Boolean.TRUE) && ObjectUtil.notEqual(orgInfo.getDispatch_robot_enabled(), Boolean.TRUE)){
            return CollUtil.newArrayList(845L,846L,847L);
        }

        return new ArrayList<>();
    }

    /**
     * 构建菜单
     *
     * @param roleId   角色id
     * @param excludeFunctionIds 需要排除的菜单id
     * @return {@link List}<{@link FunctionVO}>
     */
    public List<FunctionVO> buildMenu(Long roleId,List<Long> excludeFunctionIds) {
        List<Long> functionIds = getFunctionIds(roleId,excludeFunctionIds);
        if (ObjectUtil.isEmpty(functionIds)) {
            return new ArrayList<>();
        }
        List<Function> functions = selectByIds(functionIds);
        filterFuncList(functions);

        List<FunctionVO> functionVOList = getFunctionVO(functions);
        return functionVOList;
    }

    /**
     * 构建菜单
     *
     * @param userId   用户id
     * @param excludeFunctionIds 需要排除的菜单id
     * @return {@link List}<{@link FunctionVO}>
     */
    public List<FunctionVO> buildMenuByUserId(Long userId,List<Long> excludeFunctionIds) {
        List<Long> functionIds = getFunctionIdsByUserId(userId,excludeFunctionIds);
        if (ObjectUtil.isEmpty(functionIds)) {
            return new ArrayList<>();
        }
        List<Function> functions = selectByIds(functionIds);
        filterFuncList(functions);

        List<FunctionVO> functionVOList = getFunctionVO(functions);
        return functionVOList;
    }

    /**
     * 获取菜单功能id
     *
     * @param userId   用户id
     * @param excludeFunctionIds 需要排除的菜单id
     * @return {@link List}<{@link Long}>
     */
    private List<Long> getFunctionIdsByUserId(Long userId,List<Long> excludeFunctionIds) {
        // 查询权限
        List<Long> functionIds = functionMapper.selectByUserId(userId);

        if (ObjectUtil.isNotEmpty(excludeFunctionIds)){
            functionIds =  functionIds.stream().filter(functionId -> excludeFunctionIds.stream().noneMatch(excludeFunctionId -> ObjectUtil.equals(functionId,excludeFunctionId))).collect(Collectors.toList());
        }

        if (ObjectUtil.isEmpty(functionIds)){
            return new ArrayList<>();
        }

        return deployStrategyHolder.select().filterFunctionId(getOrgId(),functionIds);
    }


    /**
     * 获取菜单功能id
     *
     * @param roleId   角色id
     * @param excludeFunctionIds 需要排除的菜单id
     * @return {@link List}<{@link Long}>
     */
    private List<Long> getFunctionIds(Long roleId,List<Long> excludeFunctionIds) {
        // 查询权限
        RoleFunction roleFunction = new RoleFunction();
        roleFunction.setRoleId(roleId);
        roleFunction.setStatus(0);
        List<Long> functionIds =
                roleFunctionService.select(roleFunction).stream()
                        .map(RoleFunction::getFunctionId)
                        .collect(Collectors.toList());

        if (ObjectUtil.isNotEmpty(excludeFunctionIds)){
            functionIds =  functionIds.stream().filter(functionId -> excludeFunctionIds.stream().noneMatch(excludeFunctionId -> ObjectUtil.equals(functionId,excludeFunctionId))).collect(Collectors.toList());
        }

        if (ObjectUtil.isEmpty(functionIds)){
            return new ArrayList<>();
        }

        return deployStrategyHolder.select().filterFunctionId(getOrgId(),functionIds);
    }

    /**
     * 将status为-1的过滤掉
     *
     * @param functions
     */
    private void filterFuncList(List<Function> functions) {
        for (int i = functions.size() - 1; i >= 0; i--) {
            if (functions.get(i).getStatus().intValue() == -1) {
                functions.remove(i);
            }
        }
        UserSession userSession = UserUtils.getTokenUser();
        if (userSession.getIsAmc() == null || userSession.getIsAmc() == 0) {//如果是乙方公司，则需要过滤掉甲方公司特有的权限
            for (int i = functions.size() - 1; i >= 0; i--) {
                if (functions.get(i).getIsAmc() == 1) {
                    functions.remove(i);
                }
            }
        }
    }
}
