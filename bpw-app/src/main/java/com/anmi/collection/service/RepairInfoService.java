package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.PagingUtil;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.RepairInfoEnums;
import com.anmi.collection.common.enums.RepairTaskEnums;
import com.anmi.collection.entity.requset.repair.ApplyModel;
import com.anmi.collection.entity.requset.repair.RepairApplyEnums;
import com.anmi.collection.entity.requset.repair.RepairInfoQuery;
import com.anmi.collection.entity.requset.sys.user.RepairCreateModel;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.repair.Content;
import com.anmi.collection.entity.response.repair.RepVO;
import com.anmi.collection.entity.response.repair.RepairInfoListVO;
import com.anmi.collection.entity.response.repair.RepairInfoVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.mapper.CompanyMapper;
import com.anmi.collection.mapper.RepairInfoMapper;
import com.anmi.collection.utils.CommonUtil;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseInfoResult;
import com.anmi.domain.cases.CaseQueryResult;
import com.anmi.domain.user.Company;
import com.anmi.domain.user.RepairInfo;
import com.anmi.domain.user.RepairTask;
import com.google.common.collect.ImmutableList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by jianghp on 2019-04-17.
 */

@Service
public class RepairInfoService extends BaseService<RepairInfo> {

    @Autowired
    private RepairInfoMapper repairInfoMapper;
    @Autowired
    private DuyanManager duyanManager;
    @Autowired
    private CompanyMapper companyMapper;
    @Autowired
    private CaseService caseService;
    @Autowired
    private RepairTaskService repairTaskService;
    @Autowired
    private CaseMapper caseMapper;
    @Autowired
    private DeltService deltService;
    @Autowired
    private InnerBatchService innerBatchService;
    @Autowired
    private OutBatchService outBatchService;
    @Autowired
    private ProductService productService;
    @Autowired
    private UserService userService;

    @Transactional
    public void addRepair(RepairCreateModel model) {
        UserSession session = UserUtils.getTokenUser();
        Company company = companyMapper.selectByPrimaryKey(session.getOrgId());
        List<Content> contents = createContents(model.getCaseIds());
        RepairInfoVO vo = duyanManager.addInfoRepair(company.getDuyanReferId(), model.getName(),
                contents);
        if (vo == null) {
            throw new ApiException("创建信修任务失败！");
        }
        // 现在本地数据库创建记录
        updateRepairs(model, vo);
    }

    /**
     * 更新信修状态,同时创建任务，更新案件状态
     *
     * @param model
     * @param vo
     */
    private void updateRepairs(RepairCreateModel model, RepairInfoVO vo) {
        UserSession session = UserUtils.getTokenUser();
        Example caseExp = new Example(Case.class);
        Example.Criteria caseCrt = caseExp.createCriteria();
        caseCrt.andIn("id", model.getCaseIds());
        List<Case> cases = caseService.selectByExample(caseExp);
        List<RepairInfo> infos = new ArrayList<>();
        // 先创建信修任务RepairTask
        RepairTask task = new RepairTask();
        task.setName(model.getName());
        task.setOrgId(session.getOrgId());
        task.setState((byte) RepairTaskEnums.State.SUBMITTED.getCode());// 一开始默认状态
        task.setCreateTime(new Date());
        task.setUpdateTime(new Date());
        task.setCreateBy(session.getId().toString());
        task.setUpdateBy(session.getId().toString());
        task.setDuyanTaskId(vo.getTaskId());
        int res = repairTaskService.insertSelective(task);
        if (res != 1) {
            throw new ApiException("插入任务失败！");
        }
        cases.stream().forEach(ca -> {
            // 插入具体内容
            RepairInfo info = new RepairInfo();
            info.setCreateTime(new Date());
            info.setUpdateTime(new Date());
            info.setCaseId(ca.getId());
            info.setCreateBy(session.getId().toString());
            info.setUpdateBy(session.getId().toString());
            info.setIdCard(ca.getIdCard());
            info.setName(ca.getName());
            info.setTaskId(vo.getTaskId());
            info.setStatus((byte) RepairInfoEnums.Status.REPAIRING.getCode());
            infos.add(info);
            // 更新案件最新状态审核通过（修复中）
            ca.setRepairStatus((byte) RepairInfoEnums.Status.REPAIRING.getCode());
            int r = caseService.updateByPrimaryKeySelective(ca);
            if (r != 1) {
                throw new ApiException("更新案件最新信修状态失败！");
            }
        });
        res = repairInfoMapper.insertList(infos);
        if (res != infos.size()) {
            throw new ApiException("创建信修任务失败！");
        }
    }

    private List<Content> createContents(List<Long> caseIds) {
        if (CommonUtil.isEmpty(caseIds)) {
            return null;
        }
        Example caseExp = new Example(Case.class);
        Example.Criteria caseCrt = caseExp.createCriteria();
        caseCrt.andIn("id", caseIds);
        List<Case> cases = caseService.selectByExample(caseExp);
        // 不过滤，用户提交几个算几个
        // filterCase(cases);
        if (CommonUtil.isEmpty(cases)) {
            return null;
        }
        List<Content> contents = new ArrayList<>();
        cases.stream().forEach(ca -> {
            Content content = convertToContent(ca);
            contents.add(content);
        });
        return contents;
    }

    private Content convertToContent(Case ca) {
        Content content = new Content();
        content.setId_number(ca.getIdCard());
        content.setName(ca.getName());
        // content.setMobile("18770057694");
        return content;
    }

    /**
     * 申请信修，创建RepairInfo记录，状态为审核中
     *
     * @param model
     */
    @Transactional
    public void apply(ApplyModel model) {
        Example caseQuery = new Example(Case.class);
        Example.Criteria caseCrt = caseQuery.createCriteria();
        // caseCrt.andEqualTo("status", CaseEnums.Status.NORMAL.getCode());
        List<Long> caseIds = null;
        if (model.getAllSelect()) {
            // 全部筛选
            caseIds = filterCase();
        } else {
            caseIds = model.getCaseIds();
        }
        caseCrt.andIn("id", caseIds);
        List<Case> cases = caseService.selectByExample(caseQuery);
        if (!CommonUtil.isEmpty(cases)) {
            cases.stream().forEach(ca -> {
                // RepairInfo info = convert(ca, RepairInfoEnums.Status.审核中.getCode());
                ca.setRepairStatus((byte) RepairInfoEnums.Status.IN_AUDIT.getCode());
                ca.setUpdateTime(new Date());
                caseService.updateByPrimaryKeySelective(ca);
            });
        }
    }

    /**
     * 全部筛选结果：拿出全部的案件去申请信修
     *
     * @return
     */
    private List<Long> filterCase() {
        UserSession userSession = UserUtils.getTokenUser();
        List<CaseInfoResult> caseList = null;
        Map params = new HashMap();
        //选择全部
        //查询操作有所属团队用户编号
        params.put("orgIds", UserUtils.getOrgIds(userSession.getOrgId()));
        params.remove("status");
        params.put("allotStatusList", ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode()));
        params.put("caseStatusList", ImmutableList.of(CaseEnums.CaseStatus.NORMAL.getCode(), CaseEnums.CaseStatus.STOP.getCode()));

        caseList = caseMapper.queryCaseByParams(params);
        if (CommonUtils.isEmpty(caseList)) {
            throw new ApiException("请确定所选案子的状态！");
        }
        return caseList.stream().map(CaseInfoResult::getId).collect(Collectors.toList());
    }

    public RepairInfoListVO applyList(RepairInfoQuery param, PageParam pageParam) {
        //查询案件编号
        if (!StringUtils.isEmpty(param.getOutSerialNo()) && param.getOrgDeltId() != null) {
            param.setOutSerialNo(param.getOutSerialNo() + "#" + param.getOrgDeltId());
        }
        PageOutput<RepVO> repVOPageOutput = null;
        RepairInfoListVO pageOutput = null;
        List<CaseQueryResult> list = queryCaseQueryResultByCustomSql(param);
        // 筛选出符合状态的case
        List<RepVO> vos = filterCases(list, (int) param.getRepairStatus());
        repVOPageOutput = PagingUtil.createPageOutput(pageParam, vos);
        pageOutput = new RepairInfoListVO(pageParam.getPage(),
                pageParam.getLimit(), repVOPageOutput.getTotal(), repVOPageOutput.getList());
        return pageOutput;
    }

    private void convertVO(CaseQueryResult l, RepVO vo) {
        vo.setIdCard(l.getIdCard());
        vo.setCaseId(l.getId());
        vo.setOutSerialNo(l.getOutSerialNo().substring(0, l.getOutSerialNo().lastIndexOf("#")));
        vo.setOrgDeltName(deltService.getNames().get(l.getOrgDeltId()));
        vo.setBatchNo(innerBatchService.getNames().get(l.getInnerBatchId()));
        vo.setOutBatchNo(outBatchService.getNames().get(l.getOutBatchId()));
        vo.setProductName(productService.getNames().get(l.getProductId()));
        vo.setUserName(userService.getNames(l.getOrgId()).get(l.getUserId()));
        vo.setOrgDeltId(l.getOrgDeltId());
        vo.setCallType(l.getCallStatus());
        //特殊处理状态 增加state 需要兼容老的状态
    }

    private List<RepVO> filterCases(List<CaseQueryResult> list, Integer repairStatus) {
        //        RepairInfo query = new RepairInfo();
//        // 代表待审核状态
//        if (state != null) {
//            query.setStatus((byte) state.intValue());
//        }
        List<RepVO> vos = new ArrayList<>();
        RepairInfo infoQuery = new RepairInfo();
        for (int i = list.size() - 1; i >= 0; i--) {
            CaseQueryResult res = list.get(i);
            RepVO vo = new RepVO();
            BeanUtils.copyProperties(res, vo);
            Long caseId = res.getId();
            infoQuery.setCaseId(caseId);
            // 该案件的所有记录信息
            List<RepairInfo> infos = select(infoQuery);
            if (res.getRepairStatus() != null) {
                if (repairStatus == RepairApplyEnums.RepairStatus.审核通过.getCode()) {
                    if (res.getRepairStatus() == RepairInfoEnums.Status.REPAIR_SUCCESS.getCode()) {
                        // 根据caseId查询信修
                        filterRepInfos(infos, RepairInfoEnums.Status.REPAIR_SUCCESS.getCode());
                        vo.setRepairStatus((byte) RepairInfoEnums.Status.REPAIR_SUCCESS.getCode());
                    }
                    if (res.getRepairStatus() == RepairInfoEnums.Status.REPAIRING.getCode()) {
                        filterRepInfos(infos, RepairInfoEnums.Status.REPAIRING.getCode());
                        vo.setRepairStatus((byte) RepairInfoEnums.Status.REPAIRING.getCode());
                    }
                    if (res.getRepairStatus() == RepairInfoEnums.Status.REPAIR_FAIL.getCode()) {
                        filterRepInfos(infos, RepairInfoEnums.Status.REPAIR_FAIL.getCode());
                        vo.setRepairStatus((byte) RepairInfoEnums.Status.REPAIR_FAIL.getCode());
                    }
                    convertVO(res, vo);
                    infos.stream().forEach(info -> {
                        vo.setRepairStatus(info.getStatus());
                        vos.add(vo);
                    });
                } else if (repairStatus == RepairApplyEnums.RepairStatus.待审核.getCode()) {
                    // System.out.println("状态：" + res.getRepairStatus());
                    if (res.getRepairStatus() == RepairInfoEnums.Status.IN_AUDIT.getCode()) {
                        convertVO(res, vo);
                        vo.setRepairStatus(res.getRepairStatus());
                        vos.add(vo);
                    }
                } else if (repairStatus == RepairApplyEnums.RepairStatus.审核失败.getCode()) {
                    if (res.getRepairStatus() == RepairInfoEnums.Status.AUDIT_FAIL.getCode()) {
                        convertVO(res, vo);
                        vo.setRepairStatus(res.getRepairStatus());
                        vos.add(vo);
                    }
                }
            }
        }
        return vos;
    }

    private void filterRepInfos(List<RepairInfo> infos, int code) {
        infos = infos.stream().filter(info -> (int) info.getStatus() == code).collect(Collectors.toList());
    }

    List<CaseQueryResult> queryCaseQueryResultByCustomSql(RepairInfoQuery query) {
        Map map = JsonUtils.fromJson(JsonUtils.toJson(query), Map.class);
        map.remove("repairStatus");
        UserSession session = UserUtils.getTokenUser();
        map.put("orgId", session.getOrgId());
        map.put("depId", session.getDepId());
        return queryResult(map);
    }

    public List<CaseQueryResult> queryResult(Map map) {
        map.put("orderByTime", "orderByTime");
        return caseMapper.queryResult(map);
    }

    /**
     * 根据idCard查询信修结果
     *
     * @param idCard
     * @return
     */
    // 表合并修改
    public RepairInfo successInfos(String idCard) {
        Long orgId = UserUtils.getTokenUser().getOrgId();
        Case query = new Case();
        query.setOrgId(orgId);
        List<Case> rels = caseService.select(query);
        List<Long> caseIds = rels.stream().map(Case::getId).collect(Collectors.toList());
        Example repQuery = new Example(RepairInfo.class);
        Example.Criteria repCrt = repQuery.createCriteria();
        repCrt.andIn("caseId", caseIds);
        repCrt.andEqualTo("idCard", idCard);
        List<RepairInfo> locInfos = repairInfoMapper.selectByExample(repQuery);
        if (CommonUtil.isEmpty(locInfos)) {
            return null;
        }
        locInfos =
                locInfos.stream().filter(loc -> !StringUtils.isEmpty(loc.getMobile())).collect(Collectors.toList());
        if (CommonUtil.isEmpty(locInfos)) {
            return null;
        }
        locInfos =
                locInfos.stream().sorted(Comparator.comparing(RepairInfo::getUpdateTime)).collect(Collectors.toList());
        return locInfos.get(locInfos.size() - 1);
    }

    public void updateBatch(List<RepairInfo> newInfos) {
        if (CommonUtil.isEmpty(newInfos)) {
            return;
        }
        int res = repairInfoMapper.updateBatch(newInfos);
        if (res != 1) {
            throw new ApiException("批量更新信修任务详情失败！");
        }
    }
}
