package com.anmi.collection.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.CaseReductionEnums;
import com.anmi.collection.common.enums.flow.ApplyEnums;
import com.anmi.collection.common.enums.flow.FlowEnums;
import com.anmi.collection.common.enums.flow.FlowHandleRecordEnums;
import com.anmi.collection.configs.FileStoreConfig;
import com.anmi.collection.dto.fileStorage.UploadZipFileInfo;
import com.anmi.collection.encrypt.EncryptProperties;
import com.anmi.collection.encrypt.EncryptService;
import com.anmi.collection.entity.requset.cases.reduction.CaseReductionAuth;
import com.anmi.collection.entity.requset.cases.reduction.CaseReductionParam;
import com.anmi.collection.entity.requset.cases.reduction.ReductionAddParam;
import com.anmi.collection.entity.requset.cases.reduction.ReductionTaskCreate;
import com.anmi.collection.entity.requset.flow.ApproveListParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.ReductionVO;
import com.anmi.collection.entity.response.flow.ApproveListVO;
import com.anmi.collection.entity.response.flow.FlowNodeVO;
import com.anmi.collection.entity.response.flow.FlowVO;
import com.anmi.collection.mapper.ReductionMapper;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.service.flow.FlowService;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.CmUtil;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.*;
import com.anmi.domain.cases.ApplyReduction;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.ReductionQuery;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by dongwang on 2018-12-17.
 */
@Slf4j
@Service
public class ReductionService extends BaseService<ApplyReduction> {
    @Resource private ReductionMapper reductionMapper;
    @Resource private ProductService productService;
    @Resource private DeltService deltService;
    @Resource private UserService userService;
    @Resource private InnerBatchService innerBatchService;
    @Resource private CaseService caseService;
    @Resource private DepTeamService depTeamService;
    @Resource private OutBatchService outBatchService;
    @Resource private EncryptService encryptService;
    @Resource private EncryptProperties encryptProperties;
    @Resource private FlowService flowService;
    @Resource private FileStorageStrategyFactory fileStorageStrategyFactory;

    public PageOutput<ReductionVO> selectVOByPage(CaseReductionParam query) {
        if (encryptProperties.getEnable()) {
            List<String> nameList = query.getNames();
            if (!com.anmi.collection.utils.CollectionUtils.isEmpty(nameList)) {
                query.setNames(nameList.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
            }
        }
        UserSession session = getTokenUser();
        Map<String, Object> params = JsonUtils.fromJson(JsonUtils.toJson(query), Map.class);
        params.put("orgId", session.getOrgId());
        if (UserUtils.likeBranchAdmin()) {
            params.put("depId", session.getDepId());
        } else if (UserUtils.likeTeamLeader()) {
            params.put("teamId", session.getTeamId());
        }
        //参数转换
        if (query.getApplyTimeRange() != null) {
            String applyTimeStart = query.getApplyTimeRange().split(",")[0];
            String applyTimeEnd = query.getApplyTimeRange().split(",")[1];
            params.put("applyTimeStart", DateUtils.getStartTimeOfDate(new java.util.Date(Long.valueOf(applyTimeStart))));
            params.put("applyTimeEnd", DateUtils.getEndTimeOfDay(new java.util.Date(Long.valueOf(applyTimeEnd))));
        }
        PageParam pageParam = new PageParam();
        pageParam.setPage(query.getPage());
        pageParam.setLimit(query.getLimit());
        Page page = super.setPage(pageParam);
        List<ReductionQuery> list = reductionMapper.queryResult(params);
        Map<Long,String> depTeamMap = depTeamService.getNames();
        Map<Long,String> outBatchMap = outBatchService.getNames();
        List<ReductionVO> vos = new ArrayList<>();
        list.forEach(entity -> {
            ReductionVO vo= convertVO(entity);
            vo.setTeamName(depTeamMap.getOrDefault(vo.getTeamId(), null));
            vo.setDepName(depTeamMap.getOrDefault(vo.getDepId(), null));
            vo.setOutBatchNo(outBatchMap.getOrDefault(vo.getOutBatchId(), null));
            vos.add(vo);
        });

        PageOutput pageOutput = new PageOutput(page.getPageNum(), page.getPageSize(),
                page != null ? (int) page.getTotal() : vos.size(),
                vos);
        return pageOutput;
    }

    private ReductionVO convertVO(ReductionQuery query) {
        ReductionVO vo = new ReductionVO();
        BeanUtils.copyProperties(query, vo);
        vo.setOrgDeltName(deltService.getNames().get(query.getOrgDeltId()));
        vo.setProductName(productService.getNames().get(query.getProductId()));
        vo.setBatchName(innerBatchService.getNames().get(query.getInnerBatchId()));
        if (query.getAmount() != null && query.getReduceAmount() != null) {
            vo.setAfterReduceAmount(query.getAmount() - query.getReduceAmount());
        }
        if (StringUtils.isNotBlank(query.getOutSerialNo())) {
            vo.setOutSerialNo(query.getOutSerialNo().substring(0, query.getOutSerialNo().lastIndexOf("#")));
        }
        vo.setUserName(userService.getNames(userService.getOrgId()).get(query.getUserId()));
        vo.setCreateBy(userService.getNames(userService.getOrgId()).get(query.getCreateBy()));
        vo.setUpdateBy(userService.getNames(userService.getOrgId()).get(query.getUpdateBy()));
        vo.setAuditTime(query.getAuditTime()==null?null:query.getAuditTime().getTime());
        return vo;
    }

    /**
     * 减免审批
     *
     * @param auth auth
     * @return int
     */
    @Transactional(rollbackFor = Exception.class)
    public int auth(CaseReductionAuth auth) throws Exception{
        UserSession userSession = UserUtils.getTokenUser();
        List<Long> applyIds = auth.getRedIds();
        AssertUtil.notEmpty(applyIds,"请选择申请");
        AssertUtil.isTrue(applyIds.size()==1,"暂不支持批量审批");
        AssertUtil.isTrue(ObjectUtil.equals(auth.getStatus(),CaseReductionEnums.Status.PASS.getCode())||
                ObjectUtil.equals(auth.getStatus(),CaseReductionEnums.Status.FAIL.getCode()),"审批状态错误");

        Long applyId = applyIds.get(0);
        ApplyReduction applyRecord = selectByPrimaryKey(applyId);
        AssertUtil.notNull(applyRecord, "未发现申请");
        AssertUtil.isTrue(ObjectUtil.equals(applyRecord.getStatus(), CaseReductionEnums.Status.APPLY.getCode()), "申请非审批中");
        AssertUtil.notNull(applyRecord.getFlowId(), "审批流异常");

        Long flowId = applyRecord.getFlowId();
        Long applyUserId = applyRecord.getCreateBy();

        FlowHandleRecordEnums.HandleStatus approveStatus = ObjectUtil.equals(auth.getStatus(),CaseReductionEnums.Status.PASS.getCode())?FlowHandleRecordEnums.HandleStatus.PASS:FlowHandleRecordEnums.HandleStatus.REFUSE;
        flowService.execApprove(applyId,flowId,approveStatus,auth.getDesc(),userSession,applyUserId);

        return 1;
    }

    /**
     * 更新申请 处理状态
     *
     * @param applyId       申请id
     * @param approveStatus 审批状态
     * @param approveUserId 更新人
     * @throws Exception 例外
     */
    public void updateHandleStatus(Long applyId, FlowHandleRecordEnums.HandleStatus approveStatus,Long approveUserId) throws Exception{
        Integer status = ObjectUtil.equals(approveStatus,FlowHandleRecordEnums.HandleStatus.PASS)?CaseReductionEnums.Status.PASS.getCode():CaseReductionEnums.Status.FAIL.getCode();
        ApplyReduction applyRecord = new ApplyReduction();
        applyRecord.setId(applyId);
        applyRecord.setStatus(status);
        applyRecord.setUpdateBy(approveUserId);
        applyRecord.setUpdateTime(new Date());
        applyRecord.setAuditTime(new Date());
        updateByPrimaryKeySelective(applyRecord);
    }

    /**
     * 案件彻底物理删除，级联删除待审核的减免记录
     *
     * @param caseIds 案件ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void delReduction(List<Long> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }
        Example example = new Example(ApplyReduction.class);
        example.and().andIn("caseId", caseIds).andEqualTo("status", 0);
        reductionMapper.deleteByExample(example);
    }

    public Map<Long, Long> getReductionTotalMap(List<Long> caseIdList) {
        if (CollectionUtils.isEmpty(caseIdList)) {
            return new HashMap<>();
        }
        List<Map<String, Object>> mapList = reductionMapper.getReductionAmountTotalMap(caseIdList);
        Map<Long, Long> result = new HashMap<>();
        for (Map<String, Object> map : mapList) {
            result.put(Long.valueOf(map.get("caseId").toString()), Long.valueOf(map.get("reductionTotal").toString()));
        }
        return result;
    }

    /**
     * 添加减免
     * 管理员端提交减免不进行审批、催员端提交减免需进行审批
     *
     * @param reductionAddParam
     */
    @Transactional(rollbackFor = Exception.class)
    public void addReduction(ReductionAddParam reductionAddParam, MultipartFile[] files) throws Exception{
        UserSession userSession = UserUtils.getTokenUser();
        Case aCase = caseService.selectByPrimaryKey(reductionAddParam.getCaseId());
        FlowEnums.AgentType agentType = ObjectUtil.equals(aCase.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?FlowEnums.AgentType.OUTER:FlowEnums.AgentType.INNER;
        // 获取审批流
        FlowVO flowVO = flowService.flowChoice(userSession.getOrgId(), agentType, FlowEnums.BusinessType.JM);
        // 申请是否自动通过
        Boolean autoPassApply = flowService.autoPassApply(userSession,flowVO);

        ApplyReduction reduction = BeanUtil.copyProperties(reductionAddParam, ApplyReduction.class);
        reduction.setStatus(0);
        reduction.setReduceAmount(CmUtil.getAmountLong(reductionAddParam.getReduceAmount()));
        reduction.setDesc(reductionAddParam.getReduceDesc());
        reduction.setOrgId(userSession.getOrgId());
        reduction.setOrgDeltId(aCase.getOrgDeltId());
        reduction.setOutSerialTemp(aCase.getOutSerialTemp());
        reduction.setOutSerialNo(aCase.getOutSerialNo());
        reduction.setCreateBy(userSession.getId());
        reduction.setCreateTime(new Date());
        reduction.setUpdateTime(new Date());
        reduction.setUpdateBy(userSession.getId());
        reduction.setFlowId(flowVO.getId());
        reduction.setAgentType(ObjectUtil.equals(aCase.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?ApplyEnums.AgentType.OUTER.getCode():ApplyEnums.AgentType.INNER.getCode());
        // 附件
        if (!CommonUtils.isEmpty(files)) {
            String url = toZip(files, aCase.getOutSerialTemp());
            if (!StringUtils.isBlank(url)) {
                reduction.setVoucherUrl(url);
            }
        }
        if (autoPassApply){
            reduction.setStatus(1);
            reduction.setAuditTime(new Date());
            reduction.setApplyDesc("自动通过");
        }
        Integer timeLimitSwitch = flowVO.getTimeLimitSwitch();
        if (ObjectUtil.equals(timeLimitSwitch, FlowEnums.TimeLimitSwitch.YES.getCode())){
            String timeLimit = flowVO.getTimeLimit();
            String hour = StrUtil.subBefore(timeLimit, ":", true);
            String minute = StrUtil.subAfter(timeLimit, ":", true);
            Date outTime = DateUtil.offsetMinute(DateUtil.offsetHour(new Date(),Integer.valueOf(hour)),Integer.valueOf(minute));
            reduction.setOutTime(outTime);
        }
        insertSelective(reduction);
        List<FlowNodeVO> nodes = flowVO.getNodes();
        Map<String,Object> applyMap = cn.hutool.core.bean.BeanUtil.beanToMap(reduction);
        String parentUniqueCode = flowService.getApplyOrRuleNodeUniqueCode(nodes,applyMap,FlowEnums.BusinessType.JM,agentType,userSession);
        if (autoPassApply){
            flowService.findNextCopyNodeAndExecute(parentUniqueCode,flowVO, reduction.getId(),userSession);
        } else {
            flowService.findNextNodeAndExecute(parentUniqueCode,flowVO, reduction.getId(),userSession, userSession.getId());
        }
    }

    /**
     * 文件打包为zip包后进行上传
     *
     * @param files 文件列表
     * @param outSerialTemp 案件编号
     * @return
     */
    private String toZip(MultipartFile[] files, String outSerialTemp) {
        List<File> fileList = new ArrayList<>();
        for (MultipartFile multipartFile : files) {
            String fileName = multipartFile.getOriginalFilename();
            fileName = fileName.split("\\.")[0]
                            + "_"
                            + StringUtils.getRandomNumberBIT6()
                            + "."
                            + fileName.split("\\.")[1];
            File file = FileStoreConfig.convertToFile(fileName, multipartFile);
            fileList.add(file);
        }

        String rNumber = StringUtils.getRandomNumberBIT6();
        String fileRealName = "减免凭证-案件编号" + outSerialTemp + "-" + rNumber + ".zip";
        Date expireDate = DateUtils.addDays(new Date(), 10 * 365);
        UploadZipFileInfo uploadZipFileInfo = new UploadZipFileInfo();
        uploadZipFileInfo.setFiles(fileList)
                .setFileName(fileRealName)
                .setExpireDate(expireDate)
                .setBucket(systemConfig.getReductionBucket())
                .setLocalUrl(systemConfig.getReductionPath() + File.separator + fileRealName);
        FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
        return fileStorageStrategy.uploadFilesToZip(uploadZipFileInfo);
    }

    /**
     * 我的审批列表
     *
     * @param param      参数
     * @param orgId      组织id
     * @param userId     用户id
     * @param formFields 表单字段
     * @return {@link List}<{@link ApproveListVO}>
     */
    public List<ApproveListVO> approveList(ApproveListParam param, Long orgId, Long userId, String formFields) {
        String applyTimeStartStr = null;
        String applyTimeEndStr = null;
        String applyTime = param.getApplyTime();
        if (StrUtil.isNotBlank(applyTime)) {
            String[] range = applyTime.split(",");
            if (range.length == 2) {
                applyTimeStartStr = DateUtils.convertDate(range[0]);
                applyTimeEndStr = DateUtils.convertDate(range[1]);
            }
        }
        return reductionMapper.approveList(param, orgId, userId, applyTimeStartStr, applyTimeEndStr, formFields);
    }

    /**
     * 审批待处理统计
     *
     * @param orgId        组织id
     * @param userId       用户id
     * @param businessType 业务类型
     * @return {@link Integer}
     */
    public Integer todoStatistics(Long orgId, Long userId, FlowEnums.BusinessType businessType) {
        return reductionMapper.todoStatistics(orgId,userId,businessType.getCode());
    }

    public void encryptQueryData(ReductionTaskCreate query) {
        if (!encryptProperties.getEnable()) {
            return;
        }
        List<String> names = query.getNames();
        if (!com.anmi.collection.utils.CollectionUtils.isEmpty(names)) {
            query.setNames(names.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
        }
    }

    /**
     * 超时记录ID
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link Long}>
     */
    public List<Long> timeOutRecordIds(Date startTime, Date endTime) {
        Example example = new Example(ApplyReduction.class);
        example.selectProperties("id");
        Example.Criteria criteria = example.and();
        criteria.andEqualTo("status", CaseReductionEnums.Status.APPLY.getCode())
                .andIsNotNull("outTime")
                .andGreaterThanOrEqualTo("outTime",startTime)
                .andLessThan("outTime",endTime);

        List<ApplyReduction> recordes = selectByExample(example);
        List<Long> timeOutRecordIds = recordes.stream().map(ApplyReduction::getId).collect(Collectors.toList());
        return timeOutRecordIds;
    }

    /**
     * 状态更新为超时
     *
     * @param applyIds 申请id
     */
    public void updateStatusWithTimeOut(List<Long> applyIds) {
        if (ObjectUtil.isEmpty(applyIds)){
            return;
        }
        Example example = new Example(ApplyReduction.class);
        Example.Criteria criteria = example.and();
        criteria.andIn("id", applyIds)
                .andEqualTo("status",CaseReductionEnums.Status.APPLY.getCode());

        ApplyReduction update = new ApplyReduction();
        update.setStatus(CaseReductionEnums.Status.TIMEOUT.getCode());
        update.setUpdateTime(new Date());

        updateByExampleSelective(update,example);
    }
}
