package com.anmi.collection.service.external;

import cn.hutool.core.collection.CollectionUtil;
import com.anmi.collection.common.enums.ContactFilterTypeEnum;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.dto.CaseContact;
import com.anmi.collection.manager.jzjr.JzjrManager;
import com.anmi.collection.manager.jzjr.req.JzjrContactReq;
import com.anmi.collection.manager.jzjr.req.JzjrContractReq;
import com.anmi.collection.manager.jzjr.resp.JzjrContactResp;
import com.anmi.collection.manager.jzjr.resp.JzjrContractResp;
import com.anmi.collection.mapper.ContactsMapper;
import com.anmi.collection.service.CustomFieldService;
import com.anmi.collection.utils.CmUtil;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.sys.CustomField;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class JzjrStrategy extends AbstractExternalStrategy {
    @Resource
    private JzjrManager jzjrManager;
    @Resource
    private CustomFieldService customFieldService;
    @Resource
    private SystemConfig systemConfig;
    @Resource
    private ContactsMapper contactsMapper;

    private final static String REPAY_JUDGE = "已还款是否外呼";

    private final static String CONTACT_ID = "联系人id";

    @Override
    public String thirdParty() {
        return "jzjr";
    }

    @Override
    public List<Case> filter(List<Case> caseList, Map<Integer, List<CaseContact>> filterCaseContactsMap) {
        Long orgId = caseList.get(0).getOrgId();
        if (!checkSwitch(orgId)) {
            return Collections.emptyList();
        }

        if (systemConfig.getJzjrAccount() == null || !systemConfig.getJzjrAccount().equals(orgId)) {
            return Collections.emptyList();
        }
        // 需要过滤的数据
        List<Case> needFilterCaseList = new ArrayList<>();

        String repayJudgeName = null;
        String contCodeNameSync = "contractNo";
        String contCodeNameAsync = "contract_no";
        String contactIdName = null;
        Map<String, List<CustomField>> productTypeInfo = customFieldService.selectByNames(orgId, Arrays.asList(REPAY_JUDGE, CONTACT_ID));

        if (!CollectionUtil.isEmpty(productTypeInfo) && productTypeInfo.containsKey(CONTACT_ID)) {
            List<CustomField> customFields = productTypeInfo.get(CONTACT_ID);
            if (!CollectionUtils.isEmpty(customFields)) {
                contactIdName = customFields.get(0).getValue();
            }
        }

        // 先判断黑名单
        List<Long> contactIdList = new ArrayList<>();
        Map<Long, Case> caseContactMap = new HashMap<>();
        for (Case aCase : caseList) {
            if (!CollectionUtil.isEmpty(aCase.getFieldJson()) && aCase.getFieldJson().containsKey(contactIdName)) {
                contactIdList.add(Long.valueOf(aCase.getFieldJson().get(contactIdName)));
                caseContactMap.put(Long.valueOf(aCase.getFieldJson().get(contactIdName)), aCase);
            }
        }


        if (!CollectionUtil.isEmpty(contactIdList)) {
            List<List<Long>> splitContactList = CmUtil.splitList(contactIdList, 200);
            List<JzjrContactResp> validContCodeList = new ArrayList<>();
            List<Long> validContactList = new ArrayList<>();
            for (List<Long> subContactList : splitContactList) {
                JzjrContactReq req = new JzjrContactReq();
                req.setContactIds(subContactList);
                req.setApiKey(systemConfig.getJzjrApiKey());
                List<JzjrContactResp> subValidContactList = jzjrManager.queryBlackList(req);
                if (CollectionUtils.isEmpty(subValidContactList)) {
                    // 调用吉致接口异常，需要将这一批默认为黑名单-不打电话
                    validContactList.addAll(subContactList);
                    continue;
                }
                validContCodeList.addAll(subValidContactList);
            }

            List<Long> validContactIds = validContCodeList.stream().filter(e -> e.getIsBlack().equals(1)).map(JzjrContactResp::getContactId).collect(Collectors.toList());
            validContactList.addAll(validContactIds);

            List<Case> blackCaseList = new ArrayList<>();
            for (Long validContact : validContactList) {
                Case caseInfo = caseContactMap.get(validContact);
                if (caseInfo != null) {
                    blackCaseList.add(caseInfo);
                    needFilterCaseList.add(caseInfo);
                }
            }

            if (!CollectionUtils.isEmpty(blackCaseList)) {
                // 将数据存到过滤表中
                List<Long> caseIds = blackCaseList.stream().map(Case::getId).collect(Collectors.toList());
                List<CaseContact> caseContactsAll = contactsMapper.selectContactByCaseIds(caseIds);
                // 吉致不打本人电话
                List<CaseContact> contactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 0)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(contactsList)) {
                    filterCaseContactsMap.put(ContactFilterTypeEnum.BLACK_LIST.getCode(), contactsList);
                }

                // caseList移除黑名单数据
                Set<Long> blackCaseIds = blackCaseList.stream().map(Case::getId).collect(Collectors.toSet());
                caseList.removeIf(obj -> blackCaseIds.contains(obj.getId()));
            }
        }

        if (!CollectionUtil.isEmpty(productTypeInfo) && productTypeInfo.containsKey(REPAY_JUDGE)) {
            List<CustomField> customFields = productTypeInfo.get(REPAY_JUDGE);
            if (!CollectionUtils.isEmpty(customFields)) {
                repayJudgeName = customFields.get(0).getValue();
            }
        }

        // 查询自定义字段，为是的提取合同号
        List<String> contractNoList = new ArrayList<>();
        Map<String, Case> caseContMap = new HashMap<>();
        for (Case aCase : caseList) {
            if (!CollectionUtil.isEmpty(aCase.getFieldJson()) && aCase.getFieldJson().containsKey(repayJudgeName)
                    && "否".equals(aCase.getFieldJson().get(repayJudgeName))) {
                contractNoList.add(aCase.getFieldJson().get(contCodeNameSync) == null ? aCase.getFieldJson().get(contCodeNameAsync) : aCase.getFieldJson().get(contCodeNameSync));
                caseContMap.put(aCase.getFieldJson().get(contCodeNameSync) == null ? aCase.getFieldJson().get(contCodeNameAsync) : aCase.getFieldJson().get(contCodeNameSync), aCase);
            }
        }

        if (!CollectionUtils.isEmpty(contractNoList)) {
            List<List<String>> splitList = CmUtil.splitList(contractNoList, 200);
            List<JzjrContractResp> validContCodeList = new ArrayList<>();
            // 已还款的合同号
            List<String> validContCodes = new ArrayList<>();
            for (List<String> list : splitList) {
                JzjrContractReq req = new JzjrContractReq();
                req.setContCodes(list);
                req.setApiKey(systemConfig.getJzjrApiKey());
                List<JzjrContractResp> subValidContCodeList = jzjrManager.queryContractRepayStatus(req);
                if (CollectionUtils.isEmpty(subValidContCodeList)) {
                    // 调用吉致接口异常，需要将这一批默认为已还款-不打电话
                    validContCodes.addAll(list);
                    continue;
                }
                validContCodeList.addAll(subValidContCodeList);
            }

            List<String> returnValidContCodes = validContCodeList.stream().filter(e -> e.getRepay()).map(JzjrContractResp::getContCode).collect(Collectors.toList());
            validContCodes.addAll(returnValidContCodes);
            List<Case> hasPaidCaseList = new ArrayList<>();
            for (String validContCode : validContCodes) {
                Case caseInfo = caseContMap.get(validContCode);
                if (caseInfo != null) {
                    hasPaidCaseList.add(caseInfo);
                    needFilterCaseList.add(caseInfo);
                }
            }

            if (!CollectionUtils.isEmpty(hasPaidCaseList)) {
                // 将数据存到过滤表中
                List<Long> caseIds = hasPaidCaseList.stream().map(Case::getId).collect(Collectors.toList());
                List<CaseContact> caseContactsAll = contactsMapper.selectContactByCaseIds(caseIds);
                // 吉致不打本人电话
                List<CaseContact> contactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 0)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(contactsList)) {
                    filterCaseContactsMap.put(ContactFilterTypeEnum.PAID.getCode(), contactsList);
                }
            }
        }

        return needFilterCaseList;
    }


}
