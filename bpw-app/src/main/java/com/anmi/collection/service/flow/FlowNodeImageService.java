package com.anmi.collection.service.flow;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.enums.flow.FlowEnums;
import com.anmi.collection.common.enums.flow.FlowHandleRecordEnums;
import com.anmi.collection.common.enums.flow.FlowNodeEnums;
import com.anmi.collection.common.enums.flow.FlowNodeImageEnums;
import com.anmi.collection.entity.response.flow.*;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.domain.flow.FlowHandleRecord;
import com.anmi.domain.flow.FlowNodeImage;
import com.anmi.domain.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FlowNodeImageService extends BaseService<FlowNodeImage> {

    @Resource private FlowHandleRecordService flowHandleRecordService;

    /**
     * 执行规则节点
     *
     * @param flowNodeVO   审批流节点
     * @param businessType 业务类型
     * @param applyId      申请id
     */
    public void executeRule(FlowNodeVO flowNodeVO, Integer businessType, Long applyId) {
        log.info("执行规则节点,flowNode:{},applyId:{}",flowNodeVO.getFlowId(),applyId);
        if (ObjectUtil.notEqual(flowNodeVO.getType(), FlowNodeEnums.Type.RULE.getCode())){
            return;
        }

        log.info("新增规则节点镜像,applyId:{},businessType:{},flowNode:{}",applyId,businessType, flowNodeVO.getId());
        FlowNodeImage flowNodeImage = new FlowNodeImage();
        flowNodeImage.setOrgId(flowNodeVO.getOrgId());
        flowNodeImage.setApplyId(applyId);
        flowNodeImage.setBusinessType(businessType);
        flowNodeImage.setFlowNodeId(flowNodeVO.getId());
        flowNodeImage.setFlowNodeName(flowNodeVO.getName());
        flowNodeImage.setFlowNodeType(flowNodeVO.getType());
        flowNodeImage.setFlowNodeStep(flowNodeVO.getStep());
        flowNodeImage.setUniqueCode(flowNodeVO.getUniqueCode());
        flowNodeImage.setParentUniqueCode(flowNodeVO.getParentUniqueCode());
        flowNodeImage.setConfigJson(JSONUtil.toJsonStr(flowNodeVO.getNodeConfig()));
        flowNodeImage.setHandleStatus(FlowNodeImageEnums.HandleStatus.PASS.getCode());
        insertSelective(flowNodeImage);
    }

    /**
     * 执行抄送
     *
     * @param flowNodeVO   审批流节点
     * @param businessType 业务类型
     * @param applyId      申请id
     */
    public void executeCopy(FlowNodeVO flowNodeVO, Integer businessType, Long applyId) {
        log.info("执行抄送,flowNode:{},applyId:{}",flowNodeVO.getFlowId(),applyId);
        if (ObjectUtil.notEqual(flowNodeVO.getType(), FlowNodeEnums.Type.COPY.getCode())){
            return;
        }

        log.info("新增抄送节点镜像,applyId:{},businessType:{},flowNode:{}",applyId,businessType, flowNodeVO.getId());
        FlowNodeImage flowNodeImage = new FlowNodeImage();
        flowNodeImage.setOrgId(flowNodeVO.getOrgId());
        flowNodeImage.setApplyId(applyId);
        flowNodeImage.setBusinessType(businessType);
        flowNodeImage.setFlowNodeId(flowNodeVO.getId());
        flowNodeImage.setFlowNodeName(flowNodeVO.getName());
        flowNodeImage.setFlowNodeType(flowNodeVO.getType());
        flowNodeImage.setFlowNodeStep(flowNodeVO.getStep());
        flowNodeImage.setUniqueCode(flowNodeVO.getUniqueCode());
        flowNodeImage.setParentUniqueCode(flowNodeVO.getParentUniqueCode());
        flowNodeImage.setConfigJson(JSONUtil.toJsonStr(flowNodeVO.getNodeConfig()));
        flowNodeImage.setHandleStatus(FlowNodeImageEnums.HandleStatus.COPY.getCode());
        insertSelective(flowNodeImage);

        List<NodeConfigVO.User> users = flowNodeVO.getNodeConfig().getCopyNodeConfig().getUsers();

        List<FlowHandleRecord> flowHandleRecords = users.stream().map(user -> {
            Date now = new Date();
            FlowHandleRecord flowHandleRecord = new FlowHandleRecord();
            flowHandleRecord.setFlowNodeImageId(flowNodeImage.getId());
            flowHandleRecord.setUserId(user.getId());
            flowHandleRecord.setUserName(user.getName());
            flowHandleRecord.setHandleStatus(FlowHandleRecordEnums.HandleStatus.COPY.getCode());
            flowHandleRecord.setHandleTime(now);
            flowHandleRecord.setStatus(FlowHandleRecordEnums.Status.EFFECTIVE.getCode());
            flowHandleRecord.setTransmitFlag(FlowHandleRecordEnums.TransmitFlag.NO.getCode());
            flowHandleRecord.setAutoPassFlag(FlowHandleRecordEnums.AutoPassFlag.NO.getCode());
            flowHandleRecord.setCreateTime(now);
            flowHandleRecord.setUpdateTime(now);
            return flowHandleRecord;
        }).collect(Collectors.toList());

        flowHandleRecordService.insertBatch(flowHandleRecords);
    }

    /**
     * 获取审批流节点镜像
     *
     * @param orgId        公司id
     * @param applyId      申请id
     * @param businessType 业务类型
     * @param flowNodeId   审批流节点id
     * @return {@link List}<{@link FlowNodeImageVO}>
     */
    public List<FlowNodeImageVO> getFlowNodeImages(Long orgId, Long applyId, Integer businessType, Long flowNodeId) {
        AssertUtil.notNull(orgId,"公司id为空");
        AssertUtil.notNull(applyId,"申请id为空");
        AssertUtil.notNull(businessType,"业务类型为空");
        Example example = new Example(FlowNodeImage.class);
        example.orderBy("createTime").asc();
        Example.Criteria criteria = example.and();
        criteria.andEqualTo("orgId",orgId)
                .andEqualTo("applyId",applyId)
                .andEqualTo("businessType",businessType);
        if (ObjectUtil.isNotNull(flowNodeId)){
            criteria.andEqualTo("flowNodeId",flowNodeId);
        }
        List<FlowNodeImage> flowNodeImages = selectByExample(example);
        if (ObjectUtil.isEmpty(flowNodeImages)){
            return new ArrayList<>();
        }

        List<Long> flowNodeImageIds = flowNodeImages.stream().map(FlowNodeImage::getId).collect(Collectors.toList());
        List<FlowHandleRecordVO> flowHandleRecordVOs = flowHandleRecordService.getFlowHandleRecords(flowNodeImageIds);

        return flowNodeImages.stream().map(flowNodeImage->{
            FlowNodeImageVO flowNodeImageVO = BeanUtil.copyProperties(flowNodeImage, FlowNodeImageVO.class);
            String configJson = flowNodeImage.getConfigJson();
            if (StrUtil.isNotBlank(configJson)) {
                NodeConfigVO nodeConfigVO = JSONUtil.toBean(configJson, NodeConfigVO.class);
                flowNodeImageVO.setNodeConfig(nodeConfigVO);
                if (ObjectUtil.equals(flowNodeImage.getFlowNodeType(), FlowNodeEnums.Type.APPROVAL.getCode())){
                    NodeConfigVO.Approve approve = nodeConfigVO.getApproveNodeConfig().getApprove();
                    flowNodeImageVO.setApproveType(approve.getApproveType());
                    flowNodeImageVO.setApproveModel(approve.getModel());
                }
            }
            List<FlowHandleRecordVO> flowHandleRecords = flowHandleRecordVOs.stream().filter(p -> ObjectUtil.equals(flowNodeImage.getId(), p.getFlowNodeImageId())).collect(Collectors.toList());
            flowNodeImageVO.setFlowHandleRecords(flowHandleRecords);
            return flowNodeImageVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取处理中的流节点镜像
     *
     * @param applyIds     申请id
     * @param businessType 业务类型
     * @return {@link List}<{@link Long}>
     */
    public List<Long> getIngFlowNodeImageIds(List<Long> applyIds, FlowEnums.BusinessType businessType) {
        if (ObjectUtil.isEmpty(applyIds) || ObjectUtil.isNull(businessType)){
            return new ArrayList<>();
        }
        Example example = new Example(FlowNodeImage.class);
        example.selectProperties("id");
        example.orderBy("createTime").asc();
        Example.Criteria criteria = example.and();
        criteria.andIn("applyId",applyIds)
                .andEqualTo("businessType",businessType.getCode())
                .andEqualTo("handleStatus", FlowNodeImageEnums.HandleStatus.ING.getCode());

        List<FlowNodeImage> flowNodeImages = selectByExample(example);
        List<Long> flowNodeImageIds = flowNodeImages.stream().map(FlowNodeImage::getId).collect(Collectors.toList());
        return flowNodeImageIds;
    }

    /**
     * 更新节点镜像 处理状态
     *
     * @param flowNodeImageId 节点镜像id
     * @param handleStatus    处理状态
     */
    public void updateHandleStatus(Long flowNodeImageId, Integer handleStatus) {
        if (ObjectUtil.isNull(flowNodeImageId)){
            return;
        }
        FlowNodeImage flowNodeImage = new FlowNodeImage();
        flowNodeImage.setId(flowNodeImageId);
        flowNodeImage.setHandleStatus(handleStatus);
        updateByPrimaryKeySelective(flowNodeImage);
    }

    /**
     * 更新节点镜像 处理状态
     *
     * @param flowNodeImageIds 节点镜像id
     * @param handleStatus     处理状态
     */
    public void updateHandleStatus(List<Long> flowNodeImageIds, Integer handleStatus) {
        if (ObjectUtil.isEmpty(flowNodeImageIds)){
            return;
        }
        Example example = new Example(FlowNodeImage.class);
        Example.Criteria criteria = example.and();
        criteria.andIn("id",flowNodeImageIds);

        FlowNodeImage flowNodeImage = new FlowNodeImage();
        flowNodeImage.setHandleStatus(handleStatus);

        updateByExampleSelective(flowNodeImage,example);
    }

    /**
     * 更新审批层级
     *
     * @param flowNodeImageId 节点镜像id
     * @param flowNodeLevel   流节点级别
     */
    public void updateNextLevel(Long flowNodeImageId, Integer flowNodeLevel) {
        FlowNodeImage imageUpdate = new FlowNodeImage();
        imageUpdate.setId(flowNodeImageId);
        imageUpdate.setFlowNodeLevel(flowNodeLevel);
        updateByPrimaryKeySelective(imageUpdate);

        flowHandleRecordService.updateNextLevelRecord(flowNodeImageId, flowNodeLevel);
    }

    /**
     * 新增审批节点镜像
     *
     * @param flowVO     审批流
     * @param applyId    申请id
     * @param flowNodeVO 流节点
     * @param approve    审批配置
     * @param applyUser  申请用户
     * @return {@link FlowNodeImage}
     */
    public FlowNodeImage insertApproveNodeImage(FlowVO flowVO, Long applyId, FlowNodeVO flowNodeVO, NodeConfigVO.Approve approve, User applyUser) {
        log.info("新增审批节点镜像,applyId:{},businessType:{},flowNode:{}",applyId,flowVO.getBusinessType(), flowNodeVO.getId());
        FlowNodeImage image = new FlowNodeImage();
        image.setOrgId(flowVO.getOrgId());
        image.setApplyId(applyId);
        image.setBusinessType(flowVO.getBusinessType());
        image.setFlowNodeId(flowNodeVO.getId());
        image.setFlowNodeName(flowNodeVO.getName());
        image.setFlowNodeType(flowNodeVO.getType());
        image.setFlowNodeStep(flowNodeVO.getStep());
        if (ObjectUtil.equals(approve.getApproveType(),FlowNodeEnums.ApproveType.LOOP.getCode())){
            Integer level = FlowNodeEnums.LoopLevel.DEP.getCode();
            if (ObjectUtil.isNotNull(applyUser.getTeamId())&&ObjectUtil.notEqual(applyUser.getTeamId(),0)){
                level = FlowNodeEnums.LoopLevel.DEP.getCode();
            } else if (ObjectUtil.isNotNull(applyUser.getDepId())&&ObjectUtil.notEqual(applyUser.getDepId(),0)){
                level = FlowNodeEnums.LoopLevel.AGENT.getCode();
            } else if (ObjectUtil.isNotNull(applyUser.getOrgId())){
                level = FlowNodeEnums.LoopLevel.ORG.getCode();
            }
            image.setFlowNodeLevel(level);
        }
        image.setUniqueCode(flowNodeVO.getUniqueCode());
        image.setParentUniqueCode(flowNodeVO.getParentUniqueCode());
        image.setConfigJson(JSONUtil.toJsonStr(flowNodeVO.getNodeConfig()));
        insertSelective(image);
        return image;
    }

    /**
     * 状态更新为超时
     *
     * @param flowNodeImageIds 流节点镜像id
     */
    public void updateHandleStatusWithTimeOut(List<Long> flowNodeImageIds) {
        if (ObjectUtil.isEmpty(flowNodeImageIds)){
            return;
        }
        Example example = new Example(FlowNodeImage.class);
        Example.Criteria criteria = example.and();
        criteria.andIn("id", flowNodeImageIds)
                .andEqualTo("handleStatus", FlowNodeImageEnums.HandleStatus.ING.getCode());

        FlowNodeImage update = new FlowNodeImage();
        update.setHandleStatus(FlowNodeImageEnums.HandleStatus.TIMEOUT.getCode());
        update.setUpdateTime(new Date());

        updateByExampleSelective(update,example);
    }
}
