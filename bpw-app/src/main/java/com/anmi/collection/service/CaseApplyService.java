package com.anmi.collection.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.CaseApplyEnums;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.DepTeamEnums;
import com.anmi.collection.common.enums.flow.ApplyEnums;
import com.anmi.collection.common.enums.flow.FlowEnums;
import com.anmi.collection.common.enums.flow.FlowHandleRecordEnums;
import com.anmi.collection.dto.EndInfoDTO;
import com.anmi.collection.encrypt.EncryptProperties;
import com.anmi.collection.encrypt.EncryptService;
import com.anmi.collection.entity.requset.cases.CaseApplyAddParam;
import com.anmi.collection.entity.requset.cases.CaseApplyQueryParam;
import com.anmi.collection.entity.requset.cases.CaseApplyStatusParam;
import com.anmi.collection.entity.requset.cases.CaseSwitchStatusParam;
import com.anmi.collection.entity.requset.flow.ApproveListParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.CaseApplyVO;
import com.anmi.collection.entity.response.flow.ApproveListVO;
import com.anmi.collection.entity.response.flow.FlowNodeVO;
import com.anmi.collection.entity.response.flow.FlowVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.CaseApplyMapper;
import com.anmi.collection.service.flow.FlowService;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.*;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseApply;
import com.anmi.domain.cases.CaseApplyInfo;
import com.anmi.domain.cases.CaseQueryResult;
import com.anmi.domain.user.DepTeam;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CaseApplyService extends BaseService<CaseApply> {

  @Resource private CaseService caseService;
  @Resource private UserService userService;
  @Resource private DeltService deltService;
  @Resource private ProductService productService;
  @Resource private CaseApplyMapper caseApplyMapper;
  @Resource private DepTeamService depTeamService;
  @Resource private OutBatchService outBatchService;
  @Resource private EncryptProperties encryptProperties;
  @Resource private EncryptService encryptService;
  @Resource private FlowService flowService;

  public PageOutput list(CaseApplyQueryParam query) throws Exception {
    if (encryptProperties.getEnable()) {
      List<String> nameList = query.getNameList();
      if (!CollectionUtils.isEmpty(nameList)) {
        query.setNameList(nameList.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
      }
    }
    PageParam pageParam = BeanUtil.copyProperties(query, PageParam.class);
    UserSession userSession = getTokenUser();
    Map<String, Object> params = new HashMap<>();
    if (query.getAction() == null) {
      throw new ApiException("action不能为空");
    }
    if (query.getAction() == 0) {
      // 催员单个案件申请列表
      Long caseId = query.getCaseId();
      if (caseId == null) {
        throw new ApiException("案件编号不能为空");
      }
      params.put("caseId", caseId);
    } else if (query.getAction() == 1) {
      // 催员所有案件申请列表
      params.put("applyBy", userSession.getId());
    } else if (query.getAction() == 2) {
      // 管理员审核页面
      params.put(
          "statuses",
          StringUtils.join(
              Lists.newArrayList(
                  CaseApplyEnums.Status.ING.getCode(),
                  CaseApplyEnums.Status.PASS.getCode(),
                  CaseApplyEnums.Status.REFUSE.getCode(),
                  CaseApplyEnums.Status.TIMEOUT.getCode()),
              ","));
      if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
        params.put("depId", userSession.getDepId());
      } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
        params.put("teamId", userSession.getTeamId());
      }
    } else {
      throw new ApiException("action值不对");
    }
    // 参数转换
    params.putAll(JsonUtils.fromJson(JsonUtils.toJson(query), Map.class));
    params.put("orgId", getTokenUser().getOrgId());
    if (query.getApplyTimeRange() != null) {
      String applyTimeStart = query.getApplyTimeRange().split(",")[0];
      String applyTimeEnd = query.getApplyTimeRange().split(",")[1];
      params.put(
          "applyTimeStart", DateUtils.getStartTimeOfDate(new Date(Long.valueOf(applyTimeStart))));
      params.put("applyTimeEnd", DateUtils.getEndTimeOfDay(new Date(Long.valueOf(applyTimeEnd))));
    }
    Page page = super.setPage(pageParam);
    List<CaseApplyInfo> applyInfoList = caseApplyMapper.selectApplyListByMap(params);
    if (CollectionUtils.isEmpty(applyInfoList)) {
      return new PageOutput(page.getPageNum(), page.getPageSize(), 0, new ArrayList());
    }
    List<Long> caseIds = applyInfoList.stream().map(CaseApplyInfo::getCaseId).collect(Collectors.toList());
    List<Case> caseList = caseService.selectByIdList(caseIds);
    Map<Long,Case> caseMap = caseList.stream().collect(Collectors.toMap(Case::getId, Function.identity()));

    List<Long> depIds = caseList.stream().map(Case::getDepId).collect(Collectors.toList());
    List<DepTeam> depList = depTeamService.selectByIdList(depIds);
    Map<Long,DepTeam> depMap = depList.stream().collect(Collectors.toMap(DepTeam::getId, Function.identity()));

    Map<Long,String> outBatchMap = outBatchService.getNames();
    Map<Long,String> depTeamMap = depTeamService.getNames();
    Map<Long,String> deltMap = deltService.getNames();
    Map<Long,String> userMap = userService.getNames(userSession.getOrgId());
    Map<Long,String> productMap = productService.getNames();

    List<CaseApplyVO> applyVOList = new ArrayList<>();
    if (!CollectionUtils.isEmpty(applyInfoList)) {
      for (CaseApplyInfo caseApplyInfo : applyInfoList) {
        CaseApplyVO vo = new CaseApplyVO();
        BeanUtils.copyProperties(caseApplyInfo, vo);
        vo.setApplyByName(userMap.get(caseApplyInfo.getApplyBy()));
        vo.setAuditByName(caseApplyInfo.getAuditBy() == null ? null : userMap.get(caseApplyInfo.getAuditBy()));
        vo.setOrgDeltName(deltMap.get(caseApplyInfo.getOrgDeltId()));
        vo.setProductName(productMap.get(caseApplyInfo.getProductId()));
        vo.setUserName(userMap.get(caseApplyInfo.getUserId()));
        // 案件状态
        vo.setCaseStatus(caseApplyInfo.getCaseStatus());
        vo.setIdCard(caseMap.get(caseApplyInfo.getCaseId()) == null ? null : caseMap.get(caseApplyInfo.getCaseId()).getIdCard());
        vo.setAmount(caseMap.get(caseApplyInfo.getCaseId()) == null ? null : caseMap.get(caseApplyInfo.getCaseId()).getAmount());
        vo.setDepId(caseMap.get(caseApplyInfo.getCaseId()) == null ? null : caseMap.get(caseApplyInfo.getCaseId()).getDepId());
        vo.setTeamId(caseMap.get(caseApplyInfo.getCaseId()) == null ? null : caseMap.get(caseApplyInfo.getCaseId()).getTeamId());
        vo.setOutBatchId(caseMap.get(caseApplyInfo.getCaseId()) == null ? null : caseMap.get(caseApplyInfo.getCaseId()).getOutBatchId());
        vo.setDepName(vo.getDepId() == null ? null : depTeamMap.get(vo.getDepId()));
        vo.setTeamName(vo.getTeamId() == null ? null : depTeamMap.get(vo.getTeamId()));
        vo.setOutBatchNo(vo.getOutBatchId() == null ? null : outBatchMap.get(vo.getOutBatchId()));
        vo.setAllotAgent(vo.getDepId() == null ? null :  (Objects.equals(depMap.get(vo.getDepId()).getIsBankAgent(), DepTeamEnums.IsBankAgent.YES.getCode()) ? 2 : 1));
        applyVOList.add(vo);
      }
    }
    PageOutput pageOutput =
        new PageOutput(
            page.getPageNum(),
            page.getPageSize(),
            page != null ? (int) page.getTotal() : applyVOList.size(),
            applyVOList);
    return pageOutput;
  }

  @Transactional(rollbackFor = Exception.class)
  public void add(CaseApplyAddParam caseApplyAddParam, MultipartFile[] files) throws Exception {
    Integer type = caseApplyAddParam.getType();
    if (!CaseApplyEnums.Type.END.getCode().equals(type) && StringUtils.isBlank(caseApplyAddParam.getApplyDesc())) {
      throw new ApiException("申请说明不能为空");
    }
    if (StringUtils.isNotBlank(caseApplyAddParam.getApplyDesc()) && caseApplyAddParam.getApplyDesc().length() > 500) {
      throw new ApiException("申请说明不能超过500字");
    }
    if (CaseApplyEnums.Type.DELAY.getCode().equals(type) && caseApplyAddParam.getDelayTime() == null) {
      throw new ApiException("申请留案时留案时间不能为空");
    }
    Date delayTime =
        caseApplyAddParam.getDelayTime() == null
            ? null
            : DateUtils.getStartTimeOfDate(caseApplyAddParam.getDelayTime());
    // 是否已经有申请
    List<Long> caseIdList = caseApplyAddParam.getCaseIdList();
    caseIdList = verifyIsApplyIng(caseIdList);
    CaseSwitchStatusParam param = new CaseSwitchStatusParam();
    param.setAllSelect(false);
    param.setCaseIds(caseIdList);
    Integer changeStatus = getCaseChangeStatus(type);
    List<CaseQueryResult> caseList =
        caseService.selectChangeStatusList(param, changeStatus, delayTime, null);
    if (CommonUtils.isEmpty(caseList)) {
      throw new ApiException("案件不能执行此操作");
    }
    UserSession loginUser = UserUtils.getTokenUser();
    String fileName = null;
    String url = null;
    Date applyTime = new Date();
    if (files != null && files.length >= 1) {
      fileName =
          "申请修改案件状态附件"
              + "-申请时间"
              + DateUtils.format(applyTime, DateUtils.FULL_MINUTE_FORMAT).replace(" ", "-")
              + "-"
              + StringUtils.getRandomNumberBIT6()
              + ".zip";
      url =
          ZipUtil.uploadZipFile(
              files,
              fileName,
              systemConfig.getCaseFilesBucket(),
              systemConfig.getCaseFilePath(),
              10 * 1024 * 1024L);
    }
    List<FlowVO> flowVOS = flowService.flowChoice(loginUser.getOrgId(), FlowEnums.BusinessType.AJZT);
    for (CaseQueryResult acase : caseList) {
      FlowEnums.AgentType agentType = ObjectUtil.equals(acase.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?FlowEnums.AgentType.OUTER:FlowEnums.AgentType.INNER;
      // 获取审批流
      Optional<FlowVO> first = flowVOS.stream().filter(p -> ObjectUtil.equals(agentType.getCode(), p.getAgentType())).findFirst();
      AssertUtil.isTrue(first.isPresent(),"没有匹配的审批流配置");
      FlowVO flowVO = first.get();
      // 申请是否自动通过
      Boolean autoPassApply = flowService.autoPassApply(loginUser,flowVO);
      CaseApply caseApply = cn.hutool.core.bean.BeanUtil.copyProperties(caseApplyAddParam, CaseApply.class);
      caseApply.setDelayTime(type == CaseApplyEnums.Type.DELAY.getCode() ? delayTime : null);
      caseApply.setApplyFileName(fileName);
      caseApply.setApplyUrl(url);
      caseApply.setCaseId(acase.getId());
      caseApply.setType(type);
      caseApply.setStatus(CaseApplyEnums.Status.ING.getCode());
      caseApply.setApplyBy(loginUser.getId());
      caseApply.setApplyTime(applyTime);
      caseApply.setCreateTime(new Date());
      caseApply.setUpdateTime(new Date());
      caseApply.setFlowId(flowVO.getId());
      caseApply.setAgentType(ObjectUtil.equals(acase.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?ApplyEnums.AgentType.OUTER.getCode():ApplyEnums.AgentType.INNER.getCode());
      if (autoPassApply){
        caseApply.setStatus(CaseApplyEnums.Status.PASS.getCode());
        caseApply.setAuditTime(new Date());
        caseApply.setAuditDesc("自动通过");
      }
      Integer timeLimitSwitch = flowVO.getTimeLimitSwitch();
      if (ObjectUtil.equals(timeLimitSwitch, FlowEnums.TimeLimitSwitch.YES.getCode())){
        String timeLimit = flowVO.getTimeLimit();
        String hour = StrUtil.subBefore(timeLimit, ":", true);
        String minute = StrUtil.subAfter(timeLimit, ":", true);
        Date outTime = DateUtil.offsetMinute(DateUtil.offsetHour(new Date(),Integer.valueOf(hour)),Integer.valueOf(minute));
        caseApply.setOutTime(outTime);
      }
      insertSelective(caseApply);
      List<FlowNodeVO> nodes = flowVO.getNodes();
      Map<String,Object> applyMap = cn.hutool.core.bean.BeanUtil.beanToMap(caseApply);
      String parentUniqueCode = flowService.getApplyOrRuleNodeUniqueCode(nodes,applyMap,FlowEnums.BusinessType.AJZT,agentType,loginUser);
      if (autoPassApply){
        flowService.findNextCopyNodeAndExecute(parentUniqueCode,flowVO, caseApply.getId(),loginUser);
      } else {
        flowService.findNextNodeAndExecute(parentUniqueCode,flowVO, caseApply.getId(),loginUser, loginUser.getId());
      }
    }
  }

  private List<Long> verifyIsApplyIng(List<Long> caseIdList) {
    Example example = new Example(CaseApply.class);
    example
        .createCriteria()
        .andIn("caseId", caseIdList)
        .andEqualTo("status", CaseApplyEnums.Status.ING.getCode());
    List<CaseApply> caseApplyList = selectByExample(example);
    if (CollectionUtils.isEmpty(caseApplyList)) {
      return caseIdList;
    }
    List<Long> existIdList =
        caseApplyList.stream().map(CaseApply::getCaseId).collect(Collectors.toList());
    caseIdList =
        caseIdList.stream().filter(r -> !existIdList.contains(r)).collect(Collectors.toList());
    if (CollectionUtils.isEmpty(caseIdList)) {
      throw new ApiException("案件已有其他状态申请待审核，请勿重复提交");
    }
    return caseIdList;
  }

  private Integer getCaseChangeStatus(Integer type) {
    Integer changeStatus = null;
    if (type.equals(CaseApplyEnums.Type.DELAY.getCode())) {
      changeStatus = CaseEnums.ChangeStatus.DELAY.getCode();
    } else if (type.equals(CaseApplyEnums.Type.END.getCode())) {
      changeStatus = CaseEnums.ChangeStatus.END.getCode();
    }
    return changeStatus;
  }

  @Transactional(rollbackFor = Exception.class)
  public Integer changeApplyStatus(CaseApplyStatusParam param) throws Exception{
    UserSession userSession = UserUtils.getTokenUser();
    List<Long> applyIds = param.getIdList();
    AssertUtil.notEmpty(applyIds,"请选择申请");
    AssertUtil.isTrue(applyIds.size()==1,"暂不支持批量审批");
    AssertUtil.isTrue(ObjectUtil.equals(param.getStatus(),CaseApplyEnums.Status.PASS.getCode())||
            ObjectUtil.equals(param.getStatus(),CaseApplyEnums.Status.REFUSE.getCode())||
            ObjectUtil.equals(param.getStatus(),CaseApplyEnums.Status.CANCEL.getCode()),"审批状态错误");

    Long applyId = applyIds.get(0);
    CaseApply applyRecord = selectByPrimaryKey(applyId);
    AssertUtil.notNull(applyRecord, "未发现申请");
    AssertUtil.isTrue(ObjectUtil.equals(applyRecord.getStatus(), CaseApplyEnums.Status.ING.getCode()), "申请非审批中");
    AssertUtil.notNull(applyRecord.getFlowId(), "审批流异常");

    Long flowId = applyRecord.getFlowId();
    if (ObjectUtil.equals(param.getStatus(),CaseApplyEnums.Status.CANCEL.getCode())){
      flowService.revokeCheck(applyId, flowId);
      CaseApply updateApply = new CaseApply();
      updateApply.setId(applyId);
      updateApply.setStatus(CaseApplyEnums.Status.CANCEL.getCode());
      updateApply.setUpdateTime(new Date());
      updateByPrimaryKeySelective(updateApply);
      return 0;
    }

    Long applyUserId = applyRecord.getApplyBy();
    FlowHandleRecordEnums.HandleStatus approveStatus = ObjectUtil.equals(param.getStatus(),CaseApplyEnums.Status.PASS.getCode())?FlowHandleRecordEnums.HandleStatus.PASS:FlowHandleRecordEnums.HandleStatus.REFUSE;
    flowService.execApprove(applyId,flowId,approveStatus,param.getAuditDesc(),userSession,applyUserId);
    return 0;
  }

  /**
   * 更新申请 处理状态
   *
   * @param applyId            申请id
   * @param approveStatus      审批状态
   * @param approveUserSession 更新人session
   */
  public void updateHandleStatus(Long applyId, FlowHandleRecordEnums.HandleStatus approveStatus,
                                 UserSession approveUserSession) throws Exception{
    Integer status = ObjectUtil.equals(approveStatus,FlowHandleRecordEnums.HandleStatus.PASS)?CaseApplyEnums.Status.PASS.getCode():CaseApplyEnums.Status.REFUSE.getCode();

    CaseApply applyRecord = new CaseApply();
    applyRecord.setId(applyId);
    applyRecord.setStatus(status);
    applyRecord.setAuditBy(approveUserSession.getId());
    applyRecord.setUpdateTime(new Date());
    applyRecord.setAuditTime(new Date());
    updateByPrimaryKeySelective(applyRecord);

    if (ObjectUtil.equals(approveStatus,FlowHandleRecordEnums.HandleStatus.PASS)){
      CaseApply caseApply = selectByPrimaryKey(applyId);
      handlePassApply(caseApply,approveUserSession);
    }
  }

  /**
   * 申请通过后的处理
   *
   * @param caseApply   案例适用
   * @param userSession 用户会话
   * @throws Exception 例外
   */
  private void handlePassApply(CaseApply caseApply,UserSession userSession) throws Exception{
    Integer changeStatus = getCaseChangeStatus(caseApply.getType());
    Date delayTime = caseApply.getDelayTime();
    CaseSwitchStatusParam param = new CaseSwitchStatusParam();
    param.setAllSelect(false);
    param.setCaseIds(Lists.newArrayList(caseApply.getCaseId()));
    List<CaseQueryResult> caseList = caseService.selectChangeStatusList(param, changeStatus, delayTime, null);
    if (CommonUtils.isEmpty(caseList)) {
      return;
    }
    // 批量执行修改案件信息、log记录
    EndInfoDTO endInfoDTO = new EndInfoDTO();
    endInfoDTO.setEndType(caseApply.getEndType());
    endInfoDTO.setEndConfigId(caseApply.getEndConfigId());
    caseService.changeStatusUpdate(
            caseList,
            changeStatus,
            caseApply.getApplyDesc(),
            delayTime,
            null,
            userSession,
            null,
            null,
            endInfoDTO,
            null);
  }

  public Integer checkApply() {
    UserSession userSession = getTokenUser();
    Map<String, Object> map = new HashMap<>();
    map.put("orgId", getTokenUser().getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      map.put("depId", userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      map.put("teamId", userSession.getTeamId());
    }
    Integer counts = caseApplyMapper.getIngCountByMap(map);
    return counts;
  }

  /**
   * 案件彻底物理删除，级联删除未审核的状态申请记录
   *
   * @param caseIds 案件ids
   * @return
   */
  @Transactional(rollbackFor = Exception.class)
  public Integer delCaseApply(List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return 0;
    }
    Example example = new Example(CaseApply.class);
    example.and().andIn("caseId", caseIds);
    example.and().andEqualTo("status", CaseApplyEnums.Status.ING.getCode());
    int i = caseApplyMapper.deleteByExample(example);
    return i;
  }

  /**
   * 我的审批列表
   *
   * @param param      参数
   * @param orgId      组织id
   * @param userId     用户id
   * @param formFields 表单字段
   * @return {@link List}<{@link ApproveListVO}>
   */
    public List<ApproveListVO> approveList(ApproveListParam param, Long orgId, Long userId, String formFields) {
      String applyTimeStartStr = null;
      String applyTimeEndStr = null;
      String applyTime = param.getApplyTime();
      if (StrUtil.isNotBlank(applyTime)) {
        String[] range = applyTime.split(",");
        if (range.length == 2) {
          applyTimeStartStr = DateUtils.convertDate(range[0]);
          applyTimeEndStr = DateUtils.convertDate(range[1]);
        }
      }
      return caseApplyMapper.approveList(param, orgId, userId, applyTimeStartStr, applyTimeEndStr, formFields);
    }

  /**
   * 审批待处理统计
   *
   * @param orgId        组织id
   * @param userId       用户id
   * @param businessType 业务类型
   * @return {@link Integer}
   */
  public Integer todoStatistics(Long orgId, Long userId, FlowEnums.BusinessType businessType) {
    return caseApplyMapper.todoStatistics(orgId,userId,businessType.getCode());
  }

  /**
   * 超时记录ID
   *
   * @param startTime 开始时间
   * @param endTime   结束时间
   * @return {@link List}<{@link Long}>
   */
  public List<Long> timeOutRecordIds(Date startTime, Date endTime) {
    Example example = new Example(CaseApply.class);
    example.selectProperties("id");
    Example.Criteria criteria = example.and();
    criteria.andEqualTo("status", CaseApplyEnums.Status.ING.getCode())
            .andIsNotNull("outTime")
            .andGreaterThanOrEqualTo("outTime",startTime)
            .andLessThan("outTime",endTime);

    List<CaseApply> recordes = selectByExample(example);
    List<Long> timeOutRecordIds = recordes.stream().map(CaseApply::getId).collect(Collectors.toList());
    return timeOutRecordIds;
  }

  /**
   * 状态更新为超时
   *
   * @param applyIds 申请id
   */
  public void updateStatusWithTimeOut(List<Long> applyIds) {
    if (ObjectUtil.isEmpty(applyIds)){
      return;
    }
    Example example = new Example(CaseApply.class);
    Example.Criteria criteria = example.and();
    criteria.andIn("id", applyIds)
            .andEqualTo("status",CaseApplyEnums.Status.ING.getCode());

    CaseApply update = new CaseApply();
    update.setStatus(CaseApplyEnums.Status.TIMEOUT.getCode());
    update.setUpdateTime(new Date());

    updateByExampleSelective(update,example);
  }
}
