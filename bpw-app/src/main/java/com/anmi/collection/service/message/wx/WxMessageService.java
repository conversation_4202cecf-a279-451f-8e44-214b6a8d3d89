package com.anmi.collection.service.message.wx;

import com.anmi.collection.dto.message.WxBotMessageDTO;
import com.anmi.collection.service.message.WxMessageProviderImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class WxMessageService {

    @Resource
    private WxMessageProviderImpl wxMessageProvider;


    public void sendWxMessage(String wxMessage) {
        WxBotMessageDTO wxBotMessageDTO = new WxBotMessageDTO();
        wxBotMessageDTO.setContent(wxMessage);
        wxMessageProvider.sendMsg(wxBotMessageDTO);
    }

}
