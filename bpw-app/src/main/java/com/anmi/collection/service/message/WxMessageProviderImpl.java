package com.anmi.collection.service.message;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.anmi.collection.common.enums.NoticeEnums;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.dto.message.WxBotMessageDTO;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class WxMessageProviderImpl implements IMessageProvider<WxBotMessageDTO> {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private SystemConfig systemConfig;

    @Override
    public String getNoticeType() {
        return NoticeEnums.Type.WX_BOT.getCode();
    }

    @Override
    public void sendMsg(WxBotMessageDTO wxBotMessageDTO) {
        // webHookUrl地址应从配置中获取
        String webHookUrl = systemConfig.getWxHookUrl();
        HashMap<String, Object> map = new HashMap<>();
        map.put("msgtype", "text");
        Map<String, Object> text = entity2Map(wxBotMessageDTO);
        map.put("text", text);
        HttpEntity<HashMap<String, Object>> requestEntity = new HttpEntity<>(map);
        ResponseEntity<String> exchange = restTemplate.exchange(webHookUrl, HttpMethod.POST, requestEntity, String.class);
    }

    private Map<String, Object> entity2Map(WxBotMessageDTO dto) {
        // 创建 FastJsonConfig 对象
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        // 创建 SerializeConfig 对象
        SerializeConfig serializeConfig = new SerializeConfig();
        // 设置字段命名策略为 SnakeCase，即将驼峰命名转换为带下划线命名
        serializeConfig.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;
        // 将 SerializeConfig 对象添加到 FastJsonConfig 中
        fastJsonConfig.setSerializeConfig(serializeConfig);
        // 将实体类转换为 JSON 对象
        //SerializerFeature.WriteMapNullValue
        JSONObject jsonObject = (JSONObject) JSON.toJSON(dto, fastJsonConfig.getSerializeConfig());
        return jsonObject.getInnerMap();
    }
}
