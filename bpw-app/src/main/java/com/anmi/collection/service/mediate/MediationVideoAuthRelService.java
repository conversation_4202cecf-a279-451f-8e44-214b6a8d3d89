package com.anmi.collection.service.mediate;

import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.mapper.MediationVideoAuthRelMapper;
import com.anmi.collection.service.CompanyService;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.SafeVerify;
import com.anmi.domain.mediate.MediationVideoAuthRel;
import com.anmi.domain.mediate.MediationVideoRoom;
import com.anmi.domain.user.Company;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MediationVideoAuthRelService extends BaseService<MediationVideoAuthRel> {
    @Resource
    private MediationVideoAuthRelMapper mediationVideoAuthRelMapper;
    @Resource
    private MediationVideoRoomService mediationVideoRoomService;
    @Resource
    private CompanyService companyService;

    /**
     * 查询在有效期内的已认证信息
     *
     * @param caseId 案件id
     * @param roomId 调解员id
     * @return 已认证信息
     */
    public List<MediationVideoAuthRel> queryAuthInValidHour(Long caseId, Long roomId) {
        Example example = new Example(MediationVideoAuthRel.class);
        example.and().andEqualTo("caseId", caseId)
                .andEqualTo("roomId", roomId)
                .andEqualTo("authResult", 1);
        example.and().andGreaterThan("expireTime", new Date());
        example.orderBy("id").desc();
        return mediationVideoAuthRelMapper.selectByExample(example);
    }

    /**
     * 处理e签宝人脸认证回调
     * {
     *    "flowId":"2863771845436903178",
     *    "success":true,
     *    "verifycode":"e15cbb67d0142db925a2e4b2a681b39f",
     *    "serviceId":"2863771845436903178",
     *    "status":true
     * }
     * @param jsonObject 回调信息
     * @param request    请求
     * @return  处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public JSONObject dealFaceAuthCallBack(JSONObject jsonObject,  HttpServletRequest request) {
        log.info("e签宝人脸认证回调信息:{}", jsonObject.toJSONString());

        String flowId = jsonObject.getString("flowId");
        MediationVideoAuthRel mediationVideoAuthRel = queryAuthByFlowId(flowId);
        AssertUtil.notNull(mediationVideoAuthRel, "不存在对应的认证记录");

        Company company = companyService.selectByPrimaryKey(mediationVideoAuthRel.getOrgId());
        try {
            // 校验签名
            SafeVerify sv = new SafeVerify();
            if (!sv.checkPass(request, company.getEsignSecret())) {
                log.error("e签宝人脸认证回调签名校验未通过");
                return null;
            }
        } catch (Exception e) {
            log.error("校验e签宝人脸认证回调请求签名出现异常：{}", e.getMessage());
            return null;
        }

        // 更新对应认证记录认证状态
        Boolean res = jsonObject.getBoolean("success");
        Integer authResult = Objects.equals(res, Boolean.TRUE) ? 1 : 2;
        Example example = new Example(MediationVideoAuthRel.class);
        example.and().andEqualTo("flowId", flowId);
        MediationVideoAuthRel updateAuth = new MediationVideoAuthRel();
        updateAuth.setAuthResult(authResult);
        updateAuth.setUpdateTime(new Date());
        mediationVideoAuthRelMapper.updateByExampleSelective(updateAuth, example);

        // 更新对应房间认证状态
        Long roomId = mediationVideoAuthRel.getRoomId();
        MediationVideoRoom room = mediationVideoRoomService.selectByPrimaryKey(roomId);
        AssertUtil.notNull(room, "认证记录所对应房间不存在！");
        MediationVideoRoom mediationVideoRoom = new MediationVideoRoom();
        mediationVideoRoom.setId(roomId);
        mediationVideoRoom.setAuthResult(authResult);
        mediationVideoRoom.setUpdateTime(new Date());
        mediationVideoRoomService.updateByPrimaryKeySelective(mediationVideoRoom);

        JSONObject result = new JSONObject();
        result.put("code", "200");
        result.put("msg", ResultMessage.SUCCESS_MESSAGE);
        return result;
    }

    /**
     * 根据flowId查询对应认证记录
     *
     * @param flowId 业务编号
     * @return 查询结果
     */
    public MediationVideoAuthRel queryAuthByFlowId(String flowId) {
        Example example = new Example(MediationVideoAuthRel.class);
        example.and().andEqualTo("flowId", flowId);
        example.orderBy("id").desc();
        List<MediationVideoAuthRel> mediationVideoAuthRels = mediationVideoAuthRelMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(mediationVideoAuthRels)) {
            return null;
        }
        return mediationVideoAuthRels.get(0);
    }

    /**
     * 查询对应房间的认证信息
     *
     * @param roomIds 房间
     * @return 认证信息
     */
    public Map<Long, List<MediationVideoAuthRel>> queryAuthMapById(List<Long> roomIds) {
        AssertUtil.notEmpty(roomIds, "roomIds不可为空");
        Example example = new Example(MediationVideoAuthRel.class);
        example.and().andIn("roomId", roomIds);
        example.orderBy("id").desc();
        List<MediationVideoAuthRel> mediationVideoAuthRels = mediationVideoAuthRelMapper.selectByExample(example);
        return mediationVideoAuthRels.stream().collect(Collectors.groupingBy(MediationVideoAuthRel::getRoomId));
    }

    /**
     * 删除认证信息
     *
     * @param ids ids
     */
    public void deleteAuthByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        Example example = new Example(MediationVideoAuthRel.class);
        example.and().andIn("id", ids);
        mediationVideoAuthRelMapper.deleteByExample(example);
    }
}
