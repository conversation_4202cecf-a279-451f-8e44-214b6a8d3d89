package com.anmi.collection.service.mediate;

import cn.duyan.signature.config.EqianbaoConfiguration;
import cn.duyan.signature.provider.EqianbaoSignatureProvider;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.AuthFaceEnums;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.constant.UserConstant;
import com.anmi.collection.entity.requset.mediate.VideoRoomParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.mediate.*;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.service.CaseService;
import com.anmi.collection.service.CompanyService;
import com.anmi.collection.service.DeltService;
import com.anmi.collection.service.UserService;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.mediate.MediationVideoAuthRel;
import com.anmi.domain.mediate.MediationVideoRoom;
import com.anmi.domain.principal.Delt;
import com.anmi.domain.user.Company;
import com.anmi.domain.user.User;
import com.tencentyun.TLSSigAPIv2;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 调解
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MediationService {
    @Resource
    private SystemConfig systemConfig;
    @Resource
    private CompanyService companyService;
    @Resource
    private UserService userService;
    @Resource
    private MediationVideoRoomService mediationVideoRoomService;
    @Resource
    private MediationVideoAuthRelService mediationVideoAuthRelService;
    @Resource
    private CaseService caseService;
    @Resource
    private DeltService deltService;

    /**
     * 签名生效时长 默认1天 单位：秒
     */
    private static final long ExpireTime = 86400L;

    /**
     * 开启视频调解
     *
     * @param caseId 案件id
     */
    public CreateVideoRoomVO startVideoRoom(Long caseId) {
        UserSession userSession = UserUtils.getTokenUser();
        Long userId = userSession.getId();
        // 公司视频调解权限
        Company company = checkVideoTencentConfig(userSession.getOrgId());
        // 判断催员视频调解权限
        checkVideoMediationAuth(userId);
        // 查询是否已存在视频房间且在有效期内
        List<MediationVideoRoom> rooms = mediationVideoRoomService.queryRoomInValidHour(caseId, userId);
        String uuidStr = StrUtil.EMPTY;
        if (!CollectionUtils.isEmpty(rooms)) {
            MediationVideoRoom mediationVideoRoom = rooms.get(0);
            uuidStr = mediationVideoRoom.getUuid();
        } else {
            UUID uuid = UUID.randomUUID();
            uuidStr = uuid.toString();
            Date date = new Date();
            Date expireTime = DateUtil.addHours(date, systemConfig.getRoomVaildHour());
            MediationVideoRoom newRoom = new MediationVideoRoom();
            newRoom.setCaseId(caseId);
            newRoom.setUuid(uuidStr);
            newRoom.setExpireTime(expireTime);
            newRoom.setNoticeType(0);
            newRoom.setAuthResult(0);
            newRoom.setInitiationStatus(0);
            newRoom.setOrgId(userSession.getOrgId());
            newRoom.setDepId(userSession.getDepId());
            newRoom.setTeamId(userSession.getTeamId());
            newRoom.setCreateBy(userSession.getId());
            newRoom.setUpdateBy(userSession.getId());
            newRoom.setCreateTime(date);
            newRoom.setUpdateTime(date);
            mediationVideoRoomService.insert(newRoom);
        }
        TLSSigAPIv2 api = new TLSSigAPIv2(company.getTencentAppid(), company.getTencentSecretkey());
        String sign = api.genUserSig(String.valueOf(userSession.getId()), ExpireTime);

        CreateVideoRoomVO createVideoRoomVO = new CreateVideoRoomVO();
        createVideoRoomVO.setInviteUserId(userSession.getId());
        createVideoRoomVO.setUuid(uuidStr);
        createVideoRoomVO.setSign(sign);
        createVideoRoomVO.setTencentAppId(company.getTencentAppid());
        return createVideoRoomVO;
    }

    /**
     * 校验腾讯音视频配置
     *
     * @param orgId 公司id
     */
    private Company checkVideoTencentConfig(Long orgId) {
        AssertUtil.notNull(orgId, "orgId不可为空！");
        Company company = companyService.selectByPrimaryKey(orgId);
        AssertUtil.notNull(company, "公司不存在！");
        if (Objects.isNull(company.getTencentAppid())
                || StringUtils.isBlank(company.getTencentSecretkey())) {
            throw new ApiException("使用在线视频调解需先开通身份认证功能，请联系客户经理。");
        }
        return company;
    }

    /**
     * 校验视频调解权限
     *
     * @param userId 用户id
     */
    private void checkVideoMediationAuth(Long userId) {
        AssertUtil.notNull(userId, "userId不可为空！");
        User user = userService.selectByPrimaryKey(userId);
        AssertUtil.notNull(user, "当前用户不存在！");
        if (!Objects.equals(user.getMediator(), UserConstant.MEDIATOR)) {
            throw new ApiException("当前用户非调解员！");
        }
        if (!Objects.equals(user.getIsCanVideo(), UserConstant.ISCANVIDEO)) {
            throw new ApiException("当前用户没有视频调解权限！");
        }
    }

    /**
     * 更新房间发起状态
     *
     * @param status 房间发起状态 0：未发起 1：发起
     * @param uuid   uuid
     */
    public void updateInitiationStatus(Integer status, String uuid) {
        MediationVideoRoom room = mediationVideoRoomService.queryRoomByUuid(uuid);
        AssertUtil.notNull(room, "该uuid对应房间不存在");
        mediationVideoRoomService.updateStatusByUuid(uuid, status);
    }

    /**
     * 查询对应调解案件信息
     *
     * @param uuid uuid
     * @return 调解案件信息
     */
    public MediationCaseInfoVO queryMediationCaseInfo(String uuid) {
        MediationVideoRoom room = mediationVideoRoomService.queryRoomByUuid(uuid);
        AssertUtil.notNull(room, "该uuid对应房间不存在");
        // 发起催员编号
        User user = userService.selectByPrimaryKey(room.getCreateBy());
        AssertUtil.notNull(user, "该uuid对应的发起催员不存在");
        Case aCase = caseService.selectByPrimaryKey(room.getCaseId());
        AssertUtil.notNull(aCase, "该uuid对应案件不存在");
        Long deltId = aCase.getOrgDeltId();
        Delt delt = deltService.selectByPrimaryKey(deltId);

        MediationCaseInfoVO mediationCaseInfoVO = new MediationCaseInfoVO();
        mediationCaseInfoVO.setUserNo(user.getUserNo());
        mediationCaseInfoVO.setIdCard(aCase.getIdCard());
        mediationCaseInfoVO.setDebtorName(aCase.getName());
        mediationCaseInfoVO.setAmount(aCase.getAmount());
        mediationCaseInfoVO.setOrgDeltName(delt.getName());
        mediationCaseInfoVO.setExpireTime(room.getExpireTime());
        return mediationCaseInfoVO;
    }

    /**
     * 获取e签宝人脸识别链接
     * 房间债务人已认证则直接返回已认证状态
     * 房间债务人未认证则返回新的认证链接
     *
     * @param uuid uuid
     * @return 认证数据
     */
    public MediationVideoAuthVO getEsignFaceAuthUrl(String uuid) {
        Date now = new Date();
        MediationVideoRoom room = mediationVideoRoomService.queryRoomByUuid(uuid);
        AssertUtil.notNull(room, "uuid对应的房间不存在！");
        Long caseId = room.getCaseId();
        Case aCase = caseService.selectByPrimaryKey(caseId);
        // 查询当前案件债务人是否已认证
        List<MediationVideoAuthRel> mediationVideoAuthRels = mediationVideoAuthRelService.queryAuthInValidHour(caseId, room.getId());

        // 保存案件账号关联信息
        MediationVideoAuthRel mediationVideoAuthRel = new MediationVideoAuthRel();
        MediationVideoAuthVO mediationVideoAuthVO = new MediationVideoAuthVO();
        if (CollectionUtils.isEmpty(mediationVideoAuthRels)) {

            EqianbaoSignatureProvider provider = assembleConfig(room.getOrgId());
            Map<String, String> esignMap = new HashMap<>(4);
            try {
                esignMap = provider.getIndividualFaceUrl(aCase.getName(), aCase.getIdCard(),
                        systemConfig.getFaceAuthRedirectUrl(),
                        systemConfig.getFaceAuthCallBackUrl(),
                        AuthFaceEnums.Mode.TENCENT.getCode());
            } catch (Exception e) {
                log.error("获取e签宝人脸识别链接出现异常{}", e.getMessage());
                throw new ApiException(e.getMessage());
            }
            String authUrl = esignMap.get("authUrl");

            mediationVideoAuthVO.setStatus(0);
            mediationVideoAuthRel.setOrgId(room.getOrgId());
            mediationVideoAuthRel.setCaseId(caseId);
            mediationVideoAuthRel.setName(aCase.getName());
            mediationVideoAuthRel.setIdCard(aCase.getIdCard());
            mediationVideoAuthRel.setExpireTime(room.getExpireTime());
            mediationVideoAuthRel.setRoomId(room.getId());
            mediationVideoAuthRel.setAuthResult(0);
            mediationVideoAuthRel.setCreateTime(now);
            mediationVideoAuthRel.setUpdateTime(now);
            mediationVideoAuthRel.setFlowId(esignMap.get("flowId"));
            mediationVideoAuthRel.setAuthUrl(authUrl);
            mediationVideoAuthRelService.insert(mediationVideoAuthRel);
            mediationVideoAuthVO.setAuthUrl(authUrl);
        } else {
            mediationVideoAuthVO.setStatus(1);
        }
        return mediationVideoAuthVO;
    }

    /**
     * 设置e签宝配置
     *
     * @param orgId 公司id
     * @return e签宝配置
     */
    public EqianbaoSignatureProvider assembleConfig(Long orgId) {
        Company company = companyService.selectByPrimaryKey(orgId);
        if (Objects.isNull(company.getEsignAppid()) || StringUtils.isBlank(company.getEsignSecret())) {
            throw new ApiException("e签宝配置不全！");
        }
        EqianbaoSignatureProvider provider = new EqianbaoSignatureProvider();
        EqianbaoConfiguration eqianbaoConfiguration = new EqianbaoConfiguration();
        eqianbaoConfiguration.setAppId(String.valueOf(company.getEsignAppid()));
        eqianbaoConfiguration.setSecret(company.getEsignSecret());
        eqianbaoConfiguration.setHost(systemConfig.getEqianbaoHost());
        provider.config(eqianbaoConfiguration);
        return provider;
    }

    /**
     * 用户进入房间
     *
     * @param uuid 房间id
     * @return 创建房间参数
     */
    public CreateVideoRoomVO enterRoom(String uuid) {
        MediationVideoRoom room = mediationVideoRoomService.queryRoomByUuid(uuid);
        AssertUtil.notNull(room, "uuid对应房间不存在！");
        CreateVideoRoomVO createVideoRoomVO = new CreateVideoRoomVO();
        createVideoRoomVO.setInitiationStatus(room.getInitiationStatus());
        // 判断房间是否已发起
        if (Objects.equals(room.getInitiationStatus(), 0)) {
            return createVideoRoomVO;
        }
        Company company = checkVideoTencentConfig(room.getOrgId());
        // 生成签名
        TLSSigAPIv2 api = new TLSSigAPIv2(company.getTencentAppid(), company.getTencentSecretkey());
        String sign = api.genUserSig(String.valueOf(room.getCaseId()), ExpireTime);

        createVideoRoomVO.setInviteUserId(room.getCaseId());
        createVideoRoomVO.setUuid(room.getUuid());
        createVideoRoomVO.setSign(sign);
        createVideoRoomVO.setTencentAppId(company.getTencentAppid());
        return createVideoRoomVO;
    }

    /**
     * 查询视频调解记录
     *
     * @param videoRoomParam 视频调解查询参数
     * @return 视频调解记录
     */
    public PageOutput<MediationVideoRoomDetailVO> queryRecordList(VideoRoomParam videoRoomParam) throws Exception {
        UserSession userSession = UserUtils.getTokenUser();
        handleAuth(videoRoomParam, userSession);
        handleDate(videoRoomParam);
        PageOutput<MediationVideoRoomDetailVO> vos= mediationVideoRoomService.queryRoomDetailInfoByPage(videoRoomParam);
        return vos;
    }

    /**
     * 根据案件id查询对应的视频记录
     *
     * @param caseId     案件id
     * @param pageParam  分页
     * @return           视频记录
     */
    public PageOutput<MediationVideoRoomDetailVO> queryRecordsByCaseId(Long caseId, PageParam pageParam) throws Exception {
        UserSession userSession = UserUtils.getTokenUser();
        VideoRoomParam videoRoomParam = new VideoRoomParam();
        videoRoomParam.setOrgId(userSession.getOrgId());
        videoRoomParam.setCaseId(caseId);
        videoRoomParam.setPage(pageParam.getPage());
        videoRoomParam.setLimit(pageParam.getLimit());
        PageOutput<MediationVideoRoomDetailVO> vos= mediationVideoRoomService.queryRoomDetailInfoByPage(videoRoomParam);
        return vos;
    }

    /**
     * 查询视频调解记录发起统计
     *
     * @param videoRoomParam 视频调解查询参数
     * @return 统计记录
     */
    public StatsRoomRecordVO statsRoomRecord(VideoRoomParam videoRoomParam) {
        UserSession userSession = UserUtils.getTokenUser();
        handleAuth(videoRoomParam, userSession);
        handleDate(videoRoomParam);
        return mediationVideoRoomService.statsRoomRecord(videoRoomParam);
    }

    /**
     * 导出视频记录
     *
     * @param videoRoomParam 查询条件
     */
    public void exportRoomRecord(VideoRoomParam videoRoomParam) {
        UserSession userSession = UserUtils.getTokenUser();
        handleAuth(videoRoomParam, userSession);
        handleDate(videoRoomParam);
        mediationVideoRoomService.exportRoomRecord(videoRoomParam);
    }

    /**
     * 设置权限
     *
     * @param param        查询参数
     * @param userSession  用户信息
     */
    public void handleAuth(VideoRoomParam param, UserSession userSession) {
        param.setOrgId(userSession.getOrgId());
        if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
            // 如果是分公司管理员，进行分公司隔离
           param.setDepId(userSession.getDepId());
        } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
            // 如果是小组长，进行小组长隔离
           param.setDepId(userSession.getDepId());
           param.setTeamId(userSession.getTeamId());
        }
    }

    /**
     * 处理时间
     *
     * @param param 查询参数
     */
    public void handleDate(VideoRoomParam param) {
        // 视频发起时间
        if (StringUtils.isNotBlank(param.getDateStr())) {
            String[] dates = param.getDateStr().split(",");
            if (dates.length != 2) {
                throw new ApiException("视频发起时间区间存在问题！");
            }
            String timeStart = dates[0];
            String timeEnd = dates[1];
            param.setStartTime(DateUtils.getStartTimeOfDate(new Date(Long.parseLong(timeStart))));
            param.setEndTime(DateUtils.getEndTimeOfDay(new Date(Long.parseLong(timeEnd))));
        }
    }


}
