package com.anmi.collection.service.workorder;

import com.alibaba.fastjson.JSON;
import com.anmi.collection.dto.workorder.WorkOrderFieldOption;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.requset.workorder.WorkOrderFieldParam;
import com.anmi.collection.entity.response.workorder.WorkOrderFieldVO;
import com.anmi.collection.mapper.workorder.WorkOrderFieldMapper;
import com.anmi.collection.utils.AnmiBeanutils;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.workorder.WorkOrderField;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 11:19
 */
@Service
public class WorkOrderFieldService {
    @Resource
    private WorkOrderFieldMapper workOrderFieldMapper;



    @Transactional(rollbackFor = Exception.class)
    public void addWorkOrderField(Long templateId, UserSession userSession, List<WorkOrderFieldParam> paramList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return;
        }
        List<WorkOrderField> fieldList = new ArrayList<>();
        paramList.forEach(workOrderFieldParam -> {
            WorkOrderField workOrderField = AnmiBeanutils.copy(workOrderFieldParam, WorkOrderField.class);
            workOrderField.setTemplateId(templateId);
            workOrderField.setOrgId(userSession.getOrgId());
            workOrderField.setCreateBy(userSession.getId());
            workOrderField.setUpdateBy(userSession.getId());
            workOrderField.setCreateTime(new Date());
            workOrderField.setUpdateTime(new Date());
            if (!CollectionUtils.isEmpty(workOrderFieldParam.getOptions())) {
                workOrderField.setOptions(JSON.toJSONString(workOrderFieldParam.getOptions()));
            }
            fieldList.add(workOrderField);
        });
        workOrderFieldMapper.insertList(fieldList);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateWorkOrderField(WorkOrderFieldParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        WorkOrderField workOrderField = AnmiBeanutils.copy(param, WorkOrderField.class);
        if (!CollectionUtils.isEmpty(param.getOptions())) {
            workOrderField.setOptions(JSON.toJSONString(param.getOptions()));
        }
        workOrderField.setUpdateBy(userSession.getId());
        workOrderField.setUpdateTime(new Date());
        workOrderFieldMapper.updateByPrimaryKeySelective(workOrderField);
    }

    @Transactional(rollbackFor = Exception.class)
    public void sortWorkOrderField(List<WorkOrderFieldParam> paramList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return;
        }
        List<WorkOrderField> fields = new ArrayList<>();
        paramList.forEach(param -> {
            WorkOrderField field = AnmiBeanutils.copy(param, WorkOrderField.class);
            fields.add(field);
        });
        workOrderFieldMapper.updateSeqBatch(fields);
    }

    public List<WorkOrderFieldVO> listWorkOrderField(Long templateId) {
        Example example = new Example(WorkOrderField.class);
        example.and().andEqualTo("templateId", templateId);
        example.orderBy("seq").asc();
        List<WorkOrderField> fieldList = workOrderFieldMapper.selectByExample(example);
        List<WorkOrderFieldVO> voList = new ArrayList<>();
        fieldList.forEach(workOrderField -> {
            WorkOrderFieldVO vo = AnmiBeanutils.copy(workOrderField, WorkOrderFieldVO.class);
            if (StringUtils.isNotBlank(workOrderField.getOptions())) {
                vo.setOptions(JSON.parseArray(workOrderField.getOptions(), WorkOrderFieldOption.class));
            }
            voList.add(vo);
        });
        return voList;

    }









    public List<WorkOrderFieldVO> listDefaultWorkOrderField() {
        ArrayList<WorkOrderFieldVO> defaultList = Lists.newArrayList(
                WorkOrderFieldVO.builder().isExt(0)
                        .field("type").name("工单类型").type(1).required(1).isSelected(1).seq(1).hint("请选择工单类型").build(),
                WorkOrderFieldVO.builder().isExt(0)
                        .field("templateId").name("工单模板").type(1).required(1).isSelected(1).seq(2).hint("请选择工单模板").build(),
                WorkOrderFieldVO.builder().isExt(0)
                        .field("title").name("工单标题").type(0).required(1).isSelected(1).seq(3).hint("请输入").build(),
                WorkOrderFieldVO.builder().isExt(0)
                        .field("description").name("工单描述").type(0).required(0).isSelected(1).seq(4).hint("请输入").build(),
                WorkOrderFieldVO.builder().isExt(0)
                        .field("urgency").name("紧急程度").type(1).required(1).isSelected(1).seq(5).hint("请选择").build(),
                WorkOrderFieldVO.builder().isExt(0)
                        .field("handleDuration").name("处理时效").type(3).required(0).isSelected(1).seq(6).hint("请选择").build(),
                WorkOrderFieldVO.builder()
                        .field("ext1").name("文本").type(0).required(0).isSelected(0).seq(7).isExt(1).build(),
                WorkOrderFieldVO.builder()
                        .field("ext2").name("文本").type(0).required(0).isSelected(0).seq(8).isExt(1).build(),
                WorkOrderFieldVO.builder()
                        .field("ext3").name("数字").type(3).required(0).isSelected(0).seq(9).isExt(1).build(),
                WorkOrderFieldVO.builder()
                        .field("ext4").name("数字").type(3).required(0).isSelected(0).seq(10).isExt(1).build(),
                WorkOrderFieldVO.builder()
                        .field("ext5").name("日期").type(4).required(0).isSelected(0).seq(11).isExt(1).build(),
                WorkOrderFieldVO.builder()
                        .field("ext6").name("日期").type(4).required(0).isSelected(0).seq(12).isExt(1).build(),
                WorkOrderFieldVO.builder()
                        .field("ext7").name("下拉框").type(1).required(0).isSelected(0).seq(13).isExt(1).build(),
                WorkOrderFieldVO.builder()
                        .field("ext8").name("下拉框").type(1).required(0).isSelected(0).seq(14).isExt(1).build(),
                WorkOrderFieldVO.builder()
                        .field("ext9").name("下拉框多选").type(2).required(0).isSelected(0).seq(15).isExt(1).build(),
                WorkOrderFieldVO.builder()
                        .field("ext10").name("下拉框多选").type(2).required(0).isSelected(0).seq(16).isExt(1).build(),
                WorkOrderFieldVO.builder().isExt(0)
                        .field("file").name("附件").type(6).required(0).isSelected(1).seq(17).build()
        );
        return defaultList;
    }
}
