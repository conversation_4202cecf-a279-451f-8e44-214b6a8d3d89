package com.anmi.collection.service.workorder;

import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.requset.workorder.WorkOrderPushParam;
import com.anmi.collection.entity.response.workorder.WorkOrderPushVO;
import com.anmi.collection.mapper.workorder.WorkOrderPushMapper;
import com.anmi.collection.utils.AnmiBeanutils;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.workorder.WorkOrderPush;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/11 14:30
 */
@Service
public class WorkOrderPushService {
    @Resource
    private WorkOrderPushMapper workOrderPushMapper;


    @Transactional(rollbackFor = Exception.class)
    public Long saveWorkOrderPush(WorkOrderPushParam param) {
        if (Objects.nonNull(param.getId())) {
            updateWorkOrderPush(param);
            return param.getId();
        }
        UserSession session = UserUtils.getTokenUser();
        // 防止重复提交
        WorkOrderPush push = getWorkOrderPush(session.getOrgId(), param.getType());
        if (Objects.nonNull(push)) {
            param.setId(push.getId());
            updateWorkOrderPush(param);
            return param.getId();
        }
        WorkOrderPush workOrderPush = AnmiBeanutils.copy(param, WorkOrderPush.class);
        workOrderPush.setOrgId(session.getOrgId());
        workOrderPush.setCreateBy(session.getId());
        workOrderPush.setUpdateBy(session.getId());
        workOrderPush.setCreateTime(new Date());
        workOrderPush.setUpdateTime(new Date());
        workOrderPushMapper.insertSelective(workOrderPush);
        return workOrderPush.getId();
    }

    public void updateWorkOrderPush(WorkOrderPushParam param) {
        UserSession session = UserUtils.getTokenUser();
        WorkOrderPush workOrderPush = AnmiBeanutils.copy(param, WorkOrderPush.class);
        workOrderPush.setUpdateBy(session.getId());
        workOrderPush.setUpdateTime(new Date());
        workOrderPushMapper.updateByPrimaryKeySelective(workOrderPush);
    }

    public WorkOrderPushVO getWorkOrderPush(Integer type) {
        UserSession session = UserUtils.getTokenUser();
        Example example = new Example(WorkOrderPush.class);
        example.and().andEqualTo("orgId", session.getOrgId()).andEqualTo("type", type);
        example.orderBy("id").desc();
        List<WorkOrderPush> pushList = workOrderPushMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(pushList)) {
            return null;
        }
        return AnmiBeanutils.copy(pushList.get(0), WorkOrderPushVO.class);
    }

    public WorkOrderPush getWorkOrderPush(Long orgId, Integer type) {
        Example example = new Example(WorkOrderPush.class);
        example.and().andEqualTo("orgId", orgId).andEqualTo("type", type);
        example.orderBy("id").desc();
        List<WorkOrderPush> pushList = workOrderPushMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(pushList)) {
            return null;
        }
        return pushList.get(0);
    }



}
