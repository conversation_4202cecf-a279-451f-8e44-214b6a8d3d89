package com.anmi.collection.service.workorder;

import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.constant.WorkOrderConstant;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.requset.workorder.WorkOrderTypeParam;
import com.anmi.collection.entity.requset.workorder.WorkOrderTypeQuery;
import com.anmi.collection.entity.response.workorder.WorkOrderTypeVO;
import com.anmi.collection.mapper.workorder.WorkOrderTypeMapper;
import com.anmi.collection.service.UserService;
import com.anmi.collection.utils.AnmiBeanutils;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.workorder.WorkOrderType;
import com.github.pagehelper.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 11:16
 */
@Service
public class WorkOrderTypeService {
    @Resource
    private WorkOrderTypeMapper workOrderTypeMapper;
    @Resource
    private UserService userService;

    @Transactional(rollbackFor = Exception.class)
    public void addWorkOrderType(WorkOrderTypeParam param) {
        UserSession session = UserUtils.getTokenUser();
        WorkOrderType workOrderType = new WorkOrderType();
        workOrderType.setOrgId(session.getOrgId());
        workOrderType.setName(param.getName());
        workOrderType.setEnable(WorkOrderConstant.IS_ON);
        workOrderType.setCreateBy(session.getId());
        workOrderType.setUpdateBy(session.getId());
        workOrderType.setCreateTime(new Date());
        workOrderType.setUpdateTime(new Date());
        workOrderTypeMapper.insertSelective(workOrderType);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateWorkOrderType(WorkOrderTypeParam param) {
        UserSession session = UserUtils.getTokenUser();
        WorkOrderType workOrderType = new WorkOrderType();
        workOrderType.setId(param.getId());
        workOrderType.setName(param.getName());
        workOrderType.setEnable(param.getEnable());
        workOrderType.setUpdateBy(session.getId());
        workOrderType.setUpdateTime(new Date());
        workOrderTypeMapper.updateByPrimaryKeySelective(workOrderType);
    }

    public PageOutput<WorkOrderTypeVO> pageWorkOrderType(WorkOrderTypeQuery query) {
        UserSession session = UserUtils.getTokenUser();
        Example example = new Example(WorkOrderType.class);
        example.and().andEqualTo("orgId", session.getOrgId());
        if (StringUtils.isNotBlank(query.getName())) {
            example.and().andLike("name", query.getName() + "%");
        }
        example.orderBy("id").desc();
        PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<WorkOrderType> typeList = workOrderTypeMapper.selectByExample(example);
        Map<Long, String> userMap = userService.getNames(session.getOrgId());
        List<WorkOrderTypeVO> voList = new ArrayList<>();
        typeList.forEach(workOrderType -> {
            WorkOrderTypeVO vo = AnmiBeanutils.copy(workOrderType, WorkOrderTypeVO.class);
            vo.setUpdateByName(userMap.get(vo.getUpdateBy()));
            voList.add(vo);
        });
        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), voList);
    }

    public List<WorkOrderTypeVO> listWorkOrderType(Integer type) {
        UserSession userSession = UserUtils.getTokenUser();
        Example example = new Example(WorkOrderType.class);
        example.and().andEqualTo("orgId", userSession.getOrgId())
                .andEqualTo("enable", WorkOrderConstant.IS_ON);
        if (Objects.nonNull(type)) {
            example.and().andEqualTo("type", type);
        }
        example.orderBy("id").desc();
        List<WorkOrderType> typeList = workOrderTypeMapper.selectByExample(example);
        List<WorkOrderTypeVO> voList = new ArrayList<>();
        typeList.forEach(workOrderType -> {
            voList.add(AnmiBeanutils.copy(workOrderType, WorkOrderTypeVO.class));
        });
        return voList;
    }

    public Map<Long, String> getWorkOrderTypeName(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        Example example = new Example(WorkOrderType.class);
        example.and().andIn("id", ids);
        List<WorkOrderType> typeList = workOrderTypeMapper.selectByExample(example);
        return typeList.stream().collect(Collectors.toMap(WorkOrderType::getId, WorkOrderType::getName));
    }

}
