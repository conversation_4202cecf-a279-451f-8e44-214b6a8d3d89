package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.enums.AccountLogEnums;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.utils.AuthUtils;
import com.anmi.collection.utils.CmUtil;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.sys.AccountLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccountLogService extends BaseService<AccountLog> {

    public void createAccountLog(AccountLogEnums.Type type, String remark, String contentJson) {
        try {
            UserSession userSession = UserUtils.getTokenUser();
            String ipAddr = AuthUtils.getIpAddr(CmUtil.getRequest()).split(",")[0];
            createAccountLog(type, remark, contentJson, userSession, ipAddr);
        } catch (Exception ex) {
            log.error("添加日志失败", ex);
        }
    }

    public void createAccountLog(AccountLogEnums.Type type, String remark, String contentJson, UserSession userSession, String ipAddr) {
        try {
            AccountLog accountLog = new AccountLog();
            accountLog.setCreateTime(new Date());
            accountLog.setRemark(remark);
            accountLog.setContentJson(contentJson);
            accountLog.setUserId(userSession.getId());
            accountLog.setIpAddr(ipAddr);
            accountLog.setOrgId(userSession.getOrgId());
            accountLog.setToken(userSession.getToken());
            accountLog.setType(type.getCode());
            accountLog.setUpdateTime(new Date());
            this.insertSelective(accountLog);
        } catch (Exception ex) {
            log.error("添加日志失败", ex);
        }
    }

}
