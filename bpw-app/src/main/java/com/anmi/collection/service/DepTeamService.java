package com.anmi.collection.service;
import java.util.Date;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.AccountAgeEnums;
import com.anmi.collection.common.enums.DepTeamEnums;
import com.anmi.collection.common.enums.RoleEnums;
import com.anmi.collection.dto.fileStorage.UploadFileInfo;
import com.anmi.collection.entity.requset.sys.dep.*;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.sys.dep.*;
import com.anmi.collection.entity.response.sys.role.FunctionVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.mapper.DepTeamMapper;
import com.anmi.collection.mapper.OrgAgentDetailMapper;
import com.anmi.collection.mapper.OrgDepFileMapper;
import com.anmi.collection.mapper.OrgDepSwitchMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.redis.RedisLock;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.collection.utils.dict.RoleSessionUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.CtrlTypeRel;
import com.anmi.domain.user.*;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/** Created by dongwang on 2018-11-29. */
@Service
@Slf4j
public class DepTeamService extends BaseService<DepTeam> {
  @Autowired private CompanyService companyService;
  @Autowired private DepTeamService depTeamService;
  @Autowired private UserService userService;
  @Autowired private DuyanManager duyanManager;
  @Autowired private DepTeamMapper depTeamMapper;
  @Autowired private OrgDepSwitchMapper orgDepSwitchMapper;
  @Autowired private RedisLock redisLock;
  @Autowired private CtrlTypeRelService ctrlTypeRelService;
  @Autowired private OrgDepFileMapper orgDepFileMapper;
  @Autowired private FunctionService functionService;
  @Autowired private FileStorageStrategyFactory fileStorageStrategyFactory;
  @Autowired private OrgAgentDetailMapper orgAgentDetailMapper;

  @Cacheable(value = KeyCache.DEP_NAME)
  public Map<Long, String> getNames() {
    List<DepTeam> list = super.getNameIdMap("name");
    return list.stream().collect(Collectors.toMap(DepTeam::getId, DepTeam::getName));
  }

  @Override
  @CacheEvict(value = KeyCache.DEP_NAME, allEntries = true)
  public int updateByPrimaryKeySelective(DepTeam entity) {
    Long orgId = entity.getOrgId();
    if (orgId == null) {
      DepTeam tmp = selectByPrimaryKey(entity.getId());
      if (tmp == null) {
        throw new ApiException("要更新的组织不存在！");
      }
      orgId = tmp.getOrgId();
    }
    if (userService.isConnectDuyan(orgId)
        && (entity.getStatus() != null
            && entity.getStatus() != DepTeamEnums.Status.DELETE.getCode())) {
      // 修改后，需同步到度言
      Company company = companyService.selectByPrimaryKey(orgId);
      Long duyanOrgId = company.getDuyanReferId();
      DepTeam depTeam = depTeamService.selectByPrimaryKey(entity.getId());
      Long duyanTeamId = depTeam.getDuyanReferId();
      Long duyanParentId = null;
      Long parentId = entity.getParentId();
      if (parentId == null) {
        if (entity.getType() == DepTeamEnums.type.BRANCH_OFFICE.getCode()) {
          parentId = orgId;
        }
      }
      if (depTeam.getType() == DepTeamEnums.type.TEAM.getCode()
          && depTeam.getUnderTeam() == DepTeamEnums.UnderTeam.NOT.getCode()) {
        // 一定是分公司下的小组
        duyanParentId = depTeamService.selectByPrimaryKey(parentId).getDuyanReferId();
      }
      //分公司默认不隐藏手机号，小组需判断脱敏开关
      Boolean isHiddenMobile = false;
      if(depTeam.getType() == DepTeamEnums.type.TEAM.getCode()){
        isHiddenMobile=companyService.getIsHiddenMobile(orgId,depTeam.getId());
      }
      duyanManager.updateTeam(
          duyanOrgId,
          duyanTeamId,
          entity.getName(),
          duyanParentId,
          entity.getName(),
          isHiddenMobile);
    }
    return super.updateByPrimaryKeySelective(entity);
  }

  @Override
  @CacheEvict(value = KeyCache.DEP_NAME, allEntries = true)
  public int updateByPrimaryKey(DepTeam entity) {
    Long orgId = entity.getOrgId();
    if (orgId == null) {
      DepTeam tmp = selectByPrimaryKey(entity.getId());
      if (tmp == null) {
        throw new ApiException("要更新的组织不存在！");
      }
      orgId = tmp.getOrgId();
    }
    if (userService.isConnectDuyan(orgId)
        && (entity.getStatus() != null
        && entity.getStatus() != DepTeamEnums.Status.DELETE.getCode())) {
      // 修改后，需同步到度言
      Company company = companyService.selectByPrimaryKey(orgId);
      Long duyanOrgId = company.getDuyanReferId();
      DepTeam depTeam = depTeamService.selectByPrimaryKey(entity.getId());
      Long duyanTeamId = depTeam.getDuyanReferId();
      Long duyanParentId = null;
      Long parentId = entity.getParentId();
      if (parentId == null) {
        if (entity.getType() == DepTeamEnums.type.BRANCH_OFFICE.getCode()) {
          parentId = orgId;
        }
      }
      if (depTeam.getType() == DepTeamEnums.type.TEAM.getCode()
          && depTeam.getUnderTeam() == DepTeamEnums.UnderTeam.NOT.getCode()) {
        // 一定是分公司下的小组
        duyanParentId = depTeamService.selectByPrimaryKey(parentId).getDuyanReferId();
      }
      //分公司默认不隐藏手机号，小组需判断脱敏开关
      Boolean isHiddenMobile = false;
      if(depTeam.getType() == DepTeamEnums.type.TEAM.getCode()){
        isHiddenMobile=companyService.getIsHiddenMobile(orgId,depTeam.getId());
      }
      duyanManager.updateTeam(
          duyanOrgId,
          duyanTeamId,
          entity.getName(),
          duyanParentId,
          entity.getName(),
          isHiddenMobile);
    }
    return super.updateByPrimaryKey(entity);
  }

  @Override
  @CacheEvict(value = KeyCache.DEP_NAME, allEntries = true)
  public int deleteByPrimaryKey(Object entity) {
    return super.deleteByPrimaryKey(entity);
  }

  @Override
  @CacheEvict(value = KeyCache.DEP_NAME, allEntries = true)
  @Transactional
  public int insertSelective(DepTeam entity) {
    int result = super.insertSelective(entity);
    if (result > 0 && userService.isConnectDuyan(entity.getOrgId())) { // 创建度言团队,前提是连接了度言
      // 获取度言公司id
      Company company = companyService.selectByPrimaryKey(entity.getOrgId());
      Long duyanOrgId = company.getDuyanReferId();
      Long parentTeamId = null;
      // 获取度言团队id
      Long teamId = null;
      if (null != entity.getParentId() && entity.getParentId() > 0) {
        DepTeam depTeam = null;
        Boolean isHiddenMobile = companyService.getIsHiddenMobile(entity.getOrgId(),null);
        if (entity.getType() == DepTeamEnums.type.TEAM.getCode()) {
          if (entity.getUnderTeam() == DepTeamEnums.UnderTeam.YES.getCode()) {
            // 直属小组
            teamId = duyanManager.createTeam(duyanOrgId, entity.getName(), null, isHiddenMobile);
          } else {
            // 分公司下的小组
            depTeam = super.selectByPrimaryKey(entity.getParentId());
            parentTeamId = depTeam.getDuyanReferId();
            teamId =
                duyanManager.createTeam(duyanOrgId, entity.getName(), parentTeamId, isHiddenMobile);
          }
          //设置小组是否开启脱敏
          if(isHiddenMobile){
            DepTeam depTeamUpdate = new DepTeam();
            depTeamUpdate.setId(entity.getId()).setDesensitization(DepTeamEnums.Desensitization.YES.getCode());
            updateByPrimaryKeySelective(depTeamUpdate);
          }
        } else if (entity.getType() == DepTeamEnums.type.BRANCH_OFFICE.getCode()) {
          teamId = duyanManager.createTeam(duyanOrgId, entity.getName(), null, isHiddenMobile);
        }
      }
      // duyanOrgId和parentTeamId很有可能相等（添加的是分公司的情况下）
      entity.setDuyanReferId(teamId);
      super.updateByPrimaryKeySelective(entity);
    }
    return result;
  }

  public DepVO getDepVOById(Long id) {
    DepTeam depTeam = super.selectByPrimaryKey(id);
    Map<Long, String> companyMap = companyService.getNames();
    Map<Long, String> depTeamMap = depTeamService.getNames();
    Map<Long, String> userMap = userService.getNames(userService.getOrgId());
    OrgAgentDetail orgAgentDetailQuery = new OrgAgentDetail();
    orgAgentDetailQuery.setDepId(id);
    OrgAgentDetail orgAgentDetail = orgAgentDetailMapper.selectOne(orgAgentDetailQuery);
    Map<Long,OrgAgentDetail> singleMap = orgAgentDetail==null ? null: Collections.singletonMap(orgAgentDetail.getDepId(),orgAgentDetail);
    DepVO result = convertVO(depTeam, companyMap, depTeamMap, userMap,singleMap);
    if (DepTeamEnums.IsAgent.YES.getCode().equals(depTeam.getIsAgent())) {
      OrgDepSwitch query = new OrgDepSwitch();
      query.setDepId(depTeam.getId());
      query.setOrgId(depTeam.getOrgId());
      OrgDepSwitch orgDepSwitch = orgDepSwitchMapper.selectOne(query);
      if (orgDepSwitch == null) {
        throw new ApiException("找不到开关配置，请联系管理员");
      }
      result.setFifoSwitch(orgDepSwitch.getFifoSwitch());
      result.setVisitSwitch(orgDepSwitch.getVisitSwitch());
      result.setRobotSwitch(orgDepSwitch.getRobotSwitch());
      result.setSmsSwitch(orgDepSwitch.getSmsSwitch());
    }
    OrgDepFile orgDepFile = new OrgDepFile();
    orgDepFile.setDepId(id);
    orgDepFile.setStatus(0);
    List<OrgDepFile> list = this.orgDepFileMapper.select(orgDepFile);
    if (!CollectionUtils.isEmpty(list)) {
      List<DepFileVO> depFileVOList = list.stream().map(t -> {
        DepFileVO vo = new DepFileVO();
        vo.setId(t.getId());
        vo.setName(t.getFileName());
        vo.setUrl(t.getFileUrl());
        return vo;
      }).collect(Collectors.toList());
      result.setDepFileList(depFileVOList);
    }

    return result;
  }

  public List<DepVO> selectByEntity(DepTeam depTeam) {
    List<DepVO> list = new ArrayList<DepVO>();
    List<DepTeam> deps = super.select(depTeam);
    Map<Long,String> companyMap = companyService.getNames();
    Map<Long,String> depTeamMap=depTeamService.getNames();
    Map<Long,String> userMap=userService.getNames(userService.getOrgId());

    Map<Long,OrgAgentDetail> orgAgentDetailMap = new HashMap<>();
    List<Long> depIds = deps.stream().map(DepTeam::getId).collect(Collectors.toList());
    if(!CollectionUtils.isEmpty(depIds)){
      Example example = new Example(OrgAgentDetail.class);
      example.and().andIn("depId",depIds);
      List<OrgAgentDetail> orgAgentDetailList = orgAgentDetailMapper.selectByExample(example);
      orgAgentDetailMap  = orgAgentDetailList.stream().collect(Collectors.toMap(OrgAgentDetail::getDepId, java.util.function.Function.identity()));
    }
    for(DepTeam d :deps){
      DepVO depVO = convertVO(d,companyMap,depTeamMap,userMap, orgAgentDetailMap);
      list.add(depVO);
    }
    return list;
  }

  public PageOutput<DepVO> getPageVO(DepParam param, PageParam pageParam) {
    UserSession login = UserUtils.getTokenUser();
    Long depId = login.getDepId();
    DepTeam depTeam = new DepTeam();
    BeanUtils.copyProperties(param, depTeam);
    depTeam.setStatus(DepTeamEnums.Status.NORMAL.getCode());
    depTeam.setOrgId(login.getOrgId());
    if (UserUtils.likeBranchAdmin()) {
      depTeam.setParentId(login.getDepId());
      // 分公司下的小组
      depTeam.setUnderTeam(DepTeamEnums.UnderTeam.NOT.getCode());
    }
    depTeam.setType(DepTeamEnums.type.TEAM.getCode());
    PageOutput deps = super.selectByPage(depTeam, pageParam);
    List<DepTeam> list = deps.getList();
    Role role = RoleSessionUtils.getRole();
    if (role.getType() == RoleEnums.type.BRANCH_ADMIN.getCode()) {
      // 分公司只筛选分公司小组
      list = list.stream().filter(i -> i.getParentId().equals(depId)).collect(Collectors.toList());
    } else if ((role.getType() == RoleEnums.type.CUSTOM_ADMIN.getCode()
            && (role.getDepType() != null && role.getDepType() == RoleEnums.DepType.TEAM.getCode()))
        || role.getType() == RoleEnums.type.TEAM_LEADER.getCode()) {
      // 小组长直筛选自己的组
      list =
          list.stream()
              .filter(i -> i.getId().equals(login.getTeamId()))
              .collect(Collectors.toList());
    }
    List<DepVO> vos = new ArrayList<DepVO>(list.size());
    Map<Long,String> companyMap = companyService.getNames();
    Map<Long,String> depTeamMap=depTeamService.getNames();
    Map<Long,String> userMap=userService.getNames(login.getOrgId());
    List<Long> depIds = list.stream().map(DepTeam::getId).collect(Collectors.toList());
    Example example = new Example(OrgAgentDetail.class);
    example.and().andIn("depId",depIds);
    List<OrgAgentDetail> orgAgentDetailList = orgAgentDetailMapper.selectByExample(example);
    Map<Long,OrgAgentDetail> orgAgentDetailMap = orgAgentDetailList.stream().collect(Collectors.toMap(OrgAgentDetail::getDepId, java.util.function.Function.identity()));

    list.forEach(
            l -> {
              DepVO vo = convertVO(l,companyMap,depTeamMap,userMap,orgAgentDetailMap);
              vos.add(vo);
            });
    deps.setList(vos);
    return deps;
  }

  private DepVO convertVO(DepTeam dep, Map<Long, String> companyMap, Map<Long, String> depTeamMap, Map<Long, String> userMap, Map<Long, OrgAgentDetail> orgAgentDetailMap) {
    // 公共参数设置
    DepVO vo = new DepVO();
    BeanUtils.copyProperties(dep, vo);
    // 公司名称
    vo.setOrgName(companyMap.get(dep.getOrgId()));
    vo.setParentName(depTeamMap.get(dep.getParentId()));
    vo.setCreateBy(userMap.get(dep.getCreateBy()));
    vo.setUpdateBy(userMap.get(dep.getUpdateBy()));
    vo.setBusinessTypes(StrUtil.split(dep.getBusinessTypes(), ",").stream().map(Integer::valueOf).collect(Collectors.toList()));
    // 团队有所属部门
    if (dep.getType() == DepTeamEnums.type.BRANCH_OFFICE.getCode()) {
      // 查询团队信息
      DepTeam depTeam = new DepTeam();
      depTeam.setParentId(dep.getId());
      // 要排除掉直属小组
      depTeam.setUnderTeam(DepTeamEnums.UnderTeam.NOT.getCode());
      depTeam.setStatus(DepTeamEnums.Status.NORMAL.getCode());
      List<DepVO> children = this.selectByEntity(depTeam);
      vo.setChildren(children);
    }
    if (!StringUtils.isBlank(dep.getFunctionIds())) {
      vo.setFunctionIds(Arrays.stream(dep.getFunctionIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
      List<Function> functionList = functionService.selectByIdList(vo.getFunctionIds());
      List<FunctionVO> functionVOS = functionService.functionTreeBuild(functionList);
      vo.setFunctionTree(functionVOS);
    }
    vo.setRegionSwitch(orgAgentDetailMap.getOrDefault(dep.getId(), new OrgAgentDetail()).getRegionSwitch());
    List<DepRegionVO> list = JSON.parseArray(orgAgentDetailMap.getOrDefault(dep.getId(), new OrgAgentDetail()).getRegionCityCodes(),DepRegionVO.class);
    vo.setRegionCityCodes(list);
    return vo;
  }

  /**
   * 度言删除分公司或者小组
   *
   * @param depTeam1
   */
  public void removeDY(DepTeam depTeam1) {
    Company company = companyService.selectByPrimaryKey(getOrgId());
    if (userService.isConnectDuyan(getOrgId())) {
      duyanManager.cancelTeam(company.getDuyanReferId(), depTeam1.getDuyanReferId());
    }
  }

  public Map<Long, String> getDepTeamMap(List<Long> depTeamIdList) {
    if (CollectionUtils.isEmpty(depTeamIdList)) {
      return new HashMap<>();
    }
    Example example = new Example(DepTeam.class);
    example.createCriteria().andIn("id", depTeamIdList);
    List<DepTeam> depTeamList = super.selectByExample(example);
    if (CollectionUtils.isEmpty(depTeamList)) {
      return new HashMap<>();
    }
    Map<Long, String> map =
        depTeamList.stream().collect(Collectors.toMap(DepTeam::getId, DepTeam::getName));
    return map;
  }

  public Map<Long, String> getAgentDepTeamMap(List<Long> depTeamIdList) {
    if (CollectionUtils.isEmpty(depTeamIdList)) {
      return new HashMap<>();
    }
    Example example = new Example(DepTeam.class);
    example.createCriteria().andIn("id", depTeamIdList).andEqualTo("isAgent", 1);
    List<DepTeam> depTeamList = super.selectByExample(example);
    if (CollectionUtils.isEmpty(depTeamList)) {
      return new HashMap<>();
    }
    Map<Long, String> map =
            depTeamList.stream().collect(Collectors.toMap(DepTeam::getId, DepTeam::getName));
    return map;
  }

  @Transactional(rollbackFor = Exception.class)
  public void delDuyanTeam(DepTeam depTeam, Long duyanOrgId) {
    if (depTeam.getDuyanReferId() == null) {
      return;
    }
    Long duyanTeamId = depTeam.getDuyanReferId();
    depTeam.setDuyanReferId(null);
    depTeamMapper.updateByPrimaryKey(depTeam);
    duyanManager.cancelTeam(duyanOrgId, duyanTeamId);
  }

  public List<DepTeam> selectDuyanTeamByOrgId(Long orgId) {
    Example example = new Example(DepTeam.class);
    example
        .createCriteria()
        .andEqualTo("orgId", orgId)
        .andEqualTo("status", DepTeamEnums.Status.NORMAL.getCode())
        .andIsNotNull("duyanReferId");
    List<DepTeam> depTeamList = selectByExample(example);
    return depTeamList;
  }

  public List<DepVO> getTeamList() throws Exception {
    UserSession userSession = getTokenUser();
    List<DepVO> depVOList = new ArrayList<>();
    DepTeam depTeam = new DepTeam();
    depTeam.setOrgId(userSession.getOrgId());
    depTeam.setType(DepTeamEnums.type.TEAM.getCode());
    depTeam.setStatus(DepTeamEnums.Status.NORMAL.getCode());
    List<DepTeam> list = depTeamMapper.select(depTeam);
    for (DepTeam team : list) {
      DepVO depVO = BeanUtil.copyProperties(team, DepVO.class);
      depVOList.add(depVO);
    }
    return depVOList;
  }

  // 查询该总公司下所有状态正常的直属小组id
  public List<Long> getDepTeamIdByUnderTeam() {
    UserSession userSession = getTokenUser();
    DepTeam team = new DepTeam();
    team.setOrgId(userSession.getOrgId());
    team.setStatus(DepTeamEnums.Status.NORMAL.getCode());
    team.setUnderTeam(DepTeamEnums.UnderTeam.YES.getCode());
    List<DepTeam> depTeamList = depTeamService.select(team);
    List<Long> depTeams = depTeamList.stream().map(p -> p.getId()).collect(Collectors.toList());
    return depTeams;
  }

  public List<DepVO> getDepTeamList() throws Exception {
    UserSession userSession = UserUtils.getTokenUser();
    List<DepVO> depVOList = new ArrayList<>();
    DepTeam dep = depTeamService.selectByPrimaryKey(userSession.getTeamId());
    DepTeam depTeam = new DepTeam();
    depTeam.setStatus(DepTeamEnums.Status.NORMAL.getCode());
    depTeam.setOrgId(userSession.getOrgId());
    if (dep.getUnderTeam().equals(DepTeamEnums.UnderTeam.NOT.getCode())) {
      depTeam.setParentId(userSession.getDepId());
      // 分公司下的小组
      depTeam.setUnderTeam(DepTeamEnums.UnderTeam.NOT.getCode());
    } else {
      // 总公司所有直属小组
      depTeam.setUnderTeam(DepTeamEnums.UnderTeam.YES.getCode());
    }
    depTeam.setType(DepTeamEnums.type.TEAM.getCode());
    List<DepTeam> list = depTeamMapper.select(depTeam);
    for (DepTeam team : list) {
      DepVO depVO = BeanUtil.copyProperties(team, DepVO.class);
      depVOList.add(depVO);
    }
    return depVOList;
  }

  public PageOutput<AgentVO> getAgentList(AgentParam param) throws Exception {
    UserSession userSession = UserUtils.getTokenUser();
    param.setOrgId(userSession.getOrgId());
    if (null != param.getDepSwitches()) {
      if (param.getDepSwitches().contains("fifoSwitch")) {
        param.setFifoSwitch(1);
      }
      if (param.getDepSwitches().contains("robotSwitch")) {
        param.setRobotSwitch(1);
      }
      if (param.getDepSwitches().contains("smsSwitch")) {
        param.setSmsSwitch(1);
      }
      if (param.getDepSwitches().contains("visitSwitch")) {
        param.setVisitSwitch(1);
      }
    }
    Page page = this.setPage(param);
    List<AgentVO> list = depTeamMapper.getAgentList(param);
    return new PageOutput(page.getPageNum(), page.getPageSize(), (int) page.getTotal(), list);
  }

  public List<Long> selectTeamIdByOrgId(Long orgId){
    Example example=new Example(DepTeam.class);
    example.createCriteria().andEqualTo("orgId",orgId)
            .andEqualTo("type",DepTeamEnums.type.TEAM.getCode())
            .andEqualTo("status",DepTeamEnums.Status.NORMAL.getCode());
    example.selectProperties("id");
    List<DepTeam> list=super.selectByExample(example);
    if(CollectionUtils.isEmpty(list)){
      return new ArrayList<>();
    }
    List<Long> idList=list.stream().map(t->t.getId()).collect(Collectors.toList());
    return idList;
  }

  public List<DepTeamVO> getInspectTeam(Long userId) {
    List<DepTeam> depTeamList = depTeamMapper.getInspectTeam(userId);
    List<DepTeamVO> depVOS = depTeamList.stream().map(depTeam -> AuthBeanUtils.copy(depTeam, DepTeamVO.class)).collect(Collectors.toList());
    return depVOS;
  }

  /**
   * 通过组织id获得脱敏团队
   *
   * @param orgId           组织id
   * @param desensitization 脱敏
   * @return {@link List}<{@link DepTeam}>
   */
  public List<DepTeam> getDesensitizationTeamsByOrgId(Long orgId,DepTeamEnums.Desensitization desensitization) {
    DepTeam depTeam = new DepTeam();
    depTeam.setOrgId(orgId).setDesensitization(desensitization.getCode()).setStatus(DepTeamEnums.Status.NORMAL.getCode());
    return depTeamMapper.select(depTeam);
  }

  /**
   * 按组织id更新脱敏
   *
   * @param orgId           组织id
   * @param desensitization 脱敏
   * @return {@link Boolean}
   */
  public Boolean updateDesensitizationByOrgId(Long orgId, Set<Long> teamIds, DepTeamEnums.Desensitization desensitization) {
    DepTeam depTeam = new DepTeam();
    depTeam.setDesensitization(desensitization.getCode());

    Example example=new Example(DepTeam.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("orgId",orgId)
            .andEqualTo("status",DepTeamEnums.Status.NORMAL.getCode());

    if (ObjectUtil.isNotEmpty(teamIds)){
      criteria.andIn("id",teamIds);
    }

    updateByExampleSelective(depTeam,example);

    return true;
  }

  /**
   * 清除合作公司部门及团队中的度言信息
   * @param orgId
   */
  public void cleanDuyanInfo(Long orgId) {
    depTeamMapper.cleanDuyanInfo(orgId);
  }

  /**
   * 根据指定参数获取分公司信息
   *
   * @param orgId 公司
   * @param depIds 分公司id
   * @param teamIds 小组id
   * @return
   */
  public List<DepTeam> getDepTeamByDepParams(Long orgId, List<Long> depIds, List<Long> teamIds) {
    AssertUtil.notNull(orgId, "公司id不能为空");
    Example example=new Example(DepTeam.class);
    example.selectProperties("id, name, parentId");
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("orgId",orgId)
            .andEqualTo("status",DepTeamEnums.Status.NORMAL.getCode())
            .andEqualTo("type", DepTeamEnums.type.BRANCH_OFFICE.getCode());

    if (!CollectionUtils.isEmpty(depIds)) {
      criteria.andIn("id", depIds);
    }
    return depTeamMapper.selectByExample(example);
  }

  /**
   *
   * @param orgId 公司id
   * @param teamIds 小组id
   * @return
   */
  public List<DepTeam> getDepTeamByTeamParams(Long orgId, List<Long> teamIds, List<Long> depIds) {
    AssertUtil.notNull(orgId, "公司id不能为空");
    Example example=new Example(DepTeam.class);
    example.selectProperties("id, name");
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("orgId",orgId)
            .andEqualTo("status",DepTeamEnums.Status.NORMAL.getCode())
            .andEqualTo("type", DepTeamEnums.type.TEAM.getCode());

    if (!CollectionUtils.isEmpty(teamIds)) {
      criteria.andIn("id", teamIds);
    }
    if (!CollectionUtils.isEmpty(depIds)) {
      criteria.andIn("parentId", depIds);
    }
    return depTeamMapper.selectByExample(example);
  }

  /**
   * 根据度言关联id查询对应团队
   * @param teamId
   * @return
   */
  public DepTeam selectDepTeamByDuyanId(Long teamId) {
    Example example = new Example(DepTeam.class);
    example.createCriteria().andEqualTo("duyanReferId", teamId);
    List<DepTeam> depTeams = depTeamMapper.selectByExample(example);
    if (!CollectionUtils.isEmpty(depTeams)) {
      return depTeams.get(0);
    }
    return null;
  }

  /**
   * 更新团队编号
   *
   * @param depId 分公司/委外公司id
   * @param teamNo 团队编号
   */
  public void updateTeamNo(Long depId, String teamNo) {
    UserSession userSession = UserUtils.getTokenUser();
    if (!UserUtils.likeAdmin(userSession.getRoleId())) {
      throw new ApiException("当前用户非总公司管理员");
    }
    DepTeam depTeam = depTeamMapper.selectByPrimaryKey(depId);
    if (!Objects.equals(depTeam.getOrgId(), userSession.getOrgId())) {
      throw new ApiException("不存在该分公司");
    }
    depTeam.setTeamNo(teamNo);
    depTeam.setUpdateTime(new Date());
    depTeam.setUpdateBy(userSession.getId());
    depTeamMapper.updateByPrimaryKeySelective(depTeam);
  }

  /**
   * 更新列表团队编号
   *
   * @param list 团队编号列表
   */
  public void updateTeamNoList(List<DepNoParam> list) {
    UserSession userSession = UserUtils.getTokenUser();
    if (!UserUtils.likeAdmin(userSession.getRoleId())) {
      throw new ApiException("当前用户非总公司管理员");
    }
    if (CollectionUtils.isEmpty(list)) {
      throw new ApiException("团队编号列表不能为空");
    }
    List<Long> depIds = list.stream().map(DepNoParam::getId).collect(Collectors.toList());
    Map<Long, String> depMap = list.stream().collect(HashMap::new, (map, item) -> map.put(item.getId(), item.getTeamNo()), HashMap::putAll);
    List<DepTeam> depTeams = this.selectByIdList(depIds);
    if (CollectionUtils.isEmpty(depTeams)) {
      throw new ApiException("团队编号列表不能为空");
    }
    for (DepTeam depTeam : depTeams) {
      if (!Objects.equals(userSession.getOrgId(), depTeam.getOrgId())) {
        throw new ApiException("id为{0}的团队不属于当前公司", depTeam.getId());
      }
      depTeam.setTeamNo(depMap.get(depTeam.getId()));
    }
    depTeamMapper.updateBatchTeamNo(depTeams, userSession.getId());
  }


  /**
   * 获取分公司/委外公司信息列表
   *
   * @return
   */
  public List<DepTeamVO> getDepList() {
    UserSession userSession = UserUtils.getTokenUser();
    Example example = new Example(DepTeam.class);
    example.and().andEqualTo("orgId", userSession.getOrgId())
            .andEqualTo("status",DepTeamEnums.Status.NORMAL.getCode())
            .andEqualTo("type", DepTeamEnums.type.BRANCH_OFFICE.getCode());
    List<DepTeam> depTeams = depTeamMapper.selectByExample(example);
    List<DepTeamVO> depVOS = depTeams.stream().map(depTeam -> AuthBeanUtils.copy(depTeam, DepTeamVO.class)).collect(Collectors.toList());
    return depVOS;
  }

  /**
   * 获取设置过团队编号的列表
   *
   * @param orgId 公司id
   * @return
   */
  public Map<Long, DepTeam> getDepHavingNumberMap(Long orgId) {
    AssertUtil.notNull(orgId, "公司编号不可为空！");
    Example example = new Example(DepTeam.class);
    example.and().andEqualTo("orgId", orgId)
            .andEqualTo("status",DepTeamEnums.Status.NORMAL.getCode())
            .andEqualTo("type", DepTeamEnums.type.BRANCH_OFFICE.getCode())
            .andIsNotNull("teamNo");
    List<DepTeam> depTeams = depTeamMapper.selectByExample(example);
    // 过滤掉空字符串
    depTeams = depTeams.stream().filter(depTeam -> StringUtils.isNotBlank(depTeam.getTeamNo())).collect(Collectors.toList());
    Map<Long, DepTeam> depTeamMap = depTeams.stream().collect(Collectors.toMap(DepTeam::getId, f -> f));
    return depTeamMap;
  }

  /**
   * 更新结案序号
   *
   * @param depTeam
   */
  public void updateEndSeq(DepTeam depTeam) {
    AssertUtil.notNull(depTeam.getId(), "更新团队序号depId不能为空");
    String lockKey = KeyCache.DEP_END_SEQ_LOCK + depTeam.getId();
    try {
      if (!redisLock.tryLock(lockKey)) {
        log.error("锁超时，key:{}", lockKey);
        throw new ApiException("操作过于繁忙，请稍后再试");
      }
      depTeamService.updateByPrimaryKey(depTeam);
    } catch (Exception e) {
      log.error("获取设置结案序号锁发生错误：", e);
      throw new ApiException("获取设置结案序号锁发生错误");
    }finally {
      redisLock.unlock(lockKey);
    }
  }

  /**
   * 更新留案序号
   *
   * @param depTeam
   */
  public void updateDelaySeq(DepTeam depTeam) {
    AssertUtil.notNull(depTeam.getId(), "更新团队序号depId不能为空");
    String lockKey = KeyCache.DEP_DELAY_SEQ_LOCK + depTeam.getId();
    try {
      if (!redisLock.tryLock(lockKey)) {
        log.error("锁超时，key:{}", lockKey);
        throw new ApiException("操作过于繁忙，请稍后再试");
      }
      depTeamService.updateByPrimaryKey(depTeam);
    } catch (Exception e) {
      log.error("获取设置留案序号锁发生错误：", e);
      throw new ApiException("获取设置留案序号锁发生错误");
    }finally {
      redisLock.unlock(lockKey);
    }
  }

  /**
   * 重置分公司/委外公司结案序号、留案序号不为0的序号
   *
   */
  public void resetDepSeq(){
    depTeamMapper.resetDepSeq(null);
  }

  /**
   * 序号补零，填充长度至三位，超过3位不做操作
   *
   * @param seq 序号
   * @return
   */
  public String fillSeqZero(int seq) {
    String numStr = Integer.toString(seq);
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < 3 - numStr.length(); i++) {
      sb.append("0");
    }
    sb.append(numStr);
    return sb.toString();
  }

  /**
   * 查询未配置全局管控的直属小组
   *
   * @return
   */
  public List<DepTeamVO> getNotRelCtrlTeam() {
    UserSession userSession = UserUtils.getTokenUser();
    AssertUtil.notNull(userSession.getOrgId(), "公司id不可为空");
    Example ctrlExample = new Example(CtrlTypeRel.class);
    ctrlExample.and().andEqualTo("orgId", userSession.getOrgId())
            .andEqualTo("type", 1);
    List<CtrlTypeRel> ctrlTypeRels = ctrlTypeRelService.selectByExample(ctrlExample);
    List<Long> teamIds = ctrlTypeRels.stream().map(CtrlTypeRel::getTeamId).distinct().collect(Collectors.toList());

    Example example = new Example(DepTeam.class);
    example.and().andEqualTo("orgId", userSession.getOrgId())
            .andEqualTo("type", DepTeamEnums.type.TEAM.getCode())
            .andEqualTo("status", DepTeamEnums.Status.NORMAL.getCode());
    if (!CollectionUtils.isEmpty(teamIds)) {
      example.and().andNotIn("id", teamIds);
    }

    List<DepTeam> depTeams = depTeamMapper.selectByExample(example);
    List<DepTeamVO> depVOS = depTeams.stream().map(depTeam -> AuthBeanUtils.copy(depTeam, DepTeamVO.class)).collect(Collectors.toList());
    return depVOS;
  }

  public Long uploadDeptFile(MultipartFile file, Long depId) {
    UserSession loginUser = UserUtils.getTokenUser();
    Date currentDate = new Date();
    Date expireDate = DateUtils.addDays(currentDate, 10 * 365);
    String fileName = file.getOriginalFilename();
    UploadFileInfo uploadFileInfo = new UploadFileInfo();
    uploadFileInfo.setFile(file)
            .setFileName(fileName)
            .setExpireDate(expireDate)
            .setBucket(systemConfig.getCaseFilesBucket())
            .setLocalUrl(systemConfig.getCaseFilePath() + File.separator + fileName);
    FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
    String fileUrl = fileStorageStrategy.uploadFile(uploadFileInfo);

    OrgDepFile depFile = new OrgDepFile();
    depFile.setDepId(depId);
    depFile.setOrgId(loginUser.getOrgId());
    depFile.setFileUrl(fileUrl);
    depFile.setFileName(fileName);
    depFile.setStatus(0);
    depFile.setCreateBy(loginUser.getId());
    depFile.setUpdateBy(loginUser.getId());
    depFile.setCreateTime(new Date());
    depFile.setUpdateTime(new Date());
    orgDepFileMapper.insert(depFile);
    return depFile.getId();
  }

  public PageOutput<BankAgentVO> getBankAgentList(BankAgentParam param) {
    UserSession userSession = UserUtils.getTokenUser();
    param.setOrgId(userSession.getOrgId());
    Page page = this.setPage(param);
    List<BankAgentVO> list = depTeamMapper.getBankAgentList(param);
    return new PageOutput(page.getPageNum(), page.getPageSize(), (int) page.getTotal(), list);
  }

  /**
   * 新增自定义账龄
   *
   * @param param 新增自定义账龄参数
   */
  public void addAccountAge(AccountAgeAddParam param) {
    DepTeam depTeam = depTeamMapper.selectByPrimaryKey(param.getDepId());
    AssertUtil.notNull(depTeam, "不存在对应id的委外公司");
    String accountAgeFields = depTeam.getAccountAgeFields();
    List<String> accountAgeList = new ArrayList<>();
    if (StringUtils.isNotBlank(accountAgeFields)) {
      accountAgeList = JSON.parseArray(accountAgeFields, String.class);
    }
    if (accountAgeList.contains(param.getAccountAge())) {
      throw new ApiException("该账龄已添加，无需重新添加");
    }
    accountAgeList.add(param.getAccountAge());
    String updateAccountAge = JSON.toJSONString(accountAgeList);

    UserSession userSession = UserUtils.getTokenUser();
    depTeam.setUpdateBy(userSession.getId());
    depTeam.setUpdateTime(new Date());
    depTeam.setAccountAgeFields(updateAccountAge);
    depTeamMapper.updateByPrimaryKeySelective(depTeam);
  }

  /**
   * 查询自定义账龄
   *
   * @param depId 委外公司id
   * @return 自定义账龄
   */
  public List<String> queryAccountAgeList(Long depId) {
    DepTeam depTeam = depTeamMapper.selectByPrimaryKey(depId);
    AssertUtil.notNull(depTeam, "不存在对应id的委外公司");
    UserSession userSession = UserUtils.getTokenUser();
    String accountAgeFields = depTeam.getAccountAgeFields();
    List<String> accountAgeList = new ArrayList<>();
    if (StringUtils.isNotBlank(accountAgeFields)) {
      accountAgeList = JSON.parseArray(accountAgeFields, String.class);
    } else {
      // 设置默认系统账龄
      accountAgeList = AccountAgeEnums.DefaultFields.queryAllDefault();
      String defaultFields = JSON.toJSONString(accountAgeList);

      depTeam.setUpdateTime(new Date());
      depTeam.setUpdateBy(userSession.getId());
      depTeam.setAccountAgeFields(defaultFields);
      depTeamMapper.updateByPrimaryKeySelective(depTeam);
    }
    return accountAgeList;
  }
  public OrgDepTeamTreeVO getAllDepTreeList() throws Exception {
    UserSession userSession = UserUtils.getTokenUser();
    Company company = companyService.selectByPrimaryKey(userSession.getOrgId());
    OrgDepTeamTreeVO vo = new OrgDepTeamTreeVO();
    BeanUtils.copyProperties(company,vo);
    Example example = new Example(DepTeam.class);
    example.and().andEqualTo("state",0);
    example.and().andEqualTo("status", DepTeamEnums.Status.NORMAL.getCode());
    example.and().andEqualTo("orgId",userSession.getOrgId());
    List<DepTeam> list = depTeamMapper.selectByExample(example);
    List<DepTeamTreeVO> depTeamTreeVOS = BeanUtil.copyPropertiesFromList(list, DepTeamTreeVO.class);
    List<DepTeamTreeVO> deps = TreeUtils.asTree(depTeamTreeVOS,
      DepTeamTreeVO::getId,
      DepTeamTreeVO::getParentId, DepTeamTreeVO::setChildren,
      t->Objects.equals(t.getParentId(),userSession.getOrgId()));
    for (DepTeamTreeVO depTeamTreeVO : deps) {
      if (!CollectionUtils.isEmpty(depTeamTreeVO.getChildren())) {
        depTeamTreeVO.getChildren().forEach(t -> t.setIsBankAgent(depTeamTreeVO.getIsBankAgent()));
      }
    }
    vo.setDeps(deps);
    return vo;
  }
  public List<Long> getBankAgentDepTeamIds(List<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return new ArrayList<>();
    }
    Example example = new Example(DepTeam.class);
    example.and().andIn("id", ids).andEqualTo("isBankAgent", DepTeamEnums.IsBankAgent.YES.getCode());
    List<DepTeam> depTeams = depTeamMapper.selectByExample(example);
    return depTeams.parallelStream().map(DepTeam::getId).collect(Collectors.toList());
  }

  /**
   * 按名称获取
   *
   * @param teamName 团队名称
   * @param orgId    组织id
   * @return {@link List}<{@link DepTeam}>
   */
  public List<DepTeam> getByName(String teamName, Long orgId) {
    if (StrUtil.isBlank(teamName)){
      return new ArrayList<>();
    }
    AssertUtil.notNull(orgId, "公司id不可为空！");
    Example example = new Example(DepTeam.class);
    example.and().andEqualTo("orgId", orgId)
      .andEqualTo("status", DepTeamEnums.Status.NORMAL.getCode())
      .andEqualTo("name", teamName);

    return depTeamMapper.selectByExample(example);
  }


  public List<DepTeam> queryAllData(List<Long> ids, Map<Long, DepTeam> allDepTeamMap, Long orgId) {
    Set<DepTeam> result = new HashSet<>();

    for (Long depTeamId : ids) {
      if (allDepTeamMap.containsKey(depTeamId)) {
        DepTeam d = allDepTeamMap.get(depTeamId);
        result.add(d);

        if (!Objects.equals(d.getParentId() ,orgId)) {
          result.addAll(queryAllData(new ArrayList<Long>(Collections.singleton(d.getParentId())), allDepTeamMap, orgId));
        }
      }
    }
    return new ArrayList<>(result);
  }


  public DepVO convertDepTeamToVO(DepTeam depTeam) {
    DepVO depVO = new DepVO();
    // depVO.setChildren(depTeam.getUnderTeam());
    depVO.setName(depTeam.getName());
    depVO.setType(depTeam.getType());
    // depVO.setParentName(depTeam.getParentName());
    depVO.setParentId(depTeam.getParentId());
    // depVO.setOrgName(depTeam.getOrgName());
    depVO.setOrgId(depTeam.getOrgId());
    depVO.setBusinessNo(depTeam.getBusinessNo());
    depVO.setIsAgent(depTeam.getIsAgent());
    depVO.setIsBankAgent(depTeam.getIsBankAgent());
    depVO.setDepCode(depTeam.getDepCode());
    depVO.setDepAddr(depTeam.getDepAddr());
    depVO.setRefName(depTeam.getRefName());
    depVO.setRefPhoneNo(depTeam.getRefPhoneNo());
    // depVO.setFifoSwitch(depTeam.getFifoSwitch());
    // depVO.setRobotSwitch(depTeam.getRobotSwitch());
    // depVO.setSmsSwitch(depTeam.getSmsSwitch());
    // depVO.setVisitSwitch(depTeam.getVisitSwitch());
    depVO.setDuyanReferId(depTeam.getDuyanReferId());
    depVO.setFunctionIds(StrUtil.isNotBlank(depTeam.getFunctionIds()) ? Arrays.stream(depTeam.getFunctionIds().split(",")).map(Long::valueOf).collect(Collectors.toList()) : null);
    // depVO.setFunctionTree(depTeam.getFunctionTree());
    // depVO.setDepFileList(depTeam.getDepFileList());
    depVO.setBusinessTypes(StrUtil.isNotBlank(depTeam.getBusinessTypes()) ? Arrays.stream(depTeam.getBusinessTypes().split(",")).map(Integer::valueOf).collect(Collectors.toList()) : null);
    depVO.setState(depTeam.getState());
    depVO.setVisitBaseFee(depTeam.getVisitBaseFee());
    // depVO.setRegionSwitch(depTeam.getRegionSwitch());
    // depVO.setRegionCityCodes(depTeam.getRegionCityCodes());
    depVO.setEmail(depTeam.getEmail());
    depVO.setId(depTeam.getId());
    depVO.setStatus(depTeam.getStatus());
    depVO.setDesc(depTeam.getDesc());
    depVO.setCreateTime(depTeam.getCreateTime() != null ? depTeam.getCreateTime().getTime() : null);
    depVO.setUpdateTime(depTeam.getUpdateTime() != null ? depTeam.getUpdateTime().getTime() : null);
    depVO.setCreateBy(depTeam.getCreateBy() != null ? depTeam.getCreateBy().toString() : null);
    depVO.setUpdateBy(depTeam.getUpdateBy() != null ? depTeam.getUpdateBy().toString() : null);
    return depVO;
  }


  public void build(Map<Long, List<DepVO>> children, List<DepVO> parents) {
    List<DepVO> childParents = new ArrayList<>();
    for (DepVO DepVO : parents) {
      List<DepVO> childFus = children.get(DepVO.getId());
      if (childFus == null) {
        continue;
      }
      DepVO.setChildren(childFus);
      childParents.addAll(childFus);
    }
    if (CollectionUtils.isEmpty(childParents)) {
      return;
    }
    build(children, childParents);
  }

}
