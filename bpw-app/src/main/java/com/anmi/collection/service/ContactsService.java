package com.anmi.collection.service;

import cn.duyan.thread.DuyanThreadExecutor;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.constant.CommonConstant;
import com.anmi.collection.constant.ContactsConstant;
import com.anmi.collection.constant.RegexConstant;
import com.anmi.collection.dto.CaseContact;
import com.anmi.collection.dto.CaseFollowInfo;
import com.anmi.collection.dto.ContactsExcel;
import com.anmi.collection.dto.OpenCaseContactDTO;
import com.anmi.collection.dto.cmbc.ResponseResult;
import com.anmi.collection.encrypt.EncryptProperties;
import com.anmi.collection.encrypt.EncryptService;
import com.anmi.collection.entity.requset.cases.SingleMessageParam;
import com.anmi.collection.entity.requset.open.ContactParam;
import com.anmi.collection.entity.requset.sys.contacts.*;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.contacts.CaseContactVO;
import com.anmi.collection.entity.response.contacts.ContactsVO;
import com.anmi.collection.entity.response.open.TaskSubmitVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.MessageManager;
import com.anmi.collection.manager.cmbc.CmbcManager;
import com.anmi.collection.mapper.ContactsMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.collection.utils.easyexcel.EasyExcelUtils;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseQueryResult;
import com.anmi.domain.principal.Delt;
import com.anmi.domain.sys.FileStore;
import com.anmi.domain.user.ContactTypeConfig;
import com.anmi.domain.user.Contacts;
import com.anmi.domain.user.User;
import com.anmi.domain.yunpian.SmsSend;
import com.anmi.domain.yunpian.SmsTemplate;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/** Created by dongwang on 2018-12-08. */
@Service
@Slf4j
public class ContactsService extends BaseService<Contacts> {
  @Autowired private ContactsMapper contactsMapper;
  @Autowired private CaseService caseService;
  @Autowired private  ContactsOperationRecordService contactsOperationRecordService;
  @Autowired private StringRedisTemplate stringRedisTemplate;
  @Autowired private AsyncTaskService asyncTaskService;
  @Autowired private MessageManager messageManager;
  @Autowired private SmsSendService smsSendService;
  @Autowired private SmsTemplateService smsTemplateService;
  @Autowired private UserService userService;
  @Autowired private CaseOperationService caseOperationService;
  @Autowired private DeltService deltService;
  @Autowired private CmbcManager cmbcManager;
  @Autowired private ContactTypeConfigService contactTypeConfigService;
  @Autowired private FileStoreService fileStoreService;
  @Autowired private RedisUtil redisUtil;
  @Autowired private EncryptProperties encryptProperties;
  @Autowired private EncryptService encryptService;

  private static final String ASYNC_PREFIX = "async-";


  private DuyanThreadExecutor threadExecutor=new DuyanThreadExecutor("contacts-pool");

  public PageOutput<ContactsVO> getListForApp(ContactsParam param) {
    Long caseId = param.getCaseId();
    Case caseInfo = caseService.selectByPrimaryKey(caseId);
    Long relId = caseInfo.getDebtId() == null ? caseInfo.getId() : caseInfo.getDebtId();
    Integer isConjoint =
            caseInfo.getDebtId() == null
                    ? Contacts.IsConjoint.NO.getCode()
                    : Contacts.IsConjoint.YES.getCode();
    Example example = new Example(Contacts.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("relId", relId).andEqualTo("isConjoint", isConjoint);
    if (param.getStatus() != null) {
      criteria.andEqualTo("status", param.getStatus());
    }
    //过滤掉已删除的
    criteria.andNotEqualTo("status", -1);
    example.orderBy("createTime").desc();
    PageParam pageParam = new PageParam(param.getPage(), param.getLimit());
    Page page = super.setPage(pageParam);
    List<Contacts> contactsList = super.selectByExample(example);
    List<ContactsVO> contactsVOS = new ArrayList<>();
    contactsList.forEach(contacts -> {
      ContactsVO contactsVO = AuthBeanUtils.copy(contacts, ContactsVO.class);
      String mobile = contacts.getMobile();
      CaseFollowInfo followInfo = caseOperationService.getCaseContactLastFollowInfo(caseId, mobile);
      contactsVO.setFollowCount(followInfo.getFollowCount());
      contactsVO.setLastFollowTime(followInfo.getLastFollowTime());
      contactsVOS.add(contactsVO);
    });
    PageOutput<ContactsVO> pageOutput = new PageOutput(page.getPageNum(), page.getPageSize(),
                    contactsVOS != null ? (int) page.getTotal() : contactsVOS.size(), contactsVOS);
    return pageOutput;
  }
  public List<CaseContactVO> getByCaseId(ContactsParam param) {
    Case caseInfo = caseService.selectByPrimaryKey(param.getCaseId());
    Long relId = caseInfo.getDebtId() == null ? caseInfo.getId() : caseInfo.getDebtId();
    Integer isConjoint =
      caseInfo.getDebtId() == null
        ? Contacts.IsConjoint.NO.getCode()
        : Contacts.IsConjoint.YES.getCode();
    Example example = new Example(Contacts.class);
    example.orderBy("sign").desc().orderBy("status").asc().orderBy("updateTime").desc();
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("relId", relId).andEqualTo("isConjoint", isConjoint);
    if (param.getStatus() != null) {
      criteria.andEqualTo("status", param.getStatus());
    }
    //过滤掉已删除的
    criteria.andNotEqualTo("status", -1);
    List<Contacts> select = super.selectByExample(example);
    if(CollectionUtils.isEmpty(select)){
      return Collections.emptyList();
    }
    List<CaseContactVO> result = com.anmi.collection.utils.dict.BeanUtils.collectionCopy(select,CaseContactVO.class);
    Delt delt = deltService.selectByPrimaryKey(caseInfo.getOrgDeltId());
    if (delt != null && Objects.equals(delt.getCmbcFlag(), 1)) {
      ResponseResult<Map<?,?>> cmbcResult = cmbcManager.getCaseInfo(caseInfo.getOrgId(),caseInfo.getOutSerialNo(), LogbackUtil.getTrace());
      if(Objects.equals(cmbcResult.getErrCode(),0)){
        List<Map> cmbcContacts = (List<Map>)cmbcResult.getData().get("custContactList");
        for(Map cmbcContact:cmbcContacts){
          CaseContactVO vo=new CaseContactVO();
          vo.setName(String.valueOf(cmbcContact.get("contactNm")));
          vo.setRelationType(String.valueOf(cmbcContact.get("contactRel")));
          vo.setField(String.valueOf(cmbcContact.get("field")));
          vo.setMobile(String.valueOf(cmbcContact.get("contactTel")));
          result.add(vo);
        }
        Map caseCust = ((Map)cmbcResult.getData().get("caseCust"));
        Map caseBase = ((Map)cmbcResult.getData().get("caseBase"));

        if (!StringUtils.isBlank((String)caseCust.get("coTelno"))) {
          CaseContactVO coTelno = new CaseContactVO();
          coTelno.setName(String.valueOf(caseBase.get("custName")));
          coTelno.setRelationType("本人");
          coTelno.setField("coTelno");
          coTelno.setMobile(String.valueOf(caseCust.get("coTelno")));
          result.add(coTelno);
        }

        if (!StringUtils.isBlank((String)caseCust.get("rsdncTel"))) {
          CaseContactVO coTelno = new CaseContactVO();
          coTelno.setName(String.valueOf(caseBase.get("custName")));
          coTelno.setRelationType("本人");
          coTelno.setField("rsdncTel");
          coTelno.setMobile(String.valueOf(caseCust.get("rsdncTel")));
          result.add(coTelno);
        }
        if (!StringUtils.isBlank((String) caseCust.get("mblphNo"))) {
          CaseContactVO coTelno = new CaseContactVO();
          coTelno.setName(String.valueOf(caseBase.get("custName")));
          coTelno.setRelationType("本人");
          coTelno.setField("mblphNo");
          coTelno.setMobile(String.valueOf(caseCust.get("mblphNo")));
          result.add(coTelno);
        }
      }
    }
    return result;
  }

  public PageOutput<ContactsVO> getList(ContactsParam param, PageParam pageParam) throws Exception {
    Case caseInfo = caseService.selectByPrimaryKey(param.getCaseId());
    Long relId = caseInfo.getDebtId() == null ? caseInfo.getId() : caseInfo.getDebtId();
    Integer isConjoint =
        caseInfo.getDebtId() == null
            ? Contacts.IsConjoint.NO.getCode()
            : Contacts.IsConjoint.YES.getCode();
    Example example = new Example(Contacts.class);
    example.orderBy("sign").desc().orderBy("status").asc().orderBy("updateTime").desc();
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("relId", relId).andEqualTo("isConjoint", isConjoint);
    if (param.getStatus() != null) {
      criteria.andEqualTo("status", param.getStatus());
    }
    //过滤掉已删除的
    criteria.andNotEqualTo("status", -1);
    Page page = super.setPage(pageParam);
    List<Contacts> select = super.selectByExample(example);
    List<ContactsVO> contactsVOList = BeanUtil.copyPropertiesFromList(select, ContactsVO.class);
    List<Long> contactTypeIds = contactsVOList.stream().map(ContactsVO::getContactTypeId).distinct().collect(Collectors.toList());
    Map<Long, String> selectContactTypeMap = contactTypeConfigService.selectContactTypeMap(contactTypeIds);
    contactsVOList.forEach(item -> {
      if (selectContactTypeMap.containsKey(item.getContactTypeId())) {
        item.setContactTypeName(selectContactTypeMap.get(item.getContactTypeId()));
      }
    });
    //        // 需要隐藏手机号和身份证
    //        String mobile = null;
    //        String idCard = null;
    //        boolean isChairMan = false;
    //        if (UserUtils.isChairMan()) {
    //            isChairMan = true;
    //        }
    //        boolean isHiddenIdCard =
    // Boolean.valueOf(String.valueOf(stringRedisTemplate.opsForHash().get(KeyCache.ORG_ENABLE_PREFIX + getOrgId()
    //                , "hidden_id_card")));
    //        for (Contacts contacts : select) {
    //            idCard = contacts.getIdCard();
    //            if (isHiddenIdCard) {
    //                if (isChairMan) {
    //                    // 需要隐藏
    //                    idCard = idCard.substring(0, idCard.length() - 4) + "****";
    //                }
    //            }
    //            contacts.setIdCard(idCard);
    //        }
    PageOutput pageOutput =
        new PageOutput(
            page.getPageNum(),
            page.getPageSize(),
            contactsVOList != null ? (int) page.getTotal() : contactsVOList.size(),
            contactsVOList);
    return pageOutput;
  }

  /**
   * 批量同步
   *
   * @param operateList
   */
  @Transactional(rollbackFor = Exception.class)
  public void syncContacts(ContactsSyncList operateList) {
    if (operateList.getCaseId() == null) {
      throw new ApiException("债务人身份证号必传！");
    }
    if (CommonUtils.isEmpty(operateList.getSyncs())) {
      throw new ApiException("通讯录必传！");
    }
    Case caseInfo = caseService.selectByPrimaryKey(operateList.getCaseId());
    if (caseInfo == null) {
      throw new ApiException("案件不存在");
    }
    Long relId = caseInfo.getDebtId() == null ? caseInfo.getId() : caseInfo.getDebtId();
    Integer isConjoint = caseInfo.getDebtId() == null ? 0 : 1;
    UserSession userSession = UserUtils.getTokenUser();
    Long orgId = userSession.getOrgId();
    Long userId = userSession.getId();
    List<ContactsSync> syncs = operateList.getSyncs();
    List<Contacts> contacts = new ArrayList<>();
    for (ContactsSync sync : syncs) {
      Contacts con = createContacts(sync, orgId, userId, relId, isConjoint);
      if (con != null) {
        contacts.add(con);
      }
    }
    if (CommonUtils.isEmpty(contacts)) {
      throw new ApiException("通讯录已存在您导入的联系人，无需重复同步！");
    }
    boolean flag = false;
    Case ca = caseService.selectByPrimaryKey(operateList.getCaseId());
    for (Map.Entry<String, String> entry : ca.getFieldJson().entrySet()) {
      String key = entry.getKey();
      if ("contacts".equals(key)) {
        flag = true;
      }
    }
    if (flag) {
      // 设置为已同步过
      ca.setSyncStatus(1);
    }
    caseService.updateByPrimaryKeySelective(ca);
    if (!CommonUtils.isEmpty(contacts)) {
      batchUpdateContact(contacts);
    }
  }

  private Contacts createContacts(
      ContactsSync sync, Long orgId, Long userId, Long relId, Integer isConjoint) {
    Contacts con = new Contacts();
    con.setOrgId(orgId);
    String relationType = sync.getRelationType();
    if (relationType != null && relationType.trim().length() > 0) {
      relationType = relationType.trim();
      if ("本人".equals(relationType)) {
        con.setOwnSign(1);
      } else {
        con.setOwnSign(0);
      }
      con.setRelationType(relationType);
    } else {
      // 关系为空
      con.setOwnSign(0);
      con.setRelationType("");
    }
    if (sync.getName() == null || StringUtils.isEmpty(sync.getName().trim())) {
      con.setName("");
    } else {
      con.setName(sync.getName().trim());
    }
    if (sync.getMobile() == null || StringUtils.isEmpty(sync.getMobile().trim())) {
      throw new ApiException("手机号码不能为空！");
    } else {
      if (!RegexConstant.isMatch(RegexConstant.REGEX_PHONE, sync.getMobile().trim())) {
        throw new ApiException("手机号【" + sync.getMobile().trim() + "】" + "格式错误，请重新填写后上传！");
      }
      con.setMobile(sync.getMobile().trim());
    }
    con.setCreateTime(new Date());
    con.setUpdateTime(con.getCreateTime());
    con.setCreateBy(userId);
    con.setUpdateBy(userId);
    con.setStatus(0);
    con.setWarning(0);
    con.setSign(0);
    con.setRelId(relId);
    con.setIsConjoint(isConjoint);
    return con;
  }

  public void changeStatus(ContactsChangeStatusParam param) {
    if (param.getId() == null || param.getStatus() == null) {
      throw new ApiException("参数不能为空");
    }
    Contacts contacts = selectByPrimaryKey(param.getId());
    if (contacts == null) {
      throw new ApiException("联系人不存在");
    }
    if (param.getStatus().equals(Contacts.Status.NORMAL.getCode())
        && contacts.getStatus().equals(Contacts.Status.NORMAL.getCode())) {
      throw new ApiException("联系人已是正常状态");
    }
    if (param.getStatus().equals(Contacts.Status.INVALID.getCode())
        && contacts.getStatus().equals(Contacts.Status.INVALID.getCode())) {
      throw new ApiException("联系人已是失效状态");
    }
    Contacts domain = new Contacts();
    domain.setId(param.getId());
    domain.setStatus(param.getStatus());
    domain.setUpdateBy(getTokenUser().getId());
    domain.setUpdateTime(new Date());
    updateByPrimaryKeySelective(domain);
  }

  public void deleteContacts(List<Long> idList) {
    if (idList.size() == 1) {
      Contacts contacts = selectByPrimaryKey(idList.get(0));
      if (contacts == null) {
        throw new ApiException("联系人不存在");
      }
      if (contacts.getRelationType() != null && contacts.getRelationType().equals("本人")) {
        throw new ApiException("本人不能删除");
      }
      deleteByPrimaryKey(idList.get(0));
    } else {
      Example example = new Example(Contacts.class);
      example.createCriteria().andIn("id", idList);
      deleteByExample(example);
    }
  }

  public void deleteContactsByRelIds(Long orgId, List<Long> relIdList, Integer isConjoint) {
    Example example = new Example(Contacts.class);
    List<List<Long>> groupList = CmUtil.splitList(relIdList, 500);
    for (List<Long> list : groupList) {
      example.clear();
      example.createCriteria().andIn("relId", list).andEqualTo("isConjoint", isConjoint);
      deleteByExample(example);
    }
  }

  public void batchUpdateContact(List<Contacts> contactsList) {
    if (CollectionUtils.isEmpty(contactsList)) {
      return;
    }
    contactsMapper.insertOrUpdate(contactsList);
  }

  /**
   * 由于联系人的删除时逻辑删除，并且某些字段加了唯一索引
   * 所以如果数据库已经存在逻辑删除的数据，这时候先物理删除这条数据再插入新数据
   * @param relId
   * @param isConjoint
   * @param mobile
   * @return
   */
  public Boolean isExist(Long relId, Integer isConjoint, String mobile) {
    if (encryptProperties.getEnable()) {
      mobile = encryptService.encrypt(mobile);
    }
    Contacts query = new Contacts();
    query.setRelId(relId);
    query.setIsConjoint(isConjoint);
    query.setMobile(mobile);
    Contacts contacts = contactsMapper.selectOne(query);
    if (contacts == null) {
      return false;
    }
    if (Objects.equals(contacts.getStatus(), 0)) {
      return true;
    }else {
      contactsMapper.deleteByPrimaryKey(contacts.getId());
      return false;
    }

  }

  public Contacts add(ContactsOperate create) {
    UserSession userSession = UserUtils.getTokenUser();
    Case caseInfo = caseService.selectByPrimaryKey(create.getCaseId());
    if (caseInfo == null) {
      throw new ApiException("案件不存在");
    }
    if (create.getMobile() == null || StringUtils.isEmpty(create.getMobile().trim())) {
      throw new ApiException("联系人手机号码不能为空！");
    }
    boolean isMatch = RegexConstant.isMatch(RegexConstant.REGEX_PHONE, create.getMobile().trim());
    if (!isMatch) {
      throw new ApiException("联系人手机号码不能输入中文！");
    }
    if (create.getName() == null || StringUtils.isEmpty(create.getName().trim())) {
      create.setName("");
    }
    if (create.getRelationType() == null || StringUtils.isEmpty(create.getRelationType().trim())) {
      create.setRelationType("");
    }
    if (create.getContactTypeId() != null) {
      ContactTypeConfig contactTypeConfig = contactTypeConfigService.selectByPrimaryKey(create.getContactTypeId());
      if (!Objects.equals(contactTypeConfig.getOrgId(), userSession.getOrgId())) {
        throw new ApiException("号码类型不存在！");
      }
    }
    Contacts contacts = new Contacts();
    BeanUtils.copyProperties(create, contacts);
    Long orgId = UserUtils.getParentOrgId();
    contacts.setOrgId(orgId);
    contacts.setOwnSign(0);
    // 共债关联，总公司下每个债务人对应一个通讯录,非共债关联，每个案件对应一个通讯录
    Long relId = caseInfo.getDebtId() == null ? caseInfo.getId() : caseInfo.getDebtId();
    Integer isConjoint = caseInfo.getDebtId() == null ? 0 : 1;
    contacts.setRelId(relId);
    contacts.setIsConjoint(isConjoint);
    String val = contacts.getRelId() + "," + contacts.getIsConjoint() + "," + contacts.getMobile();
    if (isExist(contacts.getRelId(), isConjoint, contacts.getMobile())) {
      throw new ApiException("联系人已存在，请勿重复添加");
    }
    if (contacts.getContactTypeId() != null) {
      // 号码类型选择系统字段本人，则设置债务人标识
      ContactTypeConfig contactTypeConfig = contactTypeConfigService.selectByPrimaryKey(contacts.getContactTypeId());
      if(ContactTypeConfigEnums.IsSys.isSelf(contactTypeConfig.getName(), contactTypeConfig.getIsSys())) {
        contacts.setOwnSign(1);
      }
    }
    contacts.setCreateBy(userSession.getId());
    contacts.setUpdateBy(userSession.getId());
    super.insertSelective(contacts);
    // 返回联系人信息有可能已经被加密存储了，所以这里直接把传的返回给前端
    contacts.setName(create.getName());
    contacts.setMobile(create.getMobile());
    return contacts;
  }

  public Contacts selectByMobile(Case caseInfo, String mobile) {
    if (encryptProperties.getEnable()) {
      mobile = encryptService.encrypt(mobile);
    }
    Long relId = caseInfo.getDebtId();
    Integer isConjoint = Contacts.IsConjoint.YES.getCode();
    if (relId == null) {
      relId = caseInfo.getId();
      isConjoint = Contacts.IsConjoint.NO.getCode();
    }
    Contacts entity = new Contacts();
    entity.setRelId(relId);
    entity.setIsConjoint(isConjoint);
    entity.setMobile(mobile);
    List<Contacts> contactsList = select(entity);
    if (CollectionUtils.isEmpty(contactsList)) {
      return null;
    }
    return contactsList.get(0);
  }

  public PageOutput<ContactsVO> getContactsList(ContactsParam param) {
    Example example = new Example(Contacts.class);
    UserSession tokenUser = UserUtils.getTokenUser();
    example.and().andEqualTo("orgId", tokenUser.getOrgId());
    //过滤掉已删除的
    example.and().andNotEqualTo("status", -1);

    if (!CollectionUtils.isEmpty(param.getNames())) {
      if (param.getNames().size() == 1) {
        example.and().andLike("name", "%" + param.getNames().get(0) + "%");
      }else {
        example.and().andIn("name", param.getNames());
      }
    }
    if (!CollectionUtils.isEmpty(param.getMobiles())) {
      if (param.getMobiles().size() == 1) {
        example.and().andLike("mobile", "%" + param.getMobiles().get(0) + "%");
      } else {
        example.and().andIn("mobile", param.getMobiles());
      }
    }
    if (StringUtils.isNotBlank(param.getRelationType())) {
      example.and().andLike("relationType", "%" + param.getRelationType() + "%");
    }
    if (param.getStatus() != null) {
      example.and().andEqualTo("status", param.getStatus());
    }
    example.orderBy("updateTime").desc();
    PageParam pageParam = new PageParam(param.getPage(), param.getLimit());
    Page page = super.setPage(pageParam);
    List<Contacts> contactsList = contactsMapper.selectByExample(example);
    List<Long> contactTypeId = contactsList.stream().map(Contacts::getContactTypeId).collect(Collectors.toList());
    Map<Long, String> conactTypeMap = contactTypeConfigService.selectContactTypeMap(contactTypeId);
    List<ContactsVO> contactsVOList = contactsList.stream().map(contacts -> {
      ContactsVO contactsVO = toContactsVO(contacts);
      contactsVO.setContactTypeName(conactTypeMap.get(contacts.getContactTypeId()));
      return contactsVO;
    }).collect(Collectors.toList());
    return new PageOutput<ContactsVO>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), contactsVOList);
  }

  @Transactional(rollbackFor = Exception.class)
  public void delContactsBatch(ContactsParam param) {
    UserSession tokenUser = UserUtils.getTokenUser();
    Example example = new Example(Contacts.class);
    example.and().andEqualTo("orgId", tokenUser.getOrgId());
    //按照条件删除时排除已删除的数据，否则重复删除导致删除数据和当初查询的数据不一致
    example.and().andNotEqualTo("status", -1);
    if (!CollectionUtils.isEmpty(param.getNames())) {
      example.and().andIn("name", param.getNames());
    }
    if (!CollectionUtils.isEmpty(param.getMobiles())) {
      example.and().andIn("mobile", param.getMobiles());
    }
    if (StringUtils.isNotBlank(param.getRelationType())) {
      example.and().andLike("relationType", param.getRelationType());
    }
    if (param.getStatus() != null) {
      example.and().andEqualTo("status", param.getStatus());
    }
    //删除条件在ids中
    if (!CollectionUtils.isEmpty(param.getIds())) {
      example.and().andIn("id", param.getIds());
    }
    Contacts contacts = new Contacts();
    //设置删除标志位
    contacts.setStatus(-1);
    contacts.setUpdateTime(new Date());
    int count = contactsMapper.updateByExampleSelective(contacts, example);
    if (count < 1) {
      throw new ApiException("根据条件删除数据0条");
    }
    threadExecutor.submit(() -> {
      contactsOperationRecordService.addRecord(tokenUser.getOrgId(), tokenUser.getId(), count, ContactsConstant.ACTION_DELETE);
    });
  }

  /**
   * 异步实现联系人手机号失效
   * @param param
   */
  void asyncContactsBatch(ContactsParam param) {
    UserSession userSession = UserUtils.getTokenUser();
    //判断是否有相同操作
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(param).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }
    Long taskId = asyncTaskService.createUpdateContactsStatusTask(param, userSession, 0L, AsyncTaskEnums.Status.ING.getCode());
    AsyncTask asyncTask = asyncTaskService.selectByPrimaryKey(taskId);
    String queryString = asyncTask.getFieldJson().get("query");
    ContactsParam temp = JsonUtils.fromJson(queryString, ContactsParam.class);
    stringRedisTemplate.opsForList().leftPush(KeyCache.CONTACT_UPDATE_STATUS_TASK_ID_LIST, taskId.toString());
  }

  public List<ContactsVO>  getContactsListNoPage(ContactsParam param) {
    Example example = new Example(Contacts.class);
    example.and().andEqualTo("orgId", param.getOrgId());
    //过滤掉已删除的
    example.and().andNotEqualTo("status", -1);

    if (!CollectionUtils.isEmpty(param.getNames())) {
      if (param.getNames().size() == 1) {
        example.and().andLike("name", "%" + param.getNames().get(0) + "%");
      }else {
        example.and().andIn("name", param.getNames());
      }
    }
    if (!CollectionUtils.isEmpty(param.getMobiles())) {
      if (param.getMobiles().size() == 1) {
        example.and().andLike("mobile", "%" + param.getMobiles().get(0) + "%");
      } else {
        example.and().andIn("mobile", param.getMobiles());
      }
    }
    if (StringUtils.isNotBlank(param.getRelationType())) {
      example.and().andLike("relationType", "%" + param.getRelationType() + "%");
    }
    //查询条件中的状态
    if (param.getStatus() != null) {
      example.and().andEqualTo("status", param.getStatus());
    }
    example.orderBy("updateTime").desc();
    List<Contacts> contactsList = contactsMapper.selectByExample(example);
    List<ContactsVO> contactsVOList = contactsList.stream().map(contacts -> toContactsVO(contacts)).collect(Collectors.toList());
    return contactsVOList;
  }

  @Transactional(rollbackFor = Exception.class)
  public void updateContactsStatusBatch(ContactsParam param) {
    //非全选状态下，联系人编号不能为空
    if (!param.getAllSelect()) {
      if (CollectionUtils.isEmpty(param.getIds())) {
        throw new ApiException("联系人编号不能为空");
      }
    }
    if (param.getNewStatus() == null) {
      throw new ApiException("联系人状态不能为空");
    }
    UserSession tokenUser = UserUtils.getTokenUser();
    if (param.getAllSelect()) {
      //提交异步
      asyncContactsBatch(param);
      return;
    }
    Example example = new Example(Contacts.class);
    //设置org_id
    example.and().andEqualTo("orgId", tokenUser.getOrgId());
    //所有要失效的ids
    if (!CollectionUtils.isEmpty(param.getIds())) {
      example.and().andIn("id", param.getIds());
    }
    Contacts contacts = new Contacts();
    //设置更新时间
    contacts.setUpdateTime(new Date());
    //设置更新状态
    contacts.setStatus(param.getNewStatus());
    int count = contactsMapper.updateByExampleSelective(contacts, example);
    if (count < 1) {
      throw new ApiException("根据条件失效联系人数据0条");
    }
    threadExecutor.submit(() -> {
      contactsOperationRecordService.addRecord(tokenUser.getOrgId(), tokenUser.getId(), count, ContactsConstant.ACTION_STATUS);
    });

  }

  public void update(ContactsOperate contactsOperate) {
    UserSession userSession = UserUtils.getTokenUser();

    Contacts contacts = AuthBeanUtils.copy(contactsOperate, Contacts.class);
    contacts.setUpdateTime(new Date());
    int update = contactsMapper.updateByPrimaryKeySelective(contacts);
    if (update < 1) {
      throw new ApiException("更新失败");
    }
    threadExecutor.submit(()->{
      contactsOperationRecordService.addRecord(userSession.getOrgId(), userSession.getId(), update, ContactsConstant.ACTION_UPDATE);

    });
  }


  ContactsVO toContactsVO(Contacts contacts) {
    if (contacts == null) {
      return null;
    }
    ContactsVO contactsVO = AuthBeanUtils.copy(contacts, ContactsVO.class);
    return contactsVO;
  }

  /**
   * 指定联系人发送单条短信
   * @param param
   */
  public String sendSingleMessage(SingleMessageParam param) {
    UserSession tokenUser = UserUtils.getTokenUser();
    // 查看催员是否存在发送短信功能
    User user = userService.selectByPrimaryKey(tokenUser.getId());
    if (user.getIsCanMsg() != 1) {
      throw new ApiException("当前催员不能发送短信！");
    }
    // 获取案件信息
    if (param.getCaseId()== null) {
      throw new ApiException("caseId不能为空！");
    }
    if (param.getContactId() == null) {
      throw new ApiException("contactId不能为空！");
    }
    Contacts contacts = contactsMapper.selectByPrimaryKey(param.getContactId());
    if (contacts == null) {
      throw new ApiException("不存在该联系人！");
    }
    Map<String, Object> map = new HashMap<>();
    map.put("id", param.getCaseId());
    List<CaseQueryResult> caseQueryResults = caseService.queryResult(map);
    if (CollectionUtils.isEmpty(caseQueryResults)) {
      throw new ApiException("该案件不存在！");
    }
    SmsTemplate smsTemplate = smsTemplateService.selectByPrimaryKey(param.getSmsTplId());
    if (!PatternUtils.isPlanMessage(contacts.getMobile())) {
      throw new ApiException("联系人手机号不正确！");
    }
    Map<String, String> extParam = smsSendService.getExtParam(smsTemplate.getVarJson(), caseQueryResults.get(0), param.getContact());
    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(extParam));
    UUID uuid = UUID.randomUUID();
    // 发送短信
    Map<String, Object> resultMap = messageManager.sendSingleMessage(jsonObject, smsTemplate, tokenUser.getOrgId(), contacts.getMobile(), uuid.toString());
    insertSmsSend(caseQueryResults.get(0), smsTemplate, tokenUser, resultMap, contacts, uuid.toString());
    return (String)resultMap.get("reason");
  }

  public String getSinglePreview(SingleMessageParam param) {
    Long caseId = param.getCaseId();
    if (caseId == null) {
      throw new ApiException("caseId不能为空！");
    }
    Map<String, Object> map = new HashMap<>();
    map.put("id", param.getCaseId());
    List<CaseQueryResult> caseQueryResults = caseService.queryResult(map);
    if (CollectionUtils.isEmpty(caseQueryResults)) {
      throw new ApiException("该案件不存在！");
    }
    Contacts contacts = contactsMapper.selectByPrimaryKey(param.getContactId());
    if (contacts == null) {
      throw new ApiException("不存在该联系人！");
    }
    // 获取信息模板变量
    SmsTemplate smsTemplate = smsTemplateService.selectByPrimaryKey(param.getSmsTplId());
    // 填充参数
    Map<String, String> extParam = smsSendService.getExtParam(smsTemplate.getVarJson(), caseQueryResults.get(0), param.getContact());
    String content = smsTemplate.getContent();
    Iterator<String> iterator = extParam.keySet().iterator();
    while (iterator.hasNext()) {
      String next = iterator.next();
      if (content.contains(next)) {
        content = content.replace("#"+next+"#", extParam.get(next));
      }
     }
    return content;
  }

  private void insertSmsSend(CaseQueryResult caseQueryResult, SmsTemplate smsTemplate,
                             UserSession session, Map<String, Object> resultMap, Contacts contacts, String uuid) {
    SmsSend smsSend = new SmsSend();
    smsSend.setOrgId(session.getOrgId());
    smsSend.setDepId(caseQueryResult.getDepId());
    smsSend.setTeamId(caseQueryResult.getTeamId());
    smsSend.setCaseId(caseQueryResult.getId());
    //smsSend.setPlanId(casePlan.getId());
    smsSend.setUuid(uuid);
    smsSend.setOrgDeltId(caseQueryResult.getOrgDeltId());
    //smsSend.setPlanName(casePlan.getName());
    smsSend.setContactName(contacts.getName());
    smsSend.setRelation(contacts.getRelationType());
    // 号码类型
    smsSend.setContactTypeId(contacts.getContactTypeId());
    smsSend.setRelationMobile(contacts.getMobile());
    smsSend.setOutSerialTemp(caseQueryResult.getOutSerialTemp());
    smsSend.setProductId(caseQueryResult.getProductId());
    smsSend.setOutBatchId(caseQueryResult.getOutBatchId());
    if ((boolean)resultMap.get("result")) {
      // -1:初始状态 发送中 接收中 0:失败 1:成功 2：未知
      smsSend.setSendStatus(1);
    } else {
      smsSend.setSendStatus(0);
      smsSend.setReason((String) resultMap.get("reason"));
    }
    smsSend.setReceiveStatus(2);
    smsSend.setContent((String) resultMap.get("content"));
    smsSend.setSendTime(new Date());
    smsSend.setCreateTime(new Date());
    smsSend.setUpdateTime(new Date());
    smsSend.setCreateBy(session.getId());
    smsSend.setUpdateBy(session.getId());
    smsSend.setStatus(0);
    smsSend.setSmsTplId(smsTemplate.getId());
    smsSend.setFee(new BigDecimal("0"));
    smsSend.setCount(0);
    smsSendService.insert(smsSend);
  }

  /**
   * 更新联系人状态为无效（供open接口使用）
   *
   * @param caseList       案件列表信息
   * @param caseContactMap 联系人列表
   * @param asyncTask      异步任务
   */
  @Transactional(rollbackFor = Exception.class)
  public void contactsInvalid(List<Case> caseList, Map<Long, List<OpenCaseContactDTO>> caseContactMap,AsyncTask asyncTask) {
    List<Long> caseIds = caseList.stream().map(Case::getId).collect(Collectors.toList());
    // 判断类型type
    Map<String, String> fieldJson = asyncTask.getFieldJson();
    Integer type = Integer.valueOf(fieldJson.get("type"));
    String reason  = fieldJson.get("reason");
    List<CaseContact> caseContacts = contactsMapper.selectContactByCaseIds(caseIds);
    List<CaseContact> dealContact = new ArrayList<>();
    if (Objects.equals(type, ContactEnums.Type.SELF.getCode())) {
      dealContact = caseContacts.stream().filter(c -> Objects.equals(c.getOwnSign(), 1)).collect(Collectors.toList());
    } else if (Objects.equals(type, ContactEnums.Type.NOTSELF.getCode())) {
      dealContact = caseContacts.stream().filter(c -> !Objects.equals(c.getOwnSign(), 1)).collect(Collectors.toList());
    } else if (Objects.equals(type, ContactEnums.Type.APPOINT.getCode())) {
      dealContact = caseContacts.stream().filter(c -> caseContactMap.containsKey(c.getCaseId()))
              .filter(c -> caseContactMap.get(c.getCaseId()).stream()
                      .anyMatch(o -> StringUtils.equals(o.getMobile(), c.getContactMobile())))
              .collect(Collectors.toList());
    } else {
      log.info("不存在该类型的type");
      asyncTaskService.updateTaskFinish(asyncTask, "不存在该类型的type");
      return;
    }
    if (!org.springframework.util.CollectionUtils.isEmpty(dealContact)) {
      // 更新联系人状态
      List<Long> contactIds = dealContact.stream().map(CaseContact::getContactId).collect(Collectors.toList());
      contactsMapper.updateBatchInvalid(contactIds, reason);
    }
  }

  /**
   * 根据手机号、案件查询联系人信息
   *
   * @param caseIds 案件id列表
   * @param mobiles 手机号列表
   * @return
   */
  public List<CaseContact> selectContactByCaseAndMobiles(List<Long> caseIds, List<String> mobiles){
    if (CollectionUtils.isEmpty(caseIds)) {
      return Collections.emptyList();
    }
    if (CollectionUtils.isEmpty(mobiles)) {
      return Collections.emptyList();
    }
    if (encryptProperties.getEnable()) {
      mobiles = mobiles.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList());
    }
    return contactsMapper.selectContactByCaseAndMobiles(mobiles,caseIds);
  }


  public void importAmcContacts(MultipartFile file, Long orgDeltId) throws IOException {
    UserSession userSession = UserUtils.getTokenUser();
    checkContactsFile(file, userSession.getLanguage());
    String fileName = file.getOriginalFilename();
    FileStore store = new FileStore();
    store.setTemplateType(CaseTemplateEnums.TempType.CONTACTS_IMPORT.getCode());
    store.setOrgDeltId(orgDeltId);
    store.setProductId(0L);
    store.setType(FileStoreEnums.Type.EXCEL.getCode());
    store.setTemplateId(0L);
    store.setOrgId(userSession.getOrgId());
    store.setCreateTime(new Date());
    store.setUpdateTime(new Date());
    store.setCreateBy(userSession.getId());
    store.setName(fileName);
    fileStoreService.insertSelective(store);
    Long storeId = store.getId();
    fileName = org.apache.commons.lang3.StringUtils.substringBeforeLast(fileName, ".") + "_" + storeId + "." + org.apache.commons.lang3.StringUtils.substringAfterLast(fileName, ".");
    File tempFile = new File(CommonConstant.tempFilePath + fileName);
    file.transferTo(tempFile);
    threadExecutor.execute(()-> {
      log.info("=======甲方联系人导入开始：=======storeId:{}", storeId);
      List<ContactsExcel> contactsExcels = EasyExcelUtils.read(tempFile, ContactsExcel.class, 2);
      int successCount = 0;
      int total = contactsExcels.size();
      int page = total/500 + ((total%500) == 0 ? 0 :1);
      log.info("=======甲方联系人导入统计：=======storeId:{}, total:{}, page:{}", storeId, total, page);
      Map<String, Long> contactTypeMap = contactTypeConfigService.selectContactTypeMapByOrgId(userSession.getOrgId());
      for (int i =0; i < page; i++) {
        int start = i * 500;
        int end = (i + 1) * 500 >= total ? total : (i + 1) * 500;
        List<ContactsExcel> subList = contactsExcels.subList(start, end);
        List<Contacts> contactsList = handleContactExcelData(storeId, subList, orgDeltId, userSession, contactTypeMap);
        if (!CollectionUtils.isEmpty(contactsList)) {
          successCount = successCount + contactsList.size();
          this.batchUpdateContact(contactsList);
        }
        log.info("=======甲方联系人导入执行进度：=======storeId:{}，执行到第page:{}页", storeId, i);
      }
      fileStoreService.handleFileStore(storeId, successCount, total, ContactsConstant.AMC_CONTACTS_IMPORT_HEAD_LIST);
      log.info("=======甲方联系人导入结束：=======storeId:{}", storeId);
    });

  }

  public TaskSubmitVO openImportContacts(Long orgId, Long orgDeltId, List<ContactParam> paramList) {
    FileStore store = new FileStore();
    store.setOrgDeltId(orgDeltId);
    store.setProductId(0L);
    store.setTemplateType(CaseTemplateEnums.TempType.CONTACTS_IMPORT.getCode());
    store.setType(FileStoreEnums.Type.OPEN_API.getCode());
    store.setTemplateId(0L);
    store.setOrgId(orgId);
    store.setCreateTime(new Date());
    store.setUpdateTime(new Date());
    store.setCreateBy(0l);
    store.setUpdateBy(0l);
    store.setTotal(paramList.size());
    store.setTotalAmount((long)paramList.size());
    fileStoreService.insertSelective(store);
    Long storeId = store.getId();
    TaskSubmitVO taskSubmitVO = new TaskSubmitVO();
    taskSubmitVO.setTask_id(storeId);
    taskSubmitVO.setTotal_count(paramList.size());
    threadExecutor.execute(()->{
      List<String> outSerialNos = paramList.parallelStream()
              .filter(param -> StringUtils.isNotBlank(param.getOut_serial_no()))
              .map(param -> param.getOut_serial_no() + "#" + orgDeltId)
              .distinct()
              .collect(Collectors.toList());
      List<Case> caseList = caseService.selectCaseByOutSerialNos(outSerialNos);
      Map<String, Long> contactTypeMap = contactTypeConfigService.selectContactTypeMapByOrgId(orgId);
      List<String> errorList = new ArrayList<>();
      List<Contacts> contactsList = new ArrayList<>();
      paramList.forEach(param -> {
        Case caseInfo = caseList.parallelStream()
                .filter(ca -> Objects.equals(ca.getOutSerialTemp(), param.getOut_serial_no()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(caseInfo)) {
          String errorData = JSONObject.toJSONString(param);
          errorList.add(errorData);
          return;
        }
        Contacts con = new Contacts();
        con.setOrgId(orgId);
        con.setCreateTime(new Date());
        con.setUpdateTime(new Date());
        con.setCreateBy(0L);
        con.setUpdateBy(0L);
        con.setStatus(0);
        con.setRelId(caseInfo.getDebtId() == null ? caseInfo.getId() : caseInfo.getDebtId());
        con.setIsConjoint(caseInfo.getDebtId() == null ? 0 : 1);
        con.setMobile(param.getMobile());
        con.setName(param.getName());
        con.setRelationType(param.getRelation());
        if (Objects.equals(caseInfo.getOwnMobile(), param.getMobile())) {
          con.setOwnSign(1);
        } else {
          //号码类型为本人，联系人标志都是1，其余为0
          con.setOwnSign(0);
          // 号码类型
          String contactTypeName = param.getContact_type_name();
          if (contactTypeMap.containsKey(contactTypeName)) {
            con.setContactTypeId(contactTypeMap.get(contactTypeName));
            if (ContactTypeConfigEnums.IsSys.isSelf(contactTypeName)) {
              con.setOwnSign(1);
            }
          }
        }
        contactsList.add(con);
      });

      if (!CollectionUtils.isEmpty(contactsList)) {
        this.batchUpdateContact(contactsList);
      }

      FileStore updateStore = new FileStore();
      updateStore.setId(storeId);
      if (contactsList.size() > 0) {
        updateStore.setStatus(FileStoreEnums.Status.SUCCESS.getCode());
      } else {
        updateStore.setStatus(FileStoreEnums.Status.FAIL.getCode());
      }
      updateStore.setIsCreating(1);
      updateStore.setSuccessAmt(contactsList.size());
      updateStore.setUpdateTime(new Date());
      fileStoreService.updateByPrimaryKeySelective(updateStore);
      if (!CollectionUtils.isEmpty(errorList)) {
        String errorDataKey = KeyCache.FILE_STORE_COLUMN_ERROR_LIST + storeId;
        stringRedisTemplate.opsForList().rightPushAll(errorDataKey, errorList);
        stringRedisTemplate.expire(errorDataKey, 3, TimeUnit.HOURS);
      }
    });
    return taskSubmitVO;
  }

  List<Contacts> handleContactExcelData(Long storeId, List<ContactsExcel> contactsExcels, Long orgDeltId,
                                        UserSession userSession, Map<String, Long> contactTypeMap) {
    List<String> outSerialNos = contactsExcels.parallelStream()
            .filter(contactsExcel -> StringUtils.isNotBlank(contactsExcel.getOutSerialNo()))
            .map(contactsExcel -> contactsExcel.getOutSerialNo() + "#" + orgDeltId)
            .distinct()
            .collect(Collectors.toList());
    List<Case> caseList = caseService.selectCaseByOutSerialNos(outSerialNos);
    List<Contacts> contactsList = new ArrayList<>();
    contactsExcels.forEach(contactsExcel -> {
      Contacts con = new Contacts();
      con.setOrgId(userSession.getOrgId());
      con.setCreateTime(new Date());
      con.setUpdateTime(new Date());
      con.setCreateBy(userSession.getId());
      con.setUpdateBy(userSession.getId());
      con.setStatus(0);
      List<Map<String, Object>> errorList = new ArrayList<>();
      String outSerialNo = contactsExcel.getOutSerialNo();
      Case caseInfo = null;
      // 校验案件编号
      if (StringUtils.isBlank(outSerialNo)) {
        errorList.add(fileStoreService.buildColumnError(0, FileStoreEnums.ColumnErrorCode.NOT_BLANK));
      } else {
        caseInfo = caseList.parallelStream()
                .filter(aCase -> Objects.equals(aCase.getOutSerialNo(), outSerialNo + "#" + orgDeltId))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(caseInfo)) {
          errorList.add(fileStoreService.buildColumnError(0, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
        } else {
          con.setRelId(caseInfo.getDebtId() == null ? caseInfo.getId() : caseInfo.getDebtId());
          con.setIsConjoint(caseInfo.getDebtId() == null ? 0 : 1);
        }
      }
      // 校验手机号
      String mobile = contactsExcel.getMobile();
      if (StringUtils.isBlank(mobile)) {
        errorList.add(fileStoreService.buildColumnError(1, FileStoreEnums.ColumnErrorCode.NOT_BLANK));
      } else {
        mobile = StringUtils.trimAnyBlank(mobile);
        boolean isMatch = RegexConstant.isMatch(RegexConstant.REGEX_PHONE, mobile);
        if (!isMatch) {
          errorList.add(fileStoreService.buildColumnError(1, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        } else {
          con.setMobile(mobile);
        }
      }

      // 姓名
      String name = contactsExcel.getName();
      if (StringUtils.isBlank(name)) {
        con.setName("");
      } else {
        // 不为空并且长度要小于100
        if (name.trim().length() > 100) {
          errorList.add(fileStoreService.buildColumnError(2, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        } else {
          con.setName(name.trim());
        }
      }
      // 关系
      String relationType = contactsExcel.getRelationType();
      if (StringUtils.isBlank(relationType)) {
        con.setRelationType("");
      } else {
        if (relationType.trim().length() > 100) {
          errorList.add(fileStoreService.buildColumnError(3, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        } else {
          con.setRelationType(relationType.trim());
        }
      }
      if (Objects.nonNull(caseInfo) && Objects.equals(caseInfo.getOwnMobile(), mobile)) {
        con.setOwnSign(1);
      } else {
        //号码类型为本人，联系人标志都是1，其余为0
        con.setOwnSign(0);
        // 号码类型
        String contactTypeName = contactsExcel.getContactType();
        if (contactTypeMap.containsKey(contactTypeName)) {
          con.setContactTypeId(contactTypeMap.get(contactTypeName));
          if (ContactTypeConfigEnums.IsSys.isSelf(contactTypeName)) {
            con.setOwnSign(1);
          }
        }
      }
      if (!CollectionUtils.isEmpty(errorList)) {
        // 错误信息保存到redis
        List<String> fieldValues = Lists.newArrayList(contactsExcel.getOutSerialNo(), contactsExcel.getMobile(),
                contactsExcel.getName(),contactsExcel.getRelationType(), contactsExcel.getContactType());
        JSONArray valueJson = JSONArray.parseArray(JSON.toJSONString(fieldValues));
        valueJson.add(errorList);
        redisUtil.lSet(KeyCache.FILE_ERROR_LIST + storeId, JSONArray.toJSONString(valueJson), 24*60*60);
        return;
      }
      contactsList.add(con);
    });
    return contactsList;
  }


  void checkContactsFile(MultipartFile file, String language) {
    String fileName = file.getOriginalFilename();
    // 检验文件格式
    if (!ExcelUtil.isExcelFile(fileName)) {
      throw new ApiException("文件必须是excel格式");
    }
    long size = file.getSize();
    // 检验文件大小
    if (size > 100 * 1024 * 1024) {
      throw new ApiException("文件大小已超过100M，请拆分文件后再上传");
    }
    // 检验文件数据是否为空
    if (EasyExcelUtils.judgeIsEmpty(file, CaseTemplateEnums.TempType.CONTACTS_IMPORT.getCode(),
            ContactsConstant.AMC_CONTACTS_IMPORT_HEAD_LIST, language)) {
      throw new ApiException("文件为空模板");
    }
  }





}
