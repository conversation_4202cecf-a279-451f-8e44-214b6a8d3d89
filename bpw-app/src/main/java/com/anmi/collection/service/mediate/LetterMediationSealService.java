package com.anmi.collection.service.mediate;

import cn.duyan.dto.eqianbao.OrgOwnSealDto;
import cn.duyan.dto.eqianbao.SealAuthorizedInfoDto;
import cn.duyan.dto.eqianbao.SealExternalAuthDto;
import cn.duyan.signature.provider.EqianbaoSignatureProvider;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.DepTeamEnums;
import com.anmi.collection.common.enums.RoleEnums;
import com.anmi.collection.dto.fileStorage.UploadFileInfo;
import com.anmi.collection.entity.requset.letter.LetterMediationSealParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.letter.LetterMediationSealVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.CompanyMapper;
import com.anmi.collection.mapper.LetterMediationSealMapper;
import com.anmi.collection.service.DepTeamService;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.utils.AuthBeanUtils;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.letter.LetterMediationSeal;
import com.anmi.domain.mediate.AuthOrgInfo;
import com.anmi.domain.user.DepTeam;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LetterMediationSealService extends BaseService<LetterMediationSeal> {
    @Resource
    private LetterMediationSealMapper letterMediationSealMapper;
    @Resource
    private EqianbaoSignatureProvider provider;
    @Resource
    private CompanyMapper companyMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private AuthOrgInfoService authOrgInfoService;
    @Resource
    private DepTeamService depTeamService;
    @Resource
    private FileStorageStrategyFactory fileStorageStrategyFactory;

    private static final String SEAL_AUTH_PREFIX = "sealAuth-";

    /**
     * 添加授权委托方
     * 若未存在直接插入，若存在的委托方进行更新
     *
     * @param param 委托信息
     * @return 添加信息
     */
    public String addOrgSeals(LetterMediationSealParam param) throws Exception {
        if (StringUtils.isBlank(param.getSealIds())) {
            throw new ApiException("印章编号不能为空！");
        }
        // 委托机构名称
        if (StringUtils.isBlank(param.getDepName())) {
            throw new ApiException("授权机构名称不能为空！");
        }
        // 委托机构id
        if (ObjectUtil.isNull(param.getDepId())) {
            throw new ApiException("授权机构id不能为空！");
        }
        List<String> sealList = Arrays.asList(param.getSealIds().split(","));
        // 查询e签宝中是否存在改对应的签章信息
        List<SealAuthorizedInfoDto> orgAuthorizedSeals = new ArrayList<>();
        try {
            orgAuthorizedSeals = provider.getOrgAuthorizedSeals(systemConfig.getEqianbaoOrgId());
        } catch (Exception e) {
            log.error("查询授权印章信息出现异常,{}", e.getMessage());
            throw new ApiException(e.getMessage());
        }
        List<SealAuthorizedInfoDto> sealDtos = orgAuthorizedSeals.stream().filter(item -> sealList.contains(item.getSealId())).collect(Collectors.toList());
        List<SealAuthorizedInfoDto> uniqueSealDtos = new ArrayList<>(sealDtos.stream()
                .collect(Collectors.toMap(SealAuthorizedInfoDto::getSealId, Function.identity(), (dto1, dto2) -> dto1))
                .values());
        if (CollectionUtils.isEmpty(uniqueSealDtos)) {
            return param.getDepName();
        }
        // 保存存在的签章信息，若存在签章信息不存在，则将不存在的授权公司信息返回给前端
        this.insertMediationSeal(uniqueSealDtos, param.getDepId());
        if (sealList.size() != uniqueSealDtos.size()) {
            return param.getDepName();
        }
        return null;
    }

    /**
     * @param sealDtos  e签宝存在的印章信息
     * @param depId     分公司（委外公司）id
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertMediationSeal(List<SealAuthorizedInfoDto> sealDtos, Long depId) throws Exception {
        UserSession userSession = UserUtils.getTokenUser();
        List<LetterMediationSeal> seals = covertSeal(sealDtos, userSession, depId);
        List<String> allSealIds = seals.stream().map(LetterMediationSeal::getSealId).collect(Collectors.toList());
        // 查询是否已存在该签章信息
        Example example = new Example(LetterMediationSeal.class);
        example.and().andIn("sealId", allSealIds);
        example.and().andEqualTo("depId", depId);
        example.and().andEqualTo("orgId", userSession.getOrgId());
        List<LetterMediationSeal> existSeals = selectByExample(example);
        List<String> existSealIds = existSeals.stream().map(LetterMediationSeal::getSealId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(existSealIds)) {
            example.clear();
            example.and().andEqualTo("depId", depId);
            example.and().andEqualTo("orgId", userSession.getOrgId());
            example.and().andIn("sealId", existSealIds);
            deleteByExample(example);
        }
        // 上传印章文件至oss
        Date expireDate = DateUtils.addDays(new Date(), 10 * 365);
        for (LetterMediationSeal seal : seals) {
            String uploadUrl = upload(seal.getImage(), seal.getSealId(), expireDate);
            if (StringUtils.isBlank(uploadUrl)) {
                throw new ApiException("文件上传失败");
            }
            log.info("印章文件已上传，文件名为：{} 地址为：{}", seal.getSealId(), uploadUrl);
            seal.setImage(uploadUrl);
            seal.setAuthStatus(1);
        }
        letterMediationSealMapper.insertList(seals);
    }

    /**
     * 转换印章格式
     * 
     * @param sealDtos     印章信息
     * @param userSession  用户信息
     * @param depId        分公司（委外公司）id
     * @return 印章列表
     */
    private List<LetterMediationSeal> covertSeal(List<SealAuthorizedInfoDto> sealDtos, UserSession userSession, Long depId) {
        List<LetterMediationSeal> seals = new ArrayList<>();
        for (SealAuthorizedInfoDto dto : sealDtos) {
            LetterMediationSeal seal = new LetterMediationSeal();
            seal.setOrgId(userSession.getOrgId());
            seal.setDepId(depId);
            seal.setType(0);
            seal.setSealId(dto.getSealId());
            seal.setAlias(dto.getSealName());
            // 授权主体名称
            seal.setName(dto.getAuthorizerOrgName());
            seal.setSealBizType(dto.getSealBizType());
            seal.setEffectiveTime(DateUtils.parseDateFull(DateUtils.convertDate(dto.getEffectiveTime().toString())));
            seal.setExpireTime(DateUtils.parseDateFull(DateUtils.convertDate(dto.getExpireTime().toString())));
            seal.setIsShow(1);
            seal.setImage(dto.getSealImageDownLoadUrl());
            seal.setAuthorizerOrgId(dto.getAuthorizerOrgId());
            seal.setSealAuthBizId(dto.getSealAuthBizId());
            seal.setCreateTime(new Date());
            seal.setUpdateTime(new Date());
            seals.add(seal);
        }
        return seals;
    }

    /**
     * 分页显示机构印章信息
     *
     * @param param 印章查询条件
     * @return 机构印章
     */
    public PageOutput<LetterMediationSealVO> getShowOrgSeals(LetterMediationSealParam param) {
        // 显示之前更新所有权限信息
        UserSession tokenUser = UserUtils.getTokenUser();
        Example example = new Example(LetterMediationSeal.class);
        Example.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(param.getDepIds())) {
            criteria.andIn("depId", param.getDepIds());
        }
        criteria.andEqualTo("orgId", tokenUser.getOrgId());
        criteria.andEqualTo("isShow", 1);
        example.orderBy("id").desc();
        PageParam pageParam = new PageParam(param.getPage(), param.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<LetterMediationSeal> sealList = letterMediationSealMapper.selectByExample(example);
        String orgName = companyMapper.selectByPrimaryKey(tokenUser.getOrgId()).getName();
        List<LetterMediationSealVO> vos = new ArrayList<>();
        if (CollectionUtils.isEmpty(sealList)) {
            return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int) page.getTotal(), vos);
        }
        List<Long> orgDepSeals = sealList.stream().map(LetterMediationSeal::getDepId).collect(Collectors.toList());
        Map<Long, String> depMap = queryDepTeamMap(orgDepSeals, tokenUser.getOrgId());
        sealList.forEach(seal -> {
            LetterMediationSealVO vo = AuthBeanUtils.copy(seal, LetterMediationSealVO.class);
            vo.setOrgName(depMap.get(vo.getDepId()));
            if (StringUtils.isBlank(vo.getOrgName())) {
                vo.setOrgName(orgName);
            }
            vos.add(vo);
        });
        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int) page.getTotal(), vos);
    }

    /**
     * 获取对应机构类型的机构信息
     *
     * @param depIds 分公司（委外公司）id
     * @return 分公司（委外公司）
     */
    private Map<Long, String> queryDepTeamMap(List<Long> depIds, Long orgId) {
        if (CollectionUtils.isEmpty(depIds)) {
            return Collections.emptyMap();
        }
        Example example = new Example(DepTeam.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", depIds)
                .andEqualTo("orgId", orgId)
                .andEqualTo("type", DepTeamEnums.type.BRANCH_OFFICE.getCode())
                .andEqualTo("status", DepTeamEnums.Status.NORMAL.getCode());
        List<DepTeam> depTeams = depTeamService.selectByExample(example);
        return depTeams.stream().collect(Collectors.toMap(DepTeam::getId, DepTeam::getName));
    }

    private String upload(String url, String sealId, Date expireDate) throws Exception {
        InputStream inputStream = null;
        FileOutputStream os = null;
        try {
            URL urls = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) urls.openConnection();
            connection.setConnectTimeout(10 * 1000);
            connection.setReadTimeout(15 * 1000);
            inputStream = connection.getInputStream();
            File newFile = new File(sealId + ".png");
            os = new FileOutputStream(newFile);
            byte[] buffer = new byte[81920];
            int bytesRead = 0;
            while ((bytesRead = inputStream.read(buffer, 0, 81920)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            MultipartFile multipartFile = getMultipartFile(newFile);
            String newName = sealId + ".png";

            UploadFileInfo uploadFileInfo = new UploadFileInfo();
            uploadFileInfo.setFile(multipartFile)
                    .setFileName(newName)
                    .setExpireDate(expireDate)
                    .setBucket(systemConfig.getCaseFilesBucket())
                    .setLocalUrl(systemConfig.getMediationFilePath() + newName);
            FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
            String urlss = fileStorageStrategy.uploadFile(uploadFileInfo);
            newFile.delete();
            return urlss;
        } catch (Exception e) {
            log.error("根据Url获取图片的file上传,发生异常error,{}", e.getMessage());
            return null;
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
            if (os != null) {
                os.flush();
                os.close();
            }

        }
    }

    private MultipartFile getMultipartFile(File file) throws IOException {
        FileInputStream fileInputStream = null;
        MultipartFile multipartFile = null;
        try {
            fileInputStream = new FileInputStream(file);
            multipartFile = new MockMultipartFile(file.getName(), file.getName(),
                    ContentType.IMAGE_PNG.toString(), fileInputStream);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fileInputStream != null) {
                fileInputStream.close();
            }
        }
        return multipartFile;
    }

    /**
     * 获取印章授权地址
     *
     * @param sealId 印章id
     * @return 授权地址
     */
    public String getAuthSealUrl(String sealId) {
        UserSession userSession = UserUtils.getTokenUser();
        Long orgId = userSession.getOrgId();
        List<AuthOrgInfo> authOrgInfos = checkRoleAuth(userSession);
        // 查询是否e签宝存在该印章信息
        OrgOwnSealDto orgSealInfo = null;
        try {
            orgSealInfo = provider.getOrgSealInfo(authOrgInfos.get(0).getOrganizationId(), sealId);
        } catch (Exception e) {
            log.error("查询授权印章时出现异常,{}", e.getMessage());
            throw new ApiException(e.getMessage());
        }
        Example sealExample = new Example(LetterMediationSeal.class);
        sealExample.and().andEqualTo("sealId", sealId);
        sealExample.and().andEqualTo("orgId", orgId);
        sealExample.and().andIsNull("depId");
        // sealStatus 0:删除 2:待审核 3：审核不通过 4：挂起
        if (orgSealInfo == null || Lists.newArrayList(0, 2, 3, 4).contains(orgSealInfo.getSealStatus())) {
            // 删除本地库中对应印章
            letterMediationSealMapper.deleteByExample(sealExample);
            throw new ApiException("该印章在e签宝已删除，无法继续授权，弹窗关闭后，印章将同步删除，请知晓");
        }
        // 排除掉没有申请授权的印章
        sealExample.and().andIsNotNull("sealAuthBizId");
        List<LetterMediationSeal> seals = letterMediationSealMapper.selectByExample(sealExample);
        // 查询e签宝签署状态
        if (!CollectionUtils.isEmpty(seals)) {
            boolean flag = querySealAuth(authOrgInfos, seals);
            if (flag) {
                throw new ApiException("当前印章授权状态发生改变，请重试");
            }
            // 查询是否已获取印章授权链接
            String sealAuthBizId = stringRedisTemplate.opsForValue().get(SEAL_AUTH_PREFIX + sealId);
            if (stringRedisTemplate.hasKey(SEAL_AUTH_PREFIX + sealId)
                    && StringUtils.isNotBlank(sealAuthBizId)
                    && sealAuthBizId.equals(seals.get(0).getSealAuthBizId())
            ) {
                return seals.get(0).getSealAuthUrl();
            }
        }
        SealExternalAuthDto sealExternalAuthDto = fetchSealAuthUrl(authOrgInfos, sealId, userSession.getOrgId());
        // 设置印章授权存储
        stringRedisTemplate.opsForValue().set(SEAL_AUTH_PREFIX + sealId, sealExternalAuthDto.getSealAuthBizId(), 1, TimeUnit.DAYS);
        return sealExternalAuthDto.getAuthorizationSignUrl();
    }

    private List<AuthOrgInfo> checkRoleAuth(UserSession userSession) {
        if (userSession.getRoleType() == null
                || (userSession.getRoleType() != RoleEnums.type.CUSTOM_ADMIN.getCode()
                && userSession.getRoleType() != RoleEnums.type.BRANCH_ADMIN.getCode()
                && userSession.getRoleType() != RoleEnums.type.SUB_ACCOUNT_ADMIN.getCode()
                && userSession.getRoleType() != RoleEnums.type.TEAM_LEADER.getCode())) {
            // 只有管理员才能获取印章授权链接
            throw new ApiException("非管理员不能设置印章授权！");
        }
        List<AuthOrgInfo> authOrgInfos = authOrgInfoService.queryAuthOrgInfo(userSession.getOrgId(), 1, 1);
        if (CollectionUtils.isEmpty(authOrgInfos)) {
            throw new ApiException("当前企业还未认证或未授权！");
        }
        return authOrgInfos;
    }

    private SealExternalAuthDto fetchSealAuthUrl(List<AuthOrgInfo> authOrgInfos, String sealId, Long orgId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orgId", authOrgInfos.get(0).getOrganizationId());
        jsonObject.put("sealId", sealId);
        jsonObject.put("transactorPsnId", authOrgInfos.get(0).getPsnId());
        // 委托机构orgId 授权印章id 授权操作人id
        JSONObject authorizedOrgInfo = new JSONObject();
        authorizedOrgInfo.put("orgName", systemConfig.getEqianbaoOrgName());
        authorizedOrgInfo.put("orgIDCardNum", systemConfig.getEqianbaoOrgIDCardNum());
        // 生效时间 过期时间
        Date effectiveTime = new Date();
        Date expireTime = DateUtils.addYears(effectiveTime, 3);
        jsonObject.put("effectiveTime", effectiveTime.getTime());
        jsonObject.put("expireTime", expireTime.getTime());
        jsonObject.put("authorizedOrgInfo", authorizedOrgInfo);
        log.info("获取企业授权印章链接参数{}", jsonObject.toJSONString());
        SealExternalAuthDto sealExternalAuthDto = null;
        try {
            sealExternalAuthDto = provider.getOrgSealExternalAuthUrl(jsonObject.toJSONString());
        } catch (Exception e) {
            log.error("获取企业授权印章链接出现异常, {}", e.getMessage());
            throw new ApiException(e.getMessage());
        }
        log.info("获取第三方企业授权印章参数" + sealExternalAuthDto);

        // 将返回数据进行存储
        Example upExample = new Example(LetterMediationSeal.class);
        upExample.and().andEqualTo("sealId", sealId);
        upExample.and().andEqualTo("orgId", orgId);
        upExample.and().andIsNull("depId");
        LetterMediationSeal seal = new LetterMediationSeal();
        seal.setSealAuthBizId(sealExternalAuthDto.getSealAuthBizId());
        seal.setSealAuthUrl(sealExternalAuthDto.getAuthorizationSignUrl());
        seal.setUpdateTime(new Date());
        letterMediationSealMapper.updateByExampleSelective(seal, upExample);
        return sealExternalAuthDto;
    }

    // 查询授权状态
    private boolean querySealAuth(List<AuthOrgInfo> authOrgInfos, List<LetterMediationSeal> seals) {
        String organizationId = authOrgInfos.get(0).getOrganizationId();
        List<SealAuthorizedInfoDto> sealAuthorizedInfoDtos = provider.getOrgSealExternalAuth(organizationId, seals.get(0).getSealId(), systemConfig.getEqianbaoOrgId());
        Map<String, SealAuthorizedInfoDto> sealMap = sealAuthorizedInfoDtos.stream().collect(Collectors.toMap(SealAuthorizedInfoDto::getSealAuthBizId, f -> f));
        boolean flag = false;
        if (sealMap.containsKey(seals.get(0).getSealAuthBizId())) {
            SealAuthorizedInfoDto sealAuthorizedInfoDto = sealMap.get(seals.get(0).getSealAuthBizId());
            log.info("印章为sealId为{}认证状态为{}", seals.get(0).getSealId(), sealAuthorizedInfoDto.getAuthorizeStatus());
            // 印章授权已生效1、失效0， 则更新印章状态
            if (sealAuthorizedInfoDto.getAuthorizeStatus() == 1) {
                Example updateExample = new Example(LetterMediationSeal.class);
                LetterMediationSeal seal = new LetterMediationSeal();
                Long effectiveTime = sealAuthorizedInfoDto.getEffectiveTime();
                Date effectiveDate = DateUtils.parseDateFull(DateUtils.convertDate(effectiveTime.toString()));
                Long expireTime = sealAuthorizedInfoDto.getExpireTime();
                Date expireDate = DateUtils.parseDateFull(DateUtils.convertDate(expireTime.toString()));
                seal.setAuthStatus(sealAuthorizedInfoDto.getAuthorizeStatus());
                seal.setEffectiveTime(effectiveDate);
                seal.setExpireTime(expireDate);
                seal.setAuthorizerOrgId(sealAuthorizedInfoDto.getAuthorizerOrgId());
                seal.setUpdateTime(new Date());
                letterMediationSealMapper.updateByExampleSelective(seal, updateExample);

                if (stringRedisTemplate.hasKey(SEAL_AUTH_PREFIX + seals.get(0).getSealId())) {
                    stringRedisTemplate.delete(SEAL_AUTH_PREFIX + seals.get(0).getSealId());
                }
                flag = true;
            }
        } else {
            // 不存在授权信息，则将本地数据库中授权信息置空,防止本地化直接在e签宝控制台删除授权书，
            // 调用印章授权信息时是获取不到该信息的。调用印章授权信息只能获取到0-失效，1- 正常，3- 待生效这三种状态。
            Example updateExample = new Example(LetterMediationSeal.class);
            LetterMediationSeal letterMediationSeal = seals.get(0);
            letterMediationSeal.setAuthStatus(-1);
            letterMediationSeal.setSealAuthUrl(null);
            letterMediationSeal.setSealAuthBizId(null);
            letterMediationSeal.setEffectiveTime(null);
            letterMediationSeal.setExpireTime(null);
            updateExample.and().andEqualTo("sealId", letterMediationSeal.getSealId());
            updateExample.and().andEqualTo("orgId", letterMediationSeal.getOrgId());
            updateExample.and().andIsNull("depId");
            letterMediationSealMapper.updateByExample(letterMediationSeal, updateExample);
            flag = true;
        }
        return flag;
    }
}
