package com.anmi.collection.service.strategy;

import com.anmi.collection.entity.requset.statistics.UserStatisticsQueryParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.statistics.*;

/**
 * <AUTHOR>
 */
public interface CaseFollowStrategy {

    /**
     * 审核消息
     * @param userSession
     * @return
     * @throws Exception
     */
    ApplyInfoVO applyInfo(UserSession userSession) throws Exception;

    /**
     * 案件跟催分布
     * @param userSession
     * @return
     */
    CaseFollowConsoleVO caseFollow(UserSession userSession);

    /**
     * 案件状态分析
     * @param userSession
     * @return
     */
    CaseStateAnalyseVO caseStateAnalyse(UserSession userSession);

    /**
     * 控制台-数据统计-今日
     * @param userSession
     * @return
     */
    CaseConsoleStatisticsVO caseConsoleStatisToday(UserSession userSession);

    /**
     * 控制台-数据统计
     * @param userSession
     * @return
     */
    CaseConsoleStatisticsVO caseConsoleStatistics(UserSession userSession,Integer type);

    /**
     * 控制台-催员还款排行榜
     * @param userSession
     * @param type
     * @return
     */
    RepaymentRankingVO repaymentRankingList(UserSession userSession, Integer type) throws Exception;

    /**
     * 控制台-还款趋势分析
     * @param userSession
     * @param type
     * @return
     */
    RepaymentAnalyseVO repaymentAnalyseList(UserSession userSession, Integer type) throws Exception;

    /**
     * 统计报表-催员还款排行榜报表
     * @param userSession
     * @param param
     * @return
     */
    RepaymentByDunnerIdVO repaymentListByDunnerIds(UserSession userSession, UserStatisticsQueryParam param) throws Exception;
}
