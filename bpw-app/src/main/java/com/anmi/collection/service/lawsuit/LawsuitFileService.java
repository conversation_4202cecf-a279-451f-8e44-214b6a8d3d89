package com.anmi.collection.service.lawsuit;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.SpaceEnums;
import com.anmi.collection.dto.LawsuitFileDTO;
import com.anmi.collection.dto.LawsuitFileTimeDTO;
import com.anmi.collection.dto.fileStorage.UploadFileInfo;
import com.anmi.collection.entity.requset.lawsuit.LawsuitFileDeleteParam;
import com.anmi.collection.entity.requset.lawsuit.LawsuitFileParam;
import com.anmi.collection.entity.requset.lawsuit.LawsuitRecordParam;
import com.anmi.collection.entity.requset.query.lawsuit.LawsuitFileQuery;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.lawsuit.LawsuitFileVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.AsyncTaskMapper;
import com.anmi.collection.mapper.DownloadTaskMapper;
import com.anmi.collection.mapper.lawsuit.LawsuitFileMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.DeltService;
import com.anmi.collection.service.OrgSpaceService;
import com.anmi.collection.service.UserService;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.OSSClientUtil;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.OrgSpace;
import com.anmi.domain.lawsuit.Lawsuit;
import com.anmi.domain.lawsuit.LawsuitFile;
import com.anmi.domain.sys.DownloadTask;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LawsuitFileService extends BaseService<LawsuitFile> {
    @Resource
    private LawsuitService lawsuitService;
    @Resource
    private LawsuitFileMapper lawsuitFileMapper;
    @Resource
    private LawsuitConfigService lawsuitConfigService;
    @Resource
    private UserService userService;
    @Resource
    private OrgSpaceService orgSpaceService;
    @Resource
    private AsyncTaskMapper asyncTaskMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private DownloadTaskMapper downloadTaskMapper;
    @Resource
    private DeltService deltService;
    @Resource
    private FileStorageStrategyFactory fileStorageStrategyFactory;

    // 导出文件最大不能超过500M
    private static final Long EXPORT_MAX_SIZE = 512000L;

    /**
     * 查询材料文件列表
     * @param param
     * @return
     */
    public PageOutput<LawsuitFileVO> queryLawsuitFileList(LawsuitRecordParam param) throws Exception {
        PageParam pageParam = new PageParam(param.getPage(), param.getLimit());
        Page page = PageUtils.setPage(pageParam);
        Example example = new Example(LawsuitFile.class);
        example.and().andEqualTo("lawsuitId", param.getLawsuitId());
        example.orderBy("id").desc();
        List<LawsuitFile> lawsuitFiles = lawsuitFileMapper.selectByExample(example);
        List<LawsuitFileVO> lawsuitFileVOS = BeanUtil.copyPropertiesFromList(lawsuitFiles, LawsuitFileVO.class);
        // 操作人
        List<Long> operatorIds = lawsuitFiles.stream().map(LawsuitFile::getOperator).collect(Collectors.toList());
        Map<Long, String> userMap = userService.queryUserMap(operatorIds);
        // 文件类型
        List<Long> fileIds = lawsuitFiles.stream().map(LawsuitFile::getFileType).collect(Collectors.toList());
        Map<Long, String> fileMap = lawsuitConfigService.selectConfigMap(fileIds);
        lawsuitFileVOS.forEach(item -> {
            if (userMap.containsKey(item.getOperator())) {
                item.setOperatorName(userMap.get(item.getOperator()));
            }
            if (fileMap.containsKey(item.getFileType())) {
                item.setFileTypeName(fileMap.get(item.getFileType()));
            }
        });

        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), lawsuitFileVOS);
    }

    /**
     * 预先计算存储空间
     * @param files
     */
    public void preCalculateSpace(MultipartFile[] files, Long orgId) {
        if (systemConfig.getLocalDeploy()) {
            return;
        }
        long sumFileSize = 0L;
        // 计算文件大小
        for (MultipartFile file : files) {
            sumFileSize += file.getSize();
        }
        BigDecimal fileSize =
                BigDecimal.valueOf(sumFileSize)
                        .divide(new BigDecimal(1024))
                        .setScale(3, RoundingMode.HALF_UP);
        OrgSpace orgSpace = new OrgSpace();
        orgSpace.setOrgId(orgId);
        orgSpace = orgSpaceService.selectFirst(orgSpace);
        BigDecimal useSpace = orgSpace.getUseSpace().add(fileSize);
        if (useSpace.compareTo(orgSpace.getTotalSpace().add(orgSpace.getFreeSpace())) > 0) {
            throw new ApiException("剩余可用存储空间不足，请清理存储文件或续费后再操作");
        }
    }

    /**
     * 新增材料文件
     * @param param
     * @param files
     */
    @Transactional(rollbackFor = Exception.class)
    public void addLawsuitFile(LawsuitFileParam param, MultipartFile[] files) {
        // 查询是否存在该诉讼案件
        Lawsuit lawsuit = lawsuitService.selectByPrimaryKey(param.getLawsuitId());
        if (lawsuit == null) {
            throw new ApiException("不存在id为{}的诉讼案件", param.getLawsuitId());
        }
        UserSession userSession = UserUtils.getTokenUser();
        preCalculateSpace(files, userSession.getOrgId());
        Date expireDate = DateUtils.addYears(new Date(), 10);
        List<LawsuitFile> lawsuitFiles = new ArrayList<>();
        for (MultipartFile file : files) {
            LawsuitFile lawsuitFile = new LawsuitFile();
            String formerName = file.getOriginalFilename();
            String fileName =
                    StringUtils.substringBeforeLast(formerName, ".")
                            + "_"
                            + StringUtils.getRandomNumberBIT6()
                            + "."
                            + StringUtils.substringAfterLast(formerName, ".");
            String alias = "lawsuit/" + userSession.getOrgId() + "/"
                    + DateUtils.formatDate(new Date(), DateUtils.YEAR_MONTH_DATE_FORMAT) + "/"
                    + fileName;
            // byte->kb 保留三位有效数字
            BigDecimal fileSize =
                    BigDecimal.valueOf(file.getSize())
                            .divide(new BigDecimal(1024))
                            .setScale(3, RoundingMode.HALF_UP);
            // 重新计算使用已存储空间、诉讼文件
            orgSpaceService.addUseSpace(userSession.getOrgId(), fileSize, SpaceEnums.Type.LAWSUIT_FILE.getCode());
            UploadFileInfo uploadFileInfo = new UploadFileInfo();
            uploadFileInfo.setFile(file)
                    .setFileName(alias)
                    .setExpireDate(expireDate)
                    .setBucket(systemConfig.getCaseFilesBucket())
                    .setLocalUrl(systemConfig.getLawsuitFilePath() + DateUtils.formatDate(new Date()) + File.separator + fileName);
            FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
            String url = fileStorageStrategy.uploadFile(uploadFileInfo);
            lawsuitFile.setOrgId(userSession.getOrgId());
            lawsuitFile.setName(formerName);
            lawsuitFile.setShortFileName(fileName);
            lawsuitFile.setUrl(url);
            lawsuitFile.setAlias(alias);
            lawsuitFile.setFileSize(fileSize);
            lawsuitFile.setOperator(userSession.getId());
            lawsuitFile.setLawsuitId(param.getLawsuitId());
            lawsuitFile.setFileType(param.getFileType());
            lawsuitFile.setCreateTime(new Date());
            lawsuitFile.setUpdateTime(new Date());
            lawsuitFiles.add(lawsuitFile);
        }
        lawsuitFileMapper.insertList(lawsuitFiles);
    }

    /**
     * 删除对应材料文件记录及oss上文件 (单个删除)
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteLawsuitFile(Long id) {
        LawsuitFile lawsuitFile = lawsuitFileMapper.selectByPrimaryKey(id);
        if (lawsuitFile == null) {
            throw new ApiException("不存在id为" + id + "的材料记录文件");
        }
        boolean b = OSSClientUtil.deleteFileByFileName(lawsuitFile.getAlias(), lawsuitFile.getUrl());
        if (b) {
            // 重新计算诉讼文件存储空间、已使用存储空间
            orgSpaceService.subtractUseSpace(lawsuitFile.getOrgId(), lawsuitFile.getFileSize(), SpaceEnums.Type.LAWSUIT_FILE.getCode());
            lawsuitFileMapper.deleteByPrimaryKey(id);
        }
    }

    /**
     * 根据诉讼案件id列表查询对应材料文件
     * @param lawsuitIds
     * @return
     * @throws Exception
     */
    public Map<Long, List<LawsuitFileVO>> queryLawsuitFileByIds(List<Long> lawsuitIds) throws Exception {
        if (CollectionUtils.isEmpty(lawsuitIds)) {
            return Collections.emptyMap();
        }
        lawsuitIds = lawsuitIds.stream().distinct().collect(Collectors.toList());
        Example example = new Example(LawsuitFile.class);
        example.and().andIn("lawsuitId", lawsuitIds);
        example.orderBy("id").desc();
        List<LawsuitFile> lawsuitFiles = lawsuitFileMapper.selectByExample(example);
        List<LawsuitFileVO> lawsuitFileVOS = BeanUtil.copyPropertiesFromList(lawsuitFiles, LawsuitFileVO.class);

        Map<Long, List<LawsuitFileVO>> lawsuitFileMap = lawsuitFileVOS.stream().collect(Collectors.groupingBy(LawsuitFileVO::getLawsuitId));
        return lawsuitFileMap;
    }

    /**
     * 诉讼文件管理 分页查询
     * @param query
     * @return
     */
    public PageOutput<LawsuitFileVO> queryLawsuitFileManager(LawsuitFileQuery query) throws Exception {
        UserSession userSession = UserUtils.getTokenUser();
        query.setOrgId(userSession.getOrgId());
        // 保证公司隔离
        if (UserUtils.likeTeamLeader()) {
            // 如果是小组长，进行小组隔离
            query.setTeamId(userSession.getTeamId());
            query.setDepId(userSession.getDepId());
        }
        if (UserUtils.likeBranchAdmin()) {
            // 如果是分公司管理员，进行分公司隔离
            query.setDepId(userSession.getDepId());
        }
        // 处理时间
        if(StringUtils.isNotBlank(query.getSubmitDate())) {
            String[] split = query.getSubmitDate().split(",");
            query.setSubmitDateStart(new Date(Long.parseLong(split[0])));
            query.setSubmitDateEnd(new Date(Long.parseLong(split[1])));
        }
        PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<LawsuitFileDTO>  lawsuitFiles = lawsuitFileMapper.selectLawsuitFileList(query);
        List<LawsuitFileVO> lawsuitFileVOS = BeanUtil.copyPropertiesFromList(lawsuitFiles, LawsuitFileVO.class);

        // 设置操作人
        List<Long> operatorIds = lawsuitFiles.stream().filter(p-> ObjectUtil.isNotNull(p.getOperator())).map(LawsuitFileDTO::getOperator).collect(Collectors.toList());
        Map<Long, String> userMap = userService.queryUserMap(operatorIds);
        // 文件类型
        List<Long> fileIds = lawsuitFiles.stream().filter(p-> ObjectUtil.isNotNull(p.getFileType())).map(LawsuitFileDTO::getFileType).collect(Collectors.toList());
        Map<Long, String> fileMap = lawsuitConfigService.selectConfigMap(fileIds);
        // 委案公司
        List<Long> orgDeltIds = lawsuitFiles.stream().filter(p-> ObjectUtil.isNotNull(p.getOrgDeltId())).map(LawsuitFileDTO::getOrgDeltId).collect(Collectors.toList());
        Map<Long, String> deltMap = deltService.getDeltMap(orgDeltIds);
        lawsuitFileVOS.forEach(item -> {
            if (userMap.containsKey(item.getOperator())) {
                item.setOperatorName(userMap.get(item.getOperator()));
            }
            if (fileMap.containsKey(item.getFileType())) {
                item.setFileTypeName(fileMap.get(item.getFileType()));
            }
            if (deltMap.containsKey(item.getOrgDeltId())) {
                item.setOrgDeltName(deltMap.get(item.getOrgDeltId()));
            }
        });
        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), lawsuitFileVOS);
    }

    /**
     * 批量删除诉讼文件
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatchLawsuitFile(LawsuitFileDeleteParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        // 判断是否全选
        if (param.getAllSelect() != null && param.getAllSelect()) {
            param.setOrgId(userSession.getOrgId());
            // 保证公司隔离
            if (UserUtils.likeTeamLeader()) {
                // 如果是小组长，进行小组隔离
                param.setTeamId(userSession.getTeamId());
                param.setDepId(userSession.getDepId());
            }
            if (UserUtils.likeBranchAdmin()) {
                // 如果是分公司管理员，进行分公司隔离
                param.setDepId(userSession.getDepId());
            }
            // 处理时间
            if(StringUtils.isNotBlank(param.getSubmitDate())) {
                String[] split = param.getSubmitDate().split(",");
                param.setSubmitDateStart(new Date(Long.parseLong(split[0])));
                param.setSubmitDateEnd(new Date(Long.parseLong(split[1])));
            }
            List<LawsuitFileDTO> lawsuitFiles = lawsuitFileMapper.selectLawsuitFileList(param);
            if (CollectionUtils.isEmpty(lawsuitFiles)) {
                return;
            }
            List<Long> lawsuitFileIds = lawsuitFiles.stream().map(LawsuitFileDTO::getId).collect(Collectors.toList());

            // 设置异步任务
            AsyncTask asyncTask = new AsyncTask();
            asyncTask.setCreateBy(userSession.getId());
            asyncTask.setOrgId(userSession.getOrgId());
            asyncTask.setType(AsyncTaskEnums.Type.DEL_LAWSUIT_FILE.getCode());
            asyncTask.setStatus(AsyncTaskEnums.Status.ING.getCode());
            asyncTask.setTotal((long)lawsuitFileIds.size());
            asyncTask.setSuccessAmt(0L);
            asyncTask.setIgnoreCount(0L);
            asyncTaskMapper.insertSelective(asyncTask);
            String fieldJson = JSON.toJSONString(param);
            asyncTaskMapper.updateFieldJson(asyncTask.getId(), fieldJson);
            // 任务添加到任务列表
            stringRedisTemplate
                    .opsForSet()
                    .add(KeyCache.DEL_LAWSUIT_FILE_ID_PREFIX + asyncTask.getId(),
                            lawsuitFileIds.stream().map(String::valueOf).toArray(String[]::new));
            stringRedisTemplate.opsForList().leftPush(KeyCache.DEL_LAWSUIT_FILE_TASK, asyncTask.getId().toString());
            return;
        }
        // 同步删除
        if (CollectionUtils.isEmpty(param.getLawsuitFileIds())) {
            throw new ApiException("诉讼文件列表不能为空");
        }
        LawsuitFileQuery lawsuitFileQuery = new LawsuitFileQuery();
        lawsuitFileQuery.setLawsuitFileIds(param.getLawsuitFileIds());
        lawsuitFileQuery.setOrgId(userSession.getOrgId());
        // 公司隔离
        if (UserUtils.likeTeamLeader()) {
            // 如果是小组长，进行小组隔离
            lawsuitFileQuery.setTeamId(userSession.getTeamId());
            lawsuitFileQuery.setDepId(userSession.getDepId());
        }
        if (UserUtils.likeBranchAdmin()) {
            // 如果是分公司管理员，进行分公司隔离
            lawsuitFileQuery.setDepId(userSession.getDepId());
        }
        long successCount = 0L;
        List<LawsuitFileDTO> lawsuitFiles = lawsuitFileMapper.selectLawsuitFileList(lawsuitFileQuery);
        for (LawsuitFileDTO lawsuitFile : lawsuitFiles) {
            boolean delResult = OSSClientUtil.deleteFileByFileName(lawsuitFile.getAlias(), lawsuitFile.getUrl());
            if (delResult) {
                // 重新计算使用存储空间、诉讼存储空间
                orgSpaceService.subtractUseSpace(lawsuitFile.getOrgId(), lawsuitFile.getFileSize(), SpaceEnums.Type.LAWSUIT_FILE.getCode());
                lawsuitFileMapper.deleteByPrimaryKey(lawsuitFile.getId());
                successCount++;
                log.debug("批量删除诉讼文件处理到第{}个文件", successCount);
            }
        }

        AsyncTask asyncTask = new AsyncTask();
        asyncTask.setCreateBy(userSession.getId());
        asyncTask.setOrgId(userSession.getOrgId());
        asyncTask.setDepId(lawsuitFileQuery.getDepId());
        asyncTask.setTeamId(lawsuitFileQuery.getTeamId());
        asyncTask.setType(AsyncTaskEnums.Type.DEL_LAWSUIT_FILE.getCode());
        asyncTask.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
        asyncTask.setTotal((long)lawsuitFiles.size());
        asyncTask.setSuccessAmt(successCount);
        asyncTask.setIgnoreCount(0L);
        asyncTaskMapper.insertSelective(asyncTask);
        String fieldJson = JSON.toJSONString(param);
        asyncTaskMapper.updateFieldJson(asyncTask.getId(), fieldJson);

    }


    /**
     * 导出诉讼文件
     * @param query
     */
    public void exportLawsuitFile(LawsuitFileQuery query) {
        if (CollectionUtils.isEmpty(query.getOrgDeltIds()) || query.getOrgDeltIds().size() != 1) {
            throw new ApiException("请选择单个委案公司导出");
        }
        UserSession userSession = UserUtils.getTokenUser();
        query.setOrgId(userSession.getOrgId());
        // 保证公司隔离
        if (UserUtils.likeTeamLeader()) {
            // 如果是小组长，进行小组隔离
            query.setTeamId(userSession.getTeamId());
            query.setDepId(userSession.getDepId());
        }
        if (UserUtils.likeBranchAdmin()) {
            // 如果是分公司管理员，进行分公司隔离
            query.setDepId(userSession.getDepId());
        }
        // 处理时间
        if(StringUtils.isNotBlank(query.getSubmitDate())) {
            String[] split = query.getSubmitDate().split(",");
            query.setSubmitDateStart(new Date(Long.parseLong(split[0])));
            query.setSubmitDateEnd(new Date(Long.parseLong(split[1])));
        }
        BigDecimal sumSize = lawsuitFileMapper.querySumSizeByParam(query);
        if (sumSize == null) {
            throw new ApiException("当前无可导出数据！");
        }
        if (sumSize.compareTo(new BigDecimal(EXPORT_MAX_SIZE)) > 0) {
            throw new ApiException("单次导出多个诉讼材料文件最大不能超过500M");
        }
        // 查询选中记录的最大上传时间和最小时间
        LawsuitFileTimeDTO lawsuitFileTimeDTO = lawsuitFileMapper.selectLawsuitFileTime(query);
        String jsonStr = JSON.toJSONString(query);
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        jsonObject.put("orgDeltIds", query.getOrgDeltIds().get(0).toString());
        jsonObject.put("localDeploy", systemConfig.getLocalDeploy());
        jsonObject.put("fileMaxTime", lawsuitFileTimeDTO.getFileMaxTime());
        jsonObject.put("fileMinTime", lawsuitFileTimeDTO.getFileMinTime());
        DownloadTask downloadTask = new DownloadTask();
        if (UserUtils.likeTeamLeader()) {
            downloadTask.setDepId(userSession.getDepId());
            downloadTask.setTeamId(userSession.getTeamId());
        }
        if (UserUtils.likeBranchAdmin()) {
            downloadTask.setDepId(userSession.getDepId());
        }
        downloadTask.setCreateBy(userSession.getId());
        downloadTask.setUpdateBy(userSession.getId());
        downloadTask.setCreateTime(new Date());
        downloadTask.setUpdateTime(new Date());
        downloadTask.setType(DownloadTask.Type.LAWSUIT_FILE_EXPORT.getCode());
        downloadTask.setStatus(DownloadTask.Status.WAIT.getCode());
        downloadTask.setExpireTime(DateUtils.addDays(new Date(), 3));
        downloadTask.setOrgId(userSession.getOrgId());
        downloadTask.setProgress(new BigDecimal(0));
        downloadTask.setData(JSON.toJSONString(jsonObject));
        downloadTaskMapper.insertSelective(downloadTask);
    }
}
