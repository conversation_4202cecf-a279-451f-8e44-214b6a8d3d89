package com.anmi.collection.service.workorder;

import com.alibaba.fastjson.JSON;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.constant.WorkOrderConstant;
import com.anmi.collection.dto.workorder.WorkOrderNode;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.requset.workorder.WorkOrderTemplateParam;
import com.anmi.collection.entity.requset.workorder.WorkOrderTemplateQuery;
import com.anmi.collection.entity.response.workorder.WorkOrderFieldVO;
import com.anmi.collection.entity.response.workorder.WorkOrderTemplateVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.workorder.WorkOrderTemplateMapper;
import com.anmi.collection.service.UserService;
import com.anmi.collection.utils.AnmiBeanutils;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.workorder.WorkOrderTemplate;
import com.github.pagehelper.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 10:37
 */
@Service
public class WorkOrderTemplateService {
    @Resource
    private WorkOrderTemplateMapper workOrderTemplateMapper;
    @Resource
    private WorkOrderFieldService workOrderFieldService;
    @Resource
    private UserService userService;


    /**
     * 受理人为自动流转时，必须配置流转节点，并且需要设置完结权限
     * 工单字段是跟着模板绑定关联的，意味着新增工单的时候必须关联对应的模板表单字段信息
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void addWorkOrderTemplate(WorkOrderTemplateParam param) {
        if (CollectionUtils.isEmpty(param.getFieldList())) {
            throw new ApiException("模板表单字段不能为空");
        }
        if (Objects.equals(param.getHandlerType(), WorkOrderConstant.HANDLER_TYPE_AUTO)
                && CollectionUtils.isEmpty(param.getNodeList())) {
            throw new ApiException("流转节点不能为空");
        }
        WorkOrderTemplate template = AnmiBeanutils.copy(param, WorkOrderTemplate.class);
        UserSession userSession = UserUtils.getTokenUser();
        template.setOrgId(userSession.getOrgId());
        template.setEnable(WorkOrderConstant.IS_ON);
        template.setNode(JSON.toJSONString(param.getNodeList()));
        template.setCreateBy(userSession.getId());
        template.setUpdateBy(userSession.getId());
        template.setCreateTime(new Date());
        template.setUpdateTime(new Date());
        workOrderTemplateMapper.insertSelective(template);
        // 添加模板表单字段
        workOrderFieldService.addWorkOrderField(template.getId(), userSession, param.getFieldList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateWorkOrderTemplate(WorkOrderTemplateParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        WorkOrderTemplate template = AnmiBeanutils.copy(param, WorkOrderTemplate.class);
        if (!CollectionUtils.isEmpty(param.getNodeList())) {
            template.setNode(JSON.toJSONString(param.getNodeList()));
        }
        template.setUpdateBy(userSession.getId());
        template.setUpdateTime(new Date());
        workOrderTemplateMapper.updateByPrimaryKeySelective(template);
        if (Objects.equals(param.getHandlerType(), WorkOrderConstant.HANDLER_TYPE_MANUAL)) {
            workOrderTemplateMapper.updateTemplateNodeToNull(param.getId());
        }
    }

    public PageOutput<WorkOrderTemplateVO> pageWorkOrderTemplate(WorkOrderTemplateQuery query) {
        UserSession session = UserUtils.getTokenUser();
        Example example = new Example(WorkOrderTemplate.class);
        example.and().andEqualTo("orgId", session.getOrgId());
        if (StringUtils.isNotBlank(query.getName())) {
            example.and().andLike("name", query.getName() + "%");
        }
        example.orderBy("id").desc();
        PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<WorkOrderTemplate> templateList = workOrderTemplateMapper.selectByExample(example);
        Map<Long, String> userMap = userService.getNames(session.getOrgId());
        List<WorkOrderTemplateVO> voList = new ArrayList<>();
        templateList.forEach(workOrderTemplate -> {
            WorkOrderTemplateVO vo = AnmiBeanutils.copy(workOrderTemplate, WorkOrderTemplateVO.class);
            vo.setUpdateByName(userMap.get(vo.getUpdateBy()));
            voList.add(vo);
        });
        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), voList);
    }

    public List<WorkOrderTemplateVO> listWorkOrderTemplate() {
        UserSession userSession = UserUtils.getTokenUser();
        Example example = new Example(WorkOrderTemplate.class);
        example.and().andEqualTo("orgId", userSession.getOrgId())
                .andEqualTo("enable", WorkOrderConstant.IS_ON);
        example.orderBy("id").desc();
        List<WorkOrderTemplate> templateList = workOrderTemplateMapper.selectByExample(example);
        List<WorkOrderTemplateVO> voList = new ArrayList<>();
        templateList.forEach(workOrderTemplate -> {
            WorkOrderTemplateVO vo = AnmiBeanutils.copy(workOrderTemplate, WorkOrderTemplateVO.class);
            voList.add(vo);
        });
        return voList;
    }

    public WorkOrderTemplateVO getWorkOrderTemplate(Long templateId) {
        WorkOrderTemplate workOrderTemplate = workOrderTemplateMapper.selectByPrimaryKey(templateId);
        WorkOrderTemplateVO vo = AnmiBeanutils.copy(workOrderTemplate, WorkOrderTemplateVO.class);
        if (StringUtils.isNotBlank(workOrderTemplate.getNode())) {
            vo.setNodeList(JSON.parseArray(workOrderTemplate.getNode(), WorkOrderNode.class));
        }
        List<WorkOrderFieldVO> fieldList = workOrderFieldService.listWorkOrderField(templateId);
        vo.setFieldList(fieldList);
        return vo;
    }

    public Map<Long ,String> getWorkOrderTemplateName(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        Example example = new Example(WorkOrderTemplate.class);
        example.and().andIn("id", ids);
        List<WorkOrderTemplate> templateList = workOrderTemplateMapper.selectByExample(example);
        return templateList.stream().collect(Collectors.toMap(WorkOrderTemplate::getId, WorkOrderTemplate::getName));
    }
}
