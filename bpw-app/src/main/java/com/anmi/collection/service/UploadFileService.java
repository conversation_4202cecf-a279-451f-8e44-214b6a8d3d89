package com.anmi.collection.service;

import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.OSSClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/29 16:31
 */
@Service
@Slf4j
public class UploadFileService {

    @Resource
    private SystemConfig systemConfig;

    /**
     * 上传公告图片
     * @param file
     * @return
     */
    public String uploadFile(MultipartFile file) {
        if (file == null) {
            return null;
        }
        String fileName = file.getOriginalFilename();
        // 上传文件
        fileName =
                StringUtils.substringBeforeLast(fileName, ".")
                        + "_"
                        + StringUtils.getRandomNumberBIT6()
                        + "."
                        + StringUtils.substringAfterLast(fileName, ".");
        Date expireDate = DateUtils.addYears(new Date(), 10);
        String url = OSSClientUtil.putFile(fileName, expireDate, systemConfig.getCaseFilesBucket(), file);
        if (StringUtils.isBlank(url)) {
            throw new ApiException("文件上传失败");
        }
        log.info("印章文件已上传至OSS。。。 文件名为：{} 地址为：{}", fileName, url);
        return url;
    }
}
