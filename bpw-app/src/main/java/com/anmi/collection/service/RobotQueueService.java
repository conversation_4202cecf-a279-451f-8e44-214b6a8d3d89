package com.anmi.collection.service;

import cn.duyan.thread.DuyanThreadExecutor;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.CaseOperationEnums;
import com.anmi.collection.common.enums.RobotQueueEnums;
import com.anmi.collection.dto.fileStorage.UploadCreatedFileInfo;
import com.anmi.collection.entity.requset.robot.RobotDownloadParam;
import com.anmi.collection.entity.requset.robot.RobotQueueExportParam;
import com.anmi.collection.entity.requset.robot.RobotQueueParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.robot.DownloadListVO;
import com.anmi.collection.entity.response.robot.RobotQueueVO;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.manager.dorest.DoRestResult;
import com.anmi.collection.manager.dorest.request.CreateRobotPointReq;
import com.anmi.collection.manager.dorest.response.CreateRobotPointRes;
import com.anmi.collection.mapper.CaseOperationMapper;
import com.anmi.collection.mapper.OrgSwitchMapper;
import com.anmi.collection.mapper.RobotQueueMapper;
import com.anmi.collection.mapper.RobotStrategyLimitMapper;
import com.anmi.collection.mq.RobotQueueDelayElement;
import com.anmi.collection.redis.RedisLock;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.strategy.CaseStrategyBO;
import com.anmi.collection.service.external.AbstractExternalStrategy;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.base.CaseOperationDownloadEntity;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseOperation;
import com.anmi.domain.cases.OrgSwitch;
import com.anmi.domain.decision.StrategyExecLog;
import com.anmi.domain.decision.StrategyInfo;
import com.anmi.domain.robot.RobotQueue;
import com.anmi.domain.robot.RobotQueueFail;
import com.anmi.domain.robot.RobotStrategyLimit;
import com.anmi.domain.robot.RobotTemplate;
import com.anmi.domain.site.SiteVarsMapping;
import com.anmi.domain.sys.DownloadTask;
import com.github.pagehelper.Page;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@Component
@RefreshScope
public class RobotQueueService extends BaseService<RobotQueue> implements CommandLineRunner {

  @Value("${rocketmq.consumer.concurrency.control:0}")
  private Long delayTime;

  private final DelayQueue<RobotQueueDelayElement> queue = new DelayQueue<>();

  private static final String REDIS_LOCK_KEY = "bpw_case_robot_queue:";

  @Resource
  private RobotTemplateService robotTemplateService;

  @Resource
  private StringRedisTemplate stringRedisTemplate;

  @Resource
  private DuyanManager duyanManager;

  @Resource
  private OrgSwitchMapper orgSwitchMapper;

  @Resource
  private CaseService caseService;

  @Resource
  private DeltService deltService;

  @Resource
  private StrategyInfoService strategyInfoService;

  @Resource
  private CaseOperationService caseOperationService;

  @Resource
  private CaseOperationMapper caseOperationMapper;

  @Resource
  private ProductService productService;

  @Resource
  private RedisLock redisLock;

  @Resource
  private SiteVarsMappingService siteVarsMappingService;

  @Resource
  private RobotStrategyLimitMapper robotStrategyLimitMapper;

  @Resource
  private RobotQueueFailService robotQueueFailService;

  @Resource
  private DownloadTaskService downloadTaskService;

  @Resource
  private UserService userService;

  @Resource
  private OutBatchService outBatchService;

  @Resource
  private FileStorageStrategyFactory fileStorageStrategyFactory;

  @Resource
  private RobotQueueMapper robotQueueMapper;

  @Resource
  private List<AbstractExternalStrategy> externalStrategyList;


  public PageOutput<RobotQueueVO> list(RobotQueueParam robotQueueParam) {
    UserSession userSession = UserUtils.getTokenUser();

    Example example = new Example(RobotQueue.class);
    example.and().andEqualTo("orgId", userSession.getOrgId());
    if (!CollectionUtils.isEmpty(robotQueueParam.getOutSerialNos())) {
      example.and().andIn("outSerialNo", robotQueueParam.getOutSerialNos());
    }
    example.orderBy("execTime").asc();
    Page<?> page = this.setPage(robotQueueParam);
    List<RobotQueue> robotQueues = this.selectByExample(example);
    if (CollectionUtils.isEmpty(robotQueues)) {
      return new PageOutput<>();
    }

    Map<Long, RobotTemplate> robotTemplateMap = new HashMap<>();
    List<Long> robotTemplateIds = robotQueues.stream().map(RobotQueue::getTemplateId).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(robotTemplateIds)) {
      List<RobotTemplate> robotTemplates = robotTemplateService.selectByIdList(robotTemplateIds);
      robotTemplateMap = robotTemplates.stream().collect(Collectors.toMap(RobotTemplate::getId, Function.identity()));
    }
    Map<Long, StrategyInfo> strategyInfoMap = new HashMap<>();
    List<Long> strategyIds = robotQueues.stream().map(RobotQueue::getSourceId).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(strategyIds)) {
      Example exampleStrategy = new Example(StrategyInfo.class);
      exampleStrategy.and().andIn("id", strategyIds);
      exampleStrategy.selectProperties("id", "name");
      List<StrategyInfo> strategyInfoList = strategyInfoService.selectByExample(exampleStrategy);
      strategyInfoMap = strategyInfoList.stream().collect(Collectors.toMap(StrategyInfo::getId, Function.identity()));
    }


    List<RobotQueueVO> list = new ArrayList<>();
    for (RobotQueue robotQueue : robotQueues) {
      RobotQueueVO vo = new RobotQueueVO();
      BeanUtils.copyProperties(robotQueue, vo);
      if (robotTemplateMap.containsKey(robotQueue.getTemplateId())) {
        RobotTemplate robotTemplate = robotTemplateMap.get(robotQueue.getTemplateId());
        vo.setTemplateName(robotTemplate.getName());
      }
      if (Objects.nonNull(robotQueue.getSourceId()) && strategyInfoMap.containsKey(robotQueue.getSourceId())) {
        StrategyInfo strategyInfo = strategyInfoMap.get(robotQueue.getSourceId());
        vo.setSourceName(strategyInfo.getName());
      }
      list.add(vo);
    }

    PageOutput<RobotQueueVO> result = new PageOutput<>();
    result.setPageNum(page.getPageNum());
    result.setTotal((int) page.getTotal());
    result.setLimit(page.getPageSize());
    result.setPages(page.getPages());
    result.setList(list);
    return result;
  }

  public Long getAllData(RobotQueueParam robotQueueParam, UserSession userSession, Consumer<PageOutput<RobotQueue>> callBack) {
    Example example = new Example(RobotQueue.class);
    example.and().andEqualTo("orgId", userSession.getOrgId());
    if (!CollectionUtils.isEmpty(robotQueueParam.getOutSerialNos())) {
      example.and().andIn("outSerialNo", robotQueueParam.getOutSerialNos());
    }
    example.orderBy("execTime").asc();
    int pageNum = 1;
    Integer limit = 500;
    int totalPage = 1;
    PageParam pageParam = new PageParam();
    pageParam.setLimit(limit);
    long totalNum = 0L;
    while (true) {
      pageParam.setPage(pageNum);
      Page<?> page = super.setPage(pageParam);
      List<RobotQueue> list = this.selectByExample(example);
      PageOutput<RobotQueue> pageInfo = new PageOutput<>();
      pageInfo.setTotal((int) page.getTotal());
      pageInfo.setPageNum(page.getPageNum());
      pageInfo.setPageSize(page.getPageSize());
      pageInfo.setLimit(page.getPageSize());
      pageInfo.setPages(page.getPages());
      pageInfo.setList(list);
      callBack.accept(pageInfo);
      totalNum += list.size();
      if (pageNum == 1) {
        Integer total = (int) page.getTotal();
        int i = (total % limit) > 0 ? 1 : 0;
        totalPage = total / limit + i;
      }
      if (pageNum >= totalPage) {
        break;
      }
      pageNum++;
    }
    return totalNum;
  }

  public void sendDelayTime(Long robotQueueId, Date execTime) {
    RobotQueueDelayElement robotQueueDelayElement = new RobotQueueDelayElement(execTime.getTime(), robotQueueId);
    queue.offer(robotQueueDelayElement);
  }

  @Transactional
  public RobotQueue saveCaseRobot(CaseStrategyBO data, RobotTemplate robotTemplate, Date execTime, UserSession session, StrategyExecLog strategyExecLog, Integer sendStatus, String macAddress) {
    RobotQueue robotQueue = new RobotQueue();
    robotQueue.setCaseId(data.getId());
    robotQueue.setCreateBy(session.getId());
    robotQueue.setCreateTime(new Date());
    robotQueue.setExecTime(execTime);
    robotQueue.setOrgId(session.getOrgId());
    robotQueue.setStatus(sendStatus);
    robotQueue.setUpdateBy(session.getId());
    robotQueue.setUpdateTime(new Date());
    robotQueue.setOutSerialNo(data.getOutSerialNo());
    robotQueue.setSourceId(strategyExecLog.getStrategyId());
    robotQueue.setTemplateId(robotTemplate.getId());
    robotQueue.setSourceMachine(macAddress);
    robotQueue.setSiteId(robotTemplate.getSiteId());
    robotQueue.setSiteName(robotTemplate.getSiteName());
    robotQueue.setCaller(robotTemplate.getCaller());
    robotQueue.setPoolId(robotTemplate.getPoolId());
    this.insertSelective(robotQueue);
    return robotQueue;
  }


  @SneakyThrows
  public void onMessage(String message) {
    LogbackUtil.insertTrace(LogbackUtil.generateTrace());
    log.info("接收到消息" + message + "执行机器人点呼");
    //每个消息消费完之后sleep一段时间，防止度言机器人并发过高
    // 比如这里设置为100的话，单个线程每秒最多消费10条，每台机器6个线程(consumeThreadNumber)每秒最多60条，
    // 三台消费节点的话每秒最多消费180
    Long robotQueueId = Long.parseLong(message);
    if (!redisLock.tryLock(REDIS_LOCK_KEY + robotQueueId, 2000L, 5000L)) {
      return;
    }
    //加锁，防止重复消费
    try {
      RobotQueue robotQueue = this.selectByPrimaryKey(robotQueueId);
      if (Objects.isNull(robotQueue)) {
        return;
      }
      Date now = new Date();
      OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(robotQueue.getOrgId());
      if (Objects.nonNull(orgSwitch.getRobotExecTimeStart()) && Objects.nonNull(orgSwitch.getRobotExecTimeEnd())) {
        String[] start = orgSwitch.getRobotExecTimeStart().split(":");
        String[] end = orgSwitch.getRobotExecTimeEnd().split(":");
        Date min = DateUtil.offsetMinute(DateUtil.beginOfDay(now), Integer.parseInt(start[0]) * 60 + Integer.parseInt(start[1]));
        Date max = DateUtil.offsetMinute(DateUtil.beginOfDay(now), Integer.parseInt(end[0]) * 60 + Integer.parseInt(end[1]));
        if (DateUtil.compare(now, min) < 0 || DateUtil.compare(now, max) > 0) {
          log.info("id为{}的机器人队列不在管控执行时间范围内，推迟到第二天执行", robotQueue.getId());
          Date execTime = DateUtil.offsetDay(min, 1);
          robotQueue.setExecTime(execTime);
          robotQueue.setUpdateTime(new Date());
          this.updateByPrimaryKey(robotQueue);
          if (DateUtil.compare(DateUtil.offsetHour(new Date(), 1), execTime) > 0) {
            sendDelayTime(robotQueue.getId(), execTime);
          }
          return;
        }
      }
      Example caseOperationQueryExample = new Example(CaseOperation.class);
      caseOperationQueryExample.and().andEqualTo("caseId", robotQueue.getCaseId());
      caseOperationQueryExample.and().andGreaterThanOrEqualTo("createTime", DateUtils.getStartTimeOfDate(now));
      caseOperationQueryExample.and().andLessThanOrEqualTo("createTime", DateUtils.getEndTimeOfDay(now));
      caseOperationQueryExample.and().andIn("submitType", Arrays.asList(CaseOperationEnums.SubmitType.ROBOT.getCode(), CaseOperationEnums.SubmitType.ROBOT_POINT.getCode()));
      List<CaseOperation> caseOperations = caseOperationService.selectByExample(caseOperationQueryExample);
      Example robotStrategyLimitExample = new Example(RobotStrategyLimit.class);
      robotStrategyLimitExample.and().andEqualTo("orgId", robotQueue.getOrgId());
      List<RobotStrategyLimit> robotStrategyLimits = robotStrategyLimitMapper.selectByExample(robotStrategyLimitExample);
      if (isLimited(caseOperations, robotStrategyLimits)) {
        log.info("id为{}的机器人队列触发呼叫频率限制，不再继续外呼", robotQueue.getId());
        this.deleteByPrimaryKey(robotQueueId);
        createRobotQueueFail(robotQueue, RobotQueueEnums.RobotFailType.LIMIT.getCode());
        return;
      }

      Case caseInfo = caseService.selectByPrimaryKey(robotQueue.getCaseId());
      List<Case> noExecCase = externalStrategyList.stream()
              .flatMap(strategy -> strategy.filter(Collections.singletonList(caseInfo), new HashMap<>()).stream())
              .collect(Collectors.toList());
      if (!CollectionUtils.isEmpty(noExecCase)) {
        return;
      }

      CreateRobotPointReq createRobotPointReq = new CreateRobotPointReq();
      createRobotPointReq.setRobot_id(null);
      createRobotPointReq.setCallee(caseInfo.getOwnMobile());
      if(Objects.nonNull(robotQueue.getPoolId())){
        createRobotPointReq.setPool_id(robotQueue.getPoolId());
      }else if(StringUtils.isBlank(robotQueue.getCaller())) {
        createRobotPointReq.setCaller(robotQueue.getCaller());
      }
      createRobotPointReq.setImmediate_call_type(3);
      createRobotPointReq.setVoice_site_id(robotQueue.getSiteId());
      createRobotPointReq.setIs_early_media_rec_enabled(0);
      createRobotPointReq.setVars(getVarMapping(caseInfo, robotQueue.getSiteId()));
      DoRestResult<CreateRobotPointRes> result = null;
      try {
        result = duyanManager.createRobotPoint(createRobotPointReq, caseInfo.getOrgId(), caseInfo.getId());
      } catch (Exception ex) {
        //其他异常
        createRobotQueueFail(robotQueue, RobotQueueEnums.RobotFailType.OTHER.getCode());
        this.deleteByPrimaryKey(robotQueueId);
        return;
      }
      if (!Objects.equals(result.getStatus(), 1L) || Objects.isNull(result.getData()) || Objects.isNull(result.getData().getCall_uuid())) {
        log.warn("创建机器人点呼失败,error:{},{}", result.getError_code(), result.getMessage());
        createRobotQueueFail(robotQueue, result.getError_code());
        this.deleteByPrimaryKey(robotQueueId);
        return;
      }
      Integer todayOperCount = caseOperationMapper.selectTodayRobotCount(robotQueue.getCaseId());
      Integer totalOperCount = caseOperationMapper.selectTotalRobotCount(robotQueue.getCaseId());
      //返回uuid,生成催记
      CaseOperation caseOperation = new CaseOperation();
      caseOperation.setOrgId(caseInfo.getOrgId());
      caseOperation.setOrgDeltId(caseInfo.getOrgDeltId());
      caseOperation.setOutSerialNo(caseInfo.getOutSerialNo());
      caseOperation.setOutSerialTemp(caseInfo.getOutSerialTemp());
      caseOperation.setCaseId(caseInfo.getId());
      caseOperation.setCallUuid(result.getData().getCall_uuid());
      caseOperation.setCreateTime(new Date());
      caseOperation.setUpdateTime(new Date());
      caseOperation.setCreateBy(-1L);
      caseOperation.setAdminSubmitter(-1L);
      caseOperation.setUpdateBy(-1L);
      caseOperation.setStatus(0);
      caseOperation.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
      caseOperation.setSubmitType(CaseOperationEnums.SubmitType.ROBOT_POINT.getCode());
      caseOperation.setCallee(caseInfo.getOwnMobile());
      caseOperation.setCreateType(CaseOperationEnums.CreateType.SYS.getCode());
      caseOperation.setConMobile(caseInfo.getOwnMobile());
      caseOperation.setConName(caseInfo.getName());
      caseOperation.setRelationType("本人");
      caseOperation.setCallStyle(CaseOperationEnums.CallStyle.OUT.getCode());
      caseOperation.setSiteId(robotQueue.getSiteId());
      caseOperation.setSiteName(robotQueue.getSiteName());
      caseOperation.setPoolId(robotQueue.getPoolId());
      caseOperation.setCallbackFlag(0);
      caseOperation.setOperTime(todayOperCount + 1);
      caseOperation.setTotalOperTime(totalOperCount + 1);
      caseOperationService.insertSelective(caseOperation);
      if (Objects.nonNull(orgSwitch.getRobotTimeout())) {
        pushToWaitCallbackQueue(caseOperation.getId(), orgSwitch.getRobotTimeout() * 60 * 1000L + System.currentTimeMillis());
      }

      this.deleteByPrimaryKey(robotQueueId);
    } catch (Exception ex) {
      log.error(message + "消费失败", ex);
    } finally {
      redisLock.unlock(REDIS_LOCK_KEY + robotQueueId);
    }
    Thread.sleep(delayTime);
  }

  public static final String CASE_OPERATION_TIMEOUT_KEY = "case_operation_timeout";

  private void pushToWaitCallbackQueue(Long caseOperationId, Long timeout) {
    stringRedisTemplate.opsForZSet().add(CASE_OPERATION_TIMEOUT_KEY, String.valueOf(caseOperationId), timeout);
  }

  private void createRobotQueueFail(RobotQueue robotQueue, String failType) {
    RobotQueueFail robotQueueFail = new RobotQueueFail();
    BeanUtils.copyProperties(robotQueue, robotQueueFail);
    robotQueueFail.setFailType(failType);
    robotQueueFail.setCreateTime(new Date());
    robotQueueFail.setUpdateTime(new Date());
    robotQueueFailService.insert(robotQueueFail);
  }

  private boolean isLimited(List<CaseOperation> caseOperations, List<RobotStrategyLimit> robotStrategyLimits) {
    Map<String, Long> outcomCountMap = caseOperations.stream().collect(Collectors.groupingBy(CaseOperation::getOutcome, Collectors.counting()));
    Integer total = caseOperations.size();
    for (RobotStrategyLimit robotStrategyLimit : robotStrategyLimits) {
      if (Objects.equals(robotStrategyLimit.getOperator(), "=")) {
        Long count = outcomCountMap.getOrDefault(robotStrategyLimit.getValue(), 0L);
        if (count >= robotStrategyLimit.getRateLimit()) {
          return true;
        }
      } else if (Objects.equals(robotStrategyLimit.getOperator(), "!=")) {
        Long count = outcomCountMap.getOrDefault(robotStrategyLimit.getValue(), 0L);
        if (total - count >= robotStrategyLimit.getRateLimit()) {
          return true;
        }
      }
    }
    return false;
  }


  public Map<String, String> getVarMapping(Case caseInfo, Long siteId) {
    List<SiteVarsMapping> siteVarsMappings = siteVarsMappingService.getSiteVarsMappings(caseInfo.getOrgId(), siteId);
    Map<String, String> result = new HashMap<>();
    List<String> amountFields = Arrays.asList("amount", "payAmount", "capital", "rest_capital", "overdue_rate", "overdue_punish_rate", "penalty",
      "overdue_management_fee", "late_fee", "settled_amt", "amount_per_period", "repay_min_amt", "amount_unpaid", "pro_price", "down_payment",
      "total_amount_paid", "load_amount", "rate");
    for (SiteVarsMapping siteVarsMapping : siteVarsMappings) {
      if (Objects.equals(siteVarsMapping.getAnmiVar(), "name")) {
        result.put(siteVarsMapping.getDuyanVar(), caseInfo.getName());
      } else if (Objects.equals(siteVarsMapping.getAnmiVar(), "out_serial_no")) {
        result.put(siteVarsMapping.getDuyanVar(), caseInfo.getOutSerialTemp());
      } else if (Objects.equals(siteVarsMapping.getAnmiVar(), "id_card")) {
        result.put(siteVarsMapping.getDuyanVar(), caseInfo.getIdCard());
      } else if (Objects.equals(siteVarsMapping.getAnmiVar(), "own_mobile")) {
        result.put(siteVarsMapping.getDuyanVar(), caseInfo.getOwnMobile());
      } else if (Objects.equals(siteVarsMapping.getAnmiVar(), "amount")) {
        result.put(siteVarsMapping.getDuyanVar(), String.valueOf(caseInfo.getAmount() / 1000.00));
      } else if (Objects.equals(siteVarsMapping.getAnmiVar(), "delt_id")) {
        result.put(siteVarsMapping.getDuyanVar(), deltService.getNames().get(caseInfo.getOrgDeltId()));
      } else if (Objects.equals(siteVarsMapping.getAnmiVar(), "product_name")) {
        result.put(siteVarsMapping.getDuyanVar(), productService.getNames().get(caseInfo.getProductId()));
      } else if (Objects.equals(siteVarsMapping.getAnmiVar(), "overdue_date")) {
        result.put(siteVarsMapping.getDuyanVar(), DateUtils.formatDate(caseInfo.getOverdueDate(), DateUtils.SIMPLE_DATE_FORMAT));
      } else if (Objects.equals(siteVarsMapping.getAnmiVar(), "entrust_end_time")) {
        result.put(siteVarsMapping.getDuyanVar(), DateUtils.formatDate(caseInfo.getEntrustEndTime(), DateUtils.SIMPLE_DATE_FORMAT));
      } else if (Objects.equals(siteVarsMapping.getAnmiVar(), "overdue_days")) {
        result.put(siteVarsMapping.getDuyanVar(), String.valueOf(caseInfo.getOverdueDays()));
      } else {
        if (amountFields.contains(siteVarsMapping.getAnmiVar())) {
          result.put(siteVarsMapping.getDuyanVar(), String.valueOf(Long.parseLong(caseInfo.getFieldJson().get(siteVarsMapping.getAnmiVar())) / 1000.00));
        } else {
          result.put(siteVarsMapping.getDuyanVar(), caseInfo.getFieldJson().get(siteVarsMapping.getAnmiVar()));
        }
      }
    }
    result.put("U_orgId", String.valueOf(caseInfo.getOrgId()));
    result.put("U_caseId", String.valueOf(caseInfo.getId()));
    result.put("U_phone", caseInfo.getOwnMobile());
    return result;
  }

  private final DuyanThreadExecutor duyanThreadExecutor = new DuyanThreadExecutor(5, 10, "robot-queue-consumer");

  @Override
  public void run(String... args) throws Exception {
    //刚启动的时候把当前时间前后两小时内的数据加载到内存中
    Date now = new Date();
    Date start = DateUtil.offsetHour(now, -1);
    Date end = DateUtil.offsetHour(now, 1);
    Example example = new Example(RobotQueue.class);
    example.and().andGreaterThanOrEqualTo("execTime", start);
    example.and().andLessThanOrEqualTo("execTime", end);
    example.and().andEqualTo("sourceMachine", IpUtil.ip);
    List<RobotQueue> robotQueueList = this.selectByExample(example);
    for (RobotQueue robotQueue : robotQueueList) {
      if (contains(robotQueue.getId())) {
        continue;
      }
      RobotQueueDelayElement robotQueueDelayElement = new RobotQueueDelayElement(robotQueue.getExecTime().getTime(), robotQueue.getId());
      queue.offer(robotQueueDelayElement);
    }

    duyanThreadExecutor.submit(() -> {
      while (!Thread.currentThread().isInterrupted()) {
        try {
          RobotQueueDelayElement ele = queue.poll(1000, TimeUnit.MILLISECONDS);
          if (ele != null) {
            log.info("接收到消息：{}", ele);
            duyanThreadExecutor.submit(() -> this.onMessage(String.valueOf(ele.robotQueueId)));
          }
          Thread.sleep(delayTime);
        } catch (InterruptedException ignored) {
        }
      }
    });

  }

  private boolean contains(Long robotQueueId) {
    for (RobotQueueDelayElement element : queue) {
      if (Objects.equals(element.robotQueueId, robotQueueId)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 每小时执行一次，把当前小时内的队列数据发送到消息队列,允许五分钟的误差，防止数据库时间和服务器时间差
   * 暂时不考虑一小时内数据量太大的情况
   */
  @Scheduled(cron = "0 5 * * * ? ")
  public void schedule() {
    Date now = new Date();
    Date startOfHour = DateUtils.getStartTimeOfHour(now);
    Date endOfHourOffset5M = DateUtil.offsetMinute(DateUtil.offsetHour(startOfHour, 1), 5);
    Example example = new Example(RobotQueue.class);
    example.and().andGreaterThanOrEqualTo("execTime", startOfHour);
    example.and().andLessThanOrEqualTo("execTime", endOfHourOffset5M);
    example.and().andEqualTo("sourceMachine", IpUtil.ip);
    List<RobotQueue> robotQueueList = this.selectByExample(example);
    for (RobotQueue robotQueue : robotQueueList) {
      RobotQueueDelayElement robotQueueDelayElement = new RobotQueueDelayElement(robotQueue.getExecTime().getTime(), robotQueue.getId());
      queue.offer(robotQueueDelayElement);
    }
  }


  private final DuyanThreadExecutor robotAsyncThreadExecutor = new DuyanThreadExecutor("bpw-robot-async");

  /**
   * 导出，返回下载id
   *
   * @param robotQueueExportParam 导出入参
   * @return 返回download task id
   */
  public Long export(RobotQueueExportParam robotQueueExportParam) {
    UserSession userSession = UserUtils.getTokenUser();
    DownloadTask downloadTask = new DownloadTask();
    downloadTask.setCreateTime(new Date());
    downloadTask.setUpdateTime(new Date());
    downloadTask.setType(DownloadTask.Type.ROBOT_QUEUE_EXPORT.getCode());
    downloadTask.setCreateBy(userSession.getId());
    downloadTask.setUpdateBy(userSession.getId());
    downloadTask.setStatus(DownloadTask.Status.PROCESSING.getCode());
    downloadTask.setExpireTime(DateUtils.addDays(new Date(), 3));
    downloadTask.setOrgId(userSession.getOrgId());
    downloadTask.setProgress(new BigDecimal(0));
    downloadTask.setData(JSON.toJSONString(robotQueueExportParam));
    downloadTaskService.insertSelective(downloadTask);
    robotAsyncThreadExecutor.submit(() -> {
      List<List<String>> header = createExportHeader();
      String fileName = "机器人队列导出_" + downloadTask.getId() + ".xlsx";
      String sheetName = "机器人队列导出";
      ExcelWriter excelWriter = EasyExcel.write(fileName).build();
      WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
      excelWriter.write(header, writeSheet);
      Long total = 0L;
      if (!robotQueueExportParam.getAllSelect()) {
        List<Long> robotQueueIds = robotQueueExportParam.getRobotQueueIdList();
        List<RobotQueue> robotQueues = this.selectByIdList(robotQueueIds);
        List<List<String>> data = convert(robotQueues);
        excelWriter.write(data, writeSheet);
        total = (long) robotQueues.size();
      } else {
        total = this.getAllData(robotQueueExportParam, userSession, (pageInfo) -> {
          if (!CollectionUtils.isEmpty(pageInfo.getList())) {
            List<List<String>> data = convert(pageInfo.getList());
            excelWriter.write(data, writeSheet);
          }
        });
      }
      excelWriter.finish();
      if (total == 0) {
        downloadTask.setStatus(-2);
        downloadTask.setFailedReason("导出列表为空");
      } else {
        downloadTask.setStatus(2);
        File excelFile = new File(fileName);
        downloadTask.setFileSize(excelFile.length() / 1024);
        Date expireDate = DateUtils.addDays(new Date(), 3);

        UploadCreatedFileInfo uploadCreatedFileInfo = new UploadCreatedFileInfo();
        uploadCreatedFileInfo.setFile(excelFile)
                .setFileName(fileName)
                .setExpireDate(expireDate)
                .setBucket(systemConfig.getTemporaryFileBucket())
                .setLocalUrl(systemConfig.getLocalFilePath() + File.separator + fileName);
        FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
        String url = fileStorageStrategy.uploadCreatedFile(uploadCreatedFileInfo);
        downloadTask.setDownloadUrl(url);
      }

      downloadTask.setDataNums(total);
      downloadTask.setProgress(new BigDecimal(100));
      downloadTaskService.updateByPrimaryKeySelective(downloadTask);
    });
    return downloadTask.getId();
  }

  private List<List<String>> createExportHeader() {
    return Collections.singletonList(Arrays.asList("案件编号", "预备执行时间", "机器人话术", "主叫号码", "实时监控策略来源"));
  }

  private List<List<String>> convert(List<RobotQueue> list) {
    if (CollectionUtils.isEmpty(list)) {
      return new ArrayList<>();
    }
    List<List<String>> result = new ArrayList<>();
    Map<Long, String> strategyInfoMap = new HashMap<>();
    List<Long> strategyIds = list.stream().map(RobotQueue::getSourceId).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(strategyIds)) {
      Example exampleStrategy = new Example(StrategyInfo.class);
      exampleStrategy.and().andIn("id", strategyIds);
      exampleStrategy.selectProperties("id", "name");
      List<StrategyInfo> strategyInfoList = strategyInfoService.selectByExample(exampleStrategy);
      strategyInfoMap = strategyInfoList.stream().collect(Collectors.toMap(StrategyInfo::getId, StrategyInfo::getName));
    }

    for (RobotQueue robotQueue : list) {
      List<String> tmp = new ArrayList<>();
      tmp.add(robotQueue.getOutSerialNo());
      tmp.add(DateUtil.format(robotQueue.getExecTime(), "yyyy-MM-dd HH:mm:ss"));
      tmp.add(robotQueue.getSiteName());
      tmp.add(Objects.isNull(robotQueue.getCaller()) ? "公司号码池" : robotQueue.getCaller());
      tmp.add(strategyInfoMap.get(robotQueue.getSourceId()));
      result.add(tmp);
    }
    return result;
  }

  public PageOutput<DownloadListVO> downloadList(RobotDownloadParam robotDownloadParam) {
    UserSession loginUser = getTokenUser();
    // 找到所有的催记下载任务列表
    Example example = new Example(DownloadTask.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("orgId", loginUser.getOrgId());
    criteria.andIn("type",
      Arrays.asList(DownloadTask.Type.ROBOT_QUEUE_EXPORT.getCode(),
        DownloadTask.Type.ROBOT_QUEUE_FAIL_EXPORT.getCode(),
        DownloadTask.Type.CASE_OPERATION_ROBOT_SUCCESS_EXPORT.getCode(),
        DownloadTask.Type.CASE_OPERATION_ROBOT_FAIL_EXPORT.getCode(),
        DownloadTask.Type.CASE_OPERATION_ROBOT_STATISTICS_ALL_EXPORT.getCode(),
        DownloadTask.Type.CASE_OPERATION_ROBOT_STATISTICS_DATE_EXPORT.getCode()));
    if (UserUtils.likeBranchAdmin()) {
      criteria.andEqualTo("depId", loginUser.getDepId());
    }
    if (robotDownloadParam.getCreateTime() != null) {
      String[] dates = robotDownloadParam.getCreateTime().split(",");
      criteria.andBetween("createTime", new Date(Long.parseLong(dates[0])), new Date(Long.parseLong(dates[1])));
    }
    example.setOrderByClause("create_time desc");
    Page<?> page = super.setPage(robotDownloadParam);
    List<DownloadTask> tasks = downloadTaskService.selectByExample(example);
    List<DownloadListVO> taskVOS = new ArrayList<>();
    if (!CommonUtils.isEmpty(tasks)) {
      Map<Long, String> userMap = userService.getNames(loginUser.getOrgId());
      Map<Long, String> outBatchMap = outBatchService.getNames();
      for (DownloadTask task : tasks) {
        boolean isChange = false;
        if (task.getExpireTime().before(new Date())
          && (task.getStatus() == DownloadTask.Status.WAIT.getCode()
          || task.getStatus() == DownloadTask.Status.COMPLETE.getCode())) {
          task.setStatus(DownloadTask.Status.EXPIRE.getCode());
          isChange = true;
        }
        DownloadListVO vo = convertToVO(task, outBatchMap, userMap);
        if (isChange) {
          downloadTaskService.updateByPrimaryKey(task);
        }
        taskVOS.add(vo);
      }
    }

    return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int) page.getTotal(), taskVOS);
  }

  private DownloadListVO convertToVO(DownloadTask task,Map<Long,String> outBatchMap,Map<Long,String> userMap) {
    if (task == null) {
      return null;
    }
    CaseOperationDownloadEntity entity = JSONObject.parseObject(task.getData(), CaseOperationDownloadEntity.class);
    DownloadListVO taskVO = new DownloadListVO();
    taskVO.setId(task.getId());
    taskVO.setCreateTime(task.getCreateTime().getTime());
    taskVO.setExpireTime(task.getExpireTime().getTime());
    taskVO.setStatus(task.getStatus());
    taskVO.setType(task.getType());
    taskVO.setDeltId(entity.getOrgDeltIds());
    taskVO.setProgress(task.getProgress());
    taskVO.setStartTime(entity.getStartTime());
    taskVO.setEndTime(entity.getEndTime());
    if (!StringUtils.isBlank(entity.getOutBatchIds())) {
      String outBatchIds = entity.getOutBatchIds();
      StringBuilder sb = new StringBuilder();
      String[] outBatchIdsTmp = outBatchIds.split(",");
      int index = 1;
      for (String outBatchId : outBatchIdsTmp) {
        if (index < outBatchIdsTmp.length) {
          sb.append(outBatchMap.get(Long.valueOf(outBatchId))).append(",");
        } else if (index == outBatchIdsTmp.length) {
          sb.append(outBatchMap.get(Long.valueOf(outBatchId)));
        }
        index++;
      }
      taskVO.setOutBatchNo(sb.toString());
    }
    taskVO.setTemplateId(entity.getTemplateId());
    taskVO.setId(task.getId());
    taskVO.setFileSize(task.getFileSize());
    taskVO.setRelRecord(task.getRelRecord());
    taskVO.setCreateName(userMap.get(task.getCreateBy()));
    taskVO.setFailedReason(task.getFailedReason());
    taskVO.setDataNums(task.getDataNums());
    taskVO.setDownloadUrl(task.getDownloadUrl());
    return taskVO;
  }

  /**
   * 案件删除，级联待执行队列
   *
   * @param caseIds 案件ids
   */
  @Transactional(rollbackFor = Exception.class)
  public void delQueue(List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return;
    }
    Example example = new Example(RobotQueue.class);
    example.and().andIn("caseId", caseIds)
            .andEqualTo("status", RobotQueueEnums.Status.UNSENT.getCode());
    robotQueueMapper.deleteByExample(example);
  }
}
