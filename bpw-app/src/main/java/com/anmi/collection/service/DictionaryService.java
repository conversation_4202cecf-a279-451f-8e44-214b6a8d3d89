package com.anmi.collection.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.enums.DepTeamEnums;
import com.anmi.collection.common.enums.OrgSwitchEnums;
import com.anmi.collection.desensitization.MaskSetting;
import com.anmi.collection.entity.requset.sys.DesensitizationRuleCreate;
import com.anmi.collection.entity.requset.sys.DesensitizationRuleParam;
import com.anmi.collection.entity.requset.sys.TestI18nParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.sys.DesensitizationRuleVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.mapper.DictionaryMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.OrgSwitch;
import com.anmi.domain.sys.Dictionary;
import com.anmi.domain.user.Company;
import com.anmi.domain.user.DepTeam;
import com.anmi.domain.user.OrgDesensitizationRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by dongwang on 2018-12-05.
 */

@Service
public class DictionaryService extends BaseService<Dictionary> {

    @Autowired
    private DictionaryMapper dictionaryMapper;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private DuyanManager duyanManager;
    @Autowired
    private UserService userService;
    @Autowired
    private DepTeamService depTeamService;
    @Autowired private OrgDesensitizationRuleService orgDesensitizationRuleService;
    @Autowired private OrgSwitchService orgSwitchService;
    @Autowired private StringRedisTemplate stringRedisTemplate;

    public static final String PHONE_PUBLIC = "PUBLIC";
    public static final String PHONE_TEAM = "TEAM";

    public List<Dictionary> list(List<String> keys) {
        return dictionaryMapper.queryByKeys(keys);
    }

    public Map<String, DesensitizationRuleVO> desensitizationRules() {
        // 获取对应的org的脱敏规则
        Long orgId = getOrgId();

        List<OrgDesensitizationRule> orgDesensitizationRules = orgDesensitizationRuleService.getByOrgId(orgId);
        Map<String, DesensitizationRuleVO> rulesMap = orgDesensitizationRules.stream().collect(Collectors.toMap(OrgDesensitizationRule::getName, p -> BeanUtil.copyProperties(p, DesensitizationRuleVO.class)));
        return rulesMap;
    }

    public void createRules(String isOpen, DesensitizationRuleParam ruleParam) {
        UserSession loginUser = UserUtils.getTokenUser();
        Long orgId = loginUser.getOrgId();
        boolean isConnectDuyan = userService.isConnectDuyan(orgId);

        Map<String,String> orgSwitchMap = new HashMap<>();
        orgSwitchMap.put(OrgSwitchEnums.SwitchFieldName.DESENSITIZATIONRULE.getFieldName(), isOpen);
        orgSwitchService.saveOrUpdate(orgId,orgSwitchMap);

        if ("false".equals(isOpen)) {
            // 关闭开关，度言号码公开
            if (isConnectDuyan) {
                setDuyanMobilePublic(orgId);
            }
            // 关闭脱敏，直接把公司对应的脱敏设置信息删除
            stringRedisTemplate.delete(KeyCache.ORG_DESENSITIZATION_SETTING + orgId);
            return;
        }
        // 开关设置完成后，开始添加规则
        if (ruleParam == null) {
            return;
        }
        List<DesensitizationRuleCreate> creates = ruleParam.getCreates();
        List<Long> teamIdList=ruleParam.getTeamIdList();
        if (CommonUtils.isEmpty(creates)|| CollectionUtils.isEmpty(teamIdList)) {
            throw new ApiException("参数错误");
        }
        Map<String, String> rules = new HashMap<>();
        Boolean isHiddenMobile = false;
        for (DesensitizationRuleCreate create : creates) {
            if ("mobile".equals(create.getName())) {
                isHiddenMobile = true;
            }
            rules.put(create.getName(), JsonUtils.toJson(create));
        }
        //度言设置号码是否隐藏
        List<Long> oldTeamIdList=getDesensitizationTeamIds(loginUser.getOrgId());
        if(isConnectDuyan){
            if (isHiddenMobile) {
                setDuyanMobilePrivate(orgId,oldTeamIdList,teamIdList);
            }else{
                setDuyanMobilePublic(orgId);
            }
        }
        orgDesensitizationRuleService.deleteByOrgId(orgId);

        orgDesensitizationRuleService.saveOrgRules(orgId, creates);
        //开启脱敏规则小组
        if(!CollectionUtils.isEmpty(oldTeamIdList)){
            depTeamService.updateDesensitizationByOrgId(orgId,null,DepTeamEnums.Desensitization.NO);
        }
        Set<Long> teamIdSet = teamIdList.stream().collect(Collectors.toSet());
        depTeamService.updateDesensitizationByOrgId(orgId,teamIdSet,DepTeamEnums.Desensitization.YES);

        OrgSwitch orgSwitch = orgSwitchService.selectOrgSwitchByOrgId(orgId);
        MaskSetting maskSetting = new MaskSetting();
        maskSetting.setEnable(true);
        maskSetting.setDepTeamIds(new ArrayList<>(teamIdSet));
        maskSetting.setUserLevelSwitch(Objects.equals(orgSwitch.getDesensitizationUserLevel(), 1) ? true : false);
        maskSetting.setAdminLevelSwitch(Objects.equals(orgSwitch.getDesensitizationAdminLevel(), 1) ? true : false);
        maskSetting.setRules(creates);
        stringRedisTemplate.opsForValue().set(KeyCache.ORG_DESENSITIZATION_SETTING + orgId, JSON.toJSONString(maskSetting));
    }

    public List<Long> getDesensitizationTeamIds(Long orgId){
        List<DepTeam> depTeams = depTeamService.getDesensitizationTeamsByOrgId(orgId, DepTeamEnums.Desensitization.YES);
        List<Long> teamIdList=depTeams.stream().map(DepTeam::getId).collect(Collectors.toList());
        return teamIdList;
    }

    private void setDuyanMobilePublic(Long orgId) {
        Company company = companyService.selectByPrimaryKey(orgId);
        Long duyanId = company.getDuyanReferId();
        duyanManager.setPhoneProtection(duyanId, PHONE_PUBLIC);
    }

    private void setDuyanMobilePrivate(Long orgId, List<Long> oldTeamIdList, List<Long> newTeamIdList){
        Company company = companyService.selectByPrimaryKey(orgId);
        Long duyanId = company.getDuyanReferId();
        //设置按小组可见属性
        duyanManager.setPhoneProtection(duyanId, PHONE_TEAM);
        if(CollectionUtils.isEmpty(oldTeamIdList)){
            oldTeamIdList=depTeamService.selectTeamIdByOrgId(orgId);
        }
        //原不可见小组，本次未勾选，设置为可见
        oldTeamIdList.removeAll(newTeamIdList);
        setTeamDesensitization(duyanId,oldTeamIdList,false);
        //选择的组设置为不可见
        setTeamDesensitization(duyanId,newTeamIdList,true);
    }

    private void setTeamDesensitization(Long duyanOrgId,List<Long> teamIdList, Boolean isHiddenMobile) {
        if(CollectionUtils.isEmpty(teamIdList)){
            return;
        }
        List<DepTeam> teamList=depTeamService.selectByIdList(teamIdList,DepTeam.class);
        teamList=teamList.stream().filter(t->DepTeamEnums.Status.NORMAL.getCode()==t.getStatus()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(teamList)){
            return;
        }
        List<Long> parentIdList=teamList.stream().filter(d->d.getType() == DepTeamEnums.type.TEAM.getCode()
                && d.getUnderTeam() == DepTeamEnums.UnderTeam.NOT.getCode()).map(DepTeam::getParentId).collect(Collectors.toList());
        Map<Long,DepTeam> parentMap=new HashMap<>();
        if(!CollectionUtils.isEmpty(parentIdList)){
            List<DepTeam> parentList=depTeamService.selectByIdList(parentIdList,DepTeam.class);
            parentMap=parentList.stream().collect(Collectors.toMap(DepTeam::getId,p->p));
        }
        for(DepTeam depTeam:teamList){
            Long duyanParentId = null;
            DepTeam parent=parentMap.get(depTeam.getParentId());
            if (parent!=null) {
                // 一定是分公司下的小组
                duyanParentId = parent.getDuyanReferId();
            }
            duyanManager.updateTeam(duyanOrgId, depTeam.getDuyanReferId(), depTeam.getName(), duyanParentId, depTeam.getName(), isHiddenMobile);
        }
    }

    public void repairRules() {
        Example query = new Example(Company.class);
        query.selectProperties("id");
        query.createCriteria().andNotEqualTo("status", -1);
        List<Company> companies = companyService.selectByExample(query);
        if (CommonUtils.isEmpty(companies)) {
            return;
        }
        Set<Long> orgIds = companies.stream().map(Company::getId).collect(Collectors.toSet());
        List<OrgSwitch> orgSwitches = orgSwitchService.selectOrgSwitchByOrgIds(orgIds);
        for (Company company : companies) {
            Long orgId = company.getId();
            Optional<OrgSwitch> orgSwitchOptional = orgSwitches.stream().filter(orgSwitch -> ObjectUtil.equals(orgSwitch.getOrgId(), orgId)).findFirst();
            if (!orgSwitchOptional.isPresent()) {
                continue;
            }
            OrgSwitch orgSwitch = orgSwitchOptional.get();
            Boolean hiddenIdCard = Boolean.valueOf(orgSwitch.getHiddenIdCard());
            Boolean hiddenPhone = Boolean.valueOf(orgSwitch.getHiddenPhone());
            Boolean hiddenBankCode = Boolean.valueOf(orgSwitch.getHiddenBankCode());
            if (hiddenIdCard || hiddenPhone || hiddenBankCode) {
                Map<String,String> orgSwitchMap = new HashMap<>();
                orgSwitchMap.put(OrgSwitchEnums.SwitchFieldName.DESENSITIZATIONRULE.getFieldName(), OrgSwitchEnums.DesensitizationRule.OPEN.getCode());
                orgSwitchService.saveOrUpdate(orgId,orgSwitchMap);
                // 设置规则
                Map<String, String> rules = new HashMap<>();
                List<DesensitizationRuleCreate> ruleList = new ArrayList<>();
                if (hiddenIdCard) {
                    DesensitizationRuleCreate create = new DesensitizationRuleCreate();
                    create.setName("idCard");
                    create.setType(0);
                    create.setCount(4);
                    create.setScope(-1);
                    rules.put("idCard", JsonUtils.toJson(create));
                    ruleList.add(create);
                }
                if (hiddenBankCode) {
                    DesensitizationRuleCreate create = new DesensitizationRuleCreate();
                    create.setName("bank_code");
                    create.setType(0);
                    create.setCount(4);
                    create.setScope(-1);
                    rules.put("bank_code", JsonUtils.toJson(create));
                    ruleList.add(create);
                }
                if (hiddenPhone) {
                    DesensitizationRuleCreate create = new DesensitizationRuleCreate();
                    create.setName("mobile");
                    create.setType(0);
                    create.setCount(7);
                    create.setScope(0);
                    rules.put("mobile", JsonUtils.toJson(create));
                    ruleList.add(create);
                }
                orgDesensitizationRuleService.saveOrgRules(orgId,ruleList);
            }
        }
    }

    public String testI18n(TestI18nParam param) {
        if(param.getId()==1){
            //测试老版本无参数的构造函数的兼容性
            throw new ApiException("值不能为1");
        }
        if(param.getId()==2){
            //测试多语言无参数
            throw new ApiException("值#{testI18n.id.not2}2");
        }
        if(param.getId()==3){
            //测试多语言有参数
            throw new ApiException("值#{testI18n.id.not3}2",100086,10087);
        }
        if(param.getId()==4){
            //测试老版本有参数的构造函数的兼容性
            throw new ApiException("值不能为4{0}-{1}",100086,10087);
        }
        return param.getName();
    }
}
