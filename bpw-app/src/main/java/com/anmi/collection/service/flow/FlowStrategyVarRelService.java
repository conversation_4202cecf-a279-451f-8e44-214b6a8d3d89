package com.anmi.collection.service.flow;

import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.domain.flow.FlowStrategyVarRel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
@Slf4j
public class FlowStrategyVarRelService extends BaseService<FlowStrategyVarRel> {

    /**
     * 更新维护条件字段变量的使用记录
     *
     * @param orgId               公司id
     * @param flowId              流id
     * @param flowStrategyVarRels 流量策略var rels
     */
    public void updateRecords(Long orgId, Long flowId, List<FlowStrategyVarRel> flowStrategyVarRels) {
        AssertUtil.notNull(orgId, "orgId不能为空");
        AssertUtil.notNull(flowId, "flowId不能为空");
        Example example = new Example(FlowStrategyVarRel.class);
        Example.Criteria criteria = example.and();
        criteria.andEqualTo("orgId", orgId)
                .andEqualTo("flowId", flowId);
        deleteByExample(example);

        if (ObjectUtil.isNotEmpty(flowStrategyVarRels)) {
            insertBatch(flowStrategyVarRels);
        }
    }

    /**
     * 变量是否被使用
     *
     * @param orgId         组织id
     * @param strategyVarId 策略变量id
     * @return {@link Integer}
     */
    public Integer check(Long orgId, Long strategyVarId) {
        Example example = new Example(FlowStrategyVarRel.class);
        Example.Criteria criteria = example.and();
        criteria.andEqualTo("orgId", orgId)
                .andEqualTo("strategyVarId", strategyVarId);

        return selectCountByExample(example);
    }
}
