package com.anmi.collection.service;

import cn.duyan.thread.DuyanThreadExecutor;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.common.enums.CaseOperationEnums;
import com.anmi.collection.common.enums.OrgSwitchEnums;
import com.anmi.collection.deploy.DeployStrategyHolder;
import com.anmi.collection.dto.AiAnalysisDTO;
import com.anmi.collection.dto.AiAnalysisResultDTO;
import com.anmi.collection.entity.requset.cases.AiAnalysisParam;
import com.anmi.collection.entity.requset.cases.AiAnalysisQuery;
import com.anmi.collection.entity.requset.cases.CaseMultiQuery;
import com.anmi.collection.entity.requset.cases.CaseOperationParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.AiAnalysisVO;
import com.anmi.collection.entity.response.cases.CaseOperationVO;
import com.anmi.collection.entity.response.sys.admin.AiPromptVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.init.CacheLoader;
import com.anmi.collection.manager.AiAnalysisManager;
import com.anmi.collection.mapper.AiAnalysisMapper;
import com.anmi.collection.service.admin.AiPromptService;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.admin.AiPrompt;
import com.anmi.domain.base.DictionaryEntity;
import com.anmi.domain.cases.*;
import com.anmi.domain.sys.CustomField;
import com.anmi.domain.user.Company;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AiAnalysisService extends BaseService<AiAnalysis> {
    @Resource
    private AiAnalysisMapper aiAnalysisMapper;
    @Resource
    private AiAnalysisManager aiAnalysisManager;
    @Resource
    private OrgSwitchService orgSwitchService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private AiPromptService aiPromptService;
    @Resource
    private CaseService caseService;
    @Resource
    private RepaymentService repaymentService;
    @Resource
    private CaseOperationService caseOperationService;
    @Resource
    private CompanyService companyService;
    @Resource
    private CustomFieldService customFieldService;
    @Resource
    private DeployStrategyHolder deployStrategyHolder;


    /** 账号登录前缀 */
    public static final String AI_ANALYSIS = "ai_analysis_";
    /** 分隔符号 */
    public static final String SEPARATOR_SYMBOL = "_";
    /** 案件分析最大次数 */
    public static final int MAX_ANALYSIS_NUM = 5;

    /**
     * 大模型分析
     */
    private static final String AI_ANALYSIS_URL_SUFFIX = "/api/llm_ernie";


    private final DuyanThreadExecutor threadExecutor = new DuyanThreadExecutor("ai-analysis-pool");


    /**
     * 查询案件分析结果
     *
     * @param query 查询条件
     * @return {@link PageOutput}<{@link AiAnalysisVO}>
     */
    public PageOutput<AiAnalysisVO> queryAnalysisResult(AiAnalysisQuery query) {
        AssertUtil.notNull(query.getCaseId(), "案件id不可为空");
        AssertUtil.notNull(query.getType(), "案件分析类型不可为空");
        PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
        Page page = PageUtils.setPage(pageParam);
        Example example = new Example(AiAnalysis.class);
        example.and().andEqualTo("caseId", query.getCaseId())
              .andEqualTo("type", query.getType());
        example.orderBy("id").desc();
        List<AiAnalysis> aiAnalysisList = aiAnalysisMapper.selectByExample(example);
        List<AiAnalysisVO> vos = BeanUtil.copyToList(aiAnalysisList, AiAnalysisVO.class);
        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int) page.getTotal(), vos);
    }

    /**
     * 分析案件信息
     *
     * @param param 分析信息
     */
    public void caseAnalysis(AiAnalysisParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        if (!companyService.isOpenAiAnalysis(userSession.getOrgId())) {
            throw new ApiException("ai分析开关开关未开启,请联系管理员");
        }
        if (stringRedisTemplate.hasKey(AI_ANALYSIS + param.getCaseId() + SEPARATOR_SYMBOL + param.getType())) {
            String numStr = stringRedisTemplate.opsForValue().get(AI_ANALYSIS + param.getCaseId() + SEPARATOR_SYMBOL + param.getType());
            int num = StringUtils.isBlank(numStr) ? 0 : Integer.parseInt(numStr);
            if (num >= MAX_ANALYSIS_NUM) {
                throw new ApiException("每日每个案件分析最大次数为5次");
            }
        }
        // 查询上个案件是否在分析中
        List<AiAnalysis> aiAnalyses = queryNotAnalyzedList(param.getCaseId(), param.getType());
        if (!CollectionUtils.isEmpty(aiAnalyses)) {
            throw new ApiException("上个分析还未完成,请稍等");
        }

        OrgSwitch orgSwitch = orgSwitchService.selectOrgSwitchByOrgId(userSession.getOrgId());
        String caseContent = integrateContent(param.getCaseId(), orgSwitch);
        AiAnalysis aiAnalysis = new AiAnalysis();
        // 获取prompt
        AiPrompt aiPrompt = aiPromptService.queryAiPrompt(userSession.getOrgId());
        String prompt = aiPrompt.getSummary();
        aiAnalysis.setType(param.getType());
        switch (param.getType()) {
            case 1:
                prompt = prompt + aiPrompt.getCaseAnalysis();
                aiAnalysis.setAnalysisContent(prompt + caseContent);
                break;
            case 2:
                prompt = prompt + aiPrompt.getOperationPlan();
                aiAnalysis.setAnalysisContent(prompt + caseContent);
                break;
            case 3:
                prompt = prompt + aiPrompt.getSpeechSuggest();
                aiAnalysis.setAnalysisContent(prompt + caseContent);
                break;
            default:
                break;
        }
        // 存入数据
        String requestId = UUID.randomUUID().toString();
        aiAnalysis.setCaseId(param.getCaseId());
        aiAnalysis.setRequestId(requestId);
        aiAnalysis.setReceiveFlag(0);
        aiAnalysis.setAnalysisContent(caseContent + prompt);
        aiAnalysis.setCreateTime(new Date());
        aiAnalysis.setUpdateTime(new Date());
        aiAnalysisMapper.insertSelective(aiAnalysis);

        String finalPrompt = prompt;
        threadExecutor.submit(() -> {
            AiAnalysisDTO dto = new AiAnalysisDTO();
            dto.setContent(finalPrompt + caseContent);
            dto.setRequestId(requestId);
            dto.setUid(String.valueOf(param.getCaseId()));
            dto.setRequestTime(System.currentTimeMillis());
            Map<String, Object> map = new HashMap<>(2);
            map.put("caseId", param.getCaseId());
            dto.setExtraInfo(map);

            AiAnalysis update = new AiAnalysis();
            update.setUpdateTime(new Date());
            try {
                ResultMessage<AiAnalysisResultDTO> result = aiAnalysisManager.analysisContent(systemConfig.getAiUrl() + AI_ANALYSIS_URL_SUFFIX, dto);
                if (Objects.nonNull(result) && Objects.equals(result.getStatus(), 0)) {
                    AiAnalysisResultDTO data = result.getData();
                    // 更新案件信息
                    update.setReceiveFlag(1);
                    update.setAnalysisResult(data.getSummary_result());
                    if (stringRedisTemplate.hasKey(AI_ANALYSIS + param.getCaseId() + SEPARATOR_SYMBOL + param.getType())) {
                        stringRedisTemplate.opsForValue().increment(AI_ANALYSIS + param.getCaseId() + SEPARATOR_SYMBOL + param.getType(), 1);
                    } else {
                        long between = DateUtil.between(new Date(), DateUtils.getEndTimeOfDay(new Date()), DateUnit.MS);
                        stringRedisTemplate.opsForValue().set(AI_ANALYSIS + param.getCaseId() + SEPARATOR_SYMBOL + param.getType(), String.valueOf(1), between, TimeUnit.MILLISECONDS);
                    }
                } else {
                    String errorMsg = result.getMessage();
                    log.error("调用大模型接口出现异常, {}", errorMsg);
                    update.setReceiveFlag(-1);
                }
                updateResultByRequestId(dto.getRequestId(), update);
            } catch (Exception e) {
                update.setReceiveFlag(-1);
                updateResultByRequestId(dto.getRequestId(), update);
                log.error("远程调用大模型接口出现异常==>{}", e.getMessage());
            }
        });
    }

    /**
     * 整合案件信息
     *
     * @param caseId    案件id
     * @param orgSwitch 开关
     * @return {@link String}
     */
    private String integrateContent(Long caseId, OrgSwitch orgSwitch) {
        StringBuilder sb = new StringBuilder();
        // 查询案件
        CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
        caseMultiQuery.setCaseIds(Collections.singletonList(caseId));
        List<CaseQueryResult> list = caseService.queryResultForMulti(caseMultiQuery);
        if (CollectionUtils.isEmpty(list)) {
            throw new ApiException("案件不存在");
        }
        CaseQueryResult caseQueryResult = list.get(0);
        Map<String, String> fieldJson = caseQueryResult.getFieldJson();
        sb.append("债务人姓名,逾期天数,委案金额,身份证号");

        // 新增自定义字段名称
        Company company = companyService.selectByPrimaryKey(caseQueryResult.getOrgId());
        Map<String, DictionaryEntity> commonFields = CacheLoader.getCommonFields(company.getLanguage());
        if (!CollectionUtil.isEmpty(fieldJson)) {
            // 移除是否同步地址字段
            fieldJson.remove("is_sync_address");
            List<String> keyList = Lists.newArrayList(fieldJson.keySet());
            Map<String, CustomField> keyMap = customFieldService.selectByValues(caseQueryResult.getOrgId(), keyList);
            for (Map.Entry<String, String> entry : fieldJson.entrySet()) {
                String key = entry.getKey();
                if (commonFields.containsKey(key)) {
                    DictionaryEntity dictionaryEntity = commonFields.get(key);
                    sb.append(",").append(dictionaryEntity.getName());
                } else {
                    if (keyMap.containsKey(key)) {
                        sb.append(",").append(keyMap.get(key).getName());
                    }
                }
            }
        }
        sb.append("\n");

        sb.append(caseQueryResult.getName()).append(",");
        // 逾期天数根据开关是否开启设置天数
        boolean b = !ObjectUtil.isNull(orgSwitch) && Boolean.parseBoolean(orgSwitch.getAutoUpdateOverdueDays());
        if (!b) {
            sb.append(caseQueryResult.getOverdueDays()).append(",");
        } else {
            if (ObjectUtil.isNull(caseQueryResult.getOverdueDate())) {
                sb.append(0).append(",");
            } else {
                Long days = DateUtils.countDays(caseQueryResult.getOverdueDate(), new Date());
                if (days < 0) {
                    days = 0L;
                }
                sb.append(days).append(",");
            }
        }

        sb.append(BigDecimal.valueOf(caseQueryResult.getAmount()).divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP)).append(",")
                .append(caseQueryResult.getIdCard());

        if (!CollectionUtil.isEmpty(fieldJson)) {
            for (Map.Entry<String, String> entry : fieldJson.entrySet()) {
                String value = entry.getValue();
                if (commonFields.containsKey(entry.getKey())) {
                    DictionaryEntity dictionaryEntity = commonFields.get(entry.getKey());
                    if (StringUtils.equals(dictionaryEntity.getType(), "Money") && ObjectUtil.isNotNull(value)) {
                        value = BigDecimal.valueOf(Long.parseLong(value)).divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP).toString();
                    }
                }
                sb.append(",").append(value);
            }
        }
        sb.append("\n");

        // 查询案件催收信息
        Date endDate = new Date();
        Date startDate = DateUtil.beginOfDay(DateUtils.addDays(endDate, -30));
        CaseOperationParam caseOperationParam = new CaseOperationParam();
        caseOperationParam.setCaseId(caseId);
        caseOperationParam.setStartTime(startDate);
        caseOperationParam.setEndTime(endDate);
        List<CaseOperationVO> caseOperationList = new ArrayList<>();
        if (systemConfig.getESSwitch() && caseId == null) {
            PageOutput<CaseOperationVO> operationByPage = caseOperationService.getByPageFromElasticsearch(caseOperationParam);
            caseOperationList = operationByPage.getList();
        } else {
            PageOutput<CaseOperationVO> operationByPage = caseOperationService.getByPage(caseOperationParam);
            caseOperationList = operationByPage.getList();
        }
        if (!CollectionUtils.isEmpty(caseOperationList)) {
            sb.append("催收时间,联系人姓名,联系人电话,和债务人关系,催收结果,电话结果,催收进程,催收备注,呼叫形式,通话时长,承诺还款时间,承诺还款金额")
                .append("\n");
            for (CaseOperationVO caseOperationVO : caseOperationList) {
                sb.append(DateUtils.timeStampToStr(caseOperationVO.getCreateTime())).append(",")
                        .append(caseOperationVO.getConName()).append(",")
                        .append(caseOperationVO.getConMobile()).append(",")
                        .append(caseOperationVO.getRelationType()).append(",")
                        .append(CaseOperationEnums.ActionType.getMessageByCode(caseOperationVO.getActionType())).append(",")
                        .append(CaseOperationEnums.CallType.getMessageByCode(caseOperationVO.getCallType())).append(",")
                        .append(CaseOperationEnums.State.getMessageByCode(caseOperationVO.getOperationState())).append(",")
                        .append(caseOperationVO.getDesc()).append(",")
                        .append(ObjectUtil.equals(caseOperationVO.getCallStyle(), 0) ? "呼入":"呼出").append(",")
                        .append(caseOperationVO.getCallDurtion()).append(",")
                        .append(ObjectUtil.isNull(caseOperationVO.getPtpTime()) ? null:DateUtils.timeStampToStr(caseOperationVO.getPtpTime())).append(",")
                        .append(ObjectUtil.isNull(caseOperationVO.getPtpAmount()) ?
                                BigDecimal.ZERO:BigDecimal.valueOf(caseOperationVO.getPtpAmount()).divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP))
                        .append("\n");
            }
        }

        // 查询还款记录
        Map<String, Object> map = new HashMap<>();
        map.put("caseId", caseId);
        map.put("status", 0);
        map.put("applyStatus", Lists.newArrayList(1,2));
        List<RepaymentQuery> repaymentList = repaymentService.queryResult(map);
        if (!CollectionUtils.isEmpty(repaymentList)) {
            sb.append("还款日期,还款金额").append("\n");
            for (RepaymentQuery repaymentQuery : repaymentList) {
                sb.append(DateUtils.formatDate(repaymentQuery.getRepaymentTime()))
                        .append(",")
                        .append(BigDecimal.valueOf(repaymentQuery.getRepaymentAmount()).divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP))
                        .append("\n");
            }
        }
        return sb.toString();
    }

    /**
     * 根据请求id更新分析内容
     *
     * @param requestId  请求id
     * @param aiAnalysis 更新内容
     */
    private void updateResultByRequestId(String requestId, AiAnalysis aiAnalysis) {
        Example example = new Example(AiAnalysis.class);
        example.and().andEqualTo("requestId", requestId);
        aiAnalysisMapper.updateByExampleSelective(aiAnalysis, example);
    }

    /**
     * 查询未分析完成的记录
     *
     * @param caseId 案件id
     * @param type   案件分析内容 1:案件分析 2:跟进计划 3:话术推荐
     * @return {@link List}<{@link AiAnalysis}>
     */
    private List<AiAnalysis> queryNotAnalyzedList(Long caseId, Integer type) {
        Example example = new Example(AiAnalysis.class);
        example.and().andEqualTo("caseId", caseId)
                .andEqualTo("type", type)
                .andEqualTo("receiveFlag", 0);
        return aiAnalysisMapper.selectByExample(example);
    }
}
