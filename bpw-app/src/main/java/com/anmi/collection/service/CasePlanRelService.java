package com.anmi.collection.service;

import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.enums.CasePlanEnums;
import com.anmi.collection.common.enums.CasePlanRelEnums;
import com.anmi.collection.dto.CaseContact;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.mapper.CasePlanRelMapper;
import com.anmi.domain.cases.*;
import com.anmi.domain.site.SiteVarsMapping;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CasePlanRelService extends BaseService<CasePlanRel> {

    @Resource private DuyanManager duyanManager;
    @Resource private CasePlanRelMapper casePlanRelMapper;
    @Autowired private CaseMapper caseMapper;
    @Autowired private SiteVarsMappingService siteVarsMappingService;
    @Autowired private CasePlanService casePlanService;

    public void saveCasePlanRelBatch(List<Long> caseIds, Long planId, UserSession userSession, Integer type) {
        List<CasePlanRel> casePlanRels = Lists.newArrayList();
        for (Long caseId : caseIds) {
            CasePlanRel casePlanRel = buildCasePlanRel(caseId, planId, userSession, type);
            casePlanRels.add(casePlanRel);
        }
        super.insertBatch(casePlanRels);
    }

    private CasePlanRel buildCasePlanRel(Long caseId, Long planId, UserSession userSession, Integer type) {
        CasePlanRel casePlanRel = new CasePlanRel();
        casePlanRel.setCaseId(caseId);
        casePlanRel.setPlanId(planId);
        casePlanRel.setOrgId(userSession.getOrgId());
        casePlanRel.setDepId(userSession.getDepId());
        casePlanRel.setTeamId(userSession.getTeamId());
        casePlanRel.setType(type);
        casePlanRel.setUserId(userSession.getId());
        casePlanRel.setCreateBy(userSession.getId());
        casePlanRel.setUpdateBy(userSession.getId());
        casePlanRel.setCreateTime(new Date());
        casePlanRel.setUpdateTime(new Date());
        casePlanRel.setStatus(CasePlanRelEnums.Status.NORMAL.getCode());
        return casePlanRel;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void removeCaseItem(Long relId, Long orgId, Long campaignId, String mobile, Long updateBy) {
        duyanManager.removePlanItem(orgId, campaignId, mobile, updateBy);
        CasePlanRel casePlanRel = new CasePlanRel();
        casePlanRel.setId(relId);
        casePlanRel.setStatus(CasePlanRelEnums.Status.REMOVE.getCode());
        super.updateByPrimaryKeySelective(casePlanRel);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void addCaseItem(List<CaseDuyanPlanRel> relList, Long orgId, Long duyanPlanId, List<CaseContact> list, CasePlan casePlan) {
        List<Long> caseIds = relList.stream().map(CaseDuyanPlanRel::getCaseId).collect(Collectors.toList());
        List<Case> cases = caseMapper.selectFieldJson(caseIds);
        // 新机器人计划的body
        List<Map<String,Object>> contents = new ArrayList<>();
        if (ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.NEWROBOT.getCode())){
            List<SiteVarsMapping> siteVarsMappings = siteVarsMappingService.getSiteVarsMappings(casePlan.getOrgId(),casePlan.getSiteId());
            List<SiteVarsMapping> duyanVarsMappings = siteVarsMappings.stream().filter(siteVarsMapping -> siteVarsMappings.stream()
                            .anyMatch(duyanSiteVar ->  ObjectUtil.equals(duyanSiteVar, siteVarsMapping.getDuyanVar())))
                    .collect(Collectors.toList());
            for (int i = 0; i < list.size(); i++) {
                CaseContact caseContact = list.get(i);
                Long caseId = caseContact.getCaseId();
                Optional<Case> caseOptional = cases.stream().filter(c -> ObjectUtil.equals(caseId, c.getId())).findFirst();
                Case caseDb = caseOptional.get();
                Map<String, String> fieldJsonMap = caseDb.getFieldJson();

                casePlanService.addContent(caseContact, orgId, fieldJsonMap, contents, duyanVarsMappings, casePlan);
            }
        }
        duyanManager.batchAdd(orgId, duyanPlanId, list, casePlan.getType(), casePlan, contents);
        //更新状态
        List<Long> relIdList = relList.stream().map(CaseDuyanPlanRel::getId).collect(Collectors.toList());
        CasePlanRel casePlanRel = new CasePlanRel();
        casePlanRel.setStatus(CasePlanRelEnums.Status.NORMAL.getCode());
        Example example = new Example(CasePlanRel.class);
        example.createCriteria().andIn("id", relIdList);
        super.updateByExampleSelective(casePlanRel, example);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void removeCaseItemBatch(List<Long> relIds, Long orgId, Long duyanPlanId, List<CasePlanRelContact> casePlanRelContacts, Long updateBy) {
        List<String> mobiles = casePlanRelContacts.stream().map(CasePlanRelContact::getPhoneNum).collect(Collectors.toList());
        duyanManager.removePlanItemBatch(orgId, duyanPlanId, mobiles, updateBy);
        casePlanRelMapper.updateRemovedStatues(relIds);
    }
}
