package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.entity.requset.query.LoginLogQuery;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.sys.user.LoginLogVO;
import com.anmi.collection.utils.AuthBeanUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.sys.LoginLog;
import com.github.pagehelper.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class LoginLogService extends BaseService<LoginLog> {
    @Resource
    private UserService userService;

    public PageOutput<LoginLogVO> getList(LoginLogQuery query) {
        UserSession userSession = UserUtils.getTokenUser();
        Example example = new Example(LoginLog.class);
        example.and().andEqualTo("orgId", userSession.getOrgId());
        if (StringUtils.isNotBlank(query.getUserNo())) {
            example.and().andLike("userNo", "%" + query.getUserNo() + "%");
        }
        if (Objects.nonNull(query.getUserId())) {
            example.and().andEqualTo("userId", query.getUserId());
        }
        if (StringUtils.isNotBlank(query.getLogTime())) {
            String startTime = DateUtils.convertDate(query.getLogTime().split(",")[0]);
            String endTime = DateUtils.convertDate(query.getLogTime().split(",")[1]);
            example.and().andGreaterThanOrEqualTo("createTime", startTime)
                    .andLessThanOrEqualTo("createTime", endTime);
        }
        example.orderBy("id").desc();
        PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<LoginLog> list = this.selectByExample(example);
        List<Long> userIds = list.parallelStream().map(LoginLog::getUserId).distinct().collect(Collectors.toList());
        Map<Long, String> userMap = userService.getUserMap(userIds);
        List<LoginLogVO> loginLogVOList = new ArrayList<>();
        list.forEach(loginLog -> {
            StringBuilder content = new StringBuilder();
            content.append("账号【").append(loginLog.getUserNo()).append("】").append(userMap.get(loginLog.getUserId()))
                    .append("，登录系统时间：").append(DateUtils.formatDateFull(loginLog.getCreateTime())).append("；");
            if (Objects.nonNull(loginLog.getLogoutTime())) {
                content.append("退出系统时间：").append(DateUtils.formatDateFull(loginLog.getLogoutTime())).append("；");
            }
            content.append("IP：").append(loginLog.getIpAddr());
            LoginLogVO loginLogVO = AuthBeanUtils.copy(loginLog, LoginLogVO.class);
            loginLogVO.setContent(content.toString());
            loginLogVO.setUserName(userMap.get(loginLog.getUserId()));
            loginLogVOList.add(loginLogVO);
        });
        PageOutput<LoginLogVO> pageOutput =
                new PageOutput(
                        page.getPageNum(),
                        page.getPageSize(),
                        page != null ? (int) page.getTotal() : loginLogVOList.size(),
                        loginLogVOList);
        return pageOutput;

    }


}
