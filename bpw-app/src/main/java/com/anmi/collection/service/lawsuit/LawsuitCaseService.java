package com.anmi.collection.service.lawsuit;

import com.anmi.collection.entity.response.lawsuit.LawsuitCaseVO;
import com.anmi.collection.mapper.lawsuit.LawsuitCaseMapper;
import com.anmi.collection.utils.AuthBeanUtils;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.lawsuit.Lawsuit;
import com.anmi.domain.lawsuit.LawsuitCase;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/22 15:39
 */
@Service
public class LawsuitCaseService {
    @Resource
    private LawsuitCaseMapper lawsuitCaseMapper;


    /**
     * 处理催收案件和诉讼关联，诉讼申请的一对一，案件加入诉讼非公债的案件也是一对一
     * @param lawsuits
     */
    @Transactional(rollbackFor = Exception.class)
    public void addLawsuitCase(List<Lawsuit> lawsuits) {
        if (CollectionUtils.isEmpty(lawsuits)) {
            return;
        }
        List<LawsuitCase> lawsuitCaseList = new ArrayList<>();
        lawsuits.forEach(lawsuit -> {
            LawsuitCase lawsuitCase = AuthBeanUtils.copy(lawsuit, LawsuitCase.class);
            lawsuitCase.setLawsuitId(lawsuit.getId());
            lawsuitCase.setCreateTime(new Date());
            lawsuitCase.setUpdateTime(new Date());
            lawsuitCaseList.add(lawsuitCase);
        });
        lawsuitCaseMapper.insertList(lawsuitCaseList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addLawsuitCase(Map<Lawsuit, List<Case>> lawsuitListMap) {
        if (lawsuitListMap.size() == 0) {
            return;
        }
        List<LawsuitCase> lawsuitCaseList = new ArrayList<>();
        lawsuitListMap.forEach((lawsuit, caseList) -> {
            caseList.forEach(caseInfo -> {
                // 从催收案件冗余相关字段数据
                LawsuitCase lawsuitCase = AuthBeanUtils.copy(caseInfo, LawsuitCase.class);
                lawsuitCase.setCaseId(caseInfo.getId());
                lawsuitCase.setLawsuitId(lawsuit.getId());
                lawsuitCase.setOutSerialNo(caseInfo.getOutSerialTemp());
                Long amount = caseInfo.getAmount() == null ? 0 : caseInfo.getAmount();
                lawsuitCase.setAmount(new BigDecimal(amount.toString()).divide(new BigDecimal("1000"), 2, RoundingMode.DOWN));
                lawsuitCase.setCreateTime(new Date());
                lawsuitCase.setUpdateTime(new Date());
                lawsuitCaseList.add(lawsuitCase);
            });
        });
        if (!CollectionUtils.isEmpty(lawsuitCaseList)) {
            lawsuitCaseMapper.insertList(lawsuitCaseList);
        }
    }

    public List<LawsuitCaseVO> getList(Long lawsuitId) {
        Example example = new Example(LawsuitCase.class);
        example.and().andEqualTo("lawsuitId", lawsuitId);
        List<LawsuitCase> lawsuitCaseList = lawsuitCaseMapper.selectByExample(example);
        return toLawsuitCaseVOList(lawsuitCaseList);
    }

    public Map<Long, String> getLawsuitLinkOutSerialNo(List<Long> lawsuitIds) {
        if (CollectionUtils.isEmpty(lawsuitIds)) {
            return new HashMap<>();
        }
        Example example = new Example(LawsuitCase.class);
        example.and().andIn("lawsuitId", lawsuitIds);
        List<LawsuitCase> lawsuitCaseList = lawsuitCaseMapper.selectByExample(example);
        Map<Long, List<LawsuitCase>> listMap = lawsuitCaseList.stream().collect(Collectors.groupingBy(LawsuitCase::getLawsuitId));
        Map<Long, String> result = new HashMap<>();
        listMap.forEach((lawsuitId, list)->{
            String outSerialNos = list.stream().map(LawsuitCase::getOutSerialNo).collect(Collectors.joining(","));
            result.put(lawsuitId, outSerialNos);
        });
        return result;
    }

    public List<Long> getLawsuitIdsByCaseId(Long caseId) {
        Example example = new Example(LawsuitCase.class);
        example.and().andEqualTo("caseId", caseId);
        example.selectProperties("lawsuitId");
        List<LawsuitCase> lawsuitCaseList = lawsuitCaseMapper.selectByExample(example);
        return lawsuitCaseList.parallelStream().map(LawsuitCase::getLawsuitId).collect(Collectors.toList());
    }




    List<LawsuitCaseVO> toLawsuitCaseVOList(List<LawsuitCase> lawsuitCaseList) {
        List<LawsuitCaseVO> lawsuitCaseVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(lawsuitCaseList)) {
            return lawsuitCaseVOList;
        }
        lawsuitCaseList.forEach(lawsuitCase -> {
            LawsuitCaseVO lawsuitCaseVO = AuthBeanUtils.copy(lawsuitCase, LawsuitCaseVO.class);
            lawsuitCaseVOList.add(lawsuitCaseVO);
        });
        return lawsuitCaseVOList;
    }

}
