package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.domain.user.UserView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class UserViewService extends BaseService<UserView> {

  public UserView getByUserIdAndWebTagAndMenuType(Long userId, Integer webTag, Integer menuType) {
    UserView userView = new UserView();
    userView.setUserId(userId).setWebTag(webTag).setMenuType(menuType);
    return selectOne(userView);
  }
}
