package com.anmi.collection.service.mediate;

import cn.duyan.dto.eqianbao.OrderInfoDto;
import cn.duyan.dto.eqianbao.UploadFileInfoDto;
import cn.duyan.signature.provider.EqianbaoSignatureProvider;
import cn.duyan.thread.DuyanThreadExecutor;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.dto.CaseDTO;
import com.anmi.collection.dto.mediate.*;
import com.anmi.collection.encrypt.EncryptProperties;
import com.anmi.collection.encrypt.EncryptService;
import com.anmi.collection.entity.requset.mediate.SignMediateParam;
import com.anmi.collection.entity.requset.mediate.SignerSealMatchParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.mediate.MediateSignVO;
import com.anmi.collection.entity.response.mediate.RemoveMediateFailVO;
import com.anmi.collection.entity.response.mediate.StartSignatureFailVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.*;
import com.anmi.collection.service.CaseService;
import com.anmi.collection.service.CompanyService;
import com.anmi.collection.service.DepTeamService;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.PdfUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.letter.LetterMediationSeal;
import com.anmi.domain.letter.LetterSigner;
import com.anmi.domain.mediate.*;
import com.anmi.domain.user.Company;
import com.anmi.domain.user.DepTeam;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.anmi.collection.utils.DateUtils.convertDate;

@Service
@Slf4j
public class LetterMediateService extends BaseService<LetterMediate> {
    @Resource
    private LetterMediateMapper letterMediateMapper;
    @Resource
    private LetterSignerMapper letterSignerMapper;
    @Resource
    private CaseService caseService;
    @Resource
    private LetterMediationSealMapper letterMediationSealMapper;
    @Resource
    private AuthOrgInfoMapper authOrgInfoMapper;
    @Resource
    private MediateSignerService mediateSignerService;
    @Resource
    private DepTeamService depTeamService;
    @Resource
    private EqianbaoSignatureProvider eqianbaoSignatureProvider;
    @Resource
    private CompanyService companyService;
    @Resource
    private EncryptProperties encryptProperties;
    @Resource
    private EncryptService encryptService;

    private DuyanThreadExecutor threadExecutor = new DuyanThreadExecutor("mediate-pool");


    public void preview(Long letterId, HttpServletResponse response) throws Exception {
        LetterMediate letterMediate = letterMediateMapper.selectByPrimaryKey(letterId);
        // 查询签名区域
        Example example = new Example(LetterSigner.class);
        example.and().andEqualTo("letterTemplateId", letterMediate.getTemplateId());
        List<LetterSigner> signerList = letterSignerMapper.selectByExample(example);
        Map<String, LetterSigner> orgSignerMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(signerList)) {
            // 过滤类型为机构的签署方
            orgSignerMap = signerList.stream().filter(item -> item.getType() == 0)
                    .collect(Collectors.toMap(LetterSigner::getName ,f -> f));
        }
        // 案件内容
        Long caseId = letterMediate.getCaseId();
        if (caseId == null) {
            throw new ApiException("案件参数不能为空！");
        }
        CaseDTO aCase = caseService.getCaseForLetter(caseId);
        // 获取当前所属机构的印章
        UserSession tokenUser = UserUtils.getTokenUser();
        Long orgId = tokenUser.getOrgId();
        Company company = companyService.selectByPrimaryKey(orgId);
        Integer type1 = null;
        Integer type2 = null;
        LetterSigner letterSigner = new LetterSigner();
        LetterSigner letterSigner1 = new LetterSigner();
        if (Objects.equals(company.getIsBank(), CompanyEnums.BankAmc.YES.getCode())) {
            // 主体机构
            letterSigner = orgSignerMap.get(SignerEnums.Type.MEDIATIONORG_BANK.getMessage());
            type1 = letterSigner != null ? letterSigner.getSignType() : null;
            // 调解机构（委外机构）
            letterSigner1 = orgSignerMap.get(SignerEnums.Type.ENTRUSTEDAGENCY_BANK.getMessage());
            type2 = letterSigner1!=null ? letterSigner1.getSignType() : null;
        } else {
            // 调解机构类型
            letterSigner = orgSignerMap.get(SignerEnums.Type.MEDIATIONORG.getMessage());
            type1 = letterSigner != null ? letterSigner.getSignType() : null;
            // 委托机构类型
            letterSigner1 = orgSignerMap.get(SignerEnums.Type.ENTRUSTEDAGENCY.getMessage());
            type2 = letterSigner1!=null ? letterSigner1.getSignType() : null;
        }
        //获取对应案件授权机构的印章
        Long depId = aCase.getDepId();
        List<SignerSealMatchParam> signerSealMatchList = new ArrayList<>();

        // 查询分公司（委外机构）签章信息
        if (type2 != null) {
            // 获取签章类别
            List<LetterMediationSeal> depSeals = letterMediationSealMapper.selectSealDiff(0, orgId, depId, SealTypeEnums.typeMap.get(type2));
            LetterMediationSeal depSeal = null;
            if (!CollectionUtils.isEmpty(depSeals)) {
                // 默认第一条数据
                depSeal = depSeals.get(0);
                // 设置签署区和签章进行匹配
                SignerSealMatchParam signerSealMatchParam = new SignerSealMatchParam();
                // 签署区
                BeanUtils.copyProperties(letterSigner1, signerSealMatchParam);
                // 印章信息
                BeanUtils.copyProperties(depSeal, signerSealMatchParam);
                signerSealMatchList.add(signerSealMatchParam);
            }
        }

        if (type1 != null) {
            // 查询当前机构 调解机构
            List<LetterMediationSeal> orgSeals = letterMediationSealMapper.selectSealDiff(1, orgId, null, SealTypeEnums.typeMap.get(type1));
            LetterMediationSeal orgSeal = null;
            if (!CollectionUtils.isEmpty(orgSeals)) {
                // 默认第一条数据
                orgSeal = orgSeals.get(0);
                // 设置签署区和签章进行匹配
                SignerSealMatchParam signerSealMatchParam = new SignerSealMatchParam();
                // 签署区
                BeanUtils.copyProperties(letterSigner, signerSealMatchParam);
                // 印章信息
                BeanUtils.copyProperties(orgSeal, signerSealMatchParam);
                signerSealMatchList.add(signerSealMatchParam);
            }
        }
        if (StringUtils.equals(systemConfig.getChannelCode(), FileStorageEnums.Type.LOCAL.getCode())) {
            String sealPathPrefix = systemConfig.getMediationFilePath();
            int index = sealPathPrefix.indexOf("/ng/download");
            if (index != -1) {
                sealPathPrefix = sealPathPrefix.substring(0,index);
            }
            String finalSealPathPrefix = sealPathPrefix;
            signerSealMatchList.forEach(item -> {
                item.setImage(finalSealPathPrefix + item.getImage());
            });
        }
        InputStream pdf1 = null;
        try {
            String content = letterMediate.getTemplateContent();
            String fileName = aCase.getName() + "-" + letterMediate.getName() + ".pdf";
            pdf1 = PdfUtils.createPDF1(content, fileName, signerSealMatchList);
            response.setContentType("application/pdf");
            FileCopyUtils.copy(pdf1, response.getOutputStream());
        } catch (Exception e) {
            log.error("转为pdf流时出现异常" + e.getMessage());
        } finally {
            if (pdf1 != null) {
                pdf1.close();
            }
        }
    }

    /**
     * 上传文件并将fileId、状态保存
     *
     * @param letterId 函件id
     */
    public void uploadFile(Long letterId) throws Exception{
        // 查询对应文件
        LetterMediate letterMediate = letterMediateMapper.selectByPrimaryKey(letterId);
        if ((letterMediate.getFileStatus() != null && letterMediate.getFileStatus() == 2)
                || letterMediate.getFileId() != null) {
            throw new ApiException("该文件已上传！");
        }
        String content = letterMediate.getTemplateContent();
        // 创建无印章内容填充完毕的pdf流
        try (InputStream pdf = PdfUtils.createPDFNoSeal(content, letterMediate.getName())) {
            if (pdf == null) {
                throw new ApiException("创建pdf流失败！");
            }
            int available = pdf.available();
            byte[] bytes = FileMD5Utils.readStream(pdf);
            String fileContentMD5 = FileMD5Utils.getFileContentMD5(bytes);
            Map<String, Object> map = this.getUploadUrl(letterMediate.getName(), fileContentMD5, available);
            if (map.isEmpty()) {
                throw new ApiException("获取上传文件链接返回值异常！");
            }
            String fileId = (String) map.get("fileId");
            String url = (String) map.get("url");
            boolean b = uploadFileToUrl(fileId, fileContentMD5, url, bytes);
            // 更新文件状态
            if (b) {
                updateLetterMediateStatus(fileId, letterId);
            }
        }
    }

    /**
     * 更新文件上传状态
     * @param fileId 文件id
     * @param letterId 调解函id
     */
    private void updateLetterMediateStatus(String fileId, Long letterId) {
        LetterMediate letterMediate = new LetterMediate();
        letterMediate.setId(letterId);
        letterMediate.setFileId(fileId);
        // 状态为文件已上传
        letterMediate.setFileStatus(2);
        letterMediate.setUpdateTime(new Date());
        letterMediateMapper.updateByPrimaryKeySelective(letterMediate);
    }

    /**
     * 上传文件至指定链接
     * @param fileId 文件id
     * @param md5 文件的Content-MD5值
     * @param url 文件上传地址
     * @param bytes 文件大小，单位byte
     */
    private boolean uploadFileToUrl(String fileId, String md5, String url, byte[] bytes) {
        String result = HttpRequest.put(url).header("Content-MD5", md5)
                .header("Content-Type", "application/octet-stream")
                .body(bytes).execute().body();
        JSONObject jsonResult = JSONObject.parseObject(result);
        if (0 != jsonResult.getInteger("errCode")) {
            throw new ApiException(jsonResult.getString("message"));
        }
        UploadFileInfoDto fileUploadStatus = null;
        try {
            // 查看文件上传状态
             fileUploadStatus = eqianbaoSignatureProvider.getFileUploadStatus(fileId);
        } catch (Exception e) {
            throw new ApiException(e.getMessage());
        }
        // 判断文件是否上传 2-文件上传已完成
        return fileUploadStatus.getFileStatus() == 2;
    }

    /**
     * 获取上传文件地址
     * @param letterName 文件名
     * @param fileMd5 文件的Content-MD5值
     * @param fileSize 文件大小，单位byte
     * @return 文件上传地址
     */
    private Map<String , Object> getUploadUrl(String letterName, String fileMd5, Integer fileSize) {
        Map<String, Object> map = new HashMap<>();
        UploadFileInfoDto uploadFileInfo = null;
        try {
            uploadFileInfo = eqianbaoSignatureProvider.createFileUploadUrl(letterName+".pdf", fileMd5, fileSize);
            log.info("当前文件上传的文件id{},文件具体上传地址{}", uploadFileInfo.getFileId(), uploadFileInfo.getFileUploadUrl());
            map.put("fileId", uploadFileInfo.getFileId());
            map.put("url", uploadFileInfo.getFileUploadUrl());
        } catch (Exception e) {
            log.error("生成pdf文件时发生异常" + e.getMessage());
            throw new ApiException(e.getMessage());
        }
        return map;
    }

    /**
     * 发起签署
     *
     * @param letterIds 文书ids
     * @return
     */
    public StartSignatureFailVO startSignatureBatch(String letterIds) {
        List<String> letterIdList = Arrays.asList(letterIds.split(","));
        if (CollectionUtils.isEmpty(letterIdList)) {
            throw new ApiException("letterIds参数不能为空！");
        }
        AtomicInteger failCount=new AtomicInteger(0);
        AtomicInteger successCount=new AtomicInteger(0);
        UserSession userSession = UserUtils.getTokenUser();
        List<AuthOrgInfo> authOrg = getAuthOrg();
        String organizationId = authOrg.get(0).getOrganizationId();
        String psnId = authOrg.get(0).getPsnId();
        CountDownLatch latch = new CountDownLatch(letterIdList.size());
        for (String letterId : letterIdList) {
            threadExecutor.execute(() -> {
                Map<String, Object> resMap = setUpSigner(letterId, userSession.getOrgId());
                if (ObjectUtil.isNotNull(resMap.get("result")) && Boolean.TRUE.equals((Boolean) resMap.get("result"))) {
                    String signersStr = JSONArray.toJSONString(resMap.get("signers"));
                    List<MediateSigner> mediateSigners = (List<MediateSigner>)resMap.get("mediateSigners");
                    // 当前用户的经办人 机构
                    List<String> fileIds = (List<String>)resMap.get("fileIds");
                    if (!CollectionUtils.isEmpty((List<Signer>)resMap.get("signers"))) {
                        String signFlowId = "";
                        try {
                            if (systemConfig.getEqianbaoOrgId().equals(organizationId)) {
                                signFlowId = eqianbaoSignatureProvider.createSignFlowByFile(fileIds, "调解文书",systemConfig.getEqianbaoCallBackUrl(), organizationId, psnId,0, signersStr);
                            } else {
                                signFlowId = eqianbaoSignatureProvider.createSignFlowByFile(fileIds, "调解文书",systemConfig.getEqianbaoCallBackUrl(), organizationId, psnId,1,signersStr);
                            }
                        } catch (Exception e) {
                            throw new ApiException(e.getMessage());
                        }
                        updateSignFlowIdByFileId(fileIds, signFlowId, userSession);
                        // 更新签署方信息
                        mediateSignerService.insertBatch(mediateSigners);
                    }
                    successCount.incrementAndGet();
                } else {
                    failCount.incrementAndGet();
                }
                latch.countDown();
            });
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("批量发起签署线程结束失败:{}", e.getMessage(), e);
        }
        StartSignatureFailVO startSignatureFailVO = new StartSignatureFailVO();
        startSignatureFailVO.setSuccessCount(successCount.get());
        startSignatureFailVO.setFailCount(failCount.get());
        return startSignatureFailVO;
    }

    /**
     * 发起签署
     * @param letterId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMessage<?> startSignature(String letterId) {
        if (StringUtils.isBlank(letterId)) {
            throw new ApiException("letterIds参数不能为空！");
        }
        // 校验
        ResultMessage<?> resultMessage = checkLetterMediate(letterId);
        if (!resultMessage.isSuccess()) {
            return resultMessage;
        }
        UserSession userSession = UserUtils.getTokenUser();
        List<AuthOrgInfo> authOrg = getAuthOrg();
        // 查看签章详情
        Map<String, Object> resMap = setUpSigner(letterId, userSession.getOrgId());
        String signersStr = JSONArray.toJSONString(resMap.get("signers"));
        List<String> fileIds = (List<String>)resMap.get("fileIds");
        List<MediateSigner> mediateSigners = (List<MediateSigner>)resMap.get("mediateSigners");
        String organizationId = authOrg.get(0).getOrganizationId();
        // 查询对应文书名称
        LetterMediate letterMediate = letterMediateMapper.selectByPrimaryKey(Long.valueOf(letterId));
        Case aCase = caseService.selectByPrimaryKey(letterMediate.getCaseId());
        if (!CollectionUtils.isEmpty((List<Signer>)resMap.get("signers"))) {
            String signFlowId = "";
            try {
                // 校验是平台方还是付费方
                if(systemConfig.getEqianbaoOrgId().equals(organizationId)) {
                    signFlowId = eqianbaoSignatureProvider.createSignFlowByFile(fileIds,
                            aCase.getName() + "-"+ letterMediate.getName(),
                            systemConfig.getEqianbaoCallBackUrl(),
                            organizationId, authOrg.get(0).getPsnId(),0, signersStr);
                } else {
                    // 付费方
                    signFlowId = eqianbaoSignatureProvider.createSignFlowByFile(fileIds,
                            aCase.getName() + "-"+ letterMediate.getName(),
                            systemConfig.getEqianbaoCallBackUrl(),
                            organizationId, authOrg.get(0).getPsnId(),1, signersStr);
                }
            } catch (Exception e) {
                log.error("发起签署出现异常{}", e.getMessage());
                throw new ApiException(e.getMessage());
            }
            // 更新签署流程
            updateSignFlowIdByFileId(fileIds, signFlowId, userSession);
            // 更新签署方信息
            mediateSignerService.insertBatch(mediateSigners);
        }
        return ResultMessage.success();
    }


    /**
     * 移除签署
     *
     * @param letterIds 文书ids
     */
    public RemoveMediateFailVO removeSignature(String letterIds) {
        if (StringUtils.isBlank(letterIds)) {
            throw new ApiException("letterIds参数不能为空！");
        }
        List<String> letterIdListStr = Arrays.asList(letterIds.split(","));
        List<Long> letterIdList = letterIdListStr.stream().map(Long::valueOf).collect(Collectors.toList());
        // 查询对应状态
        Example example = new Example(LetterMediate.class);
        example.and().andIn("id", letterIdList);
        List<LetterMediate> letterMediates = letterMediateMapper.selectByExample(example);
        List<Long> noDraftList = letterMediates.stream()
                .filter(item -> item.getSignFlowStatus() != 0)
                .map(LetterMediate::getId).collect(Collectors.toList());
        letterIdList.removeAll(noDraftList);
        RemoveMediateFailVO vo = new RemoveMediateFailVO();
        if (!CollectionUtils.isEmpty(noDraftList)) {
            vo.setFailCount(noDraftList.size());
        }
        // 更新文书状态
        Example updateExample = new Example(LetterMediate.class);
        updateExample.and().andIn("id", letterIdList);
        LetterMediate letterMediate = new LetterMediate();
        letterMediate.setSignFlowStatus(-1);
        letterMediateMapper.updateByExampleSelective(letterMediate, updateExample);
        vo.setSuccessCount(letterIdList.size());
        return vo;
    }

    /**
     * 更新签署流程id
     *
     * @param fileIds 签署文件ids
     */
    private void updateSignFlowIdByFileId(List<String> fileIds, String signFlowId, UserSession userSession) {
        Example example = new Example(LetterMediate.class);
        example.and().andIn("fileId", fileIds);
        LetterMediate letterMediate = new LetterMediate();
        letterMediate.setSignFlowId(signFlowId);
        // 签署状态
        letterMediate.setSignFlowStatus(1);
        // 发起状态
        letterMediate.setStartStatus(1);
        // 发起人
        letterMediate.setSignInitiator(userSession.getId());
        letterMediate.setSignInitiatorName(userSession.getName());
        // 催签时间
        letterMediate.setNoticeTime(new Date());
        letterMediateMapper.updateByExampleSelective(letterMediate, example);
    }

    private List<AuthOrgInfo> getAuthOrg() {
        UserSession tokenUser = UserUtils.getTokenUser();
        Example authOrgExample = new Example(AuthOrgInfo.class);
        authOrgExample.and().andEqualTo("orgId", tokenUser.getOrgId());
        return authOrgInfoMapper.selectByExample(authOrgExample);
    }

    /**
     * 设置签署方
     *
     * @param letterId 文书id
     * @return 签署方
     */
    private Map<String, Object> setUpSigner(String letterId, Long orgId) {
        Company company = companyService.selectByPrimaryKey(orgId);
        List<Signer> signers = new ArrayList<>();
        List<String> fileIds = new ArrayList<>();
        // 设置签章信息
        List<MediateSigner> mediateSigners = new ArrayList<>();
        boolean result = true;
        Map<String, Object> map = new HashMap<>();
        List<MediateSigner> tempMediateSigners = new ArrayList<>();
        List<Signer> tempSigners = new ArrayList<>();
        LetterMediateDetail letterMediateDetail = letterMediateMapper.selectLetterMediateDetail(letterId);
        // 当前签署任务已完成
        if (ObjectUtil.isNull(letterMediateDetail) || CollectionUtils.isEmpty(letterMediateDetail.getLetterSigners()) || letterMediateDetail.getSignFlowStatus() == 2) {
            result = false;
            map.put("result", result);
            return map;
        }
        List<LetterSigner> letterSigners = letterMediateDetail.getLetterSigners();
        for (LetterSigner letterSigner : letterSigners) {
            Signer signer = new Signer();
            MediateSigner mediateSigner = new MediateSigner();
            mediateSigner.setLetterId(Long.valueOf(letterId));
            // 类型 0-个人 1-机构
            signer.setSignerType(letterSigner.getType() == 0 ? 1 : 0);
            // 签署方顺序
            if (letterMediateDetail.getSignOrder() != 0) {
                SignConfig signConfig = new SignConfig();
                signConfig.setSignOrder(letterSigner.getSort());
                signer.setSignConfig(signConfig);
            }
            // 如果不是机构则进行短信通知
            if (letterSigner.getType() != 0) {
                NoticeConfig noticeConfig = new NoticeConfig();
                noticeConfig.setNoticeTypes("1");
                signer.setNoticeConfig(noticeConfig);
            }
            if (letterSigner.getType() == 0) {
                // 机构
                OrgSignerInfo orgSignerInfo = new OrgSignerInfo();
                //orgSignerInfo.setOrgId(authOrgInfos.get(0).getOrganizationId());
                orgSignerInfo.setOrgId(systemConfig.getEqianbaoOrgId());
                signer.setOrgSignerInfo(orgSignerInfo);
            } else {
                // 个人
                PsnSignerInfo psnSignerInfo = new PsnSignerInfo();
                // 需要区分是调解员还是被调解人
                if (SignerEnums.Type.MEDIATOR.getMessage().equals(letterSigner.getName())) {
                    // 获取当前调解人手机号
                    if (StringUtils.isBlank(letterMediateDetail.getMediateMobile())) {
                        if (StringUtils.isBlank(letterMediateDetail.getMobile())) {
                            break;
                        }
                        // 校验手机号
                        if (PatternUtils.isPlanMessage(letterMediateDetail.getMobile())) {
                            psnSignerInfo.setPsnAccount(letterMediateDetail.getMobile());
                        } else {
                            break;
                        }
                    } else {
                        // 校验手机号
                        if (PatternUtils.isPlanMessage(letterMediateDetail.getMediateMobile())) {
                            psnSignerInfo.setPsnAccount(letterMediateDetail.getMediateMobile());
                        } else {
                            break;
                        }
                    }
                    // 签署方信息
                    mediateSigner.setName(letterMediateDetail.getMediatorName());
                    mediateSigner.setMobile(StringUtils.isNotBlank(letterMediateDetail.getMediateMobile()) ? letterMediateDetail.getMediateMobile() : letterMediateDetail.getMobile());
                    mediateSigner.setSignTemplateName(SignerEnums.Type.MEDIATOR.getMessage());
                } else  {
                    // 被调解人
                    if (StringUtils.isBlank(letterMediateDetail.getCaseMobile())) {
                        break;
                    }
                    if (PatternUtils.isPlanMessage(letterMediateDetail.getCaseMobile())) {
                        psnSignerInfo.setPsnAccount(letterMediateDetail.getCaseMobile());
                    } else {
                        break;
                    }
                    // 债务人名称
                    mediateSigner.setName(letterMediateDetail.getDebtorName());
                    mediateSigner.setMobile(letterMediateDetail.getCaseMobile());
                    mediateSigner.setSignTemplateName(SignerEnums.Type.BEMEDIATOR.getMessage());
                }
                signer.setPsnSignerInfo(psnSignerInfo);
                // 签署方类型 0 机构 1个人
                mediateSigner.setType(1);
                mediateSigner.setSort(letterSigner.getSort());
                // 未签署
                mediateSigner.setStatus(1);
            }
            List<SignField> signFields = new ArrayList<>();
            // 签署区配置项
            SignField signField = new SignField();
            signField.setFileId(letterMediateDetail.getFileId());
            // 签署任务的唯一标识
            signField.setCustomBizNum(letterId);
            NormalSignFieldConfig normalSignFieldConfig = new NormalSignFieldConfig();
            // 单页签章
            normalSignFieldConfig.setSignFieldStyle(1);
            List<LetterMediationSeal> seals = new ArrayList<>();
            // 指定签章 只有机构才会指定签章
            if (letterSigner.getType() == 0
                    && (SignerEnums.Type.MEDIATIONORG.getMessage().equals(letterSigner.getName())
                        || SignerEnums.Type.MEDIATIONORG_BANK.getMessage().equals(letterSigner.getName()))) {
                normalSignFieldConfig.setAutoSign(true);
                // 调解机构
                seals = letterMediationSealMapper.selectSealDiff(1, orgId,
                        null, SealTypeEnums.typeMap.get(letterSigner.getSignType()));
                // 过滤掉未授权的
                List<LetterMediationSeal> authSeals = seals.stream().filter(seal -> seal.getAuthStatus() == 1).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(authSeals)) {
                    break;
                }
                //normalSignFieldConfig.setAssignedSealId("369d8259-2819-4cb9-b2cb-517d0cee078f");
                normalSignFieldConfig.setAssignedSealId(authSeals.get(0).getSealId());

                // 机构信息
                mediateSigner.setType(0);
                mediateSigner.setName(authSeals.get(0).getName());
                mediateSigner.setSignTemplateName(SignerEnums.Type.MEDIATIONORG.getMessage());
                if (Objects.equals(company.getIsBank(), CompanyEnums.BankAmc.YES.getCode())) {
                    mediateSigner.setSignTemplateName(SignerEnums.Type.MEDIATIONORG_BANK.getMessage());
                }
                mediateSigner.setSort(letterSigner.getSort());
                mediateSigner.setStatus(1);
                mediateSigner.setSealBizType(authSeals.get(0).getSealBizType());
            } else if (letterSigner.getType() == 0 && (SignerEnums.Type.ENTRUSTEDAGENCY.getMessage().equals(letterSigner.getName())
                || SignerEnums.Type.ENTRUSTEDAGENCY_BANK.getMessage().equals(letterSigner.getName()))){
                normalSignFieldConfig.setAutoSign(true);
                // 分公司（委外机构）
                seals = letterMediationSealMapper.selectSealDiff(0, orgId,
                        letterMediateDetail.getDepId(), SealTypeEnums.typeMap.get(letterSigner.getSignType()));
                List<LetterMediationSeal> authSeals = seals.stream().filter(seal -> seal.getAuthStatus() == 1).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(authSeals)) {
                    break;
                }
                normalSignFieldConfig.setAssignedSealId(authSeals.get(0).getSealId());
                //normalSignFieldConfig.setAssignedSealId("369d8259-2819-4cb9-b2cb-517d0cee078f");
                // 机构信息
                mediateSigner.setType(0);
                mediateSigner.setName(authSeals.get(0).getName());
                mediateSigner.setSignTemplateName(SignerEnums.Type.ENTRUSTEDAGENCY.getMessage());
                if (Objects.equals(company.getIsBank(), CompanyEnums.BankAmc.YES.getCode())) {
                    mediateSigner.setSignTemplateName(SignerEnums.Type.ENTRUSTEDAGENCY_BANK.getMessage());
                }
                mediateSigner.setDepId(authSeals.get(0).getDepId());
                mediateSigner.setSort(letterSigner.getSort());
                mediateSigner.setStatus(1);
                mediateSigner.setSealBizType(authSeals.get(0).getSealBizType());
            }
            // 签章区位置
            SignFieldPosition signFieldPosition = new SignFieldPosition();
            // 签章区所在页面
            signFieldPosition.setPositionPage(Long.toString(letterSigner.getSignPage()));
            signFieldPosition.setPositionX(Float.parseFloat(letterSigner.getSignX()));
            signFieldPosition.setPositionY(Float.parseFloat(letterSigner.getSignY()));
            normalSignFieldConfig.setSignFieldPosition(signFieldPosition);
            signField.setNormalSignFieldConfig(normalSignFieldConfig);

            SignDateConfig signDateConfig = new SignDateConfig();
            // 签章时间
            if (letterMediateDetail.getShowSignTime() == 0) {
                // 不显示
                signDateConfig.setShowSignDate(0);
            } else {
                signDateConfig.setShowSignDate(1);
                signDateConfig.setSignDatePositionX(Float.parseFloat(letterSigner.getSignX()) - 30.0f);
                signDateConfig.setSignDatePositionY(Float.parseFloat(letterSigner.getSignY())- 30.0f);
            }
            signField.setSignDateConfig(signDateConfig);
            signFields.add(signField);
            signer.setSignFields(signFields);
            tempSigners.add(signer);
            mediateSigner.setCreateTime(new Date());
            mediateSigner.setUpdateTime(new Date());
            tempMediateSigners.add(mediateSigner);
        }
        if (letterSigners.size() == tempSigners.size()) {
            signers.addAll(tempSigners);
            mediateSigners.addAll(tempMediateSigners);
            String fileId = letterMediateDetail.getFileId();
            fileIds.add(fileId);
        } else {
            result = false;
        }
        map.put("fileIds", fileIds);
        map.put("signers", signers);
        map.put("mediateSigners", mediateSigners);
        map.put("result", result);
        return map;
    }

    /**
     * 校验
     * @param letterId 函件id
     */
    private ResultMessage<?> checkLetterMediate(String letterId) {
        // 查询是否存在该文件
        LetterMediateDetail letterMediate = letterMediateMapper.selectLetterMediateDetail(letterId);
        if (letterMediate == null) {
            throw new ApiException("该文书不存在!");
        }
        // 查看当前机构是否认证
        UserSession tokenUser = UserUtils.getTokenUser();
        Long orgId = tokenUser.getOrgId();
        Example example = new Example(AuthOrgInfo.class);
        example.and().andEqualTo("orgId", orgId);
        List<AuthOrgInfo> authOrgInfos = authOrgInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(authOrgInfos) || authOrgInfos.get(0).getAuthorizedStatus() == 0) {
            return ResultMessage.error(ResultMessage.ORG_AUTH, "当前登录的机构未认证！");
        }
        String organizationId = authOrgInfos.get(0).getOrganizationId();
        // 查询套餐余额是否充足
        OrderInfoDto ordersRemainingQuantity = null;
        try {
            ordersRemainingQuantity = eqianbaoSignatureProvider.getOrdersRemainingQuantity(organizationId);
        } catch (Exception e) {
            log.error("查询套餐余额是否充足出现异常，{}", e.getMessage());
            throw new ApiException(e.getMessage());
        }
        if (ordersRemainingQuantity.getTotalRemainingQuantity() <= 0) {
            return ResultMessage.error(ResultMessage.REMAINING_QUANTITY, "商户签署余额不足!");
        }
        // 委案方授权印章
        List<LetterSigner> letterSigners = letterMediate.getLetterSigners();
        List<LetterMediationSeal> seals = new ArrayList<>();
        for (LetterSigner letterSigner : letterSigners) {
            // 比较签署机构
            if (letterSigner.getType() == 0
                    && (SignerEnums.Type.MEDIATIONORG.getMessage().equals(letterSigner.getName())
                        || SignerEnums.Type.MEDIATIONORG_BANK.getMessage().equals(letterSigner.getName()))
        ) {
                // 调解机构
                seals = letterMediationSealMapper.selectSealDiff(1, tokenUser.getOrgId(),
                        null, SealTypeEnums.typeMap.get(letterSigner.getSignType()));
                // 过滤已授权的
                List<LetterMediationSeal> authSeals = seals.stream().filter(seal -> seal.getAuthStatus() == 1).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(authSeals) || StringUtils.isBlank(authSeals.get(0).getSealId())) {
                    return ResultMessage.error(ResultMessage.ORG_SEAL_NOTFOUND, "当前机构找不到对应已授权印章信息！");
                }
                // 校验授权印章时间
                if (!DateUtils.between(new Date(),authSeals.get(0).getEffectiveTime(), authSeals.get(0).getExpireTime())) {
                    return ResultMessage.error(ResultMessage.SEAL_NO_AUTH, "授权印章已失效，请重新授权！");
                }
            } else if (letterSigner.getType() == 0
                    && (SignerEnums.Type.ENTRUSTEDAGENCY.getMessage().equals(letterSigner.getName())
                        || SignerEnums.Type.ENTRUSTEDAGENCY_BANK.getMessage().equals(letterSigner.getName()))
            ) {
                // 分公司（委外机构）
                seals = letterMediationSealMapper.selectSealDiff(0, tokenUser.getOrgId(),
                        letterMediate.getDepId(), SealTypeEnums.typeMap.get(letterSigner.getSignType()));
                List<LetterMediationSeal> authSeals = seals.stream().filter(seal -> seal.getAuthStatus() == 1).collect(Collectors.toList());
                // 查询委案方信息
                DepTeam depTeam = depTeamService.selectByPrimaryKey(letterMediate.getDepId());
                if (CollectionUtils.isEmpty(authSeals) || StringUtils.isBlank(authSeals.get(0).getSealId())) {
                    // 返回委案方名称
                    return ResultMessage.error(ResultMessage.DEP_SEAL_NOTFOUND, depTeam.getName());
                }
                // 校验授权印章时间
                if (!DateUtils.between(new Date(),authSeals.get(0).getEffectiveTime(), authSeals.get(0).getExpireTime())) {
                    return ResultMessage.error(ResultMessage.SEAL_NO_AUTH, "授权印章已失效，请重新授权！");
                }
            }
            // 比较签署签署人
            if (letterSigner.getType() == 1
                    && SignerEnums.Type.MEDIATOR.getMessage().equals(letterSigner.getName())) {
                // 查询调解员是否存在手机号 案件联系人手机号
                if ((StringUtils.isBlank(letterMediate.getMediateMobile()) && StringUtils.isBlank(letterMediate.getMobile()))) {
                    return ResultMessage.error(ResultMessage.NO_MOBILE, "签署人无手机号!");
                }
            } else if (letterSigner.getType() == 1
                    && SignerEnums.Type.BEMEDIATOR.getMessage().equals(letterSigner.getName())) {
                if (StringUtils.isBlank(letterMediate.getCaseMobile())) {
                    return ResultMessage.error(ResultMessage.NO_MOBILE, "签署人无手机号!");
                }
            }
        }
        return ResultMessage.success();
    }

    /**
     * 获取电子文件列表
     * @param param
     * @return
     */
    public PageOutput<MediateSignVO> getMediateSignList(SignMediateParam param) {
        encryptQueryData(param);
        UserSession userSession = UserUtils.getTokenUser();
        // 权限
        if (UserUtils.likeAdmin(userSession.getRoleId())) {
            param.setOrgId(userSession.getOrgId());
        } else if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
            param.setOrgId(userSession.getOrgId());
            param.setDepId(userSession.getDepId());
        } else {
            param.setOrgId(userSession.getOrgId());
            param.setUserId(userSession.getId());
        }
        param = this.setParam(param);
        // 分页
        PageParam pageParam = new PageParam();
        pageParam.setPage(param.getPage());
        pageParam.setLimit(param.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<MediateSignVO> mediateSigns = letterMediateMapper.selectMediateSignList(param);
        Map<Long, String> allDepTeamMap = depTeamService.getNames();
        mediateSigns.forEach(mediateAuditVO -> {
                if (encryptProperties.getEnable()) {
                    mediateAuditVO.setName(encryptService.decrypt(mediateAuditVO.getName()));
                    mediateAuditVO.setIdCard(encryptService.decrypt(mediateAuditVO.getIdCard()));
                    mediateAuditVO.setMobile(encryptService.decrypt(mediateAuditVO.getMobile()));
                }
                mediateAuditVO.setDepName(allDepTeamMap.getOrDefault(mediateAuditVO.getDepId(), null));
                mediateAuditVO.setTeamName(allDepTeamMap.getOrDefault(mediateAuditVO.getTeamId(), null));
            }
        );
        return new PageOutput<>(
                        page.getPageNum(),
                        page.getPageSize(),
                        (int)page.getTotal(),
                        mediateSigns
                );
    }

    void encryptQueryData(SignMediateParam query) {
        if (!encryptProperties.getEnable()) {
            return;
        }
        List<String> names = query.getNames();
        if (!CollectionUtils.isEmpty(names)) {
            query.setNames(names.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
        }
        List<String> idCards = query.getIdCards();
        if (!CollectionUtils.isEmpty(idCards)) {
            query.setIdCards(idCards.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
        }
        List<String> mobiles = query.getMobiles();
        if (!CollectionUtils.isEmpty(mobiles)) {
            query.setMobiles(mobiles.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
        }
    }
    private SignMediateParam setParam(SignMediateParam param) {
        // 申请时间
        if (param.getApplyTime() != null && param.getApplyTime().split(",").length == 2) {
            param.setApplyTimeStart(convertDate(param.getApplyTime().split(",")[0]));
            param.setApplyTimeEnd(convertDate(param.getApplyTime().split(",")[1]));
        }
        // 审核时间
        if (param.getAuditTime() != null && param.getAuditTime().split(",").length == 2) {
            param.setAuditTimeStart(convertDate(param.getAuditTime().split(",")[0]));
            param.setAuditTimeEnd(convertDate(param.getAuditTime().split(",")[1]));
        }
        return param;
    }

    /**
     * 电子文书统计
     * @param param
     * @return
     */
    public Integer getMediateSignStat(SignMediateParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        // 权限
        if (UserUtils.likeAdmin(userSession.getRoleId())) {
            param.setOrgId(userSession.getOrgId());
        } else if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
            param.setOrgId(userSession.getOrgId());
            param.setDepId(userSession.getDepId());
        } else {
            param.setOrgId(userSession.getOrgId());
            param.setUserId(userSession.getId());
        }
        param = this.setParam(param);
        // 电子文书显示的都是已经审批通过的
        List<MediateSignVO> vos = letterMediateMapper.selectMediateSignList(param);
        Integer size = 0;
        if (!CollectionUtils.isEmpty(vos)) {
            size = vos.size();
        }
        return size;
    }

    /**
     * 创建一个临时文件
     * @param url 远端文件Url
     * @return File
     */
    private File getFile(String url) {
        //对本地文件命名
        String fileName = "调解文书" + ".pdf";
        File file = null;
        URL urlfile;
        try {
            // 创建一个临时路径
            file = File.createTempFile("file", fileName);
            log.info("tempFile:->{}",file.getAbsolutePath());
            //下载
            urlfile = new URL(url);
            try (InputStream inStream = urlfile.openStream();
                 OutputStream os =  new FileOutputStream(file);) {
                int bytesRead = 0;
                byte[] buffer = new byte[8192];
                while ((bytesRead = inStream.read(buffer, 0, 8192)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
            }
        } catch (Exception e) {
            log.error("创建临时文件出错：-》{}",e.getMessage());
        }
        return file;
    }

    /**
     * 获取签署完成的文件信息
     *
     * @param signFlowId 签署流程id
     * @param response
     */
    private void getSignLetter(String signFlowId, HttpServletResponse response) throws Exception {
        String path = "";
        try {
            path = eqianbaoSignatureProvider.downloadSignFileUrl(signFlowId);
        } catch (Exception e) {
            log.error("获取签署完成文件下载地址时出现异常,{}", e.getMessage());
            throw new ApiException(e.getMessage());
        }
        FileInputStream fis = null;
        try {
            File file = getFile(path);
            fis = new FileInputStream(file);
            response.setContentType("application/pdf");
            FileCopyUtils.copy(fis, response.getOutputStream());
        } finally {
            if (fis !=null) {
                fis.close();
            }
        }

    }

    private List<LetterSigner> checkFinishSigner(Long letterId, LetterMediate letterMediate) {
        // 所有的签署方
        Example example = new Example(LetterSigner.class);
        example.and().andEqualTo("letterTemplateId", letterMediate.getTemplateId());
        List<LetterSigner> signerList = letterSignerMapper.selectByExample(example);
        // 已签署的签署方
        Example mediateSignerExample = new Example(MediateSigner.class);
        mediateSignerExample.and().andEqualTo("letterId", letterId);
        mediateSignerExample.and().andEqualTo("status", 2);
        List<MediateSigner> mediateSigners = mediateSignerService.selectByExample(mediateSignerExample);
        if (CollectionUtils.isEmpty(mediateSigners)) {
            return Collections.emptyList();
        }
        // 移除未签署的
        Map<String, MediateSigner> mediateSignerMap = mediateSigners.stream().collect(Collectors.toMap(MediateSigner::getSignTemplateName, f -> f));
        List<LetterSigner> finishSigner = signerList.stream().filter(item ->
                mediateSignerMap.containsKey(item.getName())
        ).collect(Collectors.toList());
        return finishSigner;
    }
    /**
     * 签署中或已完成签署预览
     *
     * @param letterId  文书id
     * @param response  流
     * @throws Exception
     */
    public void signPreview(Long letterId, HttpServletResponse response) throws Exception{
        LetterMediate letterMediate = letterMediateMapper.selectByPrimaryKey(letterId);
        Integer signFlowStatus = letterMediate.getSignFlowStatus();
        if (signFlowStatus == 2) {
            // 如果签署已经完成
            getSignLetter(letterMediate.getSignFlowId(), response);
            return;
        }
        List<LetterSigner> signerList = checkFinishSigner(letterId, letterMediate);
        Map<String,LetterSigner> orgSignerMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(signerList)) {
            // 过滤类型为机构的签署方
            orgSignerMap = signerList.stream().filter(item -> item.getType() == 0)
                    .collect(Collectors.toMap(LetterSigner::getName ,f -> f));
        }
        // 案件内容
        Long caseId = letterMediate.getCaseId();
        if (caseId == null) {
            throw new ApiException("案件参数不能为空！");
        }
        CaseDTO aCase = caseService.getCaseForLetter(caseId);
        // 获取当前所属机构的印章
        UserSession tokenUser = UserUtils.getTokenUser();
        Long orgId = tokenUser.getOrgId();
        // 调解机构类型
        LetterSigner letterSigner = new LetterSigner();
        Company company = companyService.selectByPrimaryKey(orgId);
        // 区分甲方系统、安米贷后
        if (Objects.equals(company.getIsBank(), CompanyEnums.BankAmc.YES.getCode())) {
            letterSigner = orgSignerMap.get(SignerEnums.Type.MEDIATIONORG_BANK.getMessage());
        } else {
            letterSigner = orgSignerMap.get(SignerEnums.Type.MEDIATIONORG.getMessage());
        }
        Integer type1 = null;
        if (letterSigner != null) {
            type1 =  letterSigner.getSignType();
        }
        // 委托机构类型
        LetterSigner letterSigner1 = new LetterSigner();
        if (Objects.equals(company.getIsBank(), CompanyEnums.BankAmc.YES.getCode())) {
            letterSigner1 = orgSignerMap.get(SignerEnums.Type.ENTRUSTEDAGENCY_BANK.getMessage());
        } else {
           letterSigner1 =  orgSignerMap.get(SignerEnums.Type.ENTRUSTEDAGENCY.getMessage());
        }
        Integer type2 = null;
        if (letterSigner1 != null) {
            type2 = letterSigner1.getSignType();
        }
        // 获取对应案件授权机构的印章
        Long depId = aCase.getDepId();
        List<SignerSealMatchParam> signerSealMatchList = new ArrayList<>();
        // 查询委托机构签章信息
        if (type2 != null) {
            // 获取签章类别
            List<LetterMediationSeal> depSeals = letterMediationSealMapper.selectSealDiff(0, orgId, depId, SealTypeEnums.typeMap.get(type2));
            // 过滤出已授权印章
            List<LetterMediationSeal> authDepSeals = depSeals.stream().filter(seal -> seal.getAuthStatus() == 1).collect(Collectors.toList());
            LetterMediationSeal depSeal = null;
            if (!CollectionUtils.isEmpty(authDepSeals)) {
                // 默认第一条数据
                depSeal = authDepSeals.get(0);
                // 设置签署区和签章进行匹配
                SignerSealMatchParam signerSealMatchParam = new SignerSealMatchParam();
                // 签署区
                BeanUtils.copyProperties(letterSigner1, signerSealMatchParam);
                // 印章信息
                BeanUtils.copyProperties(depSeal, signerSealMatchParam);
                signerSealMatchList.add(signerSealMatchParam);
            }
        }
        if (type1 != null) {
            // 查询当前机构 调解机构
            List<LetterMediationSeal> orgSeals = letterMediationSealMapper.selectSealDiff(1, orgId, null, SealTypeEnums.typeMap.get(type2));
            // 过滤出授权印章
            List<LetterMediationSeal> authOrgSeals = orgSeals.stream().filter(seal -> seal.getAuthStatus() == 1).collect(Collectors.toList());
            LetterMediationSeal orgSeal = null;
            if (!CollectionUtils.isEmpty(authOrgSeals)) {
                // 默认第一条数据
                orgSeal = authOrgSeals.get(0);
                // 设置签署区和签章进行匹配
                SignerSealMatchParam signerSealMatchParam = new SignerSealMatchParam();
                // 签署区
                BeanUtils.copyProperties(letterSigner, signerSealMatchParam);
                // 印章信息
                BeanUtils.copyProperties(orgSeal, signerSealMatchParam);
                signerSealMatchList.add(signerSealMatchParam);
            }
        }
        InputStream pdf1 = null;
        try {
            String content = letterMediate.getTemplateContent();
            String fileName = aCase.getName() + "-" + letterMediate.getName() + ".pdf";
            // 创建pdf流
            pdf1 = PdfUtils.createPDF1(content, fileName, signerSealMatchList);
            response.setContentType("application/pdf");
            FileCopyUtils.copy(pdf1, response.getOutputStream());
        } catch (Exception e) {
            log.error("转为pdf流时出现异常" + e.getMessage());
        } finally {
            if (pdf1 != null) {
                pdf1.close();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteLetterMediateByCaseId(List<Long> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }
        Example letterExample = new Example(LetterMediate.class);
        letterExample.and().andIn("caseId", caseIds);
        List<LetterMediate> letterMediates = letterMediateMapper.selectByExample(letterExample);
        if (CollectionUtils.isEmpty(letterMediates)) {
            return;
        }
        // 案件彻底删除之后，待发起状态和待审核，审核拒绝的都删除 只保留已发起签署和签署完成的
        // audit表中数据 letter_mediate中需判断状态
        List<Long> letterMediateIds = letterMediates.stream().filter(item -> item.getStartStatus() != 1)
                .map(LetterMediate::getId).collect(Collectors.toList());
        List<List<Long>> groupList = CmUtil.splitList(letterMediateIds, 500);
        Example example = new Example(LetterMediate.class);
        for (List<Long> subIds : groupList) {
            if (CommonUtils.isEmpty(subIds)) {
                continue;
            }
            example.clear();
            example.createCriteria().andIn("id", subIds);
            super.deleteByExample(example);
        }
    }
}
