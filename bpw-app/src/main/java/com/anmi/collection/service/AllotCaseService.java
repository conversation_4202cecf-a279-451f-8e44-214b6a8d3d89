package com.anmi.collection.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.algorithm.AllotAlgorithm;
import com.anmi.collection.algorithm.AllotSourceBO;
import com.anmi.collection.algorithm.AllotTargetBO;
import com.anmi.collection.algorithm.CaseAllotAlgorithm;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.constant.StrategyConstant;
import com.anmi.collection.dto.CaseAmount;
import com.anmi.collection.dto.DebtUserBO;
import com.anmi.collection.dto.StrategyRuleCondition;
import com.anmi.collection.entity.requset.cases.AdjustCaseParam;
import com.anmi.collection.entity.requset.cases.AllotCase;
import com.anmi.collection.entity.requset.cases.CaseMultiQuery;
import com.anmi.collection.entity.requset.sys.user.UserParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.event.CaseAllotEvent;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.mapper.UserMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.businessobject.AllotCaseBO;
import com.anmi.collection.service.businessobject.AllotCaseResultListBO;
import com.anmi.collection.utils.AuthBeanUtils;
import com.anmi.collection.utils.CmUtil;
import com.anmi.collection.utils.dict.*;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseAllot;
import com.anmi.domain.cases.CaseInfoResult;
import com.anmi.domain.cases.CaseQueryResult;
import com.anmi.domain.decision.StrategyRule;
import com.anmi.domain.user.DepTeam;
import com.anmi.domain.user.User;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 重写分案逻辑202207
 */
@Slf4j
@Service
public class AllotCaseService extends BaseService<CaseAllot> {
  private static final String ASYNC_PREFIX = "async-";
  @Resource private CaseService caseService;
  @Resource private ApplicationContext applicationContext;
  @Resource private UserService userService;
  @Resource private DepTeamService depTeamService;
  @Resource private StringRedisTemplate stringRedisTemplate;
  @Resource private AsyncTaskService asyncTaskService;
  @Resource private CaseMapper caseMapper;
  @Resource private CaseCooperationApplyService caseCooperationApplyService;
  @Resource private CaseCooperationService caseCooperationService;
  @Resource private UserMapper userMapper;
  @Autowired private RedisUtil redisUtil;
  @Autowired private CommissionSettlementService commissionSettlementService;
  @Autowired private StrategyRuleService strategyRuleService;
  @Resource private CaseOperationWayRelService caseOperationWayRelService;


  @Transactional(rollbackFor = Exception.class)
  public ResultMessage<?> adjust(AdjustCaseParam param) throws Exception {
    if (param.getAllSelect() || param.getFromOperator() != null) {
      asyncAdjust(param);
    } else {
      synchroAllot(param);
    }
    return ResultMessage.success();
  }

  private void asyncAdjust(AdjustCaseParam param) throws Exception {
    param.setSelectField("ca.id,ca.case_status,ca.allot_status,ca.user_id,ur.status as userStatus");
    UserSession userSession = UserUtils.getTokenUser();
    List<CaseQueryResult> caseQueryResultList = selectAdjustList(param, userSession);
    caseQueryResultList = caseService.protectCaseStatus(userSession.getOrgId(), caseQueryResultList);
    try {
      // 创建异步任务任务之前判断3分钟之前是否有相同操作
      AdjustCaseParam adjustCaseParam = AuthBeanUtils.copy(param, AdjustCaseParam.class);
      adjustCaseParam.setOrgIds(userSession.getOrgId().toString());
      String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(adjustCaseParam).getBytes());
      String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
      if (StringUtils.isNotBlank(s)) {
        throw new ApiException("当前已有相同任务进行中，请稍后操作");
      } else {
        stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
      }
      // 创建异步任务
      Long taskId =
        asyncTaskService.createAdjustTask(
          param,
          userSession,
          (long) caseQueryResultList.size(),
          AsyncTaskEnums.Status.ING.getCode());
      // 任务添加到任务列表
      stringRedisTemplate
        .opsForSet()
        .add(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + taskId, caseQueryResultList.stream().map(c -> c.getId().toString()).toArray(String[]::new));
      stringRedisTemplate
        .opsForList()
        .leftPush(KeyCache.CASE_CHANGE_STATUS_TASK_ID_LIST, taskId.toString());
    } catch (Exception e) {
      caseService.removeProtectCases(userSession.getOrgId(), caseQueryResultList);
      throw new ApiException(e.getMessage());
    }
  }

  private void synchroAllot(AdjustCaseParam param) throws Exception {
    UserSession userSession = UserUtils.getTokenUser();
    List<CaseQueryResult> caseQueryResultList = selectAdjustList(param, userSession);
    caseQueryResultList =
      caseService.protectCaseStatus(userSession.getOrgId(), caseQueryResultList);
    try {
      // 创建任务
      Long taskId =
        asyncTaskService.createAdjustTask(
          param,
          userSession,
          (long) caseQueryResultList.size(),
          AsyncTaskEnums.Status.SUCCESS.getCode());
      // 更新数据库数据，生成操作log
      updateAdjustUser(
        caseQueryResultList,
        param.getTeamId(),
        param.getToOperator(),
        param.getReason(),
        userSession,
        taskId);
    } finally {
      caseService.removeProtectCases(userSession.getOrgId(), caseQueryResultList);
    }
  }

  private List<CaseQueryResult> selectAdjustList(AdjustCaseParam param, UserSession userSession) {
    // 只有案件状态是分配完成、留案、退案才可以调整
    List<Integer> allotStatues;
    // 如果action是7，说明是分案至组列表，那么只有分案至组状态
    if (param.getAction() == 7) {
      allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_TEAM.getCode());
    } else if (param.getAction() == 8) {
      allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_BRANCH.getCode());
    } else {
      // 说明是已分配列表
      allotStatues = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode());
    }
    param.setOrgIds(userSession.getOrgId().toString());
    if (!param.getAllSelect()) {
      if (param.getFromOperator() != null) {
        param.setUserIds(param.getFromOperator().toString());
      } else {
        // 针对指定案件调整
        //  去除加锁中的数据
        List<Long> idList = param.getCaseIds();
        if (CommonUtils.isEmpty(idList)) {
          throw new ApiException("请选择案件！");
        }
        param.setCaseIds(idList);
      }
    } else {
      // 全选
      param.setCaseIds(null);
      if (UserUtils.likeBranchAdmin()) {
        param.setDepId(userSession.getDepId());
      } else if (UserUtils.likeTeamLeader()) {
        param.setTeamIds(userSession.getTeamId().toString());
      }
    }
    param.setAllotStatues(StringUtils.join(allotStatues, ","));
    List<CaseQueryResult> caseQueryResultList;
    if (param.getAllSelect() && systemConfig.getESSwitch()) {
      param.setFields(Lists.newArrayList("id", "allot_status", "case_status", "user_id"));
      caseQueryResultList = caseService.getAllCasesUsingEs(param);
    } else {
      // 根据状态、进程、来源催员id，查询
      caseQueryResultList = caseService.queryResultForMulti(param);
    }
    if (CollectionUtils.isEmpty(caseQueryResultList)) {
      throw new ApiException("没有可操作案件，请重新确定案件状态,只有已分配、暂停、留案、退案、分案至组状态案件可做调整，请确认案件状态");
    }
    return caseQueryResultList;
  }

  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public void updateAdjustUser(
    List<CaseQueryResult> caseQueryResultList,
    Long teamId,
    Long toOperator,
    String reason,
    UserSession userSession,
    Long taskId) {
    if (teamId == null) {
      throw new ApiException("批量调整时team_id必须要传！");
    }
    if (CommonUtils.isEmpty(caseQueryResultList)) {
      throw new ApiException("请选择案件！");
    }
    boolean adjustTeam = toOperator == null;
    // 将分案至组的案件全部筛选出来，因为最纯洁的状态才可以变为分案完成，其他分案至组但是状态是留案、退案、作废的案件是不需要改为分案完成的
    List<CaseQueryResult> specialCases;
    List<CaseQueryResult> others;
    if (adjustTeam) {
      // 如果是要调整到小组，那关注点在分案完成案件->分案至组案件的状态变更
      specialCases = caseQueryResultList.stream().filter(ca->ca.getAllotStatus()==CaseEnums.AllotStatus.ALLOT_USER.getCode()).collect(Collectors.toList());
      // 其他
      others = caseQueryResultList.stream().filter(ca->ca.getAllotStatus()!=CaseEnums.AllotStatus.ALLOT_USER.getCode()).collect(Collectors.toList());
    } else {
      // 如果是要调整到员工，那关注点在分案至组案件->分案完成案件的状态变更
      specialCases = caseQueryResultList.stream().filter(ca->ca.getAllotStatus()==CaseEnums.AllotStatus.ALLOT_TEAM.getCode()).collect(Collectors.toList());
      // 其他
      others = caseQueryResultList.stream().filter(ca->ca.getAllotStatus()!=CaseEnums.AllotStatus.ALLOT_TEAM.getCode()).collect(Collectors.toList());
    }
    Map<Long, Map<String, String>> diffMap = new HashMap<>();
    Map<String, Object> map = new HashMap<>();
    map.put("teamId", teamId);
    DepTeam depTeam = depTeamService.selectByPrimaryKey(teamId);
    assert depTeam != null;
    if (depTeam.getUnderTeam().equals(DepTeamEnums.UnderTeam.NOT.getCode())) {
      // 分公司小组
      map.put("depId", depTeam.getParentId());
    } else {
      if (depTeam.getUnderTeam().equals(DepTeamEnums.UnderTeam.YES.getCode())) {
        // 直属小组
        map.put("depId", null);
      }
    }
    List<Long> specialCaseIds =
      specialCases.stream()
        .map(
          infoResult -> {
            // 这一部分的日志变更，不论是special和others都可以共用
            Diff diff = new Diff();
            diff.addDiff("user_id", infoResult.getUserId(), toOperator);
            diff.addDiff("teamId", infoResult.getTeamId(), teamId);
            diff.addDiff(
              "depId",
              infoResult.getDepId(),
              depTeam.getUnderTeam().equals(DepTeamEnums.UnderTeam.NOT.getCode())
                ? depTeam.getParentId()
                : 0);
            diff.addDiff("reason", "", reason);
            diffMap.put(infoResult.getId(), diff.toStringMap());
            return infoResult.getId();
          })
        .collect(Collectors.toList());
    // 其他
    List<Long> othersCaseIds =
      others.stream()
        .map(
          infoResult -> {
            // 这一部分的日志变更，不论是special和others都可以共用
            Diff diff = new Diff();
            diff.addDiff("user_id", infoResult.getUserId(), toOperator);
            diff.addDiff("teamId", infoResult.getTeamId(), teamId);
            diff.addDiff(
              "depId",
              infoResult.getDepId(),
              depTeam.getUnderTeam().equals(DepTeamEnums.UnderTeam.NOT.getCode())
                ? depTeam.getParentId()
                : 0);
            diff.addDiff("reason", "", reason);
            diffMap.put(infoResult.getId(), diff.toStringMap());
            return infoResult.getId();
          })
        .collect(Collectors.toList());
    List<Long> caseIds = new ArrayList<>();
    caseIds.addAll(specialCaseIds);
    caseIds.addAll(othersCaseIds);
    // caseRel和case的变更
    map.put("userId", toOperator);
    map.put("list", caseIds);
    map.put("creator", userSession.getId());
    caseMapper.updateUserIdByCaseIds(map);
    // 更新case的分案日期和状态（分案完成和分案至组之间的变更）
    Map<String, Object> updateCaseMap = new HashMap<>();
    updateCaseMap.put("divisionTime", new Date());
    // 批量调整的案件的颜色变为无状态
    updateCaseMap.put("color", CaseEnums.Color.NOCOLOR.getCode());
    updateCaseMap.put("ignorePlan", CaseEnums.IgnorePlan.NO.getCode());
    updateCaseMap.put("cooperationStatus", CaseEnums.CooperationStatus.NO.getCode());
    if (!CommonUtils.isEmpty(othersCaseIds)) {
      updateCaseMap.put("list", othersCaseIds);
      // 其他案件先更新，因为不涉及改状态
      caseMapper.updateBatchByCaseIds(updateCaseMap);
    }
    // 开始更新这一批特殊案件
    if (!CommonUtils.isEmpty(specialCaseIds)) {
      updateCaseMap.put("list", specialCaseIds);
      if (adjustTeam) {
        // 分案完成变为分案至组
        updateCaseMap.put("allotStatus", CaseEnums.AllotStatus.ALLOT_TEAM.getCode());
      } else {
        // 分案至组变为分案完成
        updateCaseMap.put("allotStatus", CaseEnums.AllotStatus.ALLOT_USER.getCode());
      }
      caseMapper.updateBatchByCaseIds(updateCaseMap);
    }
    // 待审批协催申请自动拒绝，待协催自动完成
    List<Long> caseIdList = caseQueryResultList.stream().map(Case::getId).collect(Collectors.toList());
    caseCooperationApplyService.batchRefuseApply(caseIdList, userSession, "案件重新分配，需重新申请");
    caseCooperationService.batchFinishCooperation(caseIdList, userSession, "案件重新分配，协催自动结束");

    caseQueryResultList.forEach(
      result -> {
        result.setUserId(toOperator);
        result.setDepId((Long) map.get("depId"));
        result.setTeamId((Long) map.get("teamId"));
      });
    // 发布事件
    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.ADJUST.getCode());
    caseBatchUpdateEvent.setDiffMap(diffMap);
    caseBatchUpdateEvent.setCaseIds(caseIds);
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setTaskId(taskId);
    caseBatchUpdateEvent.setCaseInfoResultList(
      JsonUtils.toList(JsonUtils.toJson(caseQueryResultList), CaseInfoResult.class));
    applicationContext.publishEvent(caseBatchUpdateEvent);
  }


  @Transactional(rollbackFor = Exception.class)
  public void allotSubmit(AllotCase allotCase) throws Exception {
    if (ObjectUtil.isNotEmpty(allotCase.getResultOperationWays())&&ObjectUtil.isNull(allotCase.getSwitchType())){
      throw new ApiException("未指明流转类型");
    }
    if (allotCase.getAllSelect()) {
      // 提交异步分配任务
      asyncAllotTask(allotCase);
    } else {
      // 同步处理分配 获取预分配结果
      UserSession userSession = getTokenUser();
      // 只获取id,身份证号，金额
      allotCase.setSelectField("ca.id,ca.debt_id,ca.amount,ca.case_status,ca.allot_status");
      List<Case> caseList = selectByAllot(allotCase, allotCase.getAllSelect());
      caseList = filterCaseByAllot(caseList,allotCase);
      if (allotCase.getCaseCountLimit() != null) {
        if (caseList.size() < allotCase.getCaseCountLimit()) {
          throw new ApiException("输入的数量不可大于已选案件数量");
        }
        caseList = caseList.subList(0, allotCase.getCaseCountLimit().intValue());
      }

      AllotCaseResultListBO allotCaseBOList = preAllot(allotCase, caseList);
      User creator = userService.selectByPrimaryKey(userSession.getId());
      // 创建任务
      Integer allotCaseSize = allotCaseBOList.getAllotCaseBOS().stream().mapToInt(AllotCaseBO::getCaseCount).sum();
      Integer conjointSize = allotCaseBOList.getConjointBOS().stream().mapToInt(AllotCaseBO::getCaseCount).sum();
      Long taskId = asyncTaskService.createAllotTask(allotCase, userSession, (long) (allotCaseSize + conjointSize), AsyncTaskEnums.Status.SUCCESS.getCode());
      Date autoRecoveryDate = allotCase.getAutoRecovery() ? new Date(allotCase.getAutoRecoveryDate()) : null;
      doAllotCase(allotCaseBOList.getAllotCaseBOS(), creator, allotCase.getType(), taskId, autoRecoveryDate,allotCase.getResultOperationWays(),allotCase.getSwitchType());
      doAllotCase(allotCaseBOList.getConjointBOS(), creator, allotCase.getType(), taskId, autoRecoveryDate,allotCase.getResultOperationWays(),allotCase.getSwitchType());
    }
  }

  private void asyncAllotTask(AllotCase allotCase) throws Exception {
    CaseMultiQuery caseMultiQuery = BeanUtil.copyProperties(allotCase, CaseMultiQuery.class);
    // 只获取id
    caseMultiQuery.setSelectField("ca.id,ca.case_status,ca.allot_status");
    List<Case> caseList = selectByAllot(caseMultiQuery, allotCase.getAllSelect());
    caseList = filterCaseByAllot(caseList,allotCase);
    if (allotCase.getType() == 1 && CollectionUtils.isEmpty(allotCase.getAllotProportions())) {
      throw new ApiException("分案比例必填");
    }
    if (allotCase.getAllotObject() == 0 && CollectionUtils.isEmpty(allotCase.getUserIdList())) {
      throw new ApiException("催员不能为空");
    }
    if (allotCase.getAllotObject() == 1 && CollectionUtils.isEmpty(allotCase.getTeamIdList())) {
      throw new ApiException("小组必填！");
    }
    if (allotCase.getAllotObject() == 2 && CollectionUtils.isEmpty(allotCase.getDepList())) {
      throw new ApiException("分公司/机构必填！");
    }
    if (allotCase.getCaseCountLimit() != null) {
      if (caseList.size() < allotCase.getCaseCountLimit()) {
        throw new ApiException("输入的数量不可大于已选案件数量");
      }
      caseList = caseList.subList(0, allotCase.getCaseCountLimit().intValue());
    }
    List<String> caseIdList = caseList.stream().map(c -> c.getId().toString()).collect(Collectors.toList());
    UserSession userSession = getTokenUser();
    stringRedisTemplate.opsForSet().add(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(), caseIdList.toArray(new String[0]));
    try {
      Long taskId = asyncTaskService.createAllotTask(allotCase, userSession, (long) caseIdList.size(), AsyncTaskEnums.Status.ING.getCode());
      redisUtil.sSet(KeyCache.CASE_ALLOT_TASK_CASES + taskId, 24*60*60, caseIdList.toArray(new String[0]));
      stringRedisTemplate.opsForList().leftPush(KeyCache.CASE_ALLOT_TASK_ID_LIST, taskId.toString());
    } catch (Exception ex) {
      stringRedisTemplate.opsForSet().remove(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId(), caseIdList.toArray());
      throw ex;
    }
  }

  public void doAllotCase(List<AllotCaseBO> allotCaseBOList, User creator, Integer type, Long taskId, Date autoRecoveryDate, List<Integer> resultOperationWays, Integer switchType) {
    // 按分配结果同步操作数据库
    for (AllotCaseBO allotCaseBO : allotCaseBOList) {
      Long userId = allotCaseBO.getUserId();
      List<Long> caseList = allotCaseBO.getCaseList();
      // 更新数据库，生成log
      if (CollectionUtils.isEmpty(caseList)) {
        continue;
      }
      saveCaseAllot(taskId, allotCaseBO.getUserId(), allotCaseBO.getUserName(),
        type, creator.getId(), allotCaseBO.getConjointCount(),
        allotCaseBO.getTeamId(), allotCaseBO.getDepId(), allotCaseBO.getCaseAmount(), allotCaseBO.getCaseCount());
      if (allotCaseBO.getUserId() != null) {
        User dunner = userService.selectByPrimaryKey(userId);
        caseAllotToUser(caseList, dunner, creator, taskId,resultOperationWays,switchType);
      } else {
        caseAllotToDepTeam(caseList, allotCaseBO.getDepId(), allotCaseBO.getTeamId(), creator, taskId, autoRecoveryDate,resultOperationWays,switchType);
      }
    }
  }

  /**
   * 根据查询条件查询所有可分配的案件
   *
   * @param caseMultiQuery 查询条件
   * @param allSelect      是否全选
   * @return 返回可操作的案件列表
   * @throws Exception 未知异常
   */
  public List<Case> selectByAllot(CaseMultiQuery caseMultiQuery, Boolean allSelect) throws Exception {
    if (null == caseMultiQuery) {
      throw new ApiException("分配参数校验不通过");
    }
    List<Case> caseList;
    UserSession userSession = getTokenUser();
    Set<String> idLockList = stringRedisTemplate.opsForSet().members(KeyCache.CASE_PROTECT_EXIST_IDS + userSession.getOrgId());
    // 批量选择
    if (!allSelect) {
      if (CommonUtils.isEmpty(caseMultiQuery.getCaseIds())) {
        throw new ApiException("请选择案件");
      }
      //  去除加锁中的数据
      List<Long> idList;
      if (CollectionUtils.isEmpty(idLockList)) {
        idList = caseMultiQuery.getCaseIds();
      } else {
        idList = caseMultiQuery.getCaseIds().stream().filter(c -> !idLockList.contains(c.toString())).collect(Collectors.toList());
      }
      if (CollectionUtils.isEmpty(idList)) {
        throw new ApiException("已选案件已提交分案任务，请勿重复提交");
      }
      Example example = new Example(Case.class);
      Example.Criteria criteria = example.createCriteria();
      criteria.andIn("id", idList);
      String[] fields = caseMultiQuery.getSelectField().split(",");
      for (String field : fields) {
        example.selectProperties(StringUtils.capitalize(StringUtils.substringAfterLast(field, ".")));
      }
      caseList = caseService.selectByExample(example);
    } else {
      caseMultiQuery.setOrgIds(userSession.getOrgId().toString());
      if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
        // 如果是分公司管理员，进行分公司隔离
        caseMultiQuery.setDepId(userSession.getDepId());
      } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
        caseMultiQuery.setTeamIds(userSession.getTeamId().toString());
      }
      if (ObjectUtil.isNotNull(caseMultiQuery.getAllotAgent())) {
        caseMultiQuery.setAllotAgentState(CaseEnums.AllotAgentState.YES.getCode());
      }
      caseMultiQuery.setCaseIds(null);
      if (systemConfig.getESSwitch()) {
        caseMultiQuery.setFields(Lists.newArrayList("id", "debtId", "amount", "caseStatus", "allotStatus"));
        List<CaseQueryResult> caseQueryResults = caseService.getAllCasesUsingEs(caseMultiQuery);
        if (CollectionUtils.isEmpty(caseQueryResults)) {
          throw new ApiException("所选条件筛选结果为空");
        }
        caseList = BeanUtil.copyPropertiesFromList(caseQueryResults, Case.class);
      } else {
        List<CaseQueryResult> list = caseService.queryResultForMulti(caseMultiQuery);
        caseList = BeanUtil.copyPropertiesFromList(list, Case.class);
      }
      // 过滤加锁的案件
      if (!CollectionUtils.isEmpty(idLockList)) {
        caseList = caseList.stream().filter(c -> !idLockList.contains(c.getId().toString())).collect(Collectors.toList());
      }
    }
    return caseList;
  }


  public AllotCaseResultListBO preAllot(AllotCase allotCase, List<Case> caseList) {
    if(CollectionUtils.isEmpty(caseList)){
      throw new ApiException("筛选案件列表为空");
    }
    Long orgId = caseList.get(0).getOrgId();
    Map<Long, String> allDepTeamMap = depTeamService.getNames();
    AllotCaseResultListBO allotCaseResultListBO = new AllotCaseResultListBO();
    allotCaseResultListBO.setAllotCaseBOS(new ArrayList<>());
    //共债历史催员分案
    if (Boolean.TRUE.equals(allotCase.getIsAllAgents())) {
      List<Case> conjointList = caseList.stream().filter(o -> !Objects.isNull(o.getDebtId())).collect(Collectors.toList());
      List<List<Case>> splitList = CmUtil.splitList(conjointList, 500);
      List<DebtUserBO> debtUserBOList = new ArrayList<>();
      for (List<Case> subList : splitList) {
        List<Long> debtList = subList.stream().map(Case::getDebtId).distinct().collect(Collectors.toList());
        List<DebtUserBO> subDebtUserBOList = caseMapper.selectConjointDebtUserMap(debtList);
        debtUserBOList.addAll(subDebtUserBOList);
      }
      List<Long> conjointUserIdList = debtUserBOList.stream().map(DebtUserBO::getUserId).distinct().collect(Collectors.toList());
      List<User> userList = userService.selectByIdList(conjointUserIdList);
      if(!CollectionUtils.isEmpty(userList)) {
        Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, Function.identity()));
        Map<Long, List<DebtUserBO>> userDebtsMap = debtUserBOList.stream().collect(Collectors.groupingBy(DebtUserBO::getUserId));
        for (Map.Entry<Long, List<DebtUserBO>> entry : userDebtsMap.entrySet()) {
          Long userId = entry.getKey();
          List<DebtUserBO> debtUserBOS = entry.getValue();
          AllotCaseBO allotCaseBO = new AllotCaseBO();
          allotCaseBO.setUserId(userId);
          allotCaseBO.setUserName(userMap.get(userId).getName());
          allotCaseBO.setTeamId(userMap.get(userId).getTeamId());
          allotCaseBO.setTeamName(allDepTeamMap.get(userMap.get(userId).getTeamId()));
          allotCaseBO.setDepId(userMap.get(userId).getDepId());
          allotCaseBO.setDepName(allDepTeamMap.get(userMap.get(userId).getDepId()));
          List<Long> debtIds = debtUserBOS.stream().map(DebtUserBO::getDebtId).collect(Collectors.toList());
          List<Case> caseListTemp = caseList.stream().filter(o -> debtIds.contains(o.getDebtId())).collect(Collectors.toList());
          allotCaseBO.setCaseList(caseListTemp.stream().map(Case::getId).collect(Collectors.toList()));
          allotCaseBO.setCaseAmount(caseListTemp.stream().mapToLong(Case::getAmount).sum());
          allotCaseBO.setCaseCount(caseListTemp.size());
          allotCaseBO.setAllotToTeamMembers(true);
          allotCaseBO.setConjointCount(caseListTemp.size());
          allotCaseResultListBO.getConjointBOS().add(allotCaseBO);
          caseList.removeAll(caseListTemp);
        }
      }
    }
    // ============================剩下的案件分配
    // 判断是否规则分案
    // 业绩指标分案
    Boolean isAllotByRuleIndicator = false;
    // 特征匹配分案
    Boolean isAllotByRuleAttribute = false;
    // 执行分配案件总数包含分案对象名下已有案件
    Boolean isIncludeAllotted = false;
    StrategyRule strategyRule = null;
    if (Objects.equals(allotCase.getIsRule(), 1)) {
      Long ruleId = allotCase.getRuleId();
      strategyRule = strategyRuleService.getStrategyRule(ruleId);
      isAllotByRuleIndicator = strategyRule.getType() == 0;
      isAllotByRuleAttribute = strategyRule.getType() == 1;
      isIncludeAllotted = Objects.equals(strategyRule.getIncludeAllotted(), 1);
    }
    // 特征匹配分案，需要分组进行分案
    if (isAllotByRuleAttribute) {
      List<Long> objectIds = new ArrayList<>();
      if (Objects.equals(AllotCaseEnums.Target.USER.getCode(), allotCase.getAllotObject())) {
        objectIds = allotCase.getUserIdList();
      }
      if (Objects.equals(AllotCaseEnums.Target.TEAM.getCode(), allotCase.getAllotObject())) {
        objectIds = allotCase.getTeamIdList();
      }
      if (Objects.equals(AllotCaseEnums.Target.DEPT.getCode(), allotCase.getAllotObject()) || Objects.equals(AllotCaseEnums.Target.AGENT.getCode(), allotCase.getAllotObject())) {
        objectIds = allotCase.getDepList();
      }
      Map<List<Long>, List<Case>> map = strategyRuleService.getGroupByRuleAttribute(objectIds, caseList,
              allotCase.getAllotObject(), strategyRule);
      for (Map.Entry<List<Long>, List<Case>> entry : map.entrySet()) {
        List<Long> ids = entry.getKey();
        List<Case> cases = entry.getValue();
        if (Objects.equals(AllotCaseEnums.Target.USER.getCode(), allotCase.getAllotObject())) {
          allotCase.setUserIdList(ids);
        }
        if (Objects.equals(AllotCaseEnums.Target.TEAM.getCode(), allotCase.getAllotObject())) {
          allotCase.setTeamIdList(ids);
        }
        if (Objects.equals(AllotCaseEnums.Target.DEPT.getCode(), allotCase.getAllotObject()) || Objects.equals(AllotCaseEnums.Target.AGENT.getCode(), allotCase.getAllotObject())) {
          allotCase.setDepList(ids);
        }
        allotCaseByAlgorithmAndRule(allotCase, cases, isAllotByRuleIndicator, isIncludeAllotted, strategyRule, allotCaseResultListBO, allDepTeamMap, orgId);
      }
      return allotCaseResultListBO;
    }

    // 不是特征匹配规则分案的，无论指标分案还是基础分案都走这里
    allotCaseByAlgorithmAndRule(allotCase, caseList, isAllotByRuleIndicator, isIncludeAllotted, strategyRule, allotCaseResultListBO, allDepTeamMap, orgId);
    return allotCaseResultListBO;
  }

  public void allotCaseByAlgorithmAndRule(AllotCase allotCase, List<Case> caseList,
                                         Boolean isAllotByRuleIndicator, Boolean isIncludeAllotted,
                                          StrategyRule strategyRule, AllotCaseResultListBO allotCaseResultListBO,
                                          Map<Long, String> allDepTeamMap, Long orgId) {

    String metricField = strategyRule == null ? null : strategyRule.getMetricField();
    List<AllotSourceBO> sourceList = convertAllotSource(allotCase, caseList, metricField);
    if (CollectionUtils.isEmpty(sourceList)) {
      return;
    }
    Map<Long, List<Case>> debtCaseMap = caseList.stream().filter(ca -> !Objects.isNull(ca.getDebtId())).collect(Collectors.groupingBy(Case::getDebtId));
    if (allotCase.getType() == 1 && CollectionUtils.isEmpty(allotCase.getAllotProportions())) {
      throw new ApiException("分案比例必填");
    }
    List<AllotTargetBO> targetList = new ArrayList<>();
    if (Objects.equals(AllotCaseEnums.Target.USER.getCode(), allotCase.getAllotObject())) {
      // 分案为规则分案，且选择了自定义个人排名名单，分案对象不传，这里做适配
      Boolean isNeedUser = true;
      if (isAllotByRuleIndicator) {
        StrategyRuleCondition condition = JSONObject.parseObject(strategyRule.getCondition(), StrategyRuleCondition.class);
        if (Objects.equals(condition.getIndicator(), StrategyConstant.RULE_INDICATOR_CUSTOM_RANK) &&
                Objects.equals(condition.getSubType(), StrategyConstant.RULE_INDICATOR_CUSTOM_RANK_USER)) {
          isNeedUser = false;
        }
      }
      List<Long> userIdList = allotCase.getUserIdList();
      if (CollectionUtils.isEmpty(userIdList) && isNeedUser) {
        throw new ApiException("催员必填！");
      }
      // 根据业绩指标规则分案
      if (isAllotByRuleIndicator) {
        targetList = getTargetBOListByRuleIndicator(userIdList, allotCase.getAllotObject(), strategyRule, isIncludeAllotted, orgId);
        if (!isNeedUser) {
          userIdList = targetList.stream().map(AllotTargetBO::getId).collect(Collectors.toList());
        }
      } else {
        targetList = getTargetBOList(allotCase.getType(), userIdList, allotCase.getAllotProportions());
      }
      // 接入规则分案之后，比如说指标分案规则分给3~5名，但是所选分案对象才2个，肯定只有1~2名，
      // 该分案配置导致分配对象targetList为空，也就是不需要分案，这里直接返回即可
      if (CollectionUtils.isEmpty(targetList)) {
        return;
      }
      AllotAlgorithm<AllotSourceBO, AllotTargetBO> allotAlgorithm = new CaseAllotAlgorithm();
      allotAlgorithm.allot(sourceList, targetList);
      Map<AllotTargetBO, List<AllotSourceBO>> targetBOMap = sourceList.stream().collect(Collectors.groupingBy(AllotSourceBO::getTarget));
      List<User> userList = userService.selectByIdList(userIdList);
      Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, Function.identity()));
      for (Map.Entry<AllotTargetBO, List<AllotSourceBO>> entry : targetBOMap.entrySet()) {
        User dunner = userMap.get(entry.getKey().getId());
        AllotCaseBO allotCaseBO = new AllotCaseBO();
        allotCaseBO.setUserId(dunner.getId());
        allotCaseBO.setUserName(dunner.getName());
        allotCaseBO.setTeamId(dunner.getTeamId());
        allotCaseBO.setTeamName(allDepTeamMap.get(dunner.getTeamId()));
        allotCaseBO.setDepId(dunner.getDepId());
        allotCaseBO.setDepName(allDepTeamMap.get(dunner.getDepId()));
        List<Long> caseIds = convertToCaseIdList(debtCaseMap, entry.getValue(), allotCase);
        allotCaseBO.setCaseList(caseIds);
        allotCaseBO.setCaseAmount(entry.getValue().stream().mapToLong(o -> o.getAmount().longValue()).sum());
        allotCaseBO.setCaseCount(caseIds.size());
        allotCaseBO.setAllotToTeamMembers(true);
        allotCaseBO.setConjointCount(0);
        allotCaseResultListBO.getAllotCaseBOS().add(allotCaseBO);
      }
    }
    if (Objects.equals(AllotCaseEnums.Target.TEAM.getCode(), allotCase.getAllotObject())) {
      List<Long> teamIdList = allotCase.getTeamIdList();
      if (CollectionUtils.isEmpty(teamIdList)) {
        throw new ApiException("小组必填！");
      }
      // 根据业绩指标规则分案
      if (isAllotByRuleIndicator) {
        targetList = getTargetBOListByRuleIndicator(teamIdList, allotCase.getAllotObject(), strategyRule, isIncludeAllotted, orgId);
      } else {
        targetList = getTargetBOList(allotCase.getType(), teamIdList, allotCase.getAllotProportions());
      }
      if (CollectionUtils.isEmpty(targetList)) {
        return;
      }
      AllotAlgorithm<AllotSourceBO, AllotTargetBO> allotAlgorithm = new CaseAllotAlgorithm();
      allotAlgorithm.allot(sourceList, targetList);

      Map<AllotTargetBO, List<AllotSourceBO>> targetBOMap = sourceList.stream().collect(Collectors.groupingBy(AllotSourceBO::getTarget));
      List<DepTeam> teamList = depTeamService.selectByIdList(teamIdList);

      UserParam userParam=new UserParam();
      userParam.setTeamIds(StringUtils.join(teamIdList,","));
      List<User> dunnerList = userMapper.queryDunnerUsers(userParam);
      Map<Long, DepTeam> teamMap = teamList.stream().collect(Collectors.toMap(DepTeam::getId, Function.identity()));
      for (Map.Entry<AllotTargetBO, List<AllotSourceBO>> entry : targetBOMap.entrySet()) {
        DepTeam team = teamMap.get(entry.getKey().getId());
        List<Long> caseIds = convertToCaseIdList(debtCaseMap, entry.getValue(), allotCase);
        if(allotCase.getAllotToTeamMembers()){
          //小组下面所有催员平均分配,找到小组下面所有的催员
          List<User> teamDunnerList=dunnerList.stream().filter(u->Objects.equals(u.getTeamId(),team.getId())).collect(Collectors.toList());
          if (CollectionUtils.isEmpty(teamDunnerList)) {
            throw new ApiException("小组{}内催员列表为空,无法继续分配", team.getName());
          }
          if(CollectionUtils.isEmpty(caseIds)){
            continue;
          }
          List<Case> cases = caseService.selectByIdList(caseIds);
          if(CollectionUtils.isEmpty(cases)){
            continue;
          }
          AllotCase allotCaseToUser = new AllotCase();
          allotCaseToUser.setAllotObject(0);
          allotCaseToUser.setAllotProportions(null);
          allotCaseToUser.setIsAllAgents(false);
          allotCaseToUser.setUserIdList(teamDunnerList.stream().map(User::getId).collect(Collectors.toList()));
          allotCaseToUser.setDepList(null);
          allotCaseToUser.setCaseIds(caseIds);
          allotCaseToUser.setAutoRecoveryDate(null);
          allotCaseToUser.setType(0);
          allotCaseToUser.setIsConjoint(allotCase.getIsConjoint());
          AllotCaseResultListBO resultListBO = preAllot(allotCaseToUser,cases);//递归分案至催员
          allotCaseResultListBO.getAllotCaseBOS().addAll(resultListBO.getAllotCaseBOS());
        }else {
          AllotCaseBO allotCaseBO = new AllotCaseBO();
          allotCaseBO.setTeamId(team.getId());
          allotCaseBO.setTeamName(allDepTeamMap.get(team.getId()));
          if (DepTeamEnums.UnderTeam.YES.getCode() != team.getUnderTeam() && team.getType() == DepTeamEnums.type.TEAM.getCode()) {
            allotCaseBO.setDepId(team.getParentId());
            allotCaseBO.setDepName(allDepTeamMap.get(team.getParentId()));
          }
          allotCaseBO.setCaseList(caseIds);
          allotCaseBO.setCaseAmount(entry.getValue().stream().mapToLong(o -> o.getAmount().longValue()).sum());
          allotCaseBO.setCaseCount(caseIds.size());
          allotCaseBO.setAllotToTeamMembers(false);
          allotCaseBO.setConjointCount(0);
          allotCaseResultListBO.getAllotCaseBOS().add(allotCaseBO);
        }
      }
    }
    if (Objects.equals(AllotCaseEnums.Target.DEPT.getCode(), allotCase.getAllotObject()) || Objects.equals(AllotCaseEnums.Target.AGENT.getCode(), allotCase.getAllotObject())) {
      List<Long> depIdList = allotCase.getDepList();
      if (CollectionUtils.isEmpty(depIdList)) {
        throw new ApiException("分公司/委外机构必填！");
      }
      // 根据业绩指标规则分案
      if (isAllotByRuleIndicator) {
        targetList = getTargetBOListByRuleIndicator(depIdList, allotCase.getAllotObject(), strategyRule, isIncludeAllotted, orgId);
      } else {
        targetList = getTargetBOList(allotCase.getType(), depIdList, allotCase.getAllotProportions());
      }
      if (CollectionUtils.isEmpty(targetList)) {
        return;
      }
      AllotAlgorithm<AllotSourceBO, AllotTargetBO> allotAlgorithm = new CaseAllotAlgorithm();
      allotAlgorithm.allot(sourceList, targetList);
      Map<AllotTargetBO, List<AllotSourceBO>> targetBOMap = sourceList.stream().collect(Collectors.groupingBy(AllotSourceBO::getTarget));
      List<DepTeam> depList = depTeamService.selectByIdList(depIdList);
      Map<Long, DepTeam> depMap = depList.stream().collect(Collectors.toMap(DepTeam::getId, Function.identity()));
      for (Map.Entry<AllotTargetBO, List<AllotSourceBO>> entry : targetBOMap.entrySet()) {
        DepTeam dep = depMap.get(entry.getKey().getId());
        AllotCaseBO allotCaseBO = new AllotCaseBO();
        allotCaseBO.setDepId(dep.getId());
        allotCaseBO.setDepName(allDepTeamMap.get(dep.getId()));
        List<Long> caseIds = convertToCaseIdList(debtCaseMap, entry.getValue(), allotCase);
        allotCaseBO.setCaseList(caseIds);
        allotCaseBO.setCaseAmount(entry.getValue().stream().mapToLong(o -> o.getAmount().longValue()).sum());
        allotCaseBO.setCaseCount(caseIds.size());
        allotCaseBO.setAllotToTeamMembers(false);
        allotCaseBO.setConjointCount(0);
        allotCaseResultListBO.getAllotCaseBOS().add(allotCaseBO);
      }
    }
  }


  public List<Case> filterCaseByAllot(List<Case> caseList, AllotCase allotCase) {
    if (CollectionUtils.isEmpty(caseList)) {
      return Collections.emptyList();
    }
    //如果是分配至分公司,过滤出未分配的案件
    if (Objects.equals(AllotCaseEnums.Target.DEPT.getCode(), allotCase.getAllotObject()) || Objects.equals(AllotCaseEnums.Target.AGENT.getCode(), allotCase.getAllotObject())) {
      return caseList.stream().filter(t -> Objects.equals(t.getAllotStatus(), CaseEnums.AllotStatus.NOT_ALLOT.getCode())).collect(Collectors.toList());
    }
    //如果是分配至组，则过滤出未分配的案件和分配至分公司的案件
    if (Objects.equals(AllotCaseEnums.Target.TEAM.getCode(), allotCase.getAllotObject())) {
      return caseList.stream().filter(t -> Objects.equals(t.getAllotStatus(), CaseEnums.AllotStatus.NOT_ALLOT.getCode()) || Objects.equals(t.getAllotStatus(), CaseEnums.AllotStatus.ALLOT_BRANCH.getCode())).collect(Collectors.toList());
    }
    //如果是分配至催员，则过滤出未分配的案件和分配至分公司的案件以及分配至组的案件
    if (Objects.equals(AllotCaseEnums.Target.USER.getCode(), allotCase.getAllotObject())) {
      return caseList.stream().filter(t -> !Objects.equals(t.getAllotStatus(), CaseEnums.AllotStatus.ALLOT_USER.getCode())).collect(Collectors.toList());
    }
    throw new ApiException("分案对象错误");
  }
  /**
   * 将案件转换为分案对象
   * 如果是分案参数选择了共债分配，则将共债案件合并成一个案件进行分配(案件金额叠加)
   * 最后再拆分成多个案件，如此可保证分案算法将共债的案件分配给同一个催员
   *
   * @param allotCase 分案参数
   * @param caseList  案件列表
   * @return 返回分案结果
   */
  private List<AllotSourceBO> convertAllotSource(AllotCase allotCase, List<Case> caseList, String metricField) {
    if (allotCase.getIsConjoint()) {
      List<AllotSourceBO> allotSourceBOList = new ArrayList<>();
      Map<Long, AllotSourceBO> debtMap = new HashMap<>();
      Iterator<Case> iterator = caseList.iterator();
      while (iterator.hasNext()) {
        Case ca = iterator.next();
        BigDecimal metric = caseService.getMetricFieldValue(metricField, ca);
        // 算法金额字段取值为空，没有分配意义，移除
        if (Objects.isNull(metric)) {
          iterator.remove();
          continue;
        }
        if (Objects.isNull(ca.getDebtId())) {
          AllotSourceBO bo = new AllotSourceBO();
          bo.setCaseId(ca.getId());
          bo.setAmount(metric);
          allotSourceBOList.add(bo);
        } else {
          if (debtMap.containsKey(ca.getDebtId())) {
            AllotSourceBO bo = debtMap.get(ca.getDebtId());
            bo.setAmount(bo.getAmount().add(metric));
          } else {
            AllotSourceBO bo = new AllotSourceBO();
            bo.setDebtId(ca.getDebtId());
            bo.setAmount(metric);
            allotSourceBOList.add(bo);
            debtMap.put(ca.getDebtId(), bo);
          }
        }
      }
      return allotSourceBOList;
    } else {
      List<AllotSourceBO> allotSourceBOList = new ArrayList<>(caseList.size());
      Iterator<Case> iterator = caseList.iterator();
      while (iterator.hasNext()) {
        Case ca = iterator.next();
        BigDecimal metric = caseService.getMetricFieldValue(metricField, ca);
        // 算法金额字段取值为空，没有分配意义，移除
        if (Objects.isNull(metric)) {
          iterator.remove();
          continue;
        }
        AllotSourceBO bo = new AllotSourceBO();
        bo.setCaseId(ca.getId());
        bo.setAmount(metric);
        allotSourceBOList.add(bo);
      }
      return allotSourceBOList;
    }
  }

  private List<Long> convertToCaseIdList(Map<Long, List<Case>> debtCaseMap, List<AllotSourceBO> allotSourceBOList, AllotCase allotCase) {
    if (!allotCase.getIsConjoint()) {
      return allotSourceBOList.stream().map(AllotSourceBO::getCaseId).collect(Collectors.toList());
    }
    List<Long> caseIdList = new ArrayList<>();
    for (AllotSourceBO allotSourceBO : allotSourceBOList) {
      if (Objects.isNull(allotSourceBO.getDebtId())) {
        caseIdList.add(allotSourceBO.getCaseId());
      } else {
        caseIdList.addAll(debtCaseMap.get(allotSourceBO.getDebtId()).stream().map(Case::getId).collect(Collectors.toList()));
      }
    }
    return caseIdList;
  }

  private List<AllotTargetBO> getTargetBOList(Integer type, List<Long> targetIds, Map<Long, Integer> allotProportions) {
    List<AllotTargetBO> targetList = new ArrayList<>();
    if (type == 0) {
      targetIds.forEach(o -> targetList.add(new AllotTargetBO(o, 1)));
    } else {
      allotProportions.forEach((id, rate) -> targetList.add(new AllotTargetBO(id, rate)));
    }
    return targetList;
  }

  private List<AllotTargetBO> getTargetBOListByRuleIndicator(List<Long> targetIds, Integer allotObject,
                                                             StrategyRule rule, Boolean isIncludeAllotted, Long orgId) {
    Map<Long, Integer> proportion = strategyRuleService.getProportionByRuleIndicator(targetIds, allotObject, rule);
    List<AllotTargetBO> targetList = new ArrayList<>();
    proportion.forEach((id, rate) -> targetList.add(new AllotTargetBO(id, rate)));
    if (isIncludeAllotted && !CollectionUtils.isEmpty(targetList)) {
      targetIds = targetList.stream().map(AllotTargetBO::getId).collect(Collectors.toList());
      List<CaseAmount> caseAmounts = caseService.getCaseAmount(targetIds, allotObject, orgId, rule.getMetricField());
      targetList.forEach(allotTargetBO -> {
        CaseAmount ca = caseAmounts.parallelStream().filter(caseAmount -> Objects.equals(caseAmount.getSourceId(),
                allotTargetBO.getId())).findFirst().orElse(null);
        allotTargetBO.setAllottedMetrics(ca == null ? BigDecimal.ZERO : ca.getAmount());
      });
    }
    return targetList;
  }


  public CaseAllot saveCaseAllot(
    Long taskId, Long dunnerId,
    String dunnerName, Integer type,
    Long createBy, Integer conjointCount,
    Long teamId, Long depId,
    Long amount, Integer count) {
    CaseAllot caseAllot = new CaseAllot();
    caseAllot.setStatus(1);
    caseAllot.setType(type);
    caseAllot.setUserId(dunnerId);
    caseAllot.setUserName(dunnerName);
    caseAllot.setTeamId(teamId);
    caseAllot.setDepId(depId);
    caseAllot.setCount(count);
    caseAllot.setAmount(amount);
    caseAllot.setCreateBy(createBy);
    caseAllot.setUpdateBy(createBy);
    caseAllot.setCreateTime(new Date());
    caseAllot.setUpdateTime(new Date());
    caseAllot.setConjointCount(conjointCount);
    caseAllot.setTaskId(taskId);
    super.insertSelective(caseAllot);
    return caseAllot;
  }


  @Transactional(rollbackFor = Exception.class)
  public void caseAllotToUser(List<Long> caseList, User dunner, User creator, Long taskId, List<Integer> resultOperationWays, Integer switchType) {
    if (dunner == null) {
      throw new ApiException("分配案件需要有所属催员！");
    }

    boolean shouldUpdateOutsourceCount = false;
    if (dunner.getDepId() != null) {//判断是否需要更新委外手别
      DepTeam depTeam = depTeamService.selectByPrimaryKey(dunner.getDepId());
      if (DepTeamEnums.IsBankAgent.YES.getCode().equals(depTeam.getIsBankAgent())) {
        shouldUpdateOutsourceCount = true;
      }
    }

    List<List<Long>> groupList = CmUtil.splitList(caseList, 500);
    for (List<Long> subList : groupList) {
      Map<String, Object> params = new HashMap<>();
      params.put("list", subList);
      params.put("userId", dunner.getId());
      params.put("teamId", dunner.getTeamId());
      params.put("depId", dunner.getDepId());
      params.put("creator", creator.getId());
      if (shouldUpdateOutsourceCount) {//委外手别处理
        List<Case> caseListTmp = caseService.selectByIdList(subList);
        Long finalDepId = dunner.getDepId();
        //过滤一下案件当前的委外机构/分公司跟以前的分公司不一致的，此类案件的委外手别要+1
        caseListTmp = caseListTmp.stream().filter(ca -> !finalDepId.equals(ca.getDepId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(caseListTmp)) {
          List<Long> caseIds = caseListTmp.stream().map(Case::getId).collect(Collectors.toList());
          caseMapper.increaseOutsourceCount(caseIds);
        }
        // 新增佣金结算记录
        commissionSettlementService.saveCommissionSettlement(caseListTmp, finalDepId);
      }
      if (ObjectUtil.isNotEmpty(resultOperationWays)) {
        caseOperationWayRelService.switchWay(subList,switchType,resultOperationWays);
      }
      caseMapper.updateAllotAgent(subList, shouldUpdateOutsourceCount ? 2 : 1);
      caseMapper.updateUserIdByCaseIds(params);
      caseService.updateStatusByIds(subList, CaseEnums.AllotStatus.ALLOT_USER.getCode());
      applicationContext.publishEvent(new CaseAllotEvent(this, subList, dunner, creator, taskId));
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void caseAllotToDepTeam(List<Long> caseList, Long depId, Long teamId, User creator, Long taskId, Date autoRecoveryDate, List<Integer> resultOperationWays, Integer switchType) {
    if (depId == null) {
      DepTeam depTeam = depTeamService.selectByPrimaryKey(teamId);
      if (depTeam.getType() == DepTeamEnums.type.TEAM.getCode() && depTeam.getUnderTeam() == DepTeamEnums.UnderTeam.NOT.getCode()) {
        depId = depTeam.getParentId();
      }
    }
    boolean shouldUpdateOutsourceCount = false;
    if (depId != null) {//判断是否需要更新委外手别
      DepTeam depTeam = depTeamService.selectByPrimaryKey(depId);
      if (DepTeamEnums.IsBankAgent.YES.getCode().equals(depTeam.getIsBankAgent())) {
        shouldUpdateOutsourceCount = true;
      }
    }
    List<List<Long>> groupList = CmUtil.splitList(caseList, 500);
    for (List<Long> subList : groupList) {
      Map<String, Object> params = new HashMap<>();
      params.put("list", subList);
      params.put("userId", null);
      params.put("teamId", teamId);
      params.put("depId", depId);
      params.put("creator", creator.getId());
      params.put("autoRecoveryDate", autoRecoveryDate);
      if (shouldUpdateOutsourceCount) {//委外手别处理
        List<Case> caseListTmp = caseService.selectByIdList(subList);
        Long finalDepId = depId;
        //过滤一下案件当前的委外机构/分公司跟以前的分公司不一致的，此类案件的委外手别要+1
        caseListTmp = caseListTmp.stream().filter(ca -> !finalDepId.equals(ca.getDepId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(caseListTmp)) {
          List<Long> caseIds = caseListTmp.stream().map(Case::getId).collect(Collectors.toList());
          caseMapper.increaseOutsourceCount(caseIds);
        }
        // 新增佣金结算记录
        commissionSettlementService.saveCommissionSettlement(caseListTmp, depId);
      }
      if (ObjectUtil.isNotEmpty(resultOperationWays)) {
        caseOperationWayRelService.switchWay(subList,switchType,resultOperationWays);
      }
      caseMapper.updateUserIdByCaseIds(params);
      caseMapper.updateAllotAgent(subList, shouldUpdateOutsourceCount ? 2 : 1);
      if (teamId == null) {
        caseService.updateStatusByIds(subList, CaseEnums.AllotStatus.ALLOT_BRANCH.getCode());
      } else {
        caseService.updateStatusByIds(subList, CaseEnums.AllotStatus.ALLOT_TEAM.getCode());
      }
      applicationContext.publishEvent(new CaseAllotEvent(this, subList, teamId, creator, taskId));
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void autoAllotCaseByCallIn(Long caseId) throws Exception {
    // 构造案件分配接口参数，调用案件分配接口方法，保证相同分配逻辑
    UserSession userSession = UserUtils.getTokenUser();
    AllotCase allotCase = new AllotCase();
    List<Long> caseIds = caseService.getConjointCaseIds(caseId, userSession.getOrgId());
    caseIds.add(caseId);
    // 所选案件: 当前案件及其共债案件
    allotCase.setCaseIds(caseIds);

    // 分配到指定催员（当前坐席）
    allotCase.setDepList(new ArrayList<>());
    allotCase.setTeamIdList(Lists.newArrayList(userSession.getTeamId()));
    allotCase.setUserIdList(Lists.newArrayList(userSession.getId()));

    allotCase.setAllSelect(false);
    allotCase.setAction(2);
    allotCase.setAllotObject(0);
    allotCase.setAllotToTeamMembers(true);
    allotCase.setAutoRecovery(false);
    allotCase.setType(0);
    allotCase.setIsConjoint(true);
    allotCase.setIsAllAgents(false);
    allotSubmit(allotCase);
  }
}
