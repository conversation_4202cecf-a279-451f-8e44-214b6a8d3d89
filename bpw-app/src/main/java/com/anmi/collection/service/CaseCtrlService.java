package com.anmi.collection.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.enums.OrgSwitchEnums;
import com.anmi.collection.dto.ctrl.CtrlCustomCount;
import com.anmi.collection.entity.requset.cases.ctrl.*;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.ctrl.*;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.*;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.businessobject.CaseCtrlPoolCondition;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.*;
import com.anmi.domain.user.Contacts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CaseCtrlService extends BaseService<CaseCtrlPool> {
  @Resource
  private CustomOperationStatusService customOperationStatusService;
  @Resource
  private CaseCtrlConditionMapper caseCtrlConditionMapper;
  @Resource
  private CaseCtrlCustomMapper caseCtrlCustomMapper;
  @Resource
  private CaseCtrlPoolMapper caseCtrlPoolMapper;
  @Resource
  private CaseCtrlCustomRelMapper caseCtrlCustomRelMapper;
  @Resource
  private CaseService caseService;
  @Resource
  private CaseOperationService caseOperationService;
  @Resource
  private ContactsService contactsService;
  @Resource
  private OrgSwitchMapper orgSwitchMapper;

  public List<CaseCtrlListVO> caseCtrlList() {
    UserSession userSession = UserUtils.getTokenUser();
    Assert.notNull(userSession.getOrgId(), "公司id不能为空");
    Example example = new Example(CaseCtrlPool.class);
    example.and().andEqualTo("orgId", userSession.getOrgId());
    example.setOrderByClause("sort asc,id asc");
    List<CaseCtrlPool> caseCtrlPoolList = this.selectByExample(example);
    if (CollectionUtils.isEmpty(caseCtrlPoolList)) {
      return Collections.emptyList();
    }
    List<Long> caseCtrlIds = caseCtrlPoolList.stream().map(CaseCtrlPool::getId).collect(Collectors.toList());
    if(CollectionUtils.isEmpty(caseCtrlIds)){
      return Collections.emptyList();
    }
    Example caseCtrlConditionExample = new Example(CaseCtrlCondition.class);
    caseCtrlConditionExample.and().andIn("controlId", caseCtrlIds);
    List<CaseCtrlCondition> caseCtrlConditions = caseCtrlConditionMapper.selectByExample(caseCtrlConditionExample);
    Map<Long, List<CaseCtrlCondition>> caseCtrlConditionMap = caseCtrlConditions.stream().collect(Collectors.groupingBy(CaseCtrlCondition::getControlId));

    Example caseCtrlCustomExample = new Example(CaseCtrlCustom.class);
    caseCtrlCustomExample.and().andIn("controlId", caseCtrlIds);
    List<CtrlCustomCount> ctrlCustomCountList = caseCtrlCustomMapper.selectCountByCtrlIds(caseCtrlIds);
    Map<Long, Long> ctrlCustomCountMap = ctrlCustomCountList.stream().collect(Collectors.toMap(CtrlCustomCount::getCtrlId, CtrlCustomCount::getCustomCnt));

    return convert(caseCtrlPoolList, caseCtrlConditionMap, ctrlCustomCountMap,userSession);
  }

  private List<CaseCtrlListVO> convert(List<CaseCtrlPool> caseCtrlPoolList, Map<Long, List<CaseCtrlCondition>> caseCtrlConditionMap, Map<Long, Long> ctrlCustomCountMap, UserSession userSession) {
    List<CaseCtrlListVO> result = new ArrayList<>();
    Map<Integer, String> customOperationMap = customOperationStatusService.getNames(userSession.getOrgId());
    for (CaseCtrlPool caseCtrlPool : caseCtrlPoolList) {
      CaseCtrlListVO vo = new CaseCtrlListVO();
      vo.setEnable(Objects.equals(caseCtrlPool.getEnable(), 1));
      vo.setId(caseCtrlPool.getId());
      vo.setName(caseCtrlPool.getName());
      vo.setSort(caseCtrlPool.getSort());
      vo.setCustomCnt(ctrlCustomCountMap.containsKey(caseCtrlPool.getId()) ? ctrlCustomCountMap.get(caseCtrlPool.getId()).intValue() : 0);
      String owmLimit = null;
      String conLimit = null;
      if(Objects.equals(caseCtrlPool.getDebtorOwn(),1)){
        owmLimit = MessageFormat.format("本人({0}天{1}次；{2}天最少{3}次；{4}小时最多{5}次)",
          caseCtrlPool.getOwnLimitDay(),
          caseCtrlPool.getOwnLimitDayTime(),
          caseCtrlPool.getOwnLimitDay(),
          caseCtrlPool.getOwnLimitDayLeastTime(),
          caseCtrlPool.getOwnLimitHour(),
          caseCtrlPool.getOwnLimitHourTime());
      }
      if(Objects.equals(caseCtrlPool.getContacts(),1)){
        String conTypeStr = "";
        if (Objects.equals(caseCtrlPool.getConLimitType(), 1)) {
          conTypeStr = "同一号码";
          conLimit = MessageFormat.format("联系人，{0}({1}天{2}次；{3}小时最多{4}次)",
                  conTypeStr,
                  caseCtrlPool.getConLimitDay(),
                  caseCtrlPool.getConLimitDayTime(),
                  caseCtrlPool.getConLimitHour(),
                  caseCtrlPool.getConLimitHourTime());
        } else {
          conTypeStr = "全部号码";
          conLimit = MessageFormat.format("联系人，{0}({1}天{2}次；{3}天最少{4}次)",
                  conTypeStr,
                  caseCtrlPool.getConLimitDay(),
                  caseCtrlPool.getConLimitDayTime(),
                  caseCtrlPool.getConLimitDay(),
                  caseCtrlPool.getConLimitDayLeastTime());
        }

      }
      vo.setOwnLimitName(owmLimit);
      vo.setConLimitName(conLimit);
      List<CaseCtrlCondition> caseCtrlConditions = caseCtrlConditionMap.get(caseCtrlPool.getId());
      List<String> names = new ArrayList<>();
      for (CaseCtrlCondition caseCtrlCondition : caseCtrlConditions) {
        names.add(customOperationMap.get(caseCtrlCondition.getOperCode()));
      }
      vo.setConditionsName(StringUtils.join(names, "；"));
      result.add(vo);
    }
    return result;
  }

  public CaseCtrlDetailVO caseCtrlDetail(Long caseCtrlId) {
    Assert.notNull(caseCtrlId, "id不能为空");
    UserSession userSession = UserUtils.getTokenUser();
    CaseCtrlPool caseCtrlPool = this.selectByPrimaryKey(caseCtrlId);
    if (Objects.isNull(caseCtrlPool) || !Objects.equals(caseCtrlPool.getOrgId(), userSession.getOrgId())) {
      throw new ApiException("查不到数据");
    }
    CaseCtrlDetailVO vo = new CaseCtrlDetailVO();
    BeanUtils.copyProperties(caseCtrlPool, vo);
    Example caseCtrlConditionExample = new Example(CaseCtrlCondition.class);
    caseCtrlConditionExample.and().andEqualTo("controlId", caseCtrlId);
    List<CaseCtrlCondition> caseCtrlConditions = caseCtrlConditionMapper.selectByExample(caseCtrlConditionExample);
    List<Integer> conditions = caseCtrlConditions.stream().map(CaseCtrlCondition::getOperCode).collect(Collectors.toList());
    vo.setConditions(conditions);

    Example caseCtrlCustomExample = new Example(CaseCtrlCustom.class);
    caseCtrlCustomExample.and().andEqualTo("controlId", caseCtrlId);
    List<CaseCtrlCustom> caseCtrlCustoms = caseCtrlCustomMapper.selectByExample(caseCtrlCustomExample);
    List<CaseCtrlCustomVO> caseCtrlCustomList = caseCtrlCustoms.stream().map(o -> new CaseCtrlCustomVO(o.getId(), o.getContent())).collect(Collectors.toList());
    vo.setCustomCtrlList(caseCtrlCustomList);
    return vo;
  }

  @Transactional(rollbackFor = Throwable.class)
  @CacheEvict(value= KeyCache.OPER_CODE_CASE_CTRL_MAP, key = "#userSession.orgId")
  public Long addCaseCtrl(CaseCtrlAddParam caseCtrlAddParam, UserSession userSession) {
    if (!UserUtils.likeAdmin(userSession.getRoleId())) {
      throw new ApiException("只有管理员才能操作");
    }
    Assert.notNull(userSession.getOrgId(), "公司id不能为空");
    Date now = new Date();
    CaseCtrlPool caseCtrlPool = new CaseCtrlPool();
    BeanUtils.copyProperties(caseCtrlAddParam, caseCtrlPool);
    if (caseCtrlPool.getConLimitType() == 0) {
      caseCtrlPool.setConLimitDay(1);
    }
    caseCtrlPool.setOrgId(userSession.getOrgId());
    caseCtrlPool.setEnable(0);
    caseCtrlPool.setSort(Integer.MAX_VALUE);
    caseCtrlPool.setCreateBy(userSession.getId());
    caseCtrlPool.setUpdateBy(userSession.getId());
    caseCtrlPool.setCreateTime(now);
    caseCtrlPool.setUpdateTime(now);
    this.insert(caseCtrlPool);
    List<CaseCtrlCondition> caseCtrlConditions = new ArrayList<>();
    for (Integer operCode : caseCtrlAddParam.getConditions()) {
      CaseCtrlCondition caseCtrlCondition = new CaseCtrlCondition();
      caseCtrlCondition.setControlId(caseCtrlPool.getId());
      caseCtrlCondition.setOperCode(operCode);
      caseCtrlCondition.setCreateBy(userSession.getId());
      caseCtrlCondition.setUpdateBy(userSession.getId());
      caseCtrlCondition.setCreateTime(now);
      caseCtrlCondition.setUpdateTime(now);
      caseCtrlConditions.add(caseCtrlCondition);
    }
    if (!CollectionUtils.isEmpty(caseCtrlConditions)) {
      caseCtrlConditionMapper.insertList(caseCtrlConditions);
    }

    List<CaseCtrlCustom> caseCtrlCustoms = new ArrayList<>();
    for (CaseCtrlCustomParam caseCtrlCustomParam : caseCtrlAddParam.getCustomCtrlList()) {
      CaseCtrlCustom caseCtrlCustom = new CaseCtrlCustom();
      caseCtrlCustom.setControlId(caseCtrlPool.getId());
      caseCtrlCustom.setContent(caseCtrlCustomParam.getContent());
      caseCtrlCustom.setCreateBy(userSession.getId());
      caseCtrlCustom.setUpdateBy(userSession.getId());
      caseCtrlCustom.setCreateTime(now);
      caseCtrlCustom.setUpdateTime(now);
      caseCtrlCustoms.add(caseCtrlCustom);
    }
    if (!CollectionUtils.isEmpty(caseCtrlCustoms)) {
      caseCtrlCustomMapper.insertList(caseCtrlCustoms);
    }
    return caseCtrlPool.getId();
  }

  @Transactional(rollbackFor = Throwable.class)
  @CacheEvict(value= KeyCache.OPER_CODE_CASE_CTRL_MAP, key = "#userSession.orgId")
  public void updateCaseCtrl(CaseCtrlUpdateParam caseCtrlUpdateParam, UserSession userSession) {
    if (!UserUtils.likeAdmin(userSession.getRoleId())) {
      throw new ApiException("只有管理员才能操作");
    }
    Date now = new Date();
    CaseCtrlPool caseCtrlPool = this.selectByPrimaryKey(caseCtrlUpdateParam.getId());
    if (caseCtrlPool == null || !Objects.equals(caseCtrlPool.getOrgId(), userSession.getOrgId())) {
      throw new ApiException("查不到数据");
    }
    caseCtrlPool.setName(caseCtrlUpdateParam.getName());
    caseCtrlPool.setConLimitType(caseCtrlUpdateParam.getConLimitType());
    if(caseCtrlPool.getConLimitType() == 0) {
      caseCtrlPool.setConLimitDay(1);
    } else {
      caseCtrlPool.setConLimitDay(caseCtrlUpdateParam.getConLimitDay());
    }
    caseCtrlPool.setConLimitDayTime(caseCtrlUpdateParam.getConLimitDayTime());
    caseCtrlPool.setConLimitDayLeastTime(caseCtrlUpdateParam.getConLimitDayLeastTime());
    caseCtrlPool.setConLimitHour(caseCtrlUpdateParam.getConLimitHour());
    caseCtrlPool.setConLimitHourTime(caseCtrlUpdateParam.getConLimitHourTime());

    caseCtrlPool.setOwnLimitDay(caseCtrlUpdateParam.getOwnLimitDay());
    caseCtrlPool.setOwnLimitDayTime(caseCtrlUpdateParam.getOwnLimitDayTime());
    caseCtrlPool.setOwnLimitDayLeastTime(caseCtrlUpdateParam.getOwnLimitDayLeastTime());
    caseCtrlPool.setOwnLimitHour(caseCtrlUpdateParam.getOwnLimitHour());
    caseCtrlPool.setOwnLimitHourTime(caseCtrlUpdateParam.getOwnLimitHourTime());
    caseCtrlPool.setContacts(caseCtrlUpdateParam.getContacts());
    caseCtrlPool.setDebtorOwn(caseCtrlUpdateParam.getDebtorOwn());

    caseCtrlPool.setUpdateBy(userSession.getId());
    caseCtrlPool.setUpdateTime(now);
    this.updateByPrimaryKey(caseCtrlPool);
    Example caseCtrlConditionExample = new Example(CaseCtrlCondition.class);
    caseCtrlConditionExample.and().andEqualTo("controlId", caseCtrlUpdateParam.getId());
    caseCtrlConditionMapper.deleteByExample(caseCtrlConditionExample);
    List<CaseCtrlCondition> caseCtrlConditions = new ArrayList<>();
    for (Integer operCode : caseCtrlUpdateParam.getConditions()) {
      CaseCtrlCondition caseCtrlCondition = new CaseCtrlCondition();
      caseCtrlCondition.setControlId(caseCtrlPool.getId());
      caseCtrlCondition.setOperCode(operCode);
      caseCtrlCondition.setCreateBy(userSession.getId());
      caseCtrlCondition.setUpdateBy(userSession.getId());
      caseCtrlCondition.setCreateTime(now);
      caseCtrlCondition.setUpdateTime(now);
      caseCtrlConditions.add(caseCtrlCondition);
    }
    if (!CollectionUtils.isEmpty(caseCtrlConditions)) {
      caseCtrlConditionMapper.insertList(caseCtrlConditions);
    }

    List<CaseCtrlCustom> newCaseCtrlCustoms = new ArrayList<>();
    for (CaseCtrlCustomParam caseCtrlCustomParam : caseCtrlUpdateParam.getCustomCtrlList()) {
      CaseCtrlCustom caseCtrlCustom = new CaseCtrlCustom();
      if (Objects.isNull(caseCtrlCustomParam.getId())) {
        caseCtrlCustom.setControlId(caseCtrlPool.getId());
        caseCtrlCustom.setContent(caseCtrlCustomParam.getContent());
        caseCtrlCustom.setCreateBy(userSession.getId());
        caseCtrlCustom.setUpdateBy(userSession.getId());
        caseCtrlCustom.setCreateTime(now);
        caseCtrlCustom.setUpdateTime(now);
        newCaseCtrlCustoms.add(caseCtrlCustom);
      } else {
        caseCtrlCustom.setId(caseCtrlCustomParam.getId());
        caseCtrlCustom.setContent(caseCtrlCustomParam.getContent());
        caseCtrlCustom.setUpdateBy(userSession.getId());
        caseCtrlCustom.setUpdateTime(now);
        caseCtrlCustomMapper.updateByPrimaryKeySelective(caseCtrlCustom);
      }
    }
    if (!CollectionUtils.isEmpty(newCaseCtrlCustoms)) {
      caseCtrlCustomMapper.insertList(newCaseCtrlCustoms);
    }
  }

  @Transactional(rollbackFor = Throwable.class)
  @CacheEvict(value= KeyCache.OPER_CODE_CASE_CTRL_MAP, key = "#userSession.orgId")
  public void enableCaseCtrl(Long caseCtrlId, Boolean enable, UserSession userSession) {
    if (!UserUtils.likeAdmin(userSession.getRoleId())) {
      throw new ApiException("只有管理员才能操作");
    }
    Assert.notNull(caseCtrlId, "id不能为空");
    CaseCtrlPool caseCtrlPool = this.selectByPrimaryKey(caseCtrlId);
    if (caseCtrlPool == null || !Objects.equals(caseCtrlPool.getOrgId(), userSession.getOrgId())) {
      throw new ApiException("查不到数据");
    }
    caseCtrlPool.setEnable(enable ? 1 : 0);
    caseCtrlPool.setUpdateBy(userSession.getId());
    caseCtrlPool.setUpdateTime(new Date());
    this.updateByPrimaryKey(caseCtrlPool);
  }

  @Transactional(rollbackFor = Throwable.class)
  public void sortCaseCtrl(List<CaseCtrlSortParam> sortParams) {
    if (!UserUtils.likeAdmin()) {
      throw new ApiException("只有管理员才能操作");
    }
    if (CollectionUtils.isEmpty(sortParams)) {
      throw new ApiException("排序列表不能为空");
    }
    UserSession userSession = UserUtils.getTokenUser();
    Map<Long, Integer> caseCtrlSortParamMap = sortParams.stream().collect(Collectors.toMap(CaseCtrlSortParam::getId, CaseCtrlSortParam::getSort));
    List<Long> caseCtrlIds = sortParams.stream().map(CaseCtrlSortParam::getId).collect(Collectors.toList());
    List<CaseCtrlPool> caseCtrlList = this.selectByIdList(caseCtrlIds);
    if (CollectionUtils.isEmpty(caseCtrlList)) {
      throw new ApiException("排序列表不能为空");
    }
    for (CaseCtrlPool caseCtrlPool : caseCtrlList) {
      if (!Objects.equals(userSession.getOrgId(), caseCtrlPool.getOrgId())) {
        throw new ApiException("id为{0}的管控不属于当前公司", caseCtrlPool.getId());
      }
      caseCtrlPool.setSort(caseCtrlSortParamMap.get(caseCtrlPool.getId()));
    }
    this.caseCtrlPoolMapper.updateBatch(caseCtrlList, userSession.getId());
  }

  @Transactional(rollbackFor = Throwable.class)
  public void deleteCaseCtrlCustom(Long ctrlCustomId) {
    Assert.notNull(ctrlCustomId, "id不能为空");
    if (!UserUtils.likeAdmin()) {
      throw new ApiException("只有管理员才能操作");
    }
    UserSession userSession = UserUtils.getTokenUser();
    CaseCtrlCustom caseCtrlCustom = this.caseCtrlCustomMapper.selectByPrimaryKey(ctrlCustomId);
    if (caseCtrlCustom == null) {
      throw new ApiException("查不到数据");
    }
    CaseCtrlPool caseCtrlPool = this.selectByPrimaryKey(caseCtrlCustom.getControlId());
    if (caseCtrlPool == null || !Objects.equals(userSession.getOrgId(), caseCtrlPool.getOrgId())) {
      throw new ApiException("查不到数据");
    }
    Example deleteExample = new Example(CaseCtrlCustomRel.class);
    deleteExample.and().andEqualTo("ctrlCustomId", ctrlCustomId);
    this.caseCtrlCustomRelMapper.deleteByExample(deleteExample);
    this.caseCtrlCustomMapper.deleteByPrimaryKey(ctrlCustomId);
  }

  @Cacheable(value= KeyCache.OPER_CODE_CASE_CTRL_MAP, key = "#orgId")
  public Map<Integer,List<CaseCtrlPoolCondition>> getOperCodeCtrlMap(Long orgId){
    List<CaseCtrlPoolCondition> caseCtrlPoolConditions=caseCtrlPoolMapper.selectByOrg(orgId);
    return caseCtrlPoolConditions.stream().collect(Collectors.groupingBy(CaseCtrlPoolCondition::getOperCode));
  }

  public CaseCtrlInfoVO caseCtrlInfo(Long caseId) {
    Assert.notNull(caseId, "id不能为空");
    UserSession userSession = UserUtils.getTokenUser();
    Case ca = caseService.selectByPrimaryKey(caseId);
    if (ca == null || !Objects.equals(ca.getOrgId(), userSession.getOrgId())) {
      throw new ApiException("找不到数据");
    }
    if (Objects.isNull(ca.getCtrlId())) {
      return null;
    }
    Long ctrlId = ca.getCtrlId();
    CaseCtrlPool caseCtrlPool = this.selectByPrimaryKey(ctrlId);
    if (Objects.isNull(caseCtrlPool)
      || !Objects.equals(caseCtrlPool.getOrgId(), userSession.getOrgId())
      || !Objects.equals(caseCtrlPool.getEnable(), 1)) {
      return null;
    }

    //管控基本信息
    CaseCtrlInfoVO vo = new CaseCtrlInfoVO();
    BeanUtils.copyProperties(caseCtrlPool, vo);

    //管控条件
    Example caseCtrlConditionExample = new Example(CaseCtrlCondition.class);
    caseCtrlConditionExample.and().andEqualTo("controlId", ctrlId);
    List<CaseCtrlCondition> caseCtrlConditions = caseCtrlConditionMapper.selectByExample(caseCtrlConditionExample);
    Set<Integer> conditions = caseCtrlConditions.stream().map(CaseCtrlCondition::getOperCode).collect(Collectors.toSet());
    Map<Integer, String> customOperationMap = customOperationStatusService.getNames(userSession.getOrgId());
    List<String> conditionNames = new ArrayList<>();
    for(Integer operCode:conditions){
      conditionNames.add(customOperationMap.get(operCode));
    }
    vo.setConditions(conditionNames);

    //电催完成情况
    Integer ownLimitDay = caseCtrlPool.getOwnLimitDay();
    if (ownLimitDay != null) {
      //本人呼叫次数
      Date date = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1 - ownLimitDay));
      Integer ownDayTime = caseOperationService.selectCountByCaseAndDay(caseId, date, 0, null);
      vo.setOwnFinishedTime(ownDayTime);
    }
    Integer contactLimitDay = caseCtrlPool.getConLimitDay();
    if (contactLimitDay != null) {
      //联系人呼叫次数
      Date date = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1 - contactLimitDay));
      Integer conDayTime = caseOperationService.selectCountByCaseAndDay(caseId, date, 1,null);
      vo.setContactFinishedTime(conDayTime);
    }

    //自定义管控完成情况
    Example caseCtrlCustomExample = new Example(CaseCtrlCustom.class);
    caseCtrlCustomExample.and().andEqualTo("controlId", ctrlId);
    List<CaseCtrlCustom> caseCtrlCustoms = caseCtrlCustomMapper.selectByExample(caseCtrlCustomExample);
    if(!CollectionUtils.isEmpty(caseCtrlCustoms)){
      List<Long> customIds=caseCtrlCustoms.stream().map(CaseCtrlCustom::getId).collect(Collectors.toList());
      List<CaseCtrlCustomRel> caseCtrlCustomRelList = caseCtrlCustomRelMapper.selectByCaseCustomPairs(customIds,caseId);
      Map<Long,CaseCtrlCustomRel> caseCtrlCustomRelMap = caseCtrlCustomRelList.stream().collect(Collectors.toMap(CaseCtrlCustomRel::getCtrlCustomId, Function.identity()));
      List<CaseCtrlCustomRelVO> caseCtrlCustomRelVos = new ArrayList<>();
      for(CaseCtrlCustom caseCtrlCustom : caseCtrlCustoms){
        CaseCtrlCustomRelVO caseCtrlCustomRelVO=new CaseCtrlCustomRelVO();
        caseCtrlCustomRelVO.setCtrlCustomId(caseCtrlCustom.getId());
        if(caseCtrlCustomRelMap.containsKey(caseCtrlCustom.getId())){
          caseCtrlCustomRelVO.setDesc(caseCtrlCustomRelMap.get(caseCtrlCustom.getId()).getNote());
          caseCtrlCustomRelVO.setFinished(caseCtrlCustomRelMap.get(caseCtrlCustom.getId()).getFinished());
          caseCtrlCustomRelVO.setUpdateTime(caseCtrlCustomRelMap.get(caseCtrlCustom.getId()).getUpdateTime().getTime());
        }else {
          caseCtrlCustomRelVO.setDesc(null);
          caseCtrlCustomRelVO.setFinished(0);
          caseCtrlCustomRelVO.setUpdateTime(null);
        }
        caseCtrlCustomRelVO.setContent(caseCtrlCustom.getContent());
        caseCtrlCustomRelVos.add(caseCtrlCustomRelVO);
      }
      vo.setCaseCustomRel(caseCtrlCustomRelVos);
    }
    return vo;
  }

  public Long updateCustomCaseRel(CaseCtrlCustomRelParam param) {
    UserSession userSession = UserUtils.getTokenUser();
    Case ca = caseService.selectByPrimaryKey(param.getCaseId());
    if (ca == null || !Objects.equals(ca.getOrgId(), userSession.getOrgId())) {
      throw new ApiException("找不到数据");
    }
    CaseCtrlCustom caseCtrlCustom = caseCtrlCustomMapper.selectByPrimaryKey(param.getCustomId());
    if (caseCtrlCustom == null) {
      throw new ApiException("该自定义管控内容已被删除，请刷新页面");
    }
    Date now = new Date();
    CaseCtrlCustomRel caseCtrlCustomRel = new CaseCtrlCustomRel();
    List<CaseCtrlCustomRel> caseCtrlCustomRelList = caseCtrlCustomRelMapper.selectByCaseCustomPairs(Collections.singletonList(param.getCustomId()), param.getCaseId());
    if (!CollectionUtils.isEmpty(caseCtrlCustomRelList)) {
      caseCtrlCustomRel.setId(caseCtrlCustomRelList.get(0).getId());
      caseCtrlCustomRel.setUpdateBy(userSession.getId());
      caseCtrlCustomRel.setUpdateTime(now);
      caseCtrlCustomRel.setFinished(param.getFinished());
      caseCtrlCustomRel.setNote(param.getNote());
      caseCtrlCustomRelMapper.updateByPrimaryKeySelective(caseCtrlCustomRel);
    } else {
      caseCtrlCustomRel.setCaseId(param.getCaseId());
      caseCtrlCustomRel.setCtrlCustomId(param.getCustomId());
      caseCtrlCustomRel.setCreateBy(userSession.getId());
      caseCtrlCustomRel.setUpdateBy(userSession.getId());
      caseCtrlCustomRel.setCreateTime(now);
      caseCtrlCustomRel.setUpdateTime(now);
      caseCtrlCustomRel.setFinished(param.getFinished());
      caseCtrlCustomRel.setNote(param.getNote());
      caseCtrlCustomRelMapper.insert(caseCtrlCustomRel);
    }
    return now.getTime();
  }

  public CheckCaseCtrlVO checkCaseCtrl(Long caseId, Long contactId) {
    CheckCaseCtrlVO checkCaseCtrlVO = new CheckCaseCtrlVO();
    //默认检查通过
    checkCaseCtrlVO.setCheckResult(0);

    UserSession userSession = UserUtils.getTokenUser();
    Assert.notNull(userSession.getOrgId(), "公司id不能为空");
    Assert.notNull(caseId, "案件id不能为空");
    Assert.notNull(contactId, "联系人id不能为空");
    Case ca = caseService.selectByPrimaryKey(caseId);
    if (ca == null || !Objects.equals(ca.getOrgId(), userSession.getOrgId())) {
      throw new ApiException("找不到数据");
    }
    Contacts contacts = contactsService.selectByPrimaryKey(contactId);
    if (contacts == null || !Objects.equals(contacts.getOrgId(), userSession.getOrgId())) {
      throw new ApiException("找不到数据");
    }
    if (ca.getDebtId() == null && !caseId.equals(contacts.getRelId())) {
      throw new ApiException("联系人与案件不匹配");
    }
    if (ca.getDebtId() != null && !ca.getDebtId().equals(contacts.getRelId())) {
      throw new ApiException("联系人与案件不匹配");
    }
    OrgSwitch orgSwitch = this.orgSwitchMapper.selectOrgSwitchByOrgId(UserUtils.getTokenUser().getOrgId());
    if (!OrgSwitchEnums.CaseCtrlSwitch.OPEN.getCode().equals(orgSwitch.getCtrlSwitch())) {
      //公司总开关未开启,直接验证通过
      return checkCaseCtrlVO;
    }
    Long ctrlId = ca.getCtrlId();
    if (ctrlId == null) {
      //案件不属于任何管控池,直接验证通过
      return checkCaseCtrlVO;
    }
    CaseCtrlPool caseCtrlPool = this.selectByPrimaryKey(ctrlId);
    if (caseCtrlPool == null || !Objects.equals(caseCtrlPool.getEnable(), 1)) {
      //案件管控池未开启,直接验证通过
      return checkCaseCtrlVO;
    }
    if (Objects.equals(contacts.getOwnSign(), 1)) {
      //如果呼叫对象为本人
      if (!Objects.equals(caseCtrlPool.getDebtorOwn(), 1)) {
        //债务人限制呼出
        checkCaseCtrlVO.setCheckResult(3);
        return checkCaseCtrlVO;
      }
      //计算本人天数限制内的呼叫次数
      Date startDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1 - caseCtrlPool.getOwnLimitDay()));
      Integer ownTimesDay = caseOperationService.selectCountByCaseAndDay(caseId, startDate, 0, null);
      if (ownTimesDay >= caseCtrlPool.getOwnLimitDayTime()) {
        checkCaseCtrlVO.setCheckResult(1);
        checkCaseCtrlVO.setCallTimesDay(caseCtrlPool.getOwnLimitDayTime());
        return checkCaseCtrlVO;
      }
      //计算本人小时限制内的呼叫次数
      Date startTime = DateUtil.beginOfHour(DateUtil.offsetHour(new Date(), 1 - caseCtrlPool.getOwnLimitHour()));
      Integer ownTimesHour = caseOperationService.selectCountByCaseAndDay(caseId, startTime, 0, null);
      if (ownTimesHour >= caseCtrlPool.getOwnLimitHourTime()) {
        checkCaseCtrlVO.setCheckResult(2);
        checkCaseCtrlVO.setLimitDay(caseCtrlPool.getOwnLimitDay());
        checkCaseCtrlVO.setLimitDayTime(caseCtrlPool.getOwnLimitDayTime());
        checkCaseCtrlVO.setLimitHour(caseCtrlPool.getOwnLimitHour());
        checkCaseCtrlVO.setLimitHourTime(caseCtrlPool.getOwnLimitHourTime());
        return checkCaseCtrlVO;
      }
    } else {
      //如果呼叫对象为联系人
      if (!Objects.equals(caseCtrlPool.getContacts(), 1)) {
        //联系人限制呼出
        checkCaseCtrlVO.setCheckResult(4);
        return checkCaseCtrlVO;
      }
      //如果呼叫限制为同一号码，则统计同一号码的呼叫次数，否则统计所有联系人的呼叫次数
      String conMobile = Objects.equals(caseCtrlPool.getConLimitType(), 1) ? contacts.getMobile() : null;

      //计算联系人天数限制内的呼叫次数
      Date startDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1 - caseCtrlPool.getConLimitDay()));
      Integer conTimesDay = caseOperationService.selectCountByCaseAndDay(caseId, startDate, 1, conMobile);
      if (conTimesDay >= caseCtrlPool.getConLimitDayTime()) {
        checkCaseCtrlVO.setCheckResult(1);
        checkCaseCtrlVO.setCallTimesDay(caseCtrlPool.getConLimitDayTime());
        return checkCaseCtrlVO;
      }
      if (Objects.equals(caseCtrlPool.getConLimitType(), 1)) {
        //计算联系人小时限制内的呼叫次数
        Date startTime = DateUtil.beginOfHour(DateUtil.offsetHour(new Date(), 1 - caseCtrlPool.getConLimitHour()));
        Integer conTimesHour = caseOperationService.selectCountByCaseAndDay(caseId, startTime, 1, conMobile);
        if (conTimesHour >= caseCtrlPool.getConLimitHourTime()) {
          checkCaseCtrlVO.setCheckResult(2);
          checkCaseCtrlVO.setLimitDay(caseCtrlPool.getConLimitDay());
          checkCaseCtrlVO.setLimitDayTime(caseCtrlPool.getConLimitDayTime());
          checkCaseCtrlVO.setLimitHour(caseCtrlPool.getConLimitHour());
          checkCaseCtrlVO.setLimitHourTime(caseCtrlPool.getConLimitHourTime());
          return checkCaseCtrlVO;
        }
      }
    }
    return checkCaseCtrlVO;
  }

  /**
   * 查询电催达标提醒
   *
   * @param ctrlId 管控池id
   * @param caseId 案件id
   */
  public Integer queryCaseCtrlCaution (Long ctrlId, Long caseId) {
    if (ctrlId == null || caseId == null) {
      return null;
    }
    Integer caseCtrlCaution = 0;
    CaseCtrlPool caseCtrlPool = this.selectByPrimaryKey(ctrlId);
    if (ObjectUtil.equals(0,caseCtrlPool.getEnable())) {
      return null;
    }
    Integer ownLimitDayLeastTime = caseCtrlPool.getOwnLimitDayLeastTime() == null ? 0 : caseCtrlPool.getOwnLimitDayLeastTime();
    Integer conLimitDayLeastTime = caseCtrlPool.getConLimitDayLeastTime() == null ? 0 : caseCtrlPool.getConLimitDayLeastTime();
    if (ownLimitDayLeastTime == 0 && conLimitDayLeastTime == 0 ) {
      return null;
    }

    // 管控池勾选了本人&&联系人
    if (Objects.equals(caseCtrlPool.getDebtorOwn(), 1) && Objects.equals(caseCtrlPool.getContacts(), 1)) {
      Integer ownLimitDay = caseCtrlPool.getOwnLimitDay();
      Integer ownDayTime = 0;
      if (ownLimitDay != null) {
        //本人呼叫次数
        Date date = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1 - ownLimitDay));
        ownDayTime = caseOperationService.selectCountByCaseAndDay(caseId, date, 0, null);
      }

      Integer conDayTime = 0;
      if (Objects.equals(caseCtrlPool.getConLimitType(), 0)) {
        Integer contactLimitDay = caseCtrlPool.getConLimitDay();
        if (contactLimitDay != null) {
          //联系人呼叫次数
          Date date = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1 - contactLimitDay));
          conDayTime =  caseOperationService.selectCountByCaseAndDay(caseId, date, 1,null);
        }
      }

      if (ownLimitDayLeastTime <= ownDayTime && conLimitDayLeastTime <= conDayTime) {
        caseCtrlCaution = 1;
      }
    }
    // 管控池仅勾选了本人
    if (Objects.equals(caseCtrlPool.getDebtorOwn(), 1) && Objects.equals(caseCtrlPool.getContacts(), 0)) {
      //电催完成情况
      Integer ownLimitDay = caseCtrlPool.getOwnLimitDay();
      Integer ownDayTime = 0;
      if (ownLimitDay != null) {
        //本人呼叫次数
        Date date = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1 - ownLimitDay));
        ownDayTime = caseOperationService.selectCountByCaseAndDay(caseId, date, 0, null);
      }
      if (ownLimitDayLeastTime <= ownDayTime ) {
        caseCtrlCaution = 1;
      }
    }
    // 管控池勾选了联系人且全部号码
    if (Objects.equals(caseCtrlPool.getDebtorOwn(), 0) && Objects.equals(caseCtrlPool.getContacts(), 1)
            && Objects.equals(caseCtrlPool.getConLimitType(), 0)) {
      Integer contactLimitDay = caseCtrlPool.getConLimitDay();
      Integer conDayTime = 0;
      if (contactLimitDay != null) {
        //联系人呼叫次数
        Date date = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1 - contactLimitDay));
        conDayTime =  caseOperationService.selectCountByCaseAndDay(caseId, date, 1,null);
      }
      if (conLimitDayLeastTime <= conDayTime) {
        caseCtrlCaution = 1;
      }
    }

    return caseCtrlCaution;
  }
}
