package com.anmi.collection.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.common.enums.template.TemplateFieldEnums;
import com.anmi.collection.constant.FiletStoreConstants;
import com.anmi.collection.constant.RegexConstant;
import com.anmi.collection.constant.SmsContants;
import com.anmi.collection.constant.SystemConst;
import com.anmi.collection.dto.fileStorage.UploadCreatedFileInfo;
import com.anmi.collection.dto.fileStorage.UploadFileInfo;
import com.anmi.collection.entity.requset.file.FileUpload;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.filestore.FileStoreVO;
import com.anmi.collection.entity.response.template.TemplateFieldVO;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.exception.OpenException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.manager.YunpianManager;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.mapper.CaseTemplateMapper;
import com.anmi.collection.mapper.FileStoreMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.scheduled.async.ExportExcelTask;
import com.anmi.collection.service.bi.BiStatisticsService;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.service.template.TemplateFieldService;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.*;
import com.anmi.collection.utils.easyexcel.EasyExcelUtils;
import com.anmi.collection.utils.easyexcel.ErrorRowWriteHandler;
import com.anmi.domain.base.DictionaryEntity;
import com.anmi.domain.base.JsonField;
import com.anmi.domain.cases.*;
import com.anmi.domain.principal.Delt;
import com.anmi.domain.sys.FileStore;
import com.anmi.domain.user.*;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/** Created by dongwang on 2018-12-06. */
@Service
@Slf4j
public class FileStoreService extends BaseService<FileStore> {
  @Autowired private CaseTemplateMapper caseTemplateMapper;
  @Autowired private FileStoreMapper fileStoreMapper;
  @Autowired private CaseMapper caseMapper;
  @Autowired private DeltService deltService;
  @Autowired private UserService userService;
  @Autowired private ProductService productService;
  @Autowired private RepaymentService repaymentService;
  @Autowired private ContactsService contactsService;
  @Autowired private ApplicationContext applicationContext;
  @Autowired private ExportExcelTask exportExcelTask;
  @Autowired private StringRedisTemplate stringRedisTemplate;
  @Autowired private CaseService caseService;
  @Autowired private CaseTemplateService caseTemplateService;
  @Autowired private CaseOperationService caseOperationService;
  @Autowired private OutBatchService outBatchService;
  @Autowired private InnerBatchService innerBatchService;
  @Autowired private CaseDebtorService caseDebtorService;
  @Autowired private AsyncTaskService asyncTaskService;
  @Autowired private CaseLogService caseLogService;
  @Autowired private CompanyService companyService;
  @Autowired private RestTemplate restTemplate;
  @Autowired private UserStatisticsService userStatisticsService;
  @Autowired private DepTeamService depTeamService;
  @Autowired private RedisUtil redisUtil;
  @Autowired private BiStatisticsService biStatisticsService;
  @Autowired private MediationStatisticsService mediationStatisticsService;
  @Autowired private ContactTypeConfigService contactTypeConfigService;
  @Autowired private RepayPlanService repayPlanService;
  @Autowired private PromissoryNoteService promissoryNoteService;
  @Autowired private TemplateFieldService templateFieldService;
  @Autowired private FileStorageStrategyFactory fileStorageStrategyFactory;
  @Autowired private RoleService roleService;
  @Autowired private UserRoleService userRoleService;
  @Autowired private YunpianManager yunpianManager;
  @Autowired private AccountLogService accountLogService;

  // 模版文件校验和上传
  public FileStore uploadFile(FileUpload fileUpload, MultipartFile file) throws Exception {
    if (ObjectUtil.equals(fileUpload.getTemplateId(),0L) && ObjectUtil.isNull(fileUpload.getTemplateType())){
      throw new ApiException("模版类型为空");
    }
    Integer tempType;
    if (ObjectUtil.equals(fileUpload.getTemplateId(),0L)){
      tempType = fileUpload.getTemplateType();
    } else {
      CaseTemplate caseTemplate = caseTemplateMapper.selectByid(fileUpload.getTemplateId());
      if (caseTemplate == null) {
        throw new ApiException("无法找到对应案件模版");
      }
      tempType = caseTemplate.getTempType();
    }

    if (Objects.equals(tempType, CaseTemplateEnums.TempType.REPAYMENT_IMPORT.getCode())) {
      repaymentService.verifyTime();
    }
    // 彻底删除和导入不能同时进行
    UserSession userSession = UserUtils.getTokenUser();
    if (tempType.equals(CaseTemplateEnums.TempType.CASE_BASE_IMPORT.getCode())
        && asyncTaskService.isHaveCaseDelTask(userSession.getOrgId())) {
      throw new ApiException("案件导入和彻底删除不能同时进行！");
    }
    if (CaseTemplateEnums.TempType.CASE_BASE_IMPORT.getCode() == tempType
        && StringUtils.isBlank(fileUpload.getOutBatchNo())) {
      throw new ApiException("委案方批次号不能为空");
    }
    // 文件校验
    String fileName = file.getOriginalFilename();
    if (!ExcelUtil.isExcelFile(fileName)) {
      throw new ApiException("文件必须是excel格式");
    }
    if (file.isEmpty()) {
      throw new ApiException("文件不能为空");
    }

    final int intValue = Long.valueOf(file.getSize()).intValue();
    if (intValue > FiletStoreConstants.uploadSizeLimit) {
      throw new ApiException("文件大小已超过20M，请拆分文件后再上传");
    }
    if (tempType == SystemConst.TEMPLATE_TYPE_CASE_BASE) {
      // 是通用基础模版
      Delt delt = deltService.selectByPrimaryKey(fileUpload.getOrgDeltId());
      if (delt.getStatus() != 0 && delt.getStatus() != 1) {
        throw new ApiException("委案公司不在合作中，请联系客服！");
      }
    }
    try {
      List<String> cellNameList = caseTemplateService.getTemplateNames(fileUpload.getTemplateId(),tempType,userSession.getOrgId(),userSession.getLanguage());
      if (EasyExcelUtils.judgeIsEmpty(file, tempType, cellNameList, userSession.getLanguage())) {
        throw new ApiException("文件为空模板");
      }
      // 上传文件
      fileName =
          StringUtils.substringBeforeLast(fileName, ".")
              + "_"
              + StringUtils.getRandomNumberBIT6()
              + "."
              + StringUtils.substringAfterLast(fileName, ".");
      Date expireDate = DateUtils.addDays(new Date(), 3);
      UploadFileInfo uploadFileInfo = new UploadFileInfo();
      uploadFileInfo.setFile(file)
              .setFileName(fileName)
              .setExpireDate(expireDate)
              .setBucket(systemConfig.getTemporaryFileBucket())
              .setLocalUrl(systemConfig.getLocalFilePath()
                      + DateUtils.formatDate(new Date())
                      + File.separator
                      + fileName);
      FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
      String path = fileStorageStrategy.uploadFile(uploadFileInfo);

      // 组装返回参数
      FileStore store = new FileStore();
      store.setTemplateType(tempType);
      store.setOrgDeltId(fileUpload.getOrgDeltId());
      store.setName(fileName);
      store.setEntrustStartTime(
          fileUpload.getEntrustStart() == null
              ? null
              : DateUtils.getStartTimeOfDate(fileUpload.getEntrustStart()));
      store.setEntrustEndTime(
          fileUpload.getEntrustEnd() == null
              ? null
              : DateUtils.getStartTimeOfDate(fileUpload.getEntrustEnd()));
      store.setType(0);
      store.setProductId(fileUpload.getProductId());
      store.setTemplateId(fileUpload.getTemplateId());
      store.setPath(path);
      // store.setTotal(total);
      UserSession tokenUser = UserUtils.getTokenUser();
      store.setOrgId(tokenUser.getOrgId());
      if (UserUtils.likeBranchAdmin()) {
        store.setDepId(tokenUser.getDepId());
      }
      store.setOutBatchNo(fileUpload.getOutBatchNo());
      store.setCreateBy(tokenUser.getId());
      store.setUpdateBy(tokenUser.getId());
      store.setCreateTime(new Date());
      store.setUpdateTime(new Date());
      store.setExpireTime(expireDate);
      store.setConjointType(fileUpload.getConjointType());
      return store;
    } catch (Exception e) {
      log.error("文件解析异常", e);
      throw new ApiException("文件解析异常:" + e.getMessage());
    }
  }

  public String uploadFile(MultipartFile file) {
    String fileName = file.getOriginalFilename();
    if (file.isEmpty()) {
      throw new ApiException("文件不能为空");
    }
    try {
      //如果是本地化的公司调用saas接口，上传到oss
      if (systemConfig.getLocalDeploy()) {
        Long orgId = UserUtils.getTokenUser().getOrgId();
        Company company = companyService.selectByPrimaryKey(orgId);
        String apikey = company.getApikey();

        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("multipart/form-data");
        headers.setContentType(type);

        //设置请求体，注意是LinkedMultiValueMap
        //FileSystemResource fileSystemResource = new FileSystemResource(filePath+"/"+fileName);
        Resource fileResource = file.getResource();
        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
        form.add("file", fileResource);
        HttpEntity<MultiValueMap<String, Object>> files = new HttpEntity<>(form, headers);

        String anmiSaasUrlPrefix = systemConfig.getAnmiSaasUrlPrefix();
        String url = anmiSaasUrlPrefix + "/v1/openApi/uploadFile?apiKey=" + apikey;
        String tmp = restTemplate.postForObject(url, files, String.class);
        log.info(tmp);
        JSONObject result=JSON.parseObject(tmp);
        if ("1".equals(String.valueOf(result.get("status")))) {
          return String.valueOf(result.get("data"));
        } else {
          throw new ApiException("文件上传失败");
        }
      }
      else {
        // 上传文件
        fileName =
            StringUtils.substringBeforeLast(fileName, ".")
                + "_"
                + StringUtils.getRandomNumberBIT6()
                + "."
                + StringUtils.substringAfterLast(fileName, ".");
        Date expireDate = DateUtils.addDays(new Date(), 3);
        String path = "";
        path = OSSClientUtil.putFile(fileName, expireDate, systemConfig.getMessageFileBucket(), file);
        log.info("文件已上传至OSS。。。 文件名为：{} 地址为：{}", fileName, path);
        return path;
      }
    } catch (Exception e) {
      log.error("文件解析异常", e);
      throw new ApiException("文件解析异常:" + e.getMessage());
    }
  }

  public FileStoreVO save(FileStore store, FileUpload fileUpload) throws IOException {
    // 查询
    UserSession user = UserUtils.getTokenUser();
    if (store.getTemplateType() == CaseTemplateEnums.TempType.CASE_BASE_IMPORT.getCode()) {
      exportExcelTask.insertBDB(store, user, fileUpload);
    } else {
      String ipAddr = AuthUtils.getIpAddr(CmUtil.getRequest()).split(",")[0];
      exportExcelTask.insertRedis(store, user, fileUpload,ipAddr);
    }
    store.setIsCreating(0);
    return convertVOS(Collections.singletonList(store)).get(0);
  }

  @Transactional(rollbackFor = Exception.class)
  // 表合并修改
  public List<Case> parallelHandle(
      FileStore store,
      FileUpload fileUpload,
      UserSession user,
      List<List<String>> lines,
      Map<String, DictionaryEntity> commonFields,
      Map<Long, Set<String>> searchKeyMap,
      Map<String, Long> searchFieldIdMap,
      Map<String, String> tmplFields,
      Set<String> insertOutSerialNoSet) {
    if (CommonUtils.isEmpty(lines)) {
      return new ArrayList<>();
    }
    List<String> keys = getFieldKeyList(tmplFields);
    // outSerialNo的下标
    Integer outSerialNoIndex = keys.indexOf("out_serial_no");
    // 校验outSerialNo是否存在
    List<String> outSerialNoList = Lists.newArrayList();
    for (List<String> lineValues : lines) {
      String outSerialNo = lineValues.get(outSerialNoIndex);
      if (StringUtils.isNotBlank(outSerialNo)) {
        outSerialNoList.add(outSerialNo.trim() + "#" + store.getOrgDeltId());
      }
    }
    // 已存在案件编号
    List<String> existOutSerialNoList = caseService.getExistOutSerialNoList(outSerialNoList);
    // 插入case表
    List<Case> cases = new ArrayList<>();
    // 第一个开始过滤
    List<String> fieldNames = CmUtil.getClazzFieldNames(Case.class);
    List<Case> logCase=new ArrayList<>();
    // 查找催员
    List<User> dunnerList;
    if(Objects.equals(user.getIsBank(), CompanyEnums.BankAmc.YES.getCode())){
      dunnerList = userService.getBankDunnerList(user);
    } else {
      dunnerList = userService.getDunnerList(user);
    }
    Map<String,User> orgUserMap = dunnerList
            .stream().collect(Collectors.toMap(User::getUserNo, orgUser -> orgUser));
    for (int i = 0; i < lines.size(); i++) {
      List<String> vals = lines.get(i);
      String uuid = StringUtils.getSerialNo();
      Case aCase =
          getCase(
              store.getId(),
              tmplFields,
              vals,
              user,
              store.getOrgDeltId(),
              existOutSerialNoList,
              insertOutSerialNoSet,
              commonFields,
              fieldNames,orgUserMap);
      if (aCase == null) {
        continue;
      }
      insertOutSerialNoSet.add(aCase.getOutSerialNo());
      // 设置自定义字段搜索枚举
      setSearchKeys(aCase, searchKeyMap, searchFieldIdMap);
      // 获取委案批次号和内部批次号
      Long outBatchId = getOutBatchId(store, fileUpload);
      Long innerBatchId = getInnerBatchId(store, fileUpload);
      // 设置case
      aCase.setSerialNo(uuid);
      aCase.setEntrustStartTime(store.getEntrustStartTime());
      aCase.setEntrustEndTime(store.getEntrustEndTime());
      aCase.setOperStatus(CaseOperationEnums.ActionType.UNTREATED.getCode());
      aCase.setRecovery((byte) CaseEnums.Recovery.NORMAL.getCode());
      aCase.setOperationState(CaseOperationEnums.State.CONTACT_ONESELF.getCode());
      aCase.setSyncStatus(0);
      aCase.setColor(CaseEnums.Color.NOCOLOR.getCode());
      aCase.setIgnorePlan(CaseEnums.IgnorePlan.NO.getCode());
      // 设置一些业务ID，之前是caseRel的
      aCase.setProductId(store.getProductId());
      aCase.setOrgDeltId(store.getOrgDeltId());
      aCase.setStoreId(store.getId());
      aCase.setOrgId(user.getOrgId());
      aCase.setOutBatchId(outBatchId);
      aCase.setInnerBatchId(innerBatchId);
      aCase.setCooperationStatus(CaseEnums.CooperationStatus.NO.getCode());
      aCase.setCaseStatus(CaseEnums.CaseStatus.NORMAL.getCode());
      if (aCase.getAllotStatus() == CaseEnums.AllotStatus.NOT_ALLOT.getCode()) {
        if (user.getToken() != null && Objects.nonNull(user.getRoleId()) && UserUtils.likeBranchAdmin(user.getRoleId())) {
          aCase.setDepId(user.getDepId());
        } else {
          aCase.setDepId(null);
        }
        aCase.setTeamId(null);
      }else {
        logCase.add(aCase);
      }
      if (Objects.isNull(aCase.getAllotAgentState())) {
        aCase.setAllotAgentState(CaseEnums.AllotAgentState.NO.getCode());
      }
//      aCase.setWayAllotState(CaseEnums.WayAllotState.NO.getCode());
      aCase.setImportant(CaseEnums.Important.NO.getCode());
      aCase.setRecycleFlag(CaseEnums.RecycleFlag.NO.getCode());
      aCase.setOutsourceCount(0l);
      aCase.setIsVisit(CaseEnums.Visit.NO_VISIT.getCode());
      cases.add(aCase);
    }
    if (CommonUtils.isEmpty(cases)) {
      return cases;
    }
    // 新插入案件债务人
    createCaseDebtors(cases, fileUpload.getConjointType(), user.getOrgId());
    // 插入案件
    caseMapper.insertList(cases);
    // 插入通讯录
    createContacts(cases, user);
    if (systemConfig.getLocalDeploy()) {
      for (Case ca : cases) {
        if (!ca.getFieldJson().isEmpty()) {
          caseMapper.insertOrUpdateCaseInfoField(ca.getId(), ca.getFieldJson());
        }
      }
    }
    if(!CommonUtils.isEmpty(logCase)){
      CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
      caseBatchUpdateEvent.setType(CaseLogEnums.Type.ALLOT.getCode());
      caseBatchUpdateEvent.setUserSession(user);
      caseBatchUpdateEvent.setCases(logCase);
      applicationContext.publishEvent(caseBatchUpdateEvent);
    }
    // 导入地址
    return cases;
  }

  public void createCaseDebtors(List<Case> caseList, Integer type, Long orgId) {
    Map<String, List<Case>> noneCaseMap = new HashMap<>();
    Map<String, List<Case>> allCaseMap = new HashMap<>();
    if (OrgEnums.ConjointType.NO.getCode().equals(type)) {
      return;
    } else if (OrgEnums.ConjointType.ID_CARD.getCode().equals(type)) {
      allCaseMap = caseList.stream().collect(Collectors.groupingBy(Case::getIdCard));
    } else {
      allCaseMap = caseList.stream().collect(Collectors.groupingBy(Case::getOwnMobile));
    }
    // 找到已存在的债务人
    Map<String, Long> existDebtorMap =
        caseDebtorService.selectByUniqueCode(orgId, allCaseMap.keySet(), type);
    for (Map.Entry<String, List<Case>> entry : allCaseMap.entrySet()) {
      String uniqueCode = entry.getKey();
      List<Case> list = entry.getValue();
      Long debtorId = existDebtorMap.get(uniqueCode);
      if (debtorId != null) {
        // 如果存在，则直接关联
        list.stream().forEach(c -> c.setDebtId(debtorId));
      } else {
        noneCaseMap.put(uniqueCode, list);
      }
    }
    if (CommonUtils.isEmpty(noneCaseMap)) {
      return;
    }
    // 债务人不存在，则新插入
    List<CaseDebtor> newCaseDebtorList = new ArrayList<>();
    for (String key : noneCaseMap.keySet()) {
      CaseDebtor debtor = new CaseDebtor();
      debtor.setOrgId(orgId);
      debtor.setFollowCount(0);
      debtor.setLastFollowTime(null);
      debtor.setStatus(0);
      debtor.setType(type);
      debtor.setUniqueCode(key);
      newCaseDebtorList.add(debtor);
    }
    caseDebtorService.insertBatch(newCaseDebtorList);
    Map<String, Long> newDebtorIdMap =
        newCaseDebtorList.stream()
            .collect(Collectors.toMap(CaseDebtor::getUniqueCode, c -> c.getId()));
    for (Map.Entry<String, List<Case>> entry : noneCaseMap.entrySet()) {
      String uniqueCode = entry.getKey();
      List<Case> list = entry.getValue();
      Long debtorId = newDebtorIdMap.get(uniqueCode);
      // 直接关联
      list.stream().forEach(c -> c.setDebtId(debtorId));
    }
  }

  public void createContacts(List<Case> caseList, UserSession userSession) {
    List<Contacts> contactsList = new ArrayList<>();
    Long orgId = userSession.getOrgId();
    Long contactTypeId = contactTypeConfigService.selectSysSelf(orgId);
    for (Case caseInfo : caseList) {
      Contacts con = new Contacts();
      con.setRelationType("本人");
      con.setContactTypeId(contactTypeId);
      con.setOwnSign(1);
      con.setMobile(caseInfo.getOwnMobile());
      con.setOrgId(userSession.getOrgId());
      con.setCreateBy(userSession.getId() == null ? 0l : userSession.getId());
      con.setUpdateBy(userSession.getId() == null ? 0l : userSession.getId());
      con.setCreateTime(new Date());
      con.setUpdateTime(new Date());
      con.setName(caseInfo.getName());
      con.setStatus(0);
      con.setSign(0);
      con.setWarning(0);
      if (caseInfo.getDebtId() != null) {
        // 共债关联
        con.setRelId(caseInfo.getDebtId());
        con.setIsConjoint(Contacts.IsConjoint.YES.getCode());
      } else {
        // 非共债
        con.setRelId(caseInfo.getId());
        con.setIsConjoint(Contacts.IsConjoint.NO.getCode());
      }
      contactsList.add(con);
    }
    contactsService.batchUpdateContact(contactsList);
  }

  private Long getOutBatchId(FileStore store, FileUpload fileUpload) {
    if (store.getOutBatchId() != null) {
      return store.getOutBatchId();
    }
    Long outBatchId = null;
    if (StringUtils.isNotBlank(fileUpload.getOutBatchNo())) {
      Example example = new Example(OutBatch.class);
      example
          .createCriteria()
          .andEqualTo("orgId", store.getOrgId())
          .andEqualTo("orgDeltId", store.getOrgDeltId())
          .andEqualTo("name", fileUpload.getOutBatchNo());
      List<OutBatch> list = outBatchService.selectByExample(example);
      if (!com.anmi.collection.utils.CollectionUtils.isEmpty(list)) {
        outBatchId = list.get(0).getId();
        store.setOutBatchId(outBatchId);
        updateByPrimaryKeySelective(store);
        return list.get(0).getId();
      } else {
        outBatchId =
            outBatchService.createDeltBatchNo(
                fileUpload.getOutBatchNo(),
                store.getOrgId(),
                store.getOrgDeltId(),
                store.getCreateBy());
      }
    }
    return outBatchId;
  }

  private Long getInnerBatchId(FileStore store, FileUpload fileUpload) {
    if (store.getInnerBatchId() != null) {
      return store.getInnerBatchId();
    }
    Long innerBatchId = null;
    if (StringUtils.isNotBlank(fileUpload.getInnerBatchNo())) {
      Example example = new Example(InnerBatch.class);
      example
          .createCriteria()
          .andEqualTo("orgId", store.getOrgId())
          .andEqualTo("name", fileUpload.getInnerBatchNo());
      List<InnerBatch> list = innerBatchService.selectByExample(example);
      if (!com.anmi.collection.utils.CollectionUtils.isEmpty(list)) {
        innerBatchId = list.get(0).getId();
        store.setInnerBatchId(innerBatchId);
        updateByPrimaryKeySelective(store);
      } else {
        innerBatchId =
            innerBatchService.createInnerBatchNo(
                fileUpload.getInnerBatchNo(), store.getOrgId(), store.getCreateBy());
      }
    }
    return innerBatchId;
  }

  /**
   * 设置自定义搜索字段枚举
   *
   * @param c
   * @param searchKeyMap
   * @param searchFieldIdMap 一定是基于某个org下的结果 并且是模版里有的搜索字段
   */
  private void setSearchKeys(
      Case c, Map<Long, Set<String>> searchKeyMap, Map<String, Long> searchFieldIdMap) {
    Map<String, String> json = c.getFieldJson();
    // 找到所有以search_key开头的
    for (Map.Entry<String, String> entry : json.entrySet()) {
      String key = entry.getKey();
      String val = entry.getValue();
      if (key.startsWith("search_key")) {
        if (StringUtils.isBlank(val)) {
          continue;
        }
        Long id = searchFieldIdMap.get(key);
        if (searchKeyMap.containsKey(id)) {
          Set<String> vals = searchKeyMap.get(id);
          if (!StringUtils.isBlank(val)) {
            vals.add(val);
          }
        } else {
          Set<String> vals = new HashSet<>();
          vals.add(val);
          searchKeyMap.put(id, vals);
        }
      }
    }
  }

  private Contacts getContactInfo(
          Long storeId,
          List<String> values,
          UserSession user,
          Long parentOrgId,
          Long relId,
          Boolean isConjoint,
          Map<String, Long> contactTypeMap) {
    List<Map<String, Object>> errorList = new ArrayList<>();
    // 有可能解析出来的数据不足6个导致后面报数组越界
    int size = values.size();
    while (size < 6) {
      values.add(null);
      size++;
    }
    if (relId == null) {
      // 委案公司、案件编号必须存在
      errorList.add(buildColumnError(0, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
      errorList.add(buildColumnError(1, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
    }
    Contacts con = new Contacts();
    con.setOrgId(parentOrgId);
    con.setCreateTime(new Date());
    con.setUpdateTime(new Date());
    con.setCreateBy(user.getId());
    con.setUpdateBy(user.getId());
    con.setStatus(0);
    con.setRelId(relId);
    con.setIsConjoint(isConjoint ? 1 : 0);
    // 对手机号码格式进行判断
    String mobile = values.get(2);
    if (StringUtils.isBlank(mobile)) {
      errorList.add(buildColumnError(2, FileStoreEnums.ColumnErrorCode.NOT_BLANK));
    } else {
      mobile = StringUtils.trimAnyBlank(mobile);
      boolean isMatch = RegexConstant.isMatch(RegexConstant.REGEX_PHONE, mobile);
      if (!isMatch) {
        errorList.add(buildColumnError(2, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
      }
      con.setMobile(mobile);
    }

    // 姓名
    String name = values.get(3);
    if (StringUtils.isBlank(name)) {
      con.setName("");
    } else {
      con.setName(name.trim());
      // 不为空并且长度要小于100
      if (name.trim().length() > 100) {
        errorList.add(buildColumnError(3, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
      }
    }

    // 关系
    String relationType = values.get(4);
    if (StringUtils.isBlank(relationType)) {
      con.setRelationType("");
    } else {
      con.setRelationType(relationType.trim());
      if (relationType.trim().length() > 100) {
        errorList.add(buildColumnError(4, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
      }
    }
    //号码类型为本人，联系人标志都是1，其余为0
    con.setOwnSign(0);
    // 号码类型
    String contactTypeName = values.get(5);
    if (contactTypeMap.containsKey(contactTypeName)) {
      con.setContactTypeId(contactTypeMap.get(contactTypeName));
      if (ContactTypeConfigEnums.IsSys.isSelf(contactTypeName)) {
        con.setOwnSign(1);
      }
    }
    if (!CollectionUtils.isEmpty(errorList)) {
      // 错误信息保存到redis
      JSONArray valueJson = JSONArray.parseArray(JSON.toJSONString(values));
      valueJson.add(errorList);
      redisUtil.lSet(KeyCache.FILE_CONTACTS_ERROR_LIST + storeId, JSONArray.toJSONString(valueJson), 24*60*60);
      return null;
    }
    return con;
  }

  private boolean invoke(Object ca, String name, String value, String type, Integer maxLength) {
    name = StringUtils.upperTable(name);
    String methodName = "set" + name.substring(0, 1).toUpperCase() + name.substring(1);
    try {
      if (!type.equals("Money")
          && maxLength != null
          && maxLength > 0
          && value != null
          && value.length() > maxLength) {
        log.info("maxLength:{},value:{},字段长度{}", maxLength, value, value.length());
        return false;
      }
      Method method = null;
      if (StringUtils.isEmpty(name.trim())) {
        return false;
      }
      if (type.equals("String")) {
        method = getMothod(methodName, ca.getClass(), String.class);
        if (method == null) {
          return false;
        }
        method.invoke(ca, value);
      } else if (type.equals("Double")) {
        method = getMothod(methodName, ca.getClass(), Double.class);
        Double objVal = Double.valueOf(value);
        method.invoke(ca, objVal);
      } else if (type.equals("Integer")) {
        method = getMothod(methodName, ca.getClass(), Integer.class);
        if (method == null) {
          return false;
        }
        method.invoke(ca, Integer.valueOf(value));
      } else if (type.equals("Date")) {
        method = getMothod(methodName, ca.getClass(), Date.class);
        if (method == null) {
          return false;
        }
        Date date = DateUtils.convertToDate(value);
        if (date == null) {
          return false;
        }
        // date如果不在1971年和2100年之间，就返回false
        if (date.compareTo(FiletStoreConstants.beginTime) < 0
            || date.compareTo(FiletStoreConstants.endTime) > 0) {
          return false;
        }
        method.invoke(ca, date);
      } else if (type.equals("Long")) {
        method = getMothod(methodName, ca.getClass(), Long.class);
        if (method == null) {
          return false;
        }
        Long longValue = Long.valueOf(value);
        method.invoke(ca, longValue);
      } else if (type.equals("Money")) {
        try {
          Long objVal = CmUtil.getAmountLong(new BigDecimal(value));
          if (maxLength != null && maxLength > 0 && String.valueOf(objVal).length() > maxLength) {
            log.info("maxLength:{},value:{},字段长度{}", maxLength, objVal, String.valueOf(objVal).length());
            return false;
          }
          method = getMothod(methodName, ca.getClass(), Long.class);
          method.invoke(ca, objVal);
        } catch (NoSuchMethodException e) {
          method = getMothod(methodName, ca.getClass(), BigDecimal.class);
          method.invoke(ca, new BigDecimal(value));
        }
      }
    } catch (Exception e) {
      log.error("解析异常：字段【" + name + "】,值【" + value + "】,异常信息：" + e.getMessage(), e);
      return false;
    }
    return true;
  }

  private Method getMothod(String methodName, Class clz, Class paramType)
      throws NoSuchMethodException {
    Method method = null;
    if (paramType != null) {
      method = clz.getDeclaredMethod(methodName, paramType);
    } else {
      method = clz.getDeclaredMethod(methodName);
    }
    method.setAccessible(true);
    return method;
  }

  private JsonField getJsonField(String name, String type, Integer maxLength, String value) {
    JsonField jsonField = new JsonField();
    jsonField.setKey(name);
    if (type != null && type.equals("Money")) {
      Long newMoney = CmUtil.getAmountLong(new BigDecimal(value));
      if (maxLength != null && maxLength > 0 && String.valueOf(newMoney).length() > maxLength) {
        throw new ApiException("金额超过长度");
      }
      jsonField.setValue(newMoney.toString());
    } else {
      jsonField.setValue(value);
    }
    return jsonField;
  }

  public List<FileStoreVO> convertVOS(List<FileStore> fileStores) {
    if(CollectionUtils.isEmpty(fileStores)){
      return Collections.emptyList();
    }
    List<FileStoreVO> result = new ArrayList<>();
    Map<Long,String> productMap = productService.getNames();
    Map<Long,String> deltMap = deltService.getNames();
    Map<Long,String> userMap=userService.getNames(fileStores.get(0).getOrgId());
    for(FileStore fileStore:fileStores) {
      FileStoreVO fileStoreVO = new FileStoreVO();
      BeanUtils.copyProperties(fileStore, fileStoreVO);
      fileStoreVO.setProductName(productMap.get(fileStore.getProductId()));
      fileStoreVO.setOrgDeltName(deltMap.get(fileStore.getOrgDeltId()));
      fileStoreVO.setCreateBy(userMap.get(fileStore.getCreateBy()));
      fileStoreVO.setUpdateBy(userMap.get(fileStore.getUpdateBy()));
      fileStoreVO.setOutBatchNo(fileStore.getOutBatchNo());
      if (fileStore.getExpireTime() != null) {
        fileStoreVO.setExpireTime(fileStore.getExpireTime().getTime());
      }
      if (fileStore.getIsCreating() == 0) {
        fileStoreVO.setIsCreating(true);
      } else {
        fileStoreVO.setIsCreating(false);
      }
      result.add(fileStoreVO);
    }
    return result;
  }

  public PageOutput<FileStoreVO> getListByPage(FileStore store, PageParam pageParam) {
    // store.setTemplateType(CaseTemplateEnums.TempType.基础信息模版.getCode());
    Map map = new HashMap();
    map.put("orgId", getOrgId());
    if (UserUtils.likeBranchAdmin()) {
      map.put("depId", getTokenUser().getDepId());
    }
    Integer templateType =
        store.getTemplateType() == null
            ? CaseTemplateEnums.TempType.CASE_BASE_IMPORT.getCode()
            : store.getTemplateType();
    map.put("templateType", templateType);
    Page page = super.setPage(pageParam);
    List<FileStore> fileStores = fileStoreMapper.queryByMap(map);
    List<FileStoreVO> list = new ArrayList<>();
    if (!CommonUtils.isEmpty(fileStores)) {
      list = convertVOS(fileStores);
    }
    PageOutput<FileStoreVO> pageOutput =
        new PageOutput<>(
            page.getPageNum(),
            page.getPageSize(),
            page != null ? (int) page.getTotal() : list.size(),
            list);
    return pageOutput;
  }

  /**
   * 返回list，第一个是case，第二个是error，第三个是boolean
   *
   * @param fields
   * @param values
   * @param user
   * @param deltId
   * @return
   */
  private Case getCase(
          Long storeId,
          Map<String, String> fields,
          List<String> values,
          UserSession user,
          Long deltId,
          List<String> existOutSerialNoList,
          Set<String> insertOutSerialNoSet,
          Map<String, DictionaryEntity> commonFields,
          List<String> fieldNames, Map<String, User> orgUserMap) {
    Case ca = new Case();
    ca.setCaseStatus(CaseEnums.CaseStatus.NORMAL.getCode());
    ca.setAllotStatus(CaseEnums.AllotStatus.NOT_ALLOT.getCode());//默认为未分配
    ca.setOrgId(user.getOrgId());
    ca.setWayAllotState(CaseEnums.WayAllotState.NO.getCode());
    List<Map<String, Object>> errorList = new ArrayList<>();
    // 设置必填字段
    Iterator<String> iterator = fields.keySet().iterator();
    Map<String, String> data = new HashMap<>();
    //分公司管理员导入的案件状态设置为分配至分公司
    if(UserUtils.likeBranchAdmin(user.getRoleId())){
      ca.setAllotStatus(CaseEnums.AllotStatus.ALLOT_BRANCH.getCode());
      ca.setDepId(user.getDepId());
    }
    while (iterator.hasNext()) {
      String key = iterator.next();
      Integer index = Integer.valueOf(key);
      String name = fields.get(key);
      String value = "";
      //dunner_id
      if("dunner_code".equals(name)){
        //导入案件中包含催员编号，则自动分案
       if(!CommonUtils.isEmpty(values.get(index))){
          value = values.get(index).trim();
          User dunner= orgUserMap.get(value);
          if(dunner==null){//催员不存在
            errorList.add(buildColumnError(index, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
            continue;
          }else {
            ca.setUserId(dunner.getId());
            ca.setTeamId(dunner.getTeamId());
            ca.setDepId(dunner.getDepId());
            ca.setAllotStatus(CaseEnums.AllotStatus.ALLOT_USER.getCode());
            ca.setDivisionTime(new Date());

            ca.setOperationWay(CaseEnums.OperationWay.DC.getCode());
            ca.setWayAllotState(CaseEnums.WayAllotState.YES.getCode());
            ca.setWayUpdateDate(new Date());

            ca.setAllotAgent(CaseEnums.AllotAgent.NC.getCode());
            ca.setAllotAgentState(CaseEnums.AllotAgentState.YES.getCode());
          }
        }
      }

      if (!CommonUtils.isEmpty(values.get(index))) {
        value = values.get(index).trim(); // 拿到当前字段的值
        if (Arrays.asList("own_mobile","name","id_card","out_serial_no").contains(name)) {
          value = StringUtils.trimAnyBlank(value);
        }
      }
      DictionaryEntity entity = commonFields.get(name);
      // ======== 必填字段 ========
      if (entity != null
          && entity.getRequired() != null
          && "true".equals(entity.getRequired())
          && StringUtils.isBlank(value)) {
        errorList.add(buildColumnError(index, FileStoreEnums.ColumnErrorCode.NOT_BLANK));
        continue;
      }
      if (StringUtils.isBlank(value)) {
        continue;
      }
      if (fieldNames.contains(CmUtil.underline2Camel(name, true))) {
        // case对象内参数
        if (name.equals("out_serial_no")) {
          value = value + "#" + deltId;
          if (existOutSerialNoList.contains(value) || insertOutSerialNoSet.contains(value)) {
            // 存在该序列号
            errorList.add(buildColumnError(index, FileStoreEnums.ColumnErrorCode.NOT_REPEAT));
            continue;
          }
        }
        if (!checkCaseFields(name, value)) {
          errorList.add(buildColumnError(index, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        }
        if (!invoke(ca, name, value, entity.getType(), entity.getMaxLength())) {
          errorList.add(buildColumnError(index, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
          continue;
        }
      } else if (entity != null) {
        // 系统字段
        if (handleSysFileJson(entity, value, data, name)) {
          errorList.add(buildColumnError(index, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
          continue;
        }
      } else {
        // 自定义字段默认没有错误
        if (name.startsWith("search_key") && value.length() > 30) {
          errorList.add(buildColumnError(index, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
          continue;
        }
        data.put(name, value);
      }
    }
    if (!CollectionUtils.isEmpty(errorList)) {
      // 错误信息保存到redis
      JSONArray jsonValues = JSONArray.parseArray(JSON.toJSONString(values));
      jsonValues.add(errorList);
      redisUtil.lSet(KeyCache.FILE_ERROR_LIST + storeId, JSONArray.toJSONString(jsonValues), 24*60*60);
      return null;
    }
    ca.setCreateBy(user.getId() == null ? 0 : user.getId());
    ca.setCreateTime(new Date());
    ca.setUpdateTime(new Date());
    ca.setUpdateBy(user.getId() == null ? 0 : user.getId());
    ca.setFollowCount(0); // 新建默认是0
    // 设置手机号字段
    data.put("own_mobile", ca.getOwnMobile());
    ca.setFieldJson(data); // ca应该需要乘以一千
    String outSerialNo = ca.getOutSerialNo();
    if (!StringUtils.isEmpty(outSerialNo)) {
      ca.setOutSerialTemp(outSerialNo.substring(0, outSerialNo.lastIndexOf("#")));
    }
    ca.setPayAmount(0L); // 已还款金额
    // 逾期天数日期互推
    setOverdueDays(ca);
    return ca;
  }

  private Boolean checkCaseFields(String name, String value) {
    if (name.equals("id_card")) {
      if (StringUtils.isBlank(value) || value.trim().length() < 1 || value.trim().length() > 32) {
        return false;
      }
    }
    if (name.equals("own_mobile")) {
      if (!RegexConstant.isMatch(RegexConstant.REGEX_PHONE, value)) {
        return false;
      }
    }
    //    if (name.equals("name")) {
    //      if (!RegexConstant.isMatch(RegexConstant.ALL_CHAR, value)) {
    //        return false;
    //      }
    //    }
    // 逾期天数,超过100年则报错
    if (name.equals("overdue_days")) {
      Integer overdueDays = checkInt(value);
      if (overdueDays != null && overdueDays > 36500) {
        return false;
      }
    }
    return true;
  }

  private void setOverdueDays(Case ca) {
    Date overdueDate = ca.getOverdueDate();
    Integer overdueDays = ca.getOverdueDays();
    // 全空或全不为空则不计算
    if ((overdueDate == null && overdueDays == null)
        || (overdueDate != null && overdueDays != null)) {
      return;
    }
    if (overdueDays == null) {
      overdueDays = calculateOverDays(overdueDate);
      ca.setOverdueDays(overdueDays);
    } else {
      overdueDate = DateUtils.addDays(DateUtils.getTodayDateFormat(), -overdueDays);
      ca.setOverdueDate(overdueDate);
    }
  }

  private Integer checkInt(String target) {
    if (StringUtils.isBlank(target)) {
      return null;
    }
    Pattern pattern = Pattern.compile("^[-\\+]?[\\d]+$");
    boolean isInt = pattern.matcher(target).matches(); // 是否是整数
    if (isInt) {
      try {
        return Integer.valueOf(target);
      } catch (Exception e) {
        log.error("逾期天数转换失败：", e);
      }
      return null;
    } else {
      return null;
    }
  }

  private Integer calculateOverDays(Date overdueDate) {
    if (overdueDate == null) {
      return 0;
    }
    Long days = DateUtils.countDays(overdueDate, new Date());
    if (days < 0) {
      days = 0L;
    }
    return days.intValue();
  }

  /**
   * @param entity
   * @param value 值
   * @param data 真实数据
   * @param name 字段名
   * @return
   */
  private boolean handleSysFileJson(
      DictionaryEntity entity, String value, Map<String, String> data, String name) {
    if (entity.getType() != null && entity.getType().equals("Money")) {
      try {
        Long newMoney = CmUtil.getAmountLong(new BigDecimal(value));
        Integer maxLength = entity.getMaxLength();
        if (maxLength != null && maxLength > 0 && String.valueOf(newMoney).length() > maxLength) {
          return true;
        }
        data.put(name, newMoney.toString());
      } catch (Exception e) {
        // 非必填字段标红
        data.put(name, value);
        return true;
      }
    } else {
      // 除了money之外的都可以默认不需要校验
      data.put(name, value);
    }
    return false;
  }

  private List<String> getFieldKeyList(Map<String, String> jsonField) {
    Map<String, String> newKeys =
        new TreeMap<>(
            new Comparator<String>() {
              @Override
              public int compare(String key1, String key2) {
                return Integer.valueOf(key1) - Integer.valueOf(key2);
              }
            });
    newKeys.putAll(jsonField);
    List<String> keys = new ArrayList<>(newKeys.values());
    return keys;
  }

  @Transactional(rollbackFor = Exception.class)
  public List<CaseUpdate> caseUpdateBatchHandle(
    Long storeId,
    List<String> lists,
    UserSession userSession,
    CaseTemplate caseTemplate,
    Long deltId,
    Map<String, DictionaryEntity> fieldsMap) {
    if (CommonUtils.isEmpty(lists)) {
      return new ArrayList<>();
    }
    List<String> keys = getFieldKeyList(caseTemplate.getFieldJson());
    // outSerialNo的下标
    Integer outSerialNoIndex = keys.indexOf("out_serial_no");
    // 校验outSerialNo是否存在
    List<String> outSerialNoList = Lists.newArrayList();
    List<JSONArray> jsonArrayList = Lists.newArrayList();
    List<String> outSerialNos = new ArrayList<>();
    for (int i = 0; i < lists.size(); i++) {
      JSONArray values = JSONArray.parseArray(lists.get(i));
      jsonArrayList.add(values);
      String outSerialNo = values.getString(outSerialNoIndex);
      if (StringUtils.isNotBlank(outSerialNo)) {
        outSerialNos.add(outSerialNo.trim());
        outSerialNoList.add(outSerialNo.trim() + "#" + deltId);
      }
    }
    List<String> existOutSerialNoList = caseService.getExistOutSerialNoList(outSerialNoList);
    // 查找催员
    List<User> dunnerList = new ArrayList<>();
    if (Objects.equals(userSession.getIsBank(), CompanyEnums.BankAmc.YES.getCode())) {
      dunnerList = userService.getBankDunnerList(userSession);
    } else {
      dunnerList = userService.getDunnerList(userSession);
    }
    Map<String, User> orgUserMap = dunnerList
      .stream().filter(user -> StringUtils.isNotBlank(user.getUserNo()))
      .collect(Collectors.toMap(User::getUserNo, orgUser -> orgUser));
    // 校验数据
    List<CaseUpdate> caseUpdates = new ArrayList<>();
    List<String> fieldNames = CmUtil.getClazzFieldNames(CaseUpdate.class);
    for (int i = 0; i < jsonArrayList.size(); i++) {
      CaseUpdate ca =
        getUpdate(
          storeId,
          keys,
          jsonArrayList.get(i),
          existOutSerialNoList,
          deltId,
          fieldsMap,
          fieldNames,
          userSession,
          orgUserMap);
      if (ca == null) {
        continue;
      }
      ca.setUpdateBy(userSession.getId());
      caseUpdates.add(ca);
    }
    List<CaseLog> caseLogList = new ArrayList<>();
    if (CollectionUtils.isEmpty(caseUpdates)) {
      return Collections.emptyList();
    }
    List<Long> allotCaseIds = new ArrayList<>();
    List<Long> adjustCaseIds = new ArrayList<>();
    if (!CollectionUtils.isEmpty(outSerialNos)) {
      Example example = new Example(Case.class);
      example.createCriteria().andIn("outSerialNo", outSerialNoList);
      example.createCriteria().andIn("recovery", Arrays.asList(0, -1));
      example.createCriteria().andEqualTo("orgId", userSession.getOrgId());
      List<Case> caseList = caseService.selectByExample(example);
      Map<String, Case> outSerialCaseMap = caseList.stream().collect(Collectors.toMap(Case::getOutSerialNo, Function.identity()));
      if (!CollectionUtils.isEmpty(caseList)) {
        for (CaseUpdate caseUpdate : caseUpdates) {
          CaseLog caseLog = new CaseLog();
          String outSerialNo = caseUpdate.getOutSerialNo();
          Case oldCase = outSerialCaseMap.getOrDefault(outSerialNo, null);
          if (Objects.isNull(oldCase)) {
            continue;
          }
          // 案件更新 如果更新催员，两种情况  以前案子没有分配，那就是案件分配；如果以前案件催员为A, 现在改为催员B，
          // 这时候记录对应的案件调整日志记录，解决还款归属查询案件日志不对问题
          if (Objects.nonNull(caseUpdate.getUserId())) {
            caseUpdate.setAllotStatus(CaseEnums.AllotStatus.ALLOT_USER.getCode());
            if (Objects.isNull(oldCase.getUserId())) {
              // 此时案件分配
              allotCaseIds.add(oldCase.getId());
              if(!CaseEnums.WayAllotState.YES.getCode().equals(oldCase.getWayAllotState())){
                //老案件没有催收手段，则默认分配催收手段为电催
                caseUpdate.setOperationWay(CaseEnums.OperationWay.DC.getCode());
                caseUpdate.setWayAllotState(CaseEnums.WayAllotState.YES.getCode());
                caseUpdate.setWayUpdateDate(new Date());
              }
            }
            if (Objects.nonNull(oldCase.getUserId()) && !Objects.equals(oldCase.getUserId(), caseUpdate.getUserId())) {
              // 此时案件调整，从催员A改为B
              adjustCaseIds.add(oldCase.getId());
            }
          }

          // 添加操作日志
          caseLog.setCaseId(oldCase.getId());
          caseLog.setCreateTime(new Date());
          caseLog.setType(CaseLogEnums.Type.UPDATE.getCode());
          caseLog.setIdCard(oldCase.getIdCard());
          caseLog.setCaseName(oldCase.getName());
          caseLog.setSerialNo(oldCase.getSerialNo());
          caseLog.setOutSerialNo(oldCase.getOutSerialNo());
          caseLog.setCreateBy(userSession.getId());
          caseLog.setCreateByName(userSession.getName());
          caseLog.setOrgId(userSession.getOrgId());
          caseLog.setTeamId(userSession.getTeamId());
          caseLog.setDepId(userSession.getDepId());
          caseLog.setTeamName(depTeamService.getNames().get(userSession.getTeamId()));
          //获取产品名称
          caseLog.setBatchId(oldCase.getInnerBatchId());
          caseLog.setOrgDeltId(oldCase.getOrgDeltId());
          caseLog.setOrgDeltName(deltService.getNames().get(oldCase.getOrgDeltId()));
          caseLog.setDunnerId(oldCase.getUserId());
          caseLog.setDunnerName(userService.getNames(userSession.getOrgId()).get(oldCase.getUserId()));
          caseLog.setProductId(oldCase.getProductId());
          caseLog.setProductName(productService.getNames().get(oldCase.getProductId()));
          caseLog.setBatchNo(innerBatchService.getNames().get(oldCase.getInnerBatchId()));
          caseLogList.add(caseLog);

        }
      }
    }
    // 批量更新
    caseMapper.batchUpdateByMap(caseUpdates);
    if (systemConfig.getLocalDeploy()) {
      List<Case> caseList = caseMapper.selectFieldJsonOutSerialNos(existOutSerialNoList);
      caseList.forEach(t -> {
        if (!CollectionUtil.isEmpty(t.getFieldJson())) {
          caseMapper.insertOrUpdateCaseInfoField(t.getId(), t.getFieldJson());
        }
      });
    }
    // 批量插入操作记录日志
    caseLogService.insertBatch(caseLogList);
    // 组装事件发送信息
    CaseBatchUpdateEvent updateEvent = new CaseBatchUpdateEvent(this, null);
    List<Map> maps =
      caseUpdates.stream()
        .map(
          caseUpdate -> {
            Map param = new HashMap();
            // 拿到更新案件的案件编号
            param.put("outSerialNo", caseUpdate.getOutSerialNo());
            return param;
          })
        .collect(Collectors.toList());
    updateEvent.setCaseUpdates(maps);
    updateEvent.setUserSession(userSession);
    updateEvent.setType(CaseLogEnums.Type.UPDATE.getCode());
    applicationContext.publishEvent(updateEvent);


    // 组装事件发送信息
    if (!CollectionUtils.isEmpty(allotCaseIds)) {
      CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
      caseBatchUpdateEvent.setType(CaseLogEnums.Type.ALLOT.getCode());
      caseBatchUpdateEvent.setUserSession(userSession);
      caseBatchUpdateEvent.setCaseIds(allotCaseIds);
      applicationContext.publishEvent(caseBatchUpdateEvent);
    }
    if (!CollectionUtils.isEmpty(adjustCaseIds)) {
      CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
      caseBatchUpdateEvent.setType(CaseLogEnums.Type.ADJUST.getCode());
      caseBatchUpdateEvent.setUserSession(userSession);
      caseBatchUpdateEvent.setCaseIds(adjustCaseIds);
      applicationContext.publishEvent(caseBatchUpdateEvent);
    }

    return caseUpdates;
  }

  private CaseUpdate getUpdate(
      Long storeId,
      List<String> keys,
      JSONArray values,
      List<String> existOutSerialNoList,
      Long deltId,
      Map<String, DictionaryEntity> fieldsMap,
      List<String> fieldNames,
      UserSession userSession,
      Map<String,User> orgUserMap) {
    CaseUpdate ca = new CaseUpdate();
    List<JsonField> jsonFields = new ArrayList<>();
    List<Map<String, Object>> errorList = new ArrayList<>();
    // 校验委案号
    Integer outSerialNoIndex = keys.indexOf("out_serial_no");
    String outSerialNo = values.getString(outSerialNoIndex);
    if (StringUtils.isNotBlank(outSerialNo)) {
      outSerialNo = outSerialNo.trim() + "#" + deltId;
    }
    // 委案号不存在
    if (!existOutSerialNoList.contains(outSerialNo)) {
      errorList.add(buildColumnError(outSerialNoIndex, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
    }
    for (int j = 0; j < keys.size(); j++) {
      String name = keys.get(j);
      String value = values.getString(j);
      if("dunner_code".equals(name) && StringUtils.isNotBlank(value)){
        //导入案件中包含催员编号，则自动分案
          value = value.trim();
          User dunner= orgUserMap.get(value);
          if(dunner==null){//催员不存在
            errorList.add(buildColumnError(j, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
            continue;
          }else {
            ca.setUserId(dunner.getId());
            ca.setTeamId(dunner.getTeamId());
            ca.setDepId(dunner.getDepId());
            ca.setAllotAgent(CaseEnums.AllotAgent.NC.getCode());
            ca.setAllotAgentState(CaseEnums.AllotAgentState.YES.getCode());
          }
        }
      // 兼容老导入模板，身份证号、债务人手机号不做更新
      if (name.equals("id_card") || name.equals("own_mobile")) {
        continue;
      }
      if (name.equals("out_serial_no")) {
        value = outSerialNo;
      }
      // 判断是否是通用字段
      DictionaryEntity entity = fieldsMap.get(name);
      // ======== 必填字段 ========
      if (entity != null
          && entity.getRequired() != null
          && "true".equals(entity.getRequired())
          && StringUtils.isBlank(value)) {
        errorList.add(buildColumnError(j, FileStoreEnums.ColumnErrorCode.NOT_BLANK));
        continue;
      }
      if (StringUtils.isBlank(value)) {
        continue;
      }
      value = value.trim();
      if (fieldNames.contains(CmUtil.underline2Camel(name, true))) {
        //        if (name.equals("name") && !RegexConstant.isMatch(RegexConstant.ALL_CHAR, value))
        // {
        //          errorList.add(buildColumnError(j, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        //        }
        // 数据库字段 通过反射赋值
        if (!invoke(ca, name, value, entity.getType(), entity.getMaxLength())) {
          errorList.add(buildColumnError(j, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        }
        continue;
      }
      // 可选字段与自定义字段
      if (!name.startsWith("search_key") && entity == null) {
        errorList.add(buildColumnError(j, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        continue;
      }
      // 自定义字段长度限制
      if (name.startsWith("search_key") && value.length() > 30) {
        errorList.add(buildColumnError(j, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        continue;
      }
      try {
        JsonField jsonField =
            getJsonField(
                name,
                entity == null ? null : entity.getType(),
                entity == null ? null : entity.getMaxLength(),
                value);
        jsonFields.add(jsonField);
        if ("contacts".equals(name)) {
          JsonField contactsTime =
              getJsonField(
                  "contacts_update_time", null, null, String.valueOf(new Date().getTime()));
          jsonFields.add(contactsTime);
          ca.setSyncStatus(CaseEnums.SyncStatus.NO.getCode());
        }
      } catch (Exception e) {
        errorList.add(buildColumnError(j, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
      }
    }
    if (!CollectionUtils.isEmpty(errorList)) {
      // 错误信息保存到redis
      values.add(errorList);
      stringRedisTemplate
          .opsForList()
          .rightPush(KeyCache.FILE_UPDATE_ERROR_LIST + storeId, JSONArray.toJSONString(values));
      return null;
    }
    // 地址设置成未同步
    JsonField isSyncAddressJson = new JsonField();
    isSyncAddressJson.setKey("is_sync_address");
    isSyncAddressJson.setValue("false");
    jsonFields.add(isSyncAddressJson);
    ca.setList(jsonFields);
    return ca;
  }

  public void handleFileStore(
      Long storeId,
      CaseTemplate caseTemplate,
      Integer successNum,
      Long totalAmount,
      Integer total,String language) {
    FileStore store = selectByPrimaryKey(storeId);
    FileStore updateStore = new FileStore();
    updateStore.setId(storeId);
    if (successNum > 0) {
      updateStore.setStatus(FileStoreEnums.Status.SUCCESS.getCode());
    } else {
      updateStore.setStatus(FileStoreEnums.Status.FAIL.getCode());
    }
    updateStore.setIsCreating(1);
    updateStore.setSuccessAmt(successNum);
    updateStore.setTotalAmount(totalAmount);
    updateStore.setTotal(total);
    // 获取错误导入数据
    String errorListKey = getErrorListKey(caseTemplate.getTempType(), storeId);
    List<List<String>> data = getErrorList(caseTemplate, errorListKey,language);
    if (!CollectionUtils.isEmpty(data)) {
      log.info("storeId:{},成功:{},失败:{},开始生成错误记录文件...", storeId, successNum, total - successNum);
      // 创建文件夹
      String name =
          StringUtils.isBlank(store.getName())
              ? "错误数据记录_" + storeId + ".xlsx"
              : "(错误数据记录)" + store.getName();
      Date expireDate = DateUtils.addDays(new Date(), 3);
      File file = new File(name);
      EasyExcel.write(file)
              .registerWriteHandler(new ErrorRowWriteHandler())
              .sheet()
              .doWrite(data);
      UploadCreatedFileInfo uploadCreatedFileInfo = new UploadCreatedFileInfo();
      uploadCreatedFileInfo.setFile(file)
              .setFileName(name)
              .setExpireDate(expireDate)
              .setBucket(systemConfig.getTemporaryFileBucket())
              .setLocalUrl(systemConfig.getLocalFilePath() + DateUtils.formatDate(new Date()) + File.separator + name);
      FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
      String filePath = fileStorageStrategy.uploadCreatedFile(uploadCreatedFileInfo);
      updateStore.setPath(filePath);
      updateStore.setExpireTime(expireDate);
    }
    // 如果是案件导入和还款导入、案件更新导入、催记导入可通过open接口获取错误数据
    if ((caseTemplate.getTempType() == CaseTemplateEnums.TempType.CASE_BASE_IMPORT.getCode()
            || caseTemplate.getTempType() == CaseTemplateEnums.TempType.REPAYMENT_IMPORT.getCode()
            || caseTemplate.getTempType() == CaseTemplateEnums.TempType.CASE_UPDATE_IMPORT.getCode()
            || caseTemplate.getTempType() == CaseTemplateEnums.TempType.OPERATION_IMPORT.getCode()
            || caseTemplate.getTempType() == CaseTemplateEnums.TempType.CONTACTS_IMPORT.getCode())
        && store.getType().equals(FileStoreEnums.Type.OPEN_API.getCode())) {
      saveOpenErrorData(storeId, errorListKey, caseTemplate);
    }
    fileStoreMapper.updateByPrimaryKeySelective(updateStore);
    // 删除错误数据
    stringRedisTemplate.delete(errorListKey);
  }


  @Transactional(rollbackFor = Exception.class)
  public void handleFileStore(Long storeId, Integer successNum, Integer total, List<String> headList) {
    FileStore store = fileStoreMapper.selectByPrimaryKey(storeId);
    FileStore updateStore = new FileStore();
    updateStore.setId(storeId);
    if (successNum > 0) {
      updateStore.setStatus(FileStoreEnums.Status.SUCCESS.getCode());
    } else {
      updateStore.setStatus(FileStoreEnums.Status.FAIL.getCode());
    }
    updateStore.setIsCreating(1);
    updateStore.setSuccessAmt(successNum);
    updateStore.setTotalAmount((long)total);
    updateStore.setTotal(total);
    // 获取错误导入数据
    String errorListKey = KeyCache.FILE_ERROR_LIST + storeId;
    List<List<String>> data = getErrorList(errorListKey, headList);
    if (!CollectionUtils.isEmpty(data)) {
      log.info("storeId:{},成功:{},失败:{},开始生成错误记录文件...", storeId, successNum, total - successNum);
      // 创建文件夹
      String name =
              org.apache.commons.lang3.StringUtils.isBlank(store.getName())
                      ? "错误数据记录_" + storeId + ".xlsx"
                      : "(错误数据记录)" + org.apache.commons.lang3.StringUtils.substringBeforeLast(store.getName(), ".") + "_"
                      + storeId + "." + org.apache.commons.lang3.StringUtils.substringAfterLast(store.getName(), ".");
      Date expireDate = DateUtils.addDays(new Date(), 3);
      File file = new File(name);
      EasyExcel.write(file)
              .registerWriteHandler(new ErrorRowWriteHandler())
              .sheet()
              .doWrite(data);

      UploadCreatedFileInfo uploadCreatedFileInfo = new UploadCreatedFileInfo();
      uploadCreatedFileInfo.setFile(file)
              .setFileName(name)
              .setExpireDate(expireDate)
              .setBucket(systemConfig.getCaseFilesBucket())
              .setLocalUrl(systemConfig.getLocalFilePath() + DateUtils.formatDate(new Date()) + File.separator + name);
      FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
      String filePath = fileStorageStrategy.uploadCreatedFile(uploadCreatedFileInfo);
      updateStore.setPath(filePath);
      updateStore.setExpireTime(expireDate);
    }
    updateStore.setUpdateTime(new Date());
    fileStoreMapper.updateByPrimaryKeySelective(updateStore);
    // 删除错误数据
    stringRedisTemplate.delete(errorListKey);
  }

  public List<List<String>> getErrorList(String errorListKey, List<String> head) {
    Long total = stringRedisTemplate.opsForList().size(errorListKey);
    if (total <= 0) {
      return new ArrayList<>();
    }
    List<List<String>> dataList = new ArrayList<>();
    // 第一行
    List<String> head0 = new ArrayList<>();
    head0.add("标红字段为必填项，金额相关的数字默认保留小数点后2位，日期格式为YYYY/MM/DD，此行请勿删除");
    dataList.add(head0);
    // 第二行
    dataList.add(head);
    // 错误数据
    long start = 0;
    List<String> errorList = new ArrayList<>();
    while (start < total) {
      long end = start + 99 < total ? total - 1 : start + 99;
      errorList = stringRedisTemplate.opsForList().range(errorListKey, start, end);
      for (String error : errorList) {
        List<String> values = JSONArray.parseArray(error, String.class);
        dataList.add(values);
      }
      start = end + 1;
    }
    return dataList;
  }

  private void saveOpenErrorData(Long storeId, String errorListKey, CaseTemplate caseTemplate) {
    Long total = stringRedisTemplate.opsForList().size(errorListKey);
    if (total <= 0) {
      return;
    }
    List<String> keyList;
    if (ObjectUtil.equals(caseTemplate.getId(), 0L)) {
      Integer type = null;
      if (ObjectUtil.equals(caseTemplate.getTempType(), CaseTemplateEnums.TempType.REPAYMENT_IMPORT.getCode())) {
        type = TemplateFieldEnums.Type.REPAYMENT.getCode();
      }
      List<TemplateFieldVO> fieldList = templateFieldService.getFieldList(type, caseTemplate.getOrgId());
      keyList = fieldList.stream().map(TemplateFieldVO::getField).collect(Collectors.toList());
    } else {
      keyList = getFieldKeyList(caseTemplate.getFieldJson());
    }
    String columnErrorKey = KeyCache.FILE_STORE_COLUMN_ERROR_LIST + storeId;
    long start = 0;

    while (start < total) {
      long end = start + 99 < total ? total - 1 : start + 99;
      List<String> list = stringRedisTemplate.opsForList().range(errorListKey, start, end);
      List<String> columnErrorList = new ArrayList<>();
      for (String content : list) {
        JSONArray contentArray = JSONArray.parseArray(content);
        // 原始数据
        JSONObject data = new JSONObject();
        for (int i = 0; i < keyList.size(); i++) {
          String value = contentArray.getString(i);
          if (StringUtils.isBlank(value)) {
            continue;
          }
          String key = keyList.get(i);
          data.put(key, value);
        }
        JSONArray errorIndexArray = contentArray.getJSONArray(contentArray.size() - 1);
        // 错误数据
        JSONArray columnError = new JSONArray();
        for (int i = 0; i < errorIndexArray.size(); i++) {
          JSONObject errorObject = errorIndexArray.getJSONObject(i);
          Integer index = errorObject.getInteger("index");
          errorObject.put("column_key", keyList.get(index));
          errorObject.put("column_value", contentArray.getString(index));
          errorObject.remove("index");
          columnError.add(errorObject);
        }
        JSONObject errorJson = new JSONObject();
        errorJson.put("data", data);
        errorJson.put("error", columnError);
        columnErrorList.add(errorJson.toJSONString());
      }
      stringRedisTemplate.opsForList().rightPushAll(columnErrorKey, columnErrorList);
      start = end + 1;
    }
    // 3天后过期
    stringRedisTemplate.expire(columnErrorKey, 3, TimeUnit.DAYS);
  }

  private String getErrorListKey(Integer tempType, Long storeId) {
    String errorListKey = KeyCache.FILE_ERROR_LIST + storeId;
    if (SystemConst.TEMPLATE_TYPE_CASE_UPDATE == tempType) {
      errorListKey = KeyCache.FILE_UPDATE_ERROR_LIST + storeId;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_REPAYMENT == tempType) {
      errorListKey = KeyCache.FILE_REPAYMENT_ERROR_LIST + storeId;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_CONTACTS == tempType) {
      errorListKey = KeyCache.FILE_CONTACTS_ERROR_LIST + storeId;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_OPERATION_IMPORT == tempType) {
      errorListKey = KeyCache.FILE_OPERATION_ERROR_LIST + storeId;
    }else if (CaseTemplateEnums.TempType.USER_IMPORT.getCode() == tempType) {
      errorListKey = KeyCache.FILE_USER_ERROR_LIST + storeId;
    }
    return errorListKey;
  }

  @Transactional(rollbackFor = Exception.class)
  public List<CaseRepayment> caseRepaymentHandle(
      Long storeId,
      List<String> lists,
      UserSession userSession,
      CaseTemplate caseTemplate,
      Map<String, DictionaryEntity> fieldsMap,
      Long orgDeltId) {
    List<CaseRepayment> repayments = getCasesRepayment(storeId, lists, userSession, caseTemplate, fieldsMap, orgDeltId);
    if (CollectionUtils.isEmpty(repayments)) {
      return repayments;
    }
    repaymentService.insertBatch(repayments);
    userStatisticsService.handleBeforeRepaymentStatistics(repayments);
    biStatisticsService.handleBeforeRepaymentStatistics(repayments);
    mediationStatisticsService.handleBeforeRepaymentStatistics(repayments);
    return repayments;
  }

  // 解析数据组装caseRepayment
  @Transactional(rollbackFor = Exception.class)
  public List<CaseRepayment> getCasesRepayment(
      Long storeId,
      List<String> lists,
      UserSession tokenUser,
      CaseTemplate caseTemplate,
      Map<String, DictionaryEntity> fieldsMap,
      Long orgDeltId) {
    log.info("解析数据组装caseRepayment,storeId:{},",storeId);
    if (CommonUtils.isEmpty(lists)) {
      return null;
    }
    List<String> keyList;
    if (ObjectUtil.equals(caseTemplate.getId(), 0L)) {
      Integer type = null;
      if (ObjectUtil.equals(caseTemplate.getTempType(), CaseTemplateEnums.TempType.REPAYMENT_IMPORT.getCode())) {
        type = TemplateFieldEnums.Type.REPAYMENT.getCode();
      }
      List<TemplateFieldVO> fieldList = templateFieldService.getFieldList(type, tokenUser.getOrgId());
      keyList = fieldList.stream().map(TemplateFieldVO::getField).collect(Collectors.toList());
    } else {
      keyList = getFieldKeyList(caseTemplate.getFieldJson());
    }

    Integer outSerialNoIndex = keyList.indexOf("out_serial_no");
    // 校验outSerialNo是否存在
    List<String> outSerialNoList = Lists.newArrayList();
    List<JSONArray> jsonArrayList = Lists.newArrayList();
    for (int i = 0; i < lists.size(); i++) {
      JSONArray values = JSONArray.parseArray(lists.get(i));
      jsonArrayList.add(values);
      String outSerialNo = values.getString(outSerialNoIndex);
      if (StringUtils.isNotBlank(outSerialNo)) {
        outSerialNoList.add(outSerialNo.trim() + "#" + orgDeltId);
      }
    }
    // 案件债务人信息
    List<Case> caseList = caseService.selectCaseByOutSerialNos(outSerialNoList);
    Map<String, Case> caseMap =
            caseList.stream().collect(Collectors.toMap(Case::getOutSerialNo, c -> c));
    // 案件催员信息
    List<Long> caseIdList = caseList.stream().map(Case::getId).collect(Collectors.toList());
    Map<Long, Long> caseUserMap = caseService.getCaseUserId(caseIdList);
    Map<Long, String> userNameMap =
            userService.getNames(tokenUser.getOrgId());
    // 校验信息
    List<CaseRepayment> repayments = new ArrayList<>(lists.size());
    for (int i = 0; i < jsonArrayList.size(); i++) {
      JSONArray values = jsonArrayList.get(i);
      String outSerialNo = values.getString(outSerialNoIndex);
      if (StringUtils.isNotBlank(outSerialNo)) {
        outSerialNo = outSerialNo.trim() + "#" + orgDeltId;
      }
      Case aCase = caseMap.get(outSerialNo);
      CaseRepayment repayment = getRepayment(storeId, keyList, jsonArrayList.get(i), aCase, fieldsMap);
      if (repayment == null) {
        continue;
      }
      repayment.setCaseId(aCase.getId());
      repayment.setDebtIdCard(aCase.getIdCard());
      repayment.setOutSerialTemp(aCase.getOutSerialTemp());
      repayment.setOrgDeltId(aCase.getOrgDeltId());
      repayment.setCreateBy(tokenUser.getId() == null ? 0L : tokenUser.getId());
      repayment.setUpdateBy(tokenUser.getId() == null ? 0L : tokenUser.getId());
      repayment.setCreateTime(new Date());
      repayment.setUpdateTime(new Date());
      repayment.setStatus(0);
      repayment.setAllotAgent(aCase.getAllotAgent());
      // 审核状态为导入成功
      repayment.setApplyStatus(RepaymentEnums.ApplyStatus.IMPORT.getCode());
      // 还款方式为还款导入
      repayment.setRepaymentStyle("还款导入");
      // 类型，0：还款，1：划扣
      repayment.setType(0);
      // 还款人默认债务人本人的姓名
      if (StringUtils.isBlank(repayment.getRepaymentFrom())) {
        repayment.setRepaymentFrom(aCase.getName());
      }
      // 同时也需要更新案件的已还款金额
      aCase.setPayAmount(aCase.getPayAmount() + repayment.getRepaymentAmount());
      caseService.updateByPrimaryKeySelective(aCase);
      CaseLog caseLog = this.caseLogService.selectLastCaseLogForRepayment(aCase.getId(), DateUtils.getEndTimeOfDay(repayment.getRepaymentTime()));
      repayment.setOrgId(aCase.getOrgId());
      if (caseLog == null) {
        Long userId = caseUserMap.get(aCase.getId());
        repayment.setDepId(aCase.getDepId());
        repayment.setTeamId(aCase.getTeamId());
        if (userId != null) {
          repayment.setCaseOperator(userId);
          repayment.setCaseOperatorName(userNameMap.get(userId));
          repayment.setRepaymentAuto(RepaymentEnums.Auto.OPERATOR.getCode());
        } else {
          repayment.setRepaymentAuto(RepaymentEnums.Auto.SYS.getCode());
        }
      } else {
        repayment.setDepId(caseLog.getDepId());
        repayment.setTeamId(caseLog.getTeamId());
        repayment.setCaseOperator(caseLog.getDunnerId());
        // 这里的催员名字取user表，不取日志表，防止name修改之后，日志表还是之前的
        repayment.setCaseOperatorName(userNameMap.get(caseLog.getDunnerId()));
        repayment.setRepaymentAuto(RepaymentEnums.Auto.OPERATOR.getCode());
      }
      //ca.setOrgId();
      repayments.add(repayment);
    }
    return repayments;
  }

  private CaseRepayment getRepayment(
      Long storeId,
      List<String> keys,
      JSONArray values,
      Case aCase,
      Map<String, DictionaryEntity> fieldsMap) {
    CaseRepayment repayment = new CaseRepayment();
    String loanNumber = null;
    Integer period = null;
    Integer loanNumberIndex = -1;
    Integer periodIndex = -1;
    List<Map<String, Object>> errorList = new ArrayList<>();
    for (int i = 0; i < keys.size(); i++) {
      String name = keys.get(i);
      String value = values.getString(i);
      if (name.equals("out_serial_no")) {
        if (aCase == null) {
          errorList.add(buildColumnError(i, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
          continue;
        }
        value = aCase.getOutSerialNo();
      }
      if (Objects.equals(name, "debt_name") && StringUtils.isBlank(value) && aCase != null) {
        value = aCase.getName();
      }
      DictionaryEntity entity = fieldsMap.get(name);
      if (null == entity) {
        errorList.add(buildColumnError(i, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        continue;
      }
      // 必填字段判断
      if ("true".equals(entity.getRequired())) {
        if (StringUtils.isBlank(value)) {
          errorList.add(buildColumnError(i, FileStoreEnums.ColumnErrorCode.NOT_BLANK));
          continue;
        }
      }
      if (name.equals("debt_name") && aCase != null && !aCase.getName().equals(value.trim())) {
        errorList.add(buildColumnError(i, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
        continue;
      }
      if (Objects.equals(name, "loan_number")) {
        loanNumber = value;
        loanNumberIndex=i;
        continue;
      }
      if (Objects.equals(name, "period")) {
        try {
          periodIndex = i;
          if(!StringUtils.isBlank(value)) {
            period = Integer.valueOf(value);
          }
        }catch (Exception ignored){
          errorList.add(buildColumnError(i, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        }
        continue;
      }
      // 通过反射赋值
      if (StringUtils.isNotBlank(value)) {
        if (!invoke(repayment, name, value.trim(), entity.getType(), entity.getMaxLength())) {
          errorList.add(buildColumnError(i, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        }
      }
    }
    if (loanNumberIndex != -1 && periodIndex != -1 && aCase != null) {
      if (StringUtils.isBlank(loanNumber) && Objects.isNull(period)) {
        repayment.setPromissoryNoteId(0L);
        repayment.setRepayPlanId(0L);
      } else if (StringUtils.isBlank(loanNumber) && !Objects.isNull(period)) {
        repayment.setPromissoryNoteId(0L);
        RepayPlan query = new RepayPlan();
        query.setCaseId(aCase.getId());
        query.setPromissoryNoteId(0L);
        query.setPeriod(period);
        RepayPlan repayPlan = repayPlanService.selectOne(query);
        if (repayPlan == null) {
          errorList.add(buildColumnError(periodIndex, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
        } else {
          repayment.setRepayPlanId(repayPlan.getId());
        }
      } else if (!StringUtils.isBlank(loanNumber) && Objects.isNull(period)) {
        repayment.setRepayPlanId(0L);
        PromissoryNote query = new PromissoryNote();
        query.setCaseId(aCase.getId());
        query.setLoanNumber(loanNumber);
        PromissoryNote promissoryNote = promissoryNoteService.selectOne(query);
        if (promissoryNote == null) {
          errorList.add(buildColumnError(loanNumberIndex, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
        } else {
          repayment.setPromissoryNoteId(promissoryNote.getId());
        }
      } else {
        PromissoryNote query = new PromissoryNote();
        query.setCaseId(aCase.getId());
        query.setLoanNumber(loanNumber);
        PromissoryNote promissoryNote = promissoryNoteService.selectOne(query);
        if (promissoryNote == null) {
          errorList.add(buildColumnError(loanNumberIndex, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
        } else {
          repayment.setPromissoryNoteId(promissoryNote.getId());
          RepayPlan planQuery = new RepayPlan();
          planQuery.setCaseId(aCase.getId());
          planQuery.setPromissoryNoteId(promissoryNote.getId());
          planQuery.setPeriod(period);
          RepayPlan repayPlan = repayPlanService.selectOne(planQuery);
          if (repayPlan == null) {
            errorList.add(buildColumnError(periodIndex, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
          } else {
            repayment.setRepayPlanId(repayPlan.getId());
          }
        }
      }
    }
    if (!CollectionUtils.isEmpty(errorList)) {
      // 错误信息保存到redis
      values.add(errorList);
      stringRedisTemplate
          .opsForList()
          .rightPush(KeyCache.FILE_REPAYMENT_ERROR_LIST + storeId, JSONArray.toJSONString(values));
      return null;
    }
    return repayment;
  }

  public Integer contactsHandle(
      Long storeId, List<String> lists, UserSession user, Long parentOrgId,Map<String, Long> contactTypeMap) {
    List<Contacts> contacts = new ArrayList<>();
    Integer successAmt = 0;
    List<List<String>> valueList = new ArrayList<>();
    // 校验委案公司
    List<String> deltNameList = new ArrayList<>();
    for (int i = 0; i < lists.size(); i++) {
      String valuesStr = lists.get(i);
      List<String> values = JSONArray.parseArray(valuesStr, String.class);
      String deltName = values.get(0);
      if (StringUtils.isNotBlank(deltName)) {
        deltNameList.add(deltName.trim());
      }
      valueList.add(values);
    }
    Map<String, Long> deltIdMap = deltService.getDeltIdMap(user.getOrgId(), deltNameList);
    // 校验案件编号
    List<String> outSerialNoList = new ArrayList<>();
    for (List<String> values : valueList) {
      String deltName = values.get(0);
      String outSerialNo = values.get(1);
      if (StringUtils.isNotBlank(deltName) && StringUtils.isNotBlank(outSerialNo)) {
        Long deltId = deltIdMap.get(deltName.trim());
        if (deltId != null) {
          outSerialNoList.add(outSerialNo.trim() + "#" + deltId);
        }
      }
    }
    Map<String, Case> contactsCaseMap = getContactsCaseMap(outSerialNoList);
    for (List<String> values : valueList) {
      String deltName = values.get(0);
      String outSerialNo = values.get(1);
      Long relId = null;
      Boolean isConjoint = true;
      String ownMobile = "";
      if (StringUtils.isNotBlank(deltName)
          && StringUtils.isNotBlank(outSerialNo)
          && deltIdMap.get(deltName) != null) {
        Case aCase = contactsCaseMap.get(outSerialNo.trim() + "#" + deltIdMap.get(deltName.trim()));
        if (aCase != null) {
          if (aCase.getDebtId() == null) {
            relId = aCase.getId();
            isConjoint = false;
          } else {
            relId = aCase.getDebtId();
          }
          ownMobile = aCase.getOwnMobile();
        }
      }
      //读取表格信息
//      List<Contacts> subContacts =
//          getContact(storeId, values, user, parentOrgId, relId, isConjoint, contactTypeMap);
//      if (CollectionUtils.isEmpty(subContacts)) {
//        continue;
//      }
      // 债务人手机号关系、姓名不更新
//      if (ownMobile != null) {
//        String filterMobile = ownMobile;
//        subContacts =
//            subContacts.stream()
//                .filter(c -> !c.getMobile().equals(filterMobile))
//                .collect(Collectors.toList());
//      }
      Contacts contactInfo = getContactInfo(storeId, values, user, parentOrgId, relId, isConjoint, contactTypeMap);
      if (Objects.isNull(contactInfo)) {
        continue;
      }
      //债务人手机号不变则own_sign类型不变
      if (Objects.equals(ownMobile, contactInfo.getMobile())) {
        contactInfo.setOwnSign(1);
      }
      contacts.add(contactInfo);
      successAmt++;
    }
    if (CollectionUtils.isEmpty(contacts)) {
      return successAmt;
    }
    // 通讯录去重
    contactsService.batchUpdateContact(contacts);
    return successAmt;
  }

  private Map<String, Case> getContactsCaseMap(List<String> outSerialNoList) {
    if (CollectionUtils.isEmpty(outSerialNoList)) {
      return new HashMap<>();
    }
    Example example = new Example(Case.class);
    example.selectProperties("outSerialNo");
    example.selectProperties("id");
    example.selectProperties("debtId");
    example.selectProperties("ownMobile");
    example.createCriteria().andIn("outSerialNo", outSerialNoList);
    List<Case> caseList = caseService.selectByExample(example);
    if (CollectionUtils.isEmpty(caseList)) {
      return new HashMap<>();
    }
    return caseList.stream().collect(Collectors.toMap(Case::getOutSerialNo, c -> c));
  }

  public void updateFileFail(Long storeId) {
    FileStore fileStore = new FileStore();
    fileStore.setId(storeId);
    fileStore.setStatus(-1);
    updateByPrimaryKeySelective(fileStore);
  }

  /**
   * 导入文件对应的模版字段
   *
   * @param storeIdSet
   * @return
   */
  public Map<Long, Map<String, String>> selectTplSeqRecordMap(Set<Long> storeIdSet) {
    if (CollectionUtils.isEmpty(storeIdSet)) {
      return new HashMap<>();
    }
    List<Map<String, Object>> mapList =
        fileStoreMapper.selectTplSeqRecordMap(Lists.newArrayList(storeIdSet));
    Map<Long, Map<String, String>> result = new HashMap<>();
    for (Map<String, Object> map : mapList) {
      Long storeId = Long.valueOf(map.get("storeId").toString());
      Map<String, String> tplMap = JsonUtils.fromJson(map.get("tplFields").toString(), Map.class);
      result.put(storeId, tplMap);
    }
    return result;
  }

  @Transactional(rollbackFor = Exception.class)
  public List<CaseOperation> caseOperationImportHandle(
      Long storeId,
      List<String> lists,
      UserSession userSession,
      CaseTemplate caseTemplate,
      Map<String, DictionaryEntity> fieldsMap,
      Long orgDeltId) {
    List<CaseOperation> caseOperationList = new ArrayList<>(lists.size());
    if (CommonUtils.isEmpty(lists)) {
      return caseOperationList;
    }
    List<String> keys = getFieldKeyList(caseTemplate.getFieldJson());
    Integer outSerialNoIndex = keys.indexOf("out_serial_no");
    // 校验outSerialNo是否存在
    List<String> outSerialNoList = Lists.newArrayList();
    List<JSONArray> jsonArrayList = Lists.newArrayList();
    for (int i = 0; i < lists.size(); i++) {
      JSONArray values = JSONArray.parseArray(lists.get(i));
      jsonArrayList.add(values);
      String outSerialNo = values.getString(outSerialNoIndex);
      if (StringUtils.isNotBlank(outSerialNo)) {
        outSerialNoList.add(outSerialNo.trim() + "#" + orgDeltId);
      }
    }
    // 案件编号对应ID
    Map<String, Long> caseIdMap = caseService.getOutSerialNoAndIdMap(outSerialNoList);
    // 校验信息
    for (int i = 0; i < jsonArrayList.size(); i++) {
      JSONArray values = jsonArrayList.get(i);
      String outSerialNo = values.getString(outSerialNoIndex);
      if (StringUtils.isNotBlank(outSerialNo)) {
        outSerialNo = outSerialNo.trim() + "#" + orgDeltId;
      }
      Long caseId = caseIdMap.get(outSerialNo);
      CaseOperation caseOperation =
          getCaseOperation(storeId, keys, jsonArrayList.get(i), caseId, fieldsMap);
      if (caseOperation == null) {
        continue;
      }
      if (caseOperation.getCreateTime() == null) {
        caseOperation.setCreateTime(new Date());
      }
      Case caseTemp = caseService.selectByPrimaryKey(caseId);
      caseOperation.setCaseId(caseId);
      caseOperation.setOrgId(caseTemp.getOrgId());
      caseOperation.setOrgDeltId(caseTemp.getOrgDeltId());
      caseOperation.setOutSerialNo(caseTemp.getOutSerialNo());
      caseOperation.setOutSerialTemp(caseTemp.getOutSerialTemp());
      caseOperation.setCreateBy(0L);
      caseOperation.setAdminSubmitter(userSession.getId());
      caseOperation.setUpdateBy(userSession.getId());
      caseOperation.setUpdateTime(new Date());
      caseOperation.setStatus(0);
      caseOperation.setCreateType(CaseOperationEnums.CreateType.IMPORT.getCode());
      // 催记导入的催记，电话结果、催收结果、催收进程都是空：也就是未知
      caseOperation.setActionType(CaseOperationEnums.ActionType.UNKNOWN.getCode());
      caseOperation.setCallType(CaseOperationEnums.CallType.UNKNOWN.getCode());
      caseOperation.setOperationState(CaseOperationEnums.State.UNKNOWN.getCode());
      caseOperation.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
      caseOperationList.add(caseOperation);
    }
    if (caseOperationList.isEmpty()) {
      return caseOperationList;
    }
    caseOperationService.insertBatch(caseOperationList);
    return caseOperationList;
  }

  private CaseOperation getCaseOperation(
      Long storeId,
      List<String> keys,
      JSONArray values,
      Long caseId,
      Map<String, DictionaryEntity> fieldsMap) {
    CaseOperation caseOperation = new CaseOperation();
    List<Map<String, Object>> errorList = new ArrayList<>();
    for (int i = 0; i < keys.size(); i++) {
      String name = keys.get(i);
      String value = values.getString(i);
      if (name.equals("out_serial_no")) {
        if (caseId == null) {
          errorList.add(buildColumnError(i, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
          continue;
        } else {
          continue;
        }
      }
      DictionaryEntity entity = fieldsMap.get(name);
      if (null == entity) {
        errorList.add(buildColumnError(i, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        continue;
      }
      // 必填字段判断
      if ("true".equals(entity.getRequired())) {
        if (StringUtils.isBlank(value)) {
          errorList.add(buildColumnError(i, FileStoreEnums.ColumnErrorCode.NOT_BLANK));
          continue;
        }
      }
      if (StringUtils.isBlank(value)) {
        continue;
      }
      if (name.equals("case_ope_time")) {
        name = "create_time";
      }
      if (!invoke(caseOperation, name, value.trim(), entity.getType(), entity.getMaxLength())) {
        errorList.add(buildColumnError(i, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
      }
    }
    if (!CollectionUtils.isEmpty(errorList)) {
      // 错误信息保存到redis
      values.add(errorList);
      stringRedisTemplate
          .opsForList()
          .rightPush(KeyCache.FILE_OPERATION_ERROR_LIST + storeId, JSONArray.toJSONString(values));
      return null;
    }
    return caseOperation;
  }

  public void updateFileFail(Long storeId, Integer total, Integer successAmt, Long totalAmount) {
    FileStore fileStore = new FileStore();
    fileStore.setId(storeId);
    fileStore.setTotal(total);
    fileStore.setSuccessAmt(successAmt);
    fileStore.setTotalAmount(totalAmount);
    fileStore.setStatus(-1);
    updateByPrimaryKeySelective(fileStore);
  }

  public List<List<String>> getErrorList(CaseTemplate caseTemplate, String errorListKey,String language) {
    Long total = stringRedisTemplate.opsForList().size(errorListKey);
    if (total <= 0) {
      return new ArrayList<>();
    }
    List<List<String>> dataList = new ArrayList<>();
    // 第一行
    List<String> head0 = new ArrayList<>();
    head0.add("标红字段为必填项，金额相关的数字默认保留小数点后2位，日期格式为YYYY/MM/DD，此行请勿删除");
    dataList.add(head0);
    // 第二行
    List<String> head1 = new ArrayList<>();
    List<String> exportNameList = caseTemplateService.getExcelExportNames(caseTemplate, true,language);
    head1.addAll(exportNameList);
    dataList.add(head1);
    // 错误数据
    long start = 0;
    List<String> errorList = new ArrayList<>();
    while (start < total) {
      long end = start + 99 < total ? total - 1 : start + 99;
      errorList = stringRedisTemplate.opsForList().range(errorListKey, start, end);
      for (String error : errorList) {
        List<String> values = JSONArray.parseArray(error, String.class);
        dataList.add(values);
      }
      start = end + 1;
    }
    return dataList;
  }

  public Map<String, Object> buildColumnError(
      Integer index, FileStoreEnums.ColumnErrorCode columnErrorCode) {
    Map<String, Object> errorMap = new HashMap<>();
    errorMap.put("index", index);
    errorMap.put("error_code", columnErrorCode.getCode());
    return errorMap;
  }

  public Boolean isCaseImport(Long orgId) {
    List<String> fileKeyList = stringRedisTemplate.opsForList().range(KeyCache.FILE_LIST, 0, -1);
    if (CollectionUtils.isEmpty(fileKeyList)) {
      return false;
    }
    List<Long> storeIdList = new ArrayList<>();
    for (String fileKey : fileKeyList) {
      Long storeId = Long.valueOf(StringUtils.substringAfterLast(fileKey, "_"));
      storeIdList.add(storeId);
    }
    Example example = new Example(FileStore.class);
    example
        .createCriteria()
        .andIn("id", storeIdList)
        .andEqualTo("orgId", orgId)
        .andEqualTo("templateType", CaseTemplateEnums.TempType.CASE_BASE_IMPORT.getCode());
    Integer count = super.selectCountByExample(example);
    if (count > 0) {
      return true;
    } else {
      return false;
    }
  }


  /**
   * 校验apikey
   *
   * <p>返回orgId
   *
   * @param apikey
   * @return
   */
  public Long verifyApikey(String apikey) {
    if (org.apache.commons.lang3.StringUtils.isBlank(apikey)) {
      throw new OpenException(OpenErrorEnums.ErrorCode.APIKEY_NOTNULL_CODE);
    }
    Example query = new Example(Company.class);
    query
        .createCriteria()
        .andEqualTo("apikey", apikey)
        .andIn(
            "status",
            Arrays.asList(OrgEnums.Status.NORMAL.getCode(), OrgEnums.Status.ON_TRIAL.getCode()));
    List<Company> companies = companyService.selectByExample(query);
    if (CommonUtils.isEmpty(companies) || companies.get(0) == null) {
      throw new OpenException(OpenErrorEnums.ErrorCode.ORG_NOTFOUND_CODE);
    }
    if (companies.get(0).getCooperationEndTime().before(new Date())) {
      throw new OpenException(OpenErrorEnums.ErrorCode.NOT_IN_COOPERATION_CODE);
    }
    return companies.get(0).getId();
  }

  public String uploadOne(String apiKey, MultipartFile file){
    verifyApikey(apiKey);
    return uploadFile(file);
  }

  public String uploadSealFile(String apiKey, MultipartFile file){
    verifyApikey(apiKey);
    return handlerSealFile(file);
  }

  /**
   * 该方法逻辑和uploadFile方法逻辑一致，主要是为了适配函件印章、承诺书、凭证上传，修改了过期时间、oss存放地址
   * @param file
   * @return
   */
  public String handlerSealFile(MultipartFile file) {
    String fileName = file.getOriginalFilename();
    if (file.isEmpty()) {
      throw new ApiException("文件不能为空");
    }
    try {
      //如果是本地化的公司调用saas接口，上传到oss
      if (systemConfig.getLocalDeploy()) {
        Long orgId = UserUtils.getTokenUser().getOrgId();
        Company company = companyService.selectByPrimaryKey(orgId);
        String apikey = company.getApikey();

        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("multipart/form-data");
        headers.setContentType(type);

        //设置请求体，注意是LinkedMultiValueMap
        //FileSystemResource fileSystemResource = new FileSystemResource(filePath+"/"+fileName);
        Resource fileResource = file.getResource();
        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
        form.add("file", fileResource);
        HttpEntity<MultiValueMap<String, Object>> files = new HttpEntity<>(form, headers);

        String anmiSaasUrlPrefix = systemConfig.getAnmiSaasUrlPrefix();
        String url = anmiSaasUrlPrefix + "/v1/openApi/uploadSealFile?apiKey=" + apikey;
        String tmp = restTemplate.postForObject(url, files, String.class);
        log.info(tmp);
        JSONObject result=JSON.parseObject(tmp);
        if ("1".equals(String.valueOf(result.get("status")))) {
          return String.valueOf(result.get("data"));
        } else {
          throw new ApiException("文件上传失败");
        }
      }
      else {
        // 上传文件
        fileName =
                StringUtils.substringBeforeLast(fileName, ".")
                        + "_"
                        + StringUtils.getRandomNumberBIT6()
                        + "."
                        + StringUtils.substringAfterLast(fileName, ".");
        Date expireDate = DateUtils.addYears(new Date(), 10);
        String path = "";
        path = OSSClientUtil.putFile(fileName, expireDate, systemConfig.getCaseFilesBucket(), file);
        log.info("文件已上传至OSS。。。 文件名为：{} 地址为：{} 过期时间：{}", fileName, path, expireDate);
        return path;
      }
    } catch (Exception e) {
      log.error("文件解析异常", e);
      throw new ApiException("文件解析异常:" + e.getMessage());
    }
  }

  /**
   * 获取文件导入记录
   *
   * @param fileStoreIds 文件存储ID
   * @return {@link List}<{@link FileStore}>
   */
  public List<FileStore> selectFileStores(List<Long> fileStoreIds) {
    Example example = new Example(FileStore.class);
    example.selectProperties("id","orgId");
    example.setOrderByClause("id asc");

    Example.Criteria criteria = example.createCriteria();
    criteria.andIn("id", fileStoreIds);

    return selectByExample(example);
  }


  /**
   * 员工数据处理
   *
   * @param storeId     fileStoreId
   * @param users     数据行
   * @param userSession 用户会话
   * @param ipAddr ip地址
   * @return {@link Integer}
   */
  public Integer usersHandle(Long storeId, List<String> users, UserSession userSession, String ipAddr) {
    Integer successAmt = 0;
    List<List<String>> userList = new ArrayList<>();

    for (int i = 0; i < users.size(); i++) {
      String columnStr = users.get(i);
      List<String> userColumn = JSONArray.parseArray(columnStr, String.class);
      userList.add(userColumn);
    }

    for (List<String> userColumn : userList) {
      Boolean result = addUser(storeId, userColumn, userSession, ipAddr);
      if (result){
        successAmt++;
      }
    }
    return successAmt;
  }

  @CacheEvict(value = KeyCache.USER_NAME, key = "#userSession.orgId",allEntries = true)
  @Transactional(rollbackFor = Exception.class)
  public Boolean addUser(Long storeId, List<String> userColumn, UserSession userSession, String ipAddr) {
    String userNo = userColumn.get(0);
    String name = userColumn.get(1);
    String mobile = userColumn.get(2);
    String teamName = userColumn.get(3);
    String customOrgRoles = userColumn.get(4);
    String customBranchOrgRoles = userColumn.get(5);
    String customTeamRoles = userColumn.get(6);

    Long orgId = userSession.getOrgId();
    List<Map<String, Object>> errorList = new ArrayList<>();
    try {
      // 是否账号上限
      userService.checkUserLimit(orgId, 1);
    } catch (ApiException e){
      for (int i=0;i<7;i++) {
        errorList.add(buildColumnError(i, FileStoreEnums.ColumnErrorCode.USER_LIMIT));
      }
      return false;
    }
    // 用户编号处理
    handleUserNo(userNo,orgId,errorList);
    // 姓名处理
    handleName(name, errorList);
    // 手机号处理
    handleMobile(mobile,orgId,errorList);
    // 小组处理
    DepTeam depTeam = handleTeam(teamName,orgId,errorList);
    // 角色处理
    List<Role> roleList = handleRole(depTeam, customOrgRoles, customBranchOrgRoles, customTeamRoles, orgId, errorList);

    if (ObjectUtil.isNotEmpty(errorList)){
      // 错误信息保存到redis
      JSONArray valueJson = JSONArray.parseArray(JSON.toJSONString(userColumn));
      valueJson.add(errorList);
      redisUtil.lSet(KeyCache.FILE_USER_ERROR_LIST + storeId, JSONArray.toJSONString(valueJson), 24*60*60);
      return false;
    }

    Boolean isConnectDuyan = userService.isConnectDuyan(orgId);
    // 组建用户数据
    String initPassword = userService.getInitPassword(systemConfig.getLocalDeploy(), isConnectDuyan, mobile);
    User user = buildUser(userNo, name, mobile, initPassword, depTeam, userSession.getId(), isConnectDuyan);

    // 插入数据库
    int result = userService.insertSelective(user);
    if (result != 1) {
      throw new ApiException("添加用户失败！");
    }
    // 创建角色关联关系
    userRoleService.createUserRoleRels(user.getId(), roleList);

    String desc = String.format("添加用户[id:%s,用户名:%s,手机号:%s,用户编号:%s]", user.getId(),user.getName(),user.getMobile(),user.getUserNo());
    accountLogService.createAccountLog(AccountLogEnums.Type.ADD_USER, desc, JSON.toJSONString(user), userSession, ipAddr);

    // 发送短信(不回滚)
    if (StrUtil.isNotBlank(user.getMobile()) && (!systemConfig.getLocalDeploy() || isConnectDuyan)) {
      String content = String.format(SmsContants.SMS_CODE_USER_ADD, initPassword);
      try {
        yunpianManager.sendSms(user.getMobile(), content, false);
      } catch (Exception e) {
        log.error("发送登录密码失败,手机号:{},内容:{}", user.getMobile(), content, e);
      }
    }
    return true;
  }

  /**
   * 构建用户
   *
   * @param userNo         用户编号
   * @param name           姓名
   * @param mobile         手机号
   * @param initPassword   init密码
   * @param depTeam        小组
   * @param createBy       创建者
   * @param isConnectDuyan 是连接度言
   * @return {@link User}
   */
  private User buildUser(String userNo, String name, String mobile, String initPassword, DepTeam depTeam, Long createBy, Boolean isConnectDuyan) {
    User user = new User();
    user.setCreateBy(createBy);
    user.setUpdateBy(createBy);
    user.setMobile(mobile);
    user.setName(name);
    // 设置密码
    String salt = UserUtils.generateSalt();
    user.setSalt(salt);
    String pwd = UserUtils.generateMd5(salt, initPassword);
    user.setPassword(pwd);
    if (ObjectUtil.equals(depTeam.getUnderTeam(), DepTeamEnums.UnderTeam.NOT.getCode())) {
      user.setDepId(depTeam.getParentId());
    }
    user.setTeamId(depTeam.getId());
    user.setOrgId(depTeam.getOrgId());
    user.setUserNo(userNo);

    Integer isOldUser = User.OldUser.NO.getCode();
    // 本地化且未开通呼叫中心为老用户，其他为新员工
    if (systemConfig.getLocalDeploy() && !isConnectDuyan) {
      isOldUser = User.OldUser.YES.getCode();
    }
    user.setOldUser(isOldUser);
    return user;
  }

  /**
   * 小组处理
   *
   * @param teamName  小组名称
   * @param orgId     公司id
   * @param errorList 错误列表
   * @return {@link DepTeam}
   */
  private DepTeam handleTeam(String teamName, Long orgId, List<Map<String, Object>> errorList) {
    if (StrUtil.isBlank(teamName)){
      errorList.add(buildColumnError(3, FileStoreEnums.ColumnErrorCode.NOT_BLANK));
      return null;
    }
    List<DepTeam> depTeams = depTeamService.getByName(teamName,orgId);
    if (ObjectUtil.isEmpty(depTeams)){
      errorList.add(buildColumnError(3, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
      return null;
    }else if (depTeams.size()>1){
      errorList.add(buildColumnError(3, FileStoreEnums.ColumnErrorCode.TEAM_NOT_CONFIRM));
      return null;
    }
    return depTeams.get(0);
  }

  /**
   * 角色处理
   *
   * @param depTeam             小组
   * @param customOrgRoles       自定义总公司角色
   * @param customBranchOrgRoles 自定义分公司角色
   * @param customTeamRoles      自定义小组角色
   * @param orgId                公司id
   * @param errorList            错误列表
   */
  private List<Role> handleRole(DepTeam depTeam, String customOrgRoles, String customBranchOrgRoles, String customTeamRoles, Long orgId, List<Map<String, Object>> errorList) {
    List<Role> allRoleList = new ArrayList<>();
    if (StrUtil.isBlank(customOrgRoles)&&StrUtil.isBlank(customBranchOrgRoles)&&StrUtil.isBlank(customTeamRoles)){
      errorList.add(buildColumnError(4, FileStoreEnums.ColumnErrorCode.NOT_ALL_BLANK));
      errorList.add(buildColumnError(5, FileStoreEnums.ColumnErrorCode.NOT_ALL_BLANK));
      errorList.add(buildColumnError(6, FileStoreEnums.ColumnErrorCode.NOT_ALL_BLANK));
      errorList.add(buildColumnError(7, FileStoreEnums.ColumnErrorCode.NOT_ALL_BLANK));
      return allRoleList;
    }
    // 自定义总公司角色处理
    if (StrUtil.isNotBlank(customOrgRoles)){
      customOrgRoles = StrUtil.replace(customOrgRoles,"，",",");
      List<String> customOrgRoleNames = StrUtil.splitTrim(customOrgRoles, ",");
      if (ObjectUtil.isNotEmpty(customOrgRoleNames)){
        List<Role> customOrgRoleList = roleService.getCustomRoleByName(customOrgRoleNames,orgId,RoleEnums.DepType.HEAD_OFFICE);
        // 有角色名称不存在
        if (customOrgRoleNames.size()!=customOrgRoleList.size()){
          errorList.add(buildColumnError(5, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
        }
        allRoleList.addAll(customOrgRoleList);
      }
    }

    // 自定义分公司角色处理
    if (StrUtil.isNotBlank(customBranchOrgRoles)){
      // 直属小组 不存在分公司
      if (ObjectUtil.isNotNull(depTeam)&&ObjectUtil.equals(depTeam.getUnderTeam(), DepTeamEnums.UnderTeam.YES.getCode())) {
        errorList.add(buildColumnError(3, FileStoreEnums.ColumnErrorCode.NOT_EXIST_BRANCH_ORG));
        errorList.add(buildColumnError(6, FileStoreEnums.ColumnErrorCode.NOT_EXIST_BRANCH_ORG));
      } else {
        customBranchOrgRoles = StrUtil.replace(customBranchOrgRoles,"，",",");
        List<String> customBranchOrgRoleNames = StrUtil.splitTrim(customBranchOrgRoles, ",");
        if (ObjectUtil.isNotEmpty(customBranchOrgRoleNames)){
          List<Role> customBranchOrgRoleList = roleService.getCustomRoleByName(customBranchOrgRoleNames,orgId,RoleEnums.DepType.BRANCH_OFFICE);
          // 有角色名称不存在
          if (customBranchOrgRoleNames.size()!=customBranchOrgRoleList.size()){
            errorList.add(buildColumnError(6, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
          }
          allRoleList.addAll(customBranchOrgRoleList);
        }
      }
    }

    // 自定义小组角色处理
    if (StrUtil.isNotBlank(customTeamRoles)){
      customTeamRoles = StrUtil.replace(customTeamRoles,"，",",");
      List<String> customTeamRoleNames = StrUtil.splitTrim(customTeamRoles, ",");
      if (ObjectUtil.isNotEmpty(customTeamRoleNames)){
        List<Role> customTeamRoleList = roleService.getCustomRoleByName(customTeamRoleNames,orgId,RoleEnums.DepType.TEAM);
        // 有角色名称不存在
        if (customTeamRoleNames.size()!=customTeamRoleList.size()){
          errorList.add(buildColumnError(7, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
        }
        allRoleList.addAll(customTeamRoleList);
      }
    }

    if (ObjectUtil.isEmpty(allRoleList)){
      errorList.add(buildColumnError(4, FileStoreEnums.ColumnErrorCode.NOT_ALL_BLANK));
      errorList.add(buildColumnError(5, FileStoreEnums.ColumnErrorCode.NOT_ALL_BLANK));
      errorList.add(buildColumnError(6, FileStoreEnums.ColumnErrorCode.NOT_ALL_BLANK));
      errorList.add(buildColumnError(7, FileStoreEnums.ColumnErrorCode.NOT_ALL_BLANK));
    }

    return allRoleList;
  }

  /**
   * 用户编号处理
   *
   * @param userNo    用户编号
   * @param orgId     组织id
   * @param errorList 错误列表
   */
  private void handleUserNo(String userNo, Long orgId, List<Map<String, Object>> errorList) {
    if (StrUtil.isBlank(userNo)){
      errorList.add(buildColumnError(0, FileStoreEnums.ColumnErrorCode.NOT_BLANK));
      return;
    }
    String regex = "^[\\u4e00-\\u9fa5A-Za-z0-9@#$%^&*_+-=]{0,}$";
    if (!ReUtil.isMatch(regex,userNo)){
      errorList.add(buildColumnError(0, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
      return;
    }
    // 在本公司下进行判重
    User query = new User();
    query.setOrgId(orgId);
    query.setStatus(User.Status.NORMAL.getCode());
    query.setUserNo(userNo);
    List<User> users = userService.select(query);
    if (users.size() > 0) {
      errorList.add(buildColumnError(0, FileStoreEnums.ColumnErrorCode.NOT_REPEAT));
    }
  }

  /**
   * 用户姓名处理
   *
   * @param name    用户姓名
   * @param errorList 错误列表
   */
  private void handleName(String name, List<Map<String, Object>> errorList) {
    if (StrUtil.isBlank(name)){
      errorList.add(buildColumnError(1, FileStoreEnums.ColumnErrorCode.NOT_BLANK));
      return;
    }

    String regex = "^[\\u4e00-\\u9fa50-9a-zA-Z_]*$";
    if (!ReUtil.isMatch(regex,name)){
      errorList.add(buildColumnError(1, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
    }
  }

  /**
   * 手机号处理
   *
   * @param mobile    手机号
   * @param orgId     组织id
   * @param errorList 错误列表
   */
  private void handleMobile(String mobile, Long orgId, List<Map<String, Object>> errorList) {
    if (StrUtil.isNotBlank(mobile)) {
      String regex = "^[1][0-9]{10}$";
      if (!ReUtil.isMatch(regex,mobile)){
        errorList.add(buildColumnError(2, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
        return;
      }
      User query = new User();
      query.setOrgId(orgId);
      query.setStatus(User.Status.NORMAL.getCode());
      query.setMobile(mobile);
      List<User> users = userService.select(query);
      if (users.size() > 0) {
        errorList.add(buildColumnError(2, FileStoreEnums.ColumnErrorCode.NOT_REPEAT));
        return;
      }

      // 全局判重
      Example userExp = new Example(User.class);
      userExp
        .createCriteria()
        .andNotEqualTo("orgId", orgId)
        .andEqualTo("mobile", mobile)
        .andEqualTo("status", User.Status.NORMAL.getCode())
        .andEqualTo("interimStatus", User.InterimStatus.NORMAL.getCode());
      users = userService.selectByExample(userExp);
      if (users.size() > 0) {
        errorList.add(buildColumnError(2, FileStoreEnums.ColumnErrorCode.NOT_REPEAT));
        return;
      }

      // 是否需要删除其他公司的账号
      Example userExp1 = new Example(User.class);
      userExp1
        .createCriteria()
        .andNotEqualTo("orgId", orgId)
        .andEqualTo("mobile", mobile)
        .andNotEqualTo("status", User.Status.DELETE.getCode())
        .andEqualTo("interimStatus", User.InterimStatus.PENDING.getCode());
      // 其他公司手机号码相同的员工，并且是待定状态的
      List<User> oldUsers = userService.selectByExample(userExp1);
      if (ObjectUtil.isNotEmpty(oldUsers)) {
        List<Long> userIds = oldUsers.stream().map(User::getId).collect(Collectors.toList());
        User userUpdate = new User();
        userUpdate.setStatus(User.Status.DELETE.getCode());
        Example exampleUpdate = new Example(User.class);
        exampleUpdate.createCriteria().andIn("id",userIds);
        userService.updateByExampleSelective(userUpdate,exampleUpdate);
      }
    }
  }

}
