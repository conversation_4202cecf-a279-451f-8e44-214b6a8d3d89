package com.anmi.collection.service.wp;

import cn.duyan.constant.DefaultConstants;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.common.enums.CaseOperationEnums;
import com.anmi.collection.common.enums.UserWorkPhoneEnums;
import com.anmi.collection.constant.WorkPhoneConstant;
import com.anmi.collection.dto.wp.*;
import com.anmi.collection.entity.requset.cases.CaseOperationOperate;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.requset.wp.WorkPhoneAuthParam;
import com.anmi.collection.entity.requset.wp.WpCallback;
import com.anmi.collection.entity.response.sys.user.UserLoginVO;
import com.anmi.collection.entity.response.sys.user.UserVO;
import com.anmi.collection.entity.response.wp.UserWorkPhoneVO;
import com.anmi.collection.entity.requset.wp.UserWorkPhoneQuery;
import com.anmi.collection.entity.response.wp.WorkPhoneAgent;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.UserCenterManager;
import com.anmi.collection.manager.WorkPhoneManager;
import com.anmi.collection.mapper.UserWorkPhoneMapper;
import com.anmi.collection.service.CaseOperationService;
import com.anmi.collection.service.CaseService;
import com.anmi.collection.service.UserService;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseOperation;
import com.anmi.domain.user.OrgWorkPhone;
import com.anmi.domain.user.User;
import com.anmi.domain.user.UserWorkPhone;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/24 14:40
 */
@Service
@Slf4j
public class UserWorkPhoneService {
    @Resource
    private UserWorkPhoneMapper userWorkPhoneMapper;
    @Resource
    private UserService userService;
    @Resource
    private CaseOperationService caseOperationService;
    @Resource
    private OrgWorkPhoneService orgWorkPhoneService;
    @Resource
    private WorkPhoneManager workPhoneManager;
    @Resource
    private UserCenterManager userCenterManager;
    @Resource
    private CaseService caseService;
    @Resource
    private WpWechatService wpWechatService;

    public static final String X_APP_AUTHORIZATION_HEAD = "PALM:";
    public static final String X_APP_AUTHORIZATION = "Authorization";
    private static final String AGENT_URL_SUFFIX = "/palm/api/open/user/getUserListByUserNo";
    private static final String CALL_URL_SUFFIX = "/palm/api/open/push/anmi/make/call";
    private static final String CALL_INFO_URL_SUFFIX = "/palm/api/open/userCall/getUserCallByUuids";
    private static final String PHONE_NUMBER_URL_SUFFIX = "/palm/api/open/phoneNumber/getPhoneNumberListByIccIds";



    public PageOutput<UserWorkPhoneVO> getAgentList(UserWorkPhoneQuery query) {
        UserSession userSession = UserUtils.getTokenUser();
        query.setOrgId(userSession.getOrgId());
        if (UserUtils.likeBranchAdmin()) {
            query.setDepId(userSession.getDepId());
        }
        if (UserUtils.likeTeamLeader()) {
            query.setTeamId(userSession.getTeamId());
        }
        PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<UserWorkPhoneVO> list = userWorkPhoneMapper.getUserWorkPhoneList(query);
        PageOutput<UserWorkPhoneVO> pageOutput =
                new PageOutput(
                        page.getPageNum(),
                        page.getPageSize(),
                        page != null ? (int) page.getTotal() : list.size(),
                        list);
        return pageOutput;
    }

    public PageOutput<UserVO> getUserList(UserWorkPhoneQuery query) {
        UserSession userSession = UserUtils.getTokenUser();
        query.setOrgId(userSession.getOrgId());
        if (UserUtils.likeBranchAdmin()) {
            query.setDepId(userSession.getDepId());
        }
        if (UserUtils.likeTeamLeader()) {
            query.setTeamId(userSession.getTeamId());
        }
        PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<UserVO> userList = userWorkPhoneMapper.getUserList(query);
        PageOutput<UserVO> pageOutput =
                new PageOutput(
                        page.getPageNum(),
                        page.getPageSize(),
                        page != null ? (int) page.getTotal() : userList.size(),
                        userList);
        return pageOutput;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addUserWorkPhone(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        UserSession userSession = UserUtils.getTokenUser();
        OrgWorkPhone orgWorkPhone = orgWorkPhoneService.getOrgWorkPhone(userSession.getOrgId());
        List<User> userList = userService.selectByIdList(userIds);
        List<String> userNos = userList.stream().map(User::getUserNo).collect(Collectors.toList());

        String url = orgWorkPhone.getHost() + AGENT_URL_SUFFIX;
        String apikey = orgWorkPhone.getApikey();
        String secret = orgWorkPhone.getSecret();
        WpUser wpUser = new WpUser();
        wpUser.setUserNoList(userNos);
        SortedMap<String, Object> contentMap = userCenterManager.beanToMapFastJson(wpUser);
        SortedMap<String, Object> map = userCenterManager.getCommonMap(apikey);
        String content = userCenterManager.getContentMd5(contentMap, secret);
        String sign = userCenterManager.getSignWp(content, map, secret);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(X_APP_AUTHORIZATION, X_APP_AUTHORIZATION_HEAD + apikey);
        headerMap.put(DefaultConstants.X_APP_KEY, apikey);
        headerMap.put(DefaultConstants.X_APP_TIMESTAMP, String.valueOf(map.get(DefaultConstants.X_APP_TIMESTAMP)));
        headerMap.put(DefaultConstants.X_APP_NONCE, String.valueOf(map.get(DefaultConstants.X_APP_NONCE)));
        headerMap.put(DefaultConstants.CONTENT_MD5, content);
        headerMap.put(DefaultConstants.SIGN, sign);
        String errorMsg = "";
        List<WorkPhoneAgent> workPhoneAgents = new ArrayList<>();
        try {
            ResultMessage<List<WorkPhoneAgent>> result = workPhoneManager.getWorkPhoneAgentByUserNo(url, headerMap, wpUser);
            if (Objects.nonNull(result) && Objects.equals(result.getStatus(), 200)) {
                workPhoneAgents = result.getData();
            } else {
                errorMsg = result.getMessage();
                throw new ApiException(errorMsg);
            }
        } catch (Exception e) {
                log.error("调用工作手机获取坐席异常：", e);
//                throw new ApiException(StringUtils.isNoneBlank(errorMsg) ? errorMsg : "调用工作手机获取坐席异常");
        }
        List<UserWorkPhone> userWorkPhones = new ArrayList<>();
        List<WorkPhoneAgent> finalWorkPhoneAgents = workPhoneAgents;
        userList.forEach(user -> {
            UserWorkPhone userWorkPhone = new UserWorkPhone();
            userWorkPhone.setUserId(user.getId());
            userWorkPhone.setOrgId(user.getOrgId());
            userWorkPhone.setCreator(userSession.getId());
            userWorkPhone.setUpdater(userSession.getId());
            userWorkPhone.setCreateTime(new Date());
            userWorkPhone.setUpdateTime(new Date());
            WorkPhoneAgent phoneAgent = finalWorkPhoneAgents.parallelStream().filter(workPhoneAgent ->
                    Objects.equals(workPhoneAgent.getUserNo(), user.getUserNo())).findFirst().orElse(null);
            userWorkPhone.setStatus(phoneAgent == null ? 0 : 1);
            userWorkPhone.setWpPhone(phoneAgent == null ? null : phoneAgent.getAccount());
            userWorkPhone.setWpName(phoneAgent == null ? null : phoneAgent.getName());
            userWorkPhone.setWpCallPhone(UserWorkPhoneEnums.WpCallPhone.OPEN.getCode());
            userWorkPhone.setWpAddWechat(UserWorkPhoneEnums.WpAddWechat.OPEN.getCode());
            userWorkPhone.setWpSendSms(UserWorkPhoneEnums.WpSendSms.OPEN.getCode());
            userWorkPhone.setWpCustomSmsContent(UserWorkPhoneEnums.WpCustomSmsContent.OPEN.getCode());
            userWorkPhone.setWpCustomWechatApply(UserWorkPhoneEnums.WpCustomWechatApply.OPEN.getCode());
            userWorkPhones.add(userWorkPhone);
        });
        userWorkPhoneMapper.insertList(userWorkPhones);
    }


    @Transactional(rollbackFor = Exception.class)
    public void refreshStatus(UserSession userSession) {
        Long userId = userSession.getId();
        Long orgId = userSession.getOrgId();
        Example example = new Example(UserWorkPhone.class);
        example.and().andEqualTo("orgId", orgId);
        List<UserWorkPhone> userWorkPhones = userWorkPhoneMapper.selectByExample(example);
        List<Long> userIds = userWorkPhones.stream().map(UserWorkPhone::getUserId).collect(Collectors.toList());
        OrgWorkPhone orgWorkPhone = orgWorkPhoneService.getOrgWorkPhone(orgId);
        List<User> userList = userService.selectByIdList(userIds);
        List<String> userNos = userList.stream().map(User::getUserNo).collect(Collectors.toList());
        List<WorkPhoneAgent> workPhoneAgents = new ArrayList<>();
        String url = orgWorkPhone.getHost() + AGENT_URL_SUFFIX;
        String apikey = orgWorkPhone.getApikey();
        String secret = orgWorkPhone.getSecret();
        WpUser wpUser = new WpUser();
        wpUser.setUserNoList(userNos);
        SortedMap<String, Object> contentMap = userCenterManager.beanToMapFastJson(wpUser);
        SortedMap<String, Object> map = userCenterManager.getCommonMap(apikey);
        String content = userCenterManager.getContentMd5(contentMap, secret);
        String sign = userCenterManager.getSignWp(content, map, secret);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(X_APP_AUTHORIZATION, X_APP_AUTHORIZATION_HEAD + apikey);
        headerMap.put(DefaultConstants.X_APP_KEY, apikey);
        headerMap.put(DefaultConstants.X_APP_TIMESTAMP, String.valueOf(map.get(DefaultConstants.X_APP_TIMESTAMP)));
        headerMap.put(DefaultConstants.X_APP_NONCE, String.valueOf(map.get(DefaultConstants.X_APP_NONCE)));
        headerMap.put(DefaultConstants.CONTENT_MD5, content);
        headerMap.put(DefaultConstants.SIGN, sign);
        String errorMsg = "";
        try {
            ResultMessage<List<WorkPhoneAgent>> result = workPhoneManager.getWorkPhoneAgentByUserNo(url, headerMap, wpUser);
            if (Objects.nonNull(result) && Objects.equals(result.getStatus(), 200)) {
                workPhoneAgents = result.getData();
            } else {
                errorMsg = result.getMessage();
                throw new ApiException(errorMsg);
            }
        } catch (Exception e) {
            log.error("调用工作手机获取坐席异常：", e);
            throw new ApiException(StringUtils.isNoneBlank(errorMsg) ? errorMsg : "调用工作手机获取坐席异常");
        }
        List<UserWorkPhone> validUserWorkPhones = new ArrayList<>();
        List<Long> invalidUserIds = new ArrayList<>();
        List<WorkPhoneAgent> finalWorkPhoneAgents = workPhoneAgents;
        userList.forEach(user -> {
            WorkPhoneAgent phoneAgent = finalWorkPhoneAgents.parallelStream().filter(workPhoneAgent ->
                    Objects.equals(workPhoneAgent.getUserNo(), user.getUserNo())).findFirst().orElse(null);
            if (phoneAgent != null) {
                UserWorkPhone workPhone = userWorkPhones.stream().filter(userWorkPhone -> Objects.equals(user.getId(),
                        userWorkPhone.getUserId())).findFirst().orElse(null);
                UserWorkPhone userWorkPhone = new UserWorkPhone();
                userWorkPhone.setId(workPhone.getId());
                userWorkPhone.setUserId(user.getId());
                userWorkPhone.setWpPhone(phoneAgent.getAccount());
                userWorkPhone.setWpName(phoneAgent.getName());
                userWorkPhone.setStatus(WorkPhoneConstant.IS_VALID);
                userWorkPhone.setUpdateTime(new Date());
                userWorkPhone.setUpdater(userId);
                validUserWorkPhones.add(userWorkPhone);
            } else {
                invalidUserIds.add(user.getId());
            }
        });
        if (!CollectionUtils.isEmpty(validUserWorkPhones)) {
            validUserWorkPhones.forEach(userWorkPhone -> {
                userWorkPhoneMapper.updateByPrimaryKeySelective(userWorkPhone);
            });
        }
        if (!CollectionUtils.isEmpty(invalidUserIds)) {
            userWorkPhoneMapper.updateInvalidStatus(invalidUserIds, userId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void delUserWorkPhone(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        Example example = new Example(UserWorkPhone.class);
        example.and().andIn("id", ids);
        userWorkPhoneMapper.deleteByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    public String callByWorkPhone(CaseOperationOperate operate) {
        // 工作手机打电话，由后端生成催记唯一标识uuid，传给工作手机，以备后续回调回填管理录音记录
        String uuid = UUID.randomUUID().toString();
        // 先插入一条催记
        operate.setCallUuid(uuid);
        operate.setCallStyle((byte)1);
        operate.setSubmitType(CaseOperationEnums.SubmitType.WORK_PHONE.getCode());
        caseOperationService.addAutomatic(operate);
        UserSession userSession = UserUtils.getTokenUser();
        User user = userService.selectByPrimaryKey(userSession.getId());
        OrgWorkPhone orgWorkPhone = orgWorkPhoneService.getOrgWorkPhone(userSession.getOrgId());
        WpCall wpCall = new WpCall();
        wpCall.setUserNo(user.getUserNo());
        // 0使⽤第⼀个卡槽⼿机卡,1使⽤第⼆卡槽⼿机卡，2⾃动选择⼀个卡
        wpCall.setSolt(operate.getSlot());
        wpCall.setUuid(uuid);
        wpCall.setTargetNumber(operate.getMobile());
        String url = orgWorkPhone.getHost() + CALL_URL_SUFFIX;
        String apikey = orgWorkPhone.getApikey();
        String secret = orgWorkPhone.getSecret();
        SortedMap<String, Object> contentMap = userCenterManager.beanToMapFastJson(wpCall);
        SortedMap<String, Object> map = userCenterManager.getCommonMap(apikey);
        String content = userCenterManager.getContentMd5(contentMap, secret);
        String sign = userCenterManager.getSignWp(content, map, secret);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(X_APP_AUTHORIZATION, X_APP_AUTHORIZATION_HEAD + apikey);
        headerMap.put(DefaultConstants.X_APP_KEY, apikey);
        headerMap.put(DefaultConstants.X_APP_TIMESTAMP, String.valueOf(map.get(DefaultConstants.X_APP_TIMESTAMP)));
        headerMap.put(DefaultConstants.X_APP_NONCE, String.valueOf(map.get(DefaultConstants.X_APP_NONCE)));
        headerMap.put(DefaultConstants.CONTENT_MD5, content);
        headerMap.put(DefaultConstants.SIGN, sign);
        String errorMsg = "";
        try {
            ResultMessage<List<WorkPhoneAgent>> result = workPhoneManager.callByWorkPhone(url, headerMap, wpCall);
            if (Objects.nonNull(result) && !Objects.equals(result.getStatus(), 200)) {
                errorMsg = result.getMessage();
                throw new ApiException(errorMsg);
            }
        } catch (Exception e) {
            log.error("调用工作手机打电话异常：", e);
            throw new ApiException(StringUtils.isNoneBlank(errorMsg) ? errorMsg : "调用工作手机打电话异常");
        }
        return uuid;
    }

    public void workPhoneCallback(List<WpCallback> wpCallbackList) {
        if (CollectionUtils.isEmpty(wpCallbackList)) {
            return;
        }
        wpCallbackList.forEach(wpCallback -> {
            String uuid = wpCallback.getUuid();
            CaseOperation operation = caseOperationService.selectOne(new CaseOperation(uuid));
            if (operation == null) {
                log.info("工作手机电话记录uuid: {}, 不存在", uuid);
                return;
            }
            CaseOperation newOperation = new CaseOperation();
            newOperation.setId(operation.getId());
            newOperation.setCallUuid(uuid);
            newOperation.setCallTime(wpCallback.getCallTime());
            newOperation.setCaller(wpCallback.getCaller());
            newOperation.setCallDurtion(wpCallback.getCallDuration() == null ? 0 : wpCallback.getCallDuration());
            newOperation.setRingDurtion(wpCallback.getRingDuration() == null ? 0 : wpCallback.getRingDuration());
            newOperation.setOutcome(wpCallback.getOutcome() == null ? "FAIL" : wpCallback.getOutcome());
            newOperation.setCallee(wpCallback.getCallee());
            // 电话结果为未填写时使用度言电话结果
            if (CaseOperationEnums.CallType.NOT_FILLED.getCode() == operation.getCallType()) {
                newOperation.setCallType(
                        wpCallback.getOutcome() == null ? operation.getCallType() : caseOperationService.getOutcome(wpCallback.getOutcome()));
            }
            // 更新数据
            caseOperationService.updateByPrimaryKeySelective(newOperation);
            caseOperationService.syncCaseFollowInfo(operation.getCaseId());
            Case ca=caseService.selectByPrimaryKey(operation.getCaseId());
            caseOperationService.syncCaseDebtorFollowInfo(ca.getDebtId());
        });
    }

    public String getVoiceUrlByUuid(String uuid, UserSession userSession) {
        OrgWorkPhone orgWorkPhone = orgWorkPhoneService.getOrgWorkPhone(userSession.getOrgId());
        String apikey = orgWorkPhone.getApikey();
        WpCall wpCall = new WpCall();
        wpCall.setApiKey(apikey);
        wpCall.setUuidList(Lists.newArrayList(uuid));
        String url = orgWorkPhone.getHost() + CALL_INFO_URL_SUFFIX;
        String errorMsg = "";
        try {
            ResultMessage<List<WpCallInfo>> result = workPhoneManager.getCallInfo(url, wpCall);
            if (Objects.nonNull(result) && Objects.equals(result.getStatus(), 200)) {
                List<WpCallInfo> wpCallInfoList = result.getData();
                return CollectionUtils.isEmpty(wpCallInfoList) ? null : wpCallInfoList.get(0).getRecordingFile();
            } else {
                errorMsg = result.getMessage();
                throw new ApiException(errorMsg);
            }
        } catch (Exception e) {
            log.error("调用工作手机打电话异常：", e);
            throw new ApiException(StringUtils.isNoneBlank(errorMsg) ? errorMsg : "调用工作手机打电话异常");
        }
    }

    public String getPhoneNumberByIccId(String iccId, Long orgId) {
        OrgWorkPhone orgWorkPhone = orgWorkPhoneService.getOrgWorkPhone(orgId);
        if (Objects.isNull(orgWorkPhone)) {
            throw new ApiException("请先开通工作手机功能");
        }
        String url = orgWorkPhone.getHost() + PHONE_NUMBER_URL_SUFFIX;
        String apikey = orgWorkPhone.getApikey();
        String secret = orgWorkPhone.getSecret();
        SortedMap<String, Object> contentMap = new TreeMap<>();
        contentMap.put("iccIds", iccId);
        SortedMap<String, Object> map = userCenterManager.getCommonMap(apikey);
        String content = userCenterManager.getContentMd5(contentMap, secret);
        String sign = userCenterManager.getSignWp(content, map, secret);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(X_APP_AUTHORIZATION, X_APP_AUTHORIZATION_HEAD + apikey);
        headerMap.put(DefaultConstants.X_APP_KEY, apikey);
        headerMap.put(DefaultConstants.X_APP_TIMESTAMP, String.valueOf(map.get(DefaultConstants.X_APP_TIMESTAMP)));
        headerMap.put(DefaultConstants.X_APP_NONCE, String.valueOf(map.get(DefaultConstants.X_APP_NONCE)));
        headerMap.put(DefaultConstants.CONTENT_MD5, content);
        headerMap.put(DefaultConstants.SIGN, sign);
        String errorMsg = "";
        try {
            ResultMessage<List<PhoneNumber>> result = workPhoneManager.getPhoneNumberListByIccIds(url, iccId, headerMap);
            if (Objects.nonNull(result) && Objects.equals(result.getStatus(), 200)) {
                List<PhoneNumber> data = result.getData();
                return CollectionUtils.isEmpty(data) ? null : data.get(0).getNumber();
            } else {
                errorMsg = result.getMessage();
                throw new ApiException(errorMsg);
            }
        } catch (Exception e) {
            log.error("调用工作手机根据iccId获取手机号异常：", e);
            throw new ApiException(StringUtils.isNoneBlank(errorMsg) ? errorMsg : "调用工作手机根据iccId获取手机号异常");
        }
    }

    public void isBindWorkPhone(UserLoginVO loginVO, Long userId){
        UserWorkPhone query = new UserWorkPhone();
        query.setUserId(userId);
        UserWorkPhone userWorkPhone = userWorkPhoneMapper.selectOne(query);
        loginVO.setWorkPhoneAgent(userWorkPhone == null ? 0 : 1);
        if (userWorkPhone != null) {
            loginVO.setWpCallPhone(userWorkPhone.getWpCallPhone());
            loginVO.setWpSendSms(userWorkPhone.getWpSendSms());
            loginVO.setWpAddWechat(userWorkPhone.getWpAddWechat());
            loginVO.setWpCustomSmsContent(userWorkPhone.getWpCustomSmsContent());
            loginVO.setWpCustomWechatApply(userWorkPhone.getWpCustomWechatApply());
        }
    }


    public void workPhoneAgent(UserLoginVO loginVO, Long orgId, Long userId) {
        OrgWorkPhone orgWorkPhone = orgWorkPhoneService.getOrgWorkPhone(orgId);
        if (orgWorkPhone == null || Objects.equals(orgWorkPhone.getStatus(), WorkPhoneConstant.IS_OFF)) {
            loginVO.setWorkPhoneAgent(WorkPhoneConstant.IS_OFF);
        } else {
            UserWorkPhone query = new UserWorkPhone();
            query.setUserId(userId);
            UserWorkPhone userWorkPhone = userWorkPhoneMapper.selectOne(query);
            if (userWorkPhone == null) {
                loginVO.setWorkPhoneAgent(WorkPhoneConstant.IS_OFF);
                return;
            }
            loginVO.setWorkPhoneAgent(WorkPhoneConstant.IS_OPEN);
            loginVO.setWpCallPhone(userWorkPhone.getWpCallPhone());
            loginVO.setWpSendSms(userWorkPhone.getWpSendSms());
            loginVO.setWpAddWechat(userWorkPhone.getWpAddWechat());
            loginVO.setWpCustomSmsContent(userWorkPhone.getWpCustomSmsContent());
            loginVO.setWpCustomWechatApply(userWorkPhone.getWpCustomWechatApply());
        }
    }


    /**
     * 修改坐席权限
     *
     * @param workPhoneAuthParam 工作手机权限参数
     */
    public void editWorkPhoneAuth(WorkPhoneAuthParam workPhoneAuthParam) {
        UserSession userSession = UserUtils.getTokenUser();
        workPhoneAuthParam.setUpdateBy(userSession.getId());
        userWorkPhoneMapper.editWorkPhoneAuth(workPhoneAuthParam);
    }


    /**
     * 获取催员绑定的微信信息
     *
     * @param userId 催员id
     * @return 微信信息
     */
    public List<UserWorkPhoneVO> queryUserBindWx(Long userId) {
        UserSession userSession = UserUtils.getTokenUser();

        User user = userService.selectByPrimaryKey(userId);
        if (StringUtils.isBlank(user.getUserNo())) {
            return Collections.emptyList();
        }
        //user.setUserNo("12312,89757");
        List<WpWechatInfo> wpWechatInfos = wpWechatService.queryWpUserWechat(user.getUserNo(), userSession.getOrgId());
        // 关于wechatNo和originWechatNo说明，传给前端的是originWechatNo，
        // originWechatNo为唯一微信标识，固定存在，wechatNo从工作手机获取可能为空，实际情况根据用户是否更改过微信号而定，未更改过为空，更改过则有数据
        // 根据微信号查询微信对话接口中的传参wechatNo使用wechatNo和originWechatNo都可，根据实际业务场景使用
        ArrayList<UserWorkPhoneVO> vos = new ArrayList<>();
        wpWechatInfos.forEach(item -> {
            UserWorkPhoneVO userWorkPhoneVO = new UserWorkPhoneVO();
            userWorkPhoneVO.setUserNo(user.getUserNo());
            userWorkPhoneVO.setUserName(user.getName());
            userWorkPhoneVO.setUserId(user.getId());
            userWorkPhoneVO.setNickName(item.getNickname());
            userWorkPhoneVO.setWechatNo(item.getOriginWechatNo());
            userWorkPhoneVO.setImg(item.getImg());
            vos.add(userWorkPhoneVO);
        });
        return vos;
    }


    /**
     * 根据催员id查询对应用户信息
     *
     * @param userId
     * @return
     */
    public UserWorkPhone queryUserAuthByUserId(Long userId) {
        Example example = new Example(UserWorkPhone.class);
        example.and().andEqualTo("userId", userId);
        List<UserWorkPhone> userWorkPhones = userWorkPhoneMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(userWorkPhones)) {
            return null;
        }
        return userWorkPhones.get(0);
    }


}
