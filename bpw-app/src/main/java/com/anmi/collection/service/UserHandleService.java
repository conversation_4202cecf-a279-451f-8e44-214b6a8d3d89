package com.anmi.collection.service;

import com.anmi.collection.common.enums.RoleEnums;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.mapper.RoleMapper;
import com.anmi.collection.mapper.UserMapper;
import com.anmi.domain.user.DepTeam;
import com.anmi.domain.user.Role;
import com.anmi.domain.user.User;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class UserHandleService {

  @Autowired private DepTeamService depTeamService;
  @Autowired private DuyanManager duyanManager;
  @Autowired private UserMapper userMapper;
  @Autowired private RoleMapper roleMapper;
  @Autowired private InspectorsService inspectorsService;

  public void addDuyanAdmin(User user, Long duyanOrgId, Long updateBy) {
    if (user.getDuyanAdmin() != null && user.getDuyanAdmin() == 1) {
      return;
    }
    // 必须是安米管理员
    List<Role> roleList = roleMapper.selectRolesByUserId(user.getId());
    // 系统级总公司管理员、分公司管理员、小组长
    List<Integer> adminType =
        Lists.newArrayList(
            RoleEnums.type.SUB_ACCOUNT_ADMIN.getCode(),
            RoleEnums.type.BRANCH_ADMIN.getCode(),
            RoleEnums.type.TEAM_LEADER.getCode());
    Long sysAdminSize = roleList.stream().filter(r -> adminType.contains(r.getType())).count();
    // 自定义总公司管理员、分公司管理员、小组长
    List<Integer> depType =
        Lists.newArrayList(
            RoleEnums.DepType.HEAD_OFFICE.getCode(),
            RoleEnums.DepType.BRANCH_OFFICE.getCode(),
            RoleEnums.DepType.TEAM.getCode());
    Long customAdminSize =
        roleList.stream()
            .filter(
                r ->
                    depType.contains(r.getDepType())
                        && RoleEnums.type.CUSTOM_ADMIN.getCode() == r.getType())
            .count();
    if (sysAdminSize == 0 && customAdminSize == 0) {
      throw new ApiException(user.getMobile() + ":度言管理员必须是安米管理员！");
    }
    user.setDuyanAdmin((byte) User.DuyanAdmin.YES.getCode());
    user.setUpdateBy(updateBy);
    user.setUpdateTime(new Date());
    if (user.getDuyanAccountId() == null) {
      Long accountId = duyanManager.createDuyanUserAndRole(user, null, null, updateBy);
      user.setDuyanAccountId(accountId);
    } else {
      // 管理员设置
      Boolean isAdmin = duyanManager.setAdmin(user.getDuyanAccountId(), duyanOrgId);
      if (!isAdmin) {
        throw new ApiException(user.getMobile() + ":设置失败，请联系管理员");
      }
    }
    userMapper.updateByPrimaryKey(user);
  }

  public void addDuyanAgent(User user, Long duyanOrgId, Long updateBy) {
    if (user.getDuyanSeat() != null && user.getDuyanSeat() == 1) {
      return;
    }
    // 必须是安米催员
    List<Role> roleList = roleMapper.selectRolesByUserId(user.getId());
    DepTeam team = depTeamService.selectByPrimaryKey(user.getTeamId());
    if (team == null) {
      throw new ApiException(user.getMobile() + ":员工需设置所属小组才能添加坐席");
    }
    Long duyanTeamId = team.getDuyanReferId();
    user.setDuyanSeat((byte) User.DuyanSeat.YES.getCode());
    user.setUpdateBy(updateBy);
    user.setUpdateTime(new Date());
    // 设置坐席
    if (user.getDuyanAccountId() == null) {
      Long accountId = duyanManager.createDuyanUserAndRole(user, duyanTeamId, null, updateBy);
      user.setDuyanAccountId(accountId);
    } else {
      Boolean isAgent = duyanManager.setAgent(duyanOrgId, user.getDuyanAccountId(), duyanTeamId);
      if (!isAgent) {
        throw new ApiException(user.getMobile() + ":设置失败，请联系管理员");
      }
    }
    userMapper.updateByPrimaryKey(user);
  }

  public void addDuyanTeamLeader(User user, Long duyanOrgId, Long updateBy) {
    if (user.getDuyanTeamLeader() != null && user.getDuyanTeamLeader() == 1) {
      return;
    }
    // 必须是安米小组长
    List<Role> roleList = roleMapper.selectRolesByUserId(user.getId());
    Long teamLeaderSize =
        roleList.stream()
            .filter(
                r ->
                    RoleEnums.type.TEAM_LEADER.getCode() == r.getType()
                        || (RoleEnums.type.CUSTOM_ADMIN.getCode() == r.getType()
                            && RoleEnums.DepType.TEAM.getCode() == r.getDepType()))
            .count();
    if (teamLeaderSize == 0) {
      throw new ApiException(user.getMobile() + ":呼叫中心小组长必须是安米小组长！");
    }
    // 同步组长前需要是度言坐席
    if (user.getDuyanAccountId() == null || user.getDuyanSeat() == 0) {
      throw new ApiException(user.getMobile() + ":设置为度言团队主管之前，需要在度言创建员工并且是坐席！");
    }
    user.setDuyanTeamLeader((byte) User.DuyanTeamLeader.YES.getCode());
    user.setUpdateBy(updateBy);
    // user.setUpdateTime(new Date());
    userMapper.updateByPrimaryKey(user);
    Boolean isTeamLeader = duyanManager.setTeamLeader(duyanOrgId, user.getDuyanAccountId());
    if (!isTeamLeader) {
      throw new ApiException(user.getMobile() + ":设置失败，请联系管理员");
    }
  }

  public void addDuyanFifoAgent(User user, Long duyanOrgId, Long updateBy) {
    if (user.getDuyanFifoAgent() != null && user.getDuyanFifoAgent() == 1) {
      return;
    }
    // 设置预测式外呼计划前需要是度言坐席
    if (user.getDuyanAccountId() == null || user.getDuyanSeat() == 0) {
      throw new ApiException(user.getMobile() + ":设置为预测式外呼坐席必须是坐席！");
    }
    user.setDuyanFifoAgent((byte) User.DuyanFifoAgent.YES.getCode());
    user.setUpdateBy(updateBy);
    // user.setUpdateTime(new Date());
    userMapper.updateByPrimaryKey(user);
    Boolean isFifoAgent = duyanManager.setFifoAgent(duyanOrgId, user.getDuyanAccountId());
    if (!isFifoAgent) {
      throw new ApiException(user.getMobile() + ":度言设置预测式外呼坐席失败！");
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void delDuyanAccount(User user, Long duyanOrgId) {
    if (user.getDuyanAccountId() == null) {
      return;
    }
    Long duyanAccountId = user.getDuyanAccountId();
    user.setDuyanAccountId(null);
    user.setDuyanSeat((byte) 0);
    user.setDuyanFifoAgent((byte) 0);
    user.setDuyanTeamLeader((byte) 0);
    user.setDuyanAdmin((byte) 0);
    user.setDuyanInspector((byte) 0);
    user.setDuyanInspectorSup(0);
    userMapper.updateByPrimaryKey(user);
    // 删除质检小组
    inspectorsService.updateInspectorsRels(user.getId(), null);
    duyanManager.removeUser(duyanAccountId, duyanOrgId);
  }

  public void delDuyanAgent(User user, Long duyanOrgId, Long updateBy) {
    if (user.getDuyanSeat() == null || user.getDuyanSeat() == 0) {
      return;
    }
    user.setDuyanSeat((byte) User.DuyanSeat.NO.getCode());
    user.setDuyanTeamLeader((byte) User.DuyanTeamLeader.NO.getCode());
    user.setDuyanFifoAgent((byte) User.DuyanFifoAgent.NO.getCode());
    user.setUpdateBy(updateBy);
    user.setUpdateTime(new Date());
    if (user.getDuyanAdmin() == 0 && user.getDuyanInspector() == 0) {
      // 如果不是度言管理员，也同时删除度言那边的员工，删除后安米这边也需要设置duyanTeamLeader为0
      Long duyanAccountId = user.getDuyanAccountId();
      user.setDuyanAccountId(null);
      user.setDuyanInspectorSup(0);
      duyanManager.removeUser(duyanAccountId, duyanOrgId);
    } else {
      // 移除坐席角色
      Boolean isAgent = duyanManager.cancelAgent(duyanOrgId, user.getDuyanAccountId());
      if (isAgent) {
        throw new ApiException(user.getMobile() + ":取消坐席失败");
      }
    }
    userMapper.updateByPrimaryKey(user);
  }

  public void delDuyanAdmin(User user, Long duyanOrgId, Long updateBy) {
    if (user.getDuyanAdmin() == null || user.getDuyanAdmin() == 0) {
      return;
    }
    user.setDuyanAdmin((byte) User.DuyanAdmin.NO.getCode());
    user.setUpdateBy(updateBy);
    user.setUpdateTime(new Date());
    if (user.getDuyanSeat() == 0 && user.getDuyanInspector() == 0) {
      // 如果不是度言坐席，也同时删除度言那边的员工
      Long duyanAccountId = user.getDuyanAccountId();
      user.setDuyanAccountId(null);
      user.setDuyanTeamLeader((byte) User.DuyanTeamLeader.NO.getCode());
      user.setDuyanFifoAgent((byte) User.DuyanFifoAgent.NO.getCode());
      user.setDuyanInspectorSup(0);
      duyanManager.removeUser(duyanAccountId, duyanOrgId);
    } else {
      // 移除管理员角色
      Boolean isAdmin = duyanManager.removeAdmin(user.getDuyanAccountId(), duyanOrgId);
      if (isAdmin) {
        throw new ApiException(user.getMobile() + ":取消团队管理员失败");
      }
    }
    userMapper.updateByPrimaryKey(user);
  }

  public void delDuyanTeamLeader(User user, Long duyanOrgId, Long updateBy) {
    if (user.getDuyanTeamLeader() == null || user.getDuyanTeamLeader() == 0) {
      return;
    }
    Boolean isTeamLeader = duyanManager.removeTeamLeader(duyanOrgId, user.getDuyanAccountId());
    if (isTeamLeader) {
      throw new ApiException(user.getMobile() + ":取消呼叫中心小组长失败");
    }
    user.setDuyanTeamLeader((byte) User.DuyanTeamLeader.NO.getCode());
    user.setUpdateBy(updateBy);
    // user.setUpdateTime(new Date());
    userMapper.updateByPrimaryKey(user);
  }

  public void delDuyanFifoAgent(User user, Long duyanOrgId, Long updateBy) {
    if (user.getDuyanFifoAgent() == null || user.getDuyanFifoAgent() == 0) {
      return;
    }
    Boolean isFifoAgent = duyanManager.removeFifoAgent(duyanOrgId, user.getDuyanAccountId());
    if (isFifoAgent) {
      throw new ApiException(user.getMobile() + ":取消预测式外呼坐席失败！");
    }
    user.setDuyanFifoAgent((byte) User.DuyanFifoAgent.NO.getCode());
    user.setUpdateBy(updateBy);
    // user.setUpdateTime(new Date());
    userMapper.updateByPrimaryKey(user);
  }
}
