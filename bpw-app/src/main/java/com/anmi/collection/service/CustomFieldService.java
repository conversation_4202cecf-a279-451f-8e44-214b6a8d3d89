package com.anmi.collection.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.enums.CustomFieldEnums;
import com.anmi.collection.common.enums.RoleEnums;
import com.anmi.collection.entity.response.cases.CustomFieldVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.init.CacheLoader;
import com.anmi.collection.utils.CommonUtil;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.sys.CustomField;
import com.anmi.domain.user.Role;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by jianghp on 2019-04-30.
 */

@Service
public class CustomFieldService extends BaseService<CustomField> {

    /**
     * 获取案件自定义字段
     *
     * 之前分公司与总公司添加的自定义字段是隔离的，不可进行互相查看
     * 优化后，不进行隔离，分公司与总公司添加的自定义字段可互相查看
     *
     * @return
     */
    public List<CustomFieldVO> getListByPage() {
        Example cusFieldExp = new Example(CustomField.class);
        Example.Criteria cusFieldCrt = cusFieldExp.createCriteria();
        cusFieldCrt.andEqualTo("orgId", getOrgId());
//        if (role.getType() == RoleEnums.type.分公司管理员.getCode()) {
//            cusFieldCrt.andEqualTo("depId", null);
//        }
        // Page page = super.setPage(pageParam);
        List<CustomField> locFields = selectByExample(cusFieldExp);
        List<CustomFieldVO> vos = new ArrayList<>();
        locFields.stream().forEach(field -> {
            CustomFieldVO vo = convertToVO(field);
            vos.add(vo);
        });
//        PageOutput pageOutput = new PageOutput(page.getPageNum(), page.getPageSize(),
//                page != null ? (int) page.getTotal() : vos.size(),
//                vos);
        return vos;
    }

    /**
     * 转换
     *
     * @param field
     * @return
     */
    private CustomFieldVO convertToVO(CustomField field) {
        CustomFieldVO vo = new CustomFieldVO();
        BeanUtils.copyProperties(field, vo);
        vo.setStatus((int) field.getStatus());
        if (field.getStatus() == CustomFieldEnums.Status.NORMAL.getCode()) {
            vo.setShow(true);
        } else {
            vo.setShow(false);
        }
        return vo;
    }

    public List<String> checkFields(List<String> fields) {
        Long orgId = getOrgId();
        Example cusFieldExp = new Example(CustomField.class);
        Example.Criteria cusFieldCrt = cusFieldExp.createCriteria();
        cusFieldCrt.andEqualTo("status", CustomFieldEnums.Status.NORMAL.getCode()).
                andEqualTo("orgId", orgId).
                andIn("name", fields);
        List<String> ret = new ArrayList<>();
        // 在公用字段里查询是否存在
        ret.addAll(CacheLoader.isExistInSysFields(getTokenUser().getLanguage(), fields));
        // 在自定义字段查询是否存在
        List<CustomField> locFields = selectByExample(cusFieldExp);
        List<String> locExits =
                locFields.stream().map(CustomField::getName).collect(Collectors.toList());
        ret.addAll(locExits);
        return ret.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 添加自定义字段到数据库
     *
     * @param fields
     */
    @Transactional
    public List<String> add(List<String> fields) {
        if (CommonUtil.isEmpty(fields)) {
            throw new ApiException("添加字段不能为空！");
        }
        List<CustomField> newFields = null;
        if (UserUtils.likeAdmin()) {
            newFields = convert(fields, getOrgId(), null);
        } else if (UserUtils.likeBranchAdmin()) {
            newFields = convert(fields, getOrgId(), getTokenUser().getDepId());
        }
        int res = insertBatch(newFields);
        if (res != newFields.size()) {
            throw new ApiException("添加自定义字段失败！");
        }
//        Map<String, String> cache = CacheLoader.customFields.get(orgId);
//        if (cache == null) {
//            cache = new HashMap<>();
//        }
//        for (CustomField field : newFields) {
//            cache.put(field.getValue(), field.getName());
//        }
//        CacheLoader.customFields.put(orgId, cache);
        return fields;
    }

    private List<CustomField> convert(List<String> fields, Long orgId, Long depId) {
        List<CustomField> newFields = new ArrayList<>(fields.size());
        String next = null;
        for (String f : fields) {
            next = getNextSerial();
            CustomField field = new CustomField();
            field.setCreateTime(new Date());
            field.setUpdateTime(new Date());
            field.setCreateBy(getTokenUser().getId());
            field.setUpdateBy(getTokenUser().getId());
            field.setDepId(depId);
            field.setOrgId(orgId);
            field.setValue(next);
            field.setName(f.trim());
            field.setStatus((byte) CustomFieldEnums.Status.NORMAL.getCode());
            newFields.add(field);
        }
        return newFields;
    }

    /**
     * 获取到下一个序列号
     *
     * @return
     */
    private String getNextSerial() {
        String uuid = UUID.randomUUID().toString().substring(0, 7);
        if (getRole().getType() == RoleEnums.type.BRANCH_ADMIN.getCode()) {
            return uuid + "_" + getOrgId() + "_" + getTokenUser().getDepId();
        } else {
            return uuid + "_" + getOrgId();
        }
    }

    /**
     * 获取公司的所有自定义字段
     *
     * @param orgId
     * @return
     */
    public List<CustomField> selectByOrgId(Long orgId) {
        Example example = new Example(CustomField.class);
        example.createCriteria().andEqualTo("orgId", orgId);
        return selectByExample(example);
    }

    /**
     * 根据字段名查询对应自定义字段
     *
     * @param orgId     公司id
     * @param valueList 字段名
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public Map<String, CustomField> selectByValues(Long orgId, List<String> valueList) {
        if (CollectionUtils.isEmpty(valueList)) {
            return Collections.emptyMap();
        }
        Example example = new Example(CustomField.class);
        example.createCriteria().andEqualTo("orgId", orgId)
            .andIn("value", valueList);
        List<CustomField> customFields = selectByExample(example);
        return customFields.stream().collect(Collectors.toMap(CustomField::getValue, f -> f));
    }

    public Map<String, List<CustomField>> selectByNames(Long orgId, List<String> nameList) {
        Map<String, List<CustomField>> map = new HashMap<>();
        if (CollectionUtils.isEmpty(nameList)) {
            return map;
        }
        Example example = new Example(CustomField.class);
        example.createCriteria().andEqualTo("orgId", orgId).andIn("name", nameList);
        List<CustomField> customFields = selectByExample(example);
        Map<String, List<CustomField>> listMap = customFields.stream().collect(Collectors.groupingBy(CustomField::getName));
        return listMap;
    }

    public CustomField selectByName(Long orgId, String name) {
        if (ObjectUtil.isNull(orgId) || StrUtil.isBlank(name)) {
            return null;
        }
        Example example = new Example(CustomField.class);
        example.createCriteria().andEqualTo("orgId", orgId).andEqualTo("name", name);
        List<CustomField> customFields = selectByExample(example);
        if (ObjectUtil.isEmpty(customFields)){
            return null;
        }
        return customFields.get(0);
    }
}
