package com.anmi.collection.service.workorder;

import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.workorder.WorkOrderVO;
import com.anmi.collection.mapper.workorder.WorkOrderLinkMapper;
import com.anmi.collection.mapper.workorder.WorkOrderMapper;
import com.anmi.collection.utils.AnmiBeanutils;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.domain.workorder.WorkOrder;
import com.anmi.domain.workorder.WorkOrderLink;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/17 15:42
 */
@Service
public class WorkOrderLinkService {
    @Resource
    private WorkOrderLinkMapper workOrderLinkMapper;
    @Resource
    private WorkOrderMapper workOrderMapper;

    @Transactional(rollbackFor = Exception.class)
    public void linkWorkOrder(Long orderId, List<Long> linkOrderIds, UserSession session) {
        if (CollectionUtils.isEmpty(linkOrderIds)) {
            return;
        }
        List<WorkOrderLink> linkList = new ArrayList<>();
        linkOrderIds.forEach(id -> {
            WorkOrderLink link = new WorkOrderLink();
            link.setOrderId(orderId);
            link.setLinkOrderId(id);
            link.setOrgId(session.getOrgId());
            link.setCreateBy(session.getId());
            link.setCreateTime(new Date());
            link.setUpdateTime(new Date());
            linkList.add(link);
        });
        workOrderLinkMapper.insertList(linkList);
    }

    public List<WorkOrderVO> listLinkWorkOrder(Long orderId) {
        Example example = new Example(WorkOrderLink.class);
        example.and().andEqualTo("orderId", orderId);
        List<WorkOrderLink> linkList = workOrderLinkMapper.selectByExample(example);
        List<Long> linkOrderIds = linkList.stream().map(WorkOrderLink::getLinkOrderId).distinct().collect(Collectors.toList());
       if (CollectionUtils.isEmpty(linkOrderIds)) {
           return new ArrayList<>();
       }
        Example e = new Example(WorkOrder.class);
        e.and().andIn("id", linkOrderIds);
        List<WorkOrder> workOrders = workOrderMapper.selectByExample(e);
        List<WorkOrderVO> voList = new ArrayList<>();
        workOrders.forEach(workOrder -> {
            WorkOrderVO vo = AnmiBeanutils.copy(workOrder, WorkOrderVO.class);
            voList.add(vo);
        });
        return voList;
    }

    /**
     * 根据工单id删除对应工单关联信息
     *
     * @param orderIds 工单ids
     */
    public void delLinkByOrderIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        Example example = new Example(WorkOrderLink.class);
        example.and().andIn("orderId", orderIds)
                .orIn("linkOrderId", orderIds);
        workOrderLinkMapper.deleteByExample(example);
    }
}
