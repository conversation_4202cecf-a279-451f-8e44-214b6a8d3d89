package com.anmi.collection.service;

import cn.duyan.thread.DuyanThreadExecutor;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.dto.DuYanSendLogDTO;
import com.anmi.collection.dto.DuYanSmsReplyDTO;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.requset.yunpian.SmsReplyParam;
import com.anmi.collection.entity.response.yunpian.SmsReplyVO;
import com.anmi.collection.manager.MessageManager;
import com.anmi.collection.mapper.SmsReplyMapper;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.user.Company;
import com.anmi.domain.yunpian.SmsReply;
import com.anmi.domain.yunpian.SmsSend;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SmsReplyService extends BaseService<SmsReply> {
    @Resource
    private SmsReplyMapper smsReplyMapper;
    @Resource
    private MessageManager messageManager;
    @Resource
    private SmsSendService smsSendService;
    @Resource
    private CompanyService companyService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private static final String ASYNC_PREFIX = "async-";

    private DuyanThreadExecutor threadExecutor = new DuyanThreadExecutor("anmi-sms-reply-task");
    // 一天的毫秒数
    private static final long ONE_DAY_MILLIS = 24 * 60 * 60 * 1000;


    /**
     * 度言接口仅供查询时间跨度为一天
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param orgId     公司id
     */
    public void callSyncSmsReply(Date startTime, Date endTime, Long orgId) {
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(startTime);
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endTime);
        while (startCalendar.before(endCalendar)) {
            Date currentDate = startCalendar.getTime();
            Date nextDate = new Date(currentDate.getTime() + ONE_DAY_MILLIS);
            if (nextDate.after(endTime)) {
                nextDate = endTime;
            }
            syncSmsReply(currentDate, nextDate, orgId);
            startCalendar.add(Calendar.DAY_OF_MONTH, 1);
        }
    }

    /**
     * 同步度言回复信息
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param orgId     公司id
     */
    public void syncSmsReply(Date startTime, Date endTime, Long orgId) {
        // 获取短信回复接口
        List<DuYanSmsReplyDTO> dtos = messageManager.queryAllReplylogList(orgId, startTime.getTime(), endTime.getTime());
        // 解析处理短信回复格式
        Set<String> uuids = new HashSet<>();
        dtos.forEach(item -> {
            DuYanSendLogDTO sendLog = item.getSendLog();
            uuids.add(sendLog.getUuId());
        });
        List<SmsSend> smsSends = smsSendService.selectSmsSendByUuids(new ArrayList<>(uuids), orgId);
        Map<String, SmsSend> smsSendMap = smsSends.stream().collect(Collectors.toMap(SmsSend::getUuid, f -> f));
        List<SmsReply> smsReplies = new ArrayList<>();
        Date now = new Date();
        dtos.forEach(item -> {
            if (!smsSendMap.containsKey(item.getSendLog().getUuId())) {
                return;
            }
            SmsSend smsSend = smsSendMap.get(item.getSendLog().getUuId());
            SmsReply smsReply = new SmsReply();
            smsReply.setContent(item.getContent());
            smsReply.setReplyTime(DateUtil.date(item.getReplyTime()));
            smsReply.setMobile(item.getMobile());
            // 回复催员
            smsReply.setReceiver(smsSend.getCreateBy());
            smsReply.setCaseId(smsSend.getCaseId());
            smsReply.setUuid(smsSend.getUuid());
            smsReply.setSendId(smsSend.getId());
            smsReply.setOrgId(smsSend.getOrgId());
            smsReply.setCreateTime(now);
            smsReply.setUpdateTime(now);
            smsReplies.add(smsReply);
        });
        if (!CollectionUtils.isEmpty(smsReplies)) {
            smsReplyMapper.insertList(smsReplies);
        }
        // 修改公司短信拉取时间
        companyService.updateSmsReplyPullTime(endTime, orgId);
    }

    /**
     * 查询催员短信回复列表
     * 查询今日进行度言信息拉取，查询非今日查数据库
     *
     * @param param 查询条件
     * @return {@link PageOutput}<{@link SmsReplyVO}>
     */
    public PageOutput<SmsReplyVO> querySmsReplyList(SmsReplyParam param, PageParam pageParam) throws Exception {
        Date now = DateUtil.beginOfDay(new Date());
        Date replyEndTime = DateUtil.endOfDay(param.getReplyTime());
        Date replyStartTime = DateUtil.beginOfDay(param.getReplyTime());
        UserSession userSession = UserUtils.getTokenUser();
        // 查询今日，创建拉取回复异步任务
        if (DateUtil.compare(now, replyStartTime) == 0) {
            Company company = companyService.selectByPrimaryKey(userSession.getOrgId());
            Date pullTime = company.getSmsReplyPullTime() == null ? now : company.getSmsReplyPullTime();
            Date pullStopTime = new Date();
            if (DateUtil.compare(pullTime,pullStopTime) < 0) {
                // 查询五分钟内是否有人拉取
                SmsReply smsReply = new SmsReply();
                smsReply.setReplyTime(param.getReplyTime());
                smsReply.setOrgId(userSession.getOrgId());
                String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(smsReply).getBytes());
                String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
                if (StringUtils.isNotBlank(s)) {
                    log.info("当前已有相同短信回复查询任务5分钟内执行过，请稍后操作");
                } else {
                    stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 5, TimeUnit.MINUTES);
                    threadExecutor.execute(() -> {
                        callSyncSmsReply(pullTime, pullStopTime, userSession.getOrgId());
                    });
                }
            }
        }
        Page page = PageUtils.setPage(pageParam);
        Example example = new Example(SmsReply.class);
        example.and().andEqualTo("receiver", userSession.getId())
                     .andGreaterThan("replyTime", replyStartTime)
                     .andLessThan("replyTime", replyEndTime);
        if (StringUtils.isNotBlank(param.getMobile())) {
            example.and().andLike("mobile", param.getMobile() + "%");
        }
        List<SmsReply> smsReplies = smsReplyMapper.selectByExample(example);
        List<SmsReplyVO> vos = BeanUtil.copyPropertiesFromList(smsReplies, SmsReplyVO.class);
        return new PageOutput<SmsReplyVO>(pageParam.getPage(), pageParam.getLimit(),
                page != null ? (int) page.getTotal() : vos.size(),
                vos);
    }

}
