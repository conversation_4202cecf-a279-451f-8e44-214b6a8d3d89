package com.anmi.collection.service.lawsuit;

import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.entity.requset.lawsuit.LawsuitHandlerAddParam;
import com.anmi.collection.entity.requset.lawsuit.LawsuitHandlerUpdateParam;
import com.anmi.collection.entity.requset.lawsuit.LawsuitRecordParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.lawsuit.LawsuitHandlerVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.lawsuit.LawsuitHandlerMapper;
import com.anmi.collection.service.UserService;
import com.anmi.collection.utils.AuthBeanUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.lawsuit.Lawsuit;
import com.anmi.domain.lawsuit.LawsuitHandler;
import com.github.pagehelper.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class LawsuitHandlerService {
    @Resource
    private LawsuitHandlerMapper lawsuitHandlerMapper;
    @Resource
    private LawsuitService lawsuitService;
    @Resource
    private UserService userService;
    @Resource
    private LawsuitConfigService lawsuitConfigService;

    /**
     * 查询对应诉讼案件下的办理记录
     * @param param
     * @return
     */
    public PageOutput<LawsuitHandlerVO> queryLawsuitHandlerList(LawsuitRecordParam param) throws Exception {
        PageParam pageParam = new PageParam(param.getPage(), param.getLimit());
        Page page = PageUtils.setPage(pageParam);
        Example example = new Example(LawsuitHandler.class);
        example.and().andEqualTo("lawsuitId",param.getLawsuitId());
        example.orderBy("id").desc();
        List<LawsuitHandler> lawsuitHandlers = lawsuitHandlerMapper.selectByExample(example);

        List<LawsuitHandlerVO> lawsuitHandlerVOS = BeanUtil.copyPropertiesFromList(lawsuitHandlers, LawsuitHandlerVO.class);
        // 办理人姓名
        List<Long> processorList = lawsuitHandlers.stream().map(LawsuitHandler::getProcessor).distinct().collect(Collectors.toList());
        Map<Long, String> userMap = userService.queryUserMap(processorList);
        // 诉讼进度
        List<Long> processIdList = lawsuitHandlers.stream().map(LawsuitHandler::getProcessId).distinct().collect(Collectors.toList());
        Map<Long, String> processMap = lawsuitConfigService.selectConfigMap(processIdList);
        // code枚举值
        Map<Long, Long>  codeMap = lawsuitConfigService.selectCodeMap(processIdList);
        // 受理法院
        List<Long> courtIdList = lawsuitHandlers.stream().map(LawsuitHandler::getCourt).distinct().collect(Collectors.toList());
        Map<Long, String> courtMap = lawsuitConfigService.selectConfigMap(courtIdList);

        lawsuitHandlerVOS.forEach(item -> {
            if (userMap.containsKey(item.getProcessor())) {
                item.setProcessorName(userMap.get(item.getProcessor()));
            }
            if (processMap.containsKey(item.getProcessId())) {
                item.setProcessName(processMap.get(item.getProcessId()));
                item.setCode(codeMap.get(item.getProcessId()));
            }
            if (courtMap.containsKey(item.getCourt())) {
                item.setCourtName(courtMap.get(item.getCourt()));
            }
        });
        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), lawsuitHandlerVOS);
    }

    /**
     * 添加办理记录
     * @param lawsuitHandlerAddParam 办理记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void addLawsuitHandler(LawsuitHandlerAddParam lawsuitHandlerAddParam) {
        UserSession userSession = UserUtils.getTokenUser();
        LawsuitHandler lawsuitHandler = AuthBeanUtils.copy(lawsuitHandlerAddParam, LawsuitHandler.class);
        // 设置办理人
        lawsuitHandler.setProcessor(userSession.getId());
        lawsuitHandler.setOrgId(userSession.getOrgId());
        // 新增办理记录
        lawsuitHandlerMapper.insertSelective(lawsuitHandler);

        Lawsuit lawsuit = AuthBeanUtils.copy(lawsuitHandler, Lawsuit.class);
        lawsuit.setId(lawsuitHandler.getLawsuitId());
        lawsuit.setUpdateTime(new Date());
        lawsuit.setRemark(null);
        // 更新诉讼案件
        lawsuitService.updateLawsuit(lawsuit);
    }

    /**
     * 修改办理记录
     * 只能修改办理日期、备注
     * @param lawsuitHandlerUpdateParam 办理记录
     */
    public void updateLawsuitHandler(LawsuitHandlerUpdateParam lawsuitHandlerUpdateParam) {
        UserSession userSession = UserUtils.getTokenUser();
        LawsuitHandler lawsuitHandler = lawsuitHandlerMapper.selectByPrimaryKey(lawsuitHandlerUpdateParam.getId());
        if (lawsuitHandler == null) {
            throw new ApiException("编号id为"+ lawsuitHandlerUpdateParam.getId() + "的办理记录不存在！");
        }
        LawsuitHandler updateHandler = AuthBeanUtils.copy(lawsuitHandlerUpdateParam, LawsuitHandler.class);
        updateHandler.setProcessor(userSession.getId());
        updateHandler.setUpdateTime(new Date());
        lawsuitHandlerMapper.updateByPrimaryKeySelective(updateHandler);
    }

    /**
     * 删除对应id的办理记录
     * @param id
     */
    public void deleteLawsuitHandler(Long id) {
        LawsuitHandler lawsuitHandler = lawsuitHandlerMapper.selectByPrimaryKey(id);
        if (lawsuitHandler == null) {
            throw new ApiException("编号id为"+ id + "的办理记录不存在！");
        }
        lawsuitHandlerMapper.deleteByPrimaryKey(id);
    }
}
