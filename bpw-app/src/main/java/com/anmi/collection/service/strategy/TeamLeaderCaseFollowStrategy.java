package com.anmi.collection.service.strategy;

import com.anmi.collection.entity.requset.statistics.UserStatisticsQueryParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.statistics.*;
import com.anmi.collection.service.UserStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
@Component("teamLeaderCaseFollowStrategy")
public class TeamLeaderCaseFollowStrategy implements CaseFollowStrategy{
    @Autowired
    private UserStatisticsService userStatisticsService;

    private HashMap<String, Object> putMap(UserSession userSession){
        HashMap<String, Object> map = new HashMap<>();
        //加orgId为了走索引
        map.put("orgId",userSession.getOrgId());
        map.put("teamId",userSession.getTeamId());
        //区分缓存key
        map.put("id","team_" + userSession.getTeamId());
        return map;
    }

    @Override
    public ApplyInfoVO applyInfo(UserSession userSession) throws Exception {
        return userStatisticsService.getApplyInfo(putMap(userSession));
    }

    @Override
    public CaseFollowConsoleVO caseFollow(UserSession userSession) {
        return userStatisticsService.getCaseFollow(putMap(userSession));
    }

    @Override
    public CaseStateAnalyseVO caseStateAnalyse(UserSession userSession) {
        return userStatisticsService.getCaseStateAnalyse(putMap(userSession));
    }

    @Override
    public CaseConsoleStatisticsVO caseConsoleStatisToday(UserSession userSession) {
        return userStatisticsService.getCaseConsoleStatisToday(putMap(userSession));
    }

    @Override
    public CaseConsoleStatisticsVO caseConsoleStatistics(UserSession userSession,Integer type) {
        return userStatisticsService.getCaseConsoleStatistics(putMap(userSession),type);
    }

    @Override
    public RepaymentRankingVO repaymentRankingList(UserSession userSession, Integer type) throws Exception{
        return userStatisticsService.getRepaymentRankingList(putMap(userSession),type);
    }

    @Override
    public RepaymentAnalyseVO repaymentAnalyseList(UserSession userSession, Integer type) throws Exception{
        return userStatisticsService.getRepaymentAnalyseList(putMap(userSession),type);
    }

    @Override
    public RepaymentByDunnerIdVO repaymentListByDunnerIds(UserSession userSession, UserStatisticsQueryParam param)  throws Exception{
        return userStatisticsService.getRepaymentListByDunnerIds(putMap(userSession),param);
    }
}
