package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.enums.SpaceEnums;
import com.anmi.collection.entity.requset.sys.org.OrgFreeSpaceParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.space.OrgSpaceVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.OrgSpaceMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.redis.RedisLock;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.OrgSpace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrgSpaceService extends BaseService<OrgSpace> {
    @Resource
    private OrgSpaceMapper orgSpaceMapper;
    @Resource
    private RedisLock redisLock;

    /**
     * 调整免费存储空间
     * @param param
     */
    public void updateFreeSpace(OrgFreeSpaceParam param) {
        Long orgId = param.getOrgId();
        Example example = new Example(OrgSpace.class);
        example.and().andEqualTo("orgId", orgId);
        List<OrgSpace> orgSpaces = orgSpaceMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(orgSpaces)) {
            throw new ApiException("不存在orgId为" + orgId + "免费存储空间记录");
        }
        OrgSpace orgSpace = new OrgSpace();
        Integer freeSpace = param.getFreeSpace();
        if (freeSpace != null) {
            // g -> kb
            BigDecimal bd = new BigDecimal(freeSpace * 1024 * 1024);
            orgSpace.setFreeSpace(bd);
            orgSpace.setUpdateTime(new Date());
            orgSpaceMapper.updateByExampleSelective(orgSpace, example);
        }
    }

    /**
     * 添加使用存储空间记录
     * @param orgId
     * @param fileSize
     * @param type 1-外访附件 2-函件 3-诉讼材料文件
     */
    @Transactional(rollbackFor = Exception.class)
    public void addUseSpace(Long orgId, BigDecimal fileSize, Integer type) {
        OrgSpace orgSpace = new OrgSpace();
        orgSpace.setOrgId(orgId);
        orgSpace = this.selectFirst(orgSpace);
        BigDecimal useSpace = orgSpace.getUseSpace().add(fileSize);
        String lockKey = KeyCache.SPACE_USE_LOCK + orgId;
        try {
            if (!redisLock.tryLock(lockKey)) {
                log.error("锁超时，key:{}", lockKey);
                throw new ApiException("操作过于繁忙，请稍后再试");
            }
            if (SpaceEnums.Type.VISIT_FILE.getCode().equals(type)) {
                BigDecimal visitSpace = orgSpace.getVisitSpace().add(fileSize);
                orgSpace.setUseSpace(useSpace);
                orgSpace.setVisitSpace(visitSpace);
            }
            if (SpaceEnums.Type.LETTER_FILE.getCode().equals(type)) {
                BigDecimal letterSpace = orgSpace.getLetterSpace().add(fileSize);
                orgSpace.setUseSpace(useSpace);
                orgSpace.setLetterSpace(letterSpace);
            }
            if (SpaceEnums.Type.LAWSUIT_FILE.getCode().equals(type)) {
                BigDecimal lawsuitSpace = orgSpace.getLawsuitSpace().add(fileSize);
                orgSpace.setUseSpace(useSpace);
                orgSpace.setLawsuitSpace(lawsuitSpace);
            }
            orgSpace.setUpdateTime(new Date());
            Example example = new Example(OrgSpace.class);
            example.and().andEqualTo("orgId", orgId);
            orgSpaceMapper.updateByExampleSelective(orgSpace, example);
        } catch (Exception e) {
            log.error("获取已使用存储空间锁发生错误：", e);
            throw new ApiException("获取已使用存储空间锁发生错误");
        }finally {
            redisLock.unlock(lockKey);
        }
    }

    /**
     * 减去已删除文件
     * @param orgId
     * @param fileSize
     * @param type 1-外访 2-函件 3-法诉
     */
    @Transactional(rollbackFor = Exception.class)
    public void subtractUseSpace(Long orgId, BigDecimal fileSize, Integer type) {
        if (fileSize == null) {
            fileSize = BigDecimal.ZERO;
        }
        String lockKey = KeyCache.SPACE_USE_LOCK + orgId;
        try {
            if (!redisLock.tryLock(lockKey)) {
                log.error("锁超时，key:{}", lockKey);
                throw new ApiException("操作过于繁忙，请稍后再试");
            }
            OrgSpace orgSpace = new OrgSpace();
            orgSpace.setOrgId(orgId);
            OrgSpace updateOrgSpace = this.selectFirst(orgSpace);
            BigDecimal useSpace = updateOrgSpace.getUseSpace().subtract(fileSize);
            if (SpaceEnums.Type.VISIT_FILE.getCode().equals(type)) {
                BigDecimal visitSpace = updateOrgSpace.getVisitSpace().subtract(fileSize);
                updateOrgSpace.setUseSpace(useSpace);
                updateOrgSpace.setVisitSpace(visitSpace);
            }
            if (SpaceEnums.Type.LETTER_FILE.getCode().equals(type)) {
                BigDecimal letterSpace = updateOrgSpace.getLetterSpace().subtract(fileSize);
                updateOrgSpace.setUseSpace(useSpace);
                updateOrgSpace.setLetterSpace(letterSpace);
            }
            if (SpaceEnums.Type.LAWSUIT_FILE.getCode().equals(type)) {
                BigDecimal lawsuitSpace = updateOrgSpace.getLawsuitSpace().subtract(fileSize);
                updateOrgSpace.setUseSpace(useSpace);
                updateOrgSpace.setLawsuitSpace(lawsuitSpace);
            }
            updateOrgSpace.setUpdateTime(new Date());
            orgSpaceMapper.updateByPrimaryKeySelective(updateOrgSpace);
        }catch (Exception e) {
            log.error("重新计算已使用存储空间出现异常：", e);
            throw new ApiException("重新计算已使用存储空间出现异常");
        }finally {
            redisLock.unlock(lockKey);
        }

    }

    /**
     * 购买存储空间，添加总空间
     * @param orgId
     * @param spaceSize
     */
    public void addTotalSpace(Long orgId, BigDecimal spaceSize) {
        String lockKey = KeyCache.SPACE_TOTAL_LOCK + orgId;
        try {
            if (!redisLock.tryLock(lockKey)) {
                log.error("锁超时，key:{}", lockKey);
                throw new ApiException("操作过于繁忙，请稍后再试");
            }
            OrgSpace orgSpace = new OrgSpace();
            orgSpace.setOrgId(orgId);
            OrgSpace updateOrgSpace = this.selectFirst(orgSpace);
            BigDecimal totalSpace = updateOrgSpace.getTotalSpace().add(spaceSize);
            updateOrgSpace.setTotalSpace(totalSpace);
            updateOrgSpace.setUpdateTime(new Date());
            orgSpaceMapper.updateByPrimaryKeySelective(updateOrgSpace);
        } catch (Exception e) {
            log.error("获取锁发生错误：", e);
            throw new ApiException("获取锁发生错误");
        } finally {
            redisLock.unlock(lockKey);
        }
    }

    /**
     * 总存储空间去除已过期的记录
     * @param orgId
     * @param spaceSize
     */
    public void subtractTotalSpace(Long orgId, BigDecimal spaceSize) {
        String lockKey = KeyCache.SPACE_TOTAL_LOCK + orgId;
        try {
            if (!redisLock.tryLock(lockKey)) {
                log.error("锁超时，key:{}", lockKey);
                throw new ApiException("操作过于繁忙，请稍后再试");
            }
            OrgSpace orgSpace = new OrgSpace();
            orgSpace.setOrgId(orgId);
            OrgSpace updateOrgSpace = this.selectFirst(orgSpace);
            BigDecimal totalSpace = updateOrgSpace.getTotalSpace().subtract(spaceSize);
            updateOrgSpace.setTotalSpace(totalSpace);
            updateOrgSpace.setUpdateTime(new Date());
            orgSpaceMapper.updateByPrimaryKeySelective(updateOrgSpace);
        } catch (Exception e) {
            log.error("获取锁发生错误：", e);
            throw new ApiException("获取锁发生错误");
        } finally {
            redisLock.unlock(lockKey);
        }
    }

    /**
     * 查询所有存储空间使用信息
     * @return
     */
    public OrgSpaceVO getSpaceByType() {
        UserSession userSession = UserUtils.getTokenUser();
        OrgSpace orgSpace = new OrgSpace();
        orgSpace.setOrgId(userSession.getOrgId());
        orgSpace = this.selectFirst(orgSpace);
        OrgSpaceVO orgSpaceVO = new OrgSpaceVO();
        BeanUtils.copyProperties(orgSpace, orgSpaceVO);
        // 转化单位 kb->g
        orgSpaceVO.setLawsuitSpace(orgSpace.getLawsuitSpace().divide(new BigDecimal(1024 * 1024),2,BigDecimal.ROUND_HALF_UP));
        orgSpaceVO.setLetterSpace(orgSpace.getLetterSpace().divide(new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP));
        orgSpaceVO.setVisitSpace(orgSpace.getVisitSpace().divide(new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP));
        orgSpaceVO.setFreeSpace(orgSpace.getFreeSpace().divide(new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP));
        orgSpaceVO.setTotalSpace(orgSpace.getTotalSpace().divide(new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP).add(orgSpaceVO.getFreeSpace()));
        orgSpaceVO.setUseSpace(orgSpace.getUseSpace().divide(new BigDecimal(1024 * 1024), 2, BigDecimal.ROUND_HALF_UP));
        return orgSpaceVO;
    }

    /**
     * 查询在指定公司编号内的公司存储信息
     * @param orgIds
     * @return
     */
    public List<OrgSpace> querySpaceByOrgIds(List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Collections.emptyList();
        }
        orgIds = orgIds.stream().distinct().collect(Collectors.toList());
        Example example = new Example(OrgSpace.class);
        example.and().andIn("orgId", orgIds);
        List<OrgSpace> orgSpaces = orgSpaceMapper.selectByExample(example);
        return orgSpaces;
    }
}
