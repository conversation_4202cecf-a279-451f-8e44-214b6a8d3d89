package com.anmi.collection.service;

import com.anmi.collection.entity.requset.sys.org.OrgNoticeParam;
import com.anmi.collection.entity.response.sys.org.OrgNoticeVO;
import com.anmi.collection.mapper.OrgNoticeMapper;
import com.anmi.collection.utils.AuthBeanUtils;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.domain.user.OrgNotice;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/15 10:19
 */
@Service
public class OrgNoticeService {
    @Resource
    private OrgNoticeMapper orgNoticeMapper;


    @Transactional(rollbackFor = Exception.class)
    public void addOrgNotice(OrgNoticeParam param) {
        OrgNotice orgNotice = AuthBeanUtils.copy(param, OrgNotice.class);
        orgNotice.setCreateTime(new Date());
        orgNotice.setUpdateTime(new Date());
        orgNoticeMapper.insertSelective(orgNotice);
    }

    public OrgNoticeVO getOrgNotice(Long orgId) {
        Example example = new Example(OrgNotice.class);
        example.and().andEqualTo("orgId", orgId);
        example.orderBy("updateTime").desc();
        List<OrgNotice> orgNotices = orgNoticeMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(orgNotices)) {
            return null;
        }
        OrgNotice orgNotice = orgNotices.get(0);
        OrgNoticeVO orgNoticeVO = AuthBeanUtils.copy(orgNotice, OrgNoticeVO.class);
        return orgNoticeVO;
    }
}
