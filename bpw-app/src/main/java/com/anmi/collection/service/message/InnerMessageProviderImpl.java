package com.anmi.collection.service.message;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.anmi.collection.common.enums.NoticeEnums;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.dto.message.InnerMessageDTO;
import com.anmi.collection.dto.message.InnerMessageSendDetailDTO;
import com.anmi.collection.entity.requset.message.InnerMessageSendParam;
import com.anmi.collection.manager.mqtt.EmqClient;
import com.anmi.collection.utils.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class InnerMessageProviderImpl implements IMessageProvider<InnerMessageDTO>{
    @Resource
    private EmqClient emqClient;
    @Resource
    private SystemConfig systemConfig;

    @Override
    public String getNoticeType() {
        return NoticeEnums.Type.INNER_MESSAGE.getCode();
    }

    @Override
    public void sendMsg(InnerMessageDTO innerMessageSend) {
        String toUsers = innerMessageSend.getToUsers();
        Date now = new Date();
        List<InnerMessageSendParam.ToUser> toUserList = JSON.parseArray(toUsers, InnerMessageSendParam.ToUser.class);
        List<InnerMessageSendDetailDTO> sendDetails = innerMessageSend.getSendDetailList();
        toUserList.parallelStream().forEach(toUser-> {
            Optional<InnerMessageSendDetailDTO> first = sendDetails.parallelStream().filter(sendDetail -> ObjectUtil.equals(toUser.getUserId(), sendDetail.getToUserId())).findFirst();
            String topic = systemConfig.getMqttDefaultTopic()+"/"+ innerMessageSend.getOrgId() +"/"+toUser.getUserId();
            Map<String,Object> body = new HashMap<>();
            body.put("sendId", innerMessageSend.getId());
            body.put("fromUserName",innerMessageSend.getFromUserName());
            body.put("subject", innerMessageSend.getSubject());
            body.put("createTime",now.getTime());
            first.ifPresent(innerMessageSendDetailDTO -> body.put("sendDetailId", innerMessageSendDetailDTO.getId()));
            emqClient.publish(topic, JSONUtil.toJsonStr(body));
        });
    }


}
