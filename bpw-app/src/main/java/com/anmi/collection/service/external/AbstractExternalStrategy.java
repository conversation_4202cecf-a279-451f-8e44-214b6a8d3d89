package com.anmi.collection.service.external;

import com.anmi.collection.dto.CaseContact;
import com.anmi.collection.service.CompanyService;
import com.anmi.domain.cases.Case;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 外部接口
 *
 * <AUTHOR>
 */
public abstract class AbstractExternalStrategy implements External {
    @Resource
    private CompanyService companyService;

    /**
     * 查询校验开关状态
     *
     * @param orgId 公司id
     * @return Integer 开关状态
     */
    public Boolean checkSwitch(Long orgId) {
        return companyService.isOpenCaseCheck(orgId);
    }

    /**
     * 是否是可执行案件
     *
     * @param data      案件信息
     * @return boolean  是否可执行
     */
    public List<Case> filter(List<Case> data, Map<Integer, List<CaseContact>> filterCaseContactsMap) {
        return data;
    }
}
