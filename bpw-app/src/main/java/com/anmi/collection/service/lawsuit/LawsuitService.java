package com.anmi.collection.service.lawsuit;

import cn.duyan.thread.DuyanThreadExecutor;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.CaseLogEnums;
import com.anmi.collection.common.enums.CaseTemplateEnums;
import com.anmi.collection.common.enums.FileStoreEnums;
import com.anmi.collection.common.enums.LawsuitConfigEnums;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.constant.CommonConstant;
import com.anmi.collection.constant.LawsuitConstant;
import com.anmi.collection.constant.RegexConstant;
import com.anmi.collection.dto.fileStorage.ReadFileInfo;
import com.anmi.collection.dto.fileStorage.UploadCreatedFileStream;
import com.anmi.collection.dto.lawsuit.LawsuitUpdateExcel;
import com.anmi.collection.entity.requset.lawsuit.LawsuitProcessParam;
import com.anmi.collection.entity.requset.lawsuit.LawsuitUpdateEmptyParam;
import com.anmi.collection.entity.requset.lawsuit.LawsuitUpdateParam;
import com.anmi.collection.entity.requset.query.lawsuit.LawsuitQuery;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.lawsuit.*;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.lawsuit.LawsuitMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.*;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.utils.AuthBeanUtils;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.ExcelUtil;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.collection.utils.easyexcel.EasyExcelUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.lawsuit.Lawsuit;
import com.anmi.domain.lawsuit.LawsuitApply;
import com.anmi.domain.sys.DownloadTask;
import com.anmi.domain.sys.FileStore;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/21 10:08
 */
@Service
@Slf4j
public class LawsuitService extends BaseService<Lawsuit> {
    @Resource
    private LawsuitMapper lawsuitMapper;

    @Resource
    private CaseService caseService;
    @Resource
    private LawsuitApplyService lawsuitApplyService;
    @Resource
    private LawsuitCaseService lawsuitCaseService;
    @Resource
    private DeltService deltService;
    @Resource
    private LawsuitProcessService lawsuitProcessService;
    @Resource
    private LawsuitNoteService lawsuitNoteService;
    @Resource
    private FileStoreService fileStoreService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private LawsuitConfigService lawsuitConfigService;
    @Resource
    private DownloadTaskService downloadTaskService;
    @Resource
    private SystemConfig systemConfig;
    @Resource
    private LawsuitPayService lawsuitPayService;
    @Resource
    private LawsuitFileService lawsuitFileService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private FileStorageStrategyFactory fileStorageStrategyFactory;


    private DuyanThreadExecutor threadExecutor=new DuyanThreadExecutor("lawsuit-pool");


    /**
     * 诉讼申请审批通过加入诉讼
     * @param applyId
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addLawsuit(Long applyId) {
        LawsuitApply lawsuitApply = lawsuitApplyService.selectByPrimaryKey(applyId);
        Long caseId = lawsuitApply.getCaseId();
        Case caseInfo = caseService.selectByPrimaryKey(caseId);

        Lawsuit lawsuit = AuthBeanUtils.copy(lawsuitApply, Lawsuit.class);
        lawsuit.setId(null);
        lawsuit.setStatus(LawsuitConstant.LAWSUIT_STATUS_RUNNING);
        lawsuit.setSource(LawsuitConstant.LAWSUIT_SOURCE_AUDIT);
        // 从催收案件冗余相关字段数据
        lawsuit.setName(caseInfo.getName());
        lawsuit.setIdCard(caseInfo.getIdCard());
        lawsuit.setMobile(caseInfo.getOwnMobile());
        lawsuit.setOrgDeltId(caseInfo.getOrgDeltId());
        lawsuit.setCreateTime(new Date());
        lawsuit.setUpdateTime(new Date());
        lawsuit.setCaseId(caseInfo.getId());
        lawsuit.setOutSerialNo(caseInfo.getOutSerialTemp());
        Long amount = caseInfo.getAmount() == null ? 0 : caseInfo.getAmount();
        lawsuit.setAmount(new BigDecimal(amount.toString()).divide(new BigDecimal("1000"), 2, RoundingMode.DOWN));

        insertSelective(lawsuit);
        lawsuitCaseService.addLawsuitCase(Arrays.asList(lawsuit));
        //添加案件操作日志记录
        UserSession userSession = UserUtils.getTokenUser();
        CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
        caseBatchUpdateEvent.setType(CaseLogEnums.Type.CASE_TO_LAWSUIT.getCode());
        caseBatchUpdateEvent.setUserSession(userSession);
        caseBatchUpdateEvent.setCaseIds(Arrays.asList(caseId));
        applicationContext.publishEvent(caseBatchUpdateEvent);

        return lawsuit.getId();
    }

    /**
     * 管理员操作催收案件加入诉讼
     * @param caseList
     * @param handleType
     */
    @Transactional(rollbackFor = Exception.class)
    public void addLawsuit(List<Case> caseList, Integer handleType) {
        if (CollectionUtils.isEmpty(caseList)) {
            return;
        }
        List<Lawsuit> lawsuitList = new ArrayList<>();
        List<Lawsuit> mergeLawsuitList = new ArrayList<>();
        Map<Lawsuit, List<Case>> lawsuitListMap = new HashMap<>();
        if (Objects.equals(handleType, LawsuitConstant.HANDLE_TYPE_MERGE)) {
            List<Case> noDebtCaseList = caseList.stream().filter(ca -> Objects.isNull(ca.getDebtId())).collect(Collectors.toList());
            Map<Long, List<Case>> debtCaseMap = caseList.stream().filter(ca -> Objects.nonNull(ca.getDebtId())).
                    collect(Collectors.groupingBy(Case::getDebtId));
            debtCaseMap.forEach((debtId, cases)->{
                List<Case> sortCaseList = cases.parallelStream().sorted(Comparator.comparing(Case::getId).reversed()).collect(Collectors.toList());
                Case caseInfo = sortCaseList.get(0);
                Lawsuit lawsuit = getNewLawsuit(caseInfo);
                mergeLawsuitList.add(lawsuit);
                lawsuitListMap.put(lawsuit, sortCaseList);
            });
            noDebtCaseList.forEach(caseInfo -> {
                Lawsuit lawsuit = getNewLawsuit(caseInfo);
                lawsuitList.add(lawsuit);
            });
        } else {
            caseList.forEach(caseInfo -> {
                Lawsuit lawsuit = getNewLawsuit(caseInfo);
                lawsuitList.add(lawsuit);
            });
        }
        if (!CollectionUtils.isEmpty(mergeLawsuitList)) {
            lawsuitMapper.insertList(mergeLawsuitList);
            lawsuitCaseService.addLawsuitCase(lawsuitListMap);
        }
        if (!CollectionUtils.isEmpty(lawsuitList)) {
            lawsuitMapper.insertList(lawsuitList);
            lawsuitCaseService.addLawsuitCase(lawsuitList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateLawsuit(Lawsuit lawsuit) {
        lawsuit.setUpdateTime(new Date());
        lawsuitMapper.updateByPrimaryKeySelective(lawsuit);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateLawsuitById(LawsuitUpdateEmptyParam param, Long id) throws NoSuchFieldException, IllegalAccessException {
        Lawsuit lawsuit = lawsuitMapper.selectByPrimaryKey(id);
        handleLawsuitUpdateEmptyParam(lawsuit, param);
        lawsuit.setUpdateTime(new Date());
        lawsuitMapper.updateByPrimaryKey(lawsuit);

    }


    @Transactional(rollbackFor = Exception.class)
    public void updateLawsuit(MultipartFile file) throws IOException {
        UserSession userSession = UserUtils.getTokenUser();
        checkCaseFile(file, userSession.getLanguage());
        String fileName = file.getOriginalFilename();
        FileStore store = new FileStore();
        store.setTemplateType(CaseTemplateEnums.TempType.LAWSUIT_UPDATE.getCode());
        store.setOrgDeltId(0L);
        store.setProductId(0L);
        store.setType(FileStoreEnums.Type.EXCEL.getCode());
        store.setTemplateId(0L);
        store.setOrgId(userSession.getOrgId());
        store.setCreateTime(new Date());
        store.setUpdateTime(new Date());
        store.setCreateBy(userSession.getId());
        store.setName(fileName);
        fileStoreService.insertSelective(store);
        Long storeId = store.getId();
        fileName = StringUtils.substringBeforeLast(fileName, ".") + "_" + storeId + "." + StringUtils.substringAfterLast(fileName, ".");
        File tempFile = new File(CommonConstant.tempFilePath + fileName);
        file.transferTo(tempFile);
        threadExecutor.execute(()->{
            log.info("=======诉讼更新导入开始：=======storeId:{}", storeId);
            List<LawsuitUpdateExcel> lawsuitUpdateExcels = EasyExcelUtils.read(tempFile, LawsuitUpdateExcel.class, 2);
            int successCount = 0;
            int total = lawsuitUpdateExcels.size();
            int page = total/500 + ((total%500) == 0 ? 0 :1);
            log.info("=======诉讼更新导入统计：=======storeId:{}, total:{}, page:{}", storeId, total, page);
            List<Lawsuit> lawsuits = new ArrayList<>();
            for (int i =0; i < page; i++) {
                int start = i * 500;
                int end = (i + 1) * 500 >= total ? total : (i + 1) * 500;
                List<LawsuitUpdateExcel> updateExcelList = lawsuitUpdateExcels.subList(start, end);
                List<Lawsuit> lawsuitList = null;
                try {
                    lawsuitList = handleLawsuitUpdateExcelData(storeId, updateExcelList, userSession);
                } catch (InterruptedException e) {
                    log.error("处理更新数据出现异常，{}", e.getMessage());
                    e.printStackTrace();
                }
                if (!CollectionUtils.isEmpty(lawsuitList)) {
                    successCount = successCount + lawsuitList.size();
                    lawsuits.addAll(lawsuitList);
                }
                log.info("=======诉讼更新导入执行进度：=======storeId:{}，执行到第page:{}页", storeId, i);
            }
            lawsuits.forEach(lawsuit -> {
                lawsuit.setUpdateTime(new Date());
                lawsuitMapper.updateByPrimaryKeySelective(lawsuit);
                // 更新诉讼进度
                if (Objects.nonNull(lawsuit.getProcess())) {
                    LawsuitProcessParam param = new LawsuitProcessParam();
                    param.setLawsuitId(lawsuit.getId());
                    param.setProcessId(lawsuit.getProcess());
                    lawsuitProcessService.addLawsuitProcess(param, userSession);
                }
            });
            fileStoreService.handleFileStore(storeId, successCount, total, LawsuitConstant.LAWSUIT_UPDATE_EXCEL_HEAD_LIST);
            log.info("=======诉讼更新导入结束：=======storeId:{}", storeId);

        });
    }

    public LawsuitDetailVO getLawsuitDetail(Long id) {
        LawsuitDetailVO lawsuitDetailVO = new LawsuitDetailVO();
        CompletableFuture<Void> lawsuitFuture = CompletableFuture.runAsync(() -> {
            Lawsuit lawsuit = lawsuitMapper.selectByPrimaryKey(id);
            LawsuitVO lawsuitVO = toLawsuitVO(lawsuit);
            lawsuitDetailVO.setLawsuit(lawsuitVO);
        }, threadExecutor);
        CompletableFuture<Void> processFuture = CompletableFuture.runAsync(() -> {
            List<LawsuitProcessVO> lawsuitProcessVOList = lawsuitProcessService.getList(id);
            lawsuitDetailVO.setLawsuitProcess(lawsuitProcessVOList);
        }, threadExecutor);

        CompletableFuture<Void> caseFuture = CompletableFuture.runAsync(() -> {
            List<LawsuitCaseVO> lawsuitCaseVOList = lawsuitCaseService.getList(id);
            lawsuitDetailVO.setLawsuitCase(lawsuitCaseVOList);
        }, threadExecutor);
        CompletableFuture<Void> noteFuture = CompletableFuture.runAsync(() -> {
            LawsuitNoteVO lastNote = lawsuitNoteService.getLastNote(id);
            lawsuitDetailVO.setLawsuitNote(lastNote);
        }, threadExecutor);
        CompletableFuture<Void> amountFuture = CompletableFuture.runAsync(() -> {
            BigDecimal payAmount = lawsuitPayService.queryLawsuitPaySumById(id);
            lawsuitDetailVO.setRepayAmount(payAmount);

        }, threadExecutor);

        try {
            CompletableFuture.allOf(lawsuitFuture, processFuture, caseFuture, noteFuture, amountFuture).get();
        } catch (Exception e) {
            log.error("<=======诉讼详情等候所有任务执行过程报错：======>", e);
        }
        return lawsuitDetailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateLawsuit(LawsuitUpdateParam param, UserSession userSession) {
        if (Objects.nonNull(param.getStatus())) {
            updateLawsuitStatus(param, userSession);
        }
        if (Objects.nonNull(param.getProcessId())) {
            updateLawsuitProcess(param, userSession);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateLawsuitStatus(LawsuitUpdateParam param, UserSession userSession) {
        Example example = handleQuery(param.getQuery(), userSession);
        Lawsuit lawsuit = new Lawsuit();
        lawsuit.setStatus(param.getStatus());
        lawsuit.setUpdateTime(new Date());
        lawsuitMapper.updateByExampleSelective(lawsuit, example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateLawsuitProcess(LawsuitUpdateParam param, UserSession userSession) {
        Example example = handleQuery(param.getQuery(), userSession);
        example.selectProperties("id");
        List<Lawsuit> lawsuits = lawsuitMapper.selectByExample(example);
        List<Long> lawsuitIds = lawsuits.stream().map(Lawsuit::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lawsuitIds)) {
            return;
        }
        Example e = new Example(Lawsuit.class);
        e.and().andIn("id", lawsuitIds);
        Lawsuit lawsuit = new Lawsuit();
        lawsuit.setProcess(param.getProcessId());
        lawsuit.setUpdateTime(new Date());
        lawsuitMapper.updateByExampleSelective(lawsuit, example);
        lawsuitProcessService.addLawsuitProcess(lawsuitIds, param.getProcessId(), userSession);
    }


    @Transactional(rollbackFor = Exception.class)
    public void exportLawsuit(LawsuitQuery query) {
        UserSession userSession = UserUtils.getTokenUser();
        DownloadTask task = downloadTaskService.createTask(null, userSession, DownloadTask.Type.LAWSUIT_EXPORT.getCode(),
                JSONObject.toJSONString(query));
        Long taskId = task.getId();
        if(!checkExist(query,userSession)){
            throw new ApiException("查询条件无匹配结果，请重新操作");
        }
        threadExecutor.execute(() -> {
            downloadTaskService.updateTaskProgress(taskId, new BigDecimal(25), DownloadTask.Status.PROCESSING.getCode());
            String fileNamePrefix = "lawsuit_" + taskId;
            String fileName = fileNamePrefix + ".zip";
            ZipFile zipFile = new ZipFile(fileName);
            query.setLimit(500);
            query.setPage(1);
            PageOutput<LawsuitVO> pageOutput = getList(query, userSession);
            List<LawsuitVO> lawsuitVOList = pageOutput.getList();
            int pages = pageOutput.getPages();
            int total = pageOutput.getTotal();
            List<List<String>> dataList = new ArrayList<>();
            String delt = I18nService.getMessage("global.txt.orgDeltName", userSession.getLanguage(), null);
            List<String> heads = new ArrayList<>();
            if (Objects.equals(query.getExportType(), LawsuitConstant.LAWSUIT_EXPORT_ALL)) {
                heads.addAll(LawsuitConstant.LAWSUIT_UPDATE_EXCEL_HEAD_LIST);
                heads.add(delt);
                heads.add("关联案件");
            } else {
                heads.addAll(LawsuitConstant.LAWSUIT_EXPORT_LIST_HEAD);
                heads.add(delt);
            }
            dataList.add(heads);
            List<List<String>> lists = handleLawsuitListData(lawsuitVOList, query.getExportType());
            dataList.addAll(lists);
            for (int i = 2; i <= pages; i++) {
                query.setLimit(500);
                query.setPage(i);
                PageOutput<LawsuitVO> result = getList(query, userSession);
                List<LawsuitVO> lawsuitVOS = result.getList();
                dataList.addAll(handleLawsuitListData(lawsuitVOS, query.getExportType()));
                // 下载诉讼材料文件
                if (Objects.equals(query.getIsExportFile(), 1)) {
                    downloadMaterialFile(lawsuitVOS, zipFile);
                }
            }
            //生成excel文件
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            EasyExcel.write(byteArrayOutputStream).sheet().doWrite(dataList);
            File excelFile = new File(fileNamePrefix + ".xlsx");
            FileInputStream inputStream = null;
            try {
                FileUtils.writeByteArrayToFile(excelFile, byteArrayOutputStream.toByteArray());
                zipFile.addFile(excelFile);
                downloadTaskService.updateTaskProgress(taskId, new BigDecimal(50), DownloadTask.Status.PROCESSING.getCode());
                // 下载诉讼材料文件
                if (Objects.equals(query.getIsExportFile(), 1)) {
                    downloadMaterialFile(lawsuitVOList, zipFile);
                }
                Long fileSize = zipFile.getFile().length() / 1024;
                Date expireDate = DateUtils.addDays(new Date(), 3);
                inputStream = new FileInputStream(zipFile.getFile());
                UploadCreatedFileStream uploadCreatedFileStream = new UploadCreatedFileStream();
                uploadCreatedFileStream.setInputStream(inputStream)
                        .setFileName(fileName)
                        .setExpireDate(expireDate)
                        .setBucket(systemConfig.getCaseFilesBucket())
                        .setLocalUrl(systemConfig.getLawsuitFilePath() + fileName);
                FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
                String url = fileStorageStrategy.uploadCreatedFileByStream(uploadCreatedFileStream);
                DownloadTask updateTask = new DownloadTask();
                updateTask.setId(taskId);
                updateTask.setDataNums((long) total);
                updateTask.setFileSize(fileSize);
                updateTask.setProgress(new BigDecimal(100));
                updateTask.setUpdateTime(new Date());
                updateTask.setDownloadUrl(url);
                updateTask.setStatus(DownloadTask.Status.COMPLETE.getCode());
                updateTask.setExpireTime(expireDate);
                downloadTaskService.updateByPrimaryKeySelective(updateTask);
            } catch (IOException e) {
                log.error("诉讼导出失败：", e);
                DownloadTask updateTask = new DownloadTask();
                updateTask.setId(taskId);
                updateTask.setUpdateTime(new Date());
                updateTask.setStatus(DownloadTask.Status.FAILED.getCode());
                updateTask.setFailedReason("诉讼导出发生异常错误");
                downloadTaskService.updateByPrimaryKeySelective(updateTask);
            } finally {
                try {
                    inputStream.close();
                    FileUtils.deleteQuietly(zipFile.getFile());
                    byteArrayOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                FileUtils.deleteQuietly(excelFile);
            }
        });
    }

    void downloadMaterialFile(List<LawsuitVO> lawsuitVOList, ZipFile zipFile) {
        try {
            List<Long> lawsuitIds = lawsuitVOList.parallelStream().map(LawsuitVO::getId).collect(Collectors.toList());
            Map<Long, List<LawsuitFileVO>> lawsuitFileMap = lawsuitFileService.queryLawsuitFileByIds(lawsuitIds);
            FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
            for (LawsuitVO lawsuitVO : lawsuitVOList) {
                List<LawsuitFileVO> lawsuitFiles = lawsuitFileMap.get(lawsuitVO.getId());
                if (CollectionUtils.isEmpty(lawsuitFiles)) {
                    continue;
                }
                // 如果诉讼案件有材料文件，就建一个文件夹放对应的文件
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                String folderName = lawsuitVO.getName() + "-" + lawsuitVO.getId() + "-" + sdf.format(new Date())+"/";
                Map<String, Integer> map = new HashMap<>();
                for (LawsuitFileVO lawsuitFileVO : lawsuitFiles) {
                    String name = lawsuitFileVO.getName();
                    String alias = lawsuitFileVO.getAlias();
                    Integer count = map.getOrDefault(name, 0);
                    map.put(name, count+1);
                    String fileName;
                    if (count > 0) {
                        String[] names = StringUtils.split(name, ".");
                        fileName = folderName + names[0] + "(" + count + ")." + names[1];
                    } else {
                        fileName = folderName + name;
                    }
                    ReadFileInfo readFileInfo = new ReadFileInfo();
                    readFileInfo.setAlias(alias)
                            .setUrl("/usr/local/duyansoft" + lawsuitFileVO.getUrl())
                            .setBucket(systemConfig.getCaseFilesBucket());
                    InputStream fileInputStream = fileStorageStrategy.readFileStream(readFileInfo);
                    File file = new File(fileName);
                    FileUtils.copyInputStreamToFile(fileInputStream, file);
                    fileInputStream.close();
                }
                zipFile.addFolder(new File(folderName));
                FileUtils.deleteQuietly(new File(folderName));
            }
        } catch (Exception e) {
            log.error("下载诉讼材料文件失败:", e);
        }
    }

    List<List<String>> handleLawsuitListData(List<LawsuitVO> list, Integer exportType) {
        List<List<String>> dataList = new ArrayList<>();
        if (Objects.equals(exportType, LawsuitConstant.LAWSUIT_EXPORT_ALL)) {
            List<Long> lawsuitIds = list.stream().map(LawsuitVO::getId).collect(Collectors.toList());
            Map<Long, String> map = lawsuitCaseService.getLawsuitLinkOutSerialNo(lawsuitIds);
            list.forEach(lawsuitVO -> {
                try {
                    LawsuitUpdateExcel updateExcel = getLawsuitUpdateExcel(lawsuitVO);
                    List<String> fieldValues = getFieldValues(updateExcel);
                    fieldValues.add(lawsuitVO.getOrgDeltName());
                    fieldValues.add(map.get(lawsuitVO.getId()));
                    dataList.add(fieldValues);
                } catch (NoSuchFieldException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            });
        } else {
            list.forEach(lawsuitVO -> {
                String status = lawsuitVO.getStatus() == 1 ? "办案" : "结案";
                List<String> data = Lists.newArrayList(lawsuitVO.getId().toString(), lawsuitVO.getLawsuitNo(), status, lawsuitVO.getProcessName(),
                        lawsuitVO.getName(), lawsuitVO.getIdCard(), lawsuitVO.getObject(), lawsuitVO.getTypeName(),
                        lawsuitVO.getLawyer(), lawsuitVO.getCourtName(), lawsuitVO.getOrgDeltName());
                dataList.add(data);
            });
        }
        return dataList;
    }



    LawsuitUpdateExcel getLawsuitUpdateExcel(LawsuitVO lawsuitVO) throws NoSuchFieldException, IllegalAccessException {
        LawsuitUpdateExcel updateExcel = AuthBeanUtils.copy(lawsuitVO, LawsuitUpdateExcel.class);
        Class<? extends LawsuitUpdateExcel> c = updateExcel.getClass();
        Class<? extends LawsuitVO> l = lawsuitVO.getClass();
        Field[] fields = c.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            String name = field.getName();
            if (name.endsWith("Time")) {
                Field declaredField = l.getDeclaredField(name);
                declaredField.setAccessible(true);
                Object o = declaredField.get(lawsuitVO);
                field.set(updateExcel, o == null ? "" : DateUtils.formatDate((Date)o));
            }
            if (name.endsWith("Amount")) {
                Field declaredField = l.getDeclaredField(name);
                declaredField.setAccessible(true);
                Object o = declaredField.get(lawsuitVO);
                field.set(updateExcel, o == null ? "" : o.toString());
            }
        }
        updateExcel.setId(lawsuitVO.getId().toString());
        updateExcel.setProcess(lawsuitVO.getProcessName());
        updateExcel.setType(lawsuitVO.getTypeName());
        updateExcel.setCourt(lawsuitVO.getCourtName());
        updateExcel.setCost(lawsuitVO.getCost() == null ? "" : lawsuitVO.getCost().toString());
        if (Objects.equals(lawsuitVO.getIsAgainJudgment(), 0)) {
            updateExcel.setIsAgainJudgment("否");
        }
        if (Objects.equals(lawsuitVO.getIsAgainJudgment(), 1)) {
            updateExcel.setIsAgainJudgment("是");
        }
        if (Objects.equals(lawsuitVO.getIsArchive(), 0)) {
            updateExcel.setIsArchive("否");
        }
        if (Objects.equals(lawsuitVO.getIsArchive(), 1)) {
            updateExcel.setIsArchive("是");
        }
        return updateExcel;
    }



    public PageOutput<LawsuitVO> getList(LawsuitQuery query, UserSession userSession) {
        if (Objects.nonNull(query.getCaseId())) {
            List<Long> lawsuitIds = lawsuitCaseService.getLawsuitIdsByCaseId(query.getCaseId());
            if (CollectionUtils.isEmpty(lawsuitIds)) {
                return new PageOutput<>(query.getPage(), query.getLimit(), 0, new ArrayList<>());
            } else {
                query.setLawsuitIds(lawsuitIds);
            }
        }
        Example example = handleQuery(query, userSession);
        PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<Lawsuit> lawsuits = lawsuitMapper.selectByExample(example);
        List<LawsuitVO> lawsuitVOS = toLawsuitVOList(lawsuits);
        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), lawsuitVOS);
    }

    public Boolean checkExist(LawsuitQuery query, UserSession userSession) {
        Example example = handleQuery(query, userSession);
        PageParam pageParam = new PageParam(1, 1);
        PageUtils.setPage(pageParam);
        List<Lawsuit> lawsuits = lawsuitMapper.selectByExample(example);
        return !CollectionUtils.isEmpty(lawsuits);
    }

    List<LawsuitVO> toLawsuitVOList(List<Lawsuit> lawsuitList) {
        List<LawsuitVO> lawsuitVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(lawsuitList)) {
            return lawsuitVOList;
        }
        Set<Long> orgDeltIds = lawsuitList.parallelStream().filter(lawsuit -> Objects.nonNull(lawsuit.
                getOrgDeltId())).map(Lawsuit::getOrgDeltId).collect(Collectors.toSet());
        Set<Long> typeIds = lawsuitList.parallelStream().filter(lawsuit -> Objects.nonNull(lawsuit.
                getType())).map(Lawsuit::getType).collect(Collectors.toSet());
        Map<Long, String> typeMap = lawsuitConfigService.selectConfigMap(new ArrayList<>(typeIds));
        Set<Long> courtIds = lawsuitList.parallelStream().filter(lawsuit -> Objects.nonNull(lawsuit.
                getCourt())).map(Lawsuit::getCourt).collect(Collectors.toSet());
        Map<Long, String> courtMap = lawsuitConfigService.selectConfigMap(new ArrayList<>(courtIds));
        Set<Long> processIds = lawsuitList.parallelStream().filter(lawsuit -> Objects.nonNull(lawsuit.
                getProcess())).map(Lawsuit::getProcess).collect(Collectors.toSet());
        Map<Long, String> processMap = lawsuitConfigService.selectConfigMap(new ArrayList<>(processIds));
        Map<Long, String> deltMap = deltService.getDeltMap(new ArrayList<>(orgDeltIds));
        Set<Long> configIds = new HashSet<>();
        lawsuitList.forEach(lawsuit -> {
            LawsuitVO lawsuitVO = AuthBeanUtils.copy(lawsuit, LawsuitVO.class);
            lawsuitVO.setOrgDeltName(deltMap.get(lawsuit.getOrgDeltId()));
            lawsuitVO.setTypeName(typeMap.get(lawsuit.getType()));
            lawsuitVO.setProcessName(processMap.get(lawsuit.getProcess()));
            lawsuitVO.setCourtName(courtMap.get(lawsuit.getCourt()));
            lawsuitVOList.add(lawsuitVO);
        });
        return lawsuitVOList;
    }

    LawsuitVO toLawsuitVO(Lawsuit lawsuit) {
        if (Objects.isNull(lawsuit)) {
            return null;
        }
        return toLawsuitVOList(Lists.newArrayList(lawsuit)).get(0);
    }

    Example handleQuery(LawsuitQuery query, UserSession session) {
        Example example = new Example(Lawsuit.class);
        example.and().andEqualTo("orgId", session.getOrgId());
        if (Objects.nonNull(query.getAllotAgent())) {
            example.and().andEqualTo("allotAgent", query.getAllotAgent());
        }
        if (UserUtils.likeBranchAdmin(session.getRoleId())) {
            // 如果是分公司管理员，进行分公司隔离
            example.and().andEqualTo("depId", session.getDepId());
        }
        if (UserUtils.likeTeamLeader(session.getRoleId())) {
            // 如果是小组长，进行小组隔离
            example.and().andEqualTo("teamId", session.getTeamId());
        }
        if (!CollectionUtils.isEmpty(query.getLawsuitIds())) {
            example.and().andIn("id", query.getLawsuitIds());
        }
        if (!CollectionUtils.isEmpty(query.getLawsuitNos())) {
            if (query.getLawsuitNos().size() == 1) {
                example.and().andLike("lawsuitNo", query.getLawsuitNos().get(0) + "%");
            } else {
                example.and().andIn("lawsuitNo", query.getLawsuitNos());
            }
        }
        if (!CollectionUtils.isEmpty(query.getNames())) {
            example.and().andIn("name", query.getNames());
        }
        if (!CollectionUtils.isEmpty(query.getIdCards())) {
            example.and().andIn("idCard", query.getIdCards());
        }
        if (!CollectionUtils.isEmpty(query.getOrgDeltIds())) {
            example.and().andIn("orgDeltId", query.getOrgDeltIds());
        }
        if (!CollectionUtils.isEmpty(query.getCourtIds())) {
            example.and().andIn("court", query.getCourtIds());
        }
        if (!CollectionUtils.isEmpty(query.getTypeIds())) {
            example.and().andIn("type", query.getTypeIds());
        }
        if (!CollectionUtils.isEmpty(query.getProcessIds())) {
            example.and().andIn("process", query.getProcessIds());
        }
        if (!CollectionUtils.isEmpty(query.getStatuses())) {
            example.and().andIn("status", query.getStatuses());
        }
        if (Objects.nonNull(query.getSource())) {
            example.and().andEqualTo("source", query.getSource());
        }
        example.orderBy("id").desc();
        return example;

    }


    Lawsuit getNewLawsuit(Case caseInfo) {
        Lawsuit lawsuit = new Lawsuit();
        // 从催收案件冗余相关字段数据
        lawsuit.setOrgId(caseInfo.getOrgId());
        lawsuit.setDepId(caseInfo.getDepId());
        lawsuit.setTeamId(caseInfo.getTeamId());
        lawsuit.setStatus(LawsuitConstant.LAWSUIT_STATUS_RUNNING);
        lawsuit.setSource(LawsuitConstant.LAWSUIT_SOURCE_ADMIN);
        lawsuit.setName(caseInfo.getName());
        lawsuit.setIdCard(caseInfo.getIdCard());
        lawsuit.setMobile(caseInfo.getOwnMobile());
        lawsuit.setOrgDeltId(caseInfo.getOrgDeltId());
        lawsuit.setCreateTime(new Date());
        lawsuit.setUpdateTime(new Date());
        lawsuit.setCaseId(caseInfo.getId());
        lawsuit.setOutSerialNo(caseInfo.getOutSerialTemp());
        lawsuit.setAllotAgent(caseInfo.getAllotAgent());
        Long amount = caseInfo.getAmount() == null ? 0 : caseInfo.getAmount();
        lawsuit.setAmount(new BigDecimal(amount.toString()).divide(new BigDecimal("1000"), 2, RoundingMode.DOWN));
        return lawsuit;
    }


    List<Lawsuit> handleLawsuitUpdateExcelData(Long storeId, List<LawsuitUpdateExcel> updateExcelList, UserSession userSession) throws InterruptedException {
        List<Lawsuit> lawsuitList = new ArrayList<>();
        List<Long> lawsuitIds = updateExcelList.parallelStream().filter(lawsuitUpdateExcel -> Objects.nonNull(lawsuitUpdateExcel.
                getId())).map(lawsuitUpdateExcel -> toLawsuitId(lawsuitUpdateExcel.getId())).distinct().collect(Collectors.toList());
        List<String> processNames = updateExcelList.parallelStream().filter(updateExcel -> StringUtils.isNotBlank(updateExcel.getProcess())).
                map(LawsuitUpdateExcel::getProcess).distinct().collect(Collectors.toList());
        List<String> typeNames = updateExcelList.parallelStream().filter(updateExcel -> StringUtils.isNotBlank(updateExcel.getType())).
                map(LawsuitUpdateExcel::getType).distinct().collect(Collectors.toList());
        List<String> courtNames = updateExcelList.parallelStream().filter(updateExcel -> StringUtils.isNotBlank(updateExcel.getCourt())).
                map(LawsuitUpdateExcel::getCourt).distinct().collect(Collectors.toList());
        Map<String, Long> processMap = lawsuitConfigService.selectConfigMap(processNames, LawsuitConfigEnums.Type.LAWSUIT_OPERATION.getCode(), userSession.getOrgId());
        Map<String, Long> typeMap = lawsuitConfigService.selectConfigMap(typeNames, LawsuitConfigEnums.Type.CASE_TYPE.getCode(), userSession.getOrgId());
        Map<String, Long> courtMap = lawsuitConfigService.selectConfigMap(courtNames, LawsuitConfigEnums.Type.COURT.getCode(), userSession.getOrgId());
        List<Long> existLawsuitIds = getExistLawsuitIds(lawsuitIds, userSession.getOrgId());
        for (LawsuitUpdateExcel updateExcel : updateExcelList) {
            List<Map<String, Object>> errorList = new ArrayList<>();
            Lawsuit lawsuit = AuthBeanUtils.copy(updateExcel, Lawsuit.class);
            String id = updateExcel.getId();
            if (StringUtils.isBlank(id)) {
                errorList.add(fileStoreService.buildColumnError(0, FileStoreEnums.ColumnErrorCode.NOT_BLANK));
            }
            if (!existLawsuitIds.contains(toLawsuitId(id))) {
                errorList.add(fileStoreService.buildColumnError(0, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
            }
            lawsuit.setId(toLawsuitId(id));
            if (StringUtils.isBlank(updateExcel.getName())) {
                errorList.add(fileStoreService.buildColumnError(2, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
            }
            if (StringUtils.isBlank(updateExcel.getIdCard())) {
                errorList.add(fileStoreService.buildColumnError(3, FileStoreEnums.ColumnErrorCode.MUST_EXIST));
            } else {
                if (updateExcel.getIdCard().trim().length() < 1 || updateExcel.getIdCard().trim().length() > 32) {
                    errorList.add(fileStoreService.buildColumnError(3, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getMobile())) {
                if (!RegexConstant.isMatch(RegexConstant.REGEX_PHONE, updateExcel.getMobile())) {
                    errorList.add(fileStoreService.buildColumnError(4, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getContactNumber())) {
                if (!RegexConstant.isMatch(RegexConstant.REGEX_PHONE, updateExcel.getContactNumber())) {
                    errorList.add(fileStoreService.buildColumnError(8, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getRegisterTime())) {
                Date registerTime = DateUtils.convertToDate(updateExcel.getRegisterTime());
                lawsuit.setRegisterTime(registerTime);
                if (Objects.isNull(registerTime)) {
                    errorList.add(fileStoreService.buildColumnError(21, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getRegisterTime())) {
                Date registerTime = DateUtils.convertToDate(updateExcel.getRegisterTime());
                lawsuit.setRegisterTime(registerTime);
                if (Objects.isNull(registerTime)) {
                    errorList.add(fileStoreService.buildColumnError(21, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getLawsuitPayTime())) {
                Date lawsuitPayTime = DateUtils.convertToDate(updateExcel.getLawsuitPayTime());
                lawsuit.setLawsuitPayTime(lawsuitPayTime);
                if (Objects.isNull(lawsuitPayTime)) {
                    errorList.add(fileStoreService.buildColumnError(25, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getSavePayTime())) {
                Date savePayTime = DateUtils.convertToDate(updateExcel.getSavePayTime());
                lawsuit.setSavePayTime(savePayTime);
                if (Objects.isNull(savePayTime)) {
                    errorList.add(fileStoreService.buildColumnError(26, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getFirstCourtTime())) {
                Date firstCourtTime = DateUtils.convertToDate(updateExcel.getFirstCourtTime());
                lawsuit.setFirstCourtTime(firstCourtTime);
                if (Objects.isNull(firstCourtTime)) {
                    errorList.add(fileStoreService.buildColumnError(28, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getExecuteRegisterTime())) {
                Date executeRegisterTime = DateUtils.convertToDate(updateExcel.getExecuteRegisterTime());
                lawsuit.setExecuteRegisterTime(executeRegisterTime);
                if (Objects.isNull(executeRegisterTime)) {
                    errorList.add(fileStoreService.buildColumnError(29, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getJudgmentTime())) {
                Date judgmentTime = DateUtils.convertToDate(updateExcel.getJudgmentTime());
                lawsuit.setJudgmentTime(judgmentTime);
                if (Objects.isNull(judgmentTime)) {
                    errorList.add(fileStoreService.buildColumnError(30, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getSecondRegisterTime())) {
                Date secondRegisterTime = DateUtils.convertToDate(updateExcel.getSecondRegisterTime());
                lawsuit.setSecondRegisterTime(secondRegisterTime);
                if (Objects.isNull(secondRegisterTime)) {
                    errorList.add(fileStoreService.buildColumnError(36, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getSecondCourtTime())) {
                Date secondCourtTime = DateUtils.convertToDate(updateExcel.getSecondCourtTime());
                lawsuit.setSecondCourtTime(secondCourtTime);
                if (Objects.isNull(secondCourtTime)) {
                    errorList.add(fileStoreService.buildColumnError(37, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getSecondJudgmentTime())) {
                Date secondJudgmentTime = DateUtils.convertToDate(updateExcel.getSecondJudgmentTime());
                lawsuit.setSecondJudgmentTime(secondJudgmentTime);
                if (Objects.isNull(secondJudgmentTime)) {
                    errorList.add(fileStoreService.buildColumnError(38, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getThirdRegisterTime())) {
                Date thirdRegisterTime = DateUtils.convertToDate(updateExcel.getThirdRegisterTime());
                lawsuit.setThirdRegisterTime(thirdRegisterTime);
                if (Objects.isNull(thirdRegisterTime)) {
                    errorList.add(fileStoreService.buildColumnError(39, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getThirdCourtTime())) {
                Date thirdCourtTime = DateUtils.convertToDate(updateExcel.getThirdCourtTime());
                lawsuit.setThirdCourtTime(thirdCourtTime);
                if (Objects.isNull(thirdCourtTime)) {
                    errorList.add(fileStoreService.buildColumnError(40, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getThirdJudgmentTime())) {
                Date thirdJudgmentTime = DateUtils.convertToDate(updateExcel.getThirdJudgmentTime());
                lawsuit.setThirdJudgmentTime(thirdJudgmentTime);
                if (Objects.isNull(thirdJudgmentTime)) {
                    errorList.add(fileStoreService.buildColumnError(41, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getJudgmentEffectiveTime())) {
                Date judgmentEffectiveTime = DateUtils.convertToDate(updateExcel.getJudgmentEffectiveTime());
                lawsuit.setJudgmentEffectiveTime(judgmentEffectiveTime);
                if (Objects.isNull(judgmentEffectiveTime)) {
                    errorList.add(fileStoreService.buildColumnError(42, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getSubmitMaterialTime())) {
                Date submitMaterialTime = DateUtils.convertToDate(updateExcel.getSubmitMaterialTime());
                lawsuit.setSubmitMaterialTime(submitMaterialTime);
                if (Objects.isNull(submitMaterialTime)) {
                    errorList.add(fileStoreService.buildColumnError(43, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getSealTime())) {
                Date sealTime = DateUtils.convertToDate(updateExcel.getSealTime());
                lawsuit.setSealTime(sealTime);
                if (Objects.isNull(sealTime)) {
                    errorList.add(fileStoreService.buildColumnError(44, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getKeepSealTime())) {
                Date keepSealTime = DateUtils.convertToDate(updateExcel.getKeepSealTime());
                lawsuit.setKeepSealTime(keepSealTime);
                if (Objects.isNull(keepSealTime)) {
                    errorList.add(fileStoreService.buildColumnError(45, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getSealExpireTime())) {
                Date sealExpireTime = DateUtils.convertToDate(updateExcel.getSealExpireTime());
                lawsuit.setSealExpireTime(sealExpireTime);
                if (Objects.isNull(sealExpireTime)) {
                    errorList.add(fileStoreService.buildColumnError(46, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getSaveAmount())) {
                try {
                    BigDecimal amount = new BigDecimal(updateExcel.getSaveAmount());
                    int count = amount.precision() - amount.scale();
                    if (count > 10) {
                        errorList.add(fileStoreService.buildColumnError(48, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                    }
                    lawsuit.setSaveAmount(amount);
                } catch (Exception e) {
                    errorList.add(fileStoreService.buildColumnError(48, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getMinLawyerAmount())) {
                try {
                    BigDecimal amount = new BigDecimal(updateExcel.getMinLawyerAmount());
                    int count = amount.precision() - amount.scale();
                    if (count > 10) {
                        errorList.add(fileStoreService.buildColumnError(49, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                    }
                    lawsuit.setMinLawyerAmount(amount);
                } catch (Exception e) {
                    errorList.add(fileStoreService.buildColumnError(49, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getRiskLawyerAmount())) {
                try {
                    BigDecimal amount = new BigDecimal(updateExcel.getRiskLawyerAmount());
                    int count = amount.precision() - amount.scale();
                    if (count > 10) {
                        errorList.add(fileStoreService.buildColumnError(50, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                    }
                    lawsuit.setRiskLawyerAmount(amount);
                } catch (Exception e) {
                    errorList.add(fileStoreService.buildColumnError(50, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getCourtAmount())) {
                try {
                    BigDecimal amount = new BigDecimal(updateExcel.getCourtAmount());
                    int count = amount.precision() - amount.scale();
                    if (count > 10) {
                        errorList.add(fileStoreService.buildColumnError(51, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                    }
                    lawsuit.setCourtAmount(amount);
                } catch (Exception e) {
                    errorList.add(fileStoreService.buildColumnError(51, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getExecuteFinalTime())) {
                Date executeFinalTime = DateUtils.convertToDate(updateExcel.getExecuteFinalTime());
                lawsuit.setExecuteFinalTime(executeFinalTime);
                if (Objects.isNull(executeFinalTime)) {
                    errorList.add(fileStoreService.buildColumnError(55, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (StringUtils.isNotBlank(updateExcel.getCost())) {
                try {
                    BigDecimal amount = new BigDecimal(updateExcel.getCost());
                    int count = amount.precision() - amount.scale();
                    if (count > 10) {
                        errorList.add(fileStoreService.buildColumnError(56, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                    }
                    lawsuit.setCost(amount);
                } catch (Exception e) {
                    errorList.add(fileStoreService.buildColumnError(56, FileStoreEnums.ColumnErrorCode.VALUE_ERROR));
                }
            }
            if (Objects.equals(updateExcel.getIsAgainJudgment(), "是")) {
                lawsuit.setIsAgainJudgment(1);
            }
            if (Objects.equals(updateExcel.getIsAgainJudgment(), "否")) {
                lawsuit.setIsAgainJudgment(0);
            }
            if (Objects.equals(updateExcel.getIsArchive(), "是")) {
                lawsuit.setIsArchive(1);
            }
            if (Objects.equals(updateExcel.getIsArchive(), "否")) {
                lawsuit.setIsArchive(0);
            }

            if (errorList.size() > 0) {
                List<String> fieldValues = getFieldValues(updateExcel);
                JSONArray jsonValues = JSONArray.parseArray(JSON.toJSONString(fieldValues));
                jsonValues.add(errorList);
                stringRedisTemplate.opsForList().rightPush(KeyCache.FILE_ERROR_LIST  + storeId, JSONArray.toJSONString(jsonValues));
                continue;
            }
            lawsuit.setProcess(processMap.get(updateExcel.getProcess()));
            lawsuit.setType(typeMap.get(updateExcel.getType()));
            lawsuit.setCourt(courtMap.get(updateExcel.getCourt()));
            lawsuitList.add(lawsuit);
        }
        return lawsuitList;



    }

    List<String> getFieldValues(LawsuitUpdateExcel updateExcel) {
        Class<? extends LawsuitUpdateExcel> c = updateExcel.getClass();
        Field[] fields = c.getDeclaredFields();
        List<String> fieldValues = new ArrayList<>();
        for (Field field : fields) {
            field.setAccessible(true);
            Object o = null;
            try {
                o = field.get(updateExcel);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            fieldValues.add(o == null ? "" : o.toString());
        }
        return fieldValues;
    }

    Long toLawsuitId(String id) {
        try {
            return Long.valueOf(id);
        } catch (Exception e) {
            log.error("诉讼案件导入id转换失败", e);
        }
        return 0l;
    }

     void handleLawsuitUpdateEmptyParam(Lawsuit lawsuit, LawsuitUpdateEmptyParam param) throws NoSuchFieldException, IllegalAccessException {
        Class<? extends Lawsuit> c = lawsuit.getClass();
        Class<? extends LawsuitUpdateEmptyParam> p = param.getClass();
        Field[] fields = p.getDeclaredFields();
        for (Field field : fields) {
            // 参数类属性名
            String name = field.getName();
            // 根据参数名取实体类中查找字段
            Field cField = c.getDeclaredField(name);
            // 获取属性值赋值给实体类
            field.setAccessible(true);
            Object value = field.get(param);
            cField.setAccessible(true);
            cField.set(lawsuit, value);
        }
    }

    /**
     * 这里需要加上公司id过滤来判断诉讼id是否存在，防止用户乱输，更新成别的公司数据
     * @param lawsuitIds
     * @param orgId
     * @return
     */
    List<Long> getExistLawsuitIds(List<Long> lawsuitIds, Long orgId) {
        Example example = new Example(Lawsuit.class);
        example.and().andEqualTo("orgId", orgId).andIn("id", lawsuitIds);
        example.selectProperties("id");
        List<Lawsuit> lawsuitList = lawsuitMapper.selectByExample(example);
        return lawsuitList.parallelStream().map(Lawsuit::getId).collect(Collectors.toList());
    }

    Map<Long, LawsuitVO> getLawsuit(List<Long> lawsuitIds) {
        if (CollectionUtils.isEmpty(lawsuitIds)) {
            return new HashMap<>();
        }
        Example example = new Example(Lawsuit.class);
        example.and().andIn("id", lawsuitIds);
        List<Lawsuit> lawsuitList = lawsuitMapper.selectByExample(example);
        Set<Long> processIds = lawsuitList.parallelStream().filter(lawsuit -> Objects.nonNull(lawsuit.getProcess()))
                .map(Lawsuit::getProcess).collect(Collectors.toSet());
        Map<Long, String> processMap = lawsuitConfigService.selectConfigMap(new ArrayList<>(processIds));
        Map<Long, LawsuitVO> result = new HashMap<>();
        lawsuitList.forEach(lawsuit -> {
            LawsuitVO lawsuitVO = AuthBeanUtils.copy(lawsuit, LawsuitVO.class);
            lawsuitVO.setProcessName(processMap.get(lawsuit.getProcess()));
            result.put(lawsuit.getId(), lawsuitVO);
        });
        return result;
    }

    void checkCaseFile(MultipartFile file, String language) {
        String fileName = file.getOriginalFilename();
        // 检验文件格式
        if (!ExcelUtil.isExcelFile(fileName)) {
            throw new ApiException("文件必须是excel格式");
        }
        long size = file.getSize();
        // 检验文件大小
        if (size > 100 * 1024 * 1024) {
            throw new ApiException("文件大小已超过100M，请拆分文件后再上传");
        }
        // 检验文件数据是否为空
        if (EasyExcelUtils.judgeIsEmpty(file, CaseTemplateEnums.TempType.LAWSUIT_UPDATE.getCode(), LawsuitConstant.LAWSUIT_UPDATE_EXCEL_HEAD_LIST, language)) {
            throw new ApiException("文件为空模板");
        }
    }





}
