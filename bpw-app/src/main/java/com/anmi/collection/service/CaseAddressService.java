package com.anmi.collection.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.CaseAddressEnums;
import com.anmi.collection.common.enums.VisitAuditEnums;
import com.anmi.collection.common.enums.VisitEnums;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.dto.AddressRegionDTO;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.requset.visit.AddressAddParam;
import com.anmi.collection.entity.requset.visit.AddressUpdateParam;
import com.anmi.collection.entity.response.cases.CaseAddressListVO;
import com.anmi.collection.entity.response.visit.CaseAddressVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.*;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.HttpUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.base.JsonField;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseAddress;
import com.anmi.domain.user.Region;
import com.anmi.domain.visit.Visit;
import com.anmi.domain.visit.VisitAudit;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CaseAddressService extends BaseService<CaseAddress>{

  @Autowired private CaseAddressMapper caseAddressMapper;
  @Autowired private CaseService caseService;
  @Autowired private CaseMapper caseMapper;

  @Autowired private UserService userService;
  @Autowired private VisitAddressMapper visitAddressMapper;
  @Autowired private VisitAuditMapper visitAuditMapper;
  @Autowired private VisitMapper visitMapper;
  @Autowired private RegionService regionService;
  @Autowired private SystemConfig systemConfig;

  public static final Map<Byte, String> importAddressMap = new HashMap<>();

  static {
    // 家庭地址
    importAddressMap.put(CaseAddressEnums.Type.FAMILY.getCode(), "address");
    // 住宅地址
    importAddressMap.put(CaseAddressEnums.Type.HOUSE.getCode(), "house_address");
    // 单位地址
    importAddressMap.put(CaseAddressEnums.Type.COMPANY.getCode(), "company_address");
    // 户籍地址
    importAddressMap.put(CaseAddressEnums.Type.RESIDENCE.getCode(), "native_address");
    // 对账单地址
    importAddressMap.put(CaseAddressEnums.Type.STATEMENT.getCode(), "bill_path");
    //其他地址
    importAddressMap.put(CaseAddressEnums.Type.OTHER.getCode(), "other_address");
  }

  @Transactional(rollbackFor = Exception.class)
  public void updateCaseAddress(Long caseId, Long updateBy) {
    // 加行锁
    Case caseInfo = caseMapper.selectByIdForUpdate(caseId);
    if (caseInfo == null) {
      throw new ApiException("案件不存在");
    }
    Map<String, String> fieldJson = caseInfo.getFieldJson();
    if (fieldJson == null
        || fieldJson.isEmpty()
        || "true".equals(fieldJson.get("is_sync_address"))) {
      return;
    }
    List<Region> regionList = regionService.getAllRegionList();
    List<CaseAddress> insertList = new ArrayList<>();
    List<CaseAddress> exitImportAddressList =
        caseAddressMapper.selectCaseAddressByCaseId(
            caseId, CaseAddressEnums.Source.IMPORT.getCode());
    Map<Byte, CaseAddress> exitImportAddressMap =
        exitImportAddressList.stream().collect(Collectors.toMap(a -> a.getType(), a -> a));
    for (Map.Entry<Byte, String> entry : importAddressMap.entrySet()) {
      String address = fieldJson.get(entry.getValue());
      if (StringUtils.isBlank(address)) {
        continue;
      }
      CaseAddress exitAddress = exitImportAddressMap.get(entry.getKey());
      if (exitAddress != null) {
        if (address.equals(exitAddress.getAddress())) {
          continue;
        }
        // 更新地址信息
        exitAddress.setAddress(address);
        exitAddress.setUpdateBy(updateBy);
        exitAddress.setUpdateTime(new Date());
        setCaseAddressRegion(exitAddress, regionList);
        caseAddressMapper.updateByPrimaryKeySelective(exitAddress);
      } else {
        // 插入地址
        CaseAddress caseAddress = new CaseAddress();
        caseAddress.setType(entry.getKey());
        caseAddress.setAddress(address);
        caseAddress.setCaseId(caseInfo.getId());
        caseAddress.setCreateBy(caseInfo.getCreateBy());
        caseAddress.setCreateTime(caseInfo.getCreateTime());
        caseAddress.setUpdateBy(updateBy);
        caseAddress.setUpdateTime(new Date());
        caseAddress.setSource(CaseAddressEnums.Source.IMPORT.getCode());
        caseAddress.setState(CaseAddressEnums.State.UNKNOWN.getCode());
        setCaseAddressRegion(caseAddress, regionList);
        insertList.add(caseAddress);
      }
    }
    //一次性插入
    if (!CollectionUtils.isEmpty(insertList)) {
      caseAddressMapper.insertList(insertList);
    }
    //修改同步地址状态
    List<JsonField> jsonFieldList = new ArrayList<>();
    JsonField jsonField = new JsonField();
    jsonField.setKey("is_sync_address");
    jsonField.setValue("true");
    jsonFieldList.add(jsonField);
    caseMapper.updateCaseFieldJson(caseId, jsonFieldList);
  }

  public PageOutput getCaseAddressList(Long caseId, PageParam pageParam) throws Exception {
    Case caseInfo=caseService.selectByPrimaryKey(caseId);
    Assert.notNull(caseInfo,"找不到案件信息");
    Page page = PageUtils.setPage(pageParam);
    List<CaseAddress> caseAddressList = caseAddressMapper.selectCaseAddressByCaseId(caseId, null);
    List<CaseAddressVO> caseAddressVOS = new ArrayList<>();
    for (CaseAddress caseAddress : caseAddressList) {
      CaseAddressVO caseAddressVO = BeanUtil.copyProperties(caseAddress, CaseAddressVO.class);
      caseAddressVO.setCreateByName(userService.getNames(caseInfo.getOrgId()).get(caseAddress.getCreateBy()));
      caseAddressVO.setVisitCount(visitAddressMapper.selectVisitAddressCount(caseAddress.getId()));
      caseAddressVO.setState(caseAddress.getState().intValue());
      caseAddressVO.setType(caseAddress.getType().intValue());
      caseAddressVO.setCreateTime(caseAddress.getCreateTime().getTime());
      caseAddressVO.setTeamId(caseInfo.getTeamId());
      caseAddressVO.setSource(caseAddress.getSource() == null ? null : caseAddress.getSource().intValue());
      Case caseIn = caseMapper.selectByPrimaryKey(caseAddress.getCaseId());
      caseAddressVO.setEntrustEndTime(caseIn.getEntrustEndTime() == null ? null : caseIn.getEntrustEndTime().getTime());
      caseAddressVOS.add(caseAddressVO);
    }

    PageOutput pageOutput =
        new PageOutput(
            page.getPageNum(),
            page.getPageSize(),
            page != null ? (int) page.getTotal() : caseAddressVOS.size(),
            caseAddressVOS);
    return pageOutput;
  }

  /**
   * 查询多个案件的地址信息
   * @param caseIds
   * @param pageParam
   * @return
   * @throws Exception
   */
  public CaseAddressListVO getCaseAddressListByCaseIds (List<Long> caseIds, PageParam pageParam) throws Exception{
    //判断案件列表是否为空
    if (CollectionUtils.isEmpty(caseIds)) {
      throw new ApiException("案件编号参数为空！");
    }
    // 分页
    Page page = super.setPage(pageParam);
    //查询对应案件地址信息
    List<CaseAddress> caseAddressList = caseAddressMapper.selectCaseAddressByCaseIds(caseIds, null);
    List<CaseAddressVO> caseAddressVOS = new ArrayList<>();
    //设置审核外访或者已在外访中的数量
    Integer visitCount = 0;
    Map<String, Object> map = new HashMap<>();
    List<Case> caseList = caseService.getCaseListByCaseIds(caseIds);
    for (CaseAddress caseAddress : caseAddressList) {
      Case aCase = caseList.parallelStream().filter(temp -> Objects.equals(caseAddress.getCaseId(), temp.getId())).findFirst().orElse(null);
      if (aCase == null) {
        //不存在案件信息则将其过滤
        continue;
      }
      CaseAddressVO caseAddressVO = BeanUtil.copyProperties(caseAddress, CaseAddressVO.class);
      //案件编号
      caseAddressVO.setOutSerialTemp(aCase.getOutSerialTemp());
      caseAddressVO.setCreateByName(userService.getNames(aCase.getOrgId()).get(caseAddress.getCreateBy()));
      caseAddressVO.setVisitCount(visitAddressMapper.selectVisitAddressCount(caseAddress.getId()));
      caseAddressVO.setState(caseAddress.getState().intValue());
      caseAddressVO.setType(caseAddress.getType().intValue());
      caseAddressVO.setCreateTime(caseAddress.getCreateTime().getTime());
      caseAddressVO.setTeamId(aCase.getTeamId());
      caseAddressVO.setSource(caseAddress.getSource() == null ? null : caseAddress.getSource().intValue());
      Case caseIn = caseMapper.selectByPrimaryKey(caseAddress.getCaseId());
      caseAddressVO.setEntrustEndTime(caseIn.getEntrustEndTime() == null ? null : caseIn.getEntrustEndTime().getTime());
      caseAddressVOS.add(caseAddressVO);

      // 该案件地址处于待审核
      map.put("caseId", caseAddress.getCaseId());
      map.put("addressId",  caseAddress.getId());
      map.put("state", VisitAuditEnums.State.ING.getCode());
      Integer vaINGCount = visitAuditMapper.selectByStateCount(map);
      //移除之前条件state
      map.remove("state");
      // 该案件地址处于待外访和外访中
      map.put("states", VisitEnums.State.WAIT.getCode() + "," + VisitEnums.State.ING.getCode());
      Integer vCount = visitMapper.selectByVisitStateCount(map);
      if (vaINGCount > 0 || vCount > 0) {
        visitCount++;
      }
    }
    //查询外访地址中的案例个数
    Map<Long, List<CaseAddress>> caseIdGroup = caseAddressList.stream().collect(Collectors.groupingBy(CaseAddress::getCaseId));
    int size = caseIdGroup.size();

    CaseAddressListVO pageOutput =
            new CaseAddressListVO(
                    page.getPageNum(),
                    page.getPageSize(),
                    page != null ? (int) page.getTotal() : caseAddressVOS.size(),
                    caseAddressVOS);
    //设置已处于待审核、待外访和外访中的个数
    pageOutput.setOutVisitCount(visitCount);
    pageOutput.setCaseCount(size);
    return pageOutput;
  }

  public void createAddress(AddressAddParam param) {
    UserSession userSession = userService.getTokenUser();
    CaseAddress caseAddress = new CaseAddress();
    caseAddress.setCaseId(param.getCaseId());
    caseAddress.setAddress(param.getAddress());
    caseAddress.setCreateBy(userSession.getId());
    caseAddress.setUpdateBy(userSession.getId());
    caseAddress.setType(param.getType().byteValue());
    caseAddress.setState(param.getState().byteValue());
    caseAddress.setDesc(param.getDesc());
    caseAddress.setSource((byte) CaseAddressEnums.Source.ADD.getCode());
    caseAddress.setCreateTime(new Date());
    int i = caseAddressMapper.insertSelective(caseAddress);
    if (i != 1) {
      throw new ApiException("新增地址失败！");
    }
  }

  public void updateAddress(AddressUpdateParam param) {
    UserSession userSession = userService.getTokenUser();
    CaseAddress caseAddress = caseAddressMapper.selectByPrimaryKey(param.getId());
    if (null == caseAddress) {
      throw new ApiException("该地址不存在！");
    }
    if (UserUtils.likeAdmin() || UserUtils.likeBranchAdmin()) {
      // 管理员可修改所有人添加的
      caseAddress.setAddress(param.getAddress());
      caseAddress.setState(param.getState().byteValue());
      caseAddress.setType(param.getType().byteValue());
      caseAddress.setDesc(param.getDesc());
      caseAddress.setUpdateBy(userSession.getId());
      caseAddress.setUpdateTime(new Date());

      // 管理员修改导入地址，同步到caseInfo表的json字段
      Case caseInfo = caseMapper.selectByPrimaryKey(caseAddress.getCaseId());
      if (caseInfo == null) {
        throw new ApiException("案件不存在");
      }
      List<JsonField> jsonFieldList = new ArrayList<>();
      JsonField jsonField = new JsonField();
      if (importAddressMap.get(param.getType().byteValue()) == null) {
        throw new ApiException("参数异常");
      }
      jsonField.setKey(importAddressMap.get(param.getType().byteValue()));
      jsonField.setValue(param.getAddress());
      jsonFieldList.add(jsonField);
      caseMapper.updateCaseFieldJson(caseAddress.getCaseId(), jsonFieldList);

    } else if (caseAddress.getSource() == CaseAddressEnums.Source.ADD.getCode()) {
      caseAddress.setAddress(param.getAddress());
      caseAddress.setType(param.getType().byteValue());
      caseAddress.setState(param.getState().byteValue());
      caseAddress.setDesc(param.getDesc());
      caseAddress.setUpdateBy(userSession.getId());
      caseAddress.setUpdateTime(new Date());

    } else if (caseAddress.getSource() == CaseAddressEnums.Source.IMPORT.getCode()
        || !caseAddress.getCreateBy().equals(userSession.getId())) {
      // 导入或其他人添加只能修改地址状态和备注
      caseAddress.setState(param.getState().byteValue());
      caseAddress.setDesc(param.getDesc());
      caseAddress.setUpdateBy(userSession.getId());
      caseAddress.setUpdateTime(new Date());
    }
    int i = caseAddressMapper.updateByPrimaryKeySelective(caseAddress);
    if (i != 1) {
      throw new ApiException("更新地址失败！");
    }
  }

  public void deleteAddress(Long addressId) {
    UserSession userSession = userService.getTokenUser();
    CaseAddress caseAddress = caseAddressMapper.selectByPrimaryKey(addressId);
    if (null == caseAddress) {
      throw new ApiException("该地址不存在！");
    }

    if (UserUtils.likeAdmin()
        || UserUtils.likeBranchAdmin()
        || (caseAddress.getSource() == CaseAddressEnums.Source.ADD.getCode()
            && caseAddress.getCreateBy().equals(userSession.getId()))) {

      if (caseAddress.getSource() == CaseAddressEnums.Source.IMPORT.getCode()) {
        // 导入无法删除
        throw new ApiException("该地址为导入地址，无法删除！");
      }

      // 有外访记录不能删除地址
      List<Visit> visitList = visitMapper.selectVisitListByVisitAddress(addressId);
      if (!CollectionUtils.isEmpty(visitList)) {
        throw new ApiException("该地址已有外访任务，无法删除！");
      }

      // 待审核无法删除
      List<VisitAudit> visitAuditList = visitAuditMapper.selectVisitAuditByAddressId(addressId);
      if (!CollectionUtils.isEmpty(visitAuditList)) {
        for (VisitAudit visitAudit : visitAuditList) {
          if (VisitAuditEnums.State.ING.getCode() == visitAudit.getState()) {
            throw new ApiException("该地址已有外访任务，无法删除！");
          }
        }
      }

      caseAddress.setState((byte) CaseAddressEnums.State.DELETE.getCode());
      caseAddress.setUpdateBy(userSession.getId());
      caseAddress.setUpdateTime(new Date());
      int i = caseAddressMapper.updateByPrimaryKeySelective(caseAddress);
      if (i != 1) {
        throw new ApiException("删除地址失败！");
      }
    }
    else {
      throw new ApiException("只有管理员或者自己添加的地址才能删除");
    }
  }

  public List<CaseAddress> getCaseAddressByCaseIds(List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return new ArrayList<>();
    }
    Example example = new Example(CaseAddress.class);
    example.and().andIn("caseId", caseIds);
    example.and().andNotEqualTo("state", -2);//-2是已删除状态
    List<CaseAddress> caseAddresses = caseAddressMapper.selectByExample(example);
    return caseAddresses;
  }

  @Transactional(rollbackFor = Exception.class)
  public void addAddress(List<CaseAddress> addressList) {
    if (CollectionUtils.isEmpty(addressList)) {
      return;
    }
    addressList.forEach(caseAddress -> {
      caseAddress.setCreateTime(new Date());
      caseAddress.setUpdateTime(new Date());
    });
    caseAddressMapper.insertList(addressList);
  }

  public AddressRegionDTO  addressResolver(String address) {
    AddressRegionDTO addressRegion = new AddressRegionDTO();
    if (StringUtils.isBlank(address)) {
      return addressRegion;
    }
    try {
      String url = "https://restapi.amap.com/v3/geocode/geo";
      JSONObject param = new JSONObject();
      param.put("key", systemConfig.getMapKey());
      param.put("address", address);
      String result = HttpUtils.requset(HttpUtils.METHOD_GET, url, HttpUtils.CONTENT_TYPE_FORM, param);
      JSONObject rst = JSONObject.parseObject(result);
      if (Objects.equals(rst.getInteger("status"), 1)) {
        JSONArray geocodes = (JSONArray)rst.get("geocodes");
        if (geocodes.size() > 0) {
          addressRegion = geocodes.getObject(0, AddressRegionDTO.class);
        }
      }
    } catch (Exception e) {
      log.error("解析地址：" + address + " 错误", e);
    }
    return addressRegion;
  }

  void setCaseAddressRegion(CaseAddress caseAddress, List<Region> regionList) {
    String address = caseAddress.getAddress();
    AddressRegionDTO addressRegion = addressResolver(address);
    String province = addressRegion.getProvince();
    String city = addressRegion.getCity();
    String district = addressRegion.getDistrict();
    if (StringUtils.isNotBlank(province)) {
      Region regionProvince = regionList.stream().filter(region -> Objects.equals(region.getName(), province))
              .findFirst().orElse(null);
      // 匹配不到省份，就不需要再匹配下级
      if (Objects.isNull(regionProvince)) {
        return;
      }
      Integer provinceCode = regionProvince.getCode();
      caseAddress.setProvince(provinceCode);
      if (StringUtils.isNotBlank(city)) {
        Region regionCity = regionList.stream().filter(region -> Objects.equals(region.getName(), city) &&
                Objects.equals(region.getParentCode(), provinceCode)).findFirst().orElse(null);
        // 没有匹配到城市
        if (Objects.isNull(regionCity)) {
          return;
        }
        Integer cityCode = regionCity.getCode();
        caseAddress.setCity(cityCode);
        if (StringUtils.isNotBlank(district)) {
          Region regionDistrict = regionList.stream().filter(region -> Objects.equals(region.getName(), district) &&
                  Objects.equals(region.getParentCode(), cityCode)).findFirst().orElse(null);
          caseAddress.setRegion(regionDistrict == null ? null : regionDistrict.getCode());
        }
      }
    }
  }

}
