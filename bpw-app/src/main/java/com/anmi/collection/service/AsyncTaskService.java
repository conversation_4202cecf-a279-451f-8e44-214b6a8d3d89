package com.anmi.collection.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.constant.LetterConstant;
import com.anmi.collection.dto.EndInfoDTO;
import com.anmi.collection.dto.fileStorage.UploadCreatedFileInfo;
import com.anmi.collection.entity.requset.cases.*;
import com.anmi.collection.entity.requset.lawsuit.CaseToLawsuitParam;
import com.anmi.collection.entity.requset.lawsuit.LawsuitAsyncTaskQuery;
import com.anmi.collection.entity.requset.letter.LetterAsyncTaskQuery;
import com.anmi.collection.entity.requset.letter.LetterParam;
import com.anmi.collection.entity.requset.mediate.CreateMediateLetterParam;
import com.anmi.collection.entity.requset.open.ContactsMobileParam;
import com.anmi.collection.entity.requset.open.DailyRepayAutoEndParam;
import com.anmi.collection.entity.requset.sys.contacts.ContactsParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.requset.visit.AllotVisitParam;
import com.anmi.collection.entity.response.cases.*;
import com.anmi.collection.entity.response.lawsuit.LawsuitAsyncTaskVO;
import com.anmi.collection.entity.response.letter.LetterAsyncTaskVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.service.letter.LetterSendRecordService;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.CaseAllot;
import com.anmi.domain.cases.CaseQueryResult;
import com.anmi.domain.user.Company;
import com.anmi.domain.user.User;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AsyncTaskService extends BaseService<AsyncTask> {
  @Autowired private AllotCaseService allotCaseService;
  @Autowired private UserService userService;
  @Autowired private DepTeamService depTeamService;
  @Autowired private StringRedisTemplate stringRedisTemplate;
  @Autowired private CaseService caseService;
  @Autowired private DeltService deltService;
  @Autowired private ProductService productService;
  @Autowired private CompanyService companyService;
  @Resource private LetterSendRecordService letterSendRecordService;
  @Resource private FileStorageStrategyFactory fileStorageStrategyFactory;

  private static final String ASYNC_PREFIX = "async-";

  public Long createAllotTask(
      AllotCase allotCase, UserSession userSession, Long total, Integer status) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.ALLOT.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0L);
    }
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("type", allotCase.getType().toString());
    fieldJson.put("isConjoint", allotCase.getIsConjoint().toString());
    fieldJson.put("allotToTeamMembers", allotCase.getAllotToTeamMembers().toString());
    fieldJson.put("userIdList", JSON.toJSONString(allotCase.getUserIdList()));
    fieldJson.put("teamIdList", JSON.toJSONString(allotCase.getTeamIdList()));
    fieldJson.put("depIdList", JSON.toJSONString(allotCase.getDepList()));
    fieldJson.put("allotProportions", JSON.toJSONString(allotCase.getAllotProportions()));
    fieldJson.put("allotObject", allotCase.getAllotObject().toString());
    fieldJson.put("isAllAgents", allotCase.getIsAllAgents().toString());
    fieldJson.put("autoRecovery",allotCase.getAutoRecovery().toString());
    fieldJson.put("operType","0");
    if(allotCase.getAutoRecovery()){
      fieldJson.put("autoRecoveryDate", allotCase.getAutoRecoveryDate().toString());
    }
    if(ObjectUtil.isNotNull(allotCase.getSwitchType())){
      fieldJson.put("switchType",allotCase.getSwitchType().toString());
    }
    if(ObjectUtil.isNotEmpty(allotCase.getResultOperationWays())){
      fieldJson.put("resultOperationWays", CollUtil.join(allotCase.getResultOperationWays(), ","));
    }
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long createOperationStateTask(ChangeOperationStateParam param, UserSession userSession, Long total, Integer status){
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.CHANGE_OPERATE_STATE.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0L);
    }
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("query",JSON.toJSONString(param));
    fieldJson.put("userSession",JSON.toJSONString(userSession));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public AsyncTask createStrategyAllotTask(
      StrategyAllotParam strategyAllotParam, Integer type, UserSession userSession, Long total) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.STRATEGY_ALLOT.getCode());
    asyncTask.setStatus(AsyncTaskEnums.Status.ING.getCode());
    asyncTask.setTotal(total);
    asyncTask.setSuccessAmt(0l);
    asyncTask.setIgnoreCount(0l);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("type", type.toString());
    fieldJson.put("strategyRule", strategyAllotParam.getStrategyRule());
    if (StringUtils.isNotBlank(strategyAllotParam.getResidueRule())) {
      fieldJson.put("residueRule", strategyAllotParam.getResidueRule());
    }
    fieldJson.put("allotObject", String.valueOf(strategyAllotParam.getAllotObject()));
    fieldJson.put("allotToTeamMembers", String.valueOf(strategyAllotParam.getAllotToTeamMembers()));
    asyncTask.setFieldJson(fieldJson);
    //创建异步任务任务之前判断3分钟之前是否有相同操作
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(asyncTask).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }
    insertSelective(asyncTask);
    return asyncTask;
  }

  public Long createChangeStatusTask(
          UserSession userSession,
          Long total,
          Integer changeStatus,
          Date delayTime,
          String reason,
          Date restartTime,
          Integer status,
          EndInfoDTO endInfo,
          Date autoRestartDate) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.CHANGE_STATUS.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0l);
    }
    asyncTask.setIgnoreCount(0l);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("changeStatus", changeStatus.toString());
    if (delayTime != null) {
      fieldJson.put("delayTime", DateUtils.formatDate(delayTime));
    }
    if (reason != null) {
      fieldJson.put("reason", reason);
    }
    if (restartTime != null) {
      fieldJson.put("restartTime", DateUtils.formatDate(restartTime));
    }
    if (endInfo.getEndType() != null) {
      fieldJson.put("endType",endInfo.getEndType().toString());
    }
    if (endInfo.getEndConfigId() != null) {
      fieldJson.put("endConfigId", endInfo.getEndConfigId().toString());
    }
    if (Objects.nonNull(autoRestartDate)) {
      fieldJson.put("autoRestartDate", DateUtils.formatDate(autoRestartDate));
    }
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long createAdjustTask(
      AdjustCaseParam adjustCaseParam, UserSession userSession, Long total, Integer status) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.ADJUST.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0l);
    }
    asyncTask.setIgnoreCount(0l);
    Map<String, String> fieldJson = new HashMap<>();
    if (adjustCaseParam.getToOperator() != null) {
      fieldJson.put("toOperator", adjustCaseParam.getToOperator().toString());
    }
    fieldJson.put("teamId", adjustCaseParam.getTeamId().toString());
    fieldJson.put("reason", adjustCaseParam.getReason());
    Long fromOperator = adjustCaseParam.getFromOperator();
    if (fromOperator != null) {
      fieldJson.put("fromOperator", adjustCaseParam.getFromOperator().toString());
    }
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long createDeleteTask(
      UserSession userSession, Long total, Boolean allSelect, Integer status) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.DELETE.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0l);
    }
    asyncTask.setIgnoreCount(0l);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("allSelect", allSelect.toString());
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long createOperDelTask(UserSession userSession, Long total, Integer status) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.OPERATION_DEL.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0l);
    }
    asyncTask.setIgnoreCount(0l);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long createAllotVisitTask(AllotVisitParam allotVisitParam, UserSession userSession, Long total, Integer status) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    //设置异步任务类型
    asyncTask.setType(AsyncTaskEnums.Type.ALLOT_VISIT.getCode());
    //设置异步任务状态
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0L);
    }
    //暂不处理
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("caseIds", allotVisitParam.getCaseIds());
    fieldJson.put("addressIds", allotVisitParam.getAddressIds());
    fieldJson.put("userIds", allotVisitParam.getUserIds());
    fieldJson.put("userId", String.valueOf(allotVisitParam.getUserId()));
    fieldJson.put("visitStartTime", String.valueOf(allotVisitParam.getVisitStartTime()));
    fieldJson.put("visitEndTime", String.valueOf(allotVisitParam.getVisitEndTime()));
    fieldJson.put("desc", allotVisitParam.getDesc());
    fieldJson.put("cutOffState", String.valueOf(allotVisitParam.getCutOffState()));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public void updateTaskSuccessAmt(AsyncTask asyncTask, Long successAmt) {
    asyncTask.setSuccessAmt(asyncTask.getSuccessAmt() + successAmt);
    asyncTask.setUpdateTime(new Date());
    updateByPrimaryKeySelective(asyncTask);
  }

  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public void updateTaskFinish(AsyncTask asyncTask, String desc) {
    // 状态
    Integer status = AsyncTaskEnums.Status.SUCCESS.getCode();
    if (asyncTask.getSuccessAmt() == 0) {
      status = AsyncTaskEnums.Status.FAIL.getCode();
    }
    asyncTask.setStatus(status);
    // 错误原因
    if (!asyncTask.getTotal().equals(asyncTask.getSuccessAmt() + asyncTask.getIgnoreCount())) {
      if (StringUtils.isBlank(desc)) {
        asyncTask.setDesc(
            asyncTask.getCreateType().equals(AsyncTaskEnums.CreateType.SYSTEM.getCode())
                ? (AsyncTaskEnums.Type.ALLOT_COOPERATOR.getCode().equals(asyncTask.getType())
                    ? "非在案案件/案件正在协催中/协催员和案件负责人不能为同一个人"
                    : "案件状态不可执行当前操作")
                : "其他原因");
      } else {
        asyncTask.setDesc(desc.length() > 100 ? "程序异常，请联系管理员" : desc + "，请联系管理员");
      }
    }
    asyncTask.setUpdateTime(new Date());
    Company company = companyService.selectByPrimaryKey(asyncTask.getOrgId());
    // 处理错误数据
    if (AsyncTaskEnums.Type.CHANGE_STATUS.getCode().equals(asyncTask.getType())
        || AsyncTaskEnums.Type.ADJUST.getCode().equals(asyncTask.getType())
        || AsyncTaskEnums.Type.DELETE.getCode().equals(asyncTask.getType())
        || AsyncTaskEnums.Type.ALLOT_RESET.getCode().equals(asyncTask.getType())) {
      handleErrorData(asyncTask,company.getLanguage());
    }
    if (AsyncTaskEnums.Type.CHANGE_STATUS.getCode().equals(asyncTask.getType())
        && AsyncTaskEnums.CreateType.OPEN_API.getCode().equals(asyncTask.getCreateType())) {
      String outSerialNoErrorKey = KeyCache.ASYNC_OUT_SERIAL_NO_LIST + asyncTask.getId();
      Long size = stringRedisTemplate.opsForSet().size(outSerialNoErrorKey);
      if (size != null && size > 0) {
        asyncTask.setExpireTime(DateUtils.addDays(new Date(), 3));
        stringRedisTemplate.expire(outSerialNoErrorKey, 3, TimeUnit.DAYS);
      }
    }
    updateByPrimaryKeySelective(asyncTask);
  }

  private void handleErrorData(AsyncTask asyncTask,String language) {
    String errorListKey = KeyCache.CASE_CHANGE_STATUS_TASK_CASES + asyncTask.getId();
    List<List<String>> data = getErrorList(errorListKey,language);
    if (CollectionUtils.isEmpty(data)) {
      return;
    }
    log.info(
        "taskId:{},成功:{},失败:{},开始生成错误记录文件...",
        asyncTask.getId(),
        asyncTask.getSuccessAmt(),
        asyncTask.getTotal() - asyncTask.getSuccessAmt());
    // 创建文件夹
    Date expireDate = DateUtils.addDays(new Date(), 3);
    String fileName = "错误数据-" + StringUtils.getRandomNumberBIT6() + ".xlsx";
    //  上传文件
    File file = new File(fileName);
    EasyExcel.write(file).sheet().doWrite(data);
    UploadCreatedFileInfo uploadCreatedFileInfo = new UploadCreatedFileInfo();
    uploadCreatedFileInfo.setFile(file)
            .setFileName(fileName)
            .setExpireDate(expireDate)
            .setBucket(systemConfig.getOssBucket())
            .setLocalUrl(systemConfig.getLocalFilePath() + DateUtils.formatDate(new Date()) + File.separator + fileName);
    FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
    String url = fileStorageStrategy.uploadCreatedFile(uploadCreatedFileInfo);
    asyncTask.setFileName(fileName);
    asyncTask.setFileUrl(url);
    asyncTask.setExpireTime(expireDate);
  }

  private List<List<String>> getErrorList(String errorListKey,String language) {
    Set<String> idStrSet = stringRedisTemplate.opsForSet().members(errorListKey);
    if (idStrSet == null || idStrSet.isEmpty()) {
      return new ArrayList<>();
    }
    List<List<String>> dataList = new ArrayList<>();
    String txtExcelHead0=I18nService.getMessage("async.task.excelErrorHead",language,null);
    // 第一行
    List<String> head0 = Lists.newArrayList(txtExcelHead0.split(","));
    dataList.add(head0);
    // 错误数据
    List<Long> idList = idStrSet.stream().map(Long::valueOf).collect(Collectors.toList());
    List<List<Long>> groupList = CmUtil.splitList(idList, 1000);
    CaseMultiQuery query = new CaseMultiQuery();
    query.setSelectField(
        "ca.out_serial_temp,ca.name,ca.product_id,ca.org_delt_id,ca.allot_status,ca.case_status");
    for (List<Long> list : groupList) {
      query.setCaseIds(list);
      List<CaseQueryResult> caseQueryResultList = caseService.queryResultForMulti(query);
      for (CaseQueryResult caseQueryResult : caseQueryResultList) {
        List<String> values = new ArrayList<>();
        values.add(caseQueryResult.getOutSerialTemp());
        values.add(caseQueryResult.getName());
        values.add(deltService.getNames().get(caseQueryResult.getOrgDeltId()));
        values.add(productService.getNames().get(caseQueryResult.getProductId()));
        values.add(EnumUtils.getByCode(caseQueryResult.getCaseStatus(), CaseEnums.CaseStatus.class).getName());
        dataList.add(values);
      }
    }
    return dataList;
  }

  public PageOutput selectAllotTaskList(AsyncTaskQueryParam asyncTaskQueryParam) throws Exception {
    UserSession userSession = getTokenUser();
    Example example = new Example(AsyncTask.class);
    example.setOrderByClause("id desc");
    Example.Criteria criteria = example.createCriteria();
    criteria
        .andEqualTo("orgId", userSession.getOrgId())
        .andIn(
            "type",
            Lists.newArrayList(
                AsyncTaskEnums.Type.ALLOT.getCode(), AsyncTaskEnums.Type.STRATEGY_ALLOT.getCode()));
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      // 如果是分公司管理员，进行分公司隔离
      criteria.andEqualTo("depId", userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      criteria.andEqualTo("teamId", userSession.getTeamId());
    }
    String createTime = asyncTaskQueryParam.getCreateTime();
    if (!StringUtils.isBlank(createTime)) {
      String[] createTimes = createTime.split(",");
      criteria.andBetween(
          "createTime",
          new Date(Long.valueOf(createTimes[0])),
          new Date(Long.valueOf(createTimes[1])));
    }
    PageOutput pageOutput =
        selectByPage(example, BeanUtil.copyProperties(asyncTaskQueryParam, PageParam.class));
    List<AsyncTask> list = pageOutput.getList();
    if (list.isEmpty()) {
      return pageOutput;
    }
    List<AsyncAllotTaskVO> asyncAllotTaskVOList = new ArrayList<>();
    for (AsyncTask asyncTask : list) {
      AsyncAllotTaskVO asyncAllotTaskVO =
          BeanUtil.copyProperties(asyncTask, AsyncAllotTaskVO.class);
      String allotType = asyncTask.getFieldJson().get("type");
      String allotObject = asyncTask.getFieldJson().get("allotObject");
      asyncAllotTaskVO.setAllotType(allotType == null ? null : Integer.valueOf(allotType));
      asyncAllotTaskVO.setAllotObject(allotObject == null ? null : Integer.valueOf(allotObject));
      if (AsyncTaskEnums.Type.ALLOT.getCode() == asyncTask.getType()) {
        String isConjoint = asyncTask.getFieldJson().get("isConjoint");
        asyncAllotTaskVO.setIsConjoint(isConjoint == null ? null : Boolean.valueOf(isConjoint));
      }
      asyncAllotTaskVO.setFailCount(
          AsyncTaskEnums.Status.ING.getCode().equals(asyncTask.getStatus())
              ? 0
              : asyncTask.getTotal() - asyncTask.getSuccessAmt() - asyncTask.getIgnoreCount());
      asyncAllotTaskVO.setCreateBy(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));
      asyncAllotTaskVO.setCreateTime(asyncTask.getCreateTime().getTime());
      asyncAllotTaskVO.setUpdateTime(asyncTask.getUpdateTime().getTime());
      if(asyncTask.getFieldJson().containsKey("operType")){
        asyncAllotTaskVO.setOperType(Integer.valueOf(asyncTask.getFieldJson().get("operType")));
      }else {
        asyncAllotTaskVO.setOperType(0);
      }

      asyncAllotTaskVOList.add(asyncAllotTaskVO);
    }
    pageOutput.setList(asyncAllotTaskVOList);
    return pageOutput;
  }

  public AllotCaseResultListVO selectAllotCaseList(Long taskId) throws Exception {
    AsyncTask asyncTask = selectByPrimaryKey(taskId);
    String allotObjectStr = asyncTask.getFieldJson().get("allotObject");
    String allotToTeamMembersStr = asyncTask.getFieldJson().get("allotToTeamMembers");
    if (StringUtils.isBlank(allotObjectStr)) {
      throw new ApiException("分配任务列表需要allotObject参数！");
    }
    if (StringUtils.isBlank(allotToTeamMembersStr)) {
      throw new ApiException("分配任务列表需要传allotToTeamMembers参数！");
    }
    AllotCaseResultListVO resultListVO = new AllotCaseResultListVO();
    // 获取到分案对象
    Integer allotObject = Integer.valueOf(allotObjectStr);
    Boolean allotToTeamMembers = Boolean.valueOf(allotToTeamMembersStr);
    // 设置共债分配结果
    List<AllotCaseVO> conjointVOS = getAllotCaseVOS(taskId, true, allotObject, allotToTeamMembers);
    resultListVO.setConjointVOS(conjointVOS);
    // 再查找出所有的非共债caseAllot，设置非共债的分配结果
    List<AllotCaseVO> allotCaseVOS =
        getAllotCaseVOS(taskId, false, allotObject, allotToTeamMembers);
    resultListVO.setAllotCaseVOS(allotCaseVOS);
    return resultListVO;
  }

  public AllotVisitVO selectAllotVisit (Long taskId) throws Exception{
    AsyncTask asyncTask = selectByPrimaryKey(taskId);
    if (asyncTask == null) {
      throw new ApiException("该外访派单结果不存在！");
    }
    String caseIdsStr = asyncTask.getFieldJson().get("caseIds");
    String addressIdsStr = asyncTask.getFieldJson().get("addressIds");
    if (StringUtils.isBlank(caseIdsStr)) {
      throw new ApiException("外访派单需要caseIds参数！");
    }
    if (StringUtils.isBlank(addressIdsStr)) {
      throw new ApiException("外访派单需要addressIds参数！");
    }
    List<Long> caseIds = Arrays.stream(caseIdsStr.split(","))
            .map(Long::valueOf).collect(Collectors.toList());
    List<Long> addressIds = Arrays.stream(addressIdsStr.split(","))
            .map(Long::valueOf).collect(Collectors.toList());
    AllotVisitVO allotVisitVO = new AllotVisitVO();
    //设置案件数
    allotVisitVO.setCaseCount(caseIds.size());
    //设置地址数
    allotVisitVO.setAddressCount(addressIds.size());
    //设置外访员
    String userId = asyncTask.getFieldJson().get("userId");
    User user = userService.selectByPrimaryKey(Long.valueOf(userId));
    allotVisitVO.setUserId(user.getId());
    allotVisitVO.setUserName(user.getName());
    //设置所属小组
    allotVisitVO.setTeamId(user.getTeamId());
    allotVisitVO.setTeamName(depTeamService.getNames().get(user.getTeamId()));
    return allotVisitVO;
  }

  /**
   * @param taskId
   * @param isConjoint
   * @param allotObject
   * @param allotToTeamMembers
   * @return
   */
  private List<AllotCaseVO> getAllotCaseVOS(
      Long taskId, Boolean isConjoint, Integer allotObject, Boolean allotToTeamMembers) {
    List<AllotCaseVO> allotCaseVOList = new ArrayList<>();
    List<CaseAllot> allotList = null;
    Example example = new Example(CaseAllot.class);
    if (isConjoint) {
      example.createCriteria().andEqualTo("taskId", taskId).andGreaterThan("conjointCount", 0);
      allotList = allotCaseService.selectByExample(example);
    } else {
      example.createCriteria().andEqualTo("taskId", taskId).andEqualTo("conjointCount", 0);
      allotList = allotCaseService.selectByExample(example);
    }
    if (CommonUtils.isEmpty(allotList)) {
      return allotCaseVOList;
    }
    Map<Long, List<CaseAllot>> map = null;
    if (allotObject == 0 || allotToTeamMembers) {
      // 分配对象是催员或者是当前是查找共债(分配对象一定是催员)
      map = allotList.stream().collect(Collectors.groupingBy(CaseAllot::getUserId));
      if (CommonUtils.isEmpty(map)) {
        return allotCaseVOList;
      }
      Example userExp = new Example(User.class);
      userExp.createCriteria().andIn("id", map.keySet());
      List<User> users = userService.selectByExample(userExp);
      Map<Long, Long> uIdTeamIdMap =
          users.stream().collect(Collectors.toMap(User::getId, User::getTeamId));
      for (Map.Entry<Long, List<CaseAllot>> entry : map.entrySet()) {
        AllotCaseVO allotCaseVO = new AllotCaseVO();
        List<CaseAllot> list = entry.getValue();
        allotCaseVO.setUserId(entry.getKey());
        allotCaseVO.setUserName(list.get(0).getUserName());
        allotCaseVO.setCaseCount(list.stream().mapToInt(CaseAllot::getCount).sum());
        allotCaseVO.setCaseAmount(list.stream().mapToLong(CaseAllot::getAmount).sum());
        allotCaseVO.setConjointCount(list.stream().mapToInt(CaseAllot::getConjointCount).sum());
        allotCaseVO.setTeamName(depTeamService.getNames().get(uIdTeamIdMap.get(entry.getKey())));
        allotCaseVOList.add(allotCaseVO);
      }
    } else if (allotObject == 2 || allotObject == 3) {
      // 分配对象是分公司或委外机构
      map = allotList.stream().collect(Collectors.groupingBy(CaseAllot::getDepId));
      if (CommonUtils.isEmpty(map)) {
        return allotCaseVOList;
      }
      for (Map.Entry<Long, List<CaseAllot>> entry : map.entrySet()) {
        AllotCaseVO allotCaseVO = new AllotCaseVO();
        List<CaseAllot> list = entry.getValue();
        allotCaseVO.setCaseCount(list.stream().mapToInt(CaseAllot::getCount).sum());
        allotCaseVO.setCaseAmount(list.stream().mapToLong(CaseAllot::getAmount).sum());
        allotCaseVO.setConjointCount(0);
        allotCaseVO.setDepId(entry.getKey());
        allotCaseVO.setDepName(depTeamService.getNames().get(entry.getKey()));
        allotCaseVOList.add(allotCaseVO);
      }
    } else {
      // 分配对象是小组
      map = allotList.stream().collect(Collectors.groupingBy(CaseAllot::getTeamId));
      if (CommonUtils.isEmpty(map)) {
        return allotCaseVOList;
      }
      for (Map.Entry<Long, List<CaseAllot>> entry : map.entrySet()) {
        AllotCaseVO allotCaseVO = new AllotCaseVO();
        List<CaseAllot> list = entry.getValue();
        allotCaseVO.setCaseCount(list.stream().mapToInt(CaseAllot::getCount).sum());
        allotCaseVO.setCaseAmount(list.stream().mapToLong(CaseAllot::getAmount).sum());
        allotCaseVO.setConjointCount(0);
        allotCaseVO.setTeamId(entry.getKey());
        allotCaseVO.setTeamName(depTeamService.getNames().get(entry.getKey()));
        allotCaseVOList.add(allotCaseVO);
      }
    }
    return allotCaseVOList;
  }

  public PageOutput selectCaseUpdateList(AsyncTaskQueryParam asyncTaskQueryParam) throws Exception {
    UserSession userSession = getTokenUser();
    Example example = new Example(AsyncTask.class);
    example.setOrderByClause("id desc");
    Example.Criteria criteria = example.createCriteria();
    List<Integer> typeList =
        Lists.newArrayList(
            AsyncTaskEnums.Type.CHANGE_STATUS.getCode(),
            AsyncTaskEnums.Type.ADJUST.getCode(),
            AsyncTaskEnums.Type.DELETE.getCode(),
            AsyncTaskEnums.Type.ALLOT_RESET.getCode(),
            AsyncTaskEnums.Type.RECOVER_CASE.getCode(),
            AsyncTaskEnums.Type.ALLOT_COOPERATOR.getCode(),
            AsyncTaskEnums.Type.CHANGE_OPERATE_STATE.getCode(),
            AsyncTaskEnums.Type.CASE_ADD_TAG.getCode(),
            AsyncTaskEnums.Type.CASE_TO_LETTER.getCode(),
            AsyncTaskEnums.Type.CASE_TO_LAWSUIT.getCode());
    criteria.andIn("type", typeList).andEqualTo("orgId", userSession.getOrgId());
    if (UserUtils.likeAdmin(userSession.getRoleId())) {
      criteria.andEqualTo("orgId", userSession.getOrgId());
    } else if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      // 如果是分公司管理员，进行分公司隔离
      criteria.andEqualTo("depId", userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      if (userSession.getDepId() != null) {
        criteria.andEqualTo("depId", userSession.getDepId());
      }
      criteria.andEqualTo("teamId", userSession.getTeamId());
    }else {
      throw new ApiException("没有操作权限");
    }
    String createTime = asyncTaskQueryParam.getCreateTime();
    if (!StringUtils.isBlank(createTime)) {
      String[] createTimes = createTime.split(",");
      criteria.andBetween(
          "createTime",
          new Date(Long.valueOf(createTimes[0])),
          new Date(Long.valueOf(createTimes[1])));
    }
    PageOutput pageOutput =
        selectByPage(example, BeanUtil.copyProperties(asyncTaskQueryParam, PageParam.class));
    List<AsyncTask> list = pageOutput.getList();
    if (list.isEmpty()) {
      return pageOutput;
    }
    List<AsyncCaseTaskVO> asyncCaseTaskVOList =
        BeanUtil.copyPropertiesFromList(list, AsyncCaseTaskVO.class);
    for (int i = 0; i < asyncCaseTaskVOList.size(); i++) {
      AsyncTask asyncTask = list.get(i);
      AsyncCaseTaskVO asyncCaseTaskVO = asyncCaseTaskVOList.get(i);
      asyncCaseTaskVO.setFailCount(
          AsyncTaskEnums.Status.ING.getCode().equals(asyncTask.getStatus())
              ? 0
              : asyncTask.getTotal() - asyncTask.getSuccessAmt());
      asyncCaseTaskVO.setCreateBy(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));
      asyncCaseTaskVO.setCreateTime(asyncTask.getCreateTime().getTime());
      asyncCaseTaskVO.setUpdateTime(asyncTask.getUpdateTime().getTime());
      if (asyncTask.getType().equals(AsyncTaskEnums.Type.ADJUST.getCode())) {
        // 20 案件调整催员
        asyncCaseTaskVO.setChangeStatus(20);
      } else if (asyncTask.getType().equals(AsyncTaskEnums.Type.DELETE.getCode())) {
        // 30 删除，31 彻底删除，32 清空回收站
        asyncCaseTaskVO.setChangeStatus(
            "false".equals(asyncTask.getFieldJson().get("delType"))
                ? 30
                : "true".equals(asyncTask.getFieldJson().get("allSelect")) ? 32 : 31);
      } else if (asyncTask.getType().equals(AsyncTaskEnums.Type.ALLOT_RESET.getCode())) {
        // 40 重新分配
        asyncCaseTaskVO.setChangeStatus(40);
      } else if (asyncTask.getType().equals(AsyncTaskEnums.Type.RECOVER_CASE.getCode())) {
        // 50 案件恢复
        asyncCaseTaskVO.setChangeStatus(50);
      } else if (asyncTask.getType().equals(AsyncTaskEnums.Type.ALLOT_COOPERATOR.getCode())) {
        // 60 协催分配
        asyncCaseTaskVO.setChangeStatus(60);
      } else if (asyncTask.getType().equals(AsyncTaskEnums.Type.CHANGE_OPERATE_STATE.getCode())) {
        // 60 协催分配
        asyncCaseTaskVO.setChangeStatus(70);
      }else if (asyncTask.getType().equals(AsyncTaskEnums.Type.CASE_ADD_TAG.getCode())) {
        // 60 协催分配
        asyncCaseTaskVO.setChangeStatus(80);
      } else if (asyncTask.getType().equals(AsyncTaskEnums.Type.CASE_TO_LETTER.getCode())) {
        // 没办法这样写，按照之前代码 90代表加入函件
        asyncCaseTaskVO.setChangeStatus(90);
      } else if (asyncTask.getType().equals(AsyncTaskEnums.Type.CASE_TO_LAWSUIT.getCode())) {
        // 没办法这样写，按照之前代码 100代表加入诉讼
        asyncCaseTaskVO.setChangeStatus(100);
      }
      else {
        asyncCaseTaskVO.setChangeStatus(
            Integer.valueOf(asyncTask.getFieldJson().get("changeStatus")));
      }
      asyncCaseTaskVO.setCreateType(asyncTask.getCreateType());
      asyncCaseTaskVO.setExpireTime(
          asyncTask.getExpireTime() != null ? asyncTask.getExpireTime().getTime() : null);
    }
    pageOutput.setList(asyncCaseTaskVOList);
    return pageOutput;
  }

  public PageOutput casePlanTaskList(AsyncTaskQueryParam asyncTaskQueryParam) throws Exception {
    UserSession userSession = getTokenUser();
    Example example = new Example(AsyncTask.class);
    example.setOrderByClause("id desc");
    Example.Criteria criteria = example.createCriteria();
    List<Integer> typeList = Lists.newArrayList(AsyncTaskEnums.Type.ADD_CASE_PLAN.getCode());
    criteria.andIn("type", typeList).andEqualTo("orgId", userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      // 如果是分公司管理员，进行分公司隔离
      criteria.andEqualTo("depId", userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      criteria.andEqualTo("teamId", userSession.getTeamId());
    }
    String createTime = asyncTaskQueryParam.getCreateTime();
    if (!StringUtils.isBlank(createTime)) {
      String[] createTimes = createTime.split(",");
      criteria.andBetween(
          "createTime",
          new Date(Long.valueOf(createTimes[0])),
          new Date(Long.valueOf(createTimes[1])));
    }
    PageOutput pageOutput =
        selectByPage(example, BeanUtil.copyProperties(asyncTaskQueryParam, PageParam.class));
    List<AsyncTask> list = pageOutput.getList();
    if (list.isEmpty()) {
      return pageOutput;
    }
    List<AsyncCaseTaskVO> asyncCaseTaskVOList =
        BeanUtil.copyPropertiesFromList(list, AsyncCaseTaskVO.class);
    for (int i = 0; i < asyncCaseTaskVOList.size(); i++) {
      AsyncTask asyncTask = list.get(i);
      AsyncCaseTaskVO asyncCaseTaskVO = asyncCaseTaskVOList.get(i);
      asyncCaseTaskVO.setFailCount(
          AsyncTaskEnums.Status.ING.getCode().equals(asyncTask.getStatus())
              ? 0
              : asyncTask.getTotal() - asyncTask.getSuccessAmt());
      asyncCaseTaskVO.setCreateBy(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));
      asyncCaseTaskVO.setCreateTime(asyncTask.getCreateTime().getTime());
      asyncCaseTaskVO.setUpdateTime(asyncTask.getUpdateTime().getTime());
      asyncCaseTaskVO.setCreateType(asyncTask.getCreateType());
    }
    pageOutput.setList(asyncCaseTaskVOList);
    return pageOutput;
  }

  public PageOutput selectTaskList(AsyncTaskQueryParam asyncTaskQueryParam) throws Exception {
    UserSession userSession = getTokenUser();
    Example example = new Example(AsyncTask.class);
    example.setOrderByClause("id desc");
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("orgId", userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      // 如果是分公司管理员，进行分公司隔离
      criteria.andEqualTo("depId", userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      criteria.andEqualTo("teamId", userSession.getTeamId());
    }
    if (Objects.nonNull(asyncTaskQueryParam.getType())) {
      criteria.andEqualTo("type", asyncTaskQueryParam.getType());
    }
    if (!CollectionUtils.isEmpty(asyncTaskQueryParam.getTypes())) {
      criteria.andIn("type", asyncTaskQueryParam.getTypes());
    }
    if(!CollectionUtils.isEmpty(asyncTaskQueryParam.getOperators())){
      criteria.andIn("createBy",asyncTaskQueryParam.getOperators());
    }
    String createTime = asyncTaskQueryParam.getCreateTime();
    if (!StringUtils.isBlank(createTime)) {
      String[] createTimes = createTime.split(",");
      criteria.andBetween(
          "createTime",
          new Date(Long.valueOf(createTimes[0])),
          new Date(Long.valueOf(createTimes[1])));
    }
    PageOutput pageOutput =
        selectByPage(example, BeanUtil.copyProperties(asyncTaskQueryParam, PageParam.class));
    List<AsyncTask> list = pageOutput.getList();
    if (list.isEmpty()) {
      return pageOutput;
    }
    List<AsyncAllotTaskVO> asyncAllotTaskVOList =
        BeanUtil.copyPropertiesFromList(list, AsyncAllotTaskVO.class);
    for (int i = 0; i < asyncAllotTaskVOList.size(); i++) {
      AsyncAllotTaskVO asyncAllotTaskVO = asyncAllotTaskVOList.get(i);
      AsyncTask asyncTask = list.get(i);
      asyncAllotTaskVO.setFailCount(
          AsyncTaskEnums.Status.ING.getCode().equals(asyncTask.getStatus())
              ? 0
              : asyncTask.getTotal() - asyncTask.getSuccessAmt());
      asyncAllotTaskVO.setCreateBy(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));
      asyncAllotTaskVO.setCreateTime(asyncTask.getCreateTime().getTime());
      asyncAllotTaskVO.setUpdateTime(asyncTask.getUpdateTime().getTime());
    }
    pageOutput.setList(asyncAllotTaskVOList);
    return pageOutput;
  }

  public PageOutput<AsyncTaskVO> selectList(AsyncTaskQueryParam asyncTaskQueryParam) throws Exception {
    UserSession userSession = getTokenUser();
    Example example = new Example(AsyncTask.class);
    example.setOrderByClause("id desc");
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("orgId", userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      // 如果是分公司管理员，进行分公司隔离
      criteria.andEqualTo("depId", userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      criteria.andEqualTo("teamId", userSession.getTeamId());
    }
    if (Objects.nonNull(asyncTaskQueryParam.getType())) {
      criteria.andEqualTo("type", asyncTaskQueryParam.getType());
    }
    if (!CollectionUtils.isEmpty(asyncTaskQueryParam.getTypes())) {
      criteria.andIn("type", asyncTaskQueryParam.getTypes());
    }
    if(!CollectionUtils.isEmpty(asyncTaskQueryParam.getOperators())){
      criteria.andIn("createBy",asyncTaskQueryParam.getOperators());
    }
    String createTime = asyncTaskQueryParam.getCreateTime();
    if (!StringUtils.isBlank(createTime)) {
      String[] createTimes = createTime.split(",");
      criteria.andBetween(
        "createTime",
        new Date(Long.parseLong(createTimes[0])),
        new Date(Long.parseLong(createTimes[1])));
    }
    PageOutput<AsyncTask> pageOutput = selectByPage(example, BeanUtil.copyProperties(asyncTaskQueryParam, PageParam.class));
    List<AsyncTask> list = pageOutput.getList();

    PageOutput<AsyncTaskVO> result = new PageOutput<>();
    if (list.isEmpty()) {
      return result;
    }
    result.setPages(pageOutput.getPages());
    result.setTotal(pageOutput.getTotal());
    result.setPageNum(pageOutput.getPageNum());
    result.setLimit(pageOutput.getLimit());
    result.setPageSize(pageOutput.getPageSize());
    Map<Long,String> userMap = userService.getNames(userSession.getOrgId());
    List<AsyncTaskVO> asyncAllotTaskVOList = BeanUtil.copyPropertiesFromList(list, AsyncTaskVO.class);
    for (int i = 0; i < asyncAllotTaskVOList.size(); i++) {
      AsyncTaskVO asyncTaskVO = asyncAllotTaskVOList.get(i);
      AsyncTask asyncTask = list.get(i);
      asyncTaskVO.setFailCount(AsyncTaskEnums.Status.ING.getCode().equals(asyncTask.getStatus())
          ? 0 : asyncTask.getTotal() - asyncTask.getSuccessAmt());
      asyncTaskVO.setCreateByName(userMap.get(asyncTask.getCreateBy()));
      asyncTaskVO.setCreateTime(asyncTask.getCreateTime().getTime());
      asyncTaskVO.setUpdateTime(asyncTask.getUpdateTime().getTime());
    }
    result.setList(asyncAllotTaskVOList);
    return result;
  }

  public PageOutput selectAllotVisitList (AsyncTaskQueryParam asyncTaskQueryParam) throws Exception{
    UserSession userSession = getTokenUser();
    Example example = new Example(AsyncTask.class);
    example.setOrderByClause("id desc");
    Example.Criteria criteria = example.createCriteria();
    //查询的异步任务类型 外访派单
    List<Integer> typeList = Lists.newArrayList(AsyncTaskEnums.Type.ALLOT_VISIT.getCode());
    criteria.andIn("type", typeList).andEqualTo("orgId", userSession.getOrgId());
    //判断当前用户权限
    if (UserUtils.likeAdmin(userSession.getRoleId())) {
      criteria.andEqualTo("orgId", userSession.getOrgId());
    } else if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      //如果是分公司管理员
      criteria.andEqualTo("depId", userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      if (userSession.getDepId() != null) {
        criteria.andEqualTo("depId", userSession.getDepId());
      }
      criteria.andEqualTo("teamId", userSession.getTeamId());
    }else {
      throw new ApiException("没有操作权限");
    }
    //操作时间
    String createTime = asyncTaskQueryParam.getCreateTime();
    if (!StringUtils.isBlank(createTime)) {
      String[] createTimes = createTime.split(",");
      criteria.andBetween("createTime",
              new Date(Long.valueOf(createTimes[0])),
              new Date(Long.valueOf(createTimes[1])));
    }
    PageOutput pageOutput =
            selectByPage(example, BeanUtil.copyProperties(asyncTaskQueryParam, PageParam.class));
    List<AsyncTask> list = pageOutput.getList();
    if (CollectionUtils.isEmpty(list)) {
      return pageOutput;
    }
    List<AsyncAllotVisitTaskVO> asyncAllotVisitTaskVOList = new ArrayList<>();
    for (AsyncTask asyncTask : list) {
      AsyncAllotVisitTaskVO asyncAllotVisitTaskVO =
              BeanUtil.copyProperties(asyncTask, AsyncAllotVisitTaskVO.class);
      //失败数量
      asyncAllotVisitTaskVO.setFailCount(
              AsyncTaskEnums.Status.ING.getCode().equals(asyncTask.getStatus())
                      ? 0 : asyncTask.getTotal() - asyncTask.getSuccessAmt() - asyncTask.getIgnoreCount());
      //设置操作人
      asyncAllotVisitTaskVO.setCreateBy(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));
      // 设置创建时间 操作时间
      asyncAllotVisitTaskVO.setCreateTime(asyncTask.getCreateTime().getTime());
      // 设置更新时间
      asyncAllotVisitTaskVO.setUpdateTime(asyncTask.getUpdateTime().getTime());
      asyncAllotVisitTaskVOList.add(asyncAllotVisitTaskVO);
    }
    pageOutput.setList(asyncAllotVisitTaskVOList);
    return pageOutput;
  }

  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public AsyncTask createOpenChangeStatusTask(
      Long orgId,
      Long total,
      Integer changeStatus,
      Date delayTime,
      String reason,
      Long orgDeltId,
      Date restartTime) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(0l);
    asyncTask.setOrgId(orgId);
    asyncTask.setType(AsyncTaskEnums.Type.CHANGE_STATUS.getCode());
    asyncTask.setStatus(AsyncTaskEnums.Status.ING.getCode());
    asyncTask.setTotal(total);
    asyncTask.setSuccessAmt(0l);
    asyncTask.setIgnoreCount(0l);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("changeStatus", changeStatus.toString());
    if (delayTime != null) {
      fieldJson.put("delayTime", DateUtils.formatDate(delayTime));
    }
    if (restartTime != null) {
      fieldJson.put("restartTime", DateUtils.formatDate(restartTime));
    }
    if (reason != null) {
      fieldJson.put("reason", reason);
    }
    fieldJson.put("orgDeltId", orgDeltId.toString());
    asyncTask.setFieldJson(fieldJson);
    asyncTask.setCreateType(AsyncTaskEnums.CreateType.OPEN_API.getCode());
    insertSelective(asyncTask);
    return asyncTask;
  }

  public Long createAllotResetTask(
      UserSession userSession,
      Long total,
      Integer taskType,
      Integer revokeTo,
      Integer status) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(taskType);
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0L);
    }
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("revokeTo", revokeTo.toString());
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long createRepDelTask(UserSession userSession, Long total) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.REPAYMENT_DEL.getCode());
    asyncTask.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
    asyncTask.setTotal(total);
    asyncTask.setSuccessAmt(total);
    asyncTask.setIgnoreCount(0l);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long createRecoverTask(UserSession userSession, Long total) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.RECOVER_CASE.getCode());
    asyncTask.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
    asyncTask.setTotal(total);
    asyncTask.setSuccessAmt(total);
    asyncTask.setIgnoreCount(0l);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public AsyncTask createCooperationAllotTask(
      UserSession userSession, Long total, Long cooperatorId) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.ALLOT_COOPERATOR.getCode());
    asyncTask.setStatus(AsyncTaskEnums.Status.ING.getCode());
    asyncTask.setTotal(total);
    asyncTask.setSuccessAmt(0l);
    asyncTask.setIgnoreCount(0l);
    Map<String, String> fieldMap = new HashMap<>();
    fieldMap.put("cooperatorId", cooperatorId.toString());
    asyncTask.setFieldJson(fieldMap);
    //创建异步任务任务之前判断3分钟之前是否有相同操作
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(asyncTask).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }
    insertSelective(asyncTask);
    return asyncTask;
  }

  public Boolean isHaveCaseDelTask(Long orgId) {
    List<String> keyList =
        stringRedisTemplate.opsForList().range(KeyCache.CASE_CHANGE_STATUS_TASK_ID_LIST, 0, -1);
    if (CollectionUtils.isEmpty(keyList)) {
      return false;
    }
    List<Long> taskIdList = new ArrayList<>();
    for (String taskIdStr : keyList) {
      Long taskId = Long.valueOf(taskIdStr);
      taskIdList.add(taskId);
    }
    Example example = new Example(AsyncTask.class);
    example
        .createCriteria()
        .andIn("id", taskIdList)
        .andEqualTo("orgId", orgId)
        .andEqualTo("type", AsyncTaskEnums.Type.DELETE.getCode());
    List<AsyncTask> asyncTasks = selectByExample(example);
    if (CollectionUtils.isEmpty(asyncTasks)) {
      return false;
    }
    for (AsyncTask asyncTask : asyncTasks) {
      Map<String, String> fieldJson = asyncTask.getFieldJson();
      if (fieldJson != null
          && fieldJson.get("delType") != null
          && Boolean.valueOf(fieldJson.get("delType"))) {
        return true;
      }
    }
    return false;
  }

  public Long createAddCaseTagTask(CaseAddTagParam param,UserSession userSession,Long total,Integer status){
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.CASE_ADD_TAG.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0L);
    }
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("query",JSON.toJSONString(param));
    fieldJson.put("userSession",JSON.toJSONString(userSession));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long createAddCaseToLetterTask(CaseToLetterParam param, UserSession userSession, Long total, Integer status){
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    asyncTask.setDepId(userSession.getDepId());
    asyncTask.setTeamId(userSession.getTeamId());
    asyncTask.setType(AsyncTaskEnums.Type.CASE_TO_LETTER.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0L);
    }
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("query",JSON.toJSONString(param));
    fieldJson.put("userSession",JSON.toJSONString(userSession));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long createAddCaseToLawsuitTask(CaseToLawsuitParam param, UserSession userSession, Long total, Integer status){
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.CASE_TO_LAWSUIT.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0L);
    }
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("query",JSON.toJSONString(param));
    fieldJson.put("userSession",JSON.toJSONString(userSession));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long createLetterFileTask(LetterParam param, UserSession userSession, Long total, Integer status, Integer success, Integer fail, String desc){
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.CASE_LETTER_CREATE_FILE.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    asyncTask.setDesc(desc);
    asyncTask.setSuccessAmt(success.longValue());
    asyncTask.setIgnoreCount(fail.longValue());
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("query",JSON.toJSONString(param));
    fieldJson.put("userSession",JSON.toJSONString(userSession));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long delLetterTask(LetterParam param, UserSession userSession, Long total, Integer status){
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.CASE_LETTER_DEL.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0L);
    }
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("query",JSON.toJSONString(param));
    fieldJson.put("userSession",JSON.toJSONString(userSession));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long updateLetterTask(LetterParam param, UserSession userSession, Integer total, Integer status, Integer success, Integer fail){
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.CASE_LETTER_UPDATE.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total.longValue());
    asyncTask.setSuccessAmt(success.longValue());
    asyncTask.setIgnoreCount(fail.longValue());
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("query",JSON.toJSONString(param));
    fieldJson.put("userSession",JSON.toJSONString(userSession));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public PageOutput<LetterAsyncTaskVO> getLetterAsyncTaskList(LetterAsyncTaskQuery query) {
    Example example = new Example(AsyncTask.class);
    UserSession userSession = UserUtils.getTokenUser();
    example.and().andEqualTo("orgId", userSession.getOrgId());
    if (UserUtils.likeBranchAdmin()) {
      // 如果是分公司管理员，进行分公司隔离
      example.and().andEqualTo("depId", userSession.getDepId());
    }
    if (query.getType() == null) {
      List<Integer> types = Lists.newArrayList( AsyncTaskEnums.Type.CASE_LETTER_CREATE_FILE.getCode(),
              AsyncTaskEnums.Type.CASE_LETTER_DEL.getCode(), AsyncTaskEnums.Type.CASE_LETTER_UPDATE.getCode());
      example.and().andIn("type", types);
    } else {
      example.and().andEqualTo("type", query.getType());
    }
    if (query.getCreateTime() != null) {
      String[] dates = query.getCreateTime().split(",");
      example.and().andBetween(
              "createTime",
              DateUtils.timeStampToStr(Long.valueOf(dates[0])),
              DateUtils.timeStampToStr(Long.valueOf(dates[1])));
    }
    example.orderBy("createTime").desc();
    PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
    Page page = PageUtils.setPage(pageParam);
    List<AsyncTask> asyncTasks = this.selectByExample(example);
    List<Long> userIds = asyncTasks.stream().filter(asyncTask -> asyncTask.getCreateBy() != null).map(AsyncTask::getCreateBy).collect(Collectors.toList());
    Map<Long, String> userMap = userService.getUserMap(userIds);
    List<LetterAsyncTaskVO> letterAsyncTaskVOS = new ArrayList<>();
    asyncTasks.forEach(asyncTask -> {
      LetterAsyncTaskVO letterAsyncTaskVO = AuthBeanUtils.copy(asyncTask, LetterAsyncTaskVO.class);
      letterAsyncTaskVO.setFailCount(asyncTask.getIgnoreCount());
      letterAsyncTaskVO.setOperatorName(userMap.get(letterAsyncTaskVO.getCreateBy()));
      letterAsyncTaskVO.setCreateTime(asyncTask.getCreateTime().getTime());
      letterAsyncTaskVO.setUpdateTime(asyncTask.getUpdateTime().getTime());
      if (StringUtils.isNotBlank(asyncTask.getFieldJson().get("query"))) {
        LetterParam param = JSONObject.parseObject(asyncTask.getFieldJson().get("query"), LetterParam.class);
        Integer sendType = param.getSendType();
        // 老数据没有传sendType，代表线下邮寄
        if (sendType == null) {
          letterAsyncTaskVO.setSendType(LetterConstant.SEND_TYPE_POST);
        } else {
          letterAsyncTaskVO.setSendType(sendType);
        }
        if (Objects.equals(sendType, LetterConstant.SEND_TYPE_SMS)) {
          Integer successAmount = letterSendRecordService.getSuccessAmount(asyncTask.getId());
          letterAsyncTaskVO.setSuccessAmt(successAmount.longValue());
          letterAsyncTaskVO.setFailCount(asyncTask.getTotal() - successAmount.longValue());
        }
      }
      letterAsyncTaskVOS.add(letterAsyncTaskVO);
    });
    return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), letterAsyncTaskVOS);
  }

  public Long createCaseIgnoreTask(CaseIgnorePlanParam param, UserSession userSession, Long total, Integer status, Integer type){
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(type);
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0L);
    }
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("query",JSON.toJSONString(param));
    fieldJson.put("userSession",JSON.toJSONString(userSession));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  public Long createUpdateContactsStatusTask(ContactsParam param, UserSession userSession, Long total, Integer status){
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.CONTACTS_STATUS_UPDATE.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0L);
    }
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("query",JSON.toJSONString(param));
    fieldJson.put("userSession",JSON.toJSONString(userSession));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  /**
   * 创建异步任务(通用)
   *
   * @param param       参数
   * @param userSession 用户会话
   * @param total       总数
   * @param status      状态
   * @param type        类型
   * @return {@link Long}
   */
  public Long createAsyncTask(Object param, UserSession userSession, Long total, Integer status, Integer type) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(type);
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    if (AsyncTaskEnums.Status.SUCCESS.getCode().equals(status)) {
      asyncTask.setSuccessAmt(total);
    } else {
      asyncTask.setSuccessAmt(0L);
    }
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("query",JSON.toJSONString(param));
    fieldJson.put("userSession",JSON.toJSONString(userSession));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  /**
   * 发函文件操作记录
   * @param query
   * @return
   */
  public PageOutput<LetterAsyncTaskVO> getLetterFileRecord(LetterAsyncTaskQuery query) {
    Example example = new Example(AsyncTask.class);
    UserSession userSession = UserUtils.getTokenUser();
    example.and().andEqualTo("orgId", userSession.getOrgId());
    // 公司隔离
    if (UserUtils.likeBranchAdmin()) {
      example.and().andEqualTo("depId", userSession.getDepId());
    }
    example.and().andEqualTo("type", AsyncTaskEnums.Type.DEL_LETTER_FILE.getCode());
    if (query.getCreateTime() != null) {
      String[] dates = query.getCreateTime().split(",");
      example.and().andBetween("createTime",
              DateUtils.timeStampToStr(Long.valueOf(dates[0])),
              DateUtils.timeStampToStr(Long.valueOf(dates[1])));
    }
    example.orderBy("id").desc();
    PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
    Page page = PageUtils.setPage(pageParam);
    List<AsyncTask> asyncTasks = this.selectByExample(example);
    List<Long> userIds = asyncTasks.stream().filter(asyncTask -> asyncTask.getCreateBy() != null).map(AsyncTask::getCreateBy).collect(Collectors.toList());
    Map<Long, String> userMap = userService.getUserMap(userIds);
    List<LetterAsyncTaskVO> letterAsyncTaskVOS = new ArrayList<>();
    asyncTasks.forEach(asyncTask -> {
      LetterAsyncTaskVO letterAsyncTaskVO = AuthBeanUtils.copy(asyncTask, LetterAsyncTaskVO.class);
      letterAsyncTaskVO.setFailCount(asyncTask.getIgnoreCount());
      letterAsyncTaskVO.setOperatorName(userMap.get(letterAsyncTaskVO.getCreateBy()));
      letterAsyncTaskVO.setCreateTime(asyncTask.getCreateTime().getTime());
      letterAsyncTaskVO.setUpdateTime(asyncTask.getUpdateTime().getTime());
      letterAsyncTaskVOS.add(letterAsyncTaskVO);
    });
    return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), letterAsyncTaskVOS);
  }

  /**
   * 诉讼操作记录
   * 目前只有删除诉讼文件
   * @param query
   * @return
   */
  public PageOutput<LawsuitAsyncTaskVO> getLawsuitFileRecord(LawsuitAsyncTaskQuery query) {
    Example example = new Example(AsyncTask.class);
    UserSession userSession = UserUtils.getTokenUser();
    example.and().andEqualTo("orgId", userSession.getOrgId());
    // 公司隔离
    if (UserUtils.likeBranchAdmin()) {
      example.and().andEqualTo("depId", userSession.getDepId());
    }
    if (UserUtils.likeTeamLeader()) {
      example.and().andEqualTo("teamId", userSession.getTeamId());
    }
    example.and().andEqualTo("type", AsyncTaskEnums.Type.DEL_LAWSUIT_FILE.getCode());
    if (StringUtils.isNotBlank(query.getCreateTime())) {
      String[] dates = query.getCreateTime().split(",");
      example.and().andBetween("createTime",
        DateUtils.timeStampToStr(Long.valueOf(dates[0])),
        DateUtils.timeStampToStr(Long.valueOf(dates[1])));
    }
    example.orderBy("id").desc();
    PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
    Page page = PageUtils.setPage(pageParam);
    List<AsyncTask> asyncTasks = this.selectByExample(example);
    List<Long> userIds = asyncTasks.stream().filter(asyncTask -> asyncTask.getCreateBy() != null).map(AsyncTask::getCreateBy).collect(Collectors.toList());
    Map<Long, String> userMap = userService.getUserMap(userIds);
    List<LawsuitAsyncTaskVO> lawsuitAsyncTaskVOS = new ArrayList<>();
    asyncTasks.forEach(asyncTask -> {
      LawsuitAsyncTaskVO lawsuitAsyncTaskVO = AuthBeanUtils.copy(asyncTask, LawsuitAsyncTaskVO.class);
      lawsuitAsyncTaskVO.setFailCount(asyncTask.getIgnoreCount());
      lawsuitAsyncTaskVO.setOperatorName(userMap.get(lawsuitAsyncTaskVO.getCreateBy()));
      lawsuitAsyncTaskVOS.add(lawsuitAsyncTaskVO);
    });
    return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int) page.getTotal(), lawsuitAsyncTaskVOS);
  }
  public PageOutput<AttachImportVO> attachImportList(PageParam pageParam) {
    UserSession loginUser = getTokenUser();

    Example example = new Example(AsyncTask.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("orgId", loginUser.getOrgId());
    criteria.andEqualTo("type", AsyncTaskEnums.Type.CASE_ATTACH_IMPORT.getCode());
    if (UserUtils.likeBranchAdmin()) {
      criteria.andEqualTo("depId", loginUser.getDepId());
    }
    example.setOrderByClause("id desc");

    Page page = super.setPage(pageParam);
    List<AsyncTask> tasks = this.selectByExample(example);
    Map<Long,String> userMap = userService.getNames(loginUser.getOrgId());
    List<AttachImportVO> attachImportVOS = tasks.stream().map(task -> {
      AttachImportVO attachImportVO = new AttachImportVO();
      attachImportVO.setId(task.getId())
              .setCreateTime(ObjectUtil.isNull(task.getCreateTime())?null:task.getCreateTime().getTime())
              .setFileName(task.getFileName())
              .setStatus(task.getStatus())
              .setTotal(task.getTotal())
              .setSuccess(task.getSuccessAmt())
              .setFail(task.getTotal()-task.getSuccessAmt())
              .setFailFileUrl(task.getFileUrl())
              .setExpireTime(ObjectUtil.isNull(task.getExpireTime())?null:task.getExpireTime().getTime())
              .setCreateBy(task.getCreateBy())
              .setCreateByName(userMap.get(task.getCreateBy()));
      return attachImportVO;
    }).collect(Collectors.toList());
    return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int) page.getTotal(), attachImportVOS);
  }

  /**
   * 创建更新案件为停催状态的任务
   *
   * @param orgId     公司id
   * @param total     列表数量
   * @param reason    停催原因
   * @param type      类型
   * @param orgDeltId 委案公司id
   * @param contacts  联系人信息
   * @return
   */
  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public AsyncTask createOpenContactInvalidTask(Long orgId, Long total, String reason, Integer type, Long orgDeltId, List<ContactsMobileParam> contacts) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(0L);
    asyncTask.setOrgId(orgId);
    asyncTask.setType(AsyncTaskEnums.Type.CONTACTS_INVALID.getCode());
    asyncTask.setStatus(AsyncTaskEnums.Status.ING.getCode());
    asyncTask.setTotal(total);
    asyncTask.setSuccessAmt(0L);
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    if (reason != null) {
      fieldJson.put("reason", reason);
    }
    fieldJson.put("type", type.toString());
    fieldJson.put("orgDeltId", orgDeltId.toString());
    asyncTask.setFieldJson(fieldJson);
    asyncTask.setCreateType(AsyncTaskEnums.CreateType.OPEN_API.getCode());
    //创建异步任务任务之前判断3分钟之前是否有相同操作
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(asyncTask).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }
    insertSelective(asyncTask);
    return asyncTask;
  }

  /**
   * 获取异步任务列表
   *
   * @param taskIds 任务ID
   * @return {@link List}<{@link AsyncTask}>
   */
  public List<AsyncTask> selectTasks(List<Long> taskIds) {
    Example example = new Example(AsyncTask.class);
    example.selectProperties("id","orgId");
    example.setOrderByClause("id asc");

    Example.Criteria criteria = example.createCriteria();
    criteria.andIn("id", taskIds);

    return selectByExample(example);
  }

  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public Long createMediateFileTask(CreateMediateLetterParam param, UserSession userSession, long total,long fail, long success, Integer status, String desc) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(userSession.getId());
    asyncTask.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      asyncTask.setDepId(userSession.getDepId());
      asyncTask.setTeamId(userSession.getTeamId());
    }
    asyncTask.setType(AsyncTaskEnums.Type.CASE_TO_MEDIATE_FILE.getCode());
    asyncTask.setStatus(status);
    asyncTask.setTotal(total);
    asyncTask.setSuccessAmt(success);
    asyncTask.setIgnoreCount(fail);
    asyncTask.setDesc(desc);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("query",JSON.toJSONString(param));
    fieldJson.put("userSession",JSON.toJSONString(userSession));
    asyncTask.setFieldJson(fieldJson);
    insertSelective(asyncTask);
    return asyncTask.getId();
  }

  /**
   * 创建每日还款自动结案异步任务
   *
   * @param orgId 公司id
   * @param param 自动结案参数
   * @return 异步任务
   */
  public AsyncTask createDailyRepayAutoEndTask(Long orgId, DailyRepayAutoEndParam param) {
    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(0L);
    asyncTask.setSuccessAmt(0L);
    asyncTask.setIgnoreCount(0L);
    asyncTask.setOrgId(orgId);
    asyncTask.setStatus(AsyncTaskEnums.Status.ING.getCode());
    asyncTask.setType(AsyncTaskEnums.Type.DAILY_REPAY_AUTO_END.getCode());
    asyncTask.setTotal((long) param.getOut_serial_no_list().size());
    asyncTask.setCreateType(AsyncTaskEnums.CreateType.OPEN_API.getCode());
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("repaymentTime", String.valueOf(param.getRepayment_time().getTime()));
    asyncTask.setFieldJson(fieldJson);

    //创建异步任务任务之前判断3分钟之前是否有相同操作
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(asyncTask).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (org.apache.commons.lang3.StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }
    insertSelective(asyncTask);
    return asyncTask;
  }
}
