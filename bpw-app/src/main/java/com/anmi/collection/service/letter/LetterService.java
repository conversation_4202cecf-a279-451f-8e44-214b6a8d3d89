package com.anmi.collection.service.letter;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.constant.CommonConstant;
import com.anmi.collection.constant.DictionaryConstant;
import com.anmi.collection.constant.LetterConstant;
import com.anmi.collection.dto.CaseDTO;
import com.anmi.collection.dto.fileStorage.ReadFileInfo;
import com.anmi.collection.dto.fileStorage.UploadCreatedFileStream;
import com.anmi.collection.dto.fileStorage.UploadFileInfo;
import com.anmi.collection.entity.requset.letter.LetterFileDeleteParam;
import com.anmi.collection.entity.requset.letter.LetterParam;
import com.anmi.collection.entity.requset.mediate.SignerSealMatchParam;
import com.anmi.collection.entity.requset.sys.MailParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.letter.LetterSignerVO;
import com.anmi.collection.entity.response.letter.LetterVO;
import com.anmi.collection.entity.response.visit.CaseAddressVO;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.exception.SpaceLessException;
import com.anmi.collection.manager.MailManager;
import com.anmi.collection.manager.MessageManager;
import com.anmi.collection.mapper.AsyncTaskMapper;
import com.anmi.collection.mapper.LetterMapper;
import com.anmi.collection.mapper.LetterSendRecordMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.*;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.utils.AuthBeanUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.RandomUtil;
import com.anmi.collection.utils.dict.OSSClientUtil;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.PdfUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.base.DictionaryEntity;
import com.anmi.domain.cases.*;
import com.anmi.domain.letter.Letter;
import com.anmi.domain.letter.LetterSendRecord;
import com.anmi.domain.letter.LetterTemplate;
import com.anmi.domain.sys.CustomField;
import com.anmi.domain.sys.DownloadTask;
import com.anmi.domain.yunpian.EmailTemplate;
import com.anmi.domain.yunpian.SmsSend;
import com.anmi.domain.yunpian.SmsTemplate;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/10 13:58
 */
@Service
@Slf4j
public class LetterService extends BaseService<Letter> {
    @Resource
    private LetterMapper letterMapper;
    @Resource
    private CaseAddressService caseAddressService;
    @Resource
    private AsyncTaskService asyncTaskService;
    @Resource
    private CaseService caseService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private DeltService deltService;
    @Resource
    private ProductService productService;
    @Resource
    private OutBatchService outBatchService;
    @Resource
    private InnerBatchService innerBatchService;
    @Resource
    private LetterTemplateService letterTemplateService;
    @Resource
    private SystemConfig systemConfig;
    @Resource
    private DownloadTaskService downloadTaskService;
    @Resource
    private UserService userService;
    @Resource
    private DepTeamService depTeamService;
    @Resource
    private RoleService roleService;
    @Resource
    private EmailTemplateService emailTemplateService;
    @Resource
    private SmsSendService smsSendService;
    @Resource
    private MessageManager messageManager;
    @Resource
    private SmsTemplateService smsTemplateService;
    @Resource
    private MailManager mailManager;
    @Resource
    private CasePlanService casePlanService;
    @Resource
    private CustomFieldService customFieldService;
    @Resource
    private LetterSendRecordMapper letterSendRecordMapper;
    @Resource
    private LetterSendRecordService letterSendRecordService;
    @Resource
    private I18nService i18nService;
    @Resource
    private LetterSignerService letterSignerService;
    @Autowired private OrgSwitchService orgSwitchService;
    @Resource
    private OrgSpaceService orgSpaceService;
    @Resource
    private AsyncTaskMapper asyncTaskMapper;
    @Resource
    private FileStorageStrategyFactory fileStorageStrategyFactory;


    private static final String SEND_DAY = "${sendDay}";
    // 自定义函件编号
    private static final String LETTER_NO = "${no}";
    private static final Map<String, String> TEMPLATE_VAR_MAP;

    static {
        TEMPLATE_VAR_MAP = new HashMap<>();
        TEMPLATE_VAR_MAP.put("name", "${name}");
        TEMPLATE_VAR_MAP.put("idCard", "${idCard}");
        TEMPLATE_VAR_MAP.put("amount", "${amount}");
        TEMPLATE_VAR_MAP.put("payAmount", "${payAmount}");
        TEMPLATE_VAR_MAP.put("entrustEndTime", "${entrustEndTime}");
        TEMPLATE_VAR_MAP.put("overdueDays", "${overdueDays}");
        TEMPLATE_VAR_MAP.put("orgDeltName", "${orgDeltName}");
        TEMPLATE_VAR_MAP.put("productName", "${productName}");
        TEMPLATE_VAR_MAP.put("overdueDate", "${overdueDate}");
        TEMPLATE_VAR_MAP.put("ownMobile", "${ownMobile}");
    }

    private static final String ASYNC_PREFIX = "async-";
    private ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("letter-pool-%d").build();
    private ExecutorService fixedThreadPool = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors()*2,
            Runtime.getRuntime().availableProcessors() * 40,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(Runtime.getRuntime().availableProcessors() * 20),
            namedThreadFactory);

    /**
     * 批量添加函件，这个方法供案件添加函件调用
     * @param letters
     */
    @Transactional(rollbackFor = Exception.class)
    public void addLetterBatch(List<Letter> letters) {
        if (CollectionUtils.isEmpty(letters)) {
            return;
        }
        letters.forEach(letter -> {
            letter.setCreateTime(new Date());
            letter.setUpdateTime(new Date());
            letter.setIsCreate(LetterConstant.IS_NOT_CREATE);
            letter.setStatus(LetterConstant.LETTER_STATUS_NEW);
            letter.setIsDelete(LetterConstant.IS_NOT_DEL);
        });
        letterMapper.insertList(letters);
    }


    /**
     * 获取函件相关列表
     * 按照现在列表有：创建函件、待发函列表、发函文件列表。其三者的展示字段几乎一样，创建函件展示未生成函件文件的数据，待发函列表展示的是
     * 生成函件文件但是还没有发送的数据，发函文件列表是指生成函件文件的数据，删除的在这里也能看到。
     * @param param
     * @return
     */
    public PageOutput<LetterVO> getList(LetterParam param, UserSession userSession) {
        Example example = handleQueryParam(param, userSession);
        PageParam pageParam = new PageParam(param.getPage(), param.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<Letter> letterList = letterMapper.selectByExample(example);
        // 如果是待发函列表，检查短信发送状态是否是度言执行失败，因为度言执行失败不会回调短信记录明细
        if (Objects.equals(param.getIsCreate(), LetterConstant.IS_CREATE) &&
                Objects.equals(param.getIsFile(), 0)) {
            checkSmsSendStatus(letterList);
        }
        List<LetterVO> letterVOList = toLetterVOList(letterList);
        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), letterVOList);
    }


    public  Boolean checkExist(LetterParam param, UserSession userSession) {
        Example example = handleQueryParam(param, userSession);
        PageParam pageParam = new PageParam(1, 1);
        PageUtils.setPage(pageParam);
        List<Letter> letterList = letterMapper.selectByExample(example);
        return !CollectionUtils.isEmpty(letterList);
    }




    /**
     * 生成函件文件：本质上接收生成pdf回填更新函件信息，生成函件肯定是在未发函列表操作的，
     * 根据选择的模板获取到对应的模板信息和印章信息，将案件数据进行回填，生成pdf
     * @param param
     */
    public void createLetterFile(LetterParam param) throws Exception{
        if (param.getAllSelect()) {
            asyncCreateLetterFile(param);
            return;
        }
        UserSession userSession = UserUtils.getTokenUser();
        List<Long> letterIds = param.getLetterIds();
        if (CollectionUtils.isEmpty(letterIds)) {
            throw new ApiException("未选择任何函件信息");
        }
        //1.查询函件信息
        List<Letter> letterList = getLetterByIds(letterIds);
        letterList.forEach(letter -> {
            if (Objects.equals(letter.getIsCreate(), LetterConstant.IS_CREATE)) {
                throw new ApiException("包含有已生成的函件数据，不支持再次生成，请重新操作");
            }
        });
        List<Long> caseIds = letterList.stream().map(Letter::getCaseId).collect(Collectors.toList());
        //2.生成pdf，并更新相关函件信息
        int failSize = 0;
        int successSize = 0;
        String desc = "";
        for (Letter letter : letterList) {
            param.setLetterId(letter.getId());
            try {
                createPDF(param,userSession.getLanguage(), userSession.getOrgId());
                successSize++;
            } catch (SpaceLessException e) {
                failSize++;
                desc = "剩余可用存储空间不足，请清理存储文件或续费后再操作";
                log.error("创建函件时出现异常",e);
            }
        }
        // 3.添加对应的任务记录，方便根据任务id查询任务操作的案件信息
        Integer status = AsyncTaskEnums.Status.SUCCESS.getCode();
        if (failSize == letterIds.size()) {
            status = AsyncTaskEnums.Status.FAIL.getCode();
        }
        Long taskId = asyncTaskService.createLetterFileTask(param, userSession, (long) letterIds.size(), status, successSize, failSize, desc);
        // 4.更新案件和记录操作日志
        updateCaseAndLog(caseIds, userSession, taskId);

    }

    void asyncCreateLetterFile(LetterParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        param.setOrgId(userSession.getOrgId());
        // 创建异步任务任务之前判断3分钟之前是否有相同操作
        String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(param).getBytes());
        String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
        if (StringUtils.isNotBlank(s)) {
            throw new ApiException("当前已有相同任务进行中，请稍后操作");
        } else {
            stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
        }
        List<LetterVO> letterList = getListNoPage(param, userSession);
        if (CollectionUtils.isEmpty(letterList)) {
            throw new ApiException("批量生成函件，函件列表为空");
        }
        letterList.forEach(letter -> {
            if (Objects.equals(letter.getIsCreate(), LetterConstant.IS_CREATE)) {
                throw new ApiException("包含有已生成的函件数据，不支持再次生成，请重新操作");
            }
        });
        // 使用线程池异步创建任务：这里不在使用后台起一个线程睡眠监听的方式
        Long taskId = asyncTaskService.createLetterFileTask(param, userSession, 0L, AsyncTaskEnums.Status.ING.getCode(), 0, 0, null);
        fixedThreadPool.submit(()->{
            try {
                List<Long> caseIds = letterList.stream().map(LetterVO::getCaseId).collect(Collectors.toList());
                //生成pdf，并更新相关函件信息
                Integer successSize = 0;
                Integer failSize = 0;
                String desc = "";
                for (LetterVO letterVO : letterList) {
                    param.setLetterId(letterVO.getId());
                    try {
                        createPDF(param,userSession.getLanguage(), userSession.getOrgId());
                        successSize++;
                    } catch (SpaceLessException e) {
                        failSize++;
                        desc = "剩余可用存储空间不足，请清理存储文件或续费后再操作";
                        log.error("创建函件出现异常",e);
                    }
                }

                // 更新任务进信息
                AsyncTask task = new AsyncTask();
                task.setId(taskId);
                if(failSize == letterList.size()) {
                    task.setStatus(AsyncTaskEnums.Status.FAIL.getCode());
                } else {
                    task.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
                }
                task.setTotal((long) letterList.size());
                task.setIgnoreCount(failSize.longValue());
                task.setSuccessAmt(successSize.longValue());
                task.setDesc(desc);
                asyncTaskService.updateByPrimaryKeySelective(task);
                // 更新案件和记录操作日志
                updateCaseAndLog(caseIds, userSession, taskId);
            } catch (Exception e) {
                log.error("生成函件文件,taskId:{},失败！", taskId, e);
                AsyncTask task = asyncTaskService.selectByPrimaryKey(taskId);
                asyncTaskService.updateTaskFinish(task, e.getMessage());
            }
        });
    }


    /**
     *移除函件，其实现逻辑和生成函件差不多
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void delLetter(LetterParam param) {
        if (param.getAllSelect()) {
            asyncDelLetter(param);
            return;
        }
        UserSession userSession = UserUtils.getTokenUser();
        List<Long> letterIds = param.getLetterIds();
        if (CollectionUtils.isEmpty(letterIds)) {
            throw new ApiException("未选择任何函件信息");
        }
        // 1.查询函件信息
        List<Letter> letterList = getLetterByIds(letterIds);
        List<Long> caseIds = letterList.stream().map(Letter::getCaseId).collect(Collectors.toList());
        // 2.移除函件
        removeLetter(param, userSession);
        // 3.添加对应的任务记录，方便根据任务id查询任务操作的案件信息
        Long taskId = asyncTaskService.delLetterTask(
                param,
                userSession,
                (long)letterIds.size(),
                AsyncTaskEnums.Status.SUCCESS.getCode());
        // 4.更新案件和记录操作日志
        updateCaseAndLog(caseIds, userSession, taskId);
    }

    void asyncDelLetter(LetterParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        param.setOrgId(userSession.getOrgId());
        // 创建异步任务任务之前判断3分钟之前是否有相同操作
        String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(param).getBytes());
        String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
        if (StringUtils.isNotBlank(s)) {
            throw new ApiException("当前已有相同任务进行中，请稍后操作");
        } else {
            stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
        }
        // 使用线程池异步创建任务：这里不在使用后台起一个线程睡眠监听的方式
        Long taskId = asyncTaskService.delLetterTask(param, userSession, 0L, AsyncTaskEnums.Status.ING.getCode());
        fixedThreadPool.submit(()->{
            try {
                List<LetterVO> letterList = getListNoPage(param, userSession);
                if (CollectionUtils.isEmpty(letterList)) {
                    throw new ApiException("批量移除函件，函件列表为空");
                }
                List<Long> caseIds = letterList.stream().map(LetterVO::getCaseId).collect(Collectors.toList());
                Integer update = removeLetter(param, userSession);
                AsyncTask task = new AsyncTask();
                task.setId(taskId);
                task.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
                task.setTotal((long) letterList.size());
                task.setSuccessAmt((long) letterList.size());
                asyncTaskService.updateByPrimaryKeySelective(task);

                // 更新案件和记录操作日志
                updateCaseAndLog(caseIds, userSession, taskId);
            } catch (Exception e) {
                log.error("移除函件,taskId:{},失败！", taskId, e);
                AsyncTask task = asyncTaskService.selectByPrimaryKey(taskId);
                asyncTaskService.updateTaskFinish(task, e.getMessage());
            }
        });
    }

    /**
     * 发函操作
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendLetter(LetterParam param) {
        if (param.getAllSelect()) {
            asyncSendLetter(param);
            return;
        }
        UserSession userSession = UserUtils.getTokenUser();
        List<Long> letterIds = param.getLetterIds();
        if (CollectionUtils.isEmpty(letterIds)) {
            throw new ApiException("未选择任何函件信息");
        }
        //1.查询函件信息
        List<Letter> letterList = getLetterByIds(letterIds);
        letterList.forEach(letter -> {
            if (!Objects.equals(letter.getIsCreate(), LetterConstant.IS_CREATE)) {
                throw new ApiException("存在未生成函件的数据，请先生成函件再操作");
            }
            if (Objects.equals(letter.getStatus(), LetterConstant.LETTER_STATUS_SENDING)) {
                throw new ApiException("包含发送中函件，请重新筛选状态或完成发送后再执行");
            }
        });
        Integer sendType = param.getSendType();
        EmailTemplate emailTemplate = null;
        // 如果发送是邮箱或者短信加邮箱，按要求先按照公司保存邮箱模板信息，如果公司已存在模板就更新
        if (Objects.equals(sendType, LetterConstant.SEND_TYPE_EMAIL) || Objects.equals(sendType, LetterConstant.SEND_TYPE_SMS_EMAIL)) {
            if (param.getEmailTemplate() == null) {
                throw new ApiException("邮箱模板信息不能为空");
            }
            emailTemplate = emailTemplateService.saveEmailTemplate(userSession.getOrgId(), param.getEmailTemplate());
        }

        List<Long> caseIds = letterList.stream().map(Letter::getCaseId).collect(Collectors.toList());
        List<Case> caseList = caseService.getCaseListByCaseIds(caseIds);
        // 2.更新函件发送人，发送方式 状态为发送中,
        updateLetter(letterIds, userSession.getId(), LetterConstant.LETTER_STATUS_SENDING, sendType);;
        // 线下邮寄
        if (Objects.equals(sendType, LetterConstant.SEND_TYPE_POST)) {
            sendPost(letterIds, caseIds, param, userSession);
        }
        // 邮箱发送操作
        if (Objects.equals(sendType, LetterConstant.SEND_TYPE_EMAIL)) {
            sendEmail(letterList, caseList, emailTemplate, param ,userSession, sendType);
        }
        // 短信发送操作
        if (Objects.equals(sendType, LetterConstant.SEND_TYPE_SMS)) {
            sendSms(letterList, caseList, userSession, param);
        }
        // 短信+邮箱发送操作
        if (Objects.equals(sendType, LetterConstant.SEND_TYPE_SMS_EMAIL)) {
            sendSms(letterList, caseList, userSession, param);
            sendEmail(letterList, caseList, emailTemplate, param ,userSession, sendType);

        }

    }

    void asyncSendLetter(LetterParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        param.setOrgId(userSession.getOrgId());
        // 创建异步任务任务之前判断3分钟之前是否有相同操作
        String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(param).getBytes());
        String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
        if (StringUtils.isNotBlank(s)) {
            throw new ApiException("当前已有相同任务进行中，请稍后操作");
        } else {
            stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
        }
        List<LetterVO> letterVOList = getListNoPage(param, userSession);
        List<Letter> letterList = new ArrayList<>();
        letterVOList.forEach(letterVO -> {
            Letter letter = AuthBeanUtils.copy(letterVO, Letter.class);
            letterList.add(letter);
        });
        if (CollectionUtils.isEmpty(letterList)) {
            throw new ApiException("批量发送函件，函件列表为空");
        }
        letterList.forEach(letter -> {
            if (!Objects.equals(letter.getIsCreate(), LetterConstant.IS_CREATE)) {
                throw new ApiException("存在未生成函件的数据，请先生成函件再操作");
            }
            if (Objects.equals(letter.getStatus(), LetterConstant.LETTER_STATUS_SENDING)) {
                throw new ApiException("包含发送中函件，请重新筛选状态或完成发送后再执行");
            }
        });
        Integer sendType = param.getSendType();
        EmailTemplate emailTemplate = null;
        // 如果发送是邮箱或者短信加邮箱，按要求先按照公司保存邮箱模板信息，如果公司已存在模板就更新
        if (Objects.equals(sendType, LetterConstant.SEND_TYPE_EMAIL) || Objects.equals(sendType, LetterConstant.SEND_TYPE_SMS_EMAIL)) {
            if (param.getEmailTemplate() == null) {
                throw new ApiException("邮箱模板信息不能为空");
            }
            emailTemplate = emailTemplateService.saveEmailTemplate(userSession.getOrgId(), param.getEmailTemplate());
        }
        // 使用线程池异步创建任务：这里不在使用后台起一个线程睡眠监听的方式
        EmailTemplate finalEmailTemplate = emailTemplate;
        fixedThreadPool.submit(()->{
            try {
                List<Long> letterIds = letterList.stream().map(Letter::getId).collect(Collectors.toList());
                List<Long> caseIds = letterList.stream().map(Letter::getCaseId).collect(Collectors.toList());
                List<Case> caseList = caseService.getCaseListByCaseIds(caseIds);
                Integer update = updateLetter(letterIds, userSession.getId(), LetterConstant.LETTER_STATUS_SENDING, param.getSendType());

                // 线下邮寄
                if (Objects.equals(sendType, LetterConstant.SEND_TYPE_POST)) {
                    sendPost(letterIds, caseIds, param, userSession);
                }
                // 邮箱+短信发送操作
                if (Objects.equals(sendType, LetterConstant.SEND_TYPE_EMAIL)
                        || Objects.equals(sendType, LetterConstant.SEND_TYPE_SMS_EMAIL)) {
                    sendEmail(letterList, caseList, finalEmailTemplate, param ,userSession, sendType);
                }
                if (Objects.equals(sendType, LetterConstant.SEND_TYPE_SMS)
                        || Objects.equals(sendType, LetterConstant.SEND_TYPE_SMS_EMAIL)) {
                    sendSms(letterList, caseList, userSession, param);
                }
            } catch (Exception e) {
                log.error("异步发函操作失败！", e);

            }
        });
    }

    public void sendPost(List<Long> letterIds, List<Long> caseIds, LetterParam param, UserSession userSession) {
        updateLetter(letterIds, LetterConstant.LETTER_STATUS_IS_SEND, null);
        int total = letterIds.size();
        int failCount = 0;
        int successCount = total;
        // 添加对应的任务记录，方便根据任务id查询任务操作的案件信息
        Long taskId = asyncTaskService.updateLetterTask(param, userSession, total, AsyncTaskEnums.Status.SUCCESS.getCode(),
                successCount, failCount);
        // 更新案件和记录操作日志
        updateCaseAndLog(caseIds, userSession, taskId);

        List<LetterSendRecord> letterSendRecords = new ArrayList<>();
        letterIds.forEach(id -> {
            LetterSendRecord record = new LetterSendRecord();
            record.setLetterId(id);
            record.setOrgId(userSession.getOrgId());
            record.setTaskId(taskId);
            record.setStatus(LetterConstant.IS_SUCCESS);
            record.setSendType(LetterConstant.SEND_TYPE_POST);
            record.setSendTime(new Date());
            record.setUpdateTime(new Date());
            record.setCreateTime(new Date());
            letterSendRecords.add(record);
        });
        if (!CollectionUtils.isEmpty(letterSendRecords)) {
            letterSendRecordMapper.insertList(letterSendRecords);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateLetterById(Long id, LetterParam param) {
        Letter letter = AuthBeanUtils.copy(param, Letter.class);
        letter.setId(id);
        letter.setUpdateTime(new Date());
        letterMapper.updateByPrimaryKeySelective(letter);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateLetter(Letter letter) {
        letter.setUpdateTime(new Date());
        letterMapper.updateByPrimaryKeySelective(letter);
    }


    @Transactional(rollbackFor = Exception.class)
    public void exportLetter(LetterParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        param.setOrgId(userSession.getOrgId());
        Integer exportType = param.getExportType();
        DownloadTask task = downloadTaskService.createTask(
                null,
                userSession,
                exportType.equals(LetterConstant.EXPORT_TYPE_IS_CREATE) ? DownloadTask.Type.LETTER_FILE_EXPORT.getCode() : DownloadTask.Type.CASE_LETTER_EXPORT.getCode(),
                JSONObject.toJSONString(param));
        Long taskId = task.getId();
        Boolean exist;
        if (Objects.equals(exportType, LetterConstant.EXPORT_TYPE_IS_SEND)) {
            exist = letterSendRecordService.checkExist(param, userSession);
        } else {
            exist = checkExist(param, userSession);
        }
        if(!exist){
            throw new ApiException("查询条件无匹配结果，请重新操作");
        }

        fixedThreadPool.submit(() -> {
            try {
                downloadTaskService.updateTaskProgress(taskId, new BigDecimal(25), DownloadTask.Status.PROCESSING.getCode());
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                String fileNamePrefix = taskId + "-" + sdf.format(new Date());
                String fileName = fileNamePrefix + ".zip";
                ZipFile zipFile = new ZipFile(fileName);
                param.setLimit(500);
                param.setPage(1);
                PageOutput<LetterVO> pageOutput = new PageOutput<>();
                if (Objects.equals(exportType, LetterConstant.EXPORT_TYPE_IS_SEND)) {
                    pageOutput = letterSendRecordService.getList(param, userSession);

                } else {
                    pageOutput = getList(param, userSession);
                }
                List<LetterVO> list = pageOutput.getList();
                int pages = pageOutput.getPages();
                int total = pageOutput.getTotal();
                downloadLetterFile(list, zipFile);
                downloadTaskService.updateTaskProgress(taskId, new BigDecimal(50), null);
                List<List<String>> dataList = handleLetterListData(list, exportType, false,userSession.getLanguage());
                if (pages > 1) {
                    for (int i = 2; i <= pages; i++) {
                        param.setLimit(500);
                        param.setPage(i);
                        PageOutput<LetterVO> output = new PageOutput<>();
                        if (Objects.equals(exportType, LetterConstant.EXPORT_TYPE_IS_SEND)) {
                            output = letterSendRecordService.getList(param, userSession);

                        } else {
                            output = getList(param, userSession);
                        }
                        List<LetterVO> letterList = output.getList();
                        List<List<String>> lists = handleLetterListData(letterList, exportType, true,userSession.getLanguage());
                        if (!CollectionUtils.isEmpty(lists)) {
                            dataList.addAll(lists);
                        }
                        downloadLetterFile(letterList, zipFile);
                    }
                }
                //生成excel文件
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                EasyExcel.write(byteArrayOutputStream).sheet().doWrite(dataList);
                File excelFile = new File(fileNamePrefix + ".xlsx");
                FileUtils.writeByteArrayToFile(excelFile, byteArrayOutputStream.toByteArray());
                zipFile.addFile(excelFile);
                byteArrayOutputStream.close();
                FileUtils.deleteQuietly(excelFile);
                downloadTaskService.updateTaskProgress(taskId, new BigDecimal(75), DownloadTask.Status.PROCESSING.getCode());
                String url = "";
                Long fileSize = zipFile.getFile().length() / 1024;
                Date expireDate = DateUtils.addDays(new Date(), 3);
                FileInputStream inputStream = new FileInputStream(zipFile.getFile());
                UploadCreatedFileStream uploadCreatedFileStream = new UploadCreatedFileStream();
                uploadCreatedFileStream.setInputStream(inputStream)
                        .setFileName(fileName)
                        .setExpireDate(expireDate)
                        .setBucket(systemConfig.getCaseFilesBucket())
                        .setLocalUrl(systemConfig.getLetterPath() + fileName);
                FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
                url = fileStorageStrategy.uploadCreatedFileByStream(uploadCreatedFileStream);
                inputStream.close();
                FileUtils.deleteQuietly(zipFile.getFile());
                DownloadTask updateTask = new DownloadTask();
                updateTask.setId(taskId);
                updateTask.setDataNums((long)total);
                updateTask.setFileSize(fileSize);
                updateTask.setProgress(new BigDecimal(100));
                updateTask.setUpdateTime(new Date());
                updateTask.setDownloadUrl(url);
                updateTask.setStatus(DownloadTask.Status.COMPLETE.getCode());
                updateTask.setExpireTime(expireDate);
                downloadTaskService.updateByPrimaryKeySelective(updateTask);
            } catch (Exception e) {
                log.error("函件导出错误：", e);
                DownloadTask updateTask = new DownloadTask();
                updateTask.setId(taskId);
                updateTask.setUpdateTime(new Date());
                updateTask.setStatus(DownloadTask.Status.FAILED.getCode());
                updateTask.setFailedReason("函件导出错误");
                downloadTaskService.updateByPrimaryKeySelective(updateTask);
            }

        });
    }

    void downloadLetterFile(List<LetterVO> list, ZipFile zipFile) throws IOException {
        FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
        for(LetterVO letterVO : list) {
            if (StringUtils.isNotBlank(letterVO.getUrl())) {
                InputStream fileInputStream;
                ReadFileInfo readFileInfo = new ReadFileInfo();
                readFileInfo.setAlias(letterVO.getAlias())
                        .setUrl("/usr/local/duyansoft" + letterVO.getUrl())
                        .setBucket(systemConfig.getCaseFilesBucket());
                fileInputStream = fileStorageStrategy.readFileStream(readFileInfo);
                StringBuilder newPdfName = new StringBuilder();
                newPdfName.append(letterVO.getName()).append("-");
                if (Objects.equals(letterVO.getType(), LetterConstant.LETTER_TYPE_LAWYER)) {
                    newPdfName.append("律师函-");
                }
                newPdfName.append(letterVO.getId()).append(".pdf");
                File file = new File(newPdfName.toString());
                FileUtils.copyInputStreamToFile(fileInputStream, file);
                zipFile.addFile(file);
                FileUtils.deleteQuietly(file);
                fileInputStream.close();
            }
        }
    }

    List<List<String>> handleLetterListData(List<LetterVO> list, Integer exportType, Boolean isHaveHead,String language) {
        List<List<String>> dataList = new ArrayList<>();
        if (!isHaveHead) {
            if (Objects.equals(exportType, LetterConstant.EXPORT_TYPE_NO_CREATE)
                    || Objects.equals(exportType, LetterConstant.EXPORT_TYPE_NO_SEND)) {
                String head = I18nService.getMessage("letter.handleLetterListData.exportTypeNoSend",language, null);
                dataList.add(Arrays.asList(head.split(",")));
            }
            if (Objects.equals(exportType, LetterConstant.EXPORT_TYPE_IS_SEND)) {
                String head = I18nService.getMessage("letter.handleLetterListData.exportTypeIsSend",language, null);
                dataList.add(Arrays.asList(head.split(",")));
            }
            if (Objects.equals(exportType, LetterConstant.EXPORT_TYPE_IS_CREATE)) {
                String head = I18nService.getMessage("letter.handleLetterListData.exportTypeIsCreate",language, null);
                dataList.add(Arrays.asList(head.split(",")));
            }
        }
        if (Objects.equals(exportType, LetterConstant.EXPORT_TYPE_NO_CREATE)
                || Objects.equals(exportType, LetterConstant.EXPORT_TYPE_NO_SEND)) {
            list.forEach(letterVO -> {
                String address = null;
                if (!CollectionUtils.isEmpty(letterVO.getAddressList())) {
                    List<CaseAddressVO> caseAddressList = letterVO.getAddressList();
                    List<String> addressList = caseAddressList.stream().map(CaseAddressVO::getAddress).collect(Collectors.toList());
                    address = String.join("; ", addressList);
                }
                String letterType = null;
                if (Objects.equals(letterVO.getType(), LetterConstant.LETTER_TYPE_LAWYER)) {
                    letterType = "律师函";
                }
                List<String> data = Lists.newArrayList(letterVO.getId().toString(), letterVO.getOutSerialNo(), letterVO.getName(), letterVO.getOwnMobile(), letterVO.getIdCard(),
                        letterVO.getOutBatchNo(), letterVO.getInnerBatchNo(), letterVO.getOrgDeltName(), letterVO.getProductName(), address, letterType, letterVO.getEmail());
                dataList.add(data);
            });
        }
        if (Objects.equals(exportType, LetterConstant.EXPORT_TYPE_IS_SEND)) {
            list.forEach(letterVO -> {
                String address = null;
                if (!CollectionUtils.isEmpty(letterVO.getAddressList())) {
                    List<CaseAddressVO> caseAddressList = letterVO.getAddressList();
                    List<String> addressList = caseAddressList.stream().map(CaseAddressVO::getAddress).collect(Collectors.toList());
                    address = String.join("; ", addressList);
                }
                String letterType = null;
                if (Objects.equals(letterVO.getType(), LetterConstant.LETTER_TYPE_LAWYER)) {
                    letterType = "律师函";
                }
                String sendType = null;
                if (Objects.equals(letterVO.getSendType(), LetterConstant.SEND_TYPE_POST)) {
                    sendType = "打印邮寄";
                }
                if (Objects.equals(letterVO.getSendType(), LetterConstant.SEND_TYPE_SMS)) {
                    sendType = "短信发送";
                }
                if (Objects.equals(letterVO.getSendType(), LetterConstant.SEND_TYPE_EMAIL)) {
                    sendType = "邮箱发送";
                }
                List<String> data = Lists.newArrayList(letterVO.getId().toString(), letterVO.getOutSerialNo(), letterVO.getName(), letterVO.getOwnMobile(), letterVO.getIdCard(),
                        letterVO.getOutBatchNo(), letterVO.getInnerBatchNo(), letterVO.getOrgDeltName(), letterVO.getProductName(), address, letterVO.getEmail(), letterType, sendType, letterVO.getSenderName());
                dataList.add(data);
            });
        }
        if (Objects.equals(exportType, LetterConstant.EXPORT_TYPE_IS_CREATE)) {
            list.forEach(letterVO -> {
                String address = null;
                if (!CollectionUtils.isEmpty(letterVO.getAddressList())) {
                    List<CaseAddressVO> caseAddressList = letterVO.getAddressList();
                    List<String> addressList = caseAddressList.stream().map(CaseAddressVO::getAddress).collect(Collectors.toList());
                    address = String.join("; ", addressList);
                }
                String letterType = null;
                if (Objects.equals(letterVO.getType(), LetterConstant.LETTER_TYPE_LAWYER)) {
                    letterType = "律师函";
                }
                String sendType = null;
                if (Objects.equals(letterVO.getSendType(), LetterConstant.SEND_TYPE_POST)) {
                    sendType = "打印邮寄";
                }
                if (Objects.equals(letterVO.getSendType(), LetterConstant.SEND_TYPE_POST)) {
                    sendType = "打印邮寄";
                }
                if (Objects.equals(letterVO.getSendType(), LetterConstant.SEND_TYPE_SMS)) {
                    sendType = "短信发送";
                }
                if (Objects.equals(letterVO.getSendType(), LetterConstant.SEND_TYPE_EMAIL)) {
                    sendType = "邮箱发送";
                }
                List<String> data = Lists.newArrayList(letterVO.getId().toString(), letterVO.getOutSerialNo(), letterVO.getName(), letterVO.getOwnMobile(), letterVO.getIdCard(),
                        letterVO.getOutBatchNo(), letterVO.getInnerBatchNo(), letterVO.getOrgDeltName(), letterVO.getProductName(), address, letterVO.getEmail(), letterType, sendType, letterVO.getTemplateName());
                dataList.add(data);
            });
        }
        return dataList;
    }


    List<Letter> getLetterByIds(List<Long> letterIds) {
        if (CollectionUtils.isEmpty(letterIds)) {
            return new ArrayList<>();
        }
        Example example = new Example(Letter.class);
        example.and().andIn("id", letterIds);
        List<Letter> letters = letterMapper.selectByExample(example);
        return letters;
    }

    public List<LetterVO> getListNoPage(LetterParam param, UserSession userSession) {
        Example example = handleQueryParam(param, userSession);
        List<Letter> letterList = letterMapper.selectByExample(example);
        List<LetterVO> letterVOList = new ArrayList<>();
        List<Long> caseIds = letterList.stream().map(Letter::getCaseId).collect(Collectors.toList());
        List<CaseAddress> caseAddressList = caseAddressService.getCaseAddressByCaseIds(caseIds);
        letterList.forEach(letter -> {
            LetterVO letterVO = AuthBeanUtils.copy(letter, LetterVO.class);
            List<CaseAddress> addresses = caseAddressList.parallelStream().filter(caseAddress -> Objects.equals(caseAddress.getCaseId(), letter.getCaseId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(addresses)) {
                letterVO.setAddressCount(addresses.size());
                List<CaseAddressVO> caseAddressVOList = addresses.stream().map(caseAddress -> AuthBeanUtils.copy(caseAddress, CaseAddressVO.class)).collect(Collectors.toList());
                letterVO.setAddressList(caseAddressVOList);
            }
            letterVOList.add(letterVO);
        });
        return letterVOList;
    }

    Integer removeLetter(LetterParam param, UserSession userSession) {
        Example example = handleQueryParam(param, userSession);
        Letter letter = new Letter();
        letter.setIsDelete(LetterConstant.IS_DEL);
        letter.setUpdateTime(new Date());
        int update = letterMapper.updateByExampleSelective(letter, example);
        return update;
    }

    Integer updateLetter(List<Long> letterIds, Long userId, Integer status, Integer sendType) {
        if (CollectionUtils.isEmpty(letterIds)) {
            return null;
        }
        Example example = new Example(Letter.class);
        example.and().andIn("id", letterIds);
        Letter letter = new Letter();
        letter.setStatus(status);
        letter.setSendType(sendType);
        letter.setSender(userId);
        letter.setSendTime(new Date());
        letter.setUpdateTime(new Date());
        int update = letterMapper.updateByExampleSelective(letter, example);
        return update;
    }

    Integer updateLetter(List<Long> letterIds, Integer status, String reason) {
        if (CollectionUtils.isEmpty(letterIds)) {
            return null;
        }
        Example example = new Example(Letter.class);
        example.and().andIn("id", letterIds);
        Letter letter = new Letter();
        letter.setStatus(status);
        letter.setReason(reason);
        letter.setUpdateTime(new Date());
        int update = letterMapper.updateByExampleSelective(letter, example);
        return update;
    }

    /**
     * 创建pdf
     *
     * @param param
     * @param language
     */
    @Transactional(rollbackFor = Exception.class)
    void createPDF(LetterParam param, String language, Long orgId) throws Exception{
        Long letterId = param.getLetterId();
        Long templateId = param.getTemplateId();
        LetterTemplate letterTemplate = letterTemplateService.selectByPrimaryKey(templateId);
        if(letterTemplate == null) {
            throw new ApiException("编号为{}的模板不存在", templateId);
        }
        // 根据模板编号获取对应的印章信息
        List<LetterSignerVO> letterSignerImage = letterSignerService.getLetterSignerImage(templateId);
        if (CollectionUtils.isEmpty(letterSignerImage)) {
            log.error("模板编号为{}不存在签署方", templateId);
            throw new ApiException("该模版不存在签署方，请修改模版");
        }
        // 查找函件信息
        Letter letter = letterMapper.selectByPrimaryKey(letterId);
        Long caseId = letter.getCaseId();
        CaseDTO caseInfo = caseService.getCaseForLetter(caseId);
        String content = letterTemplate.getContent();
        // 判断模板内容中是否使用函件模板编号
        Long letterNo = null;
        if (StringUtils.isNotBlank(letterTemplate.getContent()) && letterTemplate.getContent().contains(LETTER_NO)) {
            letterNo = addLetterNo(letterTemplate);
        }
        InputStream inputStream = null;
        try {
            // 插入自定义函件模板编号
            String finalContent = handleContent(content, caseInfo, letterNo);
            finalContent = handleContentCustomField(letter.getOrgId(), finalContent, caseInfo.getFieldJson(), language);
            String shortFileName = caseInfo.getName() + "-" + letterTemplate.getName() + "-" + letter.getId() + ".pdf";
            List<SignerSealMatchParam> signerSealMatchParams = BeanUtil.copyPropertiesFromList(letterSignerImage, SignerSealMatchParam.class);
            if (StringUtils.equals(systemConfig.getChannelCode(), FileStorageEnums.Type.LOCAL.getCode())) {
                signerSealMatchParams.forEach(signerSeal -> {
                    signerSeal.setImage("/usr/local/duyansoft" + signerSeal.getImage());
                });
            }
            inputStream = PdfUtils.createPDF1(finalContent, shortFileName, signerSealMatchParams);
            // 在bucket下新建文件夹需要在fileName前拼上文件夹路径，letter/orgId/yyyy-MM/xxx
            String fileName = "letter/" + orgId + "/"
                    + DateUtils.formatDate(new Date(), DateUtils.YEAR_MONTH_DATE_FORMAT) + "/"
                    + shortFileName;
            Date expireDate = DateUtils.addYears(new Date(), 10);

            MultipartFile file = new MockMultipartFile("file", fileName, "text/plain", inputStream);
            BigDecimal fileSize =
                    BigDecimal.valueOf(file.getSize())
                            .divide(new BigDecimal(1024))
                            .setScale(3, RoundingMode.HALF_UP);
            // 计算空间是否足够
            preCalculateSpace(orgId, fileSize);
            UploadFileInfo uploadFileInfo = new UploadFileInfo();
            uploadFileInfo.setFile(file)
                    .setFileName(fileName)
                    .setExpireDate(expireDate)
                    .setBucket(systemConfig.getCaseFilesBucket())
                    .setLocalUrl(systemConfig.getLetterPath() + fileName);
            FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
            String url = fileStorageStrategy.uploadFile(uploadFileInfo);
            // 重新计算已使用空间、函件存储空间
            orgSpaceService.addUseSpace(orgId, fileSize, SpaceEnums.Type.LETTER_FILE.getCode());
            Letter update = new Letter();
            update.setId(letterId);
            update.setUrl(url);
            update.setFileTime(new Date());
            update.setIsCreate(LetterConstant.IS_CREATE);
            update.setTemplateId(templateId);
            update.setTemplateName(letterTemplate.getName());
            update.setTemplateContent(letterTemplate.getContent());
            update.setType(letterTemplate.getType());
            update.setUpdateTime(new Date());
            String uniqueCode = RandomUtil.generateUniqueCode(letterId, 3);
            update.setUniqueCode(uniqueCode);
            // 存储空间信息
            update.setAlias(fileName);
            update.setShortFileName(shortFileName);
            update.setFileSize(fileSize);
            letterMapper.updateByPrimaryKeySelective(update);
        } catch (Exception e) {
            log.error("生成函件失败：", e);
        }
        finally {
            inputStream.close();
        }
    }

    public void preCalculateSpace(Long orgId, BigDecimal fileSize) {
        if (systemConfig.getLocalDeploy()) {
            return;
        }
        OrgSpace orgSpace = new OrgSpace();
        orgSpace.setOrgId(orgId);
        orgSpace = orgSpaceService.selectFirst(orgSpace);
        BigDecimal useSpace = orgSpace.getUseSpace().add(fileSize);
        if (useSpace.compareTo(orgSpace.getTotalSpace().add(orgSpace.getFreeSpace())) > 0) {
            throw new SpaceLessException("剩余可用存储空间不足，请清理存储文件或续费后再操作");
        }
    }

    public synchronized Long addLetterNo(LetterTemplate letterTemplate) {
        // 每生成一份函件，函件模板自定义编号+1
        Long no = letterTemplate.getNo();
        LetterTemplate newTemplate = new LetterTemplate();
        newTemplate.setNo(no + 1);
        newTemplate.setId(letterTemplate.getId());
        letterTemplateService.updateLetterTemplate(newTemplate);
        return no;
    }

    String handleContent(String content, CaseDTO caseInfo, Long letterNo) throws Exception {
        DecimalFormat df = new DecimalFormat("#.00");
        Class<? extends CaseDTO> c = caseInfo.getClass();
        for(Map.Entry<String, String> entry : TEMPLATE_VAR_MAP.entrySet()) {
            String col = entry.getKey();
            String var = entry.getValue();
            Field field = c.getDeclaredField(col);
            field.setAccessible(true);
            Object value = field.get(caseInfo);
            if (col.toLowerCase().contains("amount")) {
                long money = (long) value;
                if (money == 0L) {
                    value = "0.00";
                } else {
                    value = (double) money / 1000;
                    value = df.format(value);
                }

            }
            if (value instanceof Date) {
                value = DateUtils.formatDate((Date)value);
            }
            // 逾期天数实时计算，根据当前日期减去逾期日期
            if (Objects.equals(col, "overdueDays")) {
                value = calculateOverDays(caseInfo.getOverdueDate(), caseInfo.getOverdueDays(), caseInfo.getOrgId());
            }
            if (value != null) {
                content = content.replace(var, value.toString());
            } else {
                content = content.replace(var, "&nbsp; &nbsp; &nbsp;");
            }

        }
        if(letterNo != null) {
            content = content.replace(LETTER_NO, letterNo.toString());
        }
        content = content.replace(SEND_DAY, DateUtils.formatDate(new Date()));
        return content;
    }

    private Integer calculateOverDays(Date overdueDate, Integer overdueDays,  Long orgId) {
        OrgSwitch orgSwitch = orgSwitchService.selectOrgSwitchByOrgId(orgId);
        Boolean b = ObjectUtil.isNull(orgSwitch)?false:Boolean.valueOf(orgSwitch.getAutoUpdateOverdueDays());
        if (!b) {
            return overdueDays;
        }
        if (overdueDate == null) {
            return null;
        }
        Long days = DateUtils.countDays(overdueDate, new Date());
        if (days < 0) {
            days = 0L;
        }
        return days.intValue();
    }

    private String handleContentCustomField(Long orgId, String content, Map<String, String>fieldJson,String language) {
        if (fieldJson == null || fieldJson.size()==0) {
            return content;
        }
        Pattern pattern = Pattern.compile("#\\{.+?\\}");
        Matcher matcher = pattern.matcher(content);
        List<String> customFields = new ArrayList<>();
        while (matcher.find()) {
            String group = matcher.group();
            if (!customFields.contains(group.substring(2, group.length()-1))) {
                customFields.add(group.substring(2, group.length()-1));
            }
        }
        if (CollectionUtils.isEmpty(customFields)) {
            return content;
        }

        // 处理自定义字段
        Map<String, List<CustomField>> listMap = customFieldService.selectByNames(orgId, customFields);
        for (String field : customFields) {
            if (!listMap.containsKey(field)) {
                continue;
            }
            List<CustomField> customFieldList = listMap.get(field);
            for (CustomField customField : customFieldList) {
                String key = customField.getValue();
                if (fieldJson.containsKey(key)) {
                    String value = fieldJson.get(key);
                    if (StringUtils.isNotBlank(value)) {
                        content = content.replace("#{"+field+"}", value);
                    }
                    break;
                }
            }
        }
        // 处理系统添加的字段
        List<DictionaryEntity> caseBaseFields = i18nService.getListByKey(language,DictionaryConstant.CASE_BASE_FIELDS_KEY);
        for (String field : customFields) {
            for (DictionaryEntity entity : caseBaseFields) {
                if (Objects.equals(entity.getName(), field)) {
                    String key = entity.getValue();
                    if (fieldJson.containsKey(key)) {
                        String value = fieldJson.get(key);
                        if (StringUtils.isNotBlank(value)) {
                            if (StringUtils.isNotBlank(entity.getType()) && "Money".equals(entity.getType())) {
                                BigDecimal val = new BigDecimal(value).divide(new BigDecimal(1000));
                                content = content.replace("#{"+field+"}", val.toString());
                            } else {
                                content = content.replace("#{"+field+"}", value);
                            }
                        }
                        break;
                    }
                }
            }
        }

        List<DictionaryEntity> caseUserBaseFields = i18nService.getListByKey(language,DictionaryConstant.CASE_USER_BASE_FIELDS_KEY);
        for (String field : customFields) {
            for (DictionaryEntity entity : caseUserBaseFields) {
                if (Objects.equals(entity.getName(), field)) {
                    String key = entity.getValue();
                    if (fieldJson.containsKey(key)) {
                        String value = fieldJson.get(key);
                        if (StringUtils.isNotBlank(value)) {
                            if (StringUtils.isNotBlank(entity.getType()) && "Money".equals(entity.getType())) {
                                BigDecimal val = new BigDecimal(value).divide(new BigDecimal(1000));
                                content = content.replace("#{"+field+"}", val.toString());
                            } else {
                                content = content.replace("#{"+field+"}", value);
                            }
                        }
                        break;
                    }
                }
            }
        }
        List<DictionaryEntity> caseUpdateFields = i18nService.getListByKey(language,DictionaryConstant.CASE_UPDATE_FIELDS_KEY);
        for (String field : customFields) {
            for (DictionaryEntity entity : caseUpdateFields) {
                if (Objects.equals(entity.getName(), field)) {
                    String key = entity.getValue();
                    if (fieldJson.containsKey(key)) {
                        String value = fieldJson.get(key);
                        if (StringUtils.isNotBlank(value)) {
                            if (StringUtils.isNotBlank(entity.getType()) && "Money".equals(entity.getType())) {
                                BigDecimal val = new BigDecimal(value).divide(new BigDecimal(1000));
                                content = content.replace("#{"+field+"}", val.toString());
                            } else {
                                content = content.replace("#{"+field+"}", value);
                            }
                        }
                        break;
                    }
                }
            }
        }


        return content;
    }


    public void sendEmail(List<Letter> letterList, List<Case> caseList, EmailTemplate emailTemplate, LetterParam param, UserSession userSession, Integer sendType) {
       boolean flag = true;
       // 如果是邮箱和短信一起发送，那么邮箱发送成功与否不需要回填函件表，因为函件状态取决于短信操作
        if (Objects.equals(sendType, LetterConstant.SEND_TYPE_SMS_EMAIL)) {
            flag = false;
        }
        // 设置参数发送方式为邮箱，保存在操作记录task的json字段中，后续操作列表反序列化得到发送方式在列表中展示
        param.setSendType(LetterConstant.SEND_TYPE_EMAIL);
        Map<Long, String> failMap = new HashMap<>();
        List<Long> orgDeltIds = caseList.stream().filter(aCase -> aCase.getOrgDeltId() != null).map(Case::getOrgDeltId).collect(Collectors.toList());
        Map<Long, String> deltMap = deltService.getDeltMap(orgDeltIds);
        List<Long> caseIds = caseList.stream().map(Case::getId).collect(Collectors.toList());
        int total = letterList.size();
        int failCount = 0;
        int successCount = 0;
        List<Letter> letters = new ArrayList<>();
        // 检验函件数据和发送邮件
        letterList.forEach(letter -> {
            Case caseTemp = caseList.stream().filter(aCase -> Objects.equals(aCase.getId(), letter.getCaseId())).findFirst().orElse(null);
            if (Objects.equals(caseTemp.getCaseStatus(), CaseEnums.CaseStatus.FINISH.getCode())) {
                failMap.put(letter.getId(), "案件状态为结案");
                return;
            }
            if (Objects.equals(caseTemp.getCaseStatus(), CaseEnums.CaseStatus.STOP.getCode())) {
                failMap.put(letter.getId(), "案件状态为停催");
                return;
            }
            if (Objects.equals(caseTemp.getCaseStatus(), CaseEnums.CaseStatus.INVALID.getCode())) {
                failMap.put(letter.getId(), "案件状态为作废");
                return;
            }
            String email = caseTemp == null ? null : caseTemp.getFieldJson() == null ? null : caseTemp.getFieldJson().get("email");
            if (StringUtils.isBlank(email)) {
                failMap.put(letter.getId(), "邮箱地址为空");
                return;
            }
            MailParam mailParam = new MailParam();
            mailParam.setTo(email);
            String text = handleEmailContent(emailTemplate.getContent(), caseTemp, letter, deltMap);
            mailParam.setText(text);
            String title = handleEmailContent(emailTemplate.getName(), caseTemp, letter, deltMap);
            mailParam.setSubject(title);
            try {
                mailManager.sendHtmlMail(mailParam);
                letters.add(letter);
            } catch (Exception e) {
                log.error("函件id={}，邮件发送失败:", letter.getId(), e);
                failMap.put(letter.getId(), "邮件发送失败");
            }
        });
        failCount = failMap.size();
        successCount = total - failCount;
        // 添加对应的任务记录，方便根据任务id查询任务操作的案件信息
        Long taskId = asyncTaskService.updateLetterTask(
                param,
                userSession,
                total,
                AsyncTaskEnums.Status.SUCCESS.getCode(),
                successCount,
                failCount
        );
        // 更新案件和记录操作日志
        updateCaseAndLog(caseIds, userSession, taskId);
        // 对成功发送邮件的生成函件发送记录
        List<LetterSendRecord> letterSendRecords = new ArrayList<>();
        letters.forEach(letter -> {
            LetterSendRecord record = new LetterSendRecord();
            record.setLetterId(letter.getId());
            record.setOrgId(letter.getOrgId());
            record.setTaskId(taskId);
            record.setStatus(LetterConstant.IS_SUCCESS);
            record.setSendType(LetterConstant.SEND_TYPE_EMAIL);
            record.setSendTime(new Date());
            record.setUpdateTime(new Date());
            record.setCreateTime(new Date());
            letterSendRecords.add(record);
        });
        if (!CollectionUtils.isEmpty(letterSendRecords)) {
            letterSendRecordMapper.insertList(letterSendRecords);
        }
        // 回填函件，更新相应函件letter信息, 只有(flag=true)单独邮箱操作才回填状态
        for (Letter letter : letterList) {
            Letter updateLetter = new Letter();
            updateLetter.setId(letter.getId());
            updateLetter.setUpdateTime(new Date());
            if (failMap.containsKey(letter.getId()) && flag) {
                updateLetter.setStatus(LetterConstant.LETTER_STATUS_FAIL);
                updateLetter.setReason(failMap.get(letter.getId()));
            } else {
                if (flag) {
                    updateLetter.setStatus(LetterConstant.LETTER_STATUS_IS_SEND);
                }
                Case caseTemp = caseList.stream().filter(aCase -> Objects.equals(aCase.getId(), letter.getCaseId()))
                        .findFirst().orElse(null);
                String email = caseTemp == null ? null : caseTemp.getFieldJson() == null ? null : caseTemp.getFieldJson().get("email");
                updateLetter.setEmail(email);
            }
            letterMapper.updateByPrimaryKeySelective(updateLetter);
        }
    }

    String handleEmailContent(String content, Case caseInfo, Letter letter, Map<Long, String> deltMap) {
        Long templateId = letter.getTemplateId();
        LetterTemplate letterTemplate = letterTemplateService.getLetterTemplate(templateId);
        if (content.contains("#casename#")) {
            content = content.replace("#casename#", caseInfo.getName());
        }
        if (content.contains("#deltname#")) {
            content = content.replace("#deltname#", deltMap.get(caseInfo.getOrgDeltId()));
        }
        if (content.contains("#linkid#")) {
            if (StringUtils.equals(systemConfig.getChannelCode(), FileStorageEnums.Type.LOCAL.getCode())) {
                String shortUrlPrefix = systemConfig.getShortUrlPrefix();
                int index = shortUrlPrefix.lastIndexOf("/xapi");
                String ip = shortUrlPrefix.substring(0, index);
                String url = letter.getUrl();
                int i = url.lastIndexOf("/");
                String prefix = url.substring(0, i+1);
                String fileName = url.substring(i+1);
                try {
                    String finalUrl = ip + prefix + URLEncoder.encode(fileName, "UTF-8");
                    content = content.replace("#linkid#","<a href=\""+ finalUrl+ "\">《"+letterTemplate.getName()+"》</a>");
                } catch (UnsupportedEncodingException e) {
                    throw new ApiException("本地化函件地址编码转换失败");
                }
            } else {
                content = content.replace("#linkid#", "<a href=\"" + letter.getUrl() + "\">《" + letterTemplate.getName() + "》</a>");
            }
        }
        return content;
    }

    public void sendSms(List<Letter> letterList, List<Case> caseList, UserSession userSession, LetterParam param) {
        param.setSendType(LetterConstant.SEND_TYPE_SMS);
        SmsTemplate smsTemplate = smsTemplateService.selectByPrimaryKey(param.getSmsTemplateId());
        List<Long> caseIds = caseList.stream().map(Case::getId).collect(Collectors.toList());
        Map<Long, String> failMap = new HashMap<>();
        int total = letterList.size();
        List<Letter> letters = new ArrayList<>();
        List<Case> cases = new ArrayList<>();
        List<String> existMobile = new ArrayList<>();
        // 检验函件
        letterList.forEach(letter -> {
            if (existMobile.contains(letter.getOwnMobile())) {
                failMap.put(letter.getId(), "存在重复债务人号码，请重新发送");
                return;
            }
            Case caseTemp = caseList.stream().filter(aCase -> Objects.equals(aCase.getId(), letter.getCaseId())).findFirst().orElse(null);
            if (Objects.equals(caseTemp.getCaseStatus(), CaseEnums.CaseStatus.FINISH.getCode())) {
                failMap.put(letter.getId(), "案件状态为结案");
                return;
            }
            if (Objects.equals(caseTemp.getCaseStatus(), CaseEnums.CaseStatus.STOP.getCode())) {
                failMap.put(letter.getId(), "案件状态为停催");
                return;
            }
            if (Objects.equals(caseTemp.getCaseStatus(), CaseEnums.CaseStatus.INVALID.getCode())) {
                failMap.put(letter.getId(), "案件状态为作废");
                return;
            }
            letters.add(letter);
            existMobile.add(letter.getOwnMobile());
            if (!cases.contains(caseTemp)) {
                cases.add(caseTemp);
            }
        });
        // 添加对应的任务记录，方便根据任务id查询任务操作的案件信息，短信的成功失败条数，需要实时统计，因为只要短信回调之后才知道
        Long taskId = asyncTaskService.updateLetterTask(
                param,
                userSession,
                total,
                AsyncTaskEnums.Status.SUCCESS.getCode(),
                0,
                0
        );
        // 更新案件和记录操作日志
        updateCaseAndLog(caseIds, userSession, taskId);
        List<LetterSendRecord> letterSendRecords = new ArrayList<>();
        // 插入发函记录
        letters.forEach(letter -> {
            LetterSendRecord record = new LetterSendRecord();
            record.setLetterId(letter.getId());
            record.setOrgId(letter.getOrgId());
            record.setTaskId(taskId);
            record.setStatus(LetterConstant.IS_SENDING);
            record.setSendType(LetterConstant.SEND_TYPE_SMS);
            record.setSendTime(new Date());
            record.setUpdateTime(new Date());
            record.setCreateTime(new Date());
            letterSendRecords.add(record);
        });
        Long casePlanId= null;
        if (!CollectionUtils.isEmpty(letterSendRecords)) {
            letterSendRecordMapper.insertList(letterSendRecords);
            Map<Long, Long> letterIdToSendIdMap = letterSendRecords.stream().collect(Collectors.toMap(LetterSendRecord::getLetterId, p -> p.getId()));
            // 创建短信计划和调用短信平台发送短信
            JSONArray jsonArray = handSmsParam(letters, cases, smsTemplate);
            CasePlan casePlan = new CasePlan();
            casePlan.setName("函件计划");
            // 立即开始
            casePlan.setStartType(0);
            casePlan.setSmsTplId(param.getSmsTemplateId());
            // casePlan.setSmsSignId(smsTemplate.getSmsSignId());
            casePlan.setDebtorOwn(1);
            casePlan.setContacts(0);
            casePlan.setSelectedTotal((long)letterList.size());
            // 小组长可以看到包含该小组的所有计划，包括总公司和分公司创建的
            casePlan.setOrgId(userSession.getOrgId());
            casePlan.setDepId(userSession.getDepId());
            casePlan.setTeamId(userSession.getTeamId());
            casePlan.setCreateBy(userSession.getId());
            casePlan.setUpdateBy(userSession.getId());
            casePlan.setType(CasePlanEnums.Type.MESSAGE.getCode());
            casePlan.setStatus(CasePlanEnums.Status.ING.getCode());
            // 调度言接口添加计划 0代表立即执行， 执行开始时间可以不传
            Long duyanPlanId = messageManager.createMessagePlan(jsonArray, smsTemplate, userSession, 0, null);
            if (duyanPlanId == null) {
                // 调用创建短信计划接口失败
                letters.forEach(letter -> {
                    failMap.put(letter.getId(), "调用创建短信计划接口失败");
                });
            }
            casePlan.setDuyanPlanId(duyanPlanId);
            casePlanService.insertSelective(casePlan);
            casePlanId = casePlan.getId();
            // 添加短信发送记录方便回调
            addSmsSendRecord(jsonArray, caseList, userSession, param.getSmsTemplateId(), casePlanId, letterIdToSendIdMap);
        }
        Long finalCasePlanId = casePlanId;
        letterList.forEach(letter -> {
            Letter updateLetter = new Letter();
            updateLetter.setId(letter.getId());
            updateLetter.setUpdateTime(new Date());
            updateLetter.setPlanId(finalCasePlanId);
            if (failMap.containsKey(letter.getId())) {
                updateLetter.setStatus(LetterConstant.LETTER_STATUS_FAIL);
                updateLetter.setReason(failMap.get(letter.getId()));
            }
            letterMapper.updateByPrimaryKeySelective(updateLetter);
        });
    }

    JSONArray handSmsParam(List<Letter> letterList, List<Case> caseList, SmsTemplate smsTemplate) {
        JSONArray jsonArray = new JSONArray();
        letterList.forEach(letter -> {
            Long caseId = letter.getCaseId();
            Case caseTemp = caseList.stream().filter(aCase -> Objects.equals(aCase.getId(), caseId)).findFirst().orElse(null);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("letterId", letter.getId());
            jsonObject.put("caseId", caseTemp.getId());
            jsonObject.put("relationMobile", letter.getOwnMobile());
            jsonObject.put("uuid", UUID.randomUUID());
            jsonObject.put("relation", "本人");
            jsonObject.put("contactName", caseTemp.getName());
            CaseQueryResult caseQueryResult = AuthBeanUtils.copy(caseTemp, CaseQueryResult.class);
            Map<String, String> varJson = smsTemplate.getVarJson();
            Map<String, String> extParam = smsSendService.getExtParam(varJson, caseQueryResult, null);
            if (varJson.containsKey("linkid")) {
                // 填充短链接地址
                extParam.put("linkid", systemConfig.getShortUrlPrefix()+letter.getUniqueCode());
            }
            jsonObject.put("extParam", extParam);
            jsonArray.add(jsonObject);
        });
        return jsonArray;
    }

    public void addSmsSendRecord(JSONArray jsonArray, List<Case>caseList, UserSession userSession,
                                 Long smsTemplateId, Long planId, Map<Long, Long> letterIdToSendIdMap) {
        List<SmsSend> smsSendList = Lists.newArrayList();
        for (int i = 0; i < jsonArray.size(); i++) {
            Long letterId = jsonArray.getJSONObject(i).getLong("letterId");
            Long caseId = jsonArray.getJSONObject(i).getLong("caseId");
            Case caseTemp = caseList.stream().filter(aCase -> Objects.equals(aCase.getId(), caseId)).findFirst().orElse(null);
            SmsSend smsSend = new SmsSend();
            smsSend.setPlanId(planId);
            smsSend.setOrgId(userSession.getOrgId());
            smsSend.setDepId(userSession.getDepId());
            smsSend.setTeamId(userSession.getTeamId());
            smsSend.setCaseId(caseTemp.getId());
            smsSend.setLetterSendId(letterIdToSendIdMap.get(letterId));
            smsSend.setUuid(jsonArray.getJSONObject(i).getString("uuid"));
            smsSend.setOrgDeltId(caseTemp.getOrgDeltId());
            smsSend.setPlanName("函件计划");
            smsSend.setContactName(jsonArray.getJSONObject(i).getString("contactName"));
            smsSend.setRelation(jsonArray.getJSONObject(i).getString("relation"));
            smsSend.setRelationMobile(jsonArray.getJSONObject(i).getString("relationMobile"));
            smsSend.setOutSerialTemp(caseTemp.getOutSerialTemp());
            smsSend.setProductId(caseTemp.getProductId());
            smsSend.setOutBatchId(caseTemp.getOutBatchId());
            smsSend.setSendStatus(-1); // 初始状态 发送中
            smsSend.setReceiveStatus(-1); // 初始状态 接收中
            smsSend.setCreateTime(new Date());
            smsSend.setUpdateTime(new Date());
            smsSend.setCreateBy(userSession.getId());
            smsSend.setUpdateBy(userSession.getId());
            smsSend.setStatus(0);
            smsSend.setSmsTplId(smsTemplateId);
            smsSend.setFee(new BigDecimal("0"));
            smsSend.setCount(0);
            smsSendList.add(smsSend);

        }
        if (!CollectionUtils.isEmpty(smsSendList)) {
            smsSendService.insertBatch(smsSendList);
        }

    }

    Example handleQueryParam(LetterParam param , UserSession userSession) {
        Example example = new Example(Letter.class);
        example.and().andEqualTo("orgId", userSession.getOrgId());
        if (Objects.nonNull(param.getAllotAgent())) {
            example.and().andEqualTo("allotAgent",  param.getAllotAgent());
        }
        // 参数传入isFile=1代表查询发函文件列表，删除的也能看见 彻底删除所有tab页面都看不到
        if (!Objects.equals(param.getIsFile(), 1)) {
            example.and().andEqualTo("isDelete", CommonConstant.IS_NOT_DEL);
        }
        example.and().andNotEqualTo("isDelete", LetterConstant.IS_COMPLETE_DEL);
        if (roleService.likeBranchAdmin(userSession.getRoleId())) {
            // 如果是分公司管理员，进行分公司隔离
            example.and().andEqualTo("depId", userSession.getDepId());
        }
        if (param.getStatus() != null) {
            example.and().andEqualTo("status", param.getStatus());
        } else {
            // 查询待发函列表
            if (Objects.equals(param.getIsFile(), 0)) {
                example.and().andNotEqualTo("status", LetterConstant.LETTER_STATUS_IS_SEND);
            }
        }
        if (param.getDepId() != null) {
            example.and().andEqualTo("depId", param.getDepId());
        }
        if (!CollectionUtils.isEmpty(param.getDepIds())) {
            example.and().andIn("depId", param.getDepIds());
        }
        if (!CollectionUtils.isEmpty(param.getTeamIds())) {
            example.and().andIn("teamId", param.getTeamIds());
        }
        if (!CollectionUtils.isEmpty(param.getProductIds())) {
            example.and().andIn("productId", param.getProductIds());
        }
        if (param.getType() != null) {
            example.and().andEqualTo("type", param.getType());
        }
//        if (param.getSendType() != null) {
//            example.and().andEqualTo("sendType", param.getSendType());
//        }
        if (param.getOrgDeltId() != null) {
            example.and().andEqualTo("orgDeltId", param.getOrgDeltId());
        }
        if (param.getProductId() != null) {
            example.and().andEqualTo("productId", param.getProductId());
        }
        if (!CollectionUtils.isEmpty(param.getOutSerialNos())) {
            example.and().andIn("outSerialNo", param.getOutSerialNos());
        }
        if (!CollectionUtils.isEmpty(param.getNames())) {
            example.and().andIn("name", param.getNames());
        }
        if (!CollectionUtils.isEmpty(param.getOwnMobiles())) {
            example.and().andIn("ownMobile", param.getOwnMobiles());
        }
        if (!CollectionUtils.isEmpty(param.getIdCards())) {
            example.and().andIn("idCard", param.getIdCards());
        }
        if (param.getLetterId() != null) {
            example.and().andEqualTo("id", param.getLetterId());
        }
        if (param.getIsCreate() != null) {
            example.and().andEqualTo("isCreate", param.getIsCreate());
        }
        if (!CollectionUtils.isEmpty(param.getLetterIds())) {
            example.and().andIn("id", param.getLetterIds());
        }
        if (Objects.equals(param.getIsCreate(), LetterConstant.IS_CREATE) &&
        Objects.equals(param.getIsFile(), 0)) {
            example.orderBy("fileTime").desc().orderBy("id").desc();
        } else {
            example.orderBy("createTime").desc().orderBy("id").desc();
        }
        return example;
    }

    void updateCaseAndLog(List<Long> caseIds, UserSession userSession, Long taskId) {
        // 更新案件
        Case caseInfo = new Case();
        caseInfo.setUpdateBy(userSession.getId());
        caseInfo.setUpdateTime(new Date());
        Example example = new Example(Case.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", caseIds);
        caseService.updateByExampleSelective(caseInfo, example);
        // 添加案件操作日志记录
        CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
        caseBatchUpdateEvent.setType(CaseLogEnums.Type.CASE_LETTER_UPDATE.getCode());
        caseBatchUpdateEvent.setUserSession(userSession);
        caseBatchUpdateEvent.setCaseIds(caseIds);
        caseBatchUpdateEvent.setTaskId(taskId);
        applicationContext.publishEvent(caseBatchUpdateEvent);
    }

    public List<LetterVO> toLetterVOList(List<Letter> letterList) {
        List<LetterVO> letterVOList = new ArrayList<>();
        List<Long> caseIds = letterList.stream().filter(letter -> letter.getCaseId() != null).map(Letter::getCaseId).collect(Collectors.toList());
        List<Long> orgDeltIds = letterList.stream().filter(letter -> letter.getOrgDeltId() != null).map(Letter::getOrgDeltId).collect(Collectors.toList());
        List<Long> productIds = letterList.stream().filter(letter -> letter.getProductId() != null).map(Letter::getProductId).collect(Collectors.toList());
        List<Long> outBatchIds = letterList.stream().filter(letter -> letter.getOutBatchId()!= null).map(Letter::getOutBatchId).collect(Collectors.toList());
        List<Long> innerBatchIds = letterList.stream().filter(letter -> letter.getInnerBatchId() != null).map(Letter::getInnerBatchId).collect(Collectors.toList());
        List<Long> senders = letterList.stream().filter(letter -> letter.getSender() != null).map(Letter::getSender).collect(Collectors.toList());
        List<Long> depIds = letterList.stream().filter(letter -> letter.getDepId() != null).map(Letter::getDepId).collect(Collectors.toList());
        List<Long> teamIds = letterList.stream().filter(letter -> letter.getTeamId() != null).map(Letter::getTeamId).collect(Collectors.toList());
        Map<Long, String> userMap = userService.getUserMap(senders);
        Map<Long, String> deltMap = deltService.getDeltMap(orgDeltIds);
        Map<Long, String> productMap = productService.getProductMap(productIds);
        Map<Long, String> outBatchMap = outBatchService.getOutBatchMap(outBatchIds);
        Map<Long, String> innerBatchMap = innerBatchService.getInnerBatchMap(innerBatchIds);
        Map<Long, String> depTeamMap = depTeamService.getDepTeamMap(depIds);
        Map<Long, String> teamMap = depTeamService.getDepTeamMap(teamIds);
        List<CaseAddress> caseAddressList = caseAddressService.getCaseAddressByCaseIds(caseIds);
        letterList.forEach(letter -> {
            LetterVO letterVO = AuthBeanUtils.copy(letter, LetterVO.class);
            List<CaseAddress> addresses = caseAddressList.parallelStream().filter(caseAddress -> Objects.equals(caseAddress.getCaseId(), letter.getCaseId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(addresses)) {
                letterVO.setAddressCount(addresses.size());
                List<CaseAddressVO> caseAddressVOList = addresses.stream().map(caseAddress -> toCaseAddressVO(caseAddress)).collect(Collectors.toList());
                letterVO.setAddressList(caseAddressVOList);
            } else {
                letterVO.setAddressCount(0);
            }
            letterVO.setSenderName(userMap.get(letter.getSender()));
            letterVO.setOrgDeltName(deltMap.get(letter.getOrgDeltId()));
            letterVO.setProductName(productMap.get(letter.getProductId()));
            letterVO.setOutBatchNo(outBatchMap.get(letter.getOutBatchId()));
            letterVO.setInnerBatchNo(innerBatchMap.get(letter.getInnerBatchId()));
            letterVO.setDepName(depTeamMap.get(letter.getDepId()));
            letterVO.setTeamName(teamMap.get(letter.getTeamId()));
            letterVOList.add(letterVO);
        });
        return letterVOList;
    }


    CaseAddressVO toCaseAddressVO(CaseAddress caseAddress) {
        if (caseAddress == null) {
            return null;
        }
        CaseAddressVO caseAddressVO = AuthBeanUtils.copy(caseAddress, CaseAddressVO.class);
        caseAddressVO.setState((int)caseAddress.getState());
        caseAddressVO.setSource((int)caseAddress.getSource());
        return caseAddressVO;
    }

    public Letter getLetterByUniqueCode(String uniqueCode) {
        Example example = new Example(Letter.class);
        example.and().andEqualTo("uniqueCode", uniqueCode);
        List<Letter> letterList = letterMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(letterList)) {
            return null;
        }
        return letterList.get(0);
    }

    public void checkSmsSendStatus(List<Letter> letters) {
        if (CollectionUtils.isEmpty(letters)) {
            return;
        }
        List<Long> casePlanIds = letters.stream().filter(letter -> (letter.getPlanId() != null && Objects.equals(
                letter.getStatus(), LetterConstant.LETTER_STATUS_SENDING))).map(Letter::getPlanId).distinct().collect(Collectors.toList());
        List<Long> failPlanIds = casePlanService.getFailPlanIds(casePlanIds);
        letters.forEach(letter -> {
            if (letter.getPlanId() != null && failPlanIds.contains(letter.getPlanId())) {
                letter.setStatus(LetterConstant.LETTER_STATUS_FAIL);
                letter.setReason("度言执行失败");
            }
        });
        if (!CollectionUtils.isEmpty(failPlanIds)) {
            Example example = new Example(Letter.class);
            Letter letter = new Letter();
            letter.setStatus(LetterConstant.LETTER_STATUS_FAIL);
            letter.setReason("度言执行失败");
            letter.setUpdateTime(new Date());
            example.and().andIn("planId", failPlanIds);
            example.and().andEqualTo("status", LetterConstant.LETTER_STATUS_SENDING);
            letterMapper.updateByExampleSelective(letter, example);
        }
    }

    /**
     * 案件彻底物理删除，级联删除待发函件记录
     * @param caseIds 案件ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer delLetter(List<Long> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return 0;
        }
        Example example = new Example(Letter.class);
        example.and().andIn("caseId", caseIds);
        example.and().andEqualTo("status", LetterConstant.LETTER_STATUS_NEW);
        Letter letter = new Letter();
        letter.setIsDelete(LetterConstant.IS_DEL);
        letter.setUpdateTime(new Date());
        int update = letterMapper.updateByExampleSelective(letter, example);
        return update;
    }

    /**
     * 案件彻底物理删除，级联删除待创建函件记录
     *
     * @param caseIds 案件ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void delIsNotCreateLetter(List<Long> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }
        Example example = new Example(Letter.class);
        example.and().andIn("caseId", caseIds);
        example.and().andEqualTo("isCreate", LetterConstant.IS_NOT_CREATE);
        Letter letter = new Letter();
        letter.setIsDelete(LetterConstant.IS_DEL);
        letter.setUpdateTime(new Date());
        letterMapper.updateByExampleSelective(letter, example);
    }

    /**
     * 批量删除函件 删除按钮在生成函件页面
     * 1、待发送函件删除，记录一起删除
     * 2、发函记录删除 仅删除pdf文件
     * 逻辑删除
     * @param param
     */
    public void deleteBatchLetterFile(LetterFileDeleteParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        if (param.getDepId() == null) {
            if (roleService.likeBranchAdmin(userSession.getRoleId())) {
                // 如果是分公司管理员，进行分公司隔离
                param.setDepId(userSession.getDepId());
            }
        }
        if (param.getAllSelect() != null && param.getAllSelect()) {
            Example example = handleQueryParam(param, userSession);
            List<Letter> letterList = letterMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(letterList)) {
                return;
            }
            List<Long> letterFileIds = letterList.stream().map(Letter::getId).collect(Collectors.toList());


            // 设置异步任务
            AsyncTask asyncTask = new AsyncTask();
            asyncTask.setCreateBy(userSession.getId());
            asyncTask.setOrgId(userSession.getOrgId());
            asyncTask.setType(AsyncTaskEnums.Type.DEL_LETTER_FILE.getCode());
            asyncTask.setStatus(AsyncTaskEnums.Status.ING.getCode());
            asyncTask.setTotal((long)letterFileIds.size());
            asyncTask.setSuccessAmt(0L);
            asyncTask.setIgnoreCount(0L);

            asyncTaskMapper.insertSelective(asyncTask);
            String fieldJson = JSON.toJSONString(param);
            asyncTaskMapper.updateFieldJson(asyncTask.getId(), fieldJson);
            // 任务添加到任务列表
            stringRedisTemplate
                    .opsForSet()
                    .add(KeyCache.DEL_LETTER_FILE_ID_PREFIX + asyncTask.getId(),
                            letterFileIds.stream().map(String::valueOf).toArray(String[]::new));
            stringRedisTemplate.opsForList().leftPush(KeyCache.DEL_LETTER_FILE_TASK, asyncTask.getId().toString());
            return;
        }
        // 同步删除
        if (CollectionUtils.isEmpty(param.getLetterIds())) {
            throw new ApiException("函件文件列表不能为空");
        }
        Example example = new Example(Letter.class);
        example.and().andEqualTo("orgId", userSession.getOrgId());
        example.and().andEqualTo("depId", param.getDepId());
        example.and().andIn("id", param.getLetterIds());
        List<Letter> letters = letterMapper.selectByExample(example);
        long successCount = 0L;
        for (Letter letter: letters) {
            boolean delResult = OSSClientUtil.deleteFileByFileName(letter.getAlias(), letter.getUrl());
            if (delResult) {
                // 重新计算使用存储空间、诉讼存储空间
                orgSpaceService.subtractUseSpace(letter.getOrgId(), letter.getFileSize(), SpaceEnums.Type.LETTER_FILE.getCode());
                letter.setUpdateTime(new Date());
                letter.setUrl(null);
                letter.setFileSize(null);
                letter.setAlias(null);
                letter.setShortFileName(null);
                letter.setIsDelete(LetterConstant.IS_COMPLETE_DEL);
                letterMapper.updateByPrimaryKey(letter);
                successCount++;
            }
        }

        AsyncTask asyncTask = new AsyncTask();
        asyncTask.setCreateBy(userSession.getId());
        asyncTask.setOrgId(userSession.getOrgId());
        if (UserUtils.likeBranchAdmin()) {
            asyncTask.setDepId(userSession.getDepId());
        } else if (UserUtils.likeTeamLeader()) {
            asyncTask.setDepId(userSession.getDepId());
            asyncTask.setTeamId(userSession.getTeamId());
        }
        asyncTask.setType(AsyncTaskEnums.Type.DEL_LETTER_FILE.getCode());
        asyncTask.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
        asyncTask.setTotal((long)letters.size());
        asyncTask.setSuccessAmt(successCount);
        asyncTask.setIgnoreCount(0L);
        asyncTaskMapper.insertSelective(asyncTask);
        String fieldJson = JSON.toJSONString(param);
        asyncTaskMapper.updateFieldJson(asyncTask.getId(), fieldJson);

    }
}
