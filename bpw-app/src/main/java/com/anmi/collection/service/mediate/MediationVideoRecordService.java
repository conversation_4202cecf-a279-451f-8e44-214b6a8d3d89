package com.anmi.collection.service.mediate;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.mapper.MediationVideoRecordMapper;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.domain.mediate.MediationVideoRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MediationVideoRecordService extends BaseService<MediationVideoRecord> {
    @Resource
    private MediationVideoRecordMapper mediationVideoRecordMapper;

    /**
     * 查询对应房间的录制记录
     *
     * @param roomIds 房间
     * @return 认证信息
     */
    public Map<Long, List<MediationVideoRecord>> queryRecordMapById(List<Long> roomIds) {
        AssertUtil.notEmpty(roomIds, "roomIds不可为空");
        Example example = new Example(MediationVideoRecord.class);
        example.and().andIn("roomId", roomIds);
        example.orderBy("id").desc();
        List<MediationVideoRecord> mediationVideoRecords = mediationVideoRecordMapper.selectByExample(example);
        return mediationVideoRecords.stream().collect(Collectors.groupingBy(MediationVideoRecord::getRoomId));
    }
}
