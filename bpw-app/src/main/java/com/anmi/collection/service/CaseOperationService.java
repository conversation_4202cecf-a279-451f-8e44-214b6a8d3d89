package com.anmi.collection.service;

import cn.duyan.thread.DuyanThreadExecutor;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anmi.alfred.dto.CaseOperationQueryDto;
import com.anmi.alfred.entity.TransferCaseOperationResult;
import com.anmi.alfred.response.concrete.CaseOperationCountResult;
import com.anmi.alfred.response.concrete.CaseOperationResponse;
import com.anmi.alfred.response.concrete.CaseOperationStatisticsResponse;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.dto.CaseContact;
import com.anmi.collection.dto.CaseFollowInfo;
import com.anmi.collection.dto.fileStorage.UploadCreatedFileInfo;
import com.anmi.collection.encrypt.EncryptProperties;
import com.anmi.collection.encrypt.EncryptService;
import com.anmi.collection.entity.requset.cases.*;
import com.anmi.collection.entity.requset.cases.casePlan.VoiceSite;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.CaseFollowListVO;
import com.anmi.collection.entity.response.cases.CaseFollowVO;
import com.anmi.collection.entity.response.cases.CaseOperationStatisticsVO;
import com.anmi.collection.entity.response.cases.CaseOperationVO;
import com.anmi.collection.event.CaseAllotEvent;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.manager.NewRobotManager;
import com.anmi.collection.manager.dorest.response.RobotPageNodesRes;
import com.anmi.collection.mapper.*;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.admin.PushStrategyService;
import com.anmi.collection.service.businessobject.CaseCtrlPoolCondition;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.service.remote.RemoteAlfredService;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.*;
import com.anmi.domain.dto.CaseOperationParamDto;
import com.anmi.domain.principal.Delt;
import com.anmi.domain.robot.RobotQueue;
import com.anmi.domain.site.SiteVarsMapping;
import com.anmi.domain.sys.DownloadTask;
import com.anmi.domain.sys.PushStrategy;
import com.anmi.domain.user.Company;
import com.anmi.domain.user.Contacts;
import com.anmi.domain.user.CustomOperationStatus;
import com.anmi.domain.user.User;
import com.anmi.domain.visit.Visit;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/** Created by dongwang on 2018-12-10. */
@Service
@Slf4j
public class CaseOperationService extends BaseService<CaseOperation> {

  private static final String ASYNC_PREFIX = "async-";

  @Autowired private CaseOperationMapper caseOperationMapper;

  @Autowired private UserService userService;
  @Autowired private ProductService productService;
  @Autowired private DeltService deltService;
  @Autowired private DepTeamService depTeamService;
  @Autowired private CasePlanService casePlanService;
  @Autowired private CaseService caseService;
  @Autowired private CaseOperationService caseOperationService;
  @Autowired private CompanyService companyService;
  @Autowired private DownloadTaskService downloadTaskService;
  @Autowired private CaseMapper caseMapper;
  @Autowired private StringRedisTemplate stringRedisTemplate;
  @Autowired private AsyncTaskService asyncTaskService;
  @Autowired private ContactsService contactsService;
  @Autowired private ContactsMapper contactsMapper;
  @Autowired private InnerBatchService innerBatchService;
  @Autowired private OutBatchService outBatchService;
  @Autowired private CaseDebtorMapper caseDebtorMapper;

  @Autowired private RemoteAlfredService remoteAlfredService;
  @Autowired private CustomOperationStatusService customOperationStatusService;
  @Autowired private CaseDebtorService caseDebtorService;
  @Autowired private ApplicationContext applicationContext;
  @Autowired private DuyanManager duyanManager;
  @Autowired private OrgSwitchMapper orgSwitchMapper;
  @Autowired private VisitMapper visitMapper;
  @Autowired private CaseCallbackMapper caseCallbackMapper;
  @Autowired private CaseCallbackService caseCallbackService;
  @Autowired private OrgConfigService orgConfigService;
  @Autowired private CasePlanRelService casePlanRelService;
  @Autowired private CasePlanRelContactService casePlanRelContactService;
  @Autowired private CasePlanRelContactMapper casePlanRelContactMapper;
  @Autowired private CaseCtrlService caseCtrlService;
  @Autowired private CustomClientLabelService customClientLabelService;
  @Autowired private NewRobotManager newRobotManager;
  @Resource private RedisTemplate redisTemplate;
  @Resource private CaseOperationHistoryMapper caseOperationHistoryMapper;
  @Autowired private CaseOperationUseLessMapper caseOperationUseLessMapper;
  @Autowired private SiteVarsMappingService siteVarsMappingService;
  @Autowired private EncryptProperties encryptProperties;
  @Autowired private EncryptService encryptService;
  @Autowired private RobotQueueService robotQueueService;
  @Autowired private PushStrategyService pushStrategyService;
  @Autowired private FileStorageStrategyFactory fileStorageStrategyFactory;
  @Autowired private DialogService dialogService;

  private static final String OPERATION_CALL_UUID = "operation_call_uuid";

  public List<CasePlanDetailResult> selectCasePlanDetail(CaseOperationParam param) {
    return caseOperationMapper.selectCasePlanDetail(param);
  }

  public PageOutput<CaseOperationVO> getByPage(CaseOperationParam param) {
    param.setOrgId(getOrgId());
    if (UserUtils.likeBranchAdmin()) {
      param.setDepId(getTokenUser().getDepId());
    }

    OrgSwitch orgSwitch=this.orgSwitchMapper.selectOrgSwitchByOrgId(UserUtils.getTokenUser().getOrgId());
    if (orgSwitch != null && OrgSwitchEnums.ShowHiddenOperationSwitch.NO.getCode() == orgSwitch.getShowHiddenOperationSwitch()) {
      param.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
    }
    PageParam pageParam = new PageParam();
    pageParam.setPage(param.getPage());
    pageParam.setLimit(param.getLimit());
    Page<?> page = super.setPage(pageParam);
    List<CaseOperationResult> list = selectByPage(param);
    UserSession userSession = getTokenUser();
    List<CaseOperationVO> vos = convert(list,userSession);
    return new PageOutput<>(page.getPageNum(),page.getPageSize(), (int) page.getTotal(),vos);
  }

  public PageOutput<CaseOperationVO> getByPageFromElasticsearch(CaseOperationParam param) {
    param.setOrgId(getOrgId());
    if (UserUtils.likeBranchAdmin()) {
      param.setDepId(getTokenUser().getDepId());
    }
    // 分页
    int pageNum = param.getPage();
    int limit = param.getLimit();
    if (param.getStartStr() != null) {
      String[] times = param.getStartStr().split(",");
      param.setStartTime(new Date(Long.parseLong(times[0])));
      param.setEndTime(new Date(Long.parseLong(times[1])));
    }

    CaseOperationParamDto dto = new CaseOperationParamDto();
    try {
      BeanUtil.copyProperties(param, dto);
    } catch (Exception e) {
      log.error("数据转换错误:{}", e.getMessage());
    }
    dto.setSortField("createTime");
    dto.setSortAsc(false);
    OrgSwitch orgSwitch=this.orgSwitchMapper.selectOrgSwitchByOrgId(UserUtils.getTokenUser().getOrgId());
    if (orgSwitch != null && OrgSwitchEnums.ShowHiddenOperationSwitch.NO.getCode() == orgSwitch.getShowHiddenOperationSwitch()) {
      dto.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
    }
    CaseOperationResponse caseOperationResponse = remoteAlfredService.fetchFromCaseOperationRemote(dto);
    List<TransferCaseOperationResult> sources = caseOperationResponse.getList();
    if(CollectionUtils.isEmpty(sources)){
      return new PageOutput<>(0,20,0,Collections.emptyList());
    }

    long totalNum = caseOperationResponse.getTotalNum();
    List<Long> caseOperationIds = sources.stream().map(TransferCaseOperationResult::getId).collect(Collectors.toList());
    CaseOperationParam query = new CaseOperationParam();
    query.setDataType(param.getDataType());
    query.setOperationIdList(caseOperationIds);
    List<CaseOperationResult> list = caseOperationMapper.selectByMap(query);
    UserSession userSession = getTokenUser();
    List<CaseOperationVO> vos = convert(list,userSession);
    return new PageOutput<>(pageNum, limit, (int) totalNum, vos);
  }

  public Long getAllData(CaseOperationParam param, UserSession userSession, Consumer<PageOutput<CaseOperationResult>> callBack) {
    if (Objects.isNull(userSession) || Objects.isNull(userSession.getOrgId())) {
      throw new ApiException("orgId不能为空");
    }
    param.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      param.setDepId(userSession.getDepId());
    }
    OrgSwitch orgSwitch = this.orgSwitchMapper.selectOrgSwitchByOrgId(userSession.getOrgId());
    if (orgSwitch != null && OrgSwitchEnums.ShowHiddenOperationSwitch.NO.getCode() == orgSwitch.getShowHiddenOperationSwitch()) {
      param.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
    }
    if (param.getStartStr() != null) {
      String[] times = param.getStartStr().split(",");
      param.setStartTime(new Date(Long.parseLong(times[0])));
      param.setEndTime(new Date(Long.parseLong(times[1])));
    }
    int pageNum = 1;
    Integer limit = 500;
    int totalPage = 1;
    PageParam pageParam = new PageParam();
    pageParam.setLimit(limit);
    long totalNum = 0L;
    while (true) {
      pageParam.setPage(pageNum);
      Page page = super.setPage(pageParam);
      List<CaseOperationResult> list = caseOperationMapper.selectByMap(param);

      PageOutput<CaseOperationResult> pageInfo=new PageOutput<>();
      pageInfo.setTotal((int)page.getTotal());
      pageInfo.setPageNum(page.getPageNum());
      pageInfo.setPageSize(page.getPageSize());
      pageInfo.setLimit(page.getPageSize());
      pageInfo.setPages(page.getPages());
      pageInfo.setList(list);
      callBack.accept(pageInfo);
      totalNum += list.size();
      if (pageNum == 1) {
        Integer total = (int) page.getTotal();
        int i = (total % limit) > 0 ? 1 : 0;
        totalPage = total / limit + i;
      }
      if (pageNum >= totalPage) {
        break;
      }
      pageNum++;
    }
    return totalNum;
  }

  public Long getAllDataFromES(CaseOperationParam param, UserSession userSession, Consumer<PageOutput<CaseOperationResult>> callBack){
    if (Objects.isNull(userSession) || Objects.isNull(userSession.getOrgId())) {
      throw new ApiException("orgId不能为空");
    }
    param.setOrgId(userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      param.setDepId(userSession.getDepId());
    }
    if (param.getStartStr() != null) {
      String[] times = param.getStartStr().split(",");
      param.setStartTime(new Date(Long.parseLong(times[0])));
      param.setEndTime(new Date(Long.parseLong(times[1])));
    }

    CaseOperationParamDto dto = new CaseOperationParamDto();
    try {
      BeanUtil.copyProperties(param, dto);
    } catch (Exception e) {
      log.error("数据转换错误:{}", e.getMessage());
    }
    dto.setSortField("createTime");
    dto.setSortAsc(false);
    OrgSwitch orgSwitch = this.orgSwitchMapper.selectOrgSwitchByOrgId(userSession.getOrgId());
    if (orgSwitch != null && OrgSwitchEnums.ShowHiddenOperationSwitch.NO.getCode() == orgSwitch.getShowHiddenOperationSwitch()) {
      dto.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
    }
    int pageNum = 1;
    Integer limit = 500;
    int totalPage = 1;
    dto.setPage(pageNum);
    dto.setLimit(limit);
    long totalNum = 0L;
    while (true) {
      dto.setPage(pageNum);
      CaseOperationResponse caseOperationResponse = remoteAlfredService.fetchFromCaseOperationRemote(dto);
      List<TransferCaseOperationResult> sources = caseOperationResponse.getList();
      if(CollectionUtils.isEmpty(sources)){
        break;
      }
      List<Long> caseOperationIds = sources.stream().map(TransferCaseOperationResult::getId).collect(Collectors.toList());
      CaseOperationParam query = new CaseOperationParam();
      query.setOperationIdList(caseOperationIds);
      List<CaseOperationResult> list = caseOperationMapper.selectByMap(query);
      totalNum += list.size();
      PageOutput<CaseOperationResult> pageInfo = new PageOutput<>();
      pageInfo.setTotal((int) caseOperationResponse.getTotalNum());
      pageInfo.setPageNum(pageNum);
      pageInfo.setPageSize(limit);
      pageInfo.setLimit(limit);
      pageInfo.setList(list);
      callBack.accept(pageInfo);
      if (pageNum == 1) {
        Integer total = (int) caseOperationResponse.getTotalNum();
        int i = (total % limit) > 0 ? 1 : 0;
        totalPage = total / limit + i;
      }
      pageInfo.setPages(totalPage);
      if (pageNum >= totalPage) {
        break;
      }
      pageNum++;
    }
    return totalNum;
  }

  private List<CaseOperationVO> convert(List<CaseOperationResult> list,UserSession userSession){
    List<CaseOperationVO> vos = new ArrayList<>();
    Map<Integer, String> operationStatusNameMap = customOperationStatusService.getNames(userSession.getOrgId());
    Map<Integer, String> operationStateNamesMap =orgConfigService.getAllNames(userSession.getOrgId(),OrgConfigEnums.Type.OPERATION_STATE.getCode());
    Map<Integer, String> callTypeNamesMap =orgConfigService.getAllNames(userSession.getOrgId(),OrgConfigEnums.Type.CALL_TYPE.getCode());
    Map<Long,String> depTeamNameMap=depTeamService.getNames();
    Map<Long,String> outBatchNoMap=outBatchService.getNames();
    String recEnabled = stringRedisTemplate.opsForValue().get(KeyCache.IS_EARLY_MEDIA_REC_ENABLED);
    Boolean isEarlyMediaRecEnabled = recEnabled != null && Boolean.parseBoolean(recEnabled);
    String recObEnabled = stringRedisTemplate.opsForValue().get(KeyCache.IS_EARLY_MEDIA_REC_OB_ENABLED);
    Boolean isEarlyMediaRecObEnabled = recObEnabled != null && Boolean.parseBoolean(recObEnabled);
    List<Long> submitList = list.stream().map(CaseOperation::getAdminSubmitter).collect(Collectors.toList());
    // 查询所有管理员信息
    Map<Long, String> adminMap = new HashMap<>();
    if (!CollectionUtils.isEmpty(submitList)) {
      List<User> users = userService.selectByIdList(submitList);
      adminMap = users.stream().collect(Collectors.toMap(User::getId, User::getName));
    }
    Map<Long,String> productMap = productService.getNames();
    Map<Long,String> deltMap = deltService.getNames();
    Map<Long,String> customClientLabelMap = customClientLabelService.getNames(userSession.getOrgId());
    for (int i = 0; i < list.size(); i++) {
      CaseOperationVO vo = convertToVO(list.get(i),
        operationStatusNameMap,
        operationStateNamesMap,
        callTypeNamesMap,
        depTeamNameMap,
        outBatchNoMap,
        adminMap,
        isEarlyMediaRecEnabled,
        isEarlyMediaRecObEnabled,
        productMap,
        deltMap,
        customClientLabelMap);
      vos.add(vo);
    }
    return vos;
  }

  public CaseOperationStatisticsVO statistics(CaseOperationParam param) {
    param.setOrgId(getOrgId());
    if (UserUtils.likeBranchAdmin()) {
      param.setDepId(getTokenUser().getDepId());
    }

    OrgSwitch orgSwitch = this.orgSwitchMapper.selectOrgSwitchByOrgId(UserUtils.getTokenUser().getOrgId());
    if (orgSwitch != null && OrgSwitchEnums.ShowHiddenOperationSwitch.NO.getCode() == orgSwitch.getShowHiddenOperationSwitch()) {
      param.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
    }
    if (param.getStartStr() != null) {
      String[] times = param.getStartStr().split(",");
      param.setStartTime(new Date(Long.valueOf(times[0])));
      param.setEndTime(new Date(Long.valueOf(times[1])));
    }
    List<Map<String, Object>> result = caseOperationMapper.selectStatisticsByMap(param);
    Map<String, Object> map = result.stream().collect(Collectors.toMap(k -> (String) k.get("outcome"), v -> v.get("cnt")));
    CaseOperationStatisticsVO vo = new CaseOperationStatisticsVO();
    vo.setSuccess((Integer) map.getOrDefault("SUCCESS", 0));
    vo.setFail((Integer) map.getOrDefault("FAIL", 0));
    vo.setUserBusy((Integer) map.getOrDefault("USER_BUSY", 0));
    vo.setPowerOff((Integer) map.getOrDefault("POWER_OFF", 0));
    vo.setSuspended((Integer) map.getOrDefault("SUSPENDED", 0));
    vo.setNotExist((Integer) map.getOrDefault("NOT_EXIST", 0));
    return vo;
  }

  public CaseOperationStatisticsVO statisticsFromES(CaseOperationParam param) {
    param.setOrgId(getOrgId());
    if (UserUtils.likeBranchAdmin()) {
      param.setDepId(getTokenUser().getDepId());
    }
    if (param.getStartStr() != null) {
      String[] times = param.getStartStr().split(",");
      param.setStartTime(new Date(Long.valueOf(times[0])));
      param.setEndTime(new Date(Long.valueOf(times[1])));
    }

    CaseOperationParamDto dto = new CaseOperationParamDto();
    try {
      BeanUtil.copyProperties(param, dto);
    } catch (Exception e) {
      log.error("数据转换错误:{}", e.getMessage());
    }
    dto.setSortField("createTime");
    dto.setSortAsc(false);
    OrgSwitch orgSwitch=this.orgSwitchMapper.selectOrgSwitchByOrgId(UserUtils.getTokenUser().getOrgId());
    if (orgSwitch != null && OrgSwitchEnums.ShowHiddenOperationSwitch.NO.getCode() == orgSwitch.getShowHiddenOperationSwitch()) {
      dto.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
    }
    List<CaseOperationStatisticsResponse> caseOperationStatisticsResponse = remoteAlfredService.fetchFromCaseOperationStatisticsRemote(dto);
    Map<String, Object> map = caseOperationStatisticsResponse.stream().collect(Collectors.toMap(CaseOperationStatisticsResponse::getOutcome, CaseOperationStatisticsResponse::getCount));
    CaseOperationStatisticsVO vo = new CaseOperationStatisticsVO();
    vo.setSuccess((Integer) map.getOrDefault("SUCCESS", 0));
    vo.setFail((Integer) map.getOrDefault("FAIL", 0));
    vo.setUserBusy((Integer) map.getOrDefault("USER_BUSY", 0));
    vo.setPowerOff((Integer) map.getOrDefault("POWER_OFF", 0));
    vo.setSuspended((Integer) map.getOrDefault("SUSPENDED", 0));
    vo.setNotExist((Integer) map.getOrDefault("NOT_EXIST", 0));
    return vo;
  }


  public List<CaseOperationVO> getOperationListForOpen(CaseOperationParam param) {
    List<CaseOperationResult> list = caseOperationMapper.getOperationListForOpen(param);
    List<CaseOperationVO> vos = new ArrayList<>();
    Map<Integer, String> operationStatusNameMap =
            customOperationStatusService.getNames(param.getOrgDeltId());
    Map<Integer, String> operationStateNamesMap =orgConfigService
            .getAllNames(param.getOrgId(), OrgConfigEnums.Type.OPERATION_STATE.getCode());
    Map<Integer, String> callTypeNamesMap =orgConfigService
            .getAllNames(param.getOrgId(), OrgConfigEnums.Type.CALL_TYPE.getCode());
    Map<Long,String> depTeamNameMap=depTeamService.getNames();
    Map<Long,String> outBatchNoMap=outBatchService.getNames();
    String recEnabled = stringRedisTemplate.opsForValue().get(KeyCache.IS_EARLY_MEDIA_REC_ENABLED);
    Boolean isEarlyMediaRecEnabled = recEnabled == null ? false : Boolean.valueOf(recEnabled);
    String recObEnabled = stringRedisTemplate.opsForValue().get(KeyCache.IS_EARLY_MEDIA_REC_OB_ENABLED);
    Boolean isEarlyMediaRecObEnabled = recObEnabled == null ? false : Boolean.valueOf(recObEnabled);
    List<Long> submitList = list.stream().map(CaseOperation::getAdminSubmitter).collect(Collectors.toList());
    // 查询所有管理员信息
    Map<Long, String> adminMap = new HashMap<>();
    if (!CollectionUtils.isEmpty(submitList)) {
      List<User> users = userService.selectByIdList(submitList);
      adminMap = users.stream().collect(Collectors.toMap(User::getId, User::getName));
    }
    Map<Long,String> productMap = productService.getNames();
    Map<Long,String> deltMap = deltService.getNames();
    for (int i = 0; i < list.size(); i++) {
      CaseOperationVO vo = convertToVOForOpen(list.get(i),
              operationStatusNameMap,
              operationStateNamesMap,
              callTypeNamesMap,
              depTeamNameMap,
              outBatchNoMap,
              adminMap,
              isEarlyMediaRecEnabled,
              isEarlyMediaRecObEnabled,
              productMap,
              deltMap,
              param.getOrgId());
      vos.add(vo);
    }
    return vos;
  }

  /**
   * 获取催记逻辑视图 主要应用于open接口
   *
   * @param caseOperationResult
   * @param operationStatusNameMap
   * @param operationStatesNameMap
   * @param callTypesNameMap
   * @param depTeamNameMap
   * @param outBatchNoMap
   * @param adminMap
   * @param isEarlyMediaRecEnabled
   * @param isEarlyMediaRecObEnabled
   * @param productMap
   * @param deltMap
   * @param orgId
   * @return
   */
  public CaseOperationVO convertToVOForOpen(CaseOperationResult caseOperationResult,
                                 Map<Integer, String> operationStatusNameMap,
                                 Map<Integer,String> operationStatesNameMap,
                                 Map<Integer,String> callTypesNameMap,
                                 Map<Long,String> depTeamNameMap,
                                 Map<Long,String> outBatchNoMap,
                                 Map<Long, String> adminMap,
                                 Boolean isEarlyMediaRecEnabled,
                                 Boolean isEarlyMediaRecObEnabled,
                                 Map<Long,String> productMap,
                                 Map<Long,String> deltMap,
                                            Long orgId) {
    CaseOperationVO vo = new CaseOperationVO();
    BeanUtils.copyProperties(caseOperationResult, vo);
    vo.setUserId(caseOperationResult.getCreateBy());
    // 催员名字不直接拿取operatorName,拿取create_by对应用户数据
    if (userService.getNames(orgId).get(caseOperationResult.getCreateBy()) != null) {
      vo.setOperatorName(userService.getNames(orgId).get(caseOperationResult.getCreateBy()));
    }
    vo.setCaseName(caseOperationResult.getName());
    vo.setCaseId(caseOperationResult.getCaseId());
    vo.setProductName(productMap.get(caseOperationResult.getProductId()));
    vo.setDeltName(deltMap.get(caseOperationResult.getOrgDeltId()));
    vo.setStatus(caseOperationResult.getStatus());
    // 设置催收状态
    if (caseOperationResult
            .getCreateType()
            .equals(CaseOperationEnums.CreateType.IMPORT.getCode())) {
      vo.setOperationState(null);
    } else {
      vo.setOperationState(caseOperationResult.getOperationState());
    }
    vo.setTeamName(depTeamNameMap.get(caseOperationResult.getTeamId()));
    vo.setDepName(depTeamNameMap.get(caseOperationResult.getDepId()));
    vo.setIdCard(caseOperationResult.getIdCard());
    vo.setReduceAmount(caseOperationResult.getReduceAmount());
    vo.setCaseOpeTime(caseOperationResult.getCreateTime().getTime());
    vo.setOutBatchNo(outBatchNoMap.get(caseOperationResult.getOutBatchId()));
    vo.setOutSerialNo(
            StringUtils.isBlank(caseOperationResult.getOutSerialNo())
                    ? ""
                    : caseOperationResult
                    .getOutSerialNo()
                    .substring(0, caseOperationResult.getOutSerialNo().lastIndexOf("#")));
    vo.setOwnMobile(caseOperationResult.getOwnMobile());
    vo.setCallStyle(caseOperationResult.getCallStyle());
    vo.setTag(caseOperationResult.getTag()); // 客户标签
    vo.setAdminSubmitterName(adminMap.get(caseOperationResult.getAdminSubmitter()));
    // 催记状态 0正常，-11处理中
    Boolean isLock =
            stringRedisTemplate
                    .opsForSet()
                    .isMember(
                            KeyCache.CASE_OPERATION_PROTECT_EXIST_IDS + caseOperationResult.getOrgId(),
                            String.valueOf(caseOperationResult.getId()));
    if (isLock) {
      vo.setStatus(CaseOperationEnums.Status.HANDLE.getCode());
    }
    vo.setActionTypeName(operationStatusNameMap.get(vo.getActionType()));
    vo.setOperationStateName(operationStatesNameMap.get(vo.getOperationState()));
    vo.setCallTypeName(callTypesNameMap.get(vo.getCallType()));
    // 按要求返回电话结果FAIL情况，检测是否开启空号检测
    Integer submitType = caseOperationResult.getSubmitType();
    String outcome = caseOperationResult.getOutcome();
    // 非点呼设置失败电话结果
    if (isEarlyMediaRecEnabled && !Objects.equals(submitType, CaseOperationEnums.SubmitType.MANUAL.getCode()) && Objects.equals(outcome, "FAIL")) {
      vo.setOutcome("NO_RESPONSE");
    }
    // 点呼设置失败电话结果
    if (isEarlyMediaRecObEnabled && Objects.equals(submitType, CaseOperationEnums.SubmitType.MANUAL.getCode()) && Objects.equals(outcome, "FAIL")) {
      vo.setOutcome("NO_RESPONSE");
    }
    return vo;
  }

  public List<CaseOperationVO> getCaseOperationCallUuid(CaseOperationParam param) {
    param.setCallCenterRecord(true);
    PageOutput<CaseOperationVO> pageOutput = getByPage(param);
    List<CaseOperationVO> vos = pageOutput.getList();
    List<CaseOperationVO> result = new ArrayList<>();
    List<Case> cases = new ArrayList<>();
    List<String> recordNameRules = Arrays.asList(param.getRecordNameRule().split("-"));
    //由于自定义导出录音名称时：可能设置催员姓名或者案件编号，所以这里需要先在本地化查出来上传文件服务器供后续python脚本执行下载任务时使用
    if(recordNameRules.contains("caseName") || recordNameRules.contains("outSerialNo")) {
      List<Long> caseIds = vos.parallelStream().map(CaseOperationVO::getCaseId).collect(Collectors.toList());
      Example example = new Example(Case.class);
      example.and().andIn("id", caseIds);
      cases = caseMapper.selectByExample(example);
    }
    List<Case> finalCases = cases;
    vos.forEach(caseOperationVO -> {
      //说明有录音文件
      CaseOperationVO caseOperation = new CaseOperationVO();
      caseOperation.setId(caseOperationVO.getId());
      caseOperation.setSubmitType(caseOperationVO.getSubmitType());
      caseOperation.setCallUuid(caseOperationVO.getCallUuid());
      caseOperation.setCallTime(caseOperationVO.getCallTime());
      caseOperation.setOperatorName(caseOperationVO.getOperatorName());
      caseOperation.setConMobile(caseOperationVO.getConMobile());
      Case aCase = finalCases.parallelStream().filter(case1 -> Objects.equals(case1.getId(), caseOperationVO.getCaseId())).findFirst().orElse(null);
      caseOperation.setCaseName(aCase == null ? null : aCase.getName());
      caseOperation.setOutSerialNo(caseOperationVO.getOutSerialNo());
      result.add(caseOperation);
    });

    //List<String> callUuids = vos.parallelStream().filter(caseOperationVO -> StringUtils.isNotBlank(caseOperationVO.getCallUuid())).map(CaseOperationVO::getCallUuid).collect(Collectors.toList());
    return result;
  }

  public Integer getCaseOperationHaveCallRecordCount(CaseOperationParam param) {
    param.setCallRecord(1);
    param.setCallCenterRecord(true);
    Integer count = caseOperationMapper.getCaseOperationCount(param);
    return count;
  }

  private void decryptDataFormES(CaseOperationResult caseOperationResult) {
    if (!encryptProperties.getEnable()) {
      return;
    }
    caseOperationResult.setConMobile(encryptService.decrypt(caseOperationResult.getConMobile()));
    caseOperationResult.setOwnMobile(encryptService.decrypt(caseOperationResult.getOwnMobile()));
    caseOperationResult.setIdCard(encryptService.decrypt(caseOperationResult.getIdCard()));
    caseOperationResult.setConName(encryptService.decrypt(caseOperationResult.getConName()));
    caseOperationResult.setName(encryptService.decrypt(caseOperationResult.getName()));
  }

  public List<CaseOperationResult> selectByPage(CaseOperationParam param) {
    // trimMultiString(param);
    if (param.getStartStr() != null) {
      String[] times = param.getStartStr().split(",");
      param.setStartTime(new Date(Long.valueOf(times[0])));
      param.setEndTime(new Date(Long.valueOf(times[1])));
    }
    return caseOperationMapper.selectByMap(param);
  }

  /**
   * fjzheng批注：优化度言打电话回调安米生成对应催记接口，防止大批量数据操作接口超时
   * 按照现在度言那边的接口逻辑，不用等待我们的执行的返回结果，因为即时发生错误失败，也不会重试
   * 所以我们可以先返回，再异步执行生成催记方法逻辑代码。
   * 还需要注意情况：1.度言所有的打电话都会回调我们接口，因为度言那边不区分是电话记录是他们本地打的还是安米这边要求打的，所以有可能很多情况是失败的
   *              2.度言有个传案子关联参数配置开关，如果关了不传，那么我们这边拿不到案子关联参数而不能正常处理逻辑，最终失败
   *              3.度言那边只能设置一个回调地址，所以只能对应一个系统
   *
   * @param callback：度言回调的打电话信息参数(批量)
   * @return
   */
  public Boolean createByCallback(List<Campaigns> callback) {
    Boolean flag = true;
    try {
      List<CaseOperation> list = new ArrayList<>();
      for (Campaigns l : callback) {
        try {
          // SUCCESS：成功，FAIL：无人接听，USER_BUSY：用户忙，POWER_OFF：用户关机，SUSPENDED：用户停机，NOT_EXIST：用户空号
          int outcome = getOutcome(l.getOutcome());
          if (l.getSub_type().equals("ROBOT")) {
            if (StrUtil.isBlank(l.getVars())){
              log.info("没有vars参数：{}",l.getCampaign_id());
              continue;
            }
            CasePlan casePlan = casePlanService.selectCasePlanByDuyanPlanId(l.getCampaign_id());
            // 机器人计划
            JSONObject object = JSONObject.parseObject(l.getVars());
            Long caseId = object.getLong("U_caseId");
            Long orgId = object.getLong("U_orgId");
            log.info("回调案件ID为{}, 回调计划ID为{},orgId为{}", caseId, l.getCampaign_id(), orgId);
            if (ObjectUtil.isNull(caseId)||ObjectUtil.isNull(orgId)){
              continue;
            }

            int resultType = -1;
            String tagName = StrUtil.EMPTY;
            // 话术映射配置内容
            String bindingMapping = StrUtil.EMPTY;
            if (outcome == 0) { // 呼叫成功
              resultType = 2;
              tagName = casePlanService.getCallDetail(l, orgId, casePlan);
              if (StringUtils.isBlank(tagName)) { // 如果呼叫成功，然后没有返回tag
                tagName = "notag";
              }
              if (ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.NEWROBOT.getCode())){
                // 新机器人不需要转换
                bindingMapping = StringUtils.substring(tagName, 0, 250);
                tagName = StrUtil.EMPTY;
              }else {
                bindingMapping = casePlanService.getBindingMapping(l.getCampaign_id(), tagName);
              }
            } else { // 如果呼叫未成功
              if (ObjectUtil.notEqual(casePlan.getType(), CasePlanEnums.Type.NEWROBOT.getCode())){
                tagName = l.getOutcome();
              }
              bindingMapping = getOutcomeName(l.getOutcome());
            }
            Case caseTemp = caseService.selectByPrimaryKey(caseId);
            CaseOperation caseOperation = new CaseOperation();
            // 冗余案件字段
            caseOperation.setOrgId(caseTemp.getOrgId());
            caseOperation.setOrgDeltId(caseTemp.getOrgDeltId());
            caseOperation.setOutSerialNo(caseTemp.getOutSerialNo());
            caseOperation.setOutSerialTemp(caseTemp.getOutSerialTemp());
            caseOperation.setCallType(outcome); // 默认值，机器人计划看的是outcome
            caseOperation.setCaseId(caseId);
            caseOperation.setActionType(resultType);
            caseOperation.setCallUuid(
                    StringUtils.isBlank(l.getCall_uuid()) ? null : l.getCall_uuid());
            caseOperation.setCreateTime(new Date());
            caseOperation.setUpdateTime(new Date());
            caseOperation.setCreateBy(-1L);
            caseOperation.setAdminSubmitter(-1L);
            caseOperation.setDesc(bindingMapping);
            caseOperation.setUpdateBy(-1L);
            caseOperation.setStatus(0);
            caseOperation.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
            caseOperation.setCallTime(new Date(l.getCall_time()));
            caseOperation.setCallDurtion(l.getDuration());
            caseOperation.setRingDurtion(l.getRing_time());
            caseOperation.setOutcome(l.getOutcome());
            caseOperation.setSubmitType(CaseOperationEnums.SubmitType.ROBOT.getCode());
            caseOperation.setCaller(l.getCaller());
            caseOperation.setCallee(l.getCallee());
            caseOperation.setCreateType(CaseOperationEnums.CreateType.SYS.getCode());
            caseOperation.setTag(tagName); // 客户标签
            caseOperation.setConMobile(l.getCallee());
            caseOperation.setConName(caseTemp.getName());
            caseOperation.setRelationType("本人");
            Integer operTime = caseOperationMapper.selectTodayRobotCount(caseOperation.getCaseId());
            caseOperation.setOperTime(operTime + 1);
            Integer totalOperTime = caseOperationMapper.selectTotalRobotCount(caseOperation.getCaseId());
            caseOperation.setTotalOperTime(totalOperTime + 1);
            List<CaseContact> contacts=contactsMapper.selectContactByCaseIds(Collections.singletonList(caseId));
            if (contacts.size() > 0) {
              List<CaseContact> tmp = contacts.stream().filter(c -> c.getContactMobile() != null & c.getContactMobile().equals(l.getCallee()))
                      .collect(Collectors.toList());
              if (tmp.size() > 0) {
                caseOperation.setConName(tmp.get(0).getContactName());
                caseOperation.setRelationType(tmp.get(0).getRelationType());
              }
            }
            caseOperation.setOperationState(caseTemp.getOperationState());
            caseOperation.setCallStyle(CaseOperationEnums.CallStyle.OUT.getCode());
            caseOperation.setDuyanPlanId(l.getCampaign_id());
            caseOperation.setSiteId(l.getSite_id());
            dealSiteName(l, caseOperation);
            // 同步更新状态到case表
            Case ca = new Case();
            ca.setId(caseId);
            ca.setOperStatus(resultType);
            ca.setCallType(outcome);
//          ca.setFollowCount(caseTemp.getFollowCount() + 1);
//          ca.setLastFollowTime(new Date());
            ca.setAutoAssistDate(new Date(l.getCall_time())); // 最近智能协催时间
            ca.setAutoAssistResult(l.getOutcome()); // 最近智能协催电话结果
            ca.setAutoAssistRecord(bindingMapping); // // 智能协催催记
            ca.setTag(tagName); // 客户标签
            // 更新案件表中的承诺还款时间
            ca.setPtpTime(null);
            OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(orgId);
            Long caseCtrlPoolId = null;
            //案件管控开关
            if(OrgSwitchEnums.CaseCtrlSwitch.OPEN.getCode().equals(orgSwitch.getCtrlSwitch())){
              Map<Integer,List<CaseCtrlPoolCondition>> operMap = caseCtrlService.getOperCodeCtrlMap(caseTemp.getOrgId());
              if (operMap.containsKey(ca.getOperStatus()) && !CollectionUtils.isEmpty(operMap.get(ca.getOperStatus()))) {
                caseCtrlPoolId = operMap.get(ca.getOperStatus()).get(0).getId();
              }
            }
            ca.setCtrlId(caseCtrlPoolId);
            if(OrgSwitchEnums.CaseNoSwitch.OPEN.getCode().equals(orgSwitch.getCaseNoSwitch())){
              // 若调解有效，且未设置过留案号，则设置留案号
              CustomOperationStatus customOperationStatus = customOperationStatusService.queryCustomByCode(resultType, caseTemp.getOrgId());
              if (customOperationStatus != null && caseTemp.getDepId() != null
                      && Objects.equals(CustomOperationStatusEnums.IsEffective.YES.getCode(),customOperationStatus.getIsEffective())) {
                if (StringUtils.isBlank(caseTemp.getDelayNo())) {
                  String delayNo = caseService.addDelayNo(caseTemp.getDepId());
                  ca.setDelayNo(delayNo);
                }
              }
            }
            caseService.updateByPrimaryKeySelective(ca);

            //判断共债案件同步开关是否开启，如果开启则共债案件催收结果同步
            if (orgSwitch.getCaseSyncSwitch()
                    .equals(OrgSwitchEnums.CaseSyncSwitch.YES.getCode())) {
              if (caseTemp.getDebtId() != null && caseTemp.getOrgDeltId() != null) {
                // 查询当前委案公司下所有的共债案件
                Map map = new HashMap();
                map.put("orgId", orgId);
                map.put("orgDeltId", caseTemp.getOrgDeltId());
                map.put("debtId", caseTemp.getDebtId());
                List<Long> caseIds = caseMapper.selectConjointCaseId(map);
                if (!CommonUtils.isEmpty(caseIds)) {
                  // 同步
                  Map syncMap = new HashMap();
                  syncMap.put("caseIds", caseIds);
                  //催收结果，电话结果，催收进程，上次跟进时间，下次跟进时间，机器人协催时间，机器人协催结果，机器人协催催记，机器人客户标签
                  syncMap.put("operStatus", ca.getOperStatus());
                  syncMap.put("callStatus", caseTemp.getCallStatus());
                  syncMap.put("callType", ca.getCallType());
                  syncMap.put("lastFollowTime",new Date());
                  syncMap.put("autoAssistDate", ca.getAutoAssistDate());
                  syncMap.put("autoAssistResult", ca.getAutoAssistResult());
                  syncMap.put("autoAssistRecord", ca.getAutoAssistRecord());
                  syncMap.put("tag", ca.getTag());
                  syncMap.put("ctrlId",caseCtrlPoolId);
                  caseMapper.syncCaseOperationStatusRobot(syncMap);
                }
              }
            }
            list.add(caseOperation);

            // 设置债务人的上次跟进时间和跟进次数
//          syncCaseDebtor(caseTemp.getDebtId());
            //老机器人追加联系人
            if (casePlan != null && casePlan.getCallRule() != null && ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.ROBOT.getCode()) && (casePlan.getCallRule().equals(1) || outcome != 0)) {
              log.info("呼叫失败--追加联系人至预测式外呼计划");
              CasePlanRel query = new CasePlanRel();
              query.setCaseId(caseId);
              query.setPlanId(casePlan.getId());
              query.setOrgId(casePlan.getOrgId());
              CasePlanRel casePlanRel = casePlanRelService.selectFirst(query);
              if (casePlanRel != null) {
                List<CasePlanRelContact> casePlanRelContactList =
                        casePlanRelContactMapper.selectForAppend(casePlan.getId(),casePlanRel.getId());
                if (casePlanRelContactList.size() > 0) {
                  CasePlanRelContact casePlanRelContact = casePlanRelContactList.get(0);
                  List<CaseContact> caseContacts=contactsMapper.selectContactByContactId(casePlanRelContact.getContactId(), caseId);
                  duyanManager.batchAdd(casePlan.getOrgId(), casePlan.getDuyanPlanId(), caseContacts, casePlan.getType(),casePlan,new ArrayList<>());
                  casePlanRelContact.setCallFlag(1);
                  casePlanRelContactService.updateByPrimaryKey(casePlanRelContact);
                }
              }
            }

          } else if (l.getSub_type().equals("FCFS")) {
            if (StrUtil.isBlank(l.getCall_uuid())||StrUtil.isBlank(l.getVars())) {
              continue;
            }

            handleFifoOperation(l);
          } else {
            // 人为呼叫
            if (StringUtils.isBlank(l.getCall_uuid())) {
              continue;
            }
            // 催员打电话或外访员打电话
            String visitParams = null;
            if (!StringUtils.isBlank(l.getVars())) {
              JSONObject object = JSONObject.parseObject(l.getVars());
              String tag = object.getString("U_TAG");
              if (StringUtils.isNotBlank(tag) && tag.startsWith("VISIT")) {
                visitParams = tag;
              }
            }
            if (visitParams == null) {
              // 催员拨打电话
              CaseOperation operation = super.selectOne(new CaseOperation(l.getCall_uuid()));
              if (operation != null) {
                CaseOperation newOperation = new CaseOperation();
                newOperation.setId(operation.getId());
                newOperation.setCallUuid(l.getCall_uuid());
                newOperation.setCallTime(new Date(l.getCall_time()));
                newOperation.setCaller(l.getCaller());
                newOperation.setCallDurtion(l.getDuration() == null ? 0 : l.getDuration());
                newOperation.setRingDurtion(l.getRing_time() == null ? 0 : l.getRing_time());
                newOperation.setOutcome(l.getOutcome() == null ? "FAIL" : l.getOutcome());
                newOperation.setCallee(l.getCallee());
                // 电话结果为未填写时使用度言电话结果
                if (CaseOperationEnums.CallType.NOT_FILLED.getCode() == operation.getCallType()) {
                  newOperation.setCallType(
                          l.getOutcome() == null ? operation.getCallType() : outcome);
                }
                // 更新数据
                caseOperationService.updateByPrimaryKeySelective(newOperation);
                caseOperationService.syncCaseFollowInfo(operation.getCaseId());
                Case ca=caseService.selectByPrimaryKey(operation.getCaseId());
                caseOperationService.syncCaseDebtorFollowInfo(ca.getDebtId());
              } else {
                //根据度言回调的uuid查询催记不存在，这时候先记录下来，为了后续补填催记信息
                redisTemplate.opsForZSet().add(OPERATION_CALL_UUID, l.getCall_uuid(), System.currentTimeMillis());
                flag = false;
              }
            } else {
              // 外访员拨打电话
              handleVisitCallback(l, visitParams);
            }
          }
        } catch (Exception e) {
          log.error("度言这条数据回调填写催记失败，campaigns =" + JSON.toJSONString(l), e);
        }
      }
      if (!CommonUtils.isEmpty(list)) {
        log.info("催记保存成功");
        // 处理机器人催记是否需要推送
        List<Long> orgIds = list.parallelStream().map(CaseOperation::getOrgId).distinct().collect(Collectors.toList());
        List<Company> companyList = companyService.getCompanyList(orgIds);
        Map<Long, Company> orgMap = companyList.parallelStream().collect(Collectors.toMap(Company::getId, Function.identity()));
        List<Long> strategyIds = companyList.parallelStream().filter(company -> Objects.nonNull(company.getPushStrategyId()))
                .map(Company::getPushStrategyId).distinct().collect(Collectors.toList());
        List<PushStrategy> strategyList = pushStrategyService.getPushStrategyList(strategyIds);
        Map<Long, PushStrategy> strategyMap = strategyList.parallelStream().collect(Collectors.toMap(PushStrategy::getId, Function.identity()));
        List<Long> smsSwitchOrgList = orgSwitchMapper.selectCaseOperationSmsDayCatchOrgIds();
        list.forEach(caseOperation -> {
          // 只处理机器人催记
          if (!Objects.equals(caseOperation.getSubmitType(), CaseOperationEnums.SubmitType.ROBOT.getCode())) {
            return;
          }
          Long id = caseOperation.getId();
          Long orgId = caseOperation.getOrgId();
          Company company = orgMap.get(orgId);
          Long strategyId = company.getPushStrategyId();
          // 公司没有推送策略id，说明不需要推送
          if (Objects.isNull(strategyId)) {
            return;
          }
          PushStrategy strategy = strategyMap.get(strategyId);
          Integer period = strategy.getPeriod();
          long time = caseOperation.getCreateTime().getTime() + period * 60 * 1000;
          redisTemplate.opsForZSet().add(KeyCache.PUSH_CASE_OPERATION, id, time);

          if (smsSwitchOrgList.contains(orgId)) {
            //5 分钟后向duyan 拉取 sms
            Integer periodSms = 5;
            if (strategy != null) {
              periodSms = strategy.getPeriod();
            }
            long smsTime = caseOperation.getCreateTime().getTime() + periodSms * 60 * 1000;
            // 催记ID_是否推送状态_推送次数
            String arrStr = String.format("%s_%s_%s", caseOperation.getId(), 1, 1);
            // 这里使用stringRedisTemplate，因为这个会默认使用 StringRedisSerializer，否则会出现序列化问题
            stringRedisTemplate.opsForZSet().add(KeyCache.FETCH_CASE_OPERATION_SMS, arrStr, smsTime);
          }
        });
        super.insertBatch(list);
        //批量更新案件跟进次数和最后跟进时间
        List<Long> caseIds=list.stream().map(CaseOperation::getCaseId).collect(Collectors.toList());
        caseOperationMapper.updateCaseFollowInfoBatch(caseIds);
        //批量更新债务人跟进次数和最后跟进时间
        List<Long> debtIds=caseMapper.selectDebtIdsByCaseIds(caseIds);
        caseOperationMapper.updateDebtFollowInfoBatch(debtIds);
      }
    } catch (RuntimeException e) {
      log.error("回调安米数据异常,{}", e);
      flag = false;
    }
    return flag;
  }

  /**
   * 机器人新版回调
   *
   * @param callback 回调
   */
  public void createByRobotNewCallback(Campaigns callback) {
    try {
      // SUCCESS：成功，FAIL：无人接听，USER_BUSY：用户忙，POWER_OFF：用户关机，SUSPENDED：用户停机，NOT_EXIST：用户空号
      int outcome = getOutcome(callback.getOutcome());

      Map<String, String> variables = callback.getVariables();
      if (ObjectUtil.isEmpty(variables)){
        log.info("没有variables参数");
        return;
      }
      log.info("回调案件ID为{}, orgId为{}", variables.get("U_caseId"), variables.get("U_orgId"));
      if (StrUtil.isBlank(variables.get("U_caseId"))||StrUtil.isBlank(variables.get("U_orgId"))){
        return;
      }
      Long caseId = Long.valueOf(variables.get("U_caseId"));
      Long orgId = Long.valueOf(variables.get("U_orgId"));

      Long campaignId = callback.getCampaign_id();
      Integer submitType;

      if (ObjectUtil.isNotNull(campaignId)) {
        // 机器人计划
        submitType = CaseOperationEnums.SubmitType.ROBOT.getCode();
      } else {
        // 机器人点呼
        submitType = CaseOperationEnums.SubmitType.ROBOT_POINT.getCode();
      }

      int resultType = -1;
      // 话术映射配置内容
      String bindingMapping;
      if (outcome == 0) {
        // 呼叫成功
        resultType = 2;
        List<String> voiceSiteLabelList = Optional.ofNullable(callback.getVoice_site_labels()).orElse(new ArrayList<>());
        String voiceSiteLabels = StringUtils.join(voiceSiteLabelList, ",");
        if (StringUtils.isBlank(voiceSiteLabels)) {
          // 如果呼叫成功，然后没有返回tag
          voiceSiteLabels = "notag";
        }
        // 新机器人不需要转换
        bindingMapping = StringUtils.substring(voiceSiteLabels, 0, 250);
      } else {
        // 呼叫未成功
        bindingMapping = getOutcomeName(callback.getOutcome());
      }
      Case caseTemp = caseService.selectByPrimaryKey(caseId);
      CaseOperation caseOperation = new CaseOperation();
      caseOperation.setOrgId(caseTemp.getOrgId());
      caseOperation.setOrgDeltId(caseTemp.getOrgDeltId());
      caseOperation.setOutSerialNo(caseTemp.getOutSerialNo());
      caseOperation.setOutSerialTemp(caseTemp.getOutSerialTemp());
      // 默认值，机器人计划看的是outcome
      caseOperation.setCallType(outcome);
      caseOperation.setCaseId(caseId);
      caseOperation.setActionType(resultType);
      caseOperation.setCallUuid(StringUtils.isBlank(callback.getCall_uuid()) ? null : callback.getCall_uuid());
      caseOperation.setCreateTime(new Date());
      caseOperation.setUpdateTime(new Date());
      caseOperation.setCreateBy(-1L);
      caseOperation.setAdminSubmitter(-1L);
      caseOperation.setAutoAssistRecord(bindingMapping);
      caseOperation.setUpdateBy(-1L);
      caseOperation.setStatus(0);
      caseOperation.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
      caseOperation.setCallTime(new Date(callback.getCall_time()));
      caseOperation.setCallDurtion(callback.getDuration());
      caseOperation.setRingDurtion(callback.getRing_time());
      caseOperation.setOutcome(callback.getOutcome());
      caseOperation.setSubmitType(submitType);
      caseOperation.setCaller(callback.getCaller());
      caseOperation.setCallee(callback.getPhone());
      caseOperation.setCreateType(CaseOperationEnums.CreateType.SYS.getCode());
      // 客户标签
      caseOperation.setConMobile(callback.getPhone());
      caseOperation.setConName(caseTemp.getName());
      caseOperation.setRelationType("本人");
      // 客户意向
      caseOperation.setIntention(callback.getIntention());
      caseOperation.setIntentionName(callback.getIntention_name());
      List<CaseContact> contacts=contactsMapper.selectContactByCaseIds(Collections.singletonList(caseId));
      if (contacts.size() > 0) {
        List<CaseContact> tmp = contacts.stream().filter(c -> c.getContactMobile() != null & c.getContactMobile().equals(callback.getCallee()))
                .collect(Collectors.toList());
        if (tmp.size() > 0) {
          caseOperation.setConName(tmp.get(0).getContactName());
          caseOperation.setRelationType(tmp.get(0).getRelationType());
        }
      }
      caseOperation.setOperationState(caseTemp.getOperationState());
      caseOperation.setCallStyle(CaseOperationEnums.CallStyle.OUT.getCode());
      caseOperation.setDuyanPlanId(campaignId);
      caseOperation.setSiteId(callback.getSite_id());
      dealSiteName(callback, caseOperation);
      // 同步更新状态到case表
      Case ca = new Case();
      ca.setId(caseId);
      ca.setOperStatus(resultType);
      ca.setCallType(outcome);
      // 最近智能协催时间
      ca.setAutoAssistDate(new Date(callback.getCall_time()));
      // 最近智能协催电话结果
      ca.setAutoAssistResult(callback.getOutcome());
      // // 智能协催催记
      ca.setAutoAssistRecord(bindingMapping);
      // 更新案件表中的承诺还款时间
      ca.setPtpTime(null);
      OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(orgId);
      Long caseCtrlPoolId = null;
      //案件管控开关
      if(OrgSwitchEnums.CaseCtrlSwitch.OPEN.getCode().equals(orgSwitch.getCtrlSwitch())){
        Map<Integer,List<CaseCtrlPoolCondition>> operMap = caseCtrlService.getOperCodeCtrlMap(caseTemp.getOrgId());
        if (operMap.containsKey(ca.getOperStatus()) && !CollectionUtils.isEmpty(operMap.get(ca.getOperStatus()))) {
          caseCtrlPoolId = operMap.get(ca.getOperStatus()).get(0).getId();
        }
      }
      ca.setCtrlId(caseCtrlPoolId);
      if(OrgSwitchEnums.CaseNoSwitch.OPEN.getCode().equals(orgSwitch.getCaseNoSwitch())){
        // 若调解有效，且未设置过留案号，则设置留案号
        CustomOperationStatus customOperationStatus = customOperationStatusService.queryCustomByCode(resultType, caseTemp.getOrgId());
        if (customOperationStatus != null && caseTemp.getDepId() != null
                && Objects.equals(CustomOperationStatusEnums.IsEffective.YES.getCode(),customOperationStatus.getIsEffective())) {
          if (StringUtils.isBlank(caseTemp.getDelayNo())) {
            String delayNo = caseService.addDelayNo(caseTemp.getDepId());
            ca.setDelayNo(delayNo);
          }
        }
      }
      caseService.updateByPrimaryKeySelective(ca);

      //判断共债案件同步开关是否开启，如果开启则共债案件催收结果同步
      if (orgSwitch.getCaseSyncSwitch().equals(OrgSwitchEnums.CaseSyncSwitch.YES.getCode())) {
        if (caseTemp.getDebtId() != null && caseTemp.getOrgDeltId() != null) {
          // 查询当前委案公司下所有的共债案件
          Map map = new HashMap();
          map.put("orgId", orgId);
          map.put("orgDeltId", caseTemp.getOrgDeltId());
          map.put("debtId", caseTemp.getDebtId());
          List<Long> caseIds = caseMapper.selectConjointCaseId(map);
          if (!CommonUtils.isEmpty(caseIds)) {
            // 同步
            Map syncMap = new HashMap();
            syncMap.put("caseIds", caseIds);
            //催收结果，电话结果，催收进程，上次跟进时间，下次跟进时间，机器人协催时间，机器人协催结果，机器人协催催记，机器人客户标签
            syncMap.put("operStatus", ca.getOperStatus());
            syncMap.put("callStatus", caseTemp.getCallStatus());
            syncMap.put("callType", ca.getCallType());
            syncMap.put("lastFollowTime",new Date());
            syncMap.put("autoAssistDate", ca.getAutoAssistDate());
            syncMap.put("autoAssistResult", ca.getAutoAssistResult());
            syncMap.put("autoAssistRecord", ca.getAutoAssistRecord());
            syncMap.put("tag", ca.getTag());
            syncMap.put("ctrlId",caseCtrlPoolId);
            caseMapper.syncCaseOperationStatusRobot(syncMap);
          }
        }
      }
      Company company = companyService.selectByPrimaryKey(orgId);
      Long strategyId = company.getPushStrategyId();
      if (submitType == CaseOperationEnums.SubmitType.ROBOT.getCode()) {
        Integer operTime = caseOperationMapper.selectTodayRobotCount(caseOperation.getCaseId());
        caseOperation.setOperTime(operTime + 1);
        Integer totalOperTime = caseOperationMapper.selectTotalRobotCount(caseOperation.getCaseId());
        caseOperation.setTotalOperTime(totalOperTime + 1);
        //机器人计划回填催记以前是新增，但是现在支持机器人转人工了就不一定是新增了
        CaseOperationResult caseOperationOld = this.selectByCallUuid(callback.getCall_uuid());
        if (Objects.isNull(caseOperationOld)) {
          // 为空新增
          insertSelective(caseOperation);
        } else {
          // 不为空(机器人转人工)，更新
          CaseOperation operation = new CaseOperation();
          operation.setId(caseOperationOld.getId());
          // 当日协催次数
          operation.setOperTime(operTime + 1);
          // 总协催次数
          operation.setTotalOperTime(totalOperTime + 1);
          // 机器人协催标签
          operation.setAutoAssistRecord(bindingMapping);
          operation.setCallTime(new Date(callback.getCall_time()));
          operation.setCallDurtion(callback.getDuration());
          operation.setRingDurtion(callback.getRing_time());
          operation.setOutcome(callback.getOutcome());
          operation.setSubmitType(submitType);
          operation.setCaller(callback.getCaller());
          operation.setCallee(callback.getPhone());
          operation.setCreateType(CaseOperationEnums.CreateType.SYS.getCode());
          operation.setCallStyle(CaseOperationEnums.CallStyle.OUT.getCode());
          operation.setDuyanPlanId(campaignId);
          operation.setUpdateTime(new Date());
          operation.setSiteId(callback.getSite_id());
          dealSiteName(callback, caseOperationOld);
          // 客户意向
          if (StringUtils.isNotBlank(callback.getIntention())) {
            operation.setIntention(callback.getIntention());
            operation.setIntentionName(callback.getIntention_name());
          }

          this.updateByPrimaryKeySelective(operation);
        }
        // 公司没有推送策略id，说明不需要推送
        PushStrategy strategy = null;
        if (Objects.nonNull(strategyId)) {
          strategy = pushStrategyService.getPushStrategy(strategyId);
          Integer period = strategy.getPeriod();
          long time = caseOperation.getCreateTime().getTime() + period * 60 * 1000;
          redisTemplate.opsForZSet().add(KeyCache.PUSH_CASE_OPERATION, caseOperation.getId(), time);
        }

        if (orgSwitch.getRobotSmsSaveSwitch() == 1) {
          Integer period = 5;
          if (Objects.nonNull(strategyId)) {
            period = strategy.getPeriod();
          }
          //5 分钟后向duyan 拉取 sms
          long smsTime = caseOperation.getCreateTime().getTime() + period * 60 * 1000;
          // 催记ID_是否推送状态_推送次数
          String arrStr = String.format("%s_%s_%s", caseOperation.getId(), 1, 1);
          stringRedisTemplate.opsForZSet().add(KeyCache.FETCH_CASE_OPERATION_SMS, arrStr, smsTime);
        }

      } else {
        //机器人点呼更新催记字段
        if (StringUtils.isBlank(callback.getCall_uuid())) {
          log.error("回调没有call_uuid,回调失败");
          return;
        }
        CaseOperation caseOperationOld = this.selectByCallUuid(callback.getCall_uuid());
        if (Objects.isNull(caseOperationOld)) {
          log.error("找不到uuid：" + callback.getCall_uuid());
          return;
        }
        CaseOperation updater = new CaseOperation();
        updater.setId(caseOperationOld.getId());
        updater.setCallType(outcome);
        updater.setActionType(resultType);
        updater.setAutoAssistRecord(bindingMapping);
        updater.setCallTime(new Date(callback.getCall_time()));
        updater.setCallDurtion(callback.getDuration());
        updater.setRingDurtion(callback.getRing_time());
        updater.setOutcome(callback.getOutcome());
        updater.setCaller(callback.getCaller());
        updater.setOperationState(caseTemp.getOperationState());
        updater.setCallbackFlag(1);
        // 公司没有推送策略id，说明不需要推送
        if (Objects.nonNull(strategyId)) {
          PushStrategy strategy = pushStrategyService.getPushStrategy(strategyId);
          Integer period = strategy.getPeriod();
          long time = caseOperationOld.getCreateTime().getTime() + period * 60 * 1000;
          long diff = System.currentTimeMillis() - time;
          // 超过一个周期时间的回调不再做推送处理
          if (diff <= period * 60 * 1000) {
            redisTemplate.opsForZSet().add(KeyCache.PUSH_CASE_OPERATION, caseOperationOld.getId(), time);
          } else {
            updater.setIsHandlePush(1);
          }
        }
        this.updateByPrimaryKeySelective(updater);
        redisTemplate.opsForZSet().remove(RobotQueueService.CASE_OPERATION_TIMEOUT_KEY,String.valueOf(updater.getId()));
      }
      // 对话内容为空不进行保存
      if (StringUtils.isNotBlank(callback.getDialog()) && StringUtils.isNotBlank(callback.getCall_uuid())) {
        List<DialogInfo> dialogInfos = dialogService.queryDialogByCallUuids(Lists.newArrayList(callback.getCall_uuid()));
        DialogInfo dialogInfo = new DialogInfo();
        dialogInfo.setDialog(callback.getDialog());
        dialogInfo.setCallUuid(callback.getCall_uuid());
        dialogInfo.setUpdateTime(new Date());
        if (CollectionUtils.isEmpty(dialogInfos)) {
          dialogInfo.setCreateTime(new Date());
          dialogService.insertSelective(dialogInfo);
        } else {
          Example example = new Example(DialogInfo.class);
          example.and().andEqualTo("callUuid", callback.getCall_uuid());
          dialogService.updateByExampleSelective(dialogInfo, example);
        }

        List<Long> pageNodesSaveSwitchOrgIds = orgSwitchMapper.selectRobotPageNodesSaveSwitchOrgIds();
        if (pageNodesSaveSwitchOrgIds.contains(orgId)) {
          try {
            List<RobotPageNodesRes> robotPageNodesResList = newRobotManager.getRobotPageNodes(callback.getCall_uuid(), orgId);
            if (robotPageNodesResList != null && !robotPageNodesResList.isEmpty()) {
              DialogInfo dialogInfoUpdate = new DialogInfo();
              dialogInfoUpdate.setPageNodes(JSONUtil.toJsonStr(robotPageNodesResList));
              dialogInfoUpdate.setCallUuid(callback.getCall_uuid());
              dialogService.updateDialogSelectiveByCallUuid(dialogInfoUpdate);
            }
          } catch (Exception e) {
            log.error("机器人新版回调异常|调用机器人话术节点失败", e);
          }
        }
      }

      //批量更新案件跟进次数和最后跟进时间
      List<Long> caseIds= Collections.singletonList(caseOperation.getCaseId());
      caseOperationMapper.updateCaseFollowInfoBatch(caseIds);
      //批量更新债务人跟进次数和最后跟进时间
      List<Long> debtIds=caseMapper.selectDebtIdsByCaseIds(caseIds);
      caseOperationMapper.updateDebtFollowInfoBatch(debtIds);
    } catch (RuntimeException e) {
      log.error("机器人新版回调异常", e);
    }
  }

  private void dealSiteName(Campaigns callback, CaseOperation caseOperation) {
    if (ObjectUtil.isNotNull(callback.getSite_id())) {
      String siteName = stringRedisTemplate.opsForValue().get(KeyCache.SITE_NAME + callback.getSite_id());
      if (StringUtils.isBlank(siteName)) {
        List<VoiceSite> sites = duyanManager.getRobotSitesWithoutToken(caseOperation.getOrgId());
        Optional<VoiceSite> site = sites.stream().filter(s -> s.getId().equals(callback.getSite_id())).findFirst();
        if (site.isPresent()) {
          siteName = site.get().getName();
          stringRedisTemplate.opsForValue().set(KeyCache.SITE_NAME + callback.getSite_id(), siteName, 24, TimeUnit.HOURS);
        }
      }
      caseOperation.setSiteName(siteName);
    }
  }

  public CaseOperationVO getCaseOperation(String callUuid) {
    if (StringUtils.isBlank(callUuid)) {
      return null;
    }
    UserSession userSession = UserUtils.getTokenUser();
    CaseOperation query = new CaseOperation();
    query.setOrgId(userSession.getOrgId());
    query.setCallUuid(callUuid);
    CaseOperation operation = caseOperationMapper.selectOne(query);
    if (Objects.isNull(operation)) {
      CaseOperationUseLess q = new CaseOperationUseLess();
      q.setOrgId(userSession.getOrgId());
      q.setCallUuid(callUuid);
      CaseOperationUseLess caseOperationUseLess = caseOperationUseLessMapper.selectOne(q);
      operation = AnmiBeanutils.copy(caseOperationUseLess, CaseOperation.class);
    }
    if (Objects.isNull(operation)) {
      return null;
    }
    CaseOperationResult caseOperationResult = AnmiBeanutils.copy(operation, CaseOperationResult.class);
    Long caseId = caseOperationResult.getCaseId();
    Case ca = caseService.selectByPrimaryKey(caseId);
    caseOperationResult.setProductId(ca.getProductId());
    caseOperationResult.setUserId(ca.getUserId());
    caseOperationResult.setTeamId(ca.getTeamId());
    caseOperationResult.setDepId(ca.getDepId());
    caseOperationResult.setName(ca.getName());
    caseOperationResult.setIdCard(ca.getIdCard());
    caseOperationResult.setOwnMobile(ca.getOwnMobile());
    caseOperationResult.setOutBatchId(ca.getOutBatchId());
    caseOperationResult.setInnerBatchId(ca.getInnerBatchId());
    caseOperationResult.setFieldJson(ca.getFieldJson());
    Map<Integer, String> operationStatusNameMap =
            customOperationStatusService.getNames(userSession.getOrgId());
    Map<Integer, String> operationStateNamesMap =orgConfigService
            .getAllNames(userSession.getOrgId(),OrgConfigEnums.Type.OPERATION_STATE.getCode());
    Map<Integer, String> callTypeNamesMap =orgConfigService
            .getAllNames(userSession.getOrgId(),OrgConfigEnums.Type.CALL_TYPE.getCode());
    Map<Long,String> depTeamNameMap=depTeamService.getNames();
    Map<Long,String> outBatchNoMap=outBatchService.getNames();
    String recEnabled = stringRedisTemplate.opsForValue().get(KeyCache.IS_EARLY_MEDIA_REC_ENABLED);
    Boolean isEarlyMediaRecEnabled = recEnabled == null ? false : Boolean.valueOf(recEnabled);
    String recObEnabled = stringRedisTemplate.opsForValue().get(KeyCache.IS_EARLY_MEDIA_REC_OB_ENABLED);
    Boolean isEarlyMediaRecObEnabled = recObEnabled == null ? false : Boolean.valueOf(recObEnabled);
    // 查询所有管理员信息
    Map<Long, String> adminMap = new HashMap<>();
    if(caseOperationResult.getAdminSubmitter() != null) {
      User user = userService.selectByPrimaryKey(caseOperationResult.getAdminSubmitter());
      adminMap.put(caseOperationResult.getAdminSubmitter(), user.getName());
    }
    Map<Long,String> productMap = productService.getNames();
    Map<Long,String> deltMap = deltService.getNames();
    Map<Long,String> customClientLabelMap = customClientLabelService.getNames(userSession.getOrgId());
    CaseOperationVO vo = convertToVO(caseOperationResult,
            operationStatusNameMap,
            operationStateNamesMap,
            callTypeNamesMap,
            depTeamNameMap,
            outBatchNoMap,
            adminMap,
            isEarlyMediaRecEnabled,
            isEarlyMediaRecObEnabled,productMap,deltMap,customClientLabelMap);
    return vo;
  }

  @Transactional(rollbackFor = Exception.class)
  public void addCaseOperationComment(CaseOperationOperate operate) {
    String callUuid = operate.getCallUuid();
    CaseOperation caseOperation = new CaseOperation();
    caseOperation.setComment(operate.getComment());
    caseOperation.setUpdateTime(new Date());
    Example example = new Example(CaseOperation.class);
    example.and().andEqualTo("callUuid", callUuid);
    int updateCaseOperationResult = caseOperationMapper.updateByExampleSelective(caseOperation, example);

    if (updateCaseOperationResult<1){
      CaseOperationUseLess caseOperationUseLess = new CaseOperationUseLess();
      caseOperationUseLess.setComment(operate.getComment());
      caseOperationUseLess.setUpdateTime(new Date());
      Example exampleUseLess = new Example(CaseOperationUseLess.class);
      exampleUseLess.and().andEqualTo("callUuid", callUuid);
      caseOperationUseLessMapper.updateByExampleSelective(caseOperationUseLess, exampleUseLess);
    }
  }

  public void addOperationVoiceInfo(String callUuid) {
    CaseOperationEnums.DataType dataType = CaseOperationEnums.DataType.HEAT;
    CaseOperation operation = super.selectOne(new CaseOperation(callUuid));
    if (ObjectUtil.isNull(operation)) {
      dataType = CaseOperationEnums.DataType.COLD;
      CaseOperationUseLess caseOperationUseLess = caseOperationUseLessMapper.selectOne(new CaseOperationUseLess(callUuid));
      try {
        operation = BeanUtil.copyProperties(caseOperationUseLess, CaseOperation.class);
      }catch (Exception e){
        log.info(ExceptionUtil.stacktraceToString(e));
      }
    }
    if (operation == null) {
      throw new ApiException("催记不存在");
    }
    Case ca=caseService.selectByPrimaryKey(operation.getCaseId());
    Company company = companyService.selectByPrimaryKey(ca.getOrgId());
    Long duyanReferId = company.getDuyanReferId();
    Campaigns campaigns = duyanManager.getCallInfo(callUuid, duyanReferId);

    addOperationVoiceInfo(operation,campaigns,dataType);

    caseOperationService.syncCaseFollowInfo(operation.getCaseId());
    caseOperationService.syncCaseDebtorFollowInfo(ca.getDebtId());
  }

  private void addOperationVoiceInfo(CaseOperation operation, Campaigns campaigns, CaseOperationEnums.DataType dataType) {
    if(ObjectUtil.equal(dataType,CaseOperationEnums.DataType.HEAT)){
      addOperationVoiceInfoHeat(operation,campaigns);
    }else {
      addOperationVoiceInfoCold(operation,campaigns);
    }
  }

  private void addOperationVoiceInfoHeat(CaseOperation operation, Campaigns campaigns) {
    CaseOperation newOperation = new CaseOperation();
    newOperation.setId(operation.getId());
    newOperation.setCallUuid(campaigns.getCall_uuid());
    newOperation.setCallTime(new Date(campaigns.getCall_time()));
    newOperation.setCaller(campaigns.getCaller());
    newOperation.setCallDurtion(campaigns.getDuration() == null ? 0 : campaigns.getDuration());
    newOperation.setRingDurtion(campaigns.getRing_time() == null ? 0 : campaigns.getRing_time());
    newOperation.setOutcome(campaigns.getOutcome() == null ? "FAIL" : campaigns.getOutcome());
    newOperation.setCallee(campaigns.getCallee());
    // 电话结果为未填写时使用度言电话结果
    if (CaseOperationEnums.CallType.NOT_FILLED.getCode() == operation.getCallType()) {
      int outcome = caseOperationService.getOutcome(campaigns.getOutcome());
      newOperation.setCallType(
              campaigns.getOutcome() == null ? operation.getCallType() : outcome);
    }
    // 更新数据
    newOperation.setUpdateTime(new Date());
    caseOperationService.updateByPrimaryKeySelective(newOperation);
  }

  private void addOperationVoiceInfoCold(CaseOperation operation, Campaigns campaigns) {
    CaseOperationUseLess newOperation = new CaseOperationUseLess();
    newOperation.setId(operation.getId());
    newOperation.setCallUuid(campaigns.getCall_uuid());
    newOperation.setCallTime(new Date(campaigns.getCall_time()));
    newOperation.setCaller(campaigns.getCaller());
    newOperation.setCallDurtion(campaigns.getDuration() == null ? 0 : campaigns.getDuration());
    newOperation.setRingDurtion(campaigns.getRing_time() == null ? 0 : campaigns.getRing_time());
    newOperation.setOutcome(campaigns.getOutcome() == null ? "FAIL" : campaigns.getOutcome());
    newOperation.setCallee(campaigns.getCallee());
    // 电话结果为未填写时使用度言电话结果
    if (CaseOperationEnums.CallType.NOT_FILLED.getCode() == operation.getCallType()) {
      int outcome = caseOperationService.getOutcome(campaigns.getOutcome());
      newOperation.setCallType(
              campaigns.getOutcome() == null ? operation.getCallType() : outcome);
    }
    // 更新数据
    newOperation.setUpdateTime(new Date());
    caseOperationUseLessMapper.updateByPrimaryKeySelective(newOperation);
  }


  // 表合并修改
  public void handleFifoOperation(Campaigns campaigns) {
    if (caseCallbackService.isExist(campaigns.getCall_uuid())) {
      return;
    }
    JSONObject object = JSONObject.parseObject(campaigns.getVars());
    String tag = object.getString("U_TAG");
    if (StrUtil.isBlank(tag)){
      return;
    }
    // 度言只支持中文逗号分隔
    String[] tags = tag.split("，");
    if (tags.length<2) {
      return;
    }
    Long caseId = Long.valueOf(tags[0]);
    Integer allotAgent = Integer.valueOf(tags[1]);
    log.info("预测式外呼计划回调案件ID为{}, 回调计划ID为{}", caseId, campaigns.getCampaign_id());
    Case caseTemp = caseService.selectByPrimaryKey(caseId);
    CasePlan casePlan = casePlanService.selectCasePlanByDuyanPlanId(campaigns.getCampaign_id());
    if (ObjectUtil.isNull(caseTemp)||ObjectUtil.isNull(casePlan)) {
      log.warn("未发现对应的案件 或 计划,回调案件ID为{}, 回调计划ID为{}", caseId, campaigns.getCampaign_id());
      return;
    }
    CaseOperation caseOperation = super.selectOne(new CaseOperation(campaigns.getCall_uuid()));
    int outcome = getOutcome(campaigns.getOutcome());
    int resultType = -1;
    String tagName = StrUtil.EMPTY;
    String bindingMapping = StrUtil.EMPTY;
    if (outcome == 0) { // 呼叫成功
      resultType = 2;
      if (ObjectUtil.equals(casePlan.getFifoToRobotEnabled(), CasePlanEnums.FifoToRobotEnabled.OPEN.getCode())) {
        tagName = casePlanService.getCallDetail(campaigns, casePlan.getOrgId(), casePlan);
        if (StringUtils.isBlank(tagName)) { // 如果呼叫成功，然后没有返回tag
          tagName = StrUtil.EMPTY;
        }
        // 新机器人不需要转换
        bindingMapping = StringUtils.substring(tagName, 0, 250);
        tagName = StrUtil.EMPTY;
      }
    } else {
      // 呼叫未成功
      if (ObjectUtil.equals(casePlan.getFifoToRobotEnabled(), CasePlanEnums.FifoToRobotEnabled.OPEN.getCode())){
        tagName = campaigns.getOutcome();
      }
      bindingMapping = getOutcomeName(campaigns.getOutcome());
    }

    if (allotAgent.equals(CasePlanEnums.AllotAgent.YES.getCode())) {
      // 分配至组案件是否分配给接听催员
      if (caseTemp.getAllotStatus().equals(CaseEnums.AllotStatus.ALLOT_TEAM.getCode()) && caseOperation != null) {
        Case updateCase = new Case();
        updateCase.setId(caseId);
        updateCase.setUserId(caseOperation.getCreateBy());
        updateCase.setAllotStatus(CaseEnums.AllotStatus.ALLOT_USER.getCode());
        updateCase.setDivisionTime(new Date());
        caseService.updateByPrimaryKeySelective(updateCase);
        List<Long> caseIds=Collections.singletonList(caseId);
        User dunner=new User();
        dunner.setOrgId(caseTemp.getOrgId());
        dunner.setId(caseOperation.getCreateBy());
        dunner.setDepId(caseTemp.getDepId());
        dunner.setTeamId(caseTemp.getTeamId());
        dunner.setName(userService.getNames(caseTemp.getOrgId()).get(caseOperation.getCreateBy()));
        applicationContext.publishEvent(new CaseAllotEvent(this, caseIds, dunner,dunner, -1L));
      }
    }
    CaseOperation newOperation = new CaseOperation();
    newOperation.setCaseId(caseId);
    newOperation.setActionType(resultType);
    newOperation.setCallType(outcome);
    newOperation.setCallUuid(campaigns.getCall_uuid());
    newOperation.setCallStyle(CaseOperationEnums.CallStyle.OUT.getCode());
    newOperation.setCallTime(new Date(campaigns.getCall_time()));
    newOperation.setCallDurtion(campaigns.getDuration() == null ? 0 : campaigns.getDuration());
    newOperation.setRingDurtion(campaigns.getRing_time() == null ? 0 : campaigns.getRing_time());
    newOperation.setOutcome(campaigns.getOutcome() == null ? "FAIL" : campaigns.getOutcome());
    newOperation.setCallee(campaigns.getCallee());
    newOperation.setCaller(campaigns.getCaller());
    newOperation.setConMobile(campaigns.getCallee());
    newOperation.setConName(caseTemp.getName());
    if (caseTemp.getUserId() == null) {
      newOperation.setCreateBy(-1L);
      newOperation.setAdminSubmitter(-1L);
    } else {
      newOperation.setCreateBy(caseTemp.getUserId());
      newOperation.setAdminSubmitter(caseTemp.getUserId());
    }
    newOperation.setRelationType("本人");
    newOperation.setTag(tagName);
    newOperation.setDesc(bindingMapping);
    List<CaseContact> contacts=contactsMapper.selectContactByCaseIds(Collections.singletonList(caseId));
    if (contacts.size() > 0) {
      List<CaseContact> tmp = contacts.stream().filter(c -> c.getContactMobile() != null & c.getContactMobile().equals(campaigns.getCallee()))
          .collect(Collectors.toList());
      if (tmp.size() > 0) {
        newOperation.setConName(tmp.get(0).getContactName());
        newOperation.setRelationType(tmp.get(0).getRelationType());
      }
    }
    newOperation.setSubmitType(CaseOperationEnums.SubmitType.FIFO.getCode());
    newOperation.setDuyanPlanId(campaigns.getCampaign_id());
    newOperation.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
    newOperation.setOrgId(caseTemp.getOrgId());
    newOperation.setOrgDeltId(caseTemp.getOrgDeltId());
    newOperation.setOutSerialNo(caseTemp.getOutSerialNo());
    newOperation.setOutSerialTemp(caseTemp.getOutSerialTemp());
    // 更新数据
    int i = caseOperationMapper.insertOrUpdateFifo(newOperation);

    // 当insertOrUpdateFifo为插入时
    if (i == 1) {
      Case ca = new Case();
      ca.setId(caseId);
      ca.setCallType(outcome);
      // 更新案件表中承诺还款时间
      ca.setPtpTime(null);
      ca.setOperStatus(resultType);
      // 最近智能协催时间
      ca.setAutoAssistDate(new Date(campaigns.getCampaign_id()));
      // 智能协催催记
      ca.setAutoAssistRecord(bindingMapping);
      // 客户标签
      ca.setTag(tagName);
      OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(caseTemp.getOrgId());
      //案件管控开关
      if (OrgSwitchEnums.CaseCtrlSwitch.OPEN.getCode().equals(orgSwitch.getCtrlSwitch())) {
        Map<Integer, List<CaseCtrlPoolCondition>> operMap = caseCtrlService.getOperCodeCtrlMap(caseTemp.getOrgId());
        if (operMap.containsKey(ca.getOperStatus()) && !CollectionUtils.isEmpty(operMap.get(ca.getOperStatus()))) {
          ca.setCtrlId(operMap.get(ca.getOperStatus()).get(0).getId());
        }
      }
      if (OrgSwitchEnums.CaseNoSwitch.OPEN.getCode().equals(orgSwitch.getCaseNoSwitch())) {
        // 若调解有效，且未设置过留案号，则设置留案号
        CustomOperationStatus customOperationStatus = customOperationStatusService.queryCustomByCode(resultType, caseTemp.getOrgId());
        if (customOperationStatus != null && caseTemp.getDepId() != null
                && Objects.equals(CustomOperationStatusEnums.IsEffective.YES.getCode(),customOperationStatus.getIsEffective())) {
          if (StringUtils.isBlank(caseTemp.getDelayNo())) {
            String delayNo = caseService.addDelayNo(caseTemp.getDepId());
            ca.setDelayNo(delayNo);
          }
        }
      }
      caseService.updateByPrimaryKeySelective(ca);
    }

    caseOperationService.syncCaseFollowInfo(caseId);
    Case caseInfo=caseService.selectByPrimaryKey(caseId);
    caseOperationService.syncCaseDebtorFollowInfo(caseInfo.getDebtId());

    callbackAdd(casePlan, caseId, outcome, campaigns);
  }

  public void callbackAdd(CasePlan casePlan, Long caseId, Integer outcome,Campaigns campaigns) {
    //追加联系人
    if (casePlan != null && casePlan.getCallRule() != null && (casePlan.getCallRule().equals(1) || outcome != 0)) {
      log.info("呼叫失败--追加联系人至预测式外呼计划");
      CasePlanRel query = new CasePlanRel();
      query.setCaseId(caseId);
      query.setPlanId(casePlan.getId());
      query.setOrgId(casePlan.getOrgId());
      CasePlanRel casePlanRel = casePlanRelService.selectFirst(query);
      if (casePlanRel != null) {
        Example example = new Example(CasePlanRelContact.class);
        example.and().andEqualTo("casePlanId", casePlan.getId());
        example.and().andEqualTo("casePlanRelId", casePlanRel.getId());
        //查询未发送到度言的id
        example.and().andEqualTo("callFlag", 0);
        example.orderBy("id").asc();
        List<CasePlanRelContact> casePlanRelContactList = casePlanRelContactMapper.selectByExample(example);
        // 追加标识 true标识追加成功，管控过滤后，追加号码设置call_flag=1,循环获取未被管控过滤掉的号码
        boolean addFlag = false;
        for (CasePlanRelContact casePlanRelContact : casePlanRelContactList) {
          //追加
          List<CaseContact> caseContacts = contactsMapper.selectContactByContactId(casePlanRelContact.getContactId(), caseId);
          OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(casePlan.getOrgId());
          // 全局管控-单案件校验
          Case aCase = caseService.selectByPrimaryKey(caseId);
          List<Long> caseIds = casePlanService.getGlobalCtrlLimitCase(Collections.singletonList(aCase), orgSwitch);
          if (!CollectionUtils.isEmpty(caseIds) && caseIds.contains(caseId)) {
            log.info("预测式外呼全局管控-单案件限制管控移除，计划id{},移除案件id{}",casePlan.getId(), caseId);
            caseContacts.clear();
          }
          // 全局管控-单号码校验
          List<CaseContact> mobileLimitContact = casePlanService.getMobileLimitContact(caseContacts, orgSwitch);
          if (!CollectionUtils.isEmpty(mobileLimitContact)) {
            log.info("预测式外呼全局管控-单号码限制管控移除，计划id{},移除号码数量{}",casePlan.getId(), mobileLimitContact.size());
            caseContacts.removeAll(mobileLimitContact);
          }
          // 管控池
          List<CaseContact> caseCtrlLimit = casePlanService.getCaseCtrlLimit(caseContacts, orgSwitch);
          if (!CollectionUtils.isEmpty(caseCtrlLimit)) {
            log.info("预测式外呼管控池-管控移除，计划id{},移除号码数量{}",casePlan.getId(), caseCtrlLimit.size());
            caseContacts.removeAll(caseCtrlLimit);
          }
          // 管控池、全局管控移除后，不进行号码追加
          if (!CollectionUtils.isEmpty(caseContacts)) {
            List<Map<String, Object>> contents = assembleContents(casePlan, caseContacts);
            duyanManager.newFifoAppend(casePlan.getOrgId(), campaigns.getCampaign_id(), caseContacts, contents);
            addFlag = true;
          }
          casePlanRelContact.setCallFlag(1);
          casePlanRelContact.setUpdateTime(new Date());
          casePlanRelContactService.updateByPrimaryKey(casePlanRelContact);
          if (addFlag) {
            break;
          }
        }
      }
    }
  }

  public List<Map<String,Object>> assembleContents(CasePlan casePlan, List<CaseContact> caseContacts) {
    List<Long> caseIds = caseContacts.stream().map(CaseContact::getCaseId).distinct().collect(Collectors.toList());
    List<Case> cases = caseMapper.selectFieldJson(caseIds);
    List<Map<String,Object>> contents = new ArrayList<>();
    List<SiteVarsMapping> duyanVarsMappings = new ArrayList<>();
    if (Objects.equals(casePlan.getFifoToRobotEnabled(), CasePlanEnums.FifoToRobotEnabled.OPEN.getCode())) {
      List<SiteVarsMapping> siteVarsMappings = siteVarsMappingService.getSiteVarsMappings(casePlan.getOrgId(),casePlan.getSiteId());
      List<String> duyanSiteVars = duyanManager.getSiteVars(casePlan.getSiteId(), casePlan.getOrgId());
      duyanVarsMappings = siteVarsMappings.stream().filter(
              siteVarsMapping -> StrUtil.isNotBlank(siteVarsMapping.getAnmiVar()) && duyanSiteVars.stream().anyMatch(
                      duyanSiteVar ->  ObjectUtil.equals(duyanSiteVar, siteVarsMapping.getDuyanVar())))
              .collect(Collectors.toList());
    }
    for (int i = 0; i < caseContacts.size(); i++) {
      CaseContact caseContact = caseContacts.get(i);
      Long caseId = caseContact.getCaseId();
      Optional<Case> caseOptional = cases.stream().filter(c -> ObjectUtil.equals(caseId, c.getId())).findFirst();
      Case caseDb = caseOptional.get();
      Map<String, String> fieldJsonMap = caseDb.getFieldJson();
      casePlanService.addContent(caseContact, casePlan.getOrgId(), fieldJsonMap, contents, duyanVarsMappings, casePlan);
    }
    return contents;

  }

  private void handleVisitCallback(Campaigns campaigns, String visitParams) {
    visitParams = StringUtils.substringAfter(visitParams, "VISIT");
    String[] params = visitParams.split(";");
    Long visitId = Long.valueOf(params[0]);
    Long contactsId = Long.valueOf(params[1]);
    Long userId = Long.valueOf(params[2]);
    if (contactsId == null || visitId == null || userId == null) {
      return;
    }
    Contacts contacts = contactsService.selectByPrimaryKey(contactsId);
    Visit visit = visitMapper.selectByPrimaryKey(visitId);
    User user = userService.selectByPrimaryKey(userId);
    if (contacts == null || visit == null || user == null) {
      return;
    }
    CaseCallback caseCallback = new CaseCallback();
    caseCallback.setOrgId(user.getOrgId());
    caseCallback.setVisitId(visitId);
    caseCallback.setCaseId(visit.getCaseId());
    caseCallback.setContactsId(contactsId);
    caseCallback.setConMobile(contacts.getMobile());
    caseCallback.setConName(contacts.getName());
    caseCallback.setRelationType(contacts.getRelationType());
    caseCallback.setOperatorId(userId);
    caseCallback.setOperatorName(user.getName());
    caseCallback.setStatus((byte) 0);
    caseCallback.setCallUuid(campaigns.getCall_uuid());
    caseCallback.setCallStyle(CaseOperationEnums.CallStyle.OUT.getCode());
    caseCallback.setCallTime(new Date(campaigns.getCall_time()));
    caseCallback.setCallDuration(campaigns.getDuration() == null ? 0 : campaigns.getDuration());
    caseCallback.setRingDuration(campaigns.getRing_time() == null ? 0 : campaigns.getRing_time());
    caseCallback.setOutcome(campaigns.getOutcome() == null ? "FAIL" : campaigns.getOutcome());
    caseCallback.setCallee(campaigns.getCallee());
    caseCallback.setCaller(campaigns.getCaller());
    caseCallback.setCreateBy(userId);
    caseCallback.setUpdateBy(userId);
    caseCallback.setCreateTime(new Date());
    caseCallback.setUpdateTime(new Date());
    caseCallbackMapper.insertSelective(caseCallback);
  }

  /** SUCCESS：成功，FAIL：无人接听，USER_BUSY：用户忙，POWER_OFF：用户关机，SUSPENDED：用户停机，NOT_EXIST：用户空号 */
  private String getOutcomeName(String outcomeStr) {
    String outcome = "";
    switch (outcomeStr) {
      case "SUCCESS":
        outcome = "成功";
        break;
      case "FAIL":
        outcome = "无人接听";
        break;
      case "USER_BUSY":
        outcome = "用户忙";
        break;
      case "POWER_OFF":
        outcome = "用户关机";
        break;
      case "SUSPENDED":
        outcome = "用户停机";
        break;
      case "NOT_EXIST":
        outcome = "用户空号";
        break;
      default:
        outcome = "";
    }
    return outcome;
  }

  /**
   * [{ name: '未呼叫', value: -1 }, { name: '正常接通', value: 0 }, { name: '呼叫异常', value: 8 }, { name:
   * '关机', value: 1 }, { name: '忙线中', value: 2 }, { name: '无人接听', value: 3 }, { name: '接听挂断', value:
   * 4 }, { name: '停机', value: 5 }, { name: '空号', value: 6 }, { name: '传真机', value: 7 }],
   */
  public int getOutcome(String outcomeStr) {
    int outcome = 0;
    switch (outcomeStr) {
      case "SUCCESS":
        outcome = 0;
        break;
      case "FAIL":
        outcome = 3;
        break;
      case "USER_BUSY":
        outcome = 2;
        break;
      case "POWER_OFF":
        outcome = 1;
        break;
      case "SUSPENDED":
        outcome = 5;
        break;
      case "NOT_EXIST":
        outcome = 6;
        break;
      default:
        outcome = 8;
    }
    return outcome;
  }

  /**
   * 转为催记视图模型
   *
   * @param caseOperationResult
   * @return
   */
  private CaseOperationVO convertToVO(
      CaseOperationResult caseOperationResult,
      Map<Integer, String> operationStatusNameMap,
      Map<Integer,String> operationStatesNameMap,
      Map<Integer,String> callTypesNameMap,
      Map<Long,String> depTeamNameMap,
      Map<Long,String> outBatchNoMap,
      Map<Long, String> adminMap,
      Boolean isEarlyMediaRecEnabled,
      Boolean isEarlyMediaRecObEnabled,
      Map<Long,String> productMap,
      Map<Long,String> deltMap,
      Map<Long,String> customClientLabelMap) {
    CaseOperationVO vo = new CaseOperationVO();
    BeanUtils.copyProperties(caseOperationResult, vo);
    // 催员名字不直接拿取operatorName,拿取create_by对应用户数据
    if (userService.getNames(userService.getOrgId()).get(caseOperationResult.getCreateBy()) != null) {
      vo.setOperatorName(userService.getNames(userService.getOrgId()).get(caseOperationResult.getCreateBy()));
    }
    vo.setCaseName(caseOperationResult.getName());
    vo.setCaseId(caseOperationResult.getCaseId());
    vo.setProductName(productMap.get(caseOperationResult.getProductId()));
    vo.setDeltName(deltMap.get(caseOperationResult.getOrgDeltId()));
    vo.setStatus(caseOperationResult.getStatus());
    // 设置催收状态
    if (caseOperationResult
        .getCreateType()
        .equals(CaseOperationEnums.CreateType.IMPORT.getCode())) {
      vo.setOperationState(null);
    } else {
      vo.setOperationState(caseOperationResult.getOperationState());
    }
    if(caseOperationResult.getClientLabelId()!=null){
      vo.setClientLabelName(customClientLabelMap.get(caseOperationResult.getClientLabelId()));
    }
    vo.setTeamName(depTeamNameMap.get(caseOperationResult.getTeamId()));
    vo.setDepName(depTeamNameMap.get(caseOperationResult.getDepId()));
    vo.setIdCard(caseOperationResult.getIdCard());
    vo.setReduceAmount(caseOperationResult.getReduceAmount());
    vo.setCaseOpeTime(caseOperationResult.getCreateTime().getTime());
    vo.setOutBatchNo(outBatchNoMap.get(caseOperationResult.getOutBatchId()));
    vo.setOutSerialNo(
        StringUtils.isBlank(caseOperationResult.getOutSerialNo())
            ? ""
            : caseOperationResult
                .getOutSerialNo()
                .substring(0, caseOperationResult.getOutSerialNo().lastIndexOf("#")));
    vo.setOwnMobile(caseOperationResult.getOwnMobile());
    vo.setCallStyle(caseOperationResult.getCallStyle());
    vo.setTag(caseOperationResult.getTag()); // 客户标签
    vo.setAdminSubmitterName(adminMap.get(caseOperationResult.getAdminSubmitter()));
    int pushFlag = 0;
    if(Objects.equals(caseOperationResult.getIsHandlePush(),1)){
      pushFlag=Objects.equals(caseOperationResult.getIsPush(),1)?1:2;
    }
    vo.setPushFlag(pushFlag);
    // 催记状态 0正常，-11处理中
    Boolean isLock =
        stringRedisTemplate
            .opsForSet()
            .isMember(
                KeyCache.CASE_OPERATION_PROTECT_EXIST_IDS + caseOperationResult.getOrgId(),
                String.valueOf(caseOperationResult.getId()));
    if (isLock) {
      vo.setStatus(CaseOperationEnums.Status.HANDLE.getCode());
    }
    vo.setActionTypeName(operationStatusNameMap.get(vo.getActionType()));
    vo.setOperationStateName(operationStatesNameMap.get(vo.getOperationState()));
    vo.setCallTypeName(callTypesNameMap.get(vo.getCallType()));
    // 按要求返回电话结果FAIL情况，检测是否开启空号检测
    Integer submitType = caseOperationResult.getSubmitType();
    String outcome = caseOperationResult.getOutcome();
    // 非点呼设置失败电话结果
    if (isEarlyMediaRecEnabled && !Objects.equals(submitType, CaseOperationEnums.SubmitType.MANUAL.getCode()) && Objects.equals(outcome, "FAIL") ) {
      vo.setOutcome("NO_RESPONSE");
    }
    // 点呼设置失败电话结果
    if (isEarlyMediaRecObEnabled && Objects.equals(submitType, CaseOperationEnums.SubmitType.MANUAL.getCode()) && Objects.equals(outcome, "FAIL") ) {
      vo.setOutcome("NO_RESPONSE");
    }
    return vo;
  }

  public String queryFollowCaseDays() {
    UserSession userSession = getTokenUser();
    Map map = new HashMap();
    map.put("orgId", userSession.getOrgId());
    // 用户可能是坐席、管理员（管理员还需要判断公司）
    if (UserUtils.likeBranchAdmin()) {
      map.put("depId", userSession.getDepId());
    }
    int day1 = 0, day2 = 0, day3 = 0, repairSuccess = 0, aboutToExpire = 0;

    map.put("repairStatus", RepairInfoEnums.Status.REPAIR_SUCCESS.getCode());
    repairSuccess = caseOperationMapper.queryDays(map);
    map.put("repairStatus", null);
    map.put("listType", 0);
    day1 = caseOperationMapper.queryDays(map);
    map.put("listType", 1);
    day2 = caseOperationMapper.queryDays(map);
    map.put("listType", 2);
    day3 = caseOperationMapper.queryDays(map);
    map.put("listType", 3);
    aboutToExpire = caseOperationMapper.queryDays(map);

    String res = day1 + "," + day2 + "," + day3 + "," + repairSuccess + "," + aboutToExpire;
    return res;
  }

  /**
   * 根据查询类型，查询所有的案件催记
   *
   * @param pageParam
   * @param listType
   * @return
   */
  public CaseFollowListVO queryFollowCases(PageParam pageParam, Integer listType) {
    // 今日待跟进、明日待跟进、今日还款待跟进
    List<CaseFollowVO> caseFollowVOS = new ArrayList<>();

    // 直接根据caseIds分页查询所有的
    // List<Long> userIds = listUserIds();
    Map map = new HashMap();
    UserSession userSession = getTokenUser();
    // map.put("userIds", userIds);
    map.put("orgId", userSession.getOrgId());
    if (UserUtils.likeBranchAdmin()) {
      map.put("depId", userSession.getDepId());
    }
    map.put("allotStatues", Arrays.asList(CaseEnums.AllotStatus.ALLOT_USER.getCode()));
    map.put("caseStatues", Arrays.asList(CaseEnums.CaseStatus.NORMAL.getCode(),CaseEnums.CaseStatus.DELAY.getCode()));
    map.put("listType", listType);
    Page page = super.setPage(pageParam);
    List<CaseQueryResult> results = caseMapper.findHomeCases(map);
    Map<Integer, String> operationStatusMap =
        customOperationStatusService.getNames(userSession.getOrgId());
    Map<Integer, String> operationStateNamesMap =orgConfigService
            .getAllNames(userSession.getOrgId(),OrgConfigEnums.Type.OPERATION_STATE.getCode());
    for (CaseQueryResult res : results) {
      CaseFollowVO vo = createCaseFollow(res, operationStatusMap);
      vo.setOperationStateName(operationStateNamesMap.get(vo.getOperationState()));
      caseFollowVOS.add(vo);
    }
    CaseFollowListVO pageOutput =
        new CaseFollowListVO(
            page.getPageNum(),
            page.getPageSize(),
            page != null ? (int) page.getTotal() : caseFollowVOS.size(),
            caseFollowVOS);
    return pageOutput;
  }

  /**
   * 根据caseId和展示类型type创建案件跟催VO(CaseFollowVO)
   *
   * @return
   */
  private CaseFollowVO createCaseFollow(
      CaseQueryResult caseQueryResult, Map<Integer, String> operationStatusMap) {
    CaseFollowVO caseFollowVO = new CaseFollowVO();

    // 设置好案件债务人名称
    caseFollowVO.setName(caseQueryResult.getName());
    caseFollowVO.setActionType(caseQueryResult.getOperStatus());

    // 设置好委托公司名称
    Long orgDeltId = caseQueryResult.getOrgDeltId();
    Delt delt = deltService.selectByPrimaryKey(orgDeltId);
    caseFollowVO.setOrgDeltName(delt.getName());

    // 设置好批次号
    Long innerBatchId = caseQueryResult.getInnerBatchId();
    caseFollowVO.setBatchNo(innerBatchService.getNames().get(innerBatchId));

    // 设置好委案产品
    Long proId = caseQueryResult.getProductId();
    caseFollowVO.setProductName(productService.selectByPrimaryKey(proId).getName());

    // 设置好逾期天数
    caseFollowVO.setOverdueDays(caseQueryResult.getOverdueDays());

    // 设置好案件编号
    caseFollowVO.setOutSerialNo(
        caseQueryResult
            .getOutSerialNo()
            .substring(0, caseQueryResult.getOutSerialNo().lastIndexOf("#")));

    // 设置案件状态
    caseFollowVO.setCaseStatus(caseQueryResult.getCaseStatus());
    caseFollowVO.setAllotStatus(caseQueryResult.getAllotStatus());

    // 设置好案件id
    caseFollowVO.setId(caseQueryResult.getId());
    // 设置好催收进程
    caseFollowVO.setOperationState(caseQueryResult.getOperationState());

    caseFollowVO.setActionTypeName(operationStatusMap.get(caseFollowVO.getActionType()));
    caseFollowVO.setEntrustStartTime(caseQueryResult.getEntrustStartTime().getTime());
    caseFollowVO.setEntrustEndTime(caseQueryResult.getEntrustEndTime().getTime());
    return caseFollowVO;
  }

  /**
   * 展示催记首页所有的数据
   *
   * @param pageParam
   * @param listType
   * @return
   */
  public CaseFollowListVO listHomePage(PageParam pageParam, Integer listType) {
    CaseFollowListVO caseFollowListVO = queryFollowCases(pageParam, listType);
    return caseFollowListVO;
  }

  @Transactional
  public void update(CaseOperationUpdateModel model) {
    Long id = model.getId();
    CaseOperation caseOperation = selectByPrimaryKey(id);

    CaseOperationEnums.DataType dataType = CaseOperationEnums.DataType.HEAT;
    if (ObjectUtil.isNull(caseOperation)) {
      dataType = CaseOperationEnums.DataType.COLD;
      CaseOperationUseLess caseOperationUseLess = caseOperationUseLessMapper.selectByPrimaryKey(id);
      try {
        caseOperation = BeanUtil.copyProperties(caseOperationUseLess, CaseOperation.class);
      }catch (Exception e){
        log.info(ExceptionUtil.stacktraceToString(e));
      }
    }
    if (ObjectUtil.isNull(caseOperation)) {
      throw new ApiException("催记不存在");
    }

    // 催收备注修改权限
    UserSession userSession = getTokenUser();

    if(ObjectUtil.equal(dataType,CaseOperationEnums.DataType.HEAT)){
      CaseOperation update = new CaseOperationResult();
      update.setId(id);
      if (!StringUtils.isEmpty(model.getDesc())) {
        update.setDesc(model.getDesc());
      }
      if (model.getActionType() != null) {
        update.setActionType(model.getActionType());
      }
      updateByPrimaryKeySelective(update);
    }else {
      CaseOperationUseLess update = new CaseOperationUseLess();
      update.setId(id);
      if (!StringUtils.isEmpty(model.getDesc())) {
        update.setDesc(model.getDesc());
      }
      if (model.getActionType() != null) {
        update.setActionType(model.getActionType());
      }
      caseOperationUseLessMapper.updateByPrimaryKeySelective(update);
    }
  }

  /**
   * 获取下载地址
   *
   * @param taskId
   */
  public String getDownloadUrl(Long taskId) {
    if (null == taskId) {
      throw new ApiException("请选择下载任务记录");
    }
    DownloadTask task = downloadTaskService.selectByPrimaryKey(taskId);
    if (null == task) {
      throw new ApiException("任务不存在");
    }
    // 只有包括录音才会收费
    //        if (null != task.getRelRecord() && task.getRelRecord()) {
    //            duyanManager.chargeRecordDownload(UserUtils.getDuyanId(), task.getFileSize(), 1,
    // taskId);
    //        }
    return task.getDownloadUrl();
  }

  @Transactional
  public Long addAutomatic(CaseOperationOperate create) {
    if (StringUtils.isBlank(create.getCallUuid())) {
      return null;
    }
    if (create.getContactsId() == null) {
      throw new ApiException("联系人列表无此联系人，无法填写催记");
    }
    Contacts contacts = contactsService.selectByPrimaryKey(create.getContactsId());
    if (contacts == null) {
      throw new ApiException("联系人列表无此联系人，无法填写催记");
    }
    Case ca = caseMapper.selectByPrimaryKey(create.getCaseId());
    if (ca == null) {
      throw new ApiException("找不到催记对应的案件！");
    }
    Long userId = ca.getUserId();
    User user = new User();
    if (userId != null) {
      user = userService.selectByPrimaryKey(userId);
    }

    UserSession loginUser = getTokenUser();
    CaseOperation operation = new CaseOperation();
    operation.setActionType(CaseOperationEnums.ActionType.NOT_FILLED.getCode());
    operation.setCallType(CaseOperationEnums.CallType.NORMAL.getCode());
    operation.setCaseId(create.getCaseId());
    operation.setCallUuid(StringUtils.isBlank(create.getCallUuid()) ? null : create.getCallUuid());
    if (ca.getUserId() == null) {
      operation.setCreateBy(loginUser.getId());
      operation.setOperatorName(loginUser.getName());
    } else {
      operation.setCreateBy(ca.getUserId());
      operation.setOperatorName(user.getName());
    }
    operation.setUpdateBy(loginUser.getId());
    operation.setAdminSubmitter(loginUser.getId());
    operation.setConMobile(contacts.getMobile());
    operation.setConName(contacts.getName());
    operation.setRelationType(contacts.getRelationType());
    operation.setSubmitType(create.getSubmitType() == null ? CaseOperationEnums.SubmitType.MANUAL.getCode() : create.getSubmitType());
    operation.setCallStyle(create.getCallStyle());
    operation.setIsHidden(CaseOperationEnums.IsHidden.NO.getCode());
    operation.setOrgId(loginUser.getOrgId());
    operation.setOrgDeltId(ca.getOrgDeltId());
    operation.setOutSerialNo(ca.getOutSerialNo());
    operation.setOutSerialTemp(ca.getOutSerialTemp());
    caseOperationMapper.insertOrUpdateByAdd(operation);
    // case改为未填写
    ca.setOperStatus(CaseOperationEnums.ActionType.NOT_FILLED.getCode());
    // 更新案件表中承诺还款时间
    ca.setPtpTime(null);

    OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(ca.getOrgId());
    Long caseCtrlPoolId = null;
    //案件管控开关
    if(OrgSwitchEnums.CaseCtrlSwitch.OPEN.getCode().equals(orgSwitch.getCtrlSwitch())){
      Map<Integer,List<CaseCtrlPoolCondition>> operMap= caseCtrlService.getOperCodeCtrlMap(ca.getOrgId());
      if (operMap.containsKey(ca.getOperStatus()) && !CollectionUtils.isEmpty(operMap.get(ca.getOperStatus()))) {
        caseCtrlPoolId = operMap.get(ca.getOperStatus()).get(0).getId();
      }
    }
    //判断共债案件同步开关是否开启，如果开启则共债案件催收结果同步
    if (orgSwitch.getCaseSyncSwitch().equals(OrgSwitchEnums.CaseSyncSwitch.YES.getCode())) {
      if (ca.getDebtId() != null && ca.getOrgDeltId() != null) {
        // 查询当前委案公司下所有的共债案件
        Map map = new HashMap();
        map.put("orgId", ca.getOrgId());
        map.put("orgDeltId", ca.getOrgDeltId());
        map.put("debtId", ca.getDebtId());
        List<Long> caseIds = caseMapper.selectConjointCaseId(map);

        if (!CommonUtils.isEmpty(caseIds)) {
          // 同步
          Map syncMap = new HashMap();
          syncMap.put("caseIds", caseIds);
          //催收结果，电话结果，催收进程，上次跟进时间，下次跟进时间，机器人协催时间，机器人协催结果，机器人协催催记，机器人客户标签
          syncMap.put("operStatus", ca.getOperStatus());
          syncMap.put("lastFollowTime", new Date());
          syncMap.put("ctrlId",caseCtrlPoolId);
          caseMapper.syncCaseOperationStatusAuto(syncMap);
        }
      }
    }
    if(OrgSwitchEnums.CaseNoSwitch.OPEN.getCode().equals(orgSwitch.getCaseNoSwitch())){
      // 若调解有效，且未设置过留案号，则设置留案号
      CustomOperationStatus customOperationStatus = customOperationStatusService.queryCustomByCode(operation.getActionType(), loginUser.getOrgId());
      if (customOperationStatus != null && loginUser.getDepId() != null
              && Objects.equals(CustomOperationStatusEnums.IsEffective.YES.getCode(),customOperationStatus.getIsEffective())) {
        if (StringUtils.isBlank(ca.getDelayNo())) {
          String delayNo = caseService.addDelayNo(ca.getDepId());
          ca.setDelayNo(delayNo);
        }
      }
    }

    ca.setCtrlId(caseCtrlPoolId);
    caseMapper.updateByPrimaryKeySelective(ca);
    // 设置债务人的上次跟进时间和跟进次数
    syncCaseDebtorFollowInfo(ca.getDebtId());
    syncCaseFollowInfo(ca.getId());
    // 手机号改为后端脱敏之后，不再由前端传入，需根据contactsId查询联系人获取到手机号，放入到参数对象create中，这里放入是为了点呼通过工作手机拨打
    // 后续需要用到这个mobile，这里赋值，可以减少一次查询
    create.setMobile(contacts.getMobile());
    return operation.getId();
  }

  @Transactional
  public void syncCaseDebtorFollowInfo(Long debtId) {
    if (debtId == null) {
      return;
    }
    CaseDebtor debtor = caseDebtorService.selectByPrimaryKey(debtId);
    if (debtor == null) {
      throw new ApiException("不存在该债务人，无法同步！");
    }
    CaseFollowInfo info=getDebtLastFollowInfo(debtId);
    debtor.setLastFollowTime(info.getLastFollowTime());
    debtor.setFollowCount(info.getFollowCount());
    caseDebtorMapper.updateByPrimaryKeySelective(debtor);
  }

  @Transactional
  public void syncCaseFollowInfo(Long caseId){
    if (caseId == null) {
      return;
    }
    Case ca=caseService.selectByPrimaryKey(caseId);
    CaseFollowInfo info=getCaseLastFollowInfo(caseId);
    ca.setLastFollowTime(info.getLastFollowTime());
    ca.setFollowCount(info.getFollowCount());
    caseService.updateByPrimaryKey(ca);
  }

  public CaseFollowInfo getDebtLastFollowInfo(Long debtId){
    return caseOperationMapper.getDebtLastFollowInfo(debtId);
  }

  public CaseFollowInfo getCaseLastFollowInfo(Long caseId){
    return caseOperationMapper.getCaseLastFollowInfo(caseId);
  }

  @Transactional(rollbackFor = Exception.class)
  public void deleteOperation(CaseOperationDelParam param) throws Exception {
    UserSession userSession = getTokenUser();
    if (param.getAllSelect()) {
      asyncDelete(param, userSession);
    } else {
      List<CaseOperationResult> caseOperationResultList =
          selectDelList(param, userSession.getOrgId());
      // 创建任务
      Long taskId =
          asyncTaskService.createOperDelTask(
              userSession,
              Long.valueOf(caseOperationResultList.size()),
              AsyncTaskEnums.Status.SUCCESS.getCode());
      List<CaseOperation> caseOperationList =
          JsonUtils.toList(JsonUtils.toJson(caseOperationResultList), CaseOperation.class);
      batchDelCaseOperation(caseOperationList, userSession, taskId, param.getDataType());
    }
  }

  private void asyncDelete(CaseOperationDelParam param, UserSession userSession) throws Exception {
    List<CaseOperationResult> caseOperationResultList =
        selectDelList(param, userSession.getOrgId());
    List<String> caseOperationIdList =
        caseOperationResultList.stream()
            .map(c -> c.getId().toString())
            .collect(Collectors.toList());
    // 加锁，状态保护
    String[] caseOperations = caseOperationIdList.toArray(new String[caseOperationIdList.size()]);
    //创建异步任务任务之前判断3分钟之前是否有相同操作
    CaseOperationDelParam caseOperationDelParam = AuthBeanUtils.copy(param, CaseOperationDelParam.class);
    caseOperationDelParam.setOrgId(userSession.getOrgId());
    String md5 = DigestUtils.md5DigestAsHex(JSONObject.toJSONString(caseOperationDelParam).getBytes());
    String s = stringRedisTemplate.opsForValue().get(ASYNC_PREFIX + md5);
    if (org.apache.commons.lang.StringUtils.isNotBlank(s)) {
      throw new ApiException("当前已有相同任务进行中，请稍后操作");
    } else {
      stringRedisTemplate.opsForValue().set(ASYNC_PREFIX + md5, "1", 3, TimeUnit.MINUTES);
    }
    stringRedisTemplate
        .opsForSet()
        .add(KeyCache.CASE_OPERATION_PROTECT_EXIST_IDS + userSession.getOrgId(), caseOperations);
    // 创建异步任务
    Long taskId =
        asyncTaskService.createOperDelTask(
            userSession,
            Long.valueOf(caseOperationIdList.size()),
            AsyncTaskEnums.Status.ING.getCode());
    // 任务添加到任务列表
    stringRedisTemplate
        .opsForSet()
        .add(KeyCache.CASE_OPERATION_DEL_TASK_VALUES + taskId, caseOperations);
    stringRedisTemplate
        .opsForList()
        .leftPush(KeyCache.CASE_OPERATION_DEL_TASK_ID_LIST, taskId.toString());
  }

  private List<CaseOperationResult> selectDelList(CaseOperationDelParam param, Long orgId) {
    Set<String> idLockList =
        stringRedisTemplate.opsForSet().members(KeyCache.CASE_OPERATION_PROTECT_EXIST_IDS + orgId);
    Boolean allSelect = param.getAllSelect();
    if (!allSelect) {
      //  去除加锁中的数据
      List<Long> idList = null;
      if (idLockList.isEmpty()) {
        idList = param.getOperationIdList();
      } else {
        idList =
            param.getOperationIdList().stream()
                .filter(c -> !idLockList.contains(c.toString()))
                .collect(Collectors.toList());
      }
      if (CollectionUtils.isEmpty(idList)) {
        throw new ApiException("没有可操作催记，请刷新页面！");
      }
      Integer dataType = param.getDataType();
      param = new CaseOperationDelParam();
      param.setOperationIdList(idList);
      param.setDataType(dataType);
    } else {
      param.setOperationIdList(null);
    }
    // 只获取部分数据
    param.setSelectField("cao.id,cao.case_id");
    param.setOrgId(getOrgId());
    if (UserUtils.likeBranchAdmin()) {
      param.setDepId(getTokenUser().getDepId());
    }
    List<CaseOperationResult> caseOperationResultList = selectByPage(param);
    if (allSelect) {
      // 全部筛选的需过滤加锁的案件
      if (!idLockList.isEmpty()) {
        caseOperationResultList =
            caseOperationResultList.stream()
                .filter(c -> !idLockList.contains(c.getId().toString()))
                .collect(Collectors.toList());
      }
    }
    if (CommonUtils.isEmpty(caseOperationResultList)) {
      throw new ApiException("没有可操作催记，请刷新页面！");
    }
    return caseOperationResultList;
  }

  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public void batchDelCaseOperation(
          List<CaseOperation> caseOperationResultList, UserSession userSession, Long taskId, Integer dataType)
      throws Exception {
    if (CollectionUtils.isEmpty(caseOperationResultList)) {
      return;
    }
    List<Long> caseOperationIdList =
            caseOperationResultList.stream().map(CaseOperation::getId).collect(Collectors.toList());
    if (ObjectUtil.equal(dataType, CaseOperationEnums.DataType.COLD.getCode())) {
      Example example = new Example(CaseOperationUseLess.class);
      example.createCriteria().andIn("id", caseOperationIdList);
      CaseOperationUseLess caseOperationUseLess = new CaseOperationUseLess();
      caseOperationUseLess.setStatus(CaseOperationEnums.Status.DELETE.getCode());
      caseOperationUseLess.setUpdateBy(userSession.getId());
      caseOperationUseLess.setUpdateTime(new Date());
      caseOperationUseLessMapper.updateByExampleSelective(caseOperationUseLess, example);
    } else {
      Example example = new Example(CaseOperation.class);
      example.createCriteria().andIn("id", caseOperationIdList);
      CaseOperation caseOperation = new CaseOperation();
      caseOperation.setStatus(CaseOperationEnums.Status.DELETE.getCode());
      caseOperation.setUpdateBy(userSession.getId());
      caseOperation.setUpdateTime(new Date());
      caseOperationMapper.updateByExampleSelective(caseOperation, example);
    }
    // 生成日志
    Map<Long, List<CaseOperation>> operMap =
        caseOperationResultList.stream().collect(Collectors.groupingBy(CaseOperation::getCaseId));
    Map<Long, Map<String, String>> diffMap = new HashMap<>();
    for (Map.Entry<Long, List<CaseOperation>> entry : operMap.entrySet()) {
      Map<String, String> values = new HashMap<>();
      String operationIds =
          entry.getValue().stream().map(c -> c.getId().toString()).collect(Collectors.joining(","));
      values.put("operation_ids", operationIds);
      diffMap.put(entry.getKey(), values);
    }
    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setCaseIds(Lists.newArrayList(operMap.keySet()));
    caseBatchUpdateEvent.setDiffMap(diffMap);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.OPERATION_DEL.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);
  }

  public PageOutput<CaseOperationVO> sameDebtList(SameDebtCaseOpeParam param) {
    Case caseInfo = caseService.selectByPrimaryKey(param.getCaseId());
    if (caseInfo == null) {
      throw new ApiException("案件不存在");
    }
    if (caseInfo.getDebtId() == null) {
      return new PageOutput();
    }
    // 分页

    Byte isHidden = null;
    OrgSwitch orgSwitch = this.orgSwitchMapper.selectOrgSwitchByOrgId(UserUtils.getTokenUser().getOrgId());
    if (orgSwitch != null && OrgSwitchEnums.ShowHiddenOperationSwitch.NO.getCode() == orgSwitch.getShowHiddenOperationSwitch()) {
      isHidden = CaseOperationEnums.IsHidden.NO.getCode();
    }
    PageParam pageParam = new PageParam();
    pageParam.setPage(param.getPage());
    pageParam.setLimit(param.getLimit());
    Page page = super.setPage(pageParam);
    List<CaseOperationResult> list = caseOperationMapper.sameDebtList(caseInfo.getDebtId(), isHidden, param.getDataType());

    List<CaseOperationVO> vos = new ArrayList<>();
    UserSession userSession = getTokenUser();
    Map<Integer, String> operationStatusNameMap =
        customOperationStatusService.getNames(userSession.getOrgId());
    Map<Integer, String> operationStateNamesMap =orgConfigService
            .getAllNames(userSession.getOrgId(),OrgConfigEnums.Type.OPERATION_STATE.getCode());
    Map<Integer, String> callTypeNamesMap =orgConfigService
            .getAllNames(userSession.getOrgId(),OrgConfigEnums.Type.CALL_TYPE.getCode());
    Map<Long,String> depTeamNameMap=depTeamService.getNames();
    Map<Long,String> outBatchNoMap=outBatchService.getNames();
    String recEnabled = stringRedisTemplate.opsForValue().get(KeyCache.IS_EARLY_MEDIA_REC_ENABLED);
    Boolean isEarlyMediaRecEnabled = Boolean.parseBoolean(recEnabled);
    String recObEnabled = stringRedisTemplate.opsForValue().get(KeyCache.IS_EARLY_MEDIA_REC_OB_ENABLED);
    Boolean isEarlyMediaRecObEnabled = Boolean.parseBoolean(recObEnabled);
    List<Long> submitList = list.stream().map(CaseOperation::getAdminSubmitter).collect(Collectors.toList());
    // 查询所有管理员信息
    Map<Long, String> adminMap = new HashMap<>();
    if (!CollectionUtils.isEmpty(submitList)) {
      List<User> users = userService.selectByIdList(submitList);
      adminMap = users.stream().collect(Collectors.toMap(User::getId, User::getName));
    }
    Map<Long,String> productMap = productService.getNames();
    Map<Long,String> deltMap = deltService.getNames();
    Map<Long,String> customClientLabelMap = customClientLabelService.getNames(userSession.getOrgId());
    for (int i = 0; i < list.size(); i++) {
      CaseOperationVO vo = convertToVO(list.get(i),
              operationStatusNameMap,
              operationStateNamesMap,
              callTypeNamesMap,
              depTeamNameMap,
              outBatchNoMap,
              adminMap,
              isEarlyMediaRecEnabled,
              isEarlyMediaRecObEnabled,productMap,deltMap,customClientLabelMap);
      vos.add(vo);
    }
    PageOutput pageOutput =
        new PageOutput(
            page.getPageNum(),
            page.getPageSize(),
            page != null ? (int) page.getTotal() : vos.size(),
            vos);
    return pageOutput;
  }

  /**
   * 查询案件最近联系的次数
   * @param caseId 案件id
   * @param startOfTime 开始时间
   * @param type 0：本人，1：联系人
   * @param conMobile 联系人手机号
   * @return 返回一段时间内的催记条数
   */
  public Integer selectCountByCaseAndDay(Long caseId, Date startOfTime, Integer type, String conMobile) {
    if (encryptProperties.getEnable()) {
      conMobile = encryptService.encrypt(conMobile);
    }
    return caseOperationMapper.selectCountByCaseAndDay(caseId, startOfTime, type, conMobile);
  }

  @Transactional(rollbackFor = Exception.class)
  public Integer backupCaseOperationById(List<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return 0;
    }
    // 备份催记
    caseOperationHistoryMapper.backCaseOperationById(ids);
    // 删除催记
    Example example = new Example(CaseOperationUseLess.class);
    example.and().andIn("id", ids);
    int count = caseOperationUseLessMapper.deleteByExample(example);
    return count;
  }

  /**
   * 转移催记
   *
   * @param caseOperationIds 催记ID
   * @return int
   */
  @Transactional(rollbackFor = Exception.class)
  public int transferCaseOperation(List<Long> caseOperationIds) {
    if (CollectionUtils.isEmpty(caseOperationIds)) {
      return 0;
    }

    // 转移催记冷数据
    caseOperationUseLessMapper.transferCaseOperation(caseOperationIds);
    //物理删除催记冷数据
    Example example = new Example(CaseOperation.class);
    example.and().andIn("id", caseOperationIds);
    int cnt = caseOperationMapper.deleteByExample(example);

    return cnt;
  }

  public List<Long> getOperationIdFromEs(Long orgId, Integer dataType, Long startId, Integer limit, Boolean isAll) {
    CaseOperationParamDto dto = new CaseOperationParamDto();
    dto.setOrgId(orgId);
    dto.setStartId(startId);
    dto.setIsAll(isAll);
    dto.setPage(1);
    dto.setLimit(limit);
    dto.setSortField("id");
    dto.setSortAsc(true);
    dto.setDataType(dataType);
    CaseOperationResponse caseOperationResponse =
            remoteAlfredService.fetchFromCaseOperationRemote(dto);
    List<TransferCaseOperationResult> sources = caseOperationResponse.getList();
    List<Long> operationIds = sources.stream().map(TransferCaseOperationResult::getId).collect(Collectors.toList());
    return operationIds;
  }

  public CaseOperationResult selectByCallUuid(String callUUid) {
    CaseOperationResult caseOperationResult = caseOperationMapper.selectByCallUuid(callUUid);
    if (ObjectUtil.isNull(caseOperationResult)){
      CaseOperationUseLess caseOperationUseLess = caseOperationUseLessMapper.selectByCallUuid(callUUid);
      caseOperationResult = BeanUtils.copyProperties(caseOperationUseLess, CaseOperationResult.class);
    }
    return caseOperationResult;
  }

  public CaseFollowInfo getCaseContactLastFollowInfo(Long caseId, String conMobile) {
    if (encryptProperties.getEnable()) {
      conMobile = encryptService.encrypt(conMobile);
    }
    return caseOperationMapper.getCaseContactLastFollowInfo(caseId, conMobile);
  }

  /**
   * 通过回调更新客户标签
   *
   * @param jsonObject json对象
   */
  public void updateTagByCallback(JSONObject jsonObject) {
    JSONArray datas = jsonObject.getJSONArray("data");
    if (ObjectUtil.isNull(datas)){
      log.warn("通过回调更新客户标签-回调data为空");
      return;
    }

    for (int i = 0; i < datas.size(); i++) {
      try {
        JSONObject callData = datas.getJSONObject(i);

        String callUuid = callData.getString("call_uuid");
        if (StrUtil.isBlank(callUuid)){
          continue;
        }

        JSONArray voiceSiteLabelJSONArray = callData.getJSONArray("voice_site_labels");
        if (ObjectUtil.isEmpty(voiceSiteLabelJSONArray)){
          continue;
        }

        List<String> voiceSiteLabelList = JsonUtils.toList(voiceSiteLabelJSONArray.toJSONString(), String.class);
        String voiceSiteLabes = StringUtils.substring(StringUtils.join(voiceSiteLabelList, ","), 0, 250);

        CaseOperationResult caseOperationResult = caseOperationMapper.selectByCallUuid(callUuid);
        if (ObjectUtil.isNull(caseOperationResult)){
          continue;
        }

        Long operationId = caseOperationResult.getId();
        CaseOperation caseOperation = new CaseOperationResult();
        caseOperation.setId(operationId);
        caseOperation.setDesc(voiceSiteLabes);
        caseOperationMapper.updateByPrimaryKeySelective(caseOperation);

        Long caseId = caseOperationResult.getCaseId();
        if (ObjectUtil.isNull(caseId)){
          continue;
        }

        Case caseDb = caseService.selectByPrimaryKey(caseId);
        if (ObjectUtil.isNull(caseDb)){
          continue;
        }

        Case caseUpdate = new Case();
        caseUpdate.setId(caseId);
        caseUpdate.setAutoAssistRecord(voiceSiteLabes);
        caseService.updateByPrimaryKeySelective(caseUpdate);
      }catch (Exception e){
        log.error("通过回调更新客户标签异常",e);
      }
    }
  }

  /**
   * 查询案件电催数量
   *
   * @param startDate 开始时间
   * @param caseIds   案件id列表
   * @param type      联系人类型 0：本人 1：联系人
   * @return
   */
  public Map<Long, Integer> selectCountByCaseIds(Date startDate, List<Long> caseIds, Integer type) {
    AssertUtil.notNull(startDate, "查询案件电催开始时间不可为空");
    if (CollectionUtils.isEmpty(caseIds)) {
      return Collections.emptyMap();
    }
    List<CaseLimitCountResult> caseLimitCountResults = caseOperationMapper.selectCountByCaseIds(startDate, caseIds, type);
    Map<Long, Integer> countMap = caseLimitCountResults.stream().collect(Collectors.toMap(CaseLimitCountResult::getCaseId, CaseLimitCountResult::getCount));
    return countMap;
  }

  /**
   * 查询案件号码电催数量
   *
   * @param startDate  开始时间
   * @param caseIds    案件id列表
   * @param conMobiles 案件号码列表
   * @return
   */
  public Map<Long, List<CaseLimitCountResult>> selectCountByCaseMobiles(Date startDate, List<Long> caseIds, List<String> conMobiles) {
    AssertUtil.notNull(startDate, "查询案件电催开始时间不可为空");
    if (CollectionUtils.isEmpty(caseIds)) {
      return Collections.emptyMap();
    }
    if (CollectionUtils.isEmpty(conMobiles)) {
      return Collections.emptyMap();
    }
    if (encryptProperties.getEnable()) {
      conMobiles = conMobiles.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList());
    }
    List<CaseLimitCountResult> caseLimitCountResults = caseOperationMapper.selectCountByCaseMobiles(startDate, caseIds, conMobiles);
    Map<Long, List<CaseLimitCountResult>> countMap = caseLimitCountResults.stream().collect(Collectors.groupingBy(CaseLimitCountResult::getCaseId));
    return countMap;
  }

  /**
   * es查询案件电催数量
   *
   * @param startDate 开始时间
   * @param caseIds   案件id列表
   * @param type      联系人类型 0：本人 1：联系人
   * @return
   */
  public Map<Long, Integer> selectCountByCaseIdsUsingEs(Date startDate, List<Long> caseIds, Integer type) {
    AssertUtil.notNull(startDate, "查询案件电催开始时间不可为空");
    if (CollectionUtils.isEmpty(caseIds)) {
      return Collections.emptyMap();
    }
    List<Contacts> contactsList = new ArrayList<>();
    if (Objects.equals(type, 0)) {
      contactsList = contactsMapper.selectContactsByParam(caseIds, 1);
    }
    if (Objects.equals(type, 1)) {
      contactsList = contactsMapper.selectContactsByParam(caseIds, 0);
    }
    List<String> mobiles = contactsList.stream().map(Contacts::getMobile).collect(Collectors.toList());
    if (encryptProperties.getEnable()) {
      mobiles = mobiles.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList());
    }
    CaseOperationQueryDto dto = new CaseOperationQueryDto();
    dto.setConMobiles(mobiles);
    dto.setCaseIds(caseIds);
    dto.setStartTime(startDate);
    List<CaseOperationCountResult> results = remoteAlfredService.fetchFromCaseOperationCountRemote(dto);
    return results.stream().collect(Collectors.toMap(CaseOperationCountResult::getCaseId, CaseOperationCountResult::getCount));
  }

  /**
   * es查询案件号码电催数量
   *
   * @param startDate  开始时间
   * @param caseIds    案件id列表
   * @param conMobiles 案件号码列表
   * @return
   */
  public Map<Long, List<CaseLimitCountResult>> selectCountByCaseMobilesUsingEs(Date startDate, List<Long> caseIds, List<String> conMobiles) {
    AssertUtil.notNull(startDate, "查询案件电催开始时间不可为空");
    if (CollectionUtils.isEmpty(caseIds)) {
      return Collections.emptyMap();
    }
    if (CollectionUtils.isEmpty(conMobiles)) {
      return Collections.emptyMap();
    }
    if (encryptProperties.getEnable()) {
      conMobiles = conMobiles.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList());
    }
    CaseOperationQueryDto dto = new CaseOperationQueryDto();
    dto.setConMobiles(conMobiles);
    dto.setCaseIds(caseIds);
    dto.setStartTime(startDate);
    List<CaseOperationCountResult> results = remoteAlfredService.fetchFromCaseOperationCountGroupMobileRemote(dto);
    List<CaseLimitCountResult> caseLimitCountResults = new ArrayList<>();
    try {
      caseLimitCountResults = BeanUtil.copyPropertiesFromList(results, CaseLimitCountResult.class);
    } catch (Exception e) {
      throw new RuntimeException("数组转换失败", e);
    }
    return caseLimitCountResults.stream().collect(Collectors.groupingBy(CaseLimitCountResult::getCaseId));
  }

  public static final String ADD_ROBOT_TASK = "case_operation_add_task::";
  private final DuyanThreadExecutor caseOperationRobotAsyncThreadExecutor = new DuyanThreadExecutor("bpw-case-operation-robot-async");

  public void addRobotCall(CaseOperationAddRobotCallParam param) {
    if (Objects.equals(param.getExecType(), 1)) {
      if (Objects.isNull(param.getExecTime())) {
        throw new ApiException("执行时间不能为空");
      }
      Date execDate = new Date(param.getExecTime());
      Date now = new Date();
      if (execDate.before(now)) {
        throw new ApiException("执行时间不能小于当前时间");
      }
    }
    UserSession userSession = UserUtils.getTokenUser();
    String uuid = UUID.randomUUID().toString().replace("-", "");
    String key = ADD_ROBOT_TASK + uuid;
    if (!param.getAllSelect()) {
      List<Long> caseOperationIds = param.getOperationIdList();
      stringRedisTemplate.opsForSet().add(key, caseOperationIds.stream().map(String::valueOf).toArray(String[]::new));
    } else {
      if (systemConfig.getESSwitch()) {
        this.getAllDataFromES(param, userSession, (pageInfo) -> {
          if (!CollectionUtils.isEmpty(pageInfo.getList())) {
            stringRedisTemplate.opsForSet().add(key, pageInfo.getList().stream().map(CaseOperationResult::getId).map(String::valueOf).toArray(String[]::new));
          }
        });
      } else {
        this.getAllData(param, userSession, (pageInfo) -> {
          if (!CollectionUtils.isEmpty(pageInfo.getList())) {
            stringRedisTemplate.opsForSet().add(key, pageInfo.getList().stream().map(CaseOperationResult::getId).map(String::valueOf).toArray(String[]::new));
          }
        });
      }
    }
    stringRedisTemplate.expire(ADD_ROBOT_TASK + uuid, 2, TimeUnit.HOURS);
    caseOperationRobotAsyncThreadExecutor.submit(() -> {
      Date execTime = Objects.equals(param.getExecType(), 0) ? new Date() : new Date(param.getExecTime());
      List<String> caseOperationIdList = stringRedisTemplate.opsForSet().pop(key, 100);
      while (!CollectionUtils.isEmpty(caseOperationIdList)) {
        for (String id : caseOperationIdList) {
          CaseOperation caseOperation = this.selectByPrimaryKey(Long.parseLong(id));
          if (Objects.isNull(caseOperation)) {
            continue;
          }
          int sendStatus = 1;
          if (DateUtil.compare(DateUtil.offsetHour(new Date(), 1), execTime) < 0) {
            log.info("机器人执行时间大于1小时，不发送到mq");
            sendStatus = 0;
          }
          String macAddress = IpUtil.ip;
          RobotQueue robotQueue = new RobotQueue();
          robotQueue.setCaseId(caseOperation.getCaseId());
          robotQueue.setCreateBy(userSession.getId());
          robotQueue.setCreateTime(new Date());
          robotQueue.setExecTime(execTime);
          robotQueue.setOrgId(userSession.getOrgId());
          robotQueue.setStatus(sendStatus);
          robotQueue.setUpdateBy(userSession.getId());
          robotQueue.setUpdateTime(new Date());
          robotQueue.setOutSerialNo(caseOperation.getOutSerialTemp());
          robotQueue.setSourceMachine(macAddress);
          robotQueue.setSiteId(caseOperation.getSiteId());
          robotQueue.setSiteName(caseOperation.getSiteName());
          robotQueue.setCaller(param.getNewCaller());
          robotQueue.setPoolId(param.getNewPoolId());
          robotQueueService.insertSelective(robotQueue);
          if (sendStatus == 1) {
            //暂时不考虑发送失败的情况
            robotQueueService.sendDelayTime(robotQueue.getId(), execTime);
          }
          caseOperation.setStatus(CaseOperationEnums.Status.DELETE.getCode());
          caseOperation.setUpdateBy(userSession.getId());
          caseOperation.setUpdateTime(new Date());
          updateByPrimaryKey(caseOperation);
        }
        caseOperationIdList = stringRedisTemplate.opsForSet().pop(key, 100);
      }
    });
  }

  public Long exportRobot(CaseOperationRobotExportParam caseOperationRobotExportParam) {
    UserSession userSession = UserUtils.getTokenUser();
    DownloadTask downloadTask = new DownloadTask();
    downloadTask.setCreateTime(new Date());
    downloadTask.setUpdateTime(new Date());
    downloadTask.setType(caseOperationRobotExportParam.getType());
    downloadTask.setCreateBy(userSession.getId());
    downloadTask.setUpdateBy(userSession.getId());
    downloadTask.setStatus(DownloadTask.Status.PROCESSING.getCode());
    downloadTask.setExpireTime(DateUtils.addDays(new Date(), 3));
    downloadTask.setOrgId(userSession.getOrgId());
    downloadTask.setProgress(new BigDecimal(0));
    downloadTask.setData(JSON.toJSONString(caseOperationRobotExportParam));
    downloadTaskService.insertSelective(downloadTask);
    Map<Integer, String> callTypeNamesMap = orgConfigService.getAllNames(userSession.getOrgId(),OrgConfigEnums.Type.CALL_TYPE.getCode());
    caseOperationRobotAsyncThreadExecutor.submit(() -> {
      try {
        List<List<String>> header = createExportHeader(caseOperationRobotExportParam.getType());
        String fileName = "机器人执行导出" + downloadTask.getId() + ".xlsx";
        String sheetName = "机器人执行导出";
        ExcelWriter excelWriter = EasyExcel.write(fileName).build();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        excelWriter.write(header, writeSheet);
        Long total = 0L;
        if (!caseOperationRobotExportParam.getAllSelect()) {
          if (!CollectionUtils.isEmpty(caseOperationRobotExportParam.getOperationIdList())) {
            List<Long> caseOperationIdList = caseOperationRobotExportParam.getOperationIdList();
            CaseOperationParam caseOperationParam = new CaseOperationParam();
            caseOperationParam.setOperationIdList(caseOperationIdList);
            List<CaseOperationResult> caseOperations = caseOperationMapper.selectByMap(caseOperationParam);
            writeExcel(excelWriter, writeSheet, caseOperations, caseOperationRobotExportParam.getType(), callTypeNamesMap);
            total = (long) caseOperations.size();
          }
        } else if (systemConfig.getESSwitch()) {
          total = this.getAllDataFromES(caseOperationRobotExportParam, userSession, (pageInfo) -> writeExcel(excelWriter, writeSheet, pageInfo.getList(), caseOperationRobotExportParam.getType(), callTypeNamesMap));
        } else {
          total = this.getAllData(caseOperationRobotExportParam, userSession, (pageInfo) -> writeExcel(excelWriter, writeSheet, pageInfo.getList(), caseOperationRobotExportParam.getType(), callTypeNamesMap));
        }
        excelWriter.finish();
        if (total == 0) {
          downloadTask.setStatus(DownloadTask.Status.FAILED.getCode());
          downloadTask.setFailedReason("导出列表为空");
        } else {
          downloadTask.setStatus(2);
          File excelFile = new File(fileName);
          downloadTask.setFileSize(excelFile.getTotalSpace() / 1024);
          Date expireDate = DateUtils.addDays(new Date(), 3);
          UploadCreatedFileInfo uploadCreatedFileInfo = new UploadCreatedFileInfo();
          uploadCreatedFileInfo.setFile(excelFile)
                  .setFileName(fileName)
                  .setExpireDate(expireDate)
                  .setBucket(systemConfig.getTemporaryFileBucket())
                  .setLocalUrl(systemConfig.getLocalFilePath() + File.separator + fileName);
          FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
          String url = fileStorageStrategy.uploadCreatedFile(uploadCreatedFileInfo);
          downloadTask.setDownloadUrl(url);
        }

        downloadTask.setDataNums(total);
        downloadTask.setProgress(new BigDecimal(100));
        downloadTaskService.updateByPrimaryKeySelective(downloadTask);
      }catch (Exception ex){
        log.error("异步导出出错",ex);
        downloadTask.setUpdateTime(new Date());
        downloadTask.setStatus(DownloadTask.Status.FAILED.getCode());
        downloadTask.setFailedReason("导出出错,tracid:" + MDC.get(LogbackUtil.TRACE_ID));
        downloadTaskService.updateByPrimaryKeySelective(downloadTask);
      }
    });
    return downloadTask.getId();
  }

  private void writeExcel(ExcelWriter excelWriter,
                          WriteSheet writeSheet,
                          List<CaseOperationResult> list,
                          Integer type,
                          Map<Integer,String> callTypeNameMap) {
    if (!CollectionUtils.isEmpty(list)) {
      List<List<String>> data = convert(list, type, callTypeNameMap);
      excelWriter.write(data, writeSheet);
    }
  }

  private List<List<String>> createExportHeader(Integer type) {
    if (Objects.equals(type, DownloadTask.Type.CASE_OPERATION_ROBOT_SUCCESS_EXPORT.getCode())) {
      return Collections.singletonList(Arrays.asList("案件编号", "姓名", "债务人手机号", "联系人", "联系人手机号码", "催收时间", "呼叫时间", "呼叫类型", "机器人协催结果", "通话时长", "机器人协催客户标签", "主叫号码", "话术"));
    }
    if (Objects.equals(type, DownloadTask.Type.CASE_OPERATION_ROBOT_FAIL_EXPORT.getCode())) {
      return Collections.singletonList(Arrays.asList("案件编号", "姓名", "债务人手机号", "联系人", "联系人手机号码", "催收时间", "呼叫时间", "呼叫类型", "机器人协催结果", "通话时长", "机器人协催客户标签", "主叫号码", "话术"));
    }
    if (Objects.equals(type, DownloadTask.Type.CASE_OPERATION_ROBOT_STATISTICS_ALL_EXPORT.getCode())) {
      return Collections.singletonList(Arrays.asList("案件编号","案件批次号", "姓名", "联系人手机号", "催收时间", "呼叫时间", "呼叫类型", "机器人协催结果", "通话时长", "机器人协催客户标签", "主叫号码", "机器人话术"));
    }
    if (Objects.equals(type, DownloadTask.Type.CASE_OPERATION_ROBOT_STATISTICS_DATE_EXPORT.getCode())) {
      return Collections.singletonList(Arrays.asList("案件编号", "案件批次号","姓名", "联系人手机号", "催收时间", "呼叫时间", "机器人当日协催次数", "呼叫类型", "机器人协催结果", "通话时长", "机器人协催客户标签", "主叫号码", "机器人话术"));
    } else {
      throw new ApiException("未知的导出类型");
    }
  }

  private List<List<String>> convert(List<CaseOperationResult> list,
                                     Integer type, Map<Integer, String> callTypeNamesMap) {
    if (CollectionUtils.isEmpty(list)) {
      return new ArrayList<>();
    }
    List<List<String>> result = new ArrayList<>();
    Map<Long,String> outBatchMap = outBatchService.getNames();
    for (CaseOperationResult caseOperation : list) {
      List<String> tmp = new ArrayList<>();
      if (Objects.equals(type, DownloadTask.Type.CASE_OPERATION_ROBOT_SUCCESS_EXPORT.getCode())
        || Objects.equals(type, DownloadTask.Type.CASE_OPERATION_ROBOT_FAIL_EXPORT.getCode())) {
        tmp.add(caseOperation.getOutSerialTemp());
        tmp.add(caseOperation.getName());
        tmp.add(caseOperation.getOwnMobile());
        tmp.add(caseOperation.getConName());
        tmp.add(caseOperation.getConMobile());
        tmp.add(DateUtil.format(caseOperation.getCreateTime(), "yyyy-MM-dd"));
        tmp.add(DateUtil.format(caseOperation.getCreateTime(), "yyyy-MM-dd"));
        tmp.add(Objects.equals(CaseOperationEnums.CallStyle.COMEIN.getCode(), caseOperation.getCallStyle()) ? "呼入" : "呼出");
        tmp.add(callTypeNamesMap.get(caseOperation.getCallType()));
        tmp.add(String.valueOf(caseOperation.getCallDurtion()));
        tmp.add(caseOperation.getDesc());
        tmp.add(caseOperation.getCaller());
        tmp.add(caseOperation.getSiteName());
      } else if(Objects.equals(type, DownloadTask.Type.CASE_OPERATION_ROBOT_STATISTICS_ALL_EXPORT.getCode())) {
        tmp.add(caseOperation.getOutSerialTemp());
        tmp.add(outBatchMap.get(caseOperation.getOutBatchId()));
        tmp.add(caseOperation.getName());
        tmp.add(caseOperation.getConMobile());
        tmp.add(DateUtil.format(caseOperation.getCreateTime(), "yyyy-MM-dd"));
        tmp.add(DateUtil.format(caseOperation.getCreateTime(), "yyyy-MM-dd"));
        tmp.add(Objects.equals(CaseOperationEnums.CallStyle.COMEIN.getCode(), caseOperation.getCallStyle()) ? "呼入" : "呼出");
        tmp.add(callTypeNamesMap.get(caseOperation.getCallType()));
        tmp.add(String.valueOf(caseOperation.getCallDurtion()));
        tmp.add(caseOperation.getDesc());
        tmp.add(caseOperation.getCaller());
        tmp.add(caseOperation.getSiteName());
      }else {
        tmp.add(caseOperation.getOutSerialTemp());
        tmp.add(outBatchMap.get(caseOperation.getOutBatchId()));
        tmp.add(caseOperation.getName());
        tmp.add(caseOperation.getConMobile());
        tmp.add(DateUtil.format(caseOperation.getCreateTime(), "yyyy-MM-dd"));
        tmp.add(DateUtil.format(caseOperation.getCreateTime(), "yyyy-MM-dd"));
        tmp.add(Objects.isNull(caseOperation.getOperTime())?"":String.valueOf(caseOperation.getOperTime()));
        tmp.add(Objects.isNull(caseOperation.getTotalOperTime())?"":String.valueOf(caseOperation.getTotalOperTime()));
        tmp.add(Objects.equals(CaseOperationEnums.CallStyle.COMEIN.getCode(), caseOperation.getCallStyle()) ? "呼入" : "呼出");
        tmp.add(callTypeNamesMap.get(caseOperation.getCallType()));
        tmp.add(String.valueOf(caseOperation.getCallDurtion()));
        tmp.add(caseOperation.getDesc());
        tmp.add(caseOperation.getCaller());
        tmp.add(caseOperation.getSiteName());
      }
      result.add(tmp);
    }
    return result;
  }
}
