package com.anmi.collection.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.flow.ApplyEnums;
import com.anmi.collection.common.enums.flow.FlowEnums;
import com.anmi.collection.common.enums.flow.FlowHandleRecordEnums;
import com.anmi.collection.constant.AntiFraudConstant;
import com.anmi.collection.encrypt.EncryptProperties;
import com.anmi.collection.encrypt.EncryptService;
import com.anmi.collection.entity.requset.cases.AntiFraudParam;
import com.anmi.collection.entity.requset.flow.ApproveListParam;
import com.anmi.collection.entity.requset.query.cases.AntiFraudQuery;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.AntiFraudVO;
import com.anmi.collection.entity.response.flow.ApproveListVO;
import com.anmi.collection.entity.response.flow.FlowNodeVO;
import com.anmi.collection.entity.response.flow.FlowVO;
import com.anmi.collection.mapper.AntiFraudMapper;
import com.anmi.collection.service.flow.FlowService;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.AntiFraud;
import com.anmi.domain.cases.Case;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/13 21:13
 */
@Service
public class AntiFraudService extends BaseService<AntiFraud> {
    @Resource private AntiFraudMapper antiFraudMapper;
    @Resource private CaseService caseService;
    @Resource private UserService userService;
    @Resource private DepTeamService depTeamService;
    @Resource private ProductService productService;
    @Resource private EncryptProperties encryptProperties;
    @Resource private EncryptService encryptService;
    @Resource private FlowService flowService;

    /**
     * 反欺诈申请
     *
     * @param param 参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void addAntiFraudApply(AntiFraudParam param) throws Exception{
        UserSession userSession = UserUtils.getTokenUser();
        Case ca = caseService.selectByPrimaryKey(param.getCaseId());
        FlowEnums.AgentType agentType = ObjectUtil.equals(ca.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?FlowEnums.AgentType.OUTER:FlowEnums.AgentType.INNER;
        // 获取审批流
        FlowVO flowVO = flowService.flowChoice(userSession.getOrgId(), agentType, FlowEnums.BusinessType.FQZ);
        // 申请是否自动通过
        Boolean autoPassApply = flowService.autoPassApply(userSession,flowVO);
        AntiFraud antiFraud = AuthBeanUtils.copy(param, AntiFraud.class);
        antiFraud.setDeptId(ca.getDepId());
        antiFraud.setTeamId(ca.getTeamId());
        antiFraud.setAllotAgent(ObjectUtil.equals(ca.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?CaseEnums.AllotAgent.WW.getCode():CaseEnums.AllotAgent.NC.getCode());
        antiFraud.setOrgId(userSession.getOrgId());
        antiFraud.setApplicant(userSession.getId());
        antiFraud.setApplyTime(new Date());
        antiFraud.setCreateBy(userSession.getId());
        antiFraud.setUpdateBy(userSession.getId());
        antiFraud.setCreateTime(new Date());
        antiFraud.setUpdateTime(new Date());
        antiFraud.setFlowId(flowVO.getId());
        if (autoPassApply){
            antiFraud.setStatus(1);
            antiFraud.setAuditTime(new Date());
            antiFraud.setAuditDesc("自动通过");
        }
        Integer timeLimitSwitch = flowVO.getTimeLimitSwitch();
        if (ObjectUtil.equals(timeLimitSwitch, FlowEnums.TimeLimitSwitch.YES.getCode())){
            String timeLimit = flowVO.getTimeLimit();
            String hour = StrUtil.subBefore(timeLimit, ":", true);
            String minute = StrUtil.subAfter(timeLimit, ":", true);
            Date outTime = DateUtil.offsetMinute(DateUtil.offsetHour(new Date(),Integer.valueOf(hour)),Integer.valueOf(minute));
            antiFraud.setOutTime(outTime);
        }
        antiFraudMapper.insertSelective(antiFraud);
        List<FlowNodeVO> nodes = flowVO.getNodes();
        Map<String,Object> applyMap = BeanUtil.beanToMap(antiFraud);
        String parentUniqueCode = flowService.getApplyOrRuleNodeUniqueCode(nodes,applyMap,FlowEnums.BusinessType.FQZ,agentType,userSession);
        if (autoPassApply){
            flowService.findNextCopyNodeAndExecute(parentUniqueCode,flowVO, antiFraud.getId(),userSession);
        } else {
            flowService.findNextNodeAndExecute(parentUniqueCode,flowVO, antiFraud.getId(),userSession, userSession.getId());
        }
    }

    /**
     * 反欺诈审核
     *
     * @param param 参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void auditAntiFraud(AntiFraudParam param) throws Exception{
        UserSession userSession = UserUtils.getTokenUser();
        List<Long> applyIds = param.getIds();
        AssertUtil.notEmpty(applyIds,"请选择申请");
        AssertUtil.isTrue(applyIds.size()==1,"暂不支持批量审批");
        AssertUtil.isTrue(ObjectUtil.equals(param.getStatus(),1)||ObjectUtil.equals(param.getStatus(),-1),"审批状态错误");

        Long applyId = applyIds.get(0);
        AntiFraud applyRecord = selectByPrimaryKey(applyId);
        AssertUtil.notNull(applyRecord, "未发现申请");
        AssertUtil.isTrue(ObjectUtil.equals(applyRecord.getStatus(), 0), "申请非审批中");
        AssertUtil.notNull(applyRecord.getFlowId(), "审批流异常");

        Long flowId = applyRecord.getFlowId();
        Long applyUserId = applyRecord.getApplicant();

        FlowHandleRecordEnums.HandleStatus approveStatus = ObjectUtil.equals(param.getStatus(),1)?FlowHandleRecordEnums.HandleStatus.PASS:FlowHandleRecordEnums.HandleStatus.REFUSE;
        flowService.execApprove(applyId,flowId,approveStatus,param.getAuditDesc(),userSession,applyUserId);
    }

    /**
     * 更新申请 处理状态
     *
     * @param applyId       申请id
     * @param approveStatus 审批状态
     * @param updater       更新人
     */
    public void updateHandleStatus(Long applyId, FlowHandleRecordEnums.HandleStatus approveStatus, Long updater) {
        Integer status = ObjectUtil.equals(approveStatus,FlowHandleRecordEnums.HandleStatus.PASS)?1:-1;
        AntiFraud applyRecord = new AntiFraud();
        applyRecord.setId(applyId);
        applyRecord.setStatus(status);
        applyRecord.setUpdateBy(updater);
        applyRecord.setUpdateTime(new Date());
        applyRecord.setAuditTime(new Date());
        applyRecord.setAuditor(updater);
        updateByPrimaryKeySelective(applyRecord);
    }

    /**
     * 反欺诈取消
     *
     * @param id id
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelAntiFraud(Long id) {
        AntiFraud applyRecord = selectByPrimaryKey(id);
        AssertUtil.notNull(applyRecord, "未发现申请");
        AssertUtil.isTrue(ObjectUtil.equals(applyRecord.getStatus(), 0), "申请非审批中");
        AssertUtil.notNull(applyRecord.getFlowId(), "审批流异常");
        Long flowId = applyRecord.getFlowId();
        UserSession userSession = UserUtils.getTokenUser();
        flowService.revokeCheck(id, flowId);

        AntiFraud antiFraud = new AntiFraud();
        antiFraud.setId(id);
        antiFraud.setStatus(AntiFraudConstant.APPLY_STATUS_CANCEL);
        antiFraud.setUpdateBy(userSession.getId());
        antiFraud.setUpdateTime(new Date());
        antiFraudMapper.updateByPrimaryKeySelective(antiFraud);
    }


    public PageOutput<AntiFraudVO> getList(AntiFraudQuery query) {
        UserSession userSession = UserUtils.getTokenUser();
        query.setOrgId(userSession.getOrgId());
        String applyTimeStr = query.getApplyTimeStr();
        if (StringUtils.isNotBlank(applyTimeStr)) {
            query.setApplyTimeStart(convertDate(applyTimeStr.split(",")[0]));
            query.setApplyTimeEnd(convertDate(applyTimeStr.split(",")[1]));
        }
        String auditTimeStr = query.getAuditTimeStr();
        if (StringUtils.isNotBlank(auditTimeStr)) {
            query.setAuditTimeStart(convertDate(auditTimeStr.split(",")[0]));
            query.setAuditTimeEnd(convertDate(auditTimeStr.split(",")[1]));
        }
        query.setMinAmount(ObjectUtil.isNull(query.getMinAmount()) ? null : query.getMinAmount().multiply(new BigDecimal(1000)));
        query.setMaxAmount(ObjectUtil.isNull(query.getMaxAmount()) ? null : query.getMaxAmount().multiply(new BigDecimal(1000)));
        PageParam pageParam = new PageParam(query.getPage(), query.getLimit());
        Page page = PageUtils.setPage(pageParam);
        List<AntiFraudVO> antiFraudVOS = antiFraudMapper.getList(query);
        Map<Long, String> userMap = userService.getNames(userSession.getOrgId());
        Map<Long, String> productMap = productService.getNames();
        Map<Long, String> depTeamMap = depTeamService.getNames();
        antiFraudVOS.forEach(antiFraudVO -> {
            if (encryptProperties.getEnable()) {
                antiFraudVO.setName(encryptService.decrypt(antiFraudVO.getName()));
                antiFraudVO.setIdCard(encryptService.decrypt(antiFraudVO.getIdCard()));
                antiFraudVO.setMobile(encryptService.decrypt(antiFraudVO.getMobile()));
            }
            antiFraudVO.setApplicantName(userMap.get(antiFraudVO.getApplicant()));
            antiFraudVO.setAuditorName(userMap.get(antiFraudVO.getAuditor()));
            antiFraudVO.setDeptName(depTeamMap.get(antiFraudVO.getDeptId()));
            antiFraudVO.setTeamName(depTeamMap.get(antiFraudVO.getTeamId()));
            antiFraudVO.setProductName(productMap.get(antiFraudVO.getProductId()));
            if (ObjectUtil.isNotNull(antiFraudVO.getAllotAgent())){
                antiFraudVO.setAgentType(ObjectUtil.equals(antiFraudVO.getAllotAgent(), 1) ? ApplyEnums.AgentType.INNER.getCode():ApplyEnums.AgentType.OUTER.getCode());
            }
        });
        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), antiFraudVOS);

    }

    private String convertDate(String date) {
        if (!CommonUtil.isEmpty(date) && CommonUtil.isInteger(date)) {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.valueOf(date)));
        }
        // 是否是标准时间格式，否则返回空
        if (!CommonUtil.isValidDate(date)) {
            return null;
        }
        return date;
    }

    /**
     * 我的审批列表
     *
     * @param param      param
     * @param orgId      组织id
     * @param userId     用户id
     * @param formFields 表单字段
     * @return {@link List}<{@link ApproveListVO}>
     */
    public List<ApproveListVO> approveList(ApproveListParam param, Long orgId, Long userId, String formFields) {
        String applyTimeStartStr = null;
        String applyTimeEndStr = null;
        String applyTime = param.getApplyTime();
        if (StrUtil.isNotBlank(applyTime)) {
            String[] range = applyTime.split(",");
            if (range.length == 2) {
                applyTimeStartStr = DateUtils.convertDate(range[0]);
                applyTimeEndStr = DateUtils.convertDate(range[1]);
            }
        }
        return antiFraudMapper.approveList(param, orgId, userId, applyTimeStartStr, applyTimeEndStr, formFields);
    }

    /**
     * 审批待处理统计
     *
     * @param orgId        组织id
     * @param userId       用户id
     * @param businessType 业务类型
     * @return {@link Integer}
     */
    public Integer todoStatistics(Long orgId, Long userId, FlowEnums.BusinessType businessType) {
        return antiFraudMapper.todoStatistics(orgId,userId,businessType.getCode());
    }

    /**
     * 超时记录ID
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link Long}>
     */
    public List<Long> timeOutRecordIds(Date startTime, Date endTime) {
        Example example = new Example(AntiFraud.class);
        example.selectProperties("id");
        Example.Criteria criteria = example.and();
        criteria.andEqualTo("status", 0)
                .andIsNotNull("outTime")
                .andGreaterThanOrEqualTo("outTime",startTime)
                .andLessThan("outTime",endTime);

        List<AntiFraud> recordes = selectByExample(example);
        List<Long> timeOutRecordIds = recordes.stream().map(AntiFraud::getId).collect(Collectors.toList());
        return timeOutRecordIds;
    }

    /**
     * 状态更新为超时
     *
     * @param applyIds 申请id
     */
    public void updateStatusWithTimeOut(List<Long> applyIds) {
        if (ObjectUtil.isEmpty(applyIds)){
            return;
        }
        Example example = new Example(AntiFraud.class);
        Example.Criteria criteria = example.and();
        criteria.andIn("id", applyIds)
                .andEqualTo("status",0);

        AntiFraud update = new AntiFraud();
        update.setStatus(5);
        update.setUpdateTime(new Date());

        updateByExampleSelective(update,example);
    }

    /**
     * 案件删除 级联删除反欺诈案件
     *
     * @param caseIds 案件ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void delAntiFraud(List<Long> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }
        Example example = new Example(AntiFraud.class);
        example.and().andIn("caseId", caseIds);
        antiFraudMapper.deleteByExample(example);
    }
}
