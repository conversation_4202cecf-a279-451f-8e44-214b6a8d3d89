package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.CaseNoteEnums;
import com.anmi.collection.common.enums.RoleEnums;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.CaseNoteVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.CaseNoteMapper;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.domain.cases.CaseNote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class CaseNoteService extends BaseService<CaseNote> {

  @Autowired private UserService userService;
  @Autowired private CaseNoteMapper caseNoteMapper;

  public void save(Long id, Long caseId, String content, Byte type) {
    if (StringUtils.isBlank(content)) {
      throw new ApiException("请输入内容");
    }
    if (caseId == null) {
      throw new ApiException("案件编号不能为空");
    }
    UserSession userSession = getTokenUser();
    Long userId = userSession.getId();
    if (CaseNoteEnums.Type.COMMENT.getCode().equals(type)
        && (userSession.getRoleType() == null
            || (userSession.getRoleType() != RoleEnums.type.CUSTOM_ADMIN.getCode()
                && userSession.getRoleType() != RoleEnums.type.BRANCH_ADMIN.getCode()
                && userSession.getRoleType() != RoleEnums.type.SUB_ACCOUNT_ADMIN.getCode()
                && userSession.getRoleType() != RoleEnums.type.TEAM_LEADER.getCode()))) {
      // 只有管理员可以添加评语
      throw new ApiException("非管理员不能添加修改评语");
    }
    if (id == null) {
      CaseNote caseNote = new CaseNote();
      caseNote.setType(type);
      caseNote.setCaseId(caseId);
      caseNote.setContent(content);
      caseNote.setCreateBy(userId);
      caseNote.setUpdateBy(userId);
      insertSelective(caseNote);
    } else {
      CaseNote caseNote = selectByPrimaryKey(id);
      if (caseNote == null || !caseNote.getCaseId().equals(caseId)) {
        throw new ApiException("该记录不存在，不能修改");
      }
      caseNote.setContent(content);
      caseNote.setUpdateBy(userId);
      caseNote.setUpdateTime(new Date());
      updateByPrimaryKeySelective(caseNote);
    }
  }

  public PageOutput selectPageByCaseId(Long caseId, PageParam pageParam, Byte type)
      throws IllegalAccessException, InvocationTargetException, InstantiationException {
    Example example = new Example(CaseNote.class);
    example.createCriteria().andEqualTo("caseId", caseId).andEqualTo("type", type);
    example.setOrderByClause("id desc");
    PageOutput page = selectByPage(example, pageParam);
    List<CaseNote> caseNoteList = page.getList();
    List<CaseNoteVO> caseNoteVOList =
        BeanUtil.copyPropertiesFromList(caseNoteList, CaseNoteVO.class);
    for (int i = 0; i < caseNoteVOList.size(); i++) {
      CaseNoteVO caseNoteVO = caseNoteVOList.get(i);
      CaseNote ca = caseNoteList.get(i);
      caseNoteVO.setCreateBy(
          (ca.getCreateBy() == null || ca.getCreateBy() == 0L)
              ? ""
              : userService.selectNameById(ca.getCreateBy()).getName());
      caseNoteVO.setUpdateBy(
          (ca.getUpdateBy() == null || ca.getUpdateBy() == 0L)
              ? ""
              : userService.selectNameById(ca.getUpdateBy()).getName());
      caseNoteVO.setCreateTime(ca.getCreateTime().getTime());
      caseNoteVO.setUpdateTime(ca.getUpdateTime().getTime());
    }
    page.setList(caseNoteVOList);
    return page;
  }

  public void delete(Long id) {
    CaseNote caseNote = selectByPrimaryKey(id);
    if (caseNote == null) {
      throw new ApiException("该记录不存在");
    }
    if (CaseNoteEnums.Type.COMMENT.getCode().equals(caseNote.getType())) {
      UserSession userSession = getTokenUser();
      if (userSession.getRoleType() == null
          || (userSession.getRoleType() != RoleEnums.type.CUSTOM_ADMIN.getCode()
              && userSession.getRoleType() != RoleEnums.type.BRANCH_ADMIN.getCode()
              && userSession.getRoleType() != RoleEnums.type.SUB_ACCOUNT_ADMIN.getCode()
              && userSession.getRoleType() != RoleEnums.type.TEAM_LEADER.getCode())) {
        throw new ApiException("非管理员不能删除评语");
      }
    }
    super.deleteByPrimaryKey(id);
  }

  /**
   * 查询最近的催记小结
   * @param caseIds
   * @return
   */
  public List<CaseNote> selectNoteByCaseIds(List<Long> caseIds, Byte type) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return Collections.emptyList();
    }
    return caseNoteMapper.selectNoteByCaseIds(caseIds, type);
  }

  @Transactional(rollbackFor = Exception.class)
  public void addCaseNote(List<CaseNote> caseNoteList) {
    if (CollectionUtils.isEmpty(caseNoteList)) {
      return;
    }
    caseNoteMapper.insertList(caseNoteList);
  }

  /**
   * 案件级联删除案件小结、评语
   *
   * @param caseIds 案件ids
   */
  @Transactional(rollbackFor = Exception.class)
  public void delNoteByCaseIds(List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return;
    }
    Example example = new Example(CaseNote.class);
    example.and().andIn("caseId", caseIds);
    caseNoteMapper.deleteByExample(example);
  }
}
