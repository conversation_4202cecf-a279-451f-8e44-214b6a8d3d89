package com.anmi.collection.service;

import com.anmi.alfred.response.concrete.OutBatchStatisticsResponse;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.CompanyEnums;
import com.anmi.collection.entity.requset.cases.BatchChangeStatusParam;
import com.anmi.collection.entity.requset.cases.BatchChangeTargetParam;
import com.anmi.collection.entity.requset.cases.OutBatchQueryParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.OutBatchVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.OutBatchMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.remote.RemoteAlfredService;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.domain.cases.InnerBatch;
import com.anmi.domain.cases.OutBatch;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OutBatchService extends BaseService<OutBatch> {

  @Autowired private DeltService deltService;
  @Autowired private RemoteAlfredService remoteAlfredService;
  @Autowired private OutBatchMapper outBatchMapper;

  @Cacheable(value = KeyCache.OUT_BATCH_NAME)
  public Map<Long, String> getNames() {
    List<OutBatch> list = super.getNameIdMap("name");
    return list.stream().collect(Collectors.toMap(OutBatch::getId, OutBatch::getName));
  }

  @Override
  @CacheEvict(value = KeyCache.OUT_BATCH_NAME, allEntries = true)
  public int deleteByPrimaryKey(Object entity) {
    return super.deleteByPrimaryKey(entity);
  }

  @CacheEvict(value = KeyCache.OUT_BATCH_NAME, allEntries = true)
  public Long createDeltBatchNo(String name, Long orgId, Long orgDeltId, Long createBy) {
    OutBatch outBatch = new OutBatch();
    outBatch.setName(name);
    outBatch.setOrgId(orgId);
    outBatch.setOrgDeltId(orgDeltId);
    outBatch.setStatus(InnerBatch.Status.NORMAL.getCode());
    outBatch.setCreateBy(createBy);
    outBatch.setUpdateBy(createBy);
    super.insertSelective(outBatch);
    return outBatch.getId();
  }

  public PageOutput<OutBatchVO> selectByOrgId(
      PageParam pageParam, OutBatchQueryParam outBatchQueryParam) throws Exception {
    UserSession userSession = getTokenUser();
    Example example = new Example(OutBatch.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("orgId", userSession.getOrgId());
    if (outBatchQueryParam.getStatus() != null) {
      criteria.andEqualTo("status", outBatchQueryParam.getStatus());
    }
    if (StringUtils.isNotBlank(outBatchQueryParam.getOrgDeltIds())) {
      criteria.andIn(
          "orgDeltId", Lists.newArrayList(outBatchQueryParam.getOrgDeltIds().split(",")));
    }
    if (StringUtils.isNotBlank(outBatchQueryParam.getName())) {
      criteria.andLike("name", outBatchQueryParam.getName().trim());
    }
    if (!CollectionUtils.isEmpty(outBatchQueryParam.getOutBatchIds())) {
      criteria.andIn("id", outBatchQueryParam.getOutBatchIds());
    }
    example.setOrderByClause("id desc");
    PageOutput pageOutput = selectByPage(example, pageParam);
    List<OutBatch> list = pageOutput.getList();
    if (CollectionUtils.isEmpty(list)) {
      return pageOutput;
    }
    List<OutBatchVO> outBatchVOList = BeanUtil.copyPropertiesFromList(list, OutBatchVO.class);
    Map<Long,String> deltMap = deltService.getNames();
    for (OutBatchVO outBatchVO : outBatchVOList) {
      outBatchVO.setOrgDeltName(deltMap.get(outBatchVO.getOrgDeltId()));
    }
    if(Objects.equals(outBatchQueryParam.getIsBank(), CompanyEnums.BankAmc.YES.getCode())){
      List<Long> outBatchIds = outBatchVOList.stream().map(OutBatchVO::getId).collect(Collectors.toList());
      if(!CollectionUtils.isEmpty(outBatchIds)) {
        List<OutBatchVO> tmp = selectOutBatchAmount(outBatchIds);
        Map<Long,OutBatchVO> outBatchVOMap = tmp.stream().collect(Collectors.toMap(OutBatchVO::getId, Function.identity()));
        for (OutBatchVO outBatchVO : outBatchVOList) {
          OutBatchVO vo = outBatchVOMap.get(outBatchVO.getId());
          outBatchVO.setAmount(Optional.ofNullable(vo).orElse(new OutBatchVO()).getAmount());
          outBatchVO.setRepAmount(Optional.ofNullable(vo).orElse(new OutBatchVO()).getRepAmount());
          outBatchVO.setTargetRepAmount((vo == null || outBatchVO.getTargetRate() == null) ? null : outBatchVO.getTargetRate().multiply(new BigDecimal(vo.getAmount() / 100)).longValue());
          outBatchVO.setRealRate(vo == null ? null : new BigDecimal(vo.getRepAmount() * 100).divide(new BigDecimal(vo.getAmount()),2,BigDecimal.ROUND_HALF_UP));
          outBatchVO.setCompletionRate((vo == null || outBatchVO.getTargetRepAmount() == null) ? null : new BigDecimal(vo.getRepAmount() * 100).divide(new BigDecimal(outBatchVO.getTargetRepAmount()),2, BigDecimal.ROUND_HALF_UP));
        }
      }
    }
    pageOutput.setList(outBatchVOList);
    return pageOutput;
  }

  //根据委外批次id统计案件的还款信息和金额信息
  //返回值里面只有id，amount，repAmount字段
  public List<OutBatchVO> selectOutBatchAmount(List<Long> outBatchIds) {
    if (systemConfig.getESSwitch()) {
      List<OutBatchStatisticsResponse> responseEntity = remoteAlfredService.fetchUserCaseStatisticsQuery(outBatchIds);
      List<OutBatchVO> result = new ArrayList<>();
      for (OutBatchStatisticsResponse response : responseEntity) {
        OutBatchVO outBatchVO = new OutBatchVO();
        outBatchVO.setId(response.getOutBatchId());
        outBatchVO.setAmount(response.getAmount());
        outBatchVO.setRepAmount(response.getRepAmount());
        result.add(outBatchVO);
      }
      return result;
    }
    return outBatchMapper.selectOutBatchAmount(outBatchIds);
  }

  public void changeStatus(BatchChangeStatusParam param) {
    if (param.getId() == null || param.getStatus() == null) {
      throw new ApiException("参数不能为空");
    }
    OutBatch outBatch = selectByPrimaryKey(param.getId());
    if (outBatch == null) {
      throw new ApiException("委案方批次号不存在");
    }
    if (param.getStatus().equals(OutBatch.Status.NORMAL.getCode())
        && outBatch.getStatus().equals(OutBatch.Status.NORMAL.getCode())) {
      throw new ApiException("委案方批次号已是正常状态");
    }
    if (param.getStatus().equals(OutBatch.Status.DELETE.getCode())
        && outBatch.getStatus().equals(OutBatch.Status.DELETE.getCode())) {
      throw new ApiException("委案方批次号已是删除状态");
    }
    OutBatch domain = new OutBatch();
    domain.setId(param.getId());
    domain.setStatus(param.getStatus());
    domain.setUpdateBy(getTokenUser().getId());
    domain.setUpdateTime(new Date());
    updateByPrimaryKeySelective(domain);
  }

  public Map<Long, String> getOutBatchMap(List<Long> outBatchIdList) {
    if (CollectionUtils.isEmpty(outBatchIdList)) {
      return new HashMap<>();
    }
    Example example = new Example(OutBatch.class);
    example.createCriteria().andIn("id", outBatchIdList);
    List<OutBatch> outBatchList = super.selectByExample(example);
    if (CollectionUtils.isEmpty(outBatchList)) {
      return new HashMap<>();
    }
    Map<Long, String> map =
        outBatchList.stream().collect(Collectors.toMap(OutBatch::getId, OutBatch::getName));
    return map;
  }

  public void changeTargetRate(BatchChangeTargetParam param) {
    if (param.getId() == null || param.getTargetRate() == null) {
      throw new ApiException("参数不能为空");
    }
    OutBatch outBatch = selectByPrimaryKey(param.getId());
    if (outBatch == null || !Objects.equals(outBatch.getOrgId(), getTokenUser().getOrgId())) {
      throw new ApiException("委案方批次号不存在");
    }
    OutBatch update = new OutBatch();
    update.setId(param.getId());
    update.setTargetRate(param.getTargetRate());
    update.setUpdateBy(getTokenUser().getId());
    update.setUpdateTime(new Date());
    updateByPrimaryKeySelective(update);
  }
}
