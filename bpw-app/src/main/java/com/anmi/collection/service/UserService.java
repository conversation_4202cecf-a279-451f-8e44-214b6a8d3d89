package com.anmi.collection.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.common.enums.SysEnums;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.constant.AuthConstants;
import com.anmi.collection.constant.CommonConstant;
import com.anmi.collection.constant.SmsContants;
import com.anmi.collection.deploy.DeployStrategyHolder;
import com.anmi.collection.entity.requset.mediate.MediateUserParam;
import com.anmi.collection.entity.requset.sms.SmsSet;
import com.anmi.collection.entity.requset.sms.SmsUserParam;
import com.anmi.collection.entity.requset.sys.user.*;
import com.anmi.collection.entity.response.mediate.MediateUserVO;
import com.anmi.collection.entity.response.sms.SmsUserVO;
import com.anmi.collection.entity.response.sys.dep.DepTeamVO;
import com.anmi.collection.entity.response.sys.role.RoleVO;
import com.anmi.collection.entity.response.sys.user.*;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.manager.YunpianManager;
import com.anmi.collection.manager.region.RegionHolder;
import com.anmi.collection.manager.region.RegionInfo;
import com.anmi.collection.mapper.*;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.*;
import com.anmi.domain.cases.CaseCooperation;
import com.anmi.domain.cases.CaseQueryResult;
import com.anmi.domain.cases.OrgSwitch;
import com.anmi.domain.sys.LoginLog;
import com.anmi.domain.user.*;
import com.anmi.domain.visit.VisitInfo;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/** Created by dongwang on 2018-11-29. */
@Service
@Slf4j
public class UserService extends BaseService<User> {

  @Autowired private CompanyService companyService;
  @Autowired private DepTeamService depTeamService;
  @Autowired private RoleService roleService;
  @Autowired private SystemConfig systemConfig;
  @Autowired private DuyanManager duyanManager;
  @Autowired private CaseService caseService;
  @Autowired private StringRedisTemplate stringRedisTemplate;
  @Autowired private UserRoleMapper userRoleMapper;
  @Autowired private UserRoleService userRoleService;
  @Autowired private UserMapper userMapper;
  @Autowired private InspectorsMapper inspectorsMapper;
  @Autowired private RoleMapper roleMapper;
  @Autowired private UserHandleService userHandleService;
  @Autowired private InspectorsService inspectorsService;
  @Autowired private VisitService visitService;
  @Autowired private CaseMapper caseMapper;
  @Autowired private CaseCooperationService caseCooperationService;
  @Autowired private DictionaryService dictionaryService;
  @Autowired private LoginLogService loginLogService;
  @Autowired private OrgSwitchMapper orgSwitchMapper;
  @Autowired private I18nService i18nService;
  @Autowired private DeployStrategyHolder deployStrategyHolder;
  @Resource private OrgAgentDetailService orgAgentDetailService;
  @Resource private YunpianManager yunpianManager;


  /**
   * 登录有这么一个周期：第一次登陆（无需图形码） --》登录失败（需要图形码）--》登录成功后退出再登录（无需图形码）
   *
   * @param loginParam
   * @return
   */
  public ResultMessage authLogin(LoginParam loginParam) {
    loginParam.setPassword(EncryptUtil.base64Decode(loginParam.getPassword()));

    // 按要求登录接口每次都校验验证码
    boolean result = ValidateCodeUtil.validate(loginParam.getImageUuid(), loginParam.getValidateCode());
    if (!result) {
      throw new ApiException("验证码错误！");
    }
    // step 2 ：校验密码。
    User loginUser = getUserByLoginName(loginParam);
    if (loginUser == null || loginUser.getStatus() != 0) {
      return ResultMessage.error(
              AuthConstants.AUTH_USER_NOT_FOUND,
              AuthConstants.AUTH_USER_NOT_FOUND_MESSAGE,
              null);
    }

    String ip = AuthUtils.getIpAddr(CmUtil.getRequest());
    ip = StrUtil.subBefore(ip, ",", false).trim();

    Company company = null;
    OrgSwitch orgSwitch = null;
    // org_id 为0 是超级管理员
    if (null != loginUser.getOrgId() && loginUser.getOrgId() > 0) {
      // 校验总公司是否在合作期限内
      company = companyService.selectByPrimaryKey(loginUser.getOrgId());
      // 校验总公司是否是本地化公司，已经本地化的公司不让登陆云平台
      if (!systemConfig.getLocalDeploy() && company.getLocalDeploy() == 1) {
        throw new ApiException("您已为本地化客户，无法登录云平台！");
      }
      if (company.getStatus() != 0) {
        throw new ApiException("公司状态异常，请联系客服！");
      }
      // 判断合作时间
      if (!systemConfig.getLocalDeploy()) {
        // 非本地化需要判断
        Boolean between =
                DateUtils.between(
                        new Date(), company.getCooperationStartTime(), company.getCooperationEndTime());
        if (between == null || !between) {
          throw new ApiException("公司不在合作中，请联系商务！");
        }
      }
    } else {
      loginUser.setRole("1");
    }

    // 错误次数
    String failedAttemptsKey = KeyCache.ACCOUNT_LOGIN + loginUser.getId() + ":failedAttempts";
    String failedAttemptsStr = stringRedisTemplate.opsForValue().get(failedAttemptsKey);
    int failedAttempts = failedAttemptsStr != null ? Integer.parseInt(failedAttemptsStr) : 0;
    // 冻结状态
    String lockoutKey = KeyCache.ACCOUNT_LOGIN + loginUser.getId() + ":lockout";
    String lockoutStr = stringRedisTemplate.opsForValue().get(lockoutKey);
    int lockout = StringUtils.isBlank(lockoutStr) ? 0:Integer.parseInt(lockoutStr);
    // 账号是否锁定
    if (ObjectUtil.equals(lockout, 1)) {
      return ResultMessage.error(
              AuthConstants.AUTH_ACCOUNT_LOCK_STATUS,
              AuthConstants.AUTH_ACCOUNT_LOCK_MESSAGE);
    }

    String password = MD5Util.digest(loginParam.getPassword() + loginUser.getSalt());
    if (!password.equals(loginUser.getPassword())) {
      // 账号未被锁住，判断当前次数
      failedAttempts = failedAttempts+1;
      if (failedAttempts >= KeyCache.MAX_FAILED_ATTEMPTS) {
        // 账号锁定后密码错误计数重置
        stringRedisTemplate.delete(failedAttemptsKey);
        stringRedisTemplate.opsForValue().set(lockoutKey, String.valueOf(1), 30, TimeUnit.MINUTES);
        return ResultMessage.error(
                AuthConstants.AUTH_ACCOUNT_LOCK_STATUS,
                AuthConstants.AUTH_ACCOUNT_LOCK_MESSAGE);
      } else {
        if (stringRedisTemplate.hasKey(failedAttemptsKey)) {
          stringRedisTemplate.opsForValue().increment(failedAttemptsKey, 1);
        } else {
          stringRedisTemplate.opsForValue().set(failedAttemptsKey, String.valueOf(1), 60, TimeUnit.MINUTES);
        }
        return ResultMessage.error(
                AuthConstants.AUTH_ACCOUNT_NOT_FOUND_STATUS,
                AuthConstants.AUTH_ACCOUNT_NOT_FOUND_MESSAGE,
                null);
      }
    } else {
      // 密码正确 删除计数、锁定状态
      stringRedisTemplate.delete(failedAttemptsKey);
      stringRedisTemplate.delete(lockoutKey);
    }

    Integer strongPasswordSwitch = 0;
    if (ObjectUtil.isNotNull(orgSwitch)) {
      strongPasswordSwitch = orgSwitch.getStrongPasswordSwitch();
      //如果开启加强密码验证
      if (Objects.equals(orgSwitch.getStrongPasswordSwitch(), CommonConstant.IS_STRONG_PASSWORD)) {
        //强密码必须包含大小写字母和数字，特殊字符，长度在8-16之间
        boolean matches = loginParam.getPassword().matches("^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&*_=+-]).{8,16}$");
        if (!matches) {
          //设置这个值给前端，重置密码
          loginUser.setChangePasswd(0);
        }
      }
    }

    // step 3 ：生成token和session
    String token = UserUtils.setToken(loginUser, null, 0);
    LoginLog loginLog=new LoginLog();
    loginLog.setCreateTime(new Date());
    loginLog.setUpdateTime(new Date());
    loginLog.setToken(token);
    loginLog.setOrgId(loginUser.getOrgId());
    loginLog.setUserId(loginUser.getId());
    loginLog.setUserNo(loginUser.getUserNo());
    loginLog.setIpAddr(ip);
    loginLog.setLocLat(loginParam.getLocLat());
    loginLog.setLocLng(loginParam.getLocLng());
    loginLog.setDeviceType(0);
    BeanUtils.copyProperties(loginParam,loginLog);
    loginLogService.insert(loginLog);
    // step 4 : 组装返回结构
    String language = null;
    if (company != null) {
      language = company.getLanguage();
    }
    UserLoginVO userLoginVO = buildUserOutInfo(token, loginUser, language);
    userLoginVO.setStrongPasswordSwitch(strongPasswordSwitch);
    userLoginVO.setDefaultOrgDelt(company.getDefaultDelt());
    List<RoleVO> roleList;
    if (Objects.equals(loginParam.getIsBank(), CompanyEnums.BankAmc.YES.getCode())) {
      roleList = userLoginVO.getRoles().stream().filter(t -> Objects.equals(t.getIsBank(), RoleEnums.Bank.YES.getCode())).collect(Collectors.toList());
    } else {
      roleList = userLoginVO.getRoles().stream().filter(t -> !Objects.equals(t.getIsBank(), RoleEnums.Bank.YES.getCode())).collect(Collectors.toList());
    }
    userLoginVO.setRoles(roleList);
    UserSession userSession = UserUtils.getTokenUser(token);
    userSession.setIsBank(loginParam.getIsBank());
    userSession.setAgentType(userLoginVO.getAgentType());
    UserUtils.updateToken(userSession);
    return ResultMessage.success(userLoginVO);
  }

  public UserVO operationsLogin(String account, String password) {
    User loginUser = this.getOperationUser(account);
    if (loginUser == null) {
      throw new ApiException("查无此人");
    }
    String pass = MD5Util.digest(password + loginUser.getSalt());
    if (!pass.equals(loginUser.getPassword())) {
      throw new ApiException("密码错误");
    }

    String ip = AuthUtils.getIpAddr(CmUtil.getRequest());
    ip = StrUtil.subBefore(ip, ",", false).trim();

    Role role = new Role();
    role.setId(0L);
    role.setType(-1);
    role.setName("运营管理员");
    String token = UserUtils.setToken(loginUser, role, 0);
    LoginLog loginLog = new LoginLog();
    loginLog.setCreateTime(new Date());
    loginLog.setUpdateTime(new Date());
    loginLog.setToken(token);
    loginLog.setOrgId(loginUser.getOrgId());
    loginLog.setUserId(loginUser.getId());
    loginLog.setUserNo(loginUser.getUserNo());
    loginLog.setIpAddr(ip);
    loginLog.setDeviceType(0);
    loginLogService.insert(loginLog);

    UserVO vo=new UserVO();
    BeanUtils.copyProperties(loginUser,vo);
    vo.setToken(token);
    return vo;
  }

  public void logout() {
    UserSession userSession = UserUtils.getTokenUser();
    stringRedisTemplate.delete(KeyCache.AUTH_TOKEN + KeyCache.TOKEN_INFO_SPLITTER + userSession.getToken());
    LoginLog loginLogUpdate = new LoginLog();
    loginLogUpdate.setLogoutTime(new Date());
    Example example = new Example(LoginLog.class);
    example.createCriteria().andEqualTo("token", userSession.getToken());
    loginLogService.updateByExampleSelective(loginLogUpdate, example);

    RoleSessionUtils.removeRole();
  }

  /**
   * 给open用的
   *
   * @param loginUser
   * @param pwd
   * @throws IOException
   */
  private void verifyLoginUser2(User loginUser, String pwd) throws IOException {
    if (loginUser == null) {
      throw new ApiException("用户名或者密码错误！");
    }
    if (loginUser.getStatus() != 0) {
      throw new ApiException("用户状态异常，请联系管理员!");
    }
    // org_id 为0 是超级管理员
    if (null != loginUser.getOrgId() && loginUser.getOrgId() > 0) {
      // 校验总公司是否在合作期限内
      Company company = companyService.selectByPrimaryKey(loginUser.getOrgId());
      if (company.getType() == 1) {
        company = companyService.selectByPrimaryKey(company.getParentId());
      }
      // 校验总公司是否是本地化公司，已经本地化的公司不让登陆云平台
      if (!systemConfig.getLocalDeploy() && company.getLocalDeploy() == 1) {
        throw new ApiException("您已为本地化客户，无法登录云平台！");
      }
      if (company.getStatus() != 0) {
        throw new ApiException("公司状态异常，请联系客服！");
      }
      // 判断合作时间
      if (!systemConfig.getLocalDeploy()) {
        // 非本地化需要判断
        Boolean between =
                DateUtils.between(
                        new Date(), company.getCooperationStartTime(), company.getCooperationEndTime());
        if (between == null || !between) {
          throw new ApiException("公司不在合作中，请联系商务！");
        }
      }
      // 本地化的是永久
    } else {
      loginUser.setRole("1");
    }
    String password = MD5Util.digest(pwd + loginUser.getSalt());
    if (!password.equals(loginUser.getPassword())) {
      throw new ApiException("用户名或者密码错误！");
    }
  }

  public UserLoginVO buildUserOutInfo(String sessionToken, User loginUser,String language) {
    UserLoginVO user = buildUserLoginVO(sessionToken, loginUser, language);
    // 超级管理员
    if (null != loginUser.getOrgId() && loginUser.getOrgId() == 0) {
      return user;
    }
    boolean isAgent = Objects.equals(loginUser.getIsDunner(), SysEnums.BOOL.YES.getCode());
    // 登录度言坐席
    try {
      if (user.getEnableConnectDuyan()) {
        Company company = companyService.selectByPrimaryKey(loginUser.getOrgId());
        if (isAgent && loginUser.getDuyanSeat() == 1) {
          // 前提是连接了度言的服务
          if (null != company.getParentId() && company.getParentId() > 0) {
            Company parentCompany = companyService.selectByPrimaryKey(company.getParentId());
            company.setDuyanReferId(parentCompany.getDuyanReferId());
            user.setDuyanOrgId(parentCompany.getDuyanReferId());
          }
          OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(company.getDuyanReferId());
          // 公司是否开启本人电话自动切换线路开关
          user.setEnableAutoSwitchPhone(ObjectUtil.isNull(orgSwitch)?false:Boolean.valueOf(orgSwitch.getOwnPhoneAutoSwitch()));
          String token =
                  duyanManager.getCtiToken(company.getDuyanReferId(), loginUser.getDuyanAccountId());
          user.setDuyanToken(token);
        }
        if (loginUser.getDuyanAdmin() == 1) {
          String cfgToken =
                  duyanManager.getCfgToken(company.getDuyanReferId(), loginUser.getDuyanAccountId());
          user.setDuyanCfgToken(cfgToken);
        }
      }
    } catch (Exception e) {
      log.error("登录度言系统错误", e);
    }
    return user;
  }

  private UserLoginVO buildUserLoginVO(String sessionToken, User loginUser,String language) {
    UserLoginVO user = new UserLoginVO();
    BeanUtils.copyProperties(loginUser, user);
    user.setLocalDeploy(systemConfig.getLocalDeploy() ? 1 : 0);
    user.setDuyanAdmin((int) loginUser.getDuyanAdmin());
    user.setToken(sessionToken);
    user.setDuyanInspector(Integer.valueOf(loginUser.getDuyanInspector()));
    List<RoleVO> roleVOList = getRoleByUser(user.getId(),language);
    user.setRoles(roleVOList);
    // 超级管理员
    if (null != loginUser.getOrgId() && loginUser.getOrgId() == 0) {
      return user;
    }
    Company company = companyService.selectByPrimaryKey(loginUser.getOrgId());
    if (ObjectUtil.isNull(loginUser.getDepId())){
      user.setAgentType(0);
    } else {
      DepTeam depTeam = depTeamService.selectByPrimaryKey(loginUser.getDepId());
      if (ObjectUtil.isNull(depTeam)){
        user.setAgentType(0);
      } else {
        user.setAgentType(depTeam.getIsBankAgent());
      }
    }
    user.setDepName(depTeamService.getNames().get(loginUser.getDepId()));
    user.setTeamName(depTeamService.getNames().get(loginUser.getTeamId()));
    user.setOrgName(company != null ? company.getName() : "");
    user.setDuyanAccountId(loginUser.getDuyanAccountId());
    user.setDuyanOrgId(company.getDuyanReferId());
    user.setLanguage(company.getLanguage());
    Boolean isConnectDuyan = isConnectDuyan(loginUser.getOrgId());
    user.setEnableConnectDuyan(isConnectDuyan);
    user.setDuyanAgent((int)loginUser.getDuyanSeat());
    if (isConnectDuyan && loginUser.getDuyanSeat()==1) {
      try {
        String token = duyanManager.getCtiToken(company.getDuyanReferId(), loginUser.getDuyanAccountId());
        user.setDuyanToken(token);
      } catch (Exception e) {
        log.error("登录度言系统错误", e);
      }
    }
    return user;
  }

  public UserOpenVO openLogin(LoginParam loginParam) throws Exception {
    // 校验验证码
    loginParam.setPassword(EncryptUtil.base64Decode(loginParam.getPassword()));
    String validateCode = loginParam.getValidateCode();
    boolean result = ValidateCodeUtil.validate(loginParam.getImageUuid(), validateCode);
    if (!result) {
      throw new ApiException("验证码错误！");
    }
    // 校验密码。
    User loginUser = getUserByLoginName(loginParam);
    verifyLoginUser2(loginUser, loginParam.getPassword());
    // 验证是否是管理员
    judgeIsAdmin(loginUser.getId(), loginUser.getOrgId());
    // 组装返回结构
    UserOpenVO userOpenVO = BeanUtil.copyProperties(loginUser, UserOpenVO.class);
    // 超级管理员
    if (null != loginUser.getOrgId() && loginUser.getOrgId() == 0) {
      return userOpenVO;
    }
    // 公司信息
    Company company = companyService.selectByPrimaryKey(loginUser.getOrgId());
    userOpenVO.setDepName(depTeamService.getNames().get(loginUser.getDepId()));
    userOpenVO.setTeamName(depTeamService.getNames().get(loginUser.getTeamId()));
    userOpenVO.setOrgName(company != null ? company.getName() : "");
    userOpenVO.setOrgApikey(company.getApikey());
    return userOpenVO;
  }

  /** 校验是否在本公司存在，是否在其他公司存在，成功后自动删除其他公司的账号 */
  private void checkNewUser(
          Long userId, String mobile, String userNo, Long orgId, Boolean isUpdate) {
    // 在本公司下进行判重
    User query = new User();
    query.setOrgId(orgId);
    query.setStatus(User.Status.NORMAL.getCode());
    if (!StringUtils.isBlank(mobile)) {
      query.setMobile(mobile);
      List<User> users = super.select(query);
      if (isUpdate) {
        if ((users.size() == 1 && !userId.equals(users.get(0).getId())) || users.size() > 1) {
          // 是修改，并且返回的结果只有一个，并且修改的员工id和查出来的员工id不一致
          throw new ApiException("手机号码【" + mobile + "】已经注册");
        }
      } else {
        // 新增需要判断
        if (users.size() > 0) {
          throw new ApiException("手机号码【" + mobile + "】已经注册");
        }
      }
      // 全局判重
      Example userExp1 = new Example(User.class);
      userExp1
              .createCriteria()
              .andEqualTo("mobile", mobile)
              .andEqualTo("status", User.Status.NORMAL.getCode())
              .andNotEqualTo("orgId", orgId)
              .andEqualTo("interimStatus", User.InterimStatus.NORMAL.getCode());
      users = super.selectByExample(userExp1);
      if (users.size() > 0) {
        throw new ApiException("此员工账户存在于其他催收公司，无法重复添加！");
      }
      // 一路过关斩将后，是否需要删除其他公司的账号
      Example userExp = new Example(User.class);
      userExp
              .createCriteria()
              .andNotEqualTo("orgId", orgId)
              .andEqualTo("mobile", mobile)
              .andEqualTo("interimStatus", User.InterimStatus.PENDING.getCode());
      // 其他公司手机号码相同的员工，并且是待定状态的
      List<User> oldUsers = super.selectByExample(userExp);
      if (!CommonUtils.isEmpty(oldUsers)) {
        for (User oldUser : oldUsers) {
          oldUser.setStatus(User.Status.DELETE.getCode());
          userMapper.updateByPrimaryKeySelective(oldUser);
        }
      }
    }
    // 针对员工编号判重
    query.setMobile(null);
    query.setUserNo(userNo);
    List<User> users = super.select(query);
    if (isUpdate) {
      if ((users.size() == 1 && !userId.equals(users.get(0).getId())) || users.size() > 1) {
        // 是修改，并且返回的结果只有一个，并且修改的员工id和查出来的员工id不一致
        throw new ApiException("员工编号【" + userNo + "】已经注册");
      }
    } else {
      // 新增需要判断
      if (users.size() > 0) {
        throw new ApiException("员工编号【" + userNo + "】已经注册");
      }
    }
  }

  @CacheEvict(value = KeyCache.USER_NAME, key = "#create.orgId",allEntries = true)
  @Transactional(rollbackFor = Exception.class)
  public User addUser(UserParamCreate create, Long createBy) {
    // 获取员工需要设置的角色
    List<Long> roleIdList = CmUtil.convertToList(create.getRoles());
    List<Role> roleList = roleService.selectByIdList(roleIdList, Role.class);
    if (roleList.size() != roleIdList.size()) {
      throw new ApiException("您选择的部分角色不存在");
    }
    //如果有催员、外访员、质检员标识，则需要有归属的小组
    if((Objects.equals(create.getIsDunner(),SysEnums.BOOL.YES.getCode())||
      Objects.equals(create.getIsVisitor(),SysEnums.BOOL.YES.getCode())||
      Objects.equals(create.getIsInspector(),SysEnums.BOOL.YES.getCode()))
      &&Objects.isNull(create.getTeamId())){
      throw new ApiException("催员/质检员/外访员需要有对应的小组");
    }
    // 是否账号上限
    checkUserLimit(create.getOrgId(), 1);
    // 校验手机号、用户编号是否存在
    checkNewUser(create.getId(), create.getMobile(), create.getUserNo(), create.getOrgId(), false);
    // 校验组织架构
    checkOrgRole(create.getOrgId(), create.getDepId(), create.getTeamId(), roleList);
    Boolean isConnectDuyan = isConnectDuyan(create.getOrgId());
    Boolean isHaveDuyanRole = false;
    if (StringUtils.isNotBlank(create.getCheckTeamIds())
            || create.getIsDuyanInspectorSup()
            || create.getIsDuyanAdmin()
            || create.getIsDuyanFifoAgent()
            || create.getIsDuyanAgent()
            || create.getIsDuyanTeamLeader()) {
      isHaveDuyanRole = true;
    }
    if (!isConnectDuyan && isHaveDuyanRole) {
      throw new ApiException("未开通呼叫中心，不能设置呼叫中心相关角色");
    }
    if (StringUtils.isBlank(create.getMobile()) && isHaveDuyanRole) {
      throw new ApiException("设置呼叫中心相关角色，请填写手机号");
    }
    // 校验外访
    checkVisitor(create.getOrgId(), roleList, null);
    // 组建用户数据
    String initPassword =
            getInitPassword(systemConfig.getLocalDeploy(), isConnectDuyan, create.getMobile());
    User user = buildFromCreate(create, initPassword, isConnectDuyan, createBy);
    // 校验度言角色
    checkDuyanRole(user, roleList);
    // 校验质检员参数
    List<Long> checkTeamIdList = CmUtil.convertToList(create.getCheckTeamIds());
    List<Long> duyanInspectTeamIds = getDuyanInspectTeamIds(checkTeamIdList, roleIdList);
    // 插入数据库
    int result = userMapper.insertSelective(user);
    if (result != 1) {
      throw new ApiException("添加用户失败！");
    }
    // 创建角色关联关系
    userRoleService.createUserRoleRels(user.getId(), roleList);
    // 呼叫中心相关角色
    if (isHaveDuyanRole) {
      // 创建质检员
      inspectorsService.createInspectorsRels(user.getId(), checkTeamIdList);
      // 创建度言账号
      Long duyanTeamId = null;
      if (user.getTeamId() != null) {
        DepTeam belongToTeam = depTeamService.selectByPrimaryKey(user.getTeamId());
        duyanTeamId = belongToTeam == null ? null : belongToTeam.getDuyanReferId();
      }
      Long accountId =
              duyanManager.createDuyanUserAndRole(user, duyanTeamId, duyanInspectTeamIds, createBy);
      user.setDuyanAccountId(accountId);
      userMapper.updateByPrimaryKeySelective(user);
    }
    // 发送短信(不回滚)
    if (StringUtils.isNotBlank(user.getMobile())
            && (!systemConfig.getLocalDeploy() || isConnectDuyan)) {
      String content = String.format(SmsContants.SMS_CODE_USER_ADD, initPassword);
      try {
        yunpianManager.sendSms(user.getMobile(), content, false);
      } catch (Exception e) {
        log.error("发送登录密码失败,手机号:{},内容:{}", user.getMobile(), content, e);
      }
    }
    return user;
  }

  private User buildFromCreate(
          UserParamCreate create, String initPassword, Boolean isConnectDuyan, Long createBy) {
    User user = new User();
    BeanUtils.copyProperties(create, user);
    user.setRole(create.getRoles());
    Integer isOldUser = User.OldUser.NO.getCode();
    // 本地化且未开通呼叫中心为老用户，其他为新员工
    if (systemConfig.getLocalDeploy() && !isConnectDuyan) {
      isOldUser = User.OldUser.YES.getCode();
    }
    // 设置密码
    String salt = UserUtils.generateSalt();
    String pwd = UserUtils.generateMd5(salt, initPassword);
    user.setOldUser(isOldUser);
    user.setSalt(salt);
    user.setPassword(pwd);
    user.setChangePasswd(0);
    // 设置角色
    user.setDuyanTeamLeader(create.getIsDuyanTeamLeader() ? (byte) 1 : (byte) 0);
    user.setDuyanSeat(create.getIsDuyanAgent() ? (byte) 1 : (byte) 0);
    user.setDuyanFifoAgent(create.getIsDuyanFifoAgent() ? (byte) 1 : (byte) 0);
    user.setDuyanInspector(StringUtils.isNotBlank(create.getCheckTeamIds()) ? (byte) 1 : (byte) 0);
    user.setDuyanInspectorSup(create.getIsDuyanInspectorSup() ? 1 : 0);
    user.setDuyanAdmin(create.getIsDuyanAdmin() ? (byte) 1 : (byte) 0);
    user.setCreateBy(createBy);
    user.setUpdateBy(createBy);
    user.setCreateTime(new Date());
    user.setUpdateTime(new Date());
    return user;
  }

  public String getInitPassword(Boolean isLocalDeploy, Boolean isConnectDuyan, String mobile) {
    String initPassword = "123456";
    if (StringUtils.isNotBlank(mobile) && (!isLocalDeploy || isConnectDuyan)) {
      initPassword = UserUtils.generateRandomPwd();
    }
    return initPassword;
  }

  private void checkDuyanRole(User user, List<Role> roleList) {
    if (user.getDuyanSeat() == 0
            && user.getDuyanFifoAgent() == 0
            && user.getDuyanTeamLeader() == 0
            && user.getDuyanAdmin() == 0
            && user.getDuyanInspector() == 0
            && user.getDuyanInspectorSup() == 0) {
      return;
    }
    // 安米小组长
    List<Role> teamLeaderRole =
            roleList.stream()
                    .filter(
                            r ->
                                    RoleEnums.type.TEAM_LEADER.getCode() == r.getType()
                                            || (RoleEnums.type.CUSTOM_ADMIN.getCode() == r.getType()
                                            && RoleEnums.DepType.TEAM.getCode() == r.getDepType()))
                    .collect(Collectors.toList());
    // 安米管理员
    List<Integer> adminType =
            Lists.newArrayList(
                    RoleEnums.type.SUB_ACCOUNT_ADMIN.getCode(),
                    RoleEnums.type.BRANCH_ADMIN.getCode(),
                    RoleEnums.type.TEAM_LEADER.getCode());
    List<Integer> adminDepType =
            Lists.newArrayList(
                    RoleEnums.DepType.HEAD_OFFICE.getCode(),
                    RoleEnums.DepType.BRANCH_OFFICE.getCode(),
                    RoleEnums.DepType.TEAM.getCode());
    List<Role> adminRole =
            roleList.stream()
                    .filter(
                            r ->
                                    adminType.contains(r.getType())
                                            || (RoleEnums.type.CUSTOM_ADMIN.getCode() == r.getType()
                                            && adminDepType.contains(r.getDepType())))
                    .collect(Collectors.toList());
    boolean isDunner = Objects.equals(user.getIsDunner(),SysEnums.BOOL.YES.getCode());
    boolean isVisitor = Objects.equals(user.getIsVisitor(),SysEnums.BOOL.YES.getCode());

    if (user.getDuyanSeat() == 1 && !isDunner && !isVisitor) {
      throw new ApiException("设置呼叫中心坐席必须是安米催员或外访员");
    }
    if (user.getDuyanFifoAgent() == 1 && user.getDuyanSeat() == 0) {
      throw new ApiException("设置预测式外呼坐席必须是呼叫中心坐席");
    }
    if (user.getDuyanTeamLeader() == 1
            && (user.getDuyanSeat() == 0 || CollectionUtils.isEmpty(teamLeaderRole))) {
      throw new ApiException("设置呼叫中心小组长必须是呼叫中心坐席且是安米小组长");
    }
    if (user.getDuyanAdmin() == 1 && CollectionUtils.isEmpty(adminRole)) {
      throw new ApiException("设置度言管理员必须是安米管理员");
    }
    if (user.getDuyanInspectorSup() == 1 && user.getDuyanInspector() == 0) {
      throw new ApiException("设置质检主管必须要勾选质检小组");
    }
  }

  public void checkUserLimit(Long orgId, Integer preNumber) {
    Integer userLimit = companyService.getUserLimit(orgId);
    // -1不可添加催员，0催员无个数限制
    if (-1 == userLimit) throw new ApiException("未购买账号！");
    if (0 == userLimit) return;
    Integer userCount = getUserCount(orgId);
    if ((userCount + preNumber) > userLimit) {
      throw new ApiException("账号数量已达上限！");
    }
  }

  public Integer getUserCount(Long orgId) {
    Example example = new Example(User.class);
    example
            .createCriteria()
            .andEqualTo("orgId", orgId)
            .andEqualTo("status", User.Status.NORMAL.getCode());
    Integer userCount = userMapper.selectCountByExample(example);
    return userCount;
  }

  /**
   * 是否连接度言
   *
   * @return
   */
  public Boolean isConnectDuyan(Long orgId) {
    return deployStrategyHolder.select().getConnectDuyan(orgId);
  }

  /**
   * 将user对象转为UserQueryVO
   *
   * @param user
   * @return
   */
  private UserQueryVO convertVO(User user,Map<Long,String> orgMap,Map<Long,String> depTeamMap,String language) {
    UserQueryVO vo = new UserQueryVO();
    BeanUtils.copyProperties(user, vo);
    vo.setOrgName(orgMap.get(user.getOrgId()));
    vo.setTeamName(depTeamMap.get(user.getTeamId()));
    vo.setDepName(depTeamMap.get(user.getDepId()));
    vo.setDuyanInspectorSup(user.getDuyanInspectorSup());
    vo.setDuyanAgent((int) user.getDuyanSeat());
    vo.setDuyanFifoAgent((int) user.getDuyanFifoAgent());
    vo.setDuyanTeamLeader((int) user.getDuyanTeamLeader());
    vo.setDuyanInspector(user.getDuyanInspector().intValue());
    vo.setDuyanAdmin(user.getDuyanAdmin().intValue());
    List<RoleVO> roles = getRoleByUser(user.getId(),language);
    boolean isAgent = Objects.equals(user.getIsDunner(),SysEnums.BOOL.YES.getCode());
    boolean isInspector = Objects.equals(user.getIsInspector(),SysEnums.BOOL.YES.getCode());
    if (isInspector) {
      List<Long> checkTeamIds = getCheckTeamIds(user.getId());
      if (!CommonUtils.isEmpty(checkTeamIds)) {
        vo.setCheckTeamIds(checkTeamIds);
      }
    }
    vo.setAgent(isAgent);
    vo.setRoles(roles);
    return vo;
  }

  private List<Long> getCheckTeamIds(Long userId) {
    if (userId == null) {
      return new ArrayList<>();
    }
    Example checkMembersExp = new Example(Inspectors.class);
    checkMembersExp
            .createCriteria()
            .andEqualTo("inspectorId", userId)
            .andEqualTo("type", RoleEnums.DepType.TEAM.getCode());
    List<Inspectors> members = inspectorsMapper.selectByExample(checkMembersExp);
    List<Long> checkTeamIds =
            members.stream().map(Inspectors::getTargetId).collect(Collectors.toList());
    return checkTeamIds;
  }

  public List<RoleVO> getRoleByUser(Long userId,String language) {
    List<RoleVO> list = new ArrayList<RoleVO>();
    UserRole query = new UserRole();
    query.setUserId(userId);
    List<Long> roleIds =
            userRoleMapper.select(query).stream().map(UserRole::getRoleId).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(roleIds)) {
      List<Role> roles = roleService.selectByIdList(roleIds);
      i18nService.convertRole(roles, language);
      for (Role role : roles) {
        RoleVO vo = new RoleVO();
        vo.setType(role.getType());
        vo.setDepType(role.getDepType());
        vo.setDesc(role.getDesc());
        vo.setName(role.getName());
        vo.setId(role.getId());
        vo.setIsBank(role.getIsBank());
        list.add(vo);
      }
    }
    return list;
  }

  /**
   * @param login
   * @return
   */
  public User getUserByLoginName(LoginParam login) {
    User entity = new User();
    if (login.getLoginType() == 0) {
      if (StringUtils.isBlank(login.getLoginName())) {
        throw new ApiException("手机号不能为空");
      }
      entity.setMobile(login.getLoginName());
      entity.setStatus(User.Status.NORMAL.getCode());
    } else if (login.getLoginType() == 1) {
      if (StringUtils.isBlank(login.getOrgNo()) || StringUtils.isBlank(login.getUserNo())) {
        throw new ApiException("公司编号和用户编号不能为空");
      }
      Company org = new Company();
      org.setBusinessNo(login.getOrgNo());
      org.setType(CompanyEnums.Type.HEAD_OFFICE.getCode());
      org.setStatus(CompanyEnums.Status.NORMAL.getCode());
      List<Company> companies = companyService.select(org);
      if (companies.size() != 1) {
        throw new ApiException("总公司编号异常！");
      }
      Long orgId = companies.get(0).getId();
      List<Long> orgIds = UserUtils.getOrgIds(orgId);
      Example example = new Example(User.class);
      example
              .createCriteria()
              .andIn("orgId", orgIds)
              .andEqualTo("userNo", login.getUserNo())
              .andEqualTo("status", User.Status.NORMAL.getCode());
      List<User> users = super.selectByExample(example);
      if (users.size() != 1) {
        throw new ApiException("用户编号异常！");
      }
      return users.get(0);
    }
    return super.selectFirst(entity);
  }

  public User getOperationUser(String mobile) {
    List<User> list = this.userMapper.getOperationUser(mobile);
    if (CollectionUtils.isEmpty(list)) {
      return null;
    }
    return list.get(0);
  }

  @Cacheable(value = KeyCache.USER_NAME, key = "#orgId")
  public Map<Long, String> getNames(Long orgId) {
    Example example = new Example(User.class);
    example.and().andEqualTo("orgId", orgId);
    example.selectProperties("id", "name");
    List<User> list = userMapper.selectByExample(example);
    return list.stream().collect(Collectors.toMap(User::getId, User::getName));
  }

  @CacheEvict(value = KeyCache.USER_NAME, key ="#localUser.orgId", allEntries = true)
  @Transactional(rollbackFor = Exception.class)
  public User updateUser(UserParamUpdate update, User localUser, Long updateBy) {
    // 获取员工需要设置的角色
    List<Long> roleIdList = CmUtil.convertToList(update.getRoles());
    List<Role> roleList = roleService.selectByIdList(roleIdList, Role.class);
    if (roleList.size() != roleIdList.size()) {
      throw new ApiException("您选择的部分角色不存在");
    }
    if((Objects.equals(update.getIsDunner(),SysEnums.BOOL.YES.getCode())||
      Objects.equals(update.getIsVisitor(),SysEnums.BOOL.YES.getCode())||
      Objects.equals(update.getIsInspector(),SysEnums.BOOL.YES.getCode()))
      &&Objects.isNull(update.getTeamId())){
      throw new ApiException("催员/质检员/外访员需要有对应的小组");
    }
    // 校验是否存在
    checkNewUser(
            update.getId(), update.getMobile(), update.getUserNo(), localUser.getOrgId(), true);
    // 校验组织架构
    checkOrgRole(localUser.getOrgId(), update.getDepId(), update.getTeamId(), roleList);
    // 校验是否有案子
    checkHaveCase(update, localUser);
    // 校验外访员身份
    checkVisitor(localUser.getOrgId(), roleList, update.getId());
    Boolean isConnectDuyan = isConnectDuyan(localUser.getOrgId());
    Boolean isHaveDuyanRole = false;
    if (StringUtils.isNotBlank(update.getCheckTeamIds())
            || update.getDuyanInspectorSup() == 1
            || update.getIsDuyanAdmin()
            || update.getIsDuyanFifoAgent()
            || update.getIsDuyanAgent()
            || update.getIsDuyanTeamLeader()) {
      isHaveDuyanRole = true;
    }
    if (!isConnectDuyan && isHaveDuyanRole) {
      throw new ApiException("未开通呼叫中心，不能设置呼叫中心相关角色");
    }
    if (StringUtils.isBlank(update.getMobile()) && isHaveDuyanRole) {
      throw new ApiException("设置呼叫中心相关角色，请填写手机号");
    }
    // 组装用户信息
    String initPassword = null;
    if (StringUtils.isNotBlank(update.getMobile())
            && !update.getMobile().equals(localUser.getMobile())
            && (!systemConfig.getLocalDeploy() || isConnectDuyan)) {
      // 非本地化或已开通呼叫中心的本地化，修改手机号，需要重置密码
      initPassword = UserUtils.generateRandomPwd();
    }
    User updateUser = buildUpdateUser(update, initPassword, updateBy);
    // 校验度言角色
    checkDuyanRole(updateUser, roleList);
    // 校验质检员参数
    List<Long> checkTeamIdList = CmUtil.convertToList(update.getCheckTeamIds());
    List<Long> duyanInspectTeamIds = getDuyanInspectTeamIds(checkTeamIdList, roleIdList);
    // 更新用户角色关系
    userRoleService.updateUserRoleRels(updateUser.getId(), roleList);
    // 更新质检员
    inspectorsService.updateInspectorsRels(updateUser.getId(), checkTeamIdList);
    // 同步度言
    Company company = companyService.selectByPrimaryKey(localUser.getOrgId());
    if (isHaveDuyanRole) {
      Long duyanTeamId = null;
      if (updateUser.getTeamId() != null) {
        DepTeam belongToTeam = depTeamService.selectByPrimaryKey(updateUser.getTeamId());
        duyanTeamId = belongToTeam == null ? null : belongToTeam.getDuyanReferId();
      }
      if (localUser.getDuyanAccountId() == null) {
        // 创建度言账号
        Long accountId =
                duyanManager.createDuyanUserAndRole(
                        updateUser, duyanTeamId, duyanInspectTeamIds, updateBy);
        updateUser.setDuyanAccountId(accountId);
      } else {
        // 修改度言账号
        duyanManager.updateDuyanUserAndRole(updateUser, duyanTeamId, duyanInspectTeamIds, updateBy);
      }
    } else {
      if (localUser.getDuyanAccountId() != null) {
        // 删除度言账号
        duyanManager.removeUser(localUser.getDuyanAccountId(), company.getDuyanReferId());
        updateUser.setDuyanAccountId(null);
      }
    }
    // 更新用户信息
    userMapper.updateByPrimaryKey(updateUser);
    // 发送短信(不回滚)
    if (initPassword != null) {
      String content = String.format(SmsContants.SMS_CODE_RESET_PASSWORD, initPassword);
      try {
        yunpianManager.sendSms(updateUser.getMobile(), content, false);
      } catch (Exception e) {
        log.error("发送登录密码失败,手机号:{},内容:{}", updateUser.getMobile(), content, e);
      }
    }
    return updateUser;
  }

  private User buildUpdateUser(UserParamUpdate update, String initPassword, Long updateBy) {
    User updateUser = userMapper.selectForUpdate(update.getId());
    updateUser.setDepId(update.getDepId());
    updateUser.setTeamId(update.getTeamId());
    updateUser.setMobile(update.getMobile() == null ? "" : update.getMobile());
    updateUser.setName(update.getName());
    updateUser.setRole(update.getRoles());
    updateUser.setUserNo(update.getUserNo());
    updateUser.setDuyanTeamLeader(update.getIsDuyanTeamLeader() ? (byte) 1 : (byte) 0);
    updateUser.setDuyanSeat(update.getIsDuyanAgent() ? (byte) 1 : (byte) 0);
    updateUser.setDuyanFifoAgent(update.getIsDuyanFifoAgent() ? (byte) 1 : (byte) 0);
    updateUser.setDuyanInspector(
            StringUtils.isNotBlank(update.getCheckTeamIds()) ? (byte) 1 : (byte) 0);
    updateUser.setDuyanInspectorSup(update.getDuyanInspectorSup() == 1 ? 1 : 0);
    updateUser.setDuyanAdmin(update.getIsDuyanAdmin() ? (byte) 1 : (byte) 0);
    // 重置密码
    if (initPassword != null) {
      String salt = UserUtils.generateSalt();
      String pwd = UserUtils.generateMd5(salt, initPassword);
      updateUser.setSalt(salt);
      updateUser.setPassword(pwd);
      updateUser.setChangePasswd(0);
      updateUser.setOldUser(User.OldUser.NO.getCode());
    }
    updateUser.setUpdateTime(new Date());
    updateUser.setUpdateBy(updateBy);
    updateUser.setIsDunner(update.getIsDunner());
    updateUser.setIsVisitor(update.getIsVisitor());
    updateUser.setIsInspector(update.getIsInspector());
    updateUser.setIsDisposal(update.getIsDisposal());
    updateUser.setIsLitigation(update.getIsLitigation());
    return updateUser;
  }

  public List<Long> getDuyanInspectTeamIds(List<Long> checkTeamIdList, List<Long> roleIdList) {
    if ((CollectionUtils.isEmpty(checkTeamIdList) && roleIdList.contains(3l))
            || (!CollectionUtils.isEmpty(checkTeamIdList) && !roleIdList.contains(3l))) {
      throw new ApiException("设置质检员参数错误");
    }
    if (CollectionUtils.isEmpty(checkTeamIdList)) {
      return null;
    }
    List<DepTeam> depTeamList = depTeamService.selectByIdList(checkTeamIdList, DepTeam.class);
    List<Long> duyanInspectTeamIds =
            depTeamList.stream().map(d -> d.getDuyanReferId()).collect(Collectors.toList());
    if (CollectionUtils.isEmpty(duyanInspectTeamIds)
            || duyanInspectTeamIds.size() != checkTeamIdList.size()) {
      throw new ApiException("质检小组不存在");
    }
    return duyanInspectTeamIds;
  }

  /**
   * 同时去更新user的salt和pwd
   *
   * @param text 短信模版
   * @param target 要更新密码的targetUser
   * @param sendMobile 需要发送的手机号码
   * @return
   */
  public User resetUserPwdBySendSms(String text, User target, String sendMobile) {
    String salt = UserUtils.generateSalt();
    String pwd = duyanManager.generatePwdAndSendSms(text, sendMobile, salt);
    target.setSalt(salt);
    target.setPassword(pwd);
    return target;
  }

  /**
   * 根据参数查询员工列表
   *
   * @param param
   * @param pageParam
   * @return
   */
  public PageOutput<UserQueryVO> getByPage(UserQueryParam param, PageParam pageParam) {
    UserSession userSession = getTokenUser();
    // 查询条件
    Map<String, Object> queryMap = JsonUtils.fromJson(JsonUtils.toJson(param), Map.class);
    queryMap.put("orgId", userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      queryMap.put("depIds", userSession.getDepId().toString());
    }
    if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      queryMap.put("teamIds", userSession.getTeamId().toString());
    }
    Role role = RoleSessionUtils.getRole();
    // 通过角色id判断当前角色是什么类型
    Integer loginRoleType = role.getType(); // 登录者的角色类别
    if (loginRoleType == RoleEnums.type.CUSTOM_ADMIN.getCode()) {
      // 自定义角色
      loginRoleType = role.getDepType(); // 让loginRoleType变为depType
    }
    Page page = super.setPage(pageParam);
    List<User> userList = userMapper.selectUserList(queryMap);
    List<UserQueryVO> userQueryVOList = convertUsers(userList, loginRoleType,userSession.getLanguage());
    PageOutput pageOutput =
            new PageOutput(
                    page.getPageNum(),
                    page.getPageSize(),
                    page != null ? (int) page.getTotal() : userQueryVOList.size(),
                    userQueryVOList);
    pageOutput.setList(userQueryVOList);
    return pageOutput;
  }


  /**
   * 根据参数查询员工列表
   *
   * @param param
   * @param pageParam
   * @return
   */
  public PageOutput<UserQueryVO> getAgentByPage(UserQueryParam param, PageParam pageParam) {
    UserSession userSession = getTokenUser();
    // 查询条件
    Map<String, Object> queryMap = JsonUtils.fromJson(JsonUtils.toJson(param), Map.class);
    queryMap.put("orgId", userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      queryMap.put("depIds", userSession.getDepId().toString());
    }
    if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      queryMap.put("teamIds", userSession.getTeamId().toString());
    }
    Role role = RoleSessionUtils.getRole();
    // 通过角色id判断当前角色是什么类型
    Integer loginRoleType = role.getType(); // 登录者的角色类别
    if (loginRoleType == RoleEnums.type.CUSTOM_ADMIN.getCode()) {
      // 自定义角色
      loginRoleType = role.getDepType(); // 让loginRoleType变为depType
    }
    Page page = super.setPage(pageParam);
    List<User> userList = userMapper.selectUserList(queryMap);
    List<UserQueryVO> userQueryVOList = convertUsers(userList, loginRoleType,userSession.getLanguage());
    PageOutput pageOutput =
      new PageOutput(
        page.getPageNum(),
        page.getPageSize(),
        page != null ? (int) page.getTotal() : userQueryVOList.size(),
        userQueryVOList);
    pageOutput.setList(userQueryVOList);
    return pageOutput;
  }

  /**
   * 查询用户，不做分页
   * @param param
   * @return
   */
  public List<UserQueryVO> getList(UserQueryParam param) {
    UserSession userSession = getTokenUser();
    // 查询条件
    Map<String, Object> queryMap = JsonUtils.fromJson(JsonUtils.toJson(param), Map.class);
    queryMap.put("orgId", userSession.getOrgId());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      queryMap.put("depIds", userSession.getDepId().toString());
    }
    if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      queryMap.put("teamIds", userSession.getTeamId().toString());
    }
    Role role = RoleSessionUtils.getRole();
    // 通过角色id判断当前角色是什么类型
    Integer loginRoleType = role.getType(); // 登录者的角色类别
    if (loginRoleType == RoleEnums.type.CUSTOM_ADMIN.getCode()) {
      // 自定义角色
      loginRoleType = role.getDepType(); // 让loginRoleType变为depType
    }
    List<User> userList = userMapper.selectUserList(queryMap);
    List<UserQueryVO> userQueryVOList = convertUsers(userList, loginRoleType,userSession.getLanguage());
    return userQueryVOList;
  }

  /**
   * 判断当前员工是否越级
   *
   * @param user
   * @param loginRoleType
   */
  private boolean checkLevel(User user, Integer loginRoleType) {
    if (loginRoleType == RoleEnums.DepType.HEAD_OFFICE.getCode()) {
      return false;
    }
    // 判断当前员工是否越级
    List<Long> roleIdsLong = convertRoleIdsToLong(user.getId());
    // 查询所有的roles所属的类型
    Example roleQuery = new Example(Role.class);
    Example.Criteria roleCrt = roleQuery.createCriteria();
    roleCrt.andIn("id", roleIdsLong);
    roleCrt.andEqualTo("status", RoleEnums.Status.NORMAL.getCode());
    List<Role> roles = roleService.selectByExample(roleQuery);
    for (Role role : roles) {
      if (role.getDepType() >= loginRoleType) {
        return true; // 当前员工比登录者角色权限高或者同级
      }
    }
    return false;
  }

  private List<UserQueryVO> convertUsers(List<User> users, Integer loginRoleType,String language) {
    List<UserQueryVO> queryVOS = new ArrayList<>(users.size());
    Map<Long,String> orgMap=this.companyService.getNames();
    Map<Long,String> depTeamMap=this.depTeamService.getNames();
    if (!CommonUtil.isEmpty(users)) {
      users.stream()
              .forEach(
                      l -> {
                        UserQueryVO userQueryVO = convertVO(l,orgMap,depTeamMap,language);
                        if (loginRoleType != null
                                && loginRoleType != RoleEnums.DepType.HEAD_OFFICE.getCode()) {
                          // 当前层级的角色
                          if (checkLevel(l, loginRoleType)) {
                            userQueryVO.setIsLeapfrog(1);
                          }
                        }
                        queryVOS.add(userQueryVO);
                      });
    }
    return queryVOS;
  }

  /**
   * 将user中的role转为long类型的数组
   *
   * @return
   */
  private List<Long> convertRoleIdsToLong(Long userId) {
    if (userId == null) {
      return null;
    }
    UserRole query = new UserRole();
    query.setUserId(userId);
    return userRoleService.select(query).stream()
            .map(UserRole::getRoleId)
            .collect(Collectors.toList());
  }

  /** 判断当前user所选的角色是否合理 没选择分公司不能选择分公司管理员，没选择小组不能选择坐席 */
  public void checkOrgRole(Long orgId, Long depId, Long teamId, List<Role> roleList) {
    if (depId != null) {
      DepTeam dep = depTeamService.selectByPrimaryKey(depId);
      if (dep == null || !dep.getOrgId().equals(orgId)) {
        throw new ApiException("当前选择分公司不属于当前公司！");
      }
    }
    if (teamId != null) {
      DepTeam team = depTeamService.selectByPrimaryKey(teamId);
      if (team == null || !team.getOrgId().equals(orgId)) {
        throw new ApiException("当前选择团队不属于当前公司！");
      }
      if ((team.getUnderTeam() == DepTeamEnums.UnderTeam.YES.getCode() && depId != null)
              || (team.getUnderTeam() == DepTeamEnums.UnderTeam.NOT.getCode()
              && !team.getParentId().equals(depId))) {
        throw new ApiException("当前选择团队不属于当前分公司！");
      }
    }
    for (Role role : roleList) {
      // 分公司管理员
      if ((role.getType() == RoleEnums.type.BRANCH_ADMIN.getCode()
              || role.getDepType() == RoleEnums.DepType.BRANCH_OFFICE.getCode())
              && depId == null) {
        throw new ApiException(role.getName() + "为分公司层级角色，需要选择该员工所属分公司！");
      }
      if ((role.getType() == RoleEnums.type.TEAM_LEADER.getCode()
        || role.getDepType() == RoleEnums.DepType.TEAM.getCode())
        && teamId == null) {
        throw new ApiException(role.getName() + "为小组层级角色，需要选择该员工所属！");
      }
    }
  }

  private void checkVisitor(Long orgId, List<Role> roleList, Long userId) {
    //todo 外访员角色校验，后续需要重新
  }

  private void checkHaveCase(UserParamUpdate update, User oldUser) {
    // 更换分公司或小组
    boolean oldIsDunner = Objects.equals(oldUser.getIsDunner(), SysEnums.BOOL.YES.getCode());
    boolean newIsDunner = Objects.equals(update.getIsDunner(), SysEnums.BOOL.YES.getCode());
    if (!Objects.equals(update.getDepId(), oldUser.getDepId())
      || !Objects.equals(update.getTeamId(), oldUser.getTeamId())
      || (oldIsDunner && !newIsDunner)) {
      if (caseService.haveCases(oldUser)) {
        throw new ApiException("该员工有在案案件，不能随意切换分公司或者小组！也不能移除催员标识");
      }
      if (caseCooperationService.haveCaseCooperation(oldUser)) {
        throw new ApiException("该员工有在案案件，不能随意切换分公司或者小组！也不能移除催员标识");
      }
    }
  }

  public PageOutput<UserQueryVO> users(Integer status) {
    Long orgId = getOrgId();
    User userQuery = new User();
    UserSession session = getTokenUser();
    if (UserUtils.likeBranchAdmin()) {
      userQuery.setDepId(session.getDepId());
    } else if (UserUtils.likeTeamLeader()) {
      userQuery.setTeamId(session.getTeamId());
    }
    userQuery.setOrgId(orgId);
    userQuery.setStatus(status);
    Page page = super.setPage(null);
    List<User> userList = super.select(userQuery);
    List<UserQueryVO> voList = convertUsers(userList, null,session.getLanguage());
    List<Long> depIds = voList.stream().filter(userQueryVO -> Objects.nonNull(userQueryVO.getDepId()))
            .map(UserBaseVO::getDepId).distinct().collect(Collectors.toList());
    List<Long> bankAgentDepTeamIds = depTeamService.getBankAgentDepTeamIds(depIds);
    voList.forEach(userQueryVO -> {
      if (bankAgentDepTeamIds.contains(userQueryVO.getDepId())) {
        userQueryVO.setIsBankAgent(DepTeamEnums.IsBankAgent.YES.getCode());
      } else {
        userQueryVO.setIsBankAgent(DepTeamEnums.IsBankAgent.NO.getCode());
      }
    });
    // 不需要分页，但是需要有分页数据，数据全部返回
    PageOutput<UserQueryVO> pageOutput =
            new PageOutput(
                    page.getPageNum(),
                    page.getPageSize(),
                    page != null ? (int) page.getTotal() : voList.size(),
                    voList);
    return pageOutput;
  }

  public void callRegister(String mobile) {
    User user = selectByPrimaryKey(getTokenUser().getId());
    Company company = companyService.selectByPrimaryKey(getOrgId());
    Long referId = company.getDuyanReferId();
    Long accountId = user.getDuyanAccountId();
    duyanManager.callRegister(referId, accountId, mobile);
  }

  private void judgeIsAdmin(Long userId, Long orgId) {
    // org_id 为0 是超级管理员
    if (null != orgId && orgId == 0l) {
      return;
    }
    UserRole query = new UserRole();
    query.setUserId(userId);
    List<UserRole> userRoleList = userRoleMapper.select(query);
    if (CollectionUtils.isEmpty(userRoleList)) {
      throw new ApiException("您没有权限登陆！");
    }
    List<Long> roleIds =
            userRoleMapper.select(query).stream().map(UserRole::getRoleId).collect(Collectors.toList());
    Example example = new Example(Role.class);
    example.createCriteria().andIn("id", roleIds);
    List<Role> roles = roleService.selectByExample(example);
    List<Role> adminRoles =
            roles.stream()
                    .filter(
                            r ->
                                    r.getType() == RoleEnums.type.SUB_ACCOUNT_ADMIN.getCode()
                                            || r.getType() == RoleEnums.type.BRANCH_ADMIN.getCode()
                                            || r.getType() == RoleEnums.type.SUB_ACCOUNT_ADMIN.getCode()
                                            || (r.getDepType() != null
                                            && (r.getDepType() == RoleEnums.DepType.HEAD_OFFICE.getCode()
                                            || r.getDepType() == RoleEnums.DepType.BRANCH_OFFICE.getCode())))
                    .collect(Collectors.toList());
    if (CollectionUtils.isEmpty(adminRoles)) {
      throw new ApiException("您没有管理员或分公司管理员权限！");
    }
  }

  public Map<String, Object> fifoAgents(Long teamId) {
    if (teamId == null) {
      teamId = getTokenUser().getTeamId();
    }
    Map<String, Object> map = new HashMap<>();
    List<String> fifoAgents = selectFifoAgents(teamId);
    map.put("teamName", depTeamService.getNames().get(teamId));
    map.put("fifoAgents", fifoAgents);
    return map;
  }

  public List<String> selectFifoAgents(Long teamId) {
    Example example = new Example(User.class);
    example
            .createCriteria()
            .andEqualTo("status", User.Status.NORMAL.getCode())
            .andEqualTo("teamId", teamId)
            .andEqualTo("duyanFifoAgent", User.DuyanFifoAgent.YES.getCode());
    List<User> list = selectByExample(example);
    if (CollectionUtils.isEmpty(list)) {
      return new ArrayList<>();
    }
    return list.stream().map(User::getName).collect(Collectors.toList());
  }

  public List<UserQueryVO> excludeList(UserParam param) {
    UserSession login = UserUtils.getTokenUser();
    Long orgId = login.getOrgId();
    param.setOrgId(orgId);
    boolean isTeamLeader = UserUtils.likeTeamLeader(login.getRoleId());
    boolean isBranchAdmin = UserUtils.likeBranchAdmin(login.getRoleId());
    if (isBranchAdmin) {
      param.setDepId(login.getDepId());
    } else if (isTeamLeader) {
      param.setTeamId(login.getTeamId());
    }
    List<UserQueryVO> users = userMapper.excludeList(param);
    if (CommonUtils.isEmpty(users)) {
      // 为空，直接返回
      return users;
    }
    List<Long> userIds = users.stream().map(UserQueryVO::getId).collect(Collectors.toList());
    Example urExp = new Example(UserRole.class);
    urExp.createCriteria().andIn("userId", userIds);
    List<UserRole> userRoles = userRoleMapper.selectByExample(urExp);
    if (CommonUtils.isEmpty(userRoles)) {
      return users;
    }
    Map<Long, List<Long>> userRoleMap =
            userRoles.stream()
                    .parallel()
                    .collect(
                            Collectors.toMap(
                                    UserRole::getUserId,
                                    ur -> {
                                      List<Long> roleIds = new ArrayList<>();
                                      roleIds.add(ur.getRoleId());
                                      return roleIds;
                                    },
                                    (List<Long> value1, List<Long> value2) -> {
                                      value1.addAll(value2);
                                      return value1;
                                    }));

    // 需要被过滤出来的员工
    List<UserQueryVO> exclude = null;
    Long userId = login.getId();
    if (param.getDepType() == RoleEnums.DepType.BRANCH_OFFICE.getCode()) {
      exclude =
              users.stream()
                      .filter(
                              vo -> !param.getTargetId().equals(vo.getDepId()) && !vo.getId().equals(userId))
                      .collect(Collectors.toList());
    } else if (param.getDepType() == RoleEnums.DepType.TEAM.getCode()) {
      exclude =
              users.stream()
                      .filter(
                              vo -> !param.getTargetId().equals(vo.getTeamId()) && !vo.getId().equals(userId))
                      .collect(Collectors.toList());
    }
    Map map = new HashMap();
    for (UserQueryVO vo : exclude) {
      map.put("roleIds", userRoleMap.get(vo.getId()));
      List<Role> roles = roleMapper.selectByIds(map);
      i18nService.convertRole(roles,login.getLanguage());
      vo.setRoles(convertToRoleVOs(roles));
    }
    return exclude;
  }

  private List<RoleVO> convertToRoleVOs(List<Role> roles) {
    List<RoleVO> vos = new ArrayList<>();
    for (Role role : roles) {
      RoleVO vo = new RoleVO();
      vo.setName(role.getName());
      vo.setDepType(role.getDepType());
      vo.setType(role.getType());
      vos.add(vo);
    }
    return vos;
  }

  @Transactional(rollbackFor = Exception.class)
  public Boolean syncToDuyan(Long userId, Integer syncType, Long duyanOrgId) {
    User user = userMapper.selectForUpdate(userId);
    UserSession userSession = getTokenUser();
    if (user == null
            || User.Status.DELETE.getCode() == user.getStatus()
            || !user.getOrgId().equals(userSession.getOrgId())) {
      throw new ApiException("用户不存在");
    }
    if (StringUtils.isBlank(user.getMobile())) {
      return false;
    }
    switch (syncType) {
      case 0:
        // 坐席
        userHandleService.addDuyanAgent(user, duyanOrgId, userSession.getId());
        break;
      case 1:
        // 管理员设置
        userHandleService.addDuyanAdmin(user, duyanOrgId, userSession.getId());
        break;
      case 2:
        // 呼叫中心小组长设置
        userHandleService.addDuyanTeamLeader(user, duyanOrgId, userSession.getId());
        break;
      case 3:
        // 预测式外呼设置
        userHandleService.addDuyanFifoAgent(user, duyanOrgId, userSession.getId());
        break;
      default:
    }
    return true;
  }

  @Transactional(rollbackFor = Exception.class)
  public void removeDY(Long userId, Integer syncType, Long duyanOrgId) {
    User user = userMapper.selectForUpdate(userId);
    UserSession userSession = getTokenUser();
    if (user == null
            || User.Status.DELETE.getCode() == user.getStatus()
            || !user.getOrgId().equals(userSession.getOrgId())) {
      throw new ApiException("用户不存在");
    }
    switch (syncType) {
      case 0:
        // 坐席
        userHandleService.delDuyanAgent(user, duyanOrgId, userSession.getId());
        break;
      case 1:
        // 管理员
        userHandleService.delDuyanAdmin(user, duyanOrgId, userSession.getId());
        break;
      case 2:
        // 呼叫中心小组长
        userHandleService.delDuyanTeamLeader(user, duyanOrgId, userSession.getId());
        break;
      case 3:
        // 预测式外呼坐席
        userHandleService.delDuyanFifoAgent(user, duyanOrgId, userSession.getId());
        break;
      default:
    }
  }

  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public void giveToBranch(Long userId, Long depId, Role role, Long duyanOrgId, Long orgId) {
    User user = userMapper.selectForUpdate(userId);
    if (user == null
            || User.Status.DELETE.getCode() == user.getStatus()
            || !user.getOrgId().equals(orgId)) {
      throw new ApiException("账号不存在");
    }
    if (caseService.haveCases(user)) {
      throw new ApiException("员工：" + user.getName() + " 有案件，不能跨分公司分配角色！");
    }
    if (caseCooperationService.haveCaseCooperation(user)) {
      throw new ApiException("员工：" + user.getName() + " 有在案案件，不能跨分公司分配角色！");
    }
    checkVisitor(orgId, Lists.newArrayList(role), userId);
    user.setTeamId(null);
    user.setDepId(depId);
    user.setRole(role.getId().toString());
    UserSession userSession = getTokenUser();
    user.setUpdateBy(userSession.getId());
    user.setUpdateTime(new Date());
    // 删除关系
    UserRole del = new UserRole();
    del.setUserId(user.getId());
    userRoleMapper.delete(del);
    // 插入关系
    UserRole userRole = new UserRole();
    userRole.setUserId(user.getId());
    userRole.setRoleId(role.getId());
    userRoleMapper.insert(userRole);
    if (user.getDuyanAccountId() == null) {
      userMapper.updateByPrimaryKey(user);
    } else {
      userHandleService.delDuyanAccount(user, duyanOrgId);
    }
  }

  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public void giveToTeam(
          Long userId, Long depId, Long teamId, Role role, Long duyanOrgId, Long orgId) {
    User user = userMapper.selectForUpdate(userId);
    if (user == null
            || User.Status.DELETE.getCode() == user.getStatus()
            || !user.getOrgId().equals(orgId)) {
      throw new ApiException("账号不存在");
    }
    //        // 小组id都一致，不需要调整
    //        if (user.getTeamId() != null && user.getTeamId().equals(teamId)) {
    //            return;
    //        }
    if (caseService.haveCases(user)) {
      throw new ApiException("员工：" + user.getName() + " 有案件，不能跨小组分配角色！");
    }
    if (caseCooperationService.haveCaseCooperation(user)) {
      throw new ApiException("员工：" + user.getName() + " 有在案案件，不能跨小组分配角色！");
    }
    checkVisitor(orgId, Lists.newArrayList(role), userId);
    user.setDepId(depId);
    user.setTeamId(teamId);
    user.setRole(role.getId().toString());
    UserSession userSession = getTokenUser();
    user.setUpdateBy(userSession.getId());
    user.setUpdateTime(new Date());
    // 删除关系
    UserRole del = new UserRole();
    del.setUserId(user.getId());
    userRoleMapper.delete(del);
    // 插入关系
    UserRole userRole = new UserRole();
    userRole.setUserId(user.getId());
    userRole.setRoleId(role.getId());
    userRoleMapper.insert(userRole);
    if (user.getDuyanAccountId() == null) {
      userMapper.updateByPrimaryKey(user);
    } else {
      userHandleService.delDuyanAccount(user, duyanOrgId);
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void addUserMobile(Long userId, String mobile, Integer type, Long duyanOrgId, Long orgId) {
    User update = userMapper.selectForUpdate(userId);
    if (update == null
            || User.Status.DELETE.getCode() == update.getStatus()
            || !update.getOrgId().equals(orgId)) {
      throw new ApiException("账号不存在");
    }
    // 修改手机号
    update.setMobile(mobile);
    // 修改密码
    String salt = UserUtils.generateSalt();
    String randomPwd = UserUtils.generateRandomPwd();
    String pwd = UserUtils.generateMd5(salt, randomPwd);
    update.setSalt(salt);
    update.setPassword(pwd);
    update.setChangePasswd(0);
    UserSession userSession = getTokenUser();
    // 设置坐席或管理员
    if (type == 0) {
      userHandleService.addDuyanAgent(update, duyanOrgId, userSession.getId());
      UserUtils.setTokenIsUpdate(userId, 1);
    } else {
      userHandleService.addDuyanAdmin(update, duyanOrgId, userSession.getId());
    }
    // 发送短信（不回滚）
    String text =
            type == 0 ? SmsContants.SMS_CODE_CALLCENTER_AGENT : SmsContants.SMS_CODE_CALLCENTER_ADMIN;
    String content = String.format(text, randomPwd);
    try {
      yunpianManager.sendSms(mobile, content, false);
    } catch (Exception e) {
      log.error("发送登录密码失败,手机号:{},内容:{}", mobile, content, e);
    }
  }

  @CacheEvict(value = KeyCache.USER_NAME, key = "#orgId", allEntries = true)
  @Transactional(rollbackFor = Exception.class)
  public void delUser(Long userId, Long orgId) throws InterruptedException {
    UserSession userSession = UserUtils.getTokenUser();
    User user = userMapper.selectForUpdate(userId);
    if (null != orgId
            && orgId > 0
            && !user.getOrgId().equals(orgId)) {
      throw new ApiException("该用户不存在");
    }
    // 若存在在催案件(不包括停催)，自动回收到待分案列表下
    List<CaseQueryResult> caseQueryResults = caseService.queryCaseByUserId(String.valueOf(user.getId()));
    if (!CollectionUtils.isEmpty(caseQueryResults)) {
      caseService.allotResetForDelUser(caseQueryResults);
    }
    //  若存在协催案件，自动更新协催状态为“协催完成”，完成备注为：协催员删除，协催自动完成
    List<CaseCooperation> caseCooperationList = caseCooperationService.caseCooperationDetail(user.getId());
    List<Long> cooperationIds = caseCooperationList.stream().map(CaseCooperation::getId).collect(Collectors.toList());
    List<Long> conCaseIds = caseCooperationList.stream().map(CaseCooperation::getCaseId).distinct().collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(cooperationIds)) {
      caseCooperationService.batchFinishCooperationByIds(cooperationIds, userSession, "协催员删除，协催自动完成");
      // 修改协催案件协催状态
      caseService.finishCooperationByCaseId(conCaseIds);
    }
    // 若存在外访记录，自动更新外访状态为“已取消”。无论当下状态是外访中、待外访
    List<VisitInfo> visitInfos = visitService.selectVisitByUserId(user.getId());
    List<Long> visitIds = visitInfos.stream().map(VisitInfo::getId).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(visitIds)) {
      visitService.batchFinishVisitByIds(visitIds);
    }
    // 案件停催、退案、结案、作废、删除状态下，删除催员后，绑定关系删除
    caseMapper.updateBatchByDelUser(userId);

    Long duyanAccountId = user.getDuyanAccountId();
    user.setStatus(User.Status.DELETE.getCode());
    user.setUpdateBy(UserUtils.getTokenUser().getId());
    user.setUpdateTime(new Date());
    // 删除用户角色关系
    userRoleService.updateUserRoleRels(userId, null);
    // 删除度言账号
    if (duyanAccountId == null) {
      userMapper.updateByPrimaryKey(user);
    } else {
      Company company = companyService.selectByPrimaryKey(user.getOrgId());
      userHandleService.delDuyanAccount(user, company.getDuyanReferId());
    }
  }

  /**
   * 外访员登录
   *
   * @param loginParam
   * @return
   * @throws Exception
   */
  public UserLoginVO visitorLogin(LoginParam loginParam) {
    User loginUser = getUserByLoginName(loginParam);
    if (loginUser == null || loginUser.getStatus() != 0) {
      throw new ApiException(AuthConstants.AUTH_ACCOUNT_NOT_FOUND_MESSAGE);
    }
    // org_id 为0 是超级管理员
    if (null == loginUser.getOrgId() || loginUser.getOrgId() == 0) {
      throw new ApiException("超级管理员不能登录小程序");
    }
    // 校验总公司是否在合作期限内
    Company company = companyService.selectByPrimaryKey(loginUser.getOrgId());
    if (company.getType() == 1) {
      company = companyService.selectByPrimaryKey(company.getParentId());
    }
    if (company.getStatus() != 0) {
      throw new ApiException("公司状态异常，请联系客服！");
    }
    // 判断合作时间
    if (!systemConfig.getLocalDeploy()) {
      // 非本地化需要判断
      Boolean between =
              DateUtils.between(
                      new Date(), company.getCooperationStartTime(), company.getCooperationEndTime());
      if (between == null || !between) {
        throw new ApiException("公司不在合作中，请联系商务！");
      }
    }
    if (!companyService.isOpenMiniPrograms(company.getId())) {
      throw new ApiException("未开通小程序，无法登录");
    }
    if(!Objects.equals(loginUser.getIsVisitor(),SysEnums.BOOL.YES.getCode())){
      throw new ApiException("不是外访员，无法登录");
    }
    // 校验是否有外访员身份
    List<Role> roleList = roleMapper.selectRolesByUserId(loginUser.getId());
    if (CollectionUtils.isEmpty(roleList)) {
      throw new ApiException("账号没有任何角色，无法登录");
    }
    loginUser.setRole(roleList.get(0).getId().toString());
    String password = MD5Util.digest(loginParam.getPassword() + loginUser.getSalt());
    if (!password.equals(loginUser.getPassword())) {
      throw new ApiException(AuthConstants.AUTH_ACCOUNT_NOT_FOUND_MESSAGE);
    }
    // 生成token和session
    String token = UserUtils.setToken(loginUser, roleList.get(0), 1);
    LoginLog loginLog=new LoginLog();
    BeanUtils.copyProperties(loginParam, loginLog);
    loginLog.setCreateTime(new Date());
    loginLog.setUpdateTime(new Date());
    loginLog.setToken(token);
    loginLog.setOrgId(loginUser.getOrgId());
    loginLog.setUserId(loginUser.getId());
    loginLog.setUserNo(loginUser.getUserNo());
    String ip=AuthUtils.getIpAddr(CmUtil.getRequest());
    if (ip.contains(",")) {
      ip = ip.trim().split(",")[0].trim();
    }
    loginLog.setIpAddr(ip);
    loginLog.setLocLat(loginParam.getLocLat());
    loginLog.setLocLng(loginParam.getLocLng());
    loginLog.setDeviceType(1);
    loginLogService.insert(loginLog);
    // 组装返回结构
    UserLoginVO userLoginVO = buildUserLoginVO(token, loginUser,company.getLanguage());
    companyService.buildOrgSwitch(userLoginVO, company);
    //脱敏小组
    userLoginVO.setDesensitizationTeamIdList(dictionaryService.getDesensitizationTeamIds(loginUser.getOrgId()));
    return userLoginVO;
  }

  public UserLoginVO appLogin(LoginParam loginParam) {
    User loginUser = getUserByLoginName(loginParam);
    if (loginUser == null || loginUser.getStatus() != 0) {
      throw new ApiException(AuthConstants.AUTH_ACCOUNT_NOT_FOUND_MESSAGE);
    }
    // org_id 为0 是超级管理员
    if (null == loginUser.getOrgId() || loginUser.getOrgId() == 0) {
      throw new ApiException("超级管理员不能登录小程序");
    }
    // 校验总公司是否在合作期限内
    Company company = companyService.selectByPrimaryKey(loginUser.getOrgId());
    if (company.getType() == 1) {
      company = companyService.selectByPrimaryKey(company.getParentId());
    }
    if (company.getStatus() != 0) {
      throw new ApiException("公司状态异常，请联系客服！");
    }
    // 判断合作时间
    if (!systemConfig.getLocalDeploy()) {
      // 非本地化需要判断
      Boolean between =
              DateUtils.between(
                      new Date(), company.getCooperationStartTime(), company.getCooperationEndTime());
      if (between == null || !between) {
        throw new ApiException("公司不在合作中，请联系商务！");
      }
    }
    // 校验是否有外访员或者催员身份
    List<Role> roleList = roleMapper.selectRolesByUserId(loginUser.getId());
    if (CollectionUtils.isEmpty(roleList)) {
      throw new ApiException("账号不存在");
    }
    loginUser.setRole(roleList.get(0).getId().toString());
    String password = MD5Util.digest(loginParam.getPassword() + loginUser.getSalt());
    if (!password.equals(loginUser.getPassword())) {
      throw new ApiException(AuthConstants.AUTH_ACCOUNT_NOT_FOUND_MESSAGE);
    }
    // 生成token和session
    String token = UserUtils.setToken(loginUser, roleList.size() >0 ? roleList.get(0) : null, 1);
    LoginLog loginLog=new LoginLog();
    BeanUtils.copyProperties(loginParam, loginLog);
    loginLog.setCreateTime(new Date());
    loginLog.setUpdateTime(new Date());
    loginLog.setToken(token);
    loginLog.setOrgId(loginUser.getOrgId());
    loginLog.setUserId(loginUser.getId());
    loginLog.setUserNo(loginUser.getUserNo());
    String ip=AuthUtils.getIpAddr(CmUtil.getRequest());
    if (ip.contains(",")) {
      ip = ip.trim().split(",")[0].trim();
    }
    loginLog.setIpAddr(ip);
    loginLog.setLocLat(loginParam.getLocLat());
    loginLog.setLocLng(loginParam.getLocLng());
    loginLog.setDeviceType(1);

    loginLogService.insert(loginLog);
    if (roleList.size() >= 2) {
      // 说明该账号有多个角色：催员和外访员，这时候按要求跳转到选角色界面，所以这里返回角色供app选择即可
      UserLoginVO userLoginVO = new UserLoginVO();
      userLoginVO.setToken(token);
      List<RoleVO> roleVOS = new ArrayList<>();
      roleList.forEach(role -> {
        RoleVO roleVO = AuthBeanUtils.copy(role, RoleVO.class);
        roleVOS.add(roleVO);
      });
      userLoginVO.setRoles(roleVOS);
      return userLoginVO;
    }
    // 组装返回结构
    UserLoginVO userLoginVO = buildUserLoginVO(token, loginUser,company.getLanguage());
    UserSession userSession = UserUtils.getTokenUser(token);
    userSession.setAgentType(userLoginVO.getAgentType());
    UserUtils.updateToken(userSession);
    companyService.buildOrgSwitch(userLoginVO, company);
    //脱敏小组
    userLoginVO.setDesensitizationTeamIdList(dictionaryService.getDesensitizationTeamIds(loginUser.getOrgId()));
    return userLoginVO;
  }

  public ChoiceVO choiceRoleForApp(Long roleId, UserSession session) {
    User user = this.selectByPrimaryKey(session.getId());
    Role role = roleService.selectByPrimaryKey(roleId);
    RoleVO roleVO = AuthBeanUtils.copy(role, RoleVO.class);
    Company company = companyService.selectByPrimaryKey(user.getOrgId());
    // 更新token将role Id 赋予权限
    session.setRoleId(roleVO.getId());
    session.setRoleType(roleVO.getType());
    UserUtils.updateToken(session);
    RoleSessionUtils.setRole(role);
    ChoiceVO choiceVO = new ChoiceVO();
    choiceVO.setRole(roleVO);
    UserLoginVO userLoginVO = buildUserLoginVO(session.getToken(), user, company.getLanguage());
    companyService.buildOrgSwitch(userLoginVO, company);
    //脱敏小组
    userLoginVO.setDesensitizationTeamIdList(dictionaryService.getDesensitizationTeamIds(user.getOrgId()));
    choiceVO.setUser(userLoginVO);
    return choiceVO;
  }

  public Map<Long, String> getUserMap(List<Long> userIdList) {
    if (CollectionUtils.isEmpty(userIdList)) {
      return new HashMap<>();
    }
    Example example = new Example(User.class);
    example.createCriteria().andIn("id", userIdList);
    List<User> userList = super.selectByExample(example);
    if (CollectionUtils.isEmpty(userList)) {
      return new HashMap<>();
    }
    Map<Long, String> map = userList.stream().collect(Collectors.toMap(User::getId, User::getName));
    return map;
  }

  public Map<Long, String> getUserNoMap(List<Long> userIdList) {
    if (CollectionUtils.isEmpty(userIdList)) {
      return new HashMap<>();
    }
    Example example = new Example(User.class);
    example.createCriteria().andIn("id", userIdList);
    List<User> userList = super.selectByExample(example);
    if (CollectionUtils.isEmpty(userList)) {
      return new HashMap<>();
    }
    Map<Long, String> map = userList.stream().collect(Collectors.toMap(User::getId, User::getUserNo));
    return map;
  }

  public List<UserQueryVO> getDunnerIdList() throws Exception {
    UserSession userSession = getTokenUser();
    User userQuery = new User();
    userQuery.setOrgId(userSession.getOrgId());
    userQuery.setStatus(0);
    List<User> userList = super.select(userQuery);
    List<UserQueryVO> voList = convertUsers(userList, null,userSession.getLanguage());
    return voList;
  }

  public List<User> getDunnerList(UserSession user){
    UserParam param=new UserParam();
    param.setOrgId(user.getOrgId());
    if (user.getRoleId() != null && UserUtils.likeBranchAdmin(user.getRoleId())) {
      param.setDepId(user.getDepId());
    }
    return this.userMapper.queryDunnerUsers(param);
  }

  public List<User> getBankDunnerList(UserSession userSession) {
    List<User> dunnerList = getDunnerList(userSession);
    // 如果是甲方系统导入， 只能分配内催机构下的催员
    List<Long> depIds = dunnerList.parallelStream().filter(u -> Objects.nonNull(u.getDepId())).map(User::getDepId)
            .distinct().collect(Collectors.toList());
    List<Long> bankAgentDepTeamIds = depTeamService.getBankAgentDepTeamIds(depIds);
    return dunnerList.stream().filter(user -> !bankAgentDepTeamIds.contains(user.getDepId())).collect(Collectors.toList());
  }

  public PageOutput<SmsUserVO> getSmsUser(SmsUserParam param) {
    PageParam pageParam = new PageParam(param.getPage(), param.getLimit());
    Page page = PageUtils.setPage(pageParam);
    List<SmsUserVO> smsUser = userMapper.getSmsUser(param);
    PageOutput<SmsUserVO> pageOutput =
            new PageOutput(
                    page.getPageNum(),
                    page.getPageSize(),
                    page != null ? (int) page.getTotal() : smsUser.size(),
                    smsUser);
    return pageOutput;
  }

  @Transactional(rollbackFor = Exception.class)
  public void setUserSms(SmsSet set) {
    Long userId = UserUtils.getTokenUser().getId();
    Example example = new Example(User.class);
    example.and().andIn("id", set.getUserIds());
    User user = new User();
    user.setIsCanMsg(set.getIsOpen());
    user.setUpdateTime(new Date());
    user.setUpdateBy(userId);
    int update = userMapper.updateByExampleSelective(user, example);
  }

  public List<DepTeamVO> getInspectTeam(UserSession userSession){
    Long userId = userSession.getId();
    List<DepTeamVO> depVOList = depTeamService.getInspectTeam(userId);
    if (CollectionUtils.isEmpty(depVOList)) {
      return depVOList;
    }
    List<Long> teamIds = depVOList.stream().map(DepTeamVO::getId).collect(Collectors.toList());
    List<User> userList = userMapper.getUserList(teamIds);
    Map<Long, List<UserVO>> listMap = userList.stream().map(user -> AuthBeanUtils.copy(user, UserVO.class)).collect
            (Collectors.groupingBy(UserVO::getTeamId));
    depVOList.forEach(depTeamVO -> {
      depTeamVO.setUserList(listMap.get(depTeamVO.getId()));
    });
    return depVOList;
  }

  public PageOutput<UserVO> subAccount(PageParam pageParam, Long orgId) {
    Page page = super.setPage(pageParam);
    Company company = companyService.selectByPrimaryKey(orgId);
    if(Objects.isNull(company)){
      throw new ApiException("公司不存在");
    }
    List<User> userList = Collections.EMPTY_LIST;
    if(Objects.equals(company.getIsBank(),CompanyEnums.BankAmc.YES.getCode())){
      if(Objects.isNull(company.getDefaultSysRole())){
        throw new ApiException("公司没有默认的系统角色，请联系管理员");
      }
      userList = userMapper.getDefaultRoleUserList(orgId,company.getDefaultSysRole());
    }
    else{
      userList = userMapper.subAccountList(orgId);
    }
    List<UserVO> result = new ArrayList<>();
    for (User u : userList) {
      UserVO vo = new UserVO();
      BeanUtils.copyProperties(u, vo);
      result.add(vo);
    }
    return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int) page.getTotal(), result);
  }

  public PageOutput<MediateUserVO> getMediator(MediateUserParam param) {
    PageParam pageParam = new PageParam(param.getPage(), param.getLimit());
    Page page = PageUtils.setPage(pageParam);
    List<MediateUserVO> mediateUser = userMapper.getMediateUser(param);
    PageOutput<MediateUserVO> pageOutput =
            new PageOutput<>(
                    page.getPageNum(),
                    page.getPageSize(),
                    (int) page.getTotal(),
                    mediateUser
            );
    return pageOutput;
  }

  /**
   * 更新调解文书开关
   *
   * @param userId       用户id
   * @param isCanMediate 是否开启文书 0:关闭 1:开启
   * @param updateBy     更新人
   */
  public void updateMediatorStatus(Long userId, Integer isCanMediate, Long updateBy) {
    User user = userMapper.selectByPrimaryKey(userId);
    if (user == null) {
      throw new ApiException("无法找到对应用户！");
    }
    User newUser = new User();
    newUser.setIsCanMediate(isCanMediate);
    newUser.setId(userId);
    newUser.setUpdateTime(new Date());
    newUser.setUpdateBy(updateBy);
    userMapper.updateByPrimaryKeySelective(newUser);
  }

  /**
   * 添加调解员信息
   * @param mediatorInfo
   */
  @Transactional(rollbackFor = Exception.class)
  public void addMediator(List<String> mediatorInfo) {
    List<User> users = dealMediatorInfo(mediatorInfo);
    for (User user : users) {
      userMapper.updateByPrimaryKeySelective(user);
    }
  }

  private List<User> dealMediatorInfo(List<String> mediatorInfo) {
    List<User> users = new ArrayList<>();
    for (String mediator : mediatorInfo) {
      if (mediator.split(",").length <= 2) {
        User user = new User();
        String[] meArr = mediator.split(",");
        user.setId(Long.valueOf(meArr[0]));
        if (meArr.length == 2) {
          user.setMediateMobile(meArr[1]);
        }
        // 设置为调解员
        user.setMediator(1);
        user.setUpdateTime(new Date());
        users.add(user);
      }
    }
    return users;
  }

  public OrgTreeVO dunnerTree(UserSession userSession) {
    OrgTreeVO orgTreeVO = new OrgTreeVO();
    orgTreeVO.setOrgId(userSession.getOrgId());
    Company company = companyService.selectByPrimaryKey(userSession.getOrgId());
    orgTreeVO.setOrgName(company.getName());
//    if (!UserUtils.likeAdmin()) {
//      throw new ApiException("没有权限");
//    }
    List<User> dunnerList = this.getDunnerList(userSession);
    for (User u : dunnerList) {
      //分公司id如果为空的话 Collectors.groupingBy(User::getDepId) 会报错
      if (Objects.isNull(u.getDepId())) {
        u.setDepId(-1L);
      }
    }
    DepTeam query = new DepTeam();
    query.setOrgId(userSession.getOrgId());
    List<DepTeam> depTeams = depTeamService.select(query);
    Map<Long, DepTeam> depTeamMap = depTeams.stream().collect(Collectors.toMap(DepTeam::getId, Function.identity()));

    Map<Long, List<User>> depUserMaps = dunnerList.stream().collect(Collectors.groupingBy(User::getDepId));
    for (Map.Entry<Long, List<User>> entry : depUserMaps.entrySet()) {
      Long depId = entry.getKey();
      List<User> depUsers = entry.getValue();
      if (depId != -1L) {
        DepTreeVO depTreeVO = new DepTreeVO();
        depTreeVO.setDepId(depId);
        depTreeVO.setDepName(depTeamMap.get(depId).getName());
        depTreeVO.setIsAgent(Objects.equals(depTeamMap.get(depId).getIsAgent(), 1));
        depTreeVO.setIsBankAgent(Objects.equals(depTeamMap.get(depId).getIsBankAgent(), DepTeamEnums.IsBankAgent.YES.getCode()));
        Map<Long, List<User>> teamUserMaps = depUsers.stream().collect(Collectors.groupingBy(User::getTeamId));
        for (Map.Entry<Long, List<User>> teamEntry : teamUserMaps.entrySet()) {
          Long teamId = teamEntry.getKey();
          List<User> teamUsers = teamEntry.getValue();
          TeamTreeVO teamTreeVO = new TeamTreeVO();
          teamTreeVO.setTeamId(teamId);
          teamTreeVO.setTeamName(depTeamMap.get(teamId).getName());
          for (User user : teamUsers) {
            UserTreeVO userTreeVO = new UserTreeVO();
            userTreeVO.setUserId(user.getId());
            userTreeVO.setUserName(user.getName());
            teamTreeVO.getUsers().add(userTreeVO);
          }
          depTreeVO.getTeams().add(teamTreeVO);
        }
        orgTreeVO.getDeps().add(depTreeVO);
      } else {
        //直属小组
        Map<Long, List<User>> teamUserMaps = depUsers.stream().collect(Collectors.groupingBy(User::getTeamId));
        for (Map.Entry<Long, List<User>> teamEntry : teamUserMaps.entrySet()) {
          Long teamId = teamEntry.getKey();
          if (!Objects.equals(depTeamMap.get(teamId).getUnderTeam(), 1)) {
            //用户的分公司id为空，小组又不是直属小组，说明数据有误
            continue;
          }
          List<User> teamUsers = teamEntry.getValue();
          TeamTreeVO teamTreeVO = new TeamTreeVO();
          teamTreeVO.setTeamId(teamId);
          teamTreeVO.setTeamName(depTeamMap.get(teamId).getName());
          for (User user : teamUsers) {
            UserTreeVO userTreeVO = new UserTreeVO();
            userTreeVO.setUserId(user.getId());
            userTreeVO.setUserName(user.getName());
            teamTreeVO.getUsers().add(userTreeVO);
          }
          orgTreeVO.getUnderTeams().add(teamTreeVO);
        }
      }
    }
    if (UserUtils.likeBranchAdmin()) {
      orgTreeVO.setUnderTeams(new ArrayList<>());
      if (!CollectionUtils.isEmpty(orgTreeVO.getDeps())) {
        orgTreeVO.setDeps(orgTreeVO.getDeps().stream().filter(t -> Objects.equals(t.getDepId(), userSession.getDepId())).collect(Collectors.toList()));
      }
    }
    if (UserUtils.likeTeamLeader()) {
      if (!CollectionUtils.isEmpty(orgTreeVO.getUnderTeams())) {
        orgTreeVO.setUnderTeams(orgTreeVO.getUnderTeams().stream().filter(t -> Objects.equals(t.getTeamId(), userSession.getTeamId())).collect(Collectors.toList()));
      }
      if (!CollectionUtils.isEmpty(orgTreeVO.getDeps())) {
        orgTreeVO.setDeps(orgTreeVO.getDeps().stream().filter(t -> Objects.equals(t.getDepId(), userSession.getDepId())).collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(orgTreeVO.getDeps())) {
          orgTreeVO.getDeps().get(0).setTeams(orgTreeVO.getDeps().get(0).getTeams().stream().filter(t -> Objects.equals(t.getTeamId(), userSession.getTeamId())).collect(Collectors.toList()));
        }
      }
    }
    return orgTreeVO;
  }

  public Map<Long, String> queryUserMap(List<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Collections.emptyMap();
    }
    userIds = userIds.stream().distinct().collect(Collectors.toList());
    Example example = new Example(User.class);
    example.and().andIn("id", userIds);
    List<User> users = userMapper.selectByExample(example);
    Map<Long, String> userMap = users.stream().collect(Collectors.toMap(User::getId, User::getName));
    return userMap;
  }

  public String getCtiToken() {
    UserSession userSession = UserUtils.getTokenUser();
    Company company = companyService.selectByPrimaryKey(userSession.getOrgId());
    User user = userMapper.selectByPrimaryKey(userSession.getId());
    String token = duyanManager.getCtiToken(company.getDuyanReferId(), user.getDuyanAccountId());
    return token;
  }

  /**
   * 未关联度言account的账号，清空关于度言信息
   * @param orgId
   */
  public void cleanDuyanInfo(Long orgId) {
    // 用户度言信息
    userMapper.cleanDuyanInfo(orgId);
  }

  /**
   * 根据度言id查询对应用户信息
   *
   * @param accountId 关联度言account_id
   * @return
   */
  public User selectUserByAccountId(Long accountId) {
    Example example = new Example(User.class);
    example.createCriteria().andEqualTo("duyanAccountId", accountId);
    List<User> users = userMapper.selectByExample(example);
    if (!CollectionUtils.isEmpty(users)) {
      return users.get(0);
    }
    return null;
  }

  /**
   * 修改调解员视频调解开关
   *
   * @param userId      催员
   * @param isCanVideo  是否可以视频调解 0:禁用 1:启用
   * @param updateBy    更新人
   */
  public void updateMediatorVideo(Long userId, Integer isCanVideo, Long updateBy) {
    User user = userMapper.selectByPrimaryKey(userId);
    if (user == null) {
      throw new ApiException("无法找到对应用户！");
    }
    User newUser = new User();
    newUser.setIsCanVideo(isCanVideo);
    newUser.setId(userId);
    newUser.setUpdateTime(new Date());
    newUser.setUpdateBy(updateBy);
    userMapper.updateByPrimaryKeySelective(newUser);
  }

  /**
   * 批量更新调解员视频调解开关
   *
   * @param userIds    用户列表
   * @param isCanVideo 是否可以视频调解 0:禁用 1:启用
   * @param updateBy   操作人
   */
  public void updateBatchMediatorVideo(List<Long> userIds, Integer isCanVideo, Long updateBy) {
    Example example = new Example(User.class);
    example.and().andIn("id", userIds);
    User user = new User();
    user.setUpdateBy(updateBy);
    user.setUpdateTime(new Date());
    user.setIsCanVideo(isCanVideo);
    userMapper.updateByExampleSelective(user, example);
  }

  /**
   * 批量更新调解员
   * @param userIds       用户ids
   * @param isCanMediate  是否可以调解 0：禁用 1:启用
   * @param updateBy      更新人
   */
  public void updateBatchMediatorStatus(List<Long> userIds, Integer isCanMediate, Long updateBy) {
    Example example = new Example(User.class);
    example.and().andIn("id", userIds);
    User user = new User();
    user.setUpdateBy(updateBy);
    user.setUpdateTime(new Date());
    user.setIsCanMediate(isCanMediate);
    userMapper.updateByExampleSelective(user, example);
  }

  /**
   * 获取用户
   *
   * @param roleIds 角色ID
   * @param orgId   组织id
   * @param depId   部门id
   * @param teamId  团队id
   * @return {@link List}<{@link User}>
   */
  public List<User> getUsers(List<Long> roleIds, Long orgId, Long depId, Long teamId) {
    if (ObjectUtil.isEmpty(roleIds)){
      return new ArrayList<>();
    }
    return userMapper.getUsers(roleIds,orgId,depId,teamId);
  }

  /**
   * 获取用户列表(树形组织结构)
   *
   * @param param 参数
   * @return {@link OrgTreeVO}
   */
  public OrgTreeVO userTree(UserTreeParam param) {
    UserSession userSession = UserUtils.getTokenUser();

    Company company = companyService.selectByPrimaryKey(userSession.getOrgId());

    List<User> users = userMapper.getUsers(null,userSession.getOrgId(),null,null);

    DepTeam query = new DepTeam();
    query.setOrgId(userSession.getOrgId());
    query.setStatus(DepTeamEnums.Status.NORMAL.getCode());
    query.setState(0);
    List<DepTeam> depTeams = depTeamService.select(query);

    // 小组
    List<DepTeam> teams = depTeams.stream().filter(p -> ObjectUtil.equals(p.getType(), DepTeamEnums.type.TEAM.getCode())&&
                    ObjectUtil.equals(p.getUnderTeam(), DepTeamEnums.UnderTeam.NOT.getCode()))
            .collect(Collectors.toList());
    List<TeamTreeVO> teamTrees = teams.stream().map(p -> {
      TeamTreeVO team = new TeamTreeVO();
      team.setTeamId(p.getId());
      team.setTeamName(p.getName());
      team.setParentId(p.getParentId());
      List<UserTreeVO> teamUsers = users.stream().filter(u -> ObjectUtil.equals(u.getTeamId(), p.getId())).map(u -> {
        UserTreeVO user = new UserTreeVO();
        user.setUserId(u.getId());
        user.setUserName(u.getName());
        user.setUserNo(u.getUserNo());
        List<RoleVO> roles = new ArrayList<>();
        if (StrUtil.isNotBlank(u.getRoleIds())){
          List<Long> roleIds = StrUtil.split(u.getRoleIds(),",").stream().map(Long::parseLong).collect(Collectors.toList());
          List<String> roleNames = StrUtil.split(u.getRoleNames(),",");
          List<Integer> types = StrUtil.split(u.getTypes(),",").stream().map(Integer::parseInt).collect(Collectors.toList());
          List<Integer> depTypes = StrUtil.split(u.getDepTypes(),",").stream().map(Integer::parseInt).collect(Collectors.toList());
          for (int i =0;i<roleIds.size();i++) {
            RoleVO role = new RoleVO();
            role.setId(roleIds.get(i));
            role.setName(roleNames.get(i));
            role.setType(types.get(i));
            role.setDepType(depTypes.get(i));
            roles.add(role);
          }
        }
        user.setRoles(roles);
        return user;
      }).collect(Collectors.toList());
      team.setUsers(teamUsers);
      return team;
    }).collect(Collectors.toList());

    // 部门
    List<DepTeam> deps = depTeams.stream().filter(p -> ObjectUtil.equals(p.getType(), DepTeamEnums.type.BRANCH_OFFICE.getCode()))
            .collect(Collectors.toList());
    List<DepTreeVO> depTrees = deps.stream().map(p -> {
      DepTreeVO dep = new DepTreeVO();
      dep.setDepId(p.getId());
      dep.setDepName(p.getName());
      dep.setIsAgent(ObjectUtil.equals(p.getIsAgent(), DepTeamEnums.IsAgent.YES.getCode()));
      dep.setIsBankAgent(ObjectUtil.equals(p.getIsBankAgent(), DepTeamEnums.IsBankAgent.YES.getCode()));
      List<UserTreeVO> depUsers = users.stream().filter(u -> ObjectUtil.equals(u.getDepId(), p.getId()) &&
              (ObjectUtil.isNull(u.getTeamId()) || ObjectUtil.equals(u.getTeamId(), 0))).map(u -> {
        UserTreeVO user = new UserTreeVO();
        user.setUserId(u.getId());
        user.setUserName(u.getName());
        user.setUserNo(u.getUserNo());
        List<RoleVO> roles = new ArrayList<>();
        if (StrUtil.isNotBlank(u.getRoleIds())){
          List<Long> roleIds = StrUtil.split(u.getRoleIds(),",").stream().map(Long::parseLong).collect(Collectors.toList());
          List<String> roleNames = StrUtil.split(u.getRoleNames(),",");
          List<Integer> types = StrUtil.split(u.getTypes(),",").stream().map(Integer::parseInt).collect(Collectors.toList());
          List<Integer> depTypes = StrUtil.split(u.getDepTypes(),",").stream().map(Integer::parseInt).collect(Collectors.toList());
          for (int i =0;i<roleIds.size();i++) {
            RoleVO role = new RoleVO();
            role.setId(roleIds.get(i));
            role.setName(roleNames.get(i));
            role.setType(types.get(i));
            role.setDepType(depTypes.get(i));
            roles.add(role);
          }
        }
        user.setRoles(roles);
        return user;
      }).collect(Collectors.toList());
      dep.setDepUsers(depUsers);
      List<TeamTreeVO> teamTreeVOS = teamTrees.stream().filter(t -> ObjectUtil.equals(p.getId(), t.getParentId())).collect(Collectors.toList());
      dep.setTeams(teamTreeVOS);
      return dep;
    }).collect(Collectors.toList());
    if (ObjectUtil.equals(param.getIsBankAgent(), DepTeamEnums.IsBankAgent.YES.getCode())){
      depTrees = depTrees.stream().filter(p->p.getIsBankAgent()).collect(Collectors.toList());
    } else {
      depTrees = depTrees.stream().filter(p->!p.getIsBankAgent()).collect(Collectors.toList());
    }

    // 直属小组
   List<DepTeam> underTeams = depTeams.stream().filter(p -> ObjectUtil.equals(p.getType(), DepTeamEnums.type.TEAM.getCode())&&
                    ObjectUtil.equals(p.getUnderTeam(), DepTeamEnums.UnderTeam.YES.getCode())).collect(Collectors.toList());
    List<TeamTreeVO> underTeamTrees = underTeams.stream().map(p -> {
      TeamTreeVO underTeam = new TeamTreeVO();
      underTeam.setTeamId(p.getId());
      underTeam.setTeamName(p.getName());
      underTeam.setParentId(p.getParentId());
      List<UserTreeVO> underTeamUsers = users.stream().filter(u -> ObjectUtil.equals(u.getTeamId(), p.getId())).map(u -> {
        UserTreeVO user = new UserTreeVO();
        user.setUserId(u.getId());
        user.setUserName(u.getName());
        user.setUserNo(u.getUserNo());
        List<RoleVO> roles = new ArrayList<>();
        if (StrUtil.isNotBlank(u.getRoleIds())){
          List<Long> roleIds = StrUtil.split(u.getRoleIds(),",").stream().map(Long::parseLong).collect(Collectors.toList());
          List<String> roleNames = StrUtil.split(u.getRoleNames(),",");
          List<Integer> types = StrUtil.split(u.getTypes(),",").stream().map(Integer::parseInt).collect(Collectors.toList());
          List<Integer> depTypes = StrUtil.split(u.getDepTypes(),",").stream().map(Integer::parseInt).collect(Collectors.toList());
          for (int i =0;i<roleIds.size();i++) {
            RoleVO role = new RoleVO();
            role.setId(roleIds.get(i));
            role.setName(roleNames.get(i));
            role.setType(types.get(i));
            role.setDepType(depTypes.get(i));
            roles.add(role);
          }
        }
        user.setRoles(roles);
        return user;
      }).collect(Collectors.toList());
      underTeam.setUsers(underTeamUsers);
      return underTeam;
    }).collect(Collectors.toList());

    OrgTreeVO orgTreeVO = new OrgTreeVO();
    orgTreeVO.setOrgId(company.getId());
    orgTreeVO.setOrgName(company.getName());
    orgTreeVO.setDeps(depTrees);
    orgTreeVO.setUnderTeams(underTeamTrees);
    List<UserTreeVO> orgUsers = users.stream().filter(u -> (ObjectUtil.equals(u.getDepId(), 0)||ObjectUtil.isNull(u.getDepId()))&&
            (ObjectUtil.equals(u.getTeamId(), 0)||ObjectUtil.isNull(u.getTeamId()))).map(u -> {
      UserTreeVO user = new UserTreeVO();
      user.setUserId(u.getId());
      user.setUserName(u.getName());
      user.setUserNo(u.getUserNo());
      List<RoleVO> roles = new ArrayList<>();
      if (StrUtil.isNotBlank(u.getRoleIds())){
        List<Long> roleIds = StrUtil.split(u.getRoleIds(),",").stream().map(Long::parseLong).collect(Collectors.toList());
        List<String> roleNames = StrUtil.split(u.getRoleNames(),",");
        List<Integer> types = StrUtil.split(u.getTypes(),",").stream().map(Integer::parseInt).collect(Collectors.toList());
        List<Integer> depTypes = StrUtil.split(u.getDepTypes(),",").stream().map(Integer::parseInt).collect(Collectors.toList());
        for (int i =0;i<roleIds.size();i++) {
          RoleVO role = new RoleVO();
          role.setId(roleIds.get(i));
          role.setName(roleNames.get(i));
          role.setType(types.get(i));
          role.setDepType(depTypes.get(i));
          roles.add(role);
        }
      }
      user.setRoles(roles);
      return user;
    }).collect(Collectors.toList());
    orgTreeVO.setOrgUsers(orgUsers);

    return orgTreeVO;
  }

  public List<User> listUserByTeamId(Long teamId) {
    Example example = new Example(User.class);
    example.and().andEqualTo("teamId", teamId).andEqualTo("status", User.Status.NORMAL.getCode());
    return userMapper.selectByExample(example);
  }

  /**
   * 查询所有员工
   * @return
   */
  public List<UserQueryVO> listAllUser() {
    UserSession session = UserUtils.getTokenUser();
    Long orgId = session.getOrgId();
    User userQuery = new User();
    userQuery.setOrgId(orgId);
    userQuery.setStatus(0);
    List<User> userList = super.select(userQuery);
    List<UserQueryVO> voList = new ArrayList<>();
    Map<Long, String> depTeamMap = depTeamService.getNames();
    Map<Long, String> orgMap = companyService.getNames();
    userList.forEach(user -> {
      UserQueryVO vo = AnmiBeanutils.copy(user, UserQueryVO.class);
      vo.setDepName(depTeamMap.get(vo.getDepId()));
      vo.setTeamName(depTeamMap.get(vo.getTeamId()));
      vo.setOrgName(orgMap.get(vo.getOrgId()));
      voList.add(vo);
    });
    List<Long> depIds = voList.stream().filter(userQueryVO -> Objects.nonNull(userQueryVO.getDepId()))
            .map(UserBaseVO::getDepId).distinct().collect(Collectors.toList());
    List<Long> bankAgentDepTeamIds = depTeamService.getBankAgentDepTeamIds(depIds);
    voList.forEach(userQueryVO -> {
      if (bankAgentDepTeamIds.contains(userQueryVO.getDepId())) {
        userQueryVO.setIsBankAgent(DepTeamEnums.IsBankAgent.YES.getCode());
      } else {
        userQueryVO.setIsBankAgent(DepTeamEnums.IsBankAgent.NO.getCode());
      }
    });
    return voList;
  }

}
