package com.anmi.collection.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.alfred.dto.CaseRepaymentQueryDto;
import com.anmi.alfred.entity.TransferCaseRepaymentResult;
import com.anmi.alfred.response.concrete.CaseRepaymentResponse;
import com.anmi.alfred.response.concrete.RepStatisticsResponse;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.CaseLogEnums;
import com.anmi.collection.common.enums.RepaymentEnums;
import com.anmi.collection.common.enums.flow.ApplyEnums;
import com.anmi.collection.common.enums.flow.FlowEnums;
import com.anmi.collection.common.enums.flow.FlowHandleRecordEnums;
import com.anmi.collection.configs.FileStoreConfig;
import com.anmi.collection.dto.fileStorage.UploadZipFileInfo;
import com.anmi.collection.entity.requset.cases.RepaymentQueryParam;
import com.anmi.collection.entity.requset.cases.repayment.RepaymentApplyParam;
import com.anmi.collection.entity.requset.cases.repayment.RepaymentCreateModel;
import com.anmi.collection.entity.requset.flow.ApproveListParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.RepaymentStatisticVO;
import com.anmi.collection.entity.response.cases.RepaymentVO;
import com.anmi.collection.entity.response.flow.ApproveListVO;
import com.anmi.collection.entity.response.flow.FlowNodeVO;
import com.anmi.collection.entity.response.flow.FlowVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.mapper.OrgSwitchMapper;
import com.anmi.collection.mapper.RepaymentMapper;
import com.anmi.collection.service.bi.BiStatisticsService;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.service.flow.FlowService;
import com.anmi.collection.service.remote.RemoteAlfredService;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.CmUtil;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.*;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RepaymentService extends BaseService<CaseRepayment> {
  private static final Logger LOGGER = LoggerFactory.getLogger(RepaymentService.class);
  @Resource private RepaymentMapper repaymentMapper;
  @Resource private CompanyService companyService;
  @Resource private UserService userService;
  @Resource private InnerBatchService innerBatchService;
  @Resource private DeltService deltService;
  @Resource private ProductService productService;
  @Resource private CaseMapper caseMapper;
  @Resource private CaseService caseService;
  @Resource private DepTeamService depTeamService;
  @Resource private OutBatchService outBatchService;
  @Resource private AsyncTaskService asyncTaskService;
  @Resource private CaseLogService caseLogService;
  @Resource private UserStatisticsService userStatisticsService;
  @Resource private OrgSwitchMapper orgSwitchMapper;
  @Resource private RemoteAlfredService remoteAlfredService;
  @Resource private BiStatisticsService biStatisticsService;
  @Resource private MediationStatisticsService mediationStatisticsService;
  @Resource private FlowService flowService;
  @Resource private FileStorageStrategyFactory fileStorageStrategyFactory;

  /**
   * 还款作废
   *
   * @param repayIds
   * @return
   */
  @Transactional(rollbackFor = Exception.class)
  public void changeStatus(List<Long> repayIds) {
    List<Integer> applyStatus =
        Lists.newArrayList(
            RepaymentEnums.ApplyStatus.SUCCESS.getCode(),
            RepaymentEnums.ApplyStatus.IMPORT.getCode());
    Example example = new Example(CaseRepayment.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andIn("id", repayIds)
        .andEqualTo("status", RepaymentEnums.Status.NORMAL.getCode())
        .andIn("applyStatus", applyStatus);
    List<CaseRepayment> repaymentList = selectByExample(example);
    if (CommonUtils.isEmpty(repaymentList)) {
      throw new ApiException("只有正常的状态才能被作废");
    }
    // 过滤出案件已彻底删除的记录
    Set<Long> caseIds = repaymentList.stream().map(CaseRepayment::getCaseId).collect(Collectors.toSet());
    List<Case> caseList = caseService.selectByIdList(Lists.newArrayList(caseIds), Case.class);
    List<Case> existCaseList = caseList.stream()
            .filter(c-> c.getRecovery() != CaseEnums.Recovery.DELETE_COMPLETELY.getCode())
            .collect(Collectors.toList());
    if (CollectionUtils.isEmpty(existCaseList)) {
      throw new ApiException("案件已彻底删除，不支持作废操作。");
    }
    List<Long> existCaseIds = existCaseList.stream().map(Case::getId).collect(Collectors.toList());
    List<CaseRepayment> existRepayment = repaymentList.stream()
            .filter(r -> existCaseIds.contains(r.getCaseId()))
            .collect(Collectors.toList());
    List<Long> existRepayIds = existRepayment.stream().map(CaseRepayment::getId).collect(Collectors.toList());
    // 更新还款状态
    UserSession userSession = getTokenUser();
    CaseRepayment repayment = new CaseRepayment();
    repayment.setStatus(-1);
    repayment.setUpdateBy(userSession.getId());
    repayment.setUpdateTime(new Date());
    Example updateExample = new Example(CaseRepayment.class);
    updateExample.and().andIn("id", existRepayIds);
    repaymentMapper.updateByExampleSelective(repayment, updateExample);

    // 根据id查找所有的cases，将这些case的pay_amount字段修改
    resetCasesPayAmount(existRepayIds, -1);
    // 创建任务
    Long taskId = asyncTaskService.createRepDelTask(userSession, (long) existRepayment.size());
    // 生成日志
    Map<Long, Case> caseMap = existCaseList.stream().collect(Collectors.toMap(Case::getId, c -> c));
    List<CaseLog> caseLogList = new ArrayList<>();
    for (CaseRepayment rep : existRepayment) {
      Case aCase = caseMap.get(rep.getCaseId());
      if (aCase == null) {
        continue;
      }
      CaseLog log = new CaseLog();
      log.setCreateTime(new java.util.Date());
      log.setType(CaseLogEnums.Type.REPAYMENT_DEL.getCode());
      log.setCaseId(aCase.getId());
      log.setIdCard(aCase.getIdCard());
      log.setCaseName(aCase.getName());
      log.setSerialNo(aCase.getSerialNo());
      log.setOutSerialNo(aCase.getOutSerialNo());
      log.setCreateBy(userSession.getId() == null ? 0L : userSession.getId());
      log.setCreateByName(userSession.getName());
      log.setOrgId(userSession.getOrgId());
      // 获取关联信息
      log.setBatchId(aCase.getInnerBatchId());
      log.setBatchNo(innerBatchService.getNames().get(aCase.getInnerBatchId()));
      log.setOrgDeltId(aCase.getOrgDeltId());
      log.setOrgDeltName(deltService.getNames().get(aCase.getOrgDeltId()));
      log.setDunnerId(aCase.getUserId());
      log.setDunnerName(userService.getNames(userSession.getOrgId()).get(aCase.getUserId()));
      log.setProductId(aCase.getProductId());
      log.setProductName(productService.getNames().get(aCase.getProductId()));
      log.setCaseAmount(aCase.getAmount());
      log.setDepId(aCase.getDepId());
      log.setTeamId(aCase.getTeamId());
      log.setTeamName(depTeamService.getNames().get(aCase.getTeamId()));
      log.setOutBatchId(aCase.getOutBatchId());
      log.setOutBatchName(outBatchService.getNames().get(aCase.getOutBatchId()));
      log.setOwnMobile(aCase.getOwnMobile());
      log.setOutSerialTemp(aCase.getOutSerialTemp());
      Map<String, String> caseJson = new HashMap<>();
      caseJson.put("case_status", aCase.getCaseStatus().toString());
      caseJson.put("allot_status", aCase.getAllotStatus().toString());
      caseJson.put("recovery", aCase.getRecovery().toString());
      if (aCase.getOverdueDate() != null) {
        caseJson.put("overdue_date", String.valueOf(aCase.getOverdueDate().getTime()));
      }
      if (aCase.getOverdueDays() != null) {
        caseJson.put("overdue_days", aCase.getOverdueDays().toString());
      }
      caseJson.put("entrust_start_time", String.valueOf(aCase.getEntrustStartTime().getTime()));
      caseJson.put("entrust_end_time", String.valueOf(aCase.getEntrustEndTime().getTime()));
      log.setCaseJson(caseJson);
      log.setTaskId(taskId);
      log.setRepaymentAmount(rep.getRepaymentAmount());
      caseLogList.add(log);
    }
    caseLogService.insertBatch(caseLogList);
    // 还款作废更新历史统计数据 包括user_statistics、case_statistics和mediation_statistics三表
    userStatisticsService.repaymentInvalid(existRepayment);
    biStatisticsService.restatForRepayInvalid(existRepayment);
    mediationStatisticsService.restatForRepayInvaild(existRepayment);
  }

  /**
   * @param repayIds  还款记录ids
   * @param applyType -1：作废，1：通过
   */
  private void resetCasesPayAmount(List<Long> repayIds, Integer applyType) {
    if (CommonUtils.isEmpty(repayIds)) {
      return;
    }
    List<Repayment> caseRepayments = repaymentMapper.selectCaseRepayById(repayIds);
    Map<Long, Long> caseRepMap = new HashMap<>();
    if (applyType == -1) {
      // 作废
      for (Repayment rep : caseRepayments) {
        Long tmp = rep.getCasePayAmount() - rep.getRepaymentAmount();
        caseRepMap.put(rep.getCaseId(), tmp > 0 ? tmp : 0L);
      }
    } else if (applyType == 1) {
      // 通过
      for (Repayment rep : caseRepayments) {
        Long tmp = rep.getCasePayAmount() + rep.getRepaymentAmount();
        caseRepMap.put(rep.getCaseId(), tmp);
      }
    }
    // 更新所有的case
    if (caseRepMap.size() > 0) {
      caseMapper.updatePayAmount(caseRepMap);
    }

  }

  /**
   * 还款纪录查询
   *
   * @param params params
   * @return {@link List}<{@link RepaymentQuery}>
   */
  public List<RepaymentQuery> queryResult(Map params) {
    return repaymentMapper.selectQuery(params);
  }

  /**
   * 分页
   *
   * @param repaymentQueryParam 实体
   * @param pageParam 分页
   * @return
   */
  public PageOutput<RepaymentVO> queryByPage (RepaymentQueryParam repaymentQueryParam, PageParam pageParam){
    Map<String,Object> params = convertToMap(repaymentQueryParam);
    Page page = super.setPage(pageParam);
    List<RepaymentQuery> entityList = queryResult(params);
    List<RepaymentVO> repaymentVOS = new ArrayList<>();
    if (!CommonUtils.isEmpty(entityList)) {
      repaymentVOS=convertToVOS(entityList);
    }
    return new PageOutput(page.getPageNum(), page.getPageSize(), page != null ? (int) page.getTotal() : repaymentVOS.size(), repaymentVOS);
  }

  /**
   * 分页
   *
   * @param repaymentQueryParam 实体
   * @return
   */
  public RepaymentStatisticVO repaymentStatistic (RepaymentQueryParam repaymentQueryParam){
    Map<String,Object> params = convertToMap(repaymentQueryParam);
     return repaymentMapper.queryStatic(params);
  }

  /**
   * 分页
   *
   * @param repaymentQueryParam 实体
   * @return
   */
  public RepaymentStatisticVO repaymentStatisticEs (RepaymentQueryParam repaymentQueryParam){
    Map<String,Object> params = convertToMap(repaymentQueryParam);
    CaseRepaymentQueryDto dto = new CaseRepaymentQueryDto();
    BeanUtils.copyFromMap(params, dto);
    RepStatisticsResponse response = remoteAlfredService.fetchRepStatisticFromRemote(dto);
    RepaymentStatisticVO vo=new RepaymentStatisticVO();
    vo.setRepAmount(response.getRepAmount());
    vo.setRepCount(response.getRepCount());
    return vo;
  }

  private Map<String,Object> convertToMap(RepaymentQueryParam repaymentQueryParam){
    repaymentQueryParam.setStatus(0);
    UserSession userSession = getTokenUser();
    Map<String,Object> params = JsonUtils.fromJson(JsonUtils.toJson(repaymentQueryParam), Map.class);
    assert params != null;
    params.put("orgId", userSession.getOrgId());
    if (!CommonUtils.isEmpty(repaymentQueryParam.getRepaymentTime())) {
      String[] repaymentTime = repaymentQueryParam.getRepaymentTime().split(",");
      params.put("repaymentStart", new Date(Long.parseLong(repaymentTime[0])));
      params.put("repaymentEnd", new Date(Long.parseLong(repaymentTime[1])));
    }
    if (null != repaymentQueryParam.getStart()) {
      params.put("start", new Date(repaymentQueryParam.getStart()));
    }
    if (null != repaymentQueryParam.getEnd()) {
      params.put("end", new Date(repaymentQueryParam.getEnd()));
    }
    params.put("userIds", repaymentQueryParam.getCaseOperators());
    params.put("depIds", repaymentQueryParam.getDepIds());
    params.put("teamIds", repaymentQueryParam.getTeamIds());
    params.put("innerBatchIds", repaymentQueryParam.getInnerBatchIds());
    params.put("outBatchIds", repaymentQueryParam.getOutBatchIds());
    // 身份证号和债务人姓名多搜
    params.put("names", repaymentQueryParam.getNames());
    params.put("idCards", repaymentQueryParam.getIdCards());
    params.put("ownMobiles", repaymentQueryParam.getOwnMobiles());
    params.put("allotAgent", repaymentQueryParam.getAllotAgent());
    if (UserUtils.likeBranchAdmin()) {
      params.put("depId", getTokenUser().getDepId());
    }
    List<Integer> applyStatusInt = new ArrayList<>();
    if (StringUtils.isNotBlank(repaymentQueryParam.getApplyStatus())) {
      for (String applyStatus : repaymentQueryParam.getApplyStatus().split(",")) {
        applyStatusInt.add(Integer.valueOf(applyStatus));
      }
    }
    if (repaymentQueryParam.getRepListType() == 0) {
      // 还款列表：审核成功和导入成功(审核成功里可能是还款审核成功或者划扣审核成功，但是只要审核成功就都算还款列表里)
      if (applyStatusInt.isEmpty()) {
        applyStatusInt =
            Lists.newArrayList(
                RepaymentEnums.ApplyStatus.SUCCESS.getCode(),
                RepaymentEnums.ApplyStatus.IMPORT.getCode());
      }
    } else if (repaymentQueryParam.getRepListType() == 1
        || repaymentQueryParam.getRepListType() == 4) {
      // 还款审核、催员还款申请：还款类型、未通过、审核中、审核成功、超时终止
      params.put("type", RepaymentEnums.Type.REP.getCode());
      if (applyStatusInt.isEmpty()) {
        applyStatusInt =
            Lists.newArrayList(
                RepaymentEnums.ApplyStatus.FAIL.getCode(),
                RepaymentEnums.ApplyStatus.APPLYING.getCode(),
                RepaymentEnums.ApplyStatus.SUCCESS.getCode(),
                RepaymentEnums.ApplyStatus.TIMEOUT.getCode());
      }
    } else if (repaymentQueryParam.getRepListType() == 2) {
      // 催员还款列表：未通过、审核中、审核成功(划扣或者还款)、导入成功
      params.put("repListType2", "repListType2"); // or (type = 1 and apply_status = 1)
    } else if (repaymentQueryParam.getRepListType() == 3
        || repaymentQueryParam.getRepListType() == 5) {
      // 划扣审核、催员划扣申请：划扣类型、未通过、审核中、审核成功、超时终止
      params.put("type", RepaymentEnums.Type.PAYTO.getCode());
      if (applyStatusInt.isEmpty()) {
        applyStatusInt =
            Lists.newArrayList(
                RepaymentEnums.ApplyStatus.FAIL.getCode(),
                RepaymentEnums.ApplyStatus.APPLYING.getCode(),
                RepaymentEnums.ApplyStatus.SUCCESS.getCode(),
                RepaymentEnums.ApplyStatus.TIMEOUT.getCode());
      }
    }
    params.put("applyStatus", applyStatusInt);
    // 催员页面的所有申请需申请人是本人
    if (repaymentQueryParam.getRepListType() == 4 || repaymentQueryParam.getRepListType() == 5) {
      params.put("createBy", userSession.getId());
    }
    if (repaymentQueryParam.getApplyTimeRange() != null) {
      String applyTimeStart = repaymentQueryParam.getApplyTimeRange().split(",")[0];
      String applyTimeEnd = repaymentQueryParam.getApplyTimeRange().split(",")[1];
      params.put(
          "applyTimeStart",
          DateUtils.getStartTimeOfDate(new java.util.Date(Long.parseLong(applyTimeStart))));
      params.put(
          "applyTimeEnd",
          DateUtils.getEndTimeOfDay(new java.util.Date(Long.parseLong(applyTimeEnd))));
    }
    return params;
  }

  /**
   * 还款列表查询（es）
   *
   * @param repaymentQueryParam 还款查询参数
   * @param pageParam           页面参数
   * @return {@link PageOutput}<{@link RepaymentVO}>
   * @throws Exception 例外
   */
  public PageOutput<RepaymentVO> queryByPageUsingEs(RepaymentQueryParam repaymentQueryParam, PageParam pageParam) {
    Map<String, Object> params = convertToMap(repaymentQueryParam);
    CaseRepaymentQueryDto dto = new CaseRepaymentQueryDto();
    BeanUtils.copyFromMap(params, dto);
    CaseRepaymentResponse response = remoteAlfredService.fetchFromCaseRepaymentRemote(dto);
    if (CollectionUtils.isEmpty(response.getList())) {
      return new PageOutput<>(pageParam.getPage(), pageParam.getLimit(), 0, Collections.emptyList());
    }
    long total = response.getTotalNum();
    List<Long> repaymentIds = response.getList().stream().map(TransferCaseRepaymentResult::getId).collect(Collectors.toList());
    List<RepaymentQuery> entityList = repaymentMapper.selectQueryByIds(repaymentIds);

    List<RepaymentVO> repaymentVOS = new ArrayList<>();
    if (!CommonUtils.isEmpty(entityList)) {
      repaymentVOS = convertToVOS(entityList);
    }
    return new PageOutput<>(pageParam.getPage(), pageParam.getLimit(), (int) total, repaymentVOS);
  }

  private List<RepaymentVO> convertToVOS(List<RepaymentQuery> repaymentList) {
    if(CollectionUtils.isEmpty(repaymentList)){
      return Collections.emptyList();
    }
    Map<Long, String> deltMap = deltService.getNames();
    Map<Long, String> innerBatchMap = innerBatchService.getNames();
    Map<Long, String> outBatchMap = outBatchService.getNames();
    Map<Long, String> productMap = productService.getNames();
    Map<Long, String> userMap = userService.getNames(repaymentList.get(0).getOrgId());
    Map<Long, String> orgMap = companyService.getNames();
    Map<Long, String> depTeamMap = depTeamService.getNames();

    List<RepaymentVO> result = new ArrayList<>();
    for(RepaymentQuery repayment:repaymentList) {
      RepaymentVO repaymentVO = new RepaymentVO();
      BeanUtils.copyProperties(repayment, repaymentVO);
      // 隐藏卡号和身份证
      String idCard = repayment.getDebtIdCard();
      String cardNo = repayment.getRepaymentCardNo();
      repaymentVO.setDebtIdCard(idCard);
      repaymentVO.setRepaymentCardNo(cardNo);
      repaymentVO.setDeltName(deltMap.get(repayment.getOrgDeltId()));
      repaymentVO.setBatchNo(innerBatchMap.get(repayment.getInnerBatchId()));
      repaymentVO.setProductName(productMap.get(repayment.getProductId()));
      repaymentVO.setOutSerialNo(repayment.getOutSerialNo().substring(0,repayment.getOutSerialNo().lastIndexOf("#")));
      repaymentVO.setCreateBy(userMap.get(repayment.getCreateBy()));
      repaymentVO.setUpdateBy(userMap.get(repayment.getUpdateBy()));
      repaymentVO.setEntrustStartTime(
        repayment.getEntrustStartTime() == null ? null : repayment.getEntrustStartTime().getTime());
      repaymentVO.setEntrustEndTime(
        repayment.getEntrustEndTime() == null ? null : repayment.getEntrustEndTime().getTime());
      repaymentVO.setUserId(repayment.getUserId());
      repaymentVO.setUserName(userMap.get(repayment.getUserId()));
      repaymentVO.setAuditByName(userMap.get(repayment.getAuditBy()));
      repaymentVO.setAuditTime(repayment.getAuditTime() == null ? null : repayment.getAuditTime().getTime());
      repaymentVO.setOrgName(orgMap.get(repayment.getOrgId()));
      repaymentVO.setDepName(depTeamMap.get(repayment.getDepId()));
      repaymentVO.setTeamName(depTeamMap.get(repayment.getTeamId()));
      repaymentVO.setInnerBatchNo(innerBatchMap.get(repayment.getInnerBatchId()));
      repaymentVO.setOutBatchNo(outBatchMap.get(repayment.getOutBatchId()));
      if (ObjectUtil.isNotNull(repaymentVO.getAllotAgent())){
        repaymentVO.setAgentType(ObjectUtil.equals(repaymentVO.getAllotAgent(), 1) ? ApplyEnums.AgentType.INNER.getCode():ApplyEnums.AgentType.OUTER.getCode());
      }
      result.add(repaymentVO);
    }

    return result;
  }

  /**
   * 新增还款信息
   *
   * @param repaymentCreateModel
   * @param files
   */
  @Transactional(rollbackFor = Throwable.class)
  public void add(RepaymentCreateModel repaymentCreateModel, MultipartFile[] files) throws Exception{
    checkParam(repaymentCreateModel, files);
    String outSerialNo = repaymentCreateModel.getOutSerialNo() + "#" + repaymentCreateModel.getOrgDeltId();
    Case aCase = caseService.selectByOutSeriaNo(outSerialNo);
    if (aCase == null) {
      throw new ApiException("案件不存在");
    }
    UserSession login = getTokenUser();
    FlowEnums.BusinessType businessType;
    if (ObjectUtil.equals(RepaymentEnums.Type.PAYTO.getCode(), repaymentCreateModel.getType())){
      businessType = FlowEnums.BusinessType.HUAK;
    } else {
      businessType = FlowEnums.BusinessType.HUANK;
    }
    FlowEnums.AgentType agentType = ObjectUtil.equals(aCase.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?FlowEnums.AgentType.OUTER:FlowEnums.AgentType.INNER;
    // 获取审批流
    FlowVO flowVO = flowService.flowChoice(login.getOrgId(), agentType, businessType);
    // 申请是否自动通过
    Boolean autoPassApply = flowService.autoPassApply(login,flowVO);
    CaseRepayment repayment = new CaseRepayment();
    org.springframework.beans.BeanUtils.copyProperties(repaymentCreateModel, repayment);
    if (repaymentCreateModel.getType() == 0) {
      repayment.setRepaymentTime(new java.util.Date(repaymentCreateModel.getRepaymentTime()));
    }
    repayment.setDebtName(aCase.getName());
    repayment.setDebtIdCard(aCase.getIdCard());
    repayment.setOutSerialNo(outSerialNo);
    repayment.setCaseId(aCase.getId());
    repayment.setOutSerialTemp(aCase.getOutSerialTemp());
    repayment.setOrgDeltId(aCase.getOrgDeltId());
    repayment.setAllotAgent(ObjectUtil.equals(aCase.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?CaseEnums.AllotAgent.WW.getCode():CaseEnums.AllotAgent.NC.getCode());
    repayment.setCreateBy(login.getId());
    repayment.setUpdateBy(login.getId());
    Long newMoney = CmUtil.getAmountLong(new BigDecimal(repaymentCreateModel.getRepaymentAmount()));
    repayment.setRepaymentAmount(newMoney);
    if(RepaymentEnums.Type.PAYTO.getCode().equals(repayment.getType())){
      repayment.setRepaymentStyle("划扣还款");
    } else {
      repayment.setRepaymentStyle("提交还款");
    }

    repayment.setOrgId(login.getOrgId());
    repayment.setDepId(aCase.getDepId());
    repayment.setTeamId(aCase.getTeamId());
    repayment.setFlowId(flowVO.getId());

    // 插入成功后，打包文件并上传oss
    if (!CommonUtils.isEmpty(files)) {
      String url = toZip(files, aCase.getOutSerialTemp());
      if (!StringUtils.isBlank(url)) {
        repayment.setVoucherUrl(url);
      }
    }

    if(autoPassApply) {
      repayment.setApplyStatus(RepaymentEnums.ApplyStatus.SUCCESS.getCode());
      repayment.setAuditTime(new Date());
      if(RepaymentEnums.Type.PAYTO.getCode().equals(repayment.getType())){
        repayment.setRepaymentFrom(repayment.getDebtName());
        repayment.setRepaymentTime(new Date());
      }
      CaseLog caseLog = this.caseLogService.selectLastCaseLogForRepayment(aCase.getId(), DateUtils.getEndTimeOfDay(repayment.getRepaymentTime()));
      if (caseLog != null) {
        repayment.setDepId(caseLog.getDepId());
        repayment.setTeamId(caseLog.getTeamId());
        repayment.setCaseOperator(caseLog.getDunnerId());
        repayment.setCaseOperatorName(caseLog.getDunnerName());
        repayment.setRepaymentAuto(RepaymentEnums.Auto.OPERATOR.getCode());
      }else{
        Map<Long,String> userMap=userService.getNames(login.getOrgId());
        repayment.setCaseOperator(aCase.getUserId());
        repayment.setCaseOperatorName(userMap.get(aCase.getUserId()));
        if (aCase.getUserId() != null) {
          repayment.setRepaymentAuto(RepaymentEnums.Auto.OPERATOR.getCode());
        } else {
          repayment.setRepaymentAuto(RepaymentEnums.Auto.SYS.getCode());
        }
      }
    }
    Integer timeLimitSwitch = flowVO.getTimeLimitSwitch();
    if (ObjectUtil.equals(timeLimitSwitch, FlowEnums.TimeLimitSwitch.YES.getCode())){
      String timeLimit = flowVO.getTimeLimit();
      String hour = StrUtil.subBefore(timeLimit, ":", true);
      String minute = StrUtil.subAfter(timeLimit, ":", true);
      Date outTime = DateUtil.offsetMinute(DateUtil.offsetHour(new Date(),Integer.valueOf(hour)),Integer.valueOf(minute));
      repayment.setOutTime(outTime);
    }
    repaymentMapper.insertSelective(repayment);

    List<FlowNodeVO> nodes = flowVO.getNodes();
    Map<String,Object> applyMap = cn.hutool.core.bean.BeanUtil.beanToMap(repayment);
    String parentUniqueCode = flowService.getApplyOrRuleNodeUniqueCode(nodes,applyMap,businessType,agentType,login);
    if (autoPassApply){
      flowService.findNextCopyNodeAndExecute(parentUniqueCode,flowVO, repayment.getId(),login);
    } else {
      flowService.findNextNodeAndExecute(parentUniqueCode,flowVO, repayment.getId(),login, login.getId());
    }
  }

  /**
   * 申请通过后的处理
   *
   * @param repayment   申请记录
   * @param userSession 用户会话
   */
  private void passApplyTodo(CaseRepayment repayment, UserSession userSession) {
    // 通过的话，需要去更新case的pay_amount字段
    resetCasesPayAmount(Lists.newArrayList(repayment.getId()), RepaymentEnums.ApplyStatus.SUCCESS.getCode());
    // 还款金额大于等于贷款金额，自动结案
    OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(userSession.getOrgId());
    if (orgSwitch.getAutoEndSwitch() == 1) {
      caseService.autoEnd(userSession, Lists.newArrayList(repayment.getCaseId()));
    }

    //管理员提交的还款不需要审核，测试提交的还还款数据的还款日期有可能是早于今天的，需要更新还款统计数据，提交划扣数据的还款日期是今天，不需要操作
    if (Objects.equals(repayment.getType(), RepaymentEnums.Type.REP.getCode())) {
      userStatisticsService.handleBeforeRepaymentStatistics(Lists.newArrayList(repayment));
      biStatisticsService.handleBeforeRepaymentStatistics(Lists.newArrayList(repayment));
      mediationStatisticsService.handleBeforeRepaymentStatistics(Lists.newArrayList(repayment));
    }
  }

  /**
   * 审批
   *
   * @param applyParam 应用参数
   */
  @Transactional(rollbackFor = Exception.class)
  public void changeApplyStatus(RepaymentApplyParam applyParam) throws Exception{
    verifyTime();

    UserSession userSession = UserUtils.getTokenUser();
    List<Long> applyIds = applyParam.getIds();
    AssertUtil.notEmpty(applyIds,"请选择申请");
    AssertUtil.isTrue(applyIds.size()==1,"暂不支持批量审批");
    AssertUtil.isTrue(ObjectUtil.equals(applyParam.getApplyType(),1)||ObjectUtil.equals(applyParam.getApplyType(),-1),"审批状态错误");

    Long applyId = applyIds.get(0);
    CaseRepayment applyRecord = selectByPrimaryKey(applyId);
    AssertUtil.notNull(applyRecord, "未发现申请");
    AssertUtil.isTrue(ObjectUtil.equals(applyRecord.getStatus(), 0), "申请非审批中");
    AssertUtil.notNull(applyRecord.getFlowId(), "审批流异常");

    Long flowId = applyRecord.getFlowId();
    Long applyUserId = applyRecord.getCreateBy();

    FlowHandleRecordEnums.HandleStatus approveStatus = ObjectUtil.equals(applyParam.getApplyType(),1)?FlowHandleRecordEnums.HandleStatus.PASS:FlowHandleRecordEnums.HandleStatus.REFUSE;
    flowService.execApprove(applyId,flowId,approveStatus,applyParam.getApplyDesc(),userSession,applyUserId);
  }

  /**
   * 更新申请 处理状态
   *
   * @param applyId            申请id
   * @param approveStatus      审批状态
   * @param approveUserSession 更新人session
   */
  public void updateHandleStatus(Long applyId, FlowHandleRecordEnums.HandleStatus approveStatus,
                                 UserSession approveUserSession) throws Exception{
    Integer status = ObjectUtil.equals(approveStatus,FlowHandleRecordEnums.HandleStatus.PASS)?1:-1;
    CaseRepayment applyRecord = selectByPrimaryKey(applyId);
    applyRecord.setApplyStatus(status);
    applyRecord.setUpdateBy(approveUserSession.getId());
    applyRecord.setUpdateTime(new Date());
    if (applyRecord.getType() == 1 && status == 1) {
      // 划扣通过后变为还款，需要更改他的还款方式，还款时间变为审核通过时间
      applyRecord.setRepaymentStyle("划扣还款");
      applyRecord.setRepaymentFrom(applyRecord.getDebtName());
      applyRecord.setRepaymentTime(new Date());
    }
    applyRecord.setAuditTime(new Date());
    applyRecord.setAuditBy(approveUserSession.getId());
    updateByPrimaryKeySelective(applyRecord);

    if (ObjectUtil.equals(approveStatus,FlowHandleRecordEnums.HandleStatus.PASS)){
      passApplyTodo(applyRecord,approveUserSession);
    }
  }

  private void checkParam(RepaymentCreateModel repaymentCreateModel, MultipartFile[] files) {
    if (StringUtils.length(repaymentCreateModel.getRepaymentType()) > 100) {
      throw new ApiException("还款类型：" + repaymentCreateModel.getRepaymentType() + "超出限制！");
    }
    //        if (repaymentCreateModel.getType() == 0 && CommonUtils.isEmpty(files)) {
    //            throw new ApiException("提交还款信息需要还款凭证！");
    //        }
    // 还款人如果没填，默认本人
    if (StringUtils.isBlank(repaymentCreateModel.getRepaymentFrom())) {
      repaymentCreateModel.setRepaymentFrom("本人");
    }
  }

  private String toZip(MultipartFile[] files, String outSerialTemp) {
    List<File> fileList = new ArrayList<>();
    for (MultipartFile multipartFile : files) {
      String fileName = multipartFile.getOriginalFilename();
      fileName =
          fileName.split("\\.")[0]
              + "_"
              + StringUtils.getRandomNumberBIT6()
              + "."
              + fileName.split("\\.")[1];
      File file = FileStoreConfig.convertToFile(fileName, multipartFile);
      fileList.add(file);
    }
    String rNumber = StringUtils.getRandomNumberBIT6();
    String fileRealName = "还款(划扣)凭证-案件编号" + outSerialTemp + "-" + rNumber + ".zip";
    Date expireDate = DateUtils.addDays(new Date(), 10 * 365);

    UploadZipFileInfo uploadZipFileInfo = new UploadZipFileInfo();
    uploadZipFileInfo.setFiles(fileList)
            .setFileName(fileRealName)
            .setExpireDate(expireDate)
            .setBucket(systemConfig.getReductionBucket())
            .setLocalUrl( systemConfig.getReductionPath() + File.separator + fileRealName);
    FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
    return fileStorageStrategy.uploadFilesToZip(uploadZipFileInfo);
  }

  /**
   * 案件彻底物理删除，级联删除审核中的还款和划扣记录
   * @param caseIds 案件ids
   */
  @Transactional(rollbackFor = Exception.class)
  public void delRepayment(List<Long> caseIds) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return;
    }
    Example example = new Example(CaseRepayment.class);
    example.and().andIn("caseId", caseIds);
    example.and().andEqualTo("applyStatus", RepaymentEnums.ApplyStatus.APPLYING.getCode());
    CaseRepayment repayment = new CaseRepayment();
    repayment.setStatus(-1);
    repayment.setUpdateTime(new Date());
    int update = repaymentMapper.updateByExampleSelective(repayment, example);
  }

  public List<CaseRepayment> getRepaymentList(Long orgId, List<String> outSerialNoList) {
    if (CollectionUtils.isEmpty(outSerialNoList)) {
      return new ArrayList<>();
    }
    Example example = new Example(CaseRepayment.class);
    example.and().andEqualTo("orgId", orgId);
    example.and().andIn("outSerialNo", outSerialNoList);
    example.and().andEqualTo("applyStatus", RepaymentEnums.ApplyStatus.IMPORT.getCode());
    List<CaseRepayment> repayments = repaymentMapper.selectByExample(example);
    return repayments;
  }

  public  void verifyTime() {
    long currentTime = System.currentTimeMillis();
    long startTime = DateUtils.getStartDateTime(new Date()).getTime();
    long endTime = DateUtils.addHour(DateUtils.getStartDateTime(new Date()), 4).getTime();
    if (currentTime >= startTime && currentTime <= endTime) {
      throw new ApiException("0点-4点为系统维护，暂不支持该操作");
    }
  }

  /**
   * 查询所有甲方委外公司下还款案件id列表
   *
   * @return 案件id列表
   */
  public List<Long> queryAllBankAgentCaseIds() {
    return repaymentMapper.queryAllBankAgentCaseIds(null);
  }

  /**
   * 查询对应案件还款统计
   *
   * @param caseIds 案件id
   * @return 还款信息
   */
  public List<CaseRepaymentStats> queryDepCaseRepayStats(List<Long> caseIds) {
    return repaymentMapper.queryCaseRepaymentStatsList(caseIds);
  }

  /**
   * 查询对应案件最新还款日期
   *
   * @param caseIds 案件id
   * @return 还款日期
   */
  public List<CaseRepaymentStats> queryLastestRepayTime(List<Long> caseIds) {
    return repaymentMapper.queryLastestRepayTime(caseIds);
  }

  /**
   * 我的审批列表
   *
   * @param param      参数
   * @param orgId      组织id
   * @param userId     用户id
   * @param formFields 表单字段
   * @return {@link List}<{@link ApproveListVO}>
   */
    public List<ApproveListVO> approveList(ApproveListParam param, Long orgId, Long userId, String formFields) {
      String applyTimeStartStr = null;
      String applyTimeEndStr = null;
      String applyTime = param.getApplyTime();
      if (StrUtil.isNotBlank(applyTime)) {
        String[] range = applyTime.split(",");
        if (range.length == 2) {
          applyTimeStartStr = DateUtils.convertDate(range[0]);
          applyTimeEndStr = DateUtils.convertDate(range[1]);
        }
      }
      return repaymentMapper.approveList(param, orgId, userId, applyTimeStartStr, applyTimeEndStr, formFields);
    }

  /**
   * 审批待处理统计
   *
   * @param orgId        组织id
   * @param userId       用户id
   * @param businessType 业务类型
   * @return {@link Integer}
   */
  public Integer todoStatistics(Long orgId, Long userId, FlowEnums.BusinessType businessType) {
    return repaymentMapper.todoStatistics(orgId,userId,businessType.getCode());
  }

  /**
   * 超时记录ID
   *
   * @param startTime    开始时间
   * @param endTime      结束时间
   * @param businessType 业务类型
   * @return {@link List}<{@link Long}>
   */
  public List<Long> timeOutRecordIds(Date startTime, Date endTime, FlowEnums.BusinessType businessType) {
    Example example = new Example(CaseRepayment.class);
    example.selectProperties("id");
    Example.Criteria criteria = example.and();
    criteria.andEqualTo("applyStatus", RepaymentEnums.ApplyStatus.APPLYING.getCode())
            .andIsNotNull("outTime")
            .andGreaterThanOrEqualTo("outTime",startTime)
            .andLessThan("outTime",endTime);
    if (ObjectUtil.equals(businessType,FlowEnums.BusinessType.HUANK)){
      criteria.andEqualTo("type", 0);
    } else {
      criteria.andEqualTo("type", 1);
    }

    List<CaseRepayment> recordes = selectByExample(example);
    List<Long> timeOutRecordIds = recordes.stream().map(CaseRepayment::getId).collect(Collectors.toList());
    return timeOutRecordIds;
  }

  /**
   * 状态更新为超时
   *
   * @param applyIds 申请id
   */
  public void updateApplyStatusWithTimeOut(List<Long> applyIds) {
    if (ObjectUtil.isEmpty(applyIds)){
      return;
    }
    Example example = new Example(CaseRepayment.class);
    Example.Criteria criteria = example.and();
    criteria.andIn("id", applyIds)
            .andEqualTo("applyStatus", RepaymentEnums.ApplyStatus.APPLYING.getCode());

    CaseRepayment update = new CaseRepayment();
    update.setApplyStatus(RepaymentEnums.ApplyStatus.TIMEOUT.getCode());
    update.setUpdateTime(new Date());

    updateByExampleSelective(update,example);
  }
}
