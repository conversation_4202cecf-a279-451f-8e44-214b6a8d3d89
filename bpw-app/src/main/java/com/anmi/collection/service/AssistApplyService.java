package com.anmi.collection.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.AssistApplyEnums;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.flow.ApplyEnums;
import com.anmi.collection.common.enums.flow.FlowEnums;
import com.anmi.collection.common.enums.flow.FlowHandleRecordEnums;
import com.anmi.collection.encrypt.EncryptProperties;
import com.anmi.collection.encrypt.EncryptService;
import com.anmi.collection.entity.requset.cases.AssistApplyAddParam;
import com.anmi.collection.entity.requset.cases.AssistApplyAuditParam;
import com.anmi.collection.entity.requset.cases.AssistApplyQueryParam;
import com.anmi.collection.entity.requset.cases.AssistApplyReplyParam;
import com.anmi.collection.entity.requset.flow.ApproveListParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.AssistApplyVO;
import com.anmi.collection.entity.response.flow.ApproveListVO;
import com.anmi.collection.entity.response.flow.FlowNodeVO;
import com.anmi.collection.entity.response.flow.FlowVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.AssistApplyMapper;
import com.anmi.collection.service.flow.FlowService;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.collection.utils.dict.ZipUtil;
import com.anmi.domain.cases.AssistApply;
import com.anmi.domain.cases.AssistApplyInfo;
import com.anmi.domain.cases.Case;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AssistApplyService extends BaseService<AssistApply> {

    @Resource private CaseService caseService;
    @Resource private AssistApplyMapper assistApplyMapper;
    @Resource private UserService userService;
    @Resource private DeltService deltService;
    @Resource private ProductService productService;
    @Resource private OutBatchService outBatchService;
    @Resource private DepTeamService depTeamService;
    @Resource private EncryptProperties encryptProperties;
    @Resource private EncryptService encryptService;
    @Resource private FlowService flowService;

    @Transactional(rollbackFor = Exception.class)
    public void add(AssistApplyAddParam assistApplyAddParam, MultipartFile[] files) throws Exception {
        String content = assistApplyAddParam.getContent();
        if (StringUtils.isBlank(content)) {
            throw new ApiException("协办内容不能为空");
        }
        if (content.length() > 500) {
            throw new ApiException("协办内容不能超过500字");
        }
        Long caseId = assistApplyAddParam.getCaseId();
        Case acase = caseService.selectByPrimaryKey(caseId);
        if (acase == null) {
            throw new ApiException("案件不存在");
        }
        UserSession loginUser = UserUtils.getTokenUser();
        FlowEnums.AgentType agentType = ObjectUtil.equals(acase.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?FlowEnums.AgentType.OUTER:FlowEnums.AgentType.INNER;
        // 获取审批流
        FlowVO flowVO = flowService.flowChoice(loginUser.getOrgId(), agentType, FlowEnums.BusinessType.XB);
        // 申请是否自动通过
        Boolean autoPassApply = flowService.autoPassApply(loginUser,flowVO);
        AssistApply assistApply = cn.hutool.core.bean.BeanUtil.copyProperties(assistApplyAddParam, AssistApply.class);
        Date applyTime = new Date();
        if (files != null && files.length >= 1) {
            String fileName = "协办申请附件" + "-申请时间" + DateUtils.format(applyTime, DateUtils.FULL_MINUTE_FORMAT).replace(" ", "-")
                    + "-" + StringUtils.getRandomNumberBIT6() + ".zip";
            String url = ZipUtil.uploadZipFile(files, fileName, systemConfig.getCaseFilesBucket(), systemConfig.getCaseFilePath(), 10 * 1024 * 1024L);
            assistApply.setContentFileName(fileName);
            assistApply.setContentUrl(url);
        }
        // 插入
        assistApply.setCaseId(caseId);
        assistApply.setContent(content);
        assistApply.setApplyBy(loginUser.getId());
        assistApply.setApplyTime(applyTime);
        assistApply.setCreateTime(new Date());
        assistApply.setUpdateTime(new Date());
        assistApply.setFlowId(flowVO.getId());
        assistApply.setAgentType(ObjectUtil.equals(acase.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())?ApplyEnums.AgentType.OUTER.getCode():ApplyEnums.AgentType.INNER.getCode());
        if (autoPassApply){
            assistApply.setStatus(AssistApplyEnums.Status.REPLY_ING.getCode());
            assistApply.setAuditTime(new Date());
            assistApply.setAuditDesc("自动通过");
        }
        Integer timeLimitSwitch = flowVO.getTimeLimitSwitch();
        if (ObjectUtil.equals(timeLimitSwitch, FlowEnums.TimeLimitSwitch.YES.getCode())){
            String timeLimit = flowVO.getTimeLimit();
            String hour = StrUtil.subBefore(timeLimit, ":", true);
            String minute = StrUtil.subAfter(timeLimit, ":", true);
            Date outTime = DateUtil.offsetMinute(DateUtil.offsetHour(new Date(),Integer.valueOf(hour)),Integer.valueOf(minute));
            assistApply.setOutTime(outTime);
        }
        insertSelective(assistApply);
        List<FlowNodeVO> nodes = flowVO.getNodes();
        Map<String,Object> applyMap = cn.hutool.core.bean.BeanUtil.beanToMap(assistApply);
        String parentUniqueCode = flowService.getApplyOrRuleNodeUniqueCode(nodes,applyMap,FlowEnums.BusinessType.XB,agentType,loginUser);
        if (autoPassApply){
            flowService.findNextCopyNodeAndExecute(parentUniqueCode,flowVO, assistApply.getId(),loginUser);
        } else {
            flowService.findNextNodeAndExecute(parentUniqueCode,flowVO, assistApply.getId(),loginUser, loginUser.getId());
        }
    }

    public PageOutput list(AssistApplyQueryParam query) throws Exception {
        if (encryptProperties.getEnable()) {
            List<String> nameList = query.getNameList();
            if (!CollectionUtils.isEmpty(nameList)) {
                query.setNameList(nameList.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
            }
        }
        PageParam pageParam = BeanUtil.copyProperties(query, PageParam.class);
        Map<String, Object> params = new HashMap<>();
        UserSession userSession = getTokenUser();
        if (query.getAction() == null) {
            throw new ApiException("action不能为空");
        }
        if (query.getAction() == 0) {
            //催员单个案件协办申请界面：待审核、待回复、已回复、审核未通过
            Long caseId = query.getCaseId();
            if (caseId == null) {
                throw new ApiException("案件编号不能为空");
            }
            params.put("caseId", caseId);
        } else if (query.getAction() == 1) {
            //协办申请：待审核、待回复、已回复、审核未通过
            if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
                params.put("depId", userSession.getDepId());
            } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
                params.put("teamId", userSession.getTeamId());
            }
        } else if (query.getAction() == 2) {
            //协办处理：待回复，已回复
            List<Integer> statusList = Lists.newArrayList(AssistApplyEnums.Status.REPLY_ING.getCode(), AssistApplyEnums.Status.REPLY_FINISH.getCode());
            params.put("statuses", StringUtils.join(statusList, ","));
            if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
                params.put("depId", userSession.getDepId());
            } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
                params.put("teamId", userSession.getTeamId());
            }
        } else if (query.getAction() == 3) {
            //催员所有案件申请列表
            params.put("applyBy", userSession.getId());
        } else {
            throw new ApiException("action值不对");
        }
        //参数转换
        params.putAll(JsonUtils.fromJson(JsonUtils.toJson(query), Map.class));
        params.put("orgId", getTokenUser().getOrgId());
        if (query.getApplyTimeRange() != null) {
            String applyTimeStart = query.getApplyTimeRange().split(",")[0];
            String applyTimeEnd = query.getApplyTimeRange().split(",")[1];
            params.put("applyTimeStart", DateUtils.getStartTimeOfDate(new Date(Long.valueOf(applyTimeStart))));
            params.put("applyTimeEnd", DateUtils.getEndTimeOfDay(new Date(Long.valueOf(applyTimeEnd))));
        }
        Page page = super.setPage(pageParam);
        List<AssistApplyInfo> assistApplyInfoList = assistApplyMapper.selectAssistApplyList(params);
        List<AssistApplyVO> assistApplyVOList = new ArrayList<>();
        Map<Long,String> outBatchMap = outBatchService.getNames();
        Map<Long,String> depTeamMap = depTeamService.getNames();
        if (!CollectionUtils.isEmpty(assistApplyInfoList)) {
            for (AssistApplyInfo assistApplyInfo : assistApplyInfoList) {
                AssistApplyVO vo = new AssistApplyVO();
                BeanUtils.copyProperties(assistApplyInfo, vo);
                vo.setApplyByName(userService.getNames(userSession.getOrgId()).get(assistApplyInfo.getApplyBy()));
                vo.setAuditByName(assistApplyInfo.getAuditBy() == null ? null : userService.getNames(userSession.getOrgId()).get(assistApplyInfo.getAuditBy()));
                vo.setReplyByName(userService.getNames(userSession.getOrgId()).get(assistApplyInfo.getReplyBy()));
                vo.setOrgDeltName(deltService.getNames().get(assistApplyInfo.getOrgDeltId()));
                vo.setProductName(productService.getNames().get(assistApplyInfo.getProductId()));
                vo.setUserName(userService.getNames(userSession.getOrgId()).get(assistApplyInfo.getUserId()));
                vo.setDepName(depTeamMap.getOrDefault(vo.getDepId(), null));
                vo.setTeamName(depTeamMap.getOrDefault(vo.getTeamId(), null));
                vo.setOutBatchNo(outBatchMap.getOrDefault(vo.getOutBatchId(), null));
                //案件状态
                assistApplyVOList.add(vo);
            }
        }
        PageOutput pageOutput = new PageOutput(page.getPageNum(), page.getPageSize(),
                page != null ? (int) page.getTotal() : assistApplyVOList.size(),
                assistApplyVOList);
        return pageOutput;
    }

    @Transactional(rollbackFor = Exception.class)
    public void audit(AssistApplyAuditParam param) throws Exception{
        UserSession userSession = UserUtils.getTokenUser();
        List<Long> applyIds = param.getIdList();
        AssertUtil.notEmpty(applyIds,"请选择申请");
        AssertUtil.isTrue(applyIds.size()==1,"暂不支持批量审批");

        Long applyId = applyIds.get(0);
        AssistApply applyRecord = selectByPrimaryKey(applyId);
        AssertUtil.notNull(applyRecord, "未发现申请");
        AssertUtil.isTrue(ObjectUtil.equals(applyRecord.getStatus(), AssistApplyEnums.Status.CHECK_ING.getCode()), "申请非审批中");
        AssertUtil.notNull(applyRecord.getFlowId(), "审批流异常");

        Long flowId = applyRecord.getFlowId();
        Long applyUserId = applyRecord.getApplyBy();

        FlowHandleRecordEnums.HandleStatus approveStatus = param.getIsPassed()?FlowHandleRecordEnums.HandleStatus.PASS:FlowHandleRecordEnums.HandleStatus.REFUSE;
        flowService.execApprove(applyId,flowId,approveStatus,param.getDesc(),userSession,applyUserId);
    }

    /**
     * 更新申请 处理状态
     *
     * @param applyId       申请id
     * @param approveStatus 审批状态
     * @param approveUserId 更新人
     * @throws Exception 例外
     */
    public void updateHandleStatus(Long applyId, FlowHandleRecordEnums.HandleStatus approveStatus,Long approveUserId) throws Exception{
        Integer status = ObjectUtil.equals(approveStatus,FlowHandleRecordEnums.HandleStatus.PASS)?AssistApplyEnums.Status.REPLY_ING.getCode():AssistApplyEnums.Status.CHECK_REFUSE.getCode();
        AssistApply applyRecord = new AssistApply();
        applyRecord.setId(applyId);
        applyRecord.setStatus(status);
        applyRecord.setAuditBy(approveUserId);
        applyRecord.setUpdateTime(new Date());
        applyRecord.setAuditTime(new Date());
        updateByPrimaryKeySelective(applyRecord);
    }

    public void reply(AssistApplyReplyParam param, MultipartFile[] files) {
        AssistApply assistApply = selectByPrimaryKey(param.getId());
        if (assistApply == null) {
            throw new ApiException("申请不存在");
        }
        if (assistApply.getStatus() != AssistApplyEnums.Status.REPLY_ING.getCode()) {
            throw new ApiException("只能回复待回复状态的申请");
        }
        String desc = param.getDesc();
        if (StringUtils.isBlank(desc)) {
            throw new ApiException("回复内容不能为空");
        }
        if (desc.length() > 500) {
            throw new ApiException("回复内容不能超过500字");
        }
        UserSession userSession = getTokenUser();
        Date replyTime = new Date();
        if (files != null && files.length >= 1) {
            String fileName = "协办回复附件-" + "-申请时间" + DateUtils.format(assistApply.getApplyTime(), DateUtils.FULL_MINUTE_FORMAT).replace(" ", "-")
                    + "-" + StringUtils.getRandomNumberBIT6() + ".zip";
            String url = ZipUtil.uploadZipFile(files, fileName, systemConfig.getCaseFilesBucket(), systemConfig.getCaseFilePath(), 10 * 1024 * 1024L);
            assistApply.setReplyFileName(fileName);
            assistApply.setReplyUrl(url);
        }
        assistApply.setReplyBy(userSession.getId());
        assistApply.setReplyDesc(desc);
        assistApply.setReplyTime(replyTime);
        assistApply.setStatus(AssistApplyEnums.Status.REPLY_FINISH.getCode());
        assistApply.setUpdateTime(new Date());
        updateByPrimaryKeySelective(assistApply);
    }

    /**
     * 案件彻底物理删除，级联删除未审核、未回复的协办
     *
     * @param caseIds 案件ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void delAssistApply(List<Long> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }
        //根据案件id删除未审核、未回复的协办
        ArrayList<Integer> statusList = Lists.newArrayList(AssistApplyEnums.Status.CHECK_ING.getCode(), AssistApplyEnums.Status.REPLY_ING.getCode());
        Example example = new Example(AssistApply.class);
        example.and().andIn("caseId", caseIds);
        example.and().andIn("status", statusList);
        int i = assistApplyMapper.deleteByExample(example);
    }

    /**
     * 我的审批列表
     *
     * @param param      参数
     * @param orgId      组织id
     * @param userId     用户id
     * @param formFields 表单字段
     * @return {@link List}<{@link ApproveListVO}>
     */
    public List<ApproveListVO> approveList(ApproveListParam param, Long orgId, Long userId, String formFields) {
        String applyTimeStartStr = null;
        String applyTimeEndStr = null;
        String applyTime = param.getApplyTime();
        if (StrUtil.isNotBlank(applyTime)) {
            String[] range = applyTime.split(",");
            if (range.length == 2) {
                applyTimeStartStr = DateUtils.convertDate(range[0]);
                applyTimeEndStr = DateUtils.convertDate(range[1]);
            }
        }
        return assistApplyMapper.approveList(param, orgId, userId, applyTimeStartStr, applyTimeEndStr, formFields);
    }

    /**
     * 审批待处理统计
     *
     * @param orgId        组织id
     * @param userId       用户id
     * @param businessType 业务类型
     * @return {@link Integer}
     */
    public Integer todoStatistics(Long orgId, Long userId, FlowEnums.BusinessType businessType) {
        return assistApplyMapper.todoStatistics(orgId,userId,businessType.getCode());
    }

    /**
     * 超时记录ID
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link Long}>
     */
    public List<Long> timeOutRecordIds(Date startTime, Date endTime) {
        Example example = new Example(AssistApply.class);
        example.selectProperties("id");
        Example.Criteria criteria = example.and();
        criteria.andEqualTo("status", AssistApplyEnums.Status.CHECK_ING.getCode())
                .andIsNotNull("outTime")
                .andGreaterThanOrEqualTo("outTime",startTime)
                .andLessThan("outTime",endTime);

        List<AssistApply> recordes = selectByExample(example);
        List<Long> timeOutRecordIds = recordes.stream().map(AssistApply::getId).collect(Collectors.toList());
        return timeOutRecordIds;
    }

    /**
     * 状态更新为超时
     *
     * @param applyIds 申请id
     */
    public void updateStatusWithTimeOut(List<Long> applyIds) {
        if (ObjectUtil.isEmpty(applyIds)){
            return;
        }
        Example example = new Example(AssistApply.class);
        Example.Criteria criteria = example.and();
        criteria.andIn("id", applyIds)
                .andEqualTo("status",AssistApplyEnums.Status.CHECK_ING.getCode());

        AssistApply update = new AssistApply();
        update.setStatus(AssistApplyEnums.Status.TIMEOUT.getCode());
        update.setUpdateTime(new Date());

        updateByExampleSelective(update,example);
    }
}
