package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.SpaceEnums;
import com.anmi.collection.dto.SpaceRecordDTO;
import com.anmi.collection.dto.admin.AdminDataDTO;
import com.anmi.collection.entity.requset.space.SpaceRecordAddParam;
import com.anmi.collection.entity.requset.space.SpaceRecordAdminParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.space.SpaceRecordVO;
import com.anmi.collection.mapper.SpaceRecordMapper;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.space.SpaceRecord;
import com.github.pagehelper.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SpaceRecordService extends BaseService<SpaceRecord> {
    @Resource
    private SpaceRecordMapper spaceRecordMapper;
    @Resource
    private OrgSpaceService orgSpaceService;

    /**
     * 管理员分页获取购买存储空间记录
     * @param param
     * @return
     */
    public PageOutput<SpaceRecordVO> getSpaceRecordList(SpaceRecordAdminParam param) throws Exception {
        PageParam pageParam = new PageParam();
        pageParam.setPage(param.getPage());
        pageParam.setLimit(param.getLimit());
        Page page = super.setPage(pageParam);
        Example example = new Example(SpaceRecord.class);
        example.and().andEqualTo("orgId", param.getOrgId());
        example.orderBy("id").desc();
        List<SpaceRecord> spaceRecords = spaceRecordMapper.selectByExample(example);
        List<SpaceRecordVO> spaceRecordVOS = BeanUtil.copyPropertiesFromList(spaceRecords, SpaceRecordVO.class);
        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), spaceRecordVOS);
    }

    /**
     * 用户分页获取购买存储空间记录
     * @param param
     * @return
     * @throws Exception
     */
    public PageOutput<SpaceRecordVO> querySpaceRecord(PageParam param) throws Exception{
        UserSession userSession = UserUtils.getTokenUser();
        Page page = super.setPage(param);
        Example example = new Example(SpaceRecord.class);
        example.and().andEqualTo("orgId", userSession.getOrgId());
        example.orderBy("id").desc();
        List<SpaceRecord> spaceRecords = spaceRecordMapper.selectByExample(example);
        List<SpaceRecordVO> spaceRecordVOS = BeanUtil.copyPropertiesFromList(spaceRecords, SpaceRecordVO.class);
        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), spaceRecordVOS);
    }


    /**
     * 购买存储空间
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void addSpaceRecord(SpaceRecordAddParam param) {
        AdminDataDTO adminToken = UserUtils.getAdminToken();
        SpaceRecord spaceRecord = new SpaceRecord();
        BeanUtils.copyProperties(param,spaceRecord);
        Date expireTime = DateUtils.addYears(param.getEffectiveTime(), param.getDuration());
        spaceRecord.setExpireTime(expireTime);
        spaceRecord.setCreateTime(new Date());
        spaceRecord.setUpdateTime(new Date());
        spaceRecord.setCreateBy(adminToken.getStaffSimpleInfo().getId());
        // 查看设置的生效日期是否为当前日期，若为当前日期或当前日期前则设置存储空间启用(1)，非当前日期则设置存储空间未启用(2)
        if (DateUtils.getTodayDateFormat().getTime() >= param.getEffectiveTime().getTime()) {
            spaceRecord.setStatus(SpaceEnums.Status.INUSE.getCode());
            Integer size = param.getSize();
            BigDecimal purchase = new BigDecimal(size * 1024 * 1024);
            orgSpaceService.addTotalSpace(param.getOrgId(), purchase);
        } else {
            spaceRecord.setStatus(SpaceEnums.Status.NOTUSED.getCode());
        }
        spaceRecordMapper.insertSelective(spaceRecord);
    }

    /**
     * 查询过期记录
     * @return
     */
    public List<SpaceRecordDTO> queryExpireSpaceBySevenDaysSpace(List<Long> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Collections.emptyList();
        }
        orgIds = orgIds.stream().distinct().collect(Collectors.toList());
        List<SpaceRecordDTO> spaceRecords = spaceRecordMapper.queryExpireSpaceBySevenDaysSpace(orgIds);
        return spaceRecords;
    }

    /**
     * 查询所有未启用的购买记录
     * @return
     */
    public List<SpaceRecord> queryNotUsedRecord() {
        Example example = new Example(SpaceRecord.class);
        example.and().andEqualTo("status", SpaceEnums.Status.NOTUSED.getCode());
        example.and().andLessThanOrEqualTo("effectiveTime", DateUtils.getTodayDateFormat());
        return spaceRecordMapper.selectByExample(example);
    }

    /**
     * 查询所有已启用 生效时间已过期的购买记录
     * @return
     */
    public List<SpaceRecord> queryExpireRecord() {
        Example example = new Example(SpaceRecord.class);
        example.and().andEqualTo("status", SpaceEnums.Status.INUSE.getCode());
        example.and().andLessThan("expireTime", DateUtils.getTodayDateFormat());
        return spaceRecordMapper.selectByExample(example);
    }

}
