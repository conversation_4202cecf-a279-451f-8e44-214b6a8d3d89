package com.anmi.collection.service.lawsuit;

import com.anmi.collection.entity.requset.lawsuit.LawsuitProcessParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.lawsuit.LawsuitProcessVO;
import com.anmi.collection.mapper.lawsuit.LawsuitProcessMapper;
import com.anmi.collection.service.UserService;
import com.anmi.collection.utils.AuthBeanUtils;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.lawsuit.Lawsuit;
import com.anmi.domain.lawsuit.LawsuitProcess;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/26 11:48
 */
@Service
public class LawsuitProcessService {
    @Resource
    private LawsuitProcessMapper lawsuitProcessMapper;
    @Resource
    private UserService userService;
    @Resource
    private LawsuitConfigService lawsuitConfigService;
    @Resource
    private LawsuitService lawsuitService;

    @Transactional(rollbackFor = Exception.class)
    public void addLawsuitProcess(LawsuitProcessParam param, UserSession userSession) {
        LawsuitProcess lawsuitProcess = AuthBeanUtils.copy(param, LawsuitProcess.class);
        lawsuitProcess.setOrgId(userSession.getOrgId());
        lawsuitProcess.setOperator(userSession.getId());
        lawsuitProcess.setCreateTime(new Date());
        lawsuitProcess.setUpdateTime(new Date());
        lawsuitProcessMapper.insertSelective(lawsuitProcess);
        Lawsuit lawsuit = new Lawsuit();
        lawsuit.setId(param.getLawsuitId());
        lawsuit.setProcess(param.getProcessId());
        lawsuitService.updateLawsuit(lawsuit);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addLawsuitProcess(List<Long> lawsuitIds, Long processId, UserSession userSession) {
        if (CollectionUtils.isEmpty(lawsuitIds)) {
            return;
        }
        List<LawsuitProcess> lawsuitProcessList = new ArrayList<>();
        lawsuitIds.forEach(lawsuitId ->{
            LawsuitProcess lawsuitProcess = new LawsuitProcess();
            lawsuitProcess.setLawsuitId(lawsuitId);
            lawsuitProcess.setProcessId(processId);
            lawsuitProcess.setOrgId(userSession.getOrgId());
            lawsuitProcess.setOperator(userSession.getId());
            lawsuitProcess.setCreateTime(new Date());
            lawsuitProcess.setUpdateTime(new Date());
            lawsuitProcessList.add(lawsuitProcess);
        });
        lawsuitProcessMapper.insertList(lawsuitProcessList);

    }


    public List<LawsuitProcessVO> getList(Long lawsuitId) {
        Example example = new Example(LawsuitProcess.class);
        example.and().andEqualTo("lawsuitId", lawsuitId);
        List<LawsuitProcess> lawsuitProcessList = lawsuitProcessMapper.selectByExample(example);
        return toLawsuitProcessVOList(lawsuitProcessList);
    }

    List<LawsuitProcessVO> toLawsuitProcessVOList(List<LawsuitProcess> lawsuitProcessList) {
        List<LawsuitProcessVO> lawsuitProcessVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(lawsuitProcessList)) {
            return lawsuitProcessVOList;
        }
        Set<Long> processIds = lawsuitProcessList.parallelStream().filter(lawsuitProcess -> Objects.nonNull(lawsuitProcess.
                getProcessId())).map(LawsuitProcess::getProcessId).collect(Collectors.toSet());
        Set<Long> operators = lawsuitProcessList.parallelStream().filter(lawsuitProcess -> Objects.nonNull(lawsuitProcess.
                getOperator())).map(LawsuitProcess::getOperator).collect(Collectors.toSet());
        Map<Long, String> userMap = userService.getUserMap(new ArrayList<>(operators));
        Map<Long, String> processMap = lawsuitConfigService.selectConfigMap(new ArrayList<>(processIds));
        lawsuitProcessList.forEach(lawsuitProcess -> {
            LawsuitProcessVO lawsuitProcessVO = AuthBeanUtils.copy(lawsuitProcess, LawsuitProcessVO.class);
            lawsuitProcessVO.setOperatorName(userMap.get(lawsuitProcess.getOperator()));
            lawsuitProcessVO.setProcessName(processMap.get(lawsuitProcess.getProcessId()));
            lawsuitProcessVOList.add(lawsuitProcessVO);
        });
        return lawsuitProcessVOList;
    }


}
