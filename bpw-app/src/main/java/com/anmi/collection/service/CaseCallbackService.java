package com.anmi.collection.service;

import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.enums.CaseOperationEnums;
import com.anmi.collection.constant.CaseCallbackConstant;
import com.anmi.collection.entity.requset.cases.CaseCallbackParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.CaseCallbackVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.CaseCallbackMapper;
import com.anmi.collection.service.wp.OrgWorkPhoneService;
import com.anmi.collection.service.wp.UserWorkPhoneService;
import com.anmi.collection.utils.AnmiBeanutils;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.CaseCallback;
import com.anmi.domain.user.Contacts;
import com.anmi.domain.user.OrgWorkPhone;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class CaseCallbackService {

  @Autowired private CaseCallbackMapper caseCallbackMapper;
  @Resource
  private ContactsService contactsService;
  @Resource
  private OrgWorkPhoneService orgWorkPhoneService;
  @Resource
  private UserWorkPhoneService userWorkPhoneService;

  public List<CaseCallbackVO> selectByVisitId(Long visitId) throws Exception {
    List<CaseCallbackVO> list = new ArrayList<>();
    // 外访附件
    List<CaseCallback> caseCallbackList = caseCallbackMapper.selectByVisitId(visitId);
    if (!CollectionUtils.isEmpty(caseCallbackList)) {
      for (CaseCallback caseCallback : caseCallbackList) {
        CaseCallbackVO caseCallbackVO = BeanUtil.copyProperties(caseCallback, CaseCallbackVO.class);
        caseCallbackVO.setCallTime(caseCallback.getCallTime().getTime());
        list.add(caseCallbackVO);
      }
    }
    return list;
  }

  public Boolean isExist(String callUuid) {
    Integer count = caseCallbackMapper.selectCountByUuid(callUuid);
    if (count > 0) {
      return true;
    }
    return false;
  }

  /**
   * 这里我声明一下：caseCallback是外访的催记记录， caseOperation是电催的催记记录
   * 外访通话是通过cti拨打的，所以会在度言回调接口处判断是不是外访的催记记录进行插入，所以之前是没有新增外访催记记录的接口的
   * 详情看{@link CaseOperationService #handleVisitCallback()}
   *
   * 这里提供新增接口是为了支持保存工作手机拨打的通话记录
   *
   * @param param
   */
  @Transactional(rollbackFor = Exception.class)
  public void addCaseCallback(CaseCallbackParam param) {
    UserSession userSession = UserUtils.getTokenUser();
    Long contactsId = param.getContactsId();
    Contacts contacts = contactsService.selectByPrimaryKey(contactsId);
    CaseCallback caseCallback = AnmiBeanutils.copy(param, CaseCallback.class);
    caseCallback.setOrgId(userSession.getOrgId());
    caseCallback.setConMobile(contacts.getMobile());
    caseCallback.setConName(contacts.getName());
    caseCallback.setRelationType(contacts.getRelationType());
    caseCallback.setOperatorId(userSession.getId());
    caseCallback.setOperatorName(userSession.getName());
    caseCallback.setStatus((byte) 0);
    caseCallback.setCallDuration(param.getCallDuration() == null ? 0 : param.getCallDuration());
    caseCallback.setRingDuration(param.getRingDuration() == null ? 0 : param.getRingDuration());
    caseCallback.setOutcome(param.getOutcome() == null ? "FAIL" : param.getOutcome());
    if (Objects.equals(param.getType(), CaseCallbackConstant.TYPE_WORK_PHONE)) {
      OrgWorkPhone orgWorkPhone = orgWorkPhoneService.getOrgWorkPhone(userSession.getOrgId());
      if (orgWorkPhone == null) {
        throw new ApiException("无法提交催记，请联系管理员开通工作手机");
      }
      if (StringUtils.isNotBlank(param.getIccId()) && StringUtils.isBlank(param.getCaller())) {
        String number = userWorkPhoneService.getPhoneNumberByIccId(param.getIccId(), userSession.getOrgId());
        caseCallback.setCaller(number);
      }
    }
    caseCallback.setCreateBy(userSession.getId());
    caseCallback.setUpdateBy(userSession.getId());
    caseCallback.setCreateTime(new Date());
    caseCallback.setUpdateTime(new Date());
    caseCallbackMapper.insertSelective(caseCallback);
  }

}
