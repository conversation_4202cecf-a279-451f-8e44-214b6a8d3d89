package com.anmi.collection.service.external;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.dto.CaseContact;
import com.anmi.collection.manager.finance360.Finance360Manager;
import com.anmi.collection.manager.finance360.resp.Finance360Resp;
import com.anmi.collection.service.CustomFieldService;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.sys.CustomField;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class Finance360Strategy extends AbstractExternalStrategy{
    @Resource
    private Finance360Manager finance360Manager;
    @Resource
    private CustomFieldService customFieldService;

    private final static String PRODUCT_TYPE = "产品类型";

    @Override
    public String thirdParty() {
        return "finance360";
    }

    /**
     * 是否是可执行案件
     *
     * @param caseList  案件信息
     * @return boolean  是否可执行
     */
    @Override
    public List<Case> filter(List<Case> caseList, Map<Integer, List<CaseContact>> filterCaseContactsMap) {
        Long orgId = caseList.get(0).getOrgId();
        if (!checkSwitch(orgId)) {
            return Collections.emptyList();
        }
        // 去除自定义字段名
        String productTypeValue = null;
        Map<String, List<CustomField>> productTypeInfo = customFieldService.selectByNames(orgId, Collections.singletonList(PRODUCT_TYPE));
        if (!CollectionUtil.isEmpty(productTypeInfo) && productTypeInfo.containsKey(PRODUCT_TYPE)) {
            List<CustomField> customFields = productTypeInfo.get(PRODUCT_TYPE);
            if (!CollectionUtils.isEmpty(customFields)) {
                productTypeValue = customFields.get(0).getValue();
            }
        }

        if (StringUtils.isBlank(productTypeValue)) {
            return Collections.emptyList();
        }

        List<Case> overDueCaseList = new ArrayList<>();
        for (Case aCase : caseList) {
            if (!remoteQueryOverDueInfo(aCase, productTypeValue)) {
                overDueCaseList.add(aCase);
            }
        }
        return overDueCaseList;
    }

    public Boolean remoteQueryOverDueInfo (Case caseInfo, String productTypeValue) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("customerAccount", caseInfo.getOutSerialTemp());
        if (!CollectionUtil.isEmpty(caseInfo.getFieldJson()) && caseInfo.getFieldJson().containsKey(productTypeValue)) {
            map.put("productType", caseInfo.getFieldJson().get(productTypeValue));
        }
        try {
            Finance360Resp<Boolean> resp = finance360Manager.queryCustomerOverdueStatus(map);
            if (resp == null || !resp.success() || resp.getData() == null) {
                return Boolean.TRUE;
            }
            return resp.getData();
        } catch (Exception e) {
            log.error("调用360金融查询逾期信息：", e);
        }
        return true;
    }



}
