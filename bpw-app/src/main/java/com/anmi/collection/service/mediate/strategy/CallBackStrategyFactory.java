package com.anmi.collection.service.mediate.strategy;

import com.anmi.collection.exception.ApiException;
import com.anmi.collection.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CallBackStrategyFactory {
    @Autowired
    private List<CallBackStrategy> list;
    @Autowired
    private CommonStrategy commonStrategy;

    public CallBackStrategy getStrategy(String action) {
        if (StringUtils.isBlank(action)) {
            throw new ApiException("回调参数action参数不存在！");
        }
        CallBackStrategy cb = null;
        for (CallBackStrategy c : list) {
            if (action.equals(c.getAction())) {
                cb = c;
                break;
            }
        }
        // 交给公共处理
        if (null == cb) {
            //throw new ApiException("action为{}策略不存在！", action);
            cb = commonStrategy;
        }
        return cb;
    }

}
