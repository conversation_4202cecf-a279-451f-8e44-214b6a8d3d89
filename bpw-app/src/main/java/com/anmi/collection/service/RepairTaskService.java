package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.RepairInfoEnums;
import com.anmi.collection.common.enums.RepairTaskEnums;
import com.anmi.collection.entity.requset.sys.user.RepairQueryParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.repair.RepTaskInfoListVO;
import com.anmi.collection.entity.response.repair.RepTaskInfoVO;
import com.anmi.collection.entity.response.repair.RepairVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.RepairTaskMapper;
import com.anmi.collection.utils.CommonUtil;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.user.Company;
import com.anmi.domain.user.RepairInfo;
import com.anmi.domain.user.RepairTask;
import com.github.pagehelper.Page;
import org.omg.CORBA.INTERNAL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by jianghp on 2019-04-17.
 */

@Service
public class RepairTaskService extends BaseService<RepairTask> {

    @Autowired
    private RepairInfoService repairInfoService;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private RepairTaskMapper repairTaskMapper;

    public RepTaskInfoListVO getRecordList(RepairQueryParam param, PageParam pageParam) {
        UserSession session = UserUtils.getTokenUser();
        Long orgId = session.getOrgId();
        Company company = companyService.selectByPrimaryKey(orgId);
        // 公司信修单价
        BigDecimal amount = company.getRepairAmount();
        Example taskQuery = new Example(RepairTask.class);
        Example.Criteria taskCrt = taskQuery.createCriteria();
        taskCrt.andEqualTo("state", param.getState());
        taskCrt.andEqualTo("orgId", orgId);
        taskCrt.andEqualTo("status", RepairTaskEnums.Status.NORMAL.getCode());
        Page page = super.setPage(pageParam);
        List<RepairTask> locTasks = selectByExample(taskQuery);
        List<RepTaskInfoVO> taskInfoVOS = new ArrayList<>();
        RepairInfo infoQuery = new RepairInfo();
        locTasks.stream().forEach(locTask -> {
            RepTaskInfoVO vo = new RepTaskInfoVO();
            vo.setCreateTime(locTask.getCreateTime().getTime());
            vo.setState(locTask.getState());
            vo.setTaskName(locTask.getName());
            infoQuery.setTaskId(locTask.getDuyanTaskId());
            List<RepairInfo> infos = repairInfoService.select(infoQuery);
            Map<List<RepairVO>, List<Integer>> map = execNums(infos);
            if ((int) locTask.getState() == RepairTaskEnums.State.FINISHED.getCode()) {
                map.entrySet().stream().forEach(entry -> {
                    BigDecimal count = new BigDecimal(entry.getKey().size());
                    vo.setRepairVOList(entry.getKey());
                    vo.setRepairAmount(new BigDecimal(String.valueOf(count.multiply(amount))));
                    vo.setSuccess(entry.getKey().size());
                    vo.setOther(0);
                });
            } else {
                // 没有全部完成
                // 设置成功数和未成功数
                map.entrySet().stream().forEach(entry -> {
                    vo.setRepairVOList(entry.getKey());
                    vo.setSuccess(entry.getValue().get(0));
                    vo.setOther(entry.getValue().get(1));
                });
            }
            taskInfoVOS.add(vo);
        });
        RepTaskInfoListVO taskInfoListVO = new RepTaskInfoListVO(page.getPageNum(),
                page.getPageSize(), (int) page.getTotal(), taskInfoVOS);
        return taskInfoListVO;
    }

    /**
     * 计算出某个任务下信修成功数和未成功数以及信修详情
     *
     * @param infos
     * @return
     */
    private Map<List<RepairVO>, List<Integer>> execNums(List<RepairInfo> infos) {
        List<RepairVO> vos = new ArrayList<>(infos.size());
        List<Integer> nums = new ArrayList<>(2);
        nums.add(0);
        nums.add(0);
        infos.stream().forEach(info -> {
            if (info.getStatus() == RepairInfoEnums.Status.REPAIR_SUCCESS.getCode()) {
                int suc = nums.get(0).intValue();
                suc++;
                nums.set(0, suc);
            } else {
                int oth = nums.get(1).intValue();
                oth++;
                nums.set(1, oth);
            }
            RepairVO vo = convertToVO(info);
            vos.add(vo);
        });
        Map<List<RepairVO>, List<Integer>> map = new HashMap<>();
        map.put(vos, nums);
        return map;
    }

    private RepairVO convertToVO(RepairInfo info) {
        RepairVO vo = new RepairVO();
        vo.setName(info.getName());
        vo.setMobile(info.getMobile());
        vo.setStatus(info.getStatus());
        vo.setIdCard(info.getIdCard());
        return vo;
    }

    @Transactional
    public void updateBatch(List<RepairTask> locTasks) {
        if (CommonUtil.isEmpty(locTasks)) {
            return;
        }
        int res = repairTaskMapper.updateBatch(locTasks);
        if (res != 1) {
            throw new ApiException("批量更新信修任务失败！");
        }
    }
}
