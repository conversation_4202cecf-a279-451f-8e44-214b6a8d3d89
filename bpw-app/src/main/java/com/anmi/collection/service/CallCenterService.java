package com.anmi.collection.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.RoleEnums;
import com.anmi.collection.entity.requset.duyan.inspector.InspectorRule;
import com.anmi.collection.entity.requset.sys.user.*;
import com.anmi.collection.entity.response.callcenter.CallCenterUserVO;
import com.anmi.collection.entity.response.callcenter.InspectorVO;
import com.anmi.collection.entity.response.duyan.inspector.InspectorRuleVO;
import com.anmi.collection.entity.response.sys.user.UserBatchResultVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.mapper.InspectorsMapper;
import com.anmi.collection.mapper.RoleMapper;
import com.anmi.collection.mapper.UserMapper;
import com.anmi.collection.mapper.VisitMapper;
import com.anmi.collection.utils.CmUtil;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.user.*;
import com.anmi.domain.visit.Visit;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CallCenterService {

  @Autowired private UserMapper userMapper;
  @Autowired private UserService userService;
  @Autowired private CompanyService companyService;
  @Autowired private DuyanManager duyanManager;
  @Autowired private DepTeamService depTeamService;
  @Autowired private RoleService roleService;
  @Autowired private InspectorsMapper inspectorsMapper;
  @Autowired private RoleMapper roleMapper;
  @Autowired private InspectorsService inspectorsService;
  @Autowired private ContactsService contactsService;
  @Autowired private VisitMapper visitMapper;
  @Autowired private CaseService caseService;
  @Autowired private I18nService i18nService;

  public PageOutput<CallCenterUserVO> queryCallList(
      CallCenterQueryParam callCenterParam, PageParam pageParam, boolean isSeat) {
    UserSession userSession = UserUtils.getTokenUser();
    Map map = new HashMap();
    if (!StringUtils.isEmpty(callCenterParam.getKeyWord())) {
      map.put("keyWord", callCenterParam.getKeyWord().trim());
    }
    map.put("status", User.Status.NORMAL.getCode());
    map.put("isSeat", isSeat ? 1 : 0);
    if (isSeat) {
      if (callCenterParam.getIsSync() == 0) {
        map.put("duyanSeat", User.DuyanSeat.NO.getCode());
      } else {
        map.put("duyanSeat", User.DuyanSeat.YES.getCode());
      }
    } else {
      if (callCenterParam.getIsSync() == 0) {
        map.put("duyanAdmin", User.DuyanSeat.NO.getCode());
      } else {
        map.put("duyanAdmin", User.DuyanSeat.YES.getCode());
      }
    }
    map.put("orgId", userService.getOrgId());
    map.put("teamIds", callCenterParam.getTeamIds());
    if (UserUtils.likeBranchAdmin()) {
      map.put("depId", userService.getTokenUser().getDepId());
    } else if (UserUtils.likeTeamLeader()) {
      map.put("teamIds", String.valueOf(userService.getTokenUser().getTeamId()));
    }
    Page page = userService.setPage(pageParam);
    List<CallCenterUserVO> vos = userMapper.findCallCenter(map);
    setTeamLeaderParam(vos);
    PageOutput<CallCenterUserVO> pageOutput =
        new PageOutput(
            page.getPageNum(),
            page.getPageSize(),
            page != null ? (int) page.getTotal() : vos.size(),
            vos);
    setDepTeams(pageOutput.getList());
    return pageOutput;
  }

  private List<CallCenterUserVO> findCallCenter(
      CallCenterQueryParam callCenterParam, boolean isSeat) {
    Map map = new HashMap();
    if (!StringUtils.isEmpty(callCenterParam.getKeyWord())) {
      map.put("keyWord", callCenterParam.getKeyWord().trim());
    }
    map.put("status", User.Status.NORMAL.getCode());
    map.put("isSeat", isSeat ? 1 : 0);
    if (isSeat) {
      if (callCenterParam.getIsSync() == 0) {
        map.put("duyanSeat", User.DuyanSeat.NO.getCode());
      } else {
        map.put("duyanSeat", User.DuyanSeat.YES.getCode());
      }
    } else {
      if (callCenterParam.getIsSync() == 0) {
        map.put("duyanAdmin", User.DuyanSeat.NO.getCode());
      } else {
        map.put("duyanAdmin", User.DuyanSeat.YES.getCode());
      }
    }
    map.put("orgId", userService.getOrgId());
    map.put("teamIds", callCenterParam.getTeamIds());
    if (UserUtils.likeBranchAdmin()) {
      map.put("depId", userService.getTokenUser().getDepId());
    } else if (UserUtils.likeTeamLeader()) {
      map.put("teamIds", String.valueOf(userService.getTokenUser().getTeamId()));
    }
    List<CallCenterUserVO> vos = userMapper.findCallCenter(map);
    return vos;
  }

  public Map<String, Long> queryCallSize(CallCenterQueryParam callCenterParam, boolean isSeat) {
    List<CallCenterUserVO> vos = findCallCenter(callCenterParam, isSeat);
    Long duyanTeamLeaderNum =
        vos.stream()
            .filter(c -> c.getDuyanTeamLeader().equals(User.DuyanTeamLeader.YES.getCode()))
            .count();
    Long duyanFifoAgentNum =
        vos.stream()
            .filter(c -> c.getDuyanFifoAgent().equals(User.DuyanFifoAgent.YES.getCode()))
            .count();
    Map<String, Long> map = new HashMap<>();
    map.put("duyanTeamLeaderNum", duyanTeamLeaderNum);
    map.put("fifoAgentNum", duyanFifoAgentNum);
    return map;
  }

  private void setTeamLeaderParam(List<CallCenterUserVO> vos) {
    if (CommonUtils.isEmpty(vos)) {
      return;
    }
    List<Long> userIds = vos.stream().map(CallCenterUserVO::getId).collect(Collectors.toList());
    List<Long> teamLeaderIds = userMapper.findExistTeamLeader(userIds);
    for (CallCenterUserVO vo : vos) {
      if (teamLeaderIds.contains(vo.getId())) {
        vo.setAnmiTeamLeader(1);
      } else {
        vo.setAnmiTeamLeader(0);
      }
    }
  }

  private void setDepTeams(List<CallCenterUserVO> vos) {
    DepTeam depTeam = null;
    for (CallCenterUserVO vo : vos) {
      Long depId = vo.getDepId();
      Long teamId = vo.getTeamId();
      if (depId != null && depId > 0) {
        vo.setDepName(depTeamService.getNames().get(depId));
      }
      if (teamId != null && teamId > 0) {
        vo.setTeamName(depTeamService.getNames().get(teamId));
      }
    }
  }

  /**
   * 将员工同步到度言
   *
   * @param param
   */
  public UserBatchResultVO syncToDuyan(SyncDYParam param) {
    checkSyncType(param.getSyncType());
    List<Long> userIds = param.getUserIds();
    Map<String, Integer> seatNumMap = getSeatNum();
    if (param.getSyncType() == 0) {
      if (seatNumMap.get("agentLimit") == -1) {
        throw new ApiException("请先购买坐席！");
      }
      if (seatNumMap.get("agentLimit") != 0
          && (getUseSeatNum() + userIds.size()) > seatNumMap.get("agentLimit")) {
        throw new ApiException("设置坐席已达上限,请联系客服经理购买！");
      }
    } else if (param.getSyncType() == 3) {
      if (seatNumMap.get("fifoAgentLimit") != 0
          && (getUseFifoAgentNum() + userIds.size()) > seatNumMap.get("fifoAgentLimit")) {
        throw new ApiException("设置预测式外呼坐席已达上限，请联系客服经理购买！");
      }
    }
    UserBatchResultVO resultVO = new UserBatchResultVO();
    // 设置度言管理员
    Company company = companyService.selectByPrimaryKey(userService.getOrgId());
    if (company == null || company.getDuyanReferId() == null) {
      return resultVO;
    }
    int success = 0;
    int fail = 0;
    List<Long> mobileNullUserIds = new ArrayList<>();
    List<Long> failUserIds = new ArrayList<>();
    List<String> failMessages = new ArrayList<>();
    for (Long userId : param.getUserIds()) {
      try {
        if (!userService.syncToDuyan(userId, param.getSyncType(), company.getDuyanReferId())) {
          // 手机号为空
          fail++;
          mobileNullUserIds.add(userId);
          failUserIds.add(userId);
          continue;
        } else {
          success++;
          // 添加坐席，token失效
          if (param.getSyncType() == 0) {
            UserUtils.setTokenIsUpdate(userId, 1);
          }
        }
      } catch (Exception e) {
        log.error("添加度言角色失败", e);
        fail++;
        failUserIds.add(userId);
        failMessages.add(e.getMessage());
      }
    }
    resultVO.setFail(fail);
    resultVO.setSuccess(success);
    resultVO.setMobileNullUserIds(mobileNullUserIds);
    resultVO.setFailUserIds(failUserIds);
    resultVO.setFailMessages(failMessages);
    return resultVO;
  }

  /**
   * 从度言移除催员或者管理员
   *
   * @param param
   */
  // @Transactional
  public UserBatchResultVO removeDY(SyncDYParam param) {
    checkSyncType(param.getSyncType());
    List<Long> userIds = param.getUserIds();
    // 设置度言管理员
    Company company = companyService.selectByPrimaryKey(userService.getOrgId());
    int success = 0;
    int fail = 0;
    List<Long> failUserIds = new ArrayList<>();
    List<String> failMessages = new ArrayList<>();
    if (company != null && company.getDuyanReferId() != null) {
      for (Long userId : userIds) {
        try {
          userService.removeDY(userId, param.getSyncType(), company.getDuyanReferId());
          success++;
          // 移除坐席token失效
          if (param.getSyncType() == 0) {
            UserUtils.setTokenIsUpdate(userId, 0);
          }
        } catch (Exception e) {
          log.error("删除度言角色失败,userId:{}", userId, e);
          fail++;
          failUserIds.add(userId);
          failMessages.add(userId + ":" + e.getMessage());
        }
      }
    }
    return new UserBatchResultVO(success, fail, failUserIds, failMessages);
  }

  private void checkSyncType(Integer syncType) {
    if (syncType != 0 && syncType != 1 && syncType != 2 && syncType != 3) {
      throw new ApiException("找不到需要同步的类型！");
    }
  }

  /**
   * 获取坐席上限
   *
   * @return
   */
  public Map<String, Integer> getSeatNum() {
    Company company = companyService.selectByPrimaryKey(userService.getOrgId());
    Long referId = company.getDuyanReferId();
    return duyanManager.getSeatNum(referId);
  }

  /**
   * 获取总公司使用坐席数
   *
   * @return
   */
  public Integer getUseSeatNum() {
    Example userExp = new Example(User.class);
    Example.Criteria userCrt = userExp.createCriteria();
    userCrt.andEqualTo("orgId", userService.getOrgId());
    userCrt.andEqualTo("duyanSeat", User.DuyanSeat.YES.getCode());
    userCrt.andEqualTo("status", User.Status.NORMAL.getCode());
    int count = userService.selectCountByExample(userExp);
    return count;
  }

  public Integer getUseFifoAgentNum() {
    Example userExp = new Example(User.class);
    Example.Criteria userCrt = userExp.createCriteria();
    userCrt.andEqualTo("orgId", userService.getOrgId());
    userCrt.andEqualTo("duyanFifoAgent", User.DuyanFifoAgent.YES.getCode());
    userCrt.andEqualTo("status", User.Status.NORMAL.getCode());
    int count = userService.selectCountByExample(userExp);
    return count;
  }

  /**
   * 查找所有的质检员
   *
   * @param pageParam
   * @return
   */
  public List<InspectorVO> inspectors(PageParam pageParam) {
    Map query = new HashMap();
    UserSession loginUser = UserUtils.getTokenUser();
    query.put("orgId", loginUser.getOrgId());
    if (UserUtils.likeTeamLeader(loginUser.getRoleId())) {
      query.put("teamId", loginUser.getTeamId());
    }
    if (UserUtils.likeBranchAdmin(loginUser.getRoleId())) {
      query.put("depId", loginUser.getDepId());
    }
    // 按照要求质检员列表接口不分页。
//    Page page = userService.setPage(pageParam);
    List<InspectorVO> inspectorVOS = userMapper.queryInspectors(query);
    // 去获取每个质检员的对应的质检小组们
    for (InspectorVO vo : inspectorVOS) {
      List<Long> teamIds = getInspectorTeam(vo);
      vo.setCheckTeamIds(teamIds);
    }
    return inspectorVOS;
  }

  private List<Long> getInspectorTeam(InspectorVO vo) {
    Inspectors query = new Inspectors();
    query.setInspectorId(vo.getId());
    List<Inspectors> inspectors = inspectorsMapper.select(query);
    if (CommonUtils.isEmpty(inspectors)) {
      return new ArrayList<>();
    }
    List<Long> checkTeamIds =
        inspectors.stream().map(Inspectors::getTargetId).collect(Collectors.toList());
    return checkTeamIds;
  }

  @Transactional(rollbackFor = Exception.class)
  public void setInspectors(InspectorsSetParam inspectorsSetParam) {
    UserSession userSession = UserUtils.getTokenUser();
    Long userId = inspectorsSetParam.getUserId();
    User updateUser = userMapper.selectForUpdate(inspectorsSetParam.getUserId());
    if (updateUser == null
        || User.Status.DELETE.getCode() == updateUser.getStatus()
        || !updateUser.getOrgId().equals(userSession.getOrgId())) {
      throw new ApiException("用户不存在");
    }
    List<Role> roleList = roleMapper.selectRolesByUserId(userId);
    List<Long> roleIdList = roleList.stream().map(r -> r.getId()).collect(Collectors.toList());
    // 校验质检员参数
    List<Long> checkTeamIdList = CmUtil.convertToList(inspectorsSetParam.getCheckTeamIds());
    List<Long> duyanInspectTeamIds =
        userService.getDuyanInspectTeamIds(checkTeamIdList, roleIdList);
    // 更新关联关系
    inspectorsService.updateInspectorsRels(inspectorsSetParam.getUserId(), checkTeamIdList);
    // 更新用户信息
    updateUser.setDuyanInspectorSup(inspectorsSetParam.getDuyanInspectorSup());
    updateUser.setDuyanInspector((byte) 1);
    updateUser.setUpdateBy(userSession.getId());
    updateUser.setUpdateTime(new Date());
    userMapper.updateByPrimaryKey(updateUser);
    // 调用度言接口
    Long duyanTeamId = null;
    if (updateUser.getTeamId() != null) {
      DepTeam depTeam = depTeamService.selectByPrimaryKey(updateUser.getTeamId());
      duyanTeamId = depTeam.getDuyanReferId();
    }
    duyanManager.updateDuyanUserAndRole(
        updateUser, duyanTeamId, duyanInspectTeamIds, userSession.getId());
  }

  public void setInspection(InspectorRule inspectorRule) {
    checkInspectorRule(inspectorRule);
    UserSession loginUser = UserUtils.getTokenUser();
    Long orgId = loginUser.getOrgId();
    Company company = companyService.selectByPrimaryKey(orgId);
    Long duyanReferId = company.getDuyanReferId();
    Integer type = inspectorRule.getType();
    Map<Long, Integer> targetMap = inspectorRule.getTargetMap();
    Map<Long, Integer> duyanTargetMap = new HashMap<>();
    switch (type) {
      case 1:
        // 总公司
        for (Map.Entry<Long, Integer> entry : targetMap.entrySet()) {
          duyanTargetMap.put(duyanReferId, entry.getValue());
        }
        break;
      case 2:
        // 团队
        for (Map.Entry<Long, Integer> entry : targetMap.entrySet()) {
          Long teamDuyanReferId =
              depTeamService.selectByPrimaryKey(entry.getKey()).getDuyanReferId();
          duyanTargetMap.put(teamDuyanReferId, entry.getValue());
        }
        break;
      case 3:
        // 催员
        for (Map.Entry<Long, Integer> entry : targetMap.entrySet()) {
          Long accountId = userService.selectByPrimaryKey(entry.getKey()).getDuyanAccountId();
          duyanTargetMap.put(accountId, entry.getValue());
        }
        break;
      default:
        throw new ApiException("不支持的质检范围: " + type);
    }
    duyanManager.setInspection(
        duyanReferId,
        type,
        inspectorRule.getDurationStart(),
        inspectorRule.getDurationEnd(),
        duyanTargetMap);
  }

  private void checkInspectorRule(InspectorRule inspectorRule) {
    if (inspectorRule.getDurationStart() < 0 || inspectorRule.getDurationEnd() < 0) {
      throw new ApiException("通话时长不能小于0！");
    }
    if (inspectorRule.getDurationStart() > inspectorRule.getDurationEnd()) {
      throw new ApiException("通话最小时长不能大于最大时长！");
    }
  }

  public InspectorRuleVO getInspection() {
    UserSession loginUser = UserUtils.getTokenUser();
    Long orgId = loginUser.getOrgId();
    Company company = companyService.selectByPrimaryKey(orgId);
    Long duyanReferId = company.getDuyanReferId();
    JSONObject jsonObject = duyanManager.getInspection(duyanReferId);
    InspectorRuleVO vo = new InspectorRuleVO();
    if (jsonObject == null) {
      return vo;
    }
    vo.setType(jsonObject.getIntValue("type"));
    vo.setDurationStart(jsonObject.getIntValue("durationStart"));
    vo.setDurationEnd(jsonObject.getIntValue("durationEnd"));
    JSONArray jsonArray = (JSONArray) jsonObject.get("targetList");
    Map map = new HashMap();
    switch (jsonObject.getIntValue("type")) {
      case 1:
        // 总公司
        Company query = new Company();
        for (int i = 0; i < jsonArray.size(); i++) {
          Long targetId = Long.valueOf((Integer) jsonArray.getJSONObject(i).get("targetId"));
          query.setDuyanReferId(targetId);
          Company company1 = companyService.selectOne(query);
          map.put(company1.getId(), jsonArray.getJSONObject(i).get("rate"));
        }
        break;
      case 2:
        // 团队
        DepTeam depTeamQuery = new DepTeam();
        for (int i = 0; i < jsonArray.size(); i++) {
          Long targetId = Long.valueOf((Integer) jsonArray.getJSONObject(i).get("targetId"));
          depTeamQuery.setDuyanReferId(targetId);
          DepTeam depTeam = depTeamService.selectOne(depTeamQuery);
          map.put(depTeam.getId(), jsonArray.getJSONObject(i).get("rate"));
        }
        break;
      case 3:
        // 催员
        User userQuery = new User();
        for (int i = 0; i < jsonArray.size(); i++) {
          Long targetId = Long.valueOf((Integer) jsonArray.getJSONObject(i).get("targetId"));
          userQuery.setDuyanAccountId(targetId);
          User user = userService.selectOne(userQuery);
          map.put(user.getId(), jsonArray.getJSONObject(i).get("rate"));
        }
        break;
    }
    vo.setTargetMap(map);
    return vo;
  }

  /**
   * 批量发送短信密码到员工手里
   *
   * @param batchSendSmsParam
   */
  public UserBatchResultVO batchSendPwdSms(BatchSendSmsParam batchSendSmsParam) {
    List<SendSmsParam> userInfos = batchSendSmsParam.getUserInfos();
    if (CommonUtils.isEmpty(userInfos)) {
      return new UserBatchResultVO();
    }
    List<String> mobiles =
        userInfos.stream().parallel().map(SendSmsParam::getMobile).collect(Collectors.toList());
    Map query = new HashMap();
    query.put("mobiles", mobiles);
    String existMobile = userMapper.checkExistMobilesByOne(query);
    if (!StringUtils.isBlank(existMobile)) {
      throw new ApiException("手机号【" + existMobile + "】已存在");
    }
    UserSession userSession = userService.getTokenUser();
    Company company = companyService.selectByPrimaryKey(userSession.getOrgId());
    Integer success = 0;
    Integer fail = 0;
    List<Long> failUserIds = new ArrayList<>();
    List<String> failMessages = new ArrayList<>();
    for (SendSmsParam param : userInfos) {
      try {
        userService.addUserMobile(
            param.getUserId(),
            param.getMobile(),
            batchSendSmsParam.getType(),
            company.getDuyanReferId(),
            userSession.getOrgId());
        success++;
      } catch (Exception e) {
        log.error("设置手机号并同步为度言角色失败userId:{}", param.getUserId(), e);
        fail++;
        failUserIds.add(param.getUserId());
        failMessages.add(e.getMessage());
      }
    }
    UserBatchResultVO vo = new UserBatchResultVO(success, fail, failUserIds, failMessages);
    return vo;
  }

  public Map<Long, String> selectRoleNameMap(List<Long> userIds, List<Integer> types,String language) {
    Map<String, Object> params = new HashMap<>();
    params.put("userIds", userIds);
    params.put("types", types);
    List<Map<String, Object>> roleNames = userMapper.selectUserRoleNames(params);
    Set<Long> roleIds = new HashSet<>();
    for(Map<String, Object> map : roleNames){
      Long roleId =  map.get("roleId") == null ? null : Long.valueOf(map.get("roleId").toString());
      roleIds.add(roleId);
    }
    Map<Long, String> roleNameMap = new HashMap<>();
    if (roleIds.size() < 1) {
      return new HashMap<>();
    }
    List<Role> roleList = roleService.selectByIdList(new ArrayList<>(roleIds));
    i18nService.convertRole(roleList,language);
    //List<Role> roleListI18n = i18nService.getI18nRole(language);
    Map<Long,Role> roleMapI18n = roleList.stream().collect(Collectors.toMap(Role::getId, Function.identity()));
    for (Map<String, Object> map : roleNames) {
      Long userId = map.get("userId") == null ? null : Long.valueOf(map.get("userId").toString());
      String roleName = map.get("roleName") == null ? null : map.get("roleName").toString();
      Long roleId =  map.get("roleId") == null ? null : Long.valueOf(map.get("roleId").toString());
      if (roleMapI18n.containsKey(roleId)) {
        roleName = roleMapI18n.get(roleId).getName();
      }
      if (userId == null || StringUtils.isBlank(roleName)) {
        continue;
      }
      if (roleNameMap.containsKey(userId)) {
        roleName = roleNameMap.get(userId).concat(",").concat(roleName);
      }
      roleNameMap.put(userId, roleName);
    }
    return roleNameMap;
  }

  public void mobileOnlineSwitch(Boolean isMobileOnline) {
    UserSession userSession = UserUtils.getTokenUser();
    Company company = companyService.selectByPrimaryKey(userSession.getOrgId());
    if (company == null || company.getDuyanReferId() == null) {
      throw new ApiException("未开通呼叫中心");
    }
    User user = userService.selectByPrimaryKey(userSession.getId());
    if (user == null || user.getDuyanAccountId() == null || user.getDuyanSeat() == 0) {
      throw new ApiException("您非坐席不可切手机在线状态");
    }
    duyanManager.mobileOnlineSwitch(
        company.getDuyanReferId(), user.getDuyanAccountId(), isMobileOnline);
  }

  public String registerMobile(String mobile, Long visitId) {
    UserSession userSession = UserUtils.getTokenUser();
    Company company = companyService.selectByPrimaryKey(userSession.getOrgId());
    if (company == null || company.getDuyanReferId() == null) {
      throw new ApiException("未开通呼叫中心");
    }
    User user = userService.selectByPrimaryKey(userSession.getId());
    if (user == null || user.getDuyanAccountId() == null || user.getDuyanSeat() == 0) {
      throw new ApiException("您非坐席不可使用该功能");
    }
    Visit visit = visitMapper.selectByPrimaryKey(visitId);
    if (visit == null) {
      throw new ApiException("外访不存在");
    }
    Case caseInfo = caseService.selectByPrimaryKey(visit.getCaseId());
    Contacts contacts = contactsService.selectByMobile(caseInfo, mobile);
    if (contacts == null) {
      throw new ApiException("联系人不存在");
    }
    String tag = "VISIT" + visitId + ";" + contacts.getId() + ";" + userSession.getId();
    return duyanManager.registerMobile(
        company.getDuyanReferId(), user.getDuyanAccountId(), contacts.getMobile(), tag);
  }
}
