package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.mapper.CtrlTypeRelMapper;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.domain.cases.CtrlTypeRel;
import com.anmi.domain.cases.CtrlTypeRelResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CtrlTypeRelService extends BaseService<CtrlTypeRel> {
    @Resource
    private CtrlTypeRelMapper ctrlTypeRelMapper;

    /**
     * 根据管控id查询对应小组关联配置
     *
     * @param ctrlIds 管控ids
     * @return
     */
    public List<CtrlTypeRelResult> queryCtrlTypeRelByTeam(List<Long> ctrlIds, List<Long> typeIds) {
        if (CollectionUtils.isEmpty(ctrlIds)) {
            return Collections.emptyList();
        }
       return ctrlTypeRelMapper.queryCtrlTypeRelByTeam(ctrlIds, typeIds);
    }

    /**
     * 根据管控id查询对应委案公司关联配置
     *
     * @param ctrlIds 管控ids
     * @return
     */
    public List<CtrlTypeRelResult> queryCtrlTypeRelByDelt(List<Long> ctrlIds, List<Long> typeIds) {
        if (CollectionUtils.isEmpty(ctrlIds)) {
            return Collections.emptyList();
        }
        return ctrlTypeRelMapper.queryCtrlTypeRelByDelt(ctrlIds, typeIds);
    }

    /**
     * 查询委案公司管控关联信息
     *
     * @param typeIds
     * @return
     */
    public List<CtrlTypeRelResult> queryCtrlByDeltType(List<Long> typeIds, Integer telEnable, Integer smsEnable) {
        if (CollectionUtils.isEmpty(typeIds)) {
            return Collections.emptyList();
        }
       return ctrlTypeRelMapper.queryCtrlByDeltType(typeIds, telEnable, smsEnable);
    }

    /**
     * 查询小组管控关联信息
     *
     * @param typeIds
     * @return
     */
    public List<CtrlTypeRelResult> queryCtrlByTeamType(List<Long> typeIds, Integer telEnable, Integer smsEnbale) {
        if (CollectionUtils.isEmpty(typeIds)) {
            return Collections.emptyList();
        }
        return ctrlTypeRelMapper.queryCtrlByTeamType(typeIds, telEnable, smsEnbale);
    }
}
