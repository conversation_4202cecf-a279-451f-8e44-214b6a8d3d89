package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.enums.OrgEnums;
import com.anmi.collection.mapper.CaseDebtorMapper;
import com.anmi.collection.utils.CmUtil;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.domain.cases.CaseDebtor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class CaseDebtorService extends BaseService<CaseDebtor> {

    @Autowired
    private CaseDebtorMapper caseDebtorMapper;

    // 有事务，则用当前事务
    @Transactional(rollbackFor = Exception.class)
    public void deleteDebtorsByDebtId(List<Long> debtIds) {
        if (CommonUtils.isEmpty(debtIds)) {
            return;
        }
        List<List<Long>> groupList = CmUtil.splitList(debtIds, 500);
        Example example = new Example(CaseDebtor.class);
        for (List<Long> subIds : groupList) {
            if (CommonUtils.isEmpty(subIds)) {
                continue;
            }
            example.clear();
            example.createCriteria().andIn("id", subIds);
            super.deleteByExample(example);
        }
    }

    public Map<String, Long> selectByUniqueCode(Long orgId, Set<String> uniqueCodes, Integer type) {
        Example debtorExp = new Example(CaseDebtor.class);
        debtorExp.createCriteria().
                andEqualTo("orgId", orgId).
                andIn("uniqueCode", uniqueCodes).
                andEqualTo("type", type);
        // 找到当前所有idCards中所有已存在的债务人
        List<CaseDebtor> caseDebtorList = caseDebtorMapper.selectByExample(debtorExp);
        if (CollectionUtils.isEmpty(caseDebtorList)) {
            return new HashMap<>();
        }
        return caseDebtorList.stream().collect(Collectors.toMap(CaseDebtor::getUniqueCode, c -> c.getId(), (oldValue, newValue) -> newValue));
    }
}
