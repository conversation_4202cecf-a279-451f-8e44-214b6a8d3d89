package com.anmi.collection.service.message.inner;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.NoticeEnums;
import com.anmi.collection.dto.message.InnerMessageDTO;
import com.anmi.collection.dto.message.InnerMessageSendDetailDTO;
import com.anmi.collection.entity.requset.message.InnerMessageDetailParam;
import com.anmi.collection.entity.requset.message.InnerMessageSendParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.message.InnerMessageDetailVO;
import com.anmi.collection.service.message.IMessageProvider;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.JSONUtil;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.message.InnerMessageSend;
import com.anmi.domain.message.InnerMessageSendDetail;
import com.github.pagehelper.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 站内信发送记录
 *
 * <AUTHOR>
 * @date 2023/11/27
 */
@Service
public class InnerMessageSendService extends BaseService<InnerMessageSend> {

    @Resource private InnerMessageSendDetailService innerMessageSendDetailService;
    @Resource private List<IMessageProvider>  messageProviders;

    /**
     * 站内信发送
     *
     * @param param 参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void send(InnerMessageSendParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        Long orgId = userSession.getOrgId();

        Long fromUserId = userSession.getId();
        String fromUserName = userSession.getName();
        List<InnerMessageSendParam.ToUser> toUsers = param.getToUsers().parallelStream().distinct().collect(Collectors.toList());

        Date now = new Date();
        InnerMessageSend sendRecord = new InnerMessageSend();
        sendRecord.setOrgId(orgId);
        sendRecord.setFromUserId(fromUserId);
        sendRecord.setFromUserName(fromUserName);
        sendRecord.setToUsers(JSONUtil.toJsonStr(toUsers));
        sendRecord.setSubject(StrUtil.isBlank(param.getSubject())?"(无主题)":param.getSubject());
        sendRecord.setContent(param.getContent());
        sendRecord.setSourceType(param.getSourceType());
        sendRecord.setCreateTime(now);
        insertSelective(sendRecord);

        Long sendId = sendRecord.getId();
        innerMessageSendDetailService.addSendDetail(orgId,toUsers,sendId);

        List<InnerMessageSendDetail> sendDetails = innerMessageSendDetailService.getList(sendId);

        List<InnerMessageSendDetailDTO> detailDTOS = BeanUtil.copyToList(sendDetails, InnerMessageSendDetailDTO.class);
        InnerMessageDTO innerMessageDTO = BeanUtil.copyProperties(sendRecord, InnerMessageDTO.class);
        innerMessageDTO.setSendDetailList(detailDTOS);
        messageProviders.parallelStream().forEach(t->{
            if(ObjectUtil.equals(t.getNoticeType(), NoticeEnums.Type.INNER_MESSAGE.getCode())){
                t.sendMsg(innerMessageDTO);
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void sendMessage(InnerMessageSendParam param, Long orgId) {
        List<InnerMessageSendParam.ToUser> toUsers = param.getToUsers().parallelStream().distinct().collect(Collectors.toList());
        Date now = new Date();
        InnerMessageSend sendRecord = new InnerMessageSend();
        sendRecord.setOrgId(orgId);
        sendRecord.setFromUserId(param.getFromUserId());
        sendRecord.setFromUserName(param.getFromUserName());
        sendRecord.setToUsers(JSONUtil.toJsonStr(toUsers));
        sendRecord.setSubject(StrUtil.isBlank(param.getSubject())?"(无主题)":param.getSubject());
        sendRecord.setContent(param.getContent());
        sendRecord.setSourceType(param.getSourceType());
        sendRecord.setCreateTime(now);
        insertSelective(sendRecord);

        Long sendId = sendRecord.getId();
        innerMessageSendDetailService.addSendDetail(orgId,toUsers,sendId);

        List<InnerMessageSendDetail> sendDetails = innerMessageSendDetailService.getList(sendId);

        List<InnerMessageSendDetailDTO> detailDTOS = BeanUtil.copyToList(sendDetails, InnerMessageSendDetailDTO.class);
        InnerMessageDTO innerMessageDTO = BeanUtil.copyProperties(sendRecord, InnerMessageDTO.class);
        innerMessageDTO.setSendDetailList(detailDTOS);
        messageProviders.parallelStream().forEach(t->{
            if(ObjectUtil.equals(t.getNoticeType(), NoticeEnums.Type.INNER_MESSAGE.getCode())){
                t.sendMsg(innerMessageDTO);
            }
        });
    }



    /**
     * 发送记录列表
     *
     * @param sendIds 发送id
     * @return {@link List}<{@link InnerMessageSend}>
     */
    public List<InnerMessageSend> getList(Collection<Long> sendIds) {
        if (ObjectUtil.isEmpty(sendIds)){
            return new ArrayList<>();
        }
        Example example = new Example(InnerMessageSend.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id",sendIds);
        return selectByExample(example);
    }

    /**
     * 发件列表
     *
     * @param param 参数
     * @return {@link PageOutput}<{@link InnerMessageDetailVO}>
     */
    public PageOutput<InnerMessageDetailVO> sendList(PageParam param) {
        UserSession userSession = UserUtils.getTokenUser();
        Long orgId = userSession.getOrgId();
        Long fromUserId = userSession.getId();

        PageParam pageParam = new PageParam(param.getPage(), param.getLimit());
        Page page = PageUtils.setPage(pageParam);
        Example example = new Example(InnerMessageSend.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orgId",orgId)
                .andEqualTo("fromUserId",fromUserId);
        List<InnerMessageSend> sendRecords = selectByExample(example);

        List<InnerMessageDetailVO> messageDetailVOS = sendRecords.parallelStream().map(sendRecord -> {
            InnerMessageDetailVO detailVO = new InnerMessageDetailVO();
            detailVO.setSendId(sendRecord.getId());
            detailVO.setFromUserName(sendRecord.getFromUserName());
            detailVO.setToUsers(sendRecord.getToUsers());
            detailVO.setSubject(sendRecord.getSubject());
            detailVO.setContent(sendRecord.getContent());
            detailVO.setCreateTime(sendRecord.getCreateTime());
            return detailVO;
        }).collect(Collectors.toList());

        return new PageOutput<>(page.getPageNum(), page.getPageSize(), (int)page.getTotal(), messageDetailVOS);
    }

    /**
     * 站内信详情
     *
     * @param param 参数
     * @return {@link InnerMessageDetailVO}
     */
    public InnerMessageDetailVO detail(InnerMessageDetailParam param) {
        InnerMessageSend sendRecord = selectByPrimaryKey(param.getSendId());
        AssertUtil.notNull(sendRecord,"未发现发送记录");
        InnerMessageDetailVO detailVO = new InnerMessageDetailVO();
        detailVO.setFromUserName(sendRecord.getFromUserName());
        detailVO.setToUsers(sendRecord.getToUsers());
        detailVO.setSubject(sendRecord.getSubject());
        detailVO.setContent(sendRecord.getContent());
        detailVO.setCreateTime(sendRecord.getCreateTime());

        Long sendDetailId = param.getSendDetailId();
        if (ObjectUtil.isNotNull(sendDetailId)){
            innerMessageSendDetailService.read(sendDetailId);
        }

        return detailVO;
    }
}
