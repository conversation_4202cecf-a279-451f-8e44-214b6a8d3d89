package com.anmi.collection.service;

import cn.hutool.core.util.StrUtil;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.requset.yunpian.MessageTemplateQueryFO;
import com.anmi.collection.entity.requset.yunpian.YunpianTemplateCreate;
import com.anmi.collection.entity.response.message.MessageTplDTO;
import com.anmi.collection.entity.response.yunpian.MessageTemplateVO;
import com.anmi.collection.entity.response.yunpian.YunpianTemplateVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.MessageManager;
import com.anmi.collection.manager.YunpianManager;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.yunpian.SmsSign;
import com.anmi.domain.yunpian.SmsTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by dongwang on 2019-01-15.
 */

@Service
@Slf4j
public class SmsTemplateService extends BaseService<SmsTemplate> {
    @Autowired
    private YunpianManager yunpianManager;
    @Autowired
    private UserService userService;
    @Autowired
    private MessageManager messageManager;

    public List<YunpianTemplateVO> getList(){
        List<YunpianTemplateVO> list = yunpianManager.list(null);
        List<YunpianTemplateVO> tmp=new ArrayList<>();
        //查询通用和总公司模版
        Example example=new Example(SmsTemplate.class);
        example.createCriteria().andEqualTo("orgId",UserUtils.getParentOrgId()).orEqualTo("type",
                "0");
        List<SmsTemplate> select = super.selectByExample(example);
        list.stream().forEach(l->{
            List<SmsTemplate> smsTemplates = select.stream().
                    filter(d -> d.getYunpianTplId().equals(l.getTpl_id())).collect(Collectors.toList());
            if(smsTemplates.size()==1){
                BeanUtils.copyProperties(smsTemplates.get(0),l);
                l.setName(smsTemplates.get(0).getName());
                l.setVarJson(smsTemplates.get(0).getVarJson());
                l.setId(smsTemplates.get(0).getId());
                l.setCreateBy(userService.getNames(userService.getOrgId()).get(l.getCreateBy()));
                l.setUpdateBy(userService.getNames(userService.getOrgId()).get(l.getUpdateBy()));
                tmp.add(l);
            }
        });
        return tmp;
    }

    public int insertSelective(SmsTemplate smsTemplate){
        YunpianTemplateVO tempLate = yunpianManager.createTempLate(smsTemplate);
        if(tempLate==null){
            throw new ApiException("添加失败");
        }
        smsTemplate.setYunpianTplId(tempLate.getTpl_id());

        return super.insertSelective(smsTemplate);
    }

    public int insertTemplate(YunpianTemplateCreate templateCreate){
        // SmsSign smsSign = smsSignService.selectByPrimaryKey(templateCreate.getSmsSignId());
        // templateCreate.setPlatformSignId(smsSign.getPlatformSignId());
        MessageTplDTO messageTplDTO = messageManager.addMessageTemplate(templateCreate);
        if (messageTplDTO == null){
            throw new ApiException("模板添加失败");
        }
        UserSession userSession = UserUtils.getTokenUser();
        SmsTemplate smsTemplate = new SmsTemplate();
        smsTemplate.setName(templateCreate.getName());
        smsTemplate.setContent(messageTplDTO.getContent()); // 从短信平台返回的模板内容
        smsTemplate.setPlatformSignId(templateCreate.getPlatformSignId());
        smsTemplate.setPlatformTplId(messageTplDTO.getId()); // 从短信平台返回的短信平台模板ID
        // smsTemplate.setSmsSignId(templateCreate.getSmsSignId());
        smsTemplate.setVarJson(templateCreate.getVarJson());
        smsTemplate.setOrgId(userSession.getOrgId());
        if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
            smsTemplate.setDepId(userSession.getDepId());
        } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
            smsTemplate.setTeamId(userSession.getTeamId());
        }
        smsTemplate.setCreateBy(userSession.getId());
        smsTemplate.setUpdateBy(userSession.getId());
        smsTemplate.setCreateTime(new Date());
        smsTemplate.setUpdateTime(new Date());
        smsTemplate.setState(messageTplDTO.getState());
        smsTemplate.setAuditDesc(messageTplDTO.getRejectReason());
        smsTemplate.setStatus(0);
        return super.insert(smsTemplate);
    }

    public int updateByPrimaryKeySelective(SmsTemplate smsTemplate){
        SmsTemplate sms = super.selectByPrimaryKey(smsTemplate.getId());
        if(sms==null){
            throw new ApiException("无法找到对应的模版");
        }
        smsTemplate.setYunpianTplId(sms.getYunpianTplId());
        YunpianTemplateVO yunpianTemplateVO = yunpianManager.updateTempLate(smsTemplate);
        if(yunpianTemplateVO==null){
            throw new ApiException("更新模版失败");
        }
        return super.updateByPrimaryKeySelective(smsTemplate);
    }

    public int deleteByTplId(SmsTemplate smsTemplate){
        SmsTemplate template = super.selectByPrimaryKey(smsTemplate.getId());
        if(template == null){
            throw new ApiException("无法找到对应的模版");
        }
        boolean isDeleted = messageManager.deleteMessageTemplate(template.getPlatformTplId());
        if (!isDeleted){
            throw new ApiException("删除模版失败");
        }
        template.setStatus(-1);
        return super.updateByPrimaryKeySelective(template);
    }

    public int updateByTplId(SmsTemplate smsTemplate){
        SmsTemplate template = super.selectByPrimaryKey(smsTemplate.getId());
        if(template==null){
            throw new ApiException("无法找到对应的模版");
        }
        template.setContent(smsTemplate.getContent());
        template.setVarJson(smsTemplate.getVarJson());
        template.setCreateTime(new Date());
        template.setUpdateTime(new Date());
        template.setAuditDesc("");
        MessageTplDTO messageTplDTO = messageManager.updateMessageTemplate(template);
        if (messageTplDTO == null){
            throw new ApiException("更新模版失败");
        }
        smsTemplate.setState(0L);
        smsTemplate.setContent(messageTplDTO.getContent());
        return super.updateByPrimaryKeySelective(smsTemplate);
    }


    public int deleteByPrimaryKey(Long id){
        SmsTemplate sms = super.selectByPrimaryKey(id);
        if(sms==null){
            throw new ApiException("无法找到对应的模版");
        }
        YunpianTemplateVO yunpianTemplateVO = yunpianManager.deleteTempLate(sms.getYunpianTplId());
        if(yunpianTemplateVO==null){
            throw new ApiException("更新模版失败");
        }
        return super.deleteByPrimaryKey(id);
    }

    public PageOutput<MessageTemplateVO> messageTemplateQueryList(MessageTemplateQueryFO fo, PageParam pageParam){
        UserSession userSession = getTokenUser();

        Example example = new Example(SmsTemplate.class);
        Example.Criteria planCrt = example.createCriteria();
        example.setOrderByClause("id desc");
        planCrt.andEqualTo("orgId", userSession.getOrgId());
//        if (UserUtils.likeBranchAdmin()) {
//            planCrt.andEqualTo("depId", userSession.getDepId());
//        }
//        if (UserUtils.likeTeamLeader()) {
//            planCrt.andEqualTo("teamId", userSession.getTeamId());
//        }
//         if (fo.getSmsSignId() != null){
//             planCrt.andEqualTo("smsSignId", fo.getSmsSignId());
//         }
        if (StrUtil.isNotBlank(fo.getSmsSignName())){
            planCrt.andEqualTo("name", fo.getSmsSignName());
        }
        if (fo.getState() != null){
            planCrt.andEqualTo("state", fo.getState());
        }
        planCrt.andEqualTo("status", 0);
        PageOutput pageOutput = super.selectByPage(example, pageParam);
        List<SmsTemplate> smsTemplateList = pageOutput.getList();
        if (CollectionUtils.isEmpty(smsTemplateList)) {
            return pageOutput;
        }
        List<MessageTemplateVO> vos = new ArrayList<>();
        // 过滤出待审核的数据
        List<Long> platformTplIds = smsTemplateList.stream().filter(temp -> temp.getState() == 0 || temp.getState() == 3).
                map(SmsTemplate::getPlatformTplId).collect(Collectors.toList());
        List<MessageTplDTO> messageTplDTOS = null;
        if (platformTplIds != null && platformTplIds.size() > 0){
            messageTplDTOS = messageManager.getTemplateListByIds(platformTplIds);
        }else{
            messageTplDTOS = new ArrayList<>();
        }
        Map<Long, MessageTplDTO> messageTplDTOMap = messageTplDTOS.stream().collect(Collectors.toMap(MessageTplDTO::getId, r -> r));
        for (SmsTemplate smsTemplate : smsTemplateList) {
            MessageTemplateVO vo = new MessageTemplateVO();
            MessageTplDTO tplDTO = messageTplDTOMap.get(smsTemplate.getPlatformTplId());
            if (tplDTO != null) {
                vo.setState(tplDTO.getState());
                judgeFinished(smsTemplate.getId(), tplDTO);
            } else {
                vo.setState(smsTemplate.getState());
            }
            vo.setCreateUserName(userService.getNames(userSession.getOrgId()).get(smsTemplate.getCreateBy()));
            vo.setName(smsTemplate.getName()); // 模板名称
            vo.setSignName(smsTemplate.getName());
            vo.setContent(smsTemplate.getContent());
            vo.setAuditDesc(smsTemplate.getAuditDesc());
            vo.setPlatformSignId(smsTemplate.getPlatformSignId());
            vo.setCreateTime(smsTemplate.getCreateTime());
            vo.setId(smsTemplate.getId());
            vos.add(vo);
        }
        pageOutput.setList(vos);
        return pageOutput;
    }

    private void judgeFinished(Long id, MessageTplDTO dto) {
        if (dto.getState() == 0){ // 如果审核状态为待审核，则不作更新
            return;
        }
        SmsTemplate smsTemplate = new SmsTemplate();
        smsTemplate.setId(id);
        smsTemplate.setState(dto.getState());
        smsTemplate.setAuditDesc(dto.getRejectReason());
        super.updateByPrimaryKeySelective(smsTemplate);
    }

    public List<SmsTemplate> queryTemplateSuccess(Long smsSignId){
        Example example = new Example(SmsTemplate.class);
        UserSession userSession = UserUtils.getTokenUser();
        Example.Criteria planCrt = example.createCriteria();
        planCrt.andEqualTo("orgId", userSession.getOrgId());
//        if (UserUtils.likeBranchAdmin()) {
//            planCrt.andEqualTo("depId", userSession.getDepId());
//        }
//        if (UserUtils.likeTeamLeader()) {
//            planCrt.andEqualTo("teamId", userSession.getTeamId());
//        }
        planCrt.andEqualTo("state",1).andEqualTo("status", 0);
        planCrt.andEqualTo("smsSignId", smsSignId);
        List<SmsTemplate> smsTemplateList = super.selectByExample(example);
        return smsTemplateList;
    }
}
