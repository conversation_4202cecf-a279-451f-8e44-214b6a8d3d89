package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.entity.requset.cases.BatchChangeStatusParam;
import com.anmi.collection.entity.requset.cases.InnerBatchQueryParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.InnerBatchVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.domain.cases.InnerBatch;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class InnerBatchService extends BaseService<InnerBatch> {

  @Cacheable(value = KeyCache.INNER_BATCH_NAME)
  public Map<Long, String> getNames() {
    List<InnerBatch> list = super.getNameIdMap("name");
    return list.stream().collect(Collectors.toMap(InnerBatch::getId, InnerBatch::getName));
  }

  @Override
  @CacheEvict(value = KeyCache.INNER_BATCH_NAME, allEntries = true)
  public int deleteByPrimaryKey(Object entity) {
    return super.deleteByPrimaryKey(entity);
  }

  @CacheEvict(value = KeyCache.INNER_BATCH_NAME, allEntries = true)
  public Long createInnerBatchNo(String name, Long orgId, Long createBy) {
    InnerBatch innerBatch = new InnerBatch();
    innerBatch.setName(name);
    innerBatch.setOrgId(orgId);
    innerBatch.setStatus(InnerBatch.Status.NORMAL.getCode());
    innerBatch.setCreateBy(createBy);
    innerBatch.setUpdateBy(createBy);
    super.insertSelective(innerBatch);
    return innerBatch.getId();
  }

  public PageOutput<InnerBatchVO> selectByOrgId(
      PageParam pageParam, InnerBatchQueryParam innerBatchQueryParam) throws Exception {
    UserSession userSession = getTokenUser();
    Example example = new Example(InnerBatch.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("orgId", userSession.getOrgId());
    if (innerBatchQueryParam.getStatus() != null) {
      criteria.andEqualTo("status", innerBatchQueryParam.getStatus());
    }
    if (StringUtils.isNotBlank(innerBatchQueryParam.getName())) {
      criteria.andLike("name", innerBatchQueryParam.getName().trim());
    }
    example.setOrderByClause("id desc");
    PageOutput pageOutput = selectByPage(example, pageParam);
    List<InnerBatch> list = pageOutput.getList();
    if (CollectionUtils.isEmpty(list)) {
      return pageOutput;
    }
    List<InnerBatchVO> innerBatchVOList = BeanUtil.copyPropertiesFromList(list, InnerBatchVO.class);
    pageOutput.setList(innerBatchVOList);
    return pageOutput;
  }

  public void changeStatus(BatchChangeStatusParam param) {
    if (param.getId() == null || param.getStatus() == null) {
      throw new ApiException("参数不能为空");
    }
    InnerBatch innerBatch = selectByPrimaryKey(param.getId());
    if (innerBatch == null) {
      throw new ApiException("内部批次号不存在");
    }
    if (param.getStatus().equals(InnerBatch.Status.NORMAL.getCode())
        && innerBatch.getStatus().equals(InnerBatch.Status.NORMAL.getCode())) {
      throw new ApiException("内部批次号已是正常状态");
    }
    if (param.getStatus().equals(InnerBatch.Status.DELETE.getCode())
        && innerBatch.getStatus().equals(InnerBatch.Status.DELETE.getCode())) {
      throw new ApiException("内部批次号已是删除状态");
    }
    InnerBatch domain = new InnerBatch();
    domain.setId(param.getId());
    domain.setStatus(param.getStatus());
    domain.setUpdateBy(getTokenUser().getId());
    domain.setUpdateTime(new Date());
    updateByPrimaryKeySelective(domain);
  }

  public Map<Long, String> getInnerBatchMap(List<Long> innerBatchIdList) {
    if (CollectionUtils.isEmpty(innerBatchIdList)) {
      return new HashMap<>();
    }
    Example example = new Example(InnerBatch.class);
    example.createCriteria().andIn("id", innerBatchIdList);
    List<InnerBatch> innerBatchList = super.selectByExample(example);
    if (CollectionUtils.isEmpty(innerBatchList)) {
      return new HashMap<>();
    }
    Map<Long, String> map =
        innerBatchList.stream().collect(Collectors.toMap(InnerBatch::getId, InnerBatch::getName));
    return map;
  }
}
