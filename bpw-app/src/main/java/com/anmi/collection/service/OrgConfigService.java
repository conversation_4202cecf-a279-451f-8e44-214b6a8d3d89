package com.anmi.collection.service;

import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.enums.CaseOperationEnums;
import com.anmi.collection.common.enums.CustomOperationStatusEnums;
import com.anmi.collection.common.enums.OrgConfigEnums;
import com.anmi.collection.entity.requset.sys.org.OrgConfigAddParam;
import com.anmi.collection.entity.requset.sys.org.OrgConfigEditParam;
import com.anmi.collection.entity.requset.sys.org.OrgConfigReorderParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.principal.DeltVO;
import com.anmi.collection.entity.response.sys.org.OrgConfigVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.OrgConfigMapper;
import com.anmi.collection.redis.RedisLock;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.user.CustomOperationStatus;
import com.anmi.domain.user.OrgConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrgConfigService {

  @Autowired private OrgConfigMapper orgConfigMapper;
  @Autowired private RedisLock redisLock;
  @Resource
  private DeltService deltService;

  public static String redisLockKey = "ORG_CONFIG_LOCK_";

  public List<OrgConfigVO> selectList(Byte type) throws Exception {
    UserSession userSession = UserUtils.getTokenUser();
    Long orgId = userSession.getOrgId();
    List<OrgConfig> configList = orgConfigMapper.selectByTypeAndCode(orgId, type, null);
    if (CollectionUtils.isEmpty(configList)) {
      configList = selectDefaultConfigList(type);
    }
    List<OrgConfigVO> orgConfigVOList = BeanUtil.copyPropertiesFromList(configList, OrgConfigVO.class);
    List<Long> orgConfigIds = orgConfigVOList.stream().filter(orgConfigVO -> orgConfigVO.getId() != null).map(OrgConfigVO::getId).collect(Collectors.toList());
    Map<Long, List<DeltVO>> map = deltService.getDeltListForOperationConfig(orgConfigIds, type.intValue());
    Integer deltCount = deltService.getDeltCount(orgId);
    orgConfigVOList.forEach(orgConfigVO -> {
      if (orgConfigVO.getId() != null) {
        List<DeltVO> deltVOS = map.get(orgConfigVO.getId());
        orgConfigVO.setDeltList(deltVOS);
        int size = deltVOS == null ? 0 : deltVOS.size();
        orgConfigVO.setIsAllDelt(size == deltCount ? 1 : 0);
      }
    });
    return orgConfigVOList;
  }

  public List<OrgConfig> selectDefaultConfigList(Byte type) {
    List<OrgConfig> orgConfigList = new ArrayList<>();
    if (OrgConfigEnums.Type.OPERATION_STATE.getCode().equals(type)) {
      CaseOperationEnums.State[] enums = CaseOperationEnums.State.values();
      for (CaseOperationEnums.State emum : enums) {
        if (emum.getCode().equals(CaseOperationEnums.State.UNKNOWN.getCode())) {
          continue;
        }
        OrgConfig config = new OrgConfig();
        config.setType(OrgConfigEnums.Type.OPERATION_STATE.getCode());
        config.setCode(emum.getCode().intValue());
        config.setName(emum.getMessage());
        config.setIsSelected(OrgConfigEnums.IsSelected.YES.getCode());
        config.setIsSys(OrgConfigEnums.IsSys.YES.getCode());
        orgConfigList.add(config);
      }
    } else if (OrgConfigEnums.Type.CALL_TYPE.getCode().equals(type)) {
      CaseOperationEnums.CallType[] enums = CaseOperationEnums.CallType.values();
      for (CaseOperationEnums.CallType emum : enums) {
        if (emum.getCode().equals(CaseOperationEnums.CallType.UNKNOWN.getCode())) {
          continue;
        }
        OrgConfig config = new OrgConfig();
        config.setType(OrgConfigEnums.Type.CALL_TYPE.getCode());
        config.setCode(emum.getCode());
        config.setName(emum.getMessage());
        config.setIsSelected(OrgConfigEnums.IsSelected.YES.getCode());
        config.setIsSys(OrgConfigEnums.IsSys.YES.getCode());
        orgConfigList.add(config);
      }
    }
    return orgConfigList;
  }

  @Transactional(rollbackFor = Exception.class)
  public void add(OrgConfigAddParam orgConfigAddParam) throws InterruptedException {
    UserSession userSession = UserUtils.getTokenUser();
    if (!redisLock.tryLock(redisLockKey + userSession.getOrgId())) {
      throw new ApiException("请勿频繁操作");
    }
    try {

      List<OrgConfig> tmp = selectDefaultConfigList(orgConfigAddParam.getType());
      Integer allCount = orgConfigMapper.getCustomCount(userSession.getOrgId(),orgConfigAddParam.getType());
      if (allCount-tmp.size() >= 500) {
        throw new ApiException("可添加数量已达上限");
      }
      // 插入所有数据
      if (allCount == 0) {
        List<OrgConfig> allConfigList = selectDefaultConfigList(orgConfigAddParam.getType());
        List<OrgConfig> insertList = new ArrayList<>();
        Integer sort = 0;
        for (OrgConfig orgConfig : allConfigList) {
          orgConfig.setOrgId(userSession.getOrgId());
          orgConfig.setStatus(OrgConfigEnums.Status.NORMAL.getCode());
          orgConfig.setCreateBy(userSession.getId());
          orgConfig.setUpdateBy(userSession.getId());
          orgConfig.setCreateTime(new Date());
          orgConfig.setUpdateTime(new Date());
          orgConfig.setSort(sort);
          insertList.add(orgConfig);
          sort++;
        }
        orgConfigMapper.insertBatch(insertList);
      }
      // 新增数据
      OrgConfig orgConfig = new OrgConfig();
      orgConfig.setName("");
      orgConfig.setOperationNo(orgConfigAddParam.getOperationNo());
      orgConfig.setType(orgConfigAddParam.getType());
      orgConfig.setRename(orgConfigAddParam.getRename());
      orgConfig.setStatus(OrgConfigEnums.Status.NORMAL.getCode());
      orgConfig.setIsSelected(OrgConfigEnums.IsSelected.YES.getCode());
      orgConfig.setOrgId(userSession.getOrgId());
      orgConfig.setCreateBy(userSession.getId());
      orgConfig.setUpdateBy(userSession.getId());
      orgConfig.setCreateTime(new Date());
      orgConfig.setUpdateTime(new Date());
      orgConfig.setIsSys(OrgConfigEnums.IsSys.NO.getCode());
      orgConfig.setCode(-1);
      // 新增排序在最后
      orgConfig.setSort(999);
      orgConfigMapper.insert(orgConfig);
      orgConfig.setCode(orgConfig.getId().intValue());
      orgConfigMapper.updateByPrimaryKey(orgConfig);
      //绑定委案公司
      if (Objects.equals(orgConfigAddParam.getIsAllDelt(), 1)) {
        deltService.addAllDeltOperation(userSession.getOrgId(), orgConfigAddParam.getType().intValue(), orgConfig.getId());
      } else {
        deltService.addDeltOperation(orgConfigAddParam.getOrgDeltIds(), orgConfigAddParam.getType().intValue(), orgConfig.getId());
      }
    } finally {
      redisLock.unlock(redisLockKey + userSession.getOrgId());
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void addDefaultOperationProcessAndCallType(Long orgId, Long userId) {
    List<OrgConfig> processList = selectDefaultConfigList(OrgConfigEnums.Type.OPERATION_STATE.getCode());
    List<OrgConfig> callList = selectDefaultConfigList(OrgConfigEnums.Type.CALL_TYPE.getCode());
    List<OrgConfig> insertList = new ArrayList<>();
    int processSort = 0;
    for(OrgConfig orgConfig : processList) {
      orgConfig.setOrgId(orgId);
      orgConfig.setStatus(OrgConfigEnums.Status.NORMAL.getCode());
      orgConfig.setCreateBy(userId);
      orgConfig.setUpdateBy(userId);
      orgConfig.setCreateTime(new Date());
      orgConfig.setUpdateTime(new Date());
      orgConfig.setSort(processSort);
      insertList.add(orgConfig);
      processSort++;
    }

    int callSort = 0;
    for(OrgConfig orgConfig : callList) {
      orgConfig.setOrgId(orgId);
      orgConfig.setStatus(OrgConfigEnums.Status.NORMAL.getCode());
      orgConfig.setCreateBy(userId);
      orgConfig.setUpdateBy(userId);
      orgConfig.setCreateTime(new Date());
      orgConfig.setUpdateTime(new Date());
      orgConfig.setSort(callSort);
      insertList.add(orgConfig);
      callSort++;
    }

    if (!CollectionUtils.isEmpty(insertList)) {
      orgConfigMapper.insertBatch(insertList);
    }
  }


  @Transactional(rollbackFor = Exception.class)
  public void delete(Long id) throws InterruptedException {
    UserSession userSession = UserUtils.getTokenUser();
    if (!redisLock.tryLock(redisLockKey + userSession.getOrgId())) {
      throw new ApiException("请勿频繁操作");
    }
    try {
      OrgConfig orgConfig = orgConfigMapper.selectByPrimaryKey(id);
      if (orgConfig == null || !orgConfig.getOrgId().equals(userSession.getOrgId())) {
        throw new ApiException("该(催收进程/电话结果)结果不存在");
      }
      if (OrgConfigEnums.IsSys.YES.getCode().equals(orgConfig.getIsSys())) {
        throw new ApiException("系统结果不能删除");
      }
      List<Integer> codeList = orgConfigMapper.selectSelectedCodeList(userSession.getOrgId(),orgConfig.getType());
      if (codeList.size() == 0 || (codeList.size() == 1 && codeList.contains(orgConfig.getCode()))) {
        throw new ApiException("请至少开启一个");
      }
      orgConfig.setStatus(CustomOperationStatusEnums.Status.DELETE.getCode());
      orgConfig.setSort(0);
      orgConfig.setIsSelected(CustomOperationStatusEnums.IsSelected.NO.getCode());
      orgConfig.setUpdateBy(userSession.getId());
      orgConfig.setUpdateTime(new Date());
      orgConfigMapper.updateByPrimaryKey(orgConfig);
    } finally {
      redisLock.unlock(redisLockKey + userSession.getOrgId());
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void update(OrgConfigEditParam orgConfigEditParam) throws InterruptedException {
    UserSession userSession = UserUtils.getTokenUser();
    if (!redisLock.tryLock(redisLockKey + userSession.getOrgId())) {
      throw new ApiException("请勿频繁操作");
    }
    Integer code = orgConfigEditParam.getCode();
    try {
      List<OrgConfig> orgConfigList =
          orgConfigMapper.selectByTypeAndCode(
              userSession.getOrgId(), orgConfigEditParam.getType(), code);
      if (CollectionUtils.isEmpty(orgConfigList)) {
        // 插入所有数据
        List<OrgConfig> allConfigList = selectDefaultConfigList(orgConfigEditParam.getType());
        List<OrgConfig> insertList = new ArrayList<>();
        Integer sort = 0;
        for (OrgConfig orgConfig : allConfigList) {
          orgConfig.setOrgId(userSession.getOrgId());
          orgConfig.setCreateBy(userSession.getId());
          orgConfig.setUpdateBy(userSession.getId());
          orgConfig.setCreateTime(new Date());
          orgConfig.setUpdateTime(new Date());
          orgConfig.setStatus(OrgConfigEnums.Status.NORMAL.getCode());
          orgConfig.setSort(sort);
          if (orgConfig.getCode().equals(code)) {
            orgConfig.setRename(orgConfigEditParam.getRename());
            orgConfig.setIsSelected(orgConfigEditParam.getIsSelected());
          }
          insertList.add(orgConfig);
          sort++;
        }
        orgConfigMapper.insertBatch(insertList);
        insertList.forEach(orgConfig -> {
          //绑定委案公司
          if (Objects.equals(orgConfigEditParam.getIsAllDelt(), 1)) {
            deltService.addAllDeltOperation(userSession.getOrgId(), orgConfigEditParam.getType().intValue(), orgConfig.getId());
          } else {
            deltService.addDeltOperation(orgConfigEditParam.getOrgDeltIds(), orgConfigEditParam.getType().intValue(), orgConfig.getId());
          }
        });
      } else {
        // 更新该数据
        if (OrgConfigEnums.IsSelected.NO.getCode().equals(orgConfigEditParam.getIsSelected())) {
          List<Integer> codeList =
              orgConfigMapper.selectSelectedCodeList(
                  userSession.getOrgId(), orgConfigEditParam.getType());
          if (codeList.size() == 0 || (codeList.size() == 1 && codeList.contains(code))) {
            throw new ApiException("请至少开启一个");
          }
          // 正常接通、无人接听、忙线中、关机、停机、空号、呼叫异常，无法关闭
          if (OrgConfigEnums.Type.CALL_TYPE.getCode().equals(orgConfigEditParam.getType())
              && (CaseOperationEnums.CallType.NORMAL.getCode().equals(code)
                  || CaseOperationEnums.CallType.NO_ANSWER.getCode().equals(code)
                  || CaseOperationEnums.CallType.BUSY_LINE.getCode().equals(code)
                  || CaseOperationEnums.CallType.SHUTDOWN.getCode().equals(code)
                  || CaseOperationEnums.CallType.STOP_SERVICE.getCode().equals(code)
                  || CaseOperationEnums.CallType.EMPTY_NUMBER.getCode().equals(code)
                  || CaseOperationEnums.CallType.CALL_ERROR.getCode().equals(code))) {
            throw new ApiException("该电话结果不能关闭");
          }
        }
        OrgConfig orgConfig = orgConfigList.get(0);
        orgConfig.setRename(orgConfigEditParam.getRename());
        orgConfig.setIsSelected(orgConfigEditParam.getIsSelected());
        orgConfig.setOperationNo(orgConfigEditParam.getOperationNo());
        orgConfigMapper.updateByPrimaryKey(orgConfig);
        //绑定委案公司
        if (Objects.equals(orgConfigEditParam.getIsAllDelt(), 1)) {
          deltService.addAllDeltOperation(userSession.getOrgId(), orgConfigEditParam.getType().intValue(), orgConfig.getId());
        } else {
          deltService.addDeltOperation(orgConfigEditParam.getOrgDeltIds(), orgConfigEditParam.getType().intValue(), orgConfig.getId());
        }
      }
    } finally {
      redisLock.unlock(redisLockKey + userSession.getOrgId());
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void reorder(OrgConfigReorderParam orgConfigReorderParam) throws InterruptedException {
    UserSession userSession = UserUtils.getTokenUser();
    if (!redisLock.tryLock(redisLockKey + userSession.getOrgId())) {
      throw new ApiException("请勿频繁操作");
    }
    try {
      List<OrgConfig> configList =
          orgConfigMapper.selectByTypeAndCode(
              userSession.getOrgId(), orgConfigReorderParam.getType(), null);
      if (CollectionUtils.isEmpty(configList)) {
        List<OrgConfig> allConfigList = selectDefaultConfigList(orgConfigReorderParam.getType());
        Map<Integer, OrgConfig> orgConfigMap =
            allConfigList.stream().collect(Collectors.toMap(OrgConfig::getCode, c -> c));
        // 重新排序
        List<OrgConfig> insertList = new ArrayList<>();
        for (int i = 0; i < orgConfigReorderParam.getCodeList().size(); i++) {
          OrgConfig orgConfig = orgConfigMap.get(orgConfigReorderParam.getCodeList().get(i));
          if (orgConfig == null) {
            throw new ApiException("信息已变更，无法执行此操作，请刷新后重试");
          }
          orgConfig.setSort(i);
          orgConfig.setOrgId(userSession.getOrgId());
          orgConfig.setCreateBy(userSession.getId());
          orgConfig.setUpdateBy(userSession.getId());
          orgConfig.setCreateTime(new Date());
          orgConfig.setStatus(OrgConfigEnums.Status.NORMAL.getCode());
          orgConfig.setUpdateTime(new Date());
          insertList.add(orgConfig);
        }
        orgConfigMapper.insertBatch(insertList);
      } else {
        Map<Integer, OrgConfig> orgConfigMap =
            configList.stream().collect(Collectors.toMap(OrgConfig::getCode, c -> c));
        List<OrgConfig> updateList = new ArrayList<>();
        for (int i = 0; i < orgConfigReorderParam.getCodeList().size(); i++) {
          OrgConfig orgConfig = orgConfigMap.get(orgConfigReorderParam.getCodeList().get(i));
          if (orgConfig == null) {
            throw new ApiException("信息已变更，无法执行此操作，请刷新后重试");
          }
          orgConfig.setSort(i);
          orgConfig.setStatus(OrgConfigEnums.Status.NORMAL.getCode());
          orgConfig.setUpdateBy(userSession.getId());
          orgConfig.setUpdateTime(new Date());
          updateList.add(orgConfig);
        }
        orgConfigMapper.updateBatch(updateList);
      }
    } finally {
      redisLock.unlock(redisLockKey + userSession.getOrgId());
    }
  }

  public Map<Integer, String> getAllNames(Long orgId, Byte type) {
    Map<Integer, String> nameMap = new HashMap<>();
    List<OrgConfig> configList = orgConfigMapper.selectAllByTypeAndCode(orgId, type, null);
    if (CollectionUtils.isEmpty(configList)) {
      // 系统默认的
      configList = selectDefaultConfigList(type);
    }
    for (OrgConfig orgConfig : configList) {
      if (StringUtils.isNotBlank(orgConfig.getRename())) {
        nameMap.put(orgConfig.getCode(), orgConfig.getRename());
      } else {
        nameMap.put(orgConfig.getCode(), orgConfig.getName());
      }
    }
    if (OrgConfigEnums.Type.OPERATION_STATE.getCode().equals(type)) {
      nameMap.put(
          CaseOperationEnums.State.UNKNOWN.getCode().intValue(),
          CaseOperationEnums.State.UNKNOWN.getMessage());
    } else {
      nameMap.put(
          CaseOperationEnums.CallType.NOT_FILLED.getCode(),
          CaseOperationEnums.CallType.NOT_FILLED.getMessage());
    }
    return nameMap;
  }

  public List<OrgConfig> getList(Long orgId, Integer type) {
    List<OrgConfig> orgConfigList = orgConfigMapper.getList(orgId, type);
    return orgConfigList;
  }

  public Map<Long, String> getNameMap(Long orgId, Byte type) {
    List<OrgConfig> configList = orgConfigMapper.selectAllByTypeAndCode(orgId, type, null);
    Map<Long, String> map = new HashMap<>();
    configList.forEach(orgConfig -> {
      String name;
      if (StringUtils.isNotBlank(orgConfig.getRename())) {
        name = orgConfig.getRename();
      } else {
        name = orgConfig.getName();
      }
      map.put(orgConfig.getId(), name);
    });
    return map;
  }


}
