package com.anmi.collection.service.strategy;

import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.utils.dict.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * CaseFollow策略工厂
 * <AUTHOR>
 */
@Component
public class CaseFollowStrategyFactory {

    @Autowired
    private Map<String,CaseFollowStrategy> map;

    public CaseFollowStrategy getConcreteStrategy(UserSession userSession){
        CaseFollowStrategy strategy = null;
        if (UserUtils.likeAdmin(userSession.getRoleId())) {
            strategy = (CaseFollowStrategy) map.get("adminCaseFollowStrategy");
        }else if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
            strategy = (CaseFollowStrategy) map.get("branchAdminCaseFollowStrategy");
        }else if (UserUtils.likeTeamLeader(userSession.getRoleId())){
            strategy = (CaseFollowStrategy) map.get("teamLeaderCaseFollowStrategy");
        }
        return strategy;
    }
}
