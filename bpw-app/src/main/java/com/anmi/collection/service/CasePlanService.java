package com.anmi.collection.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.dto.CaseContact;
import com.anmi.collection.entity.requset.cases.Campaigns;
import com.anmi.collection.entity.requset.cases.CaseMultiQuery;
import com.anmi.collection.entity.requset.cases.CaseOperationParam;
import com.anmi.collection.entity.requset.cases.casePlan.*;
import com.anmi.collection.entity.requset.duyan.PlanDetailQuery;
import com.anmi.collection.entity.requset.site.SiteRelativeFO;
import com.anmi.collection.entity.requset.site.TagAndCommentBO;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.cases.*;
import com.anmi.collection.entity.response.duyan.*;
import com.anmi.collection.entity.response.message.MessageAccountDTO;
import com.anmi.collection.entity.response.message.MessageDetailDTO;
import com.anmi.collection.entity.response.site.SiteRelativeVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.manager.MessageManager;
import com.anmi.collection.manager.NewRobotManager;
import com.anmi.collection.manager.dorest.response.StatisticsPlanRes;
import com.anmi.collection.mapper.*;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.external.AbstractExternalStrategy;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.*;
import com.anmi.domain.site.SiteRelative;
import com.anmi.domain.site.SiteVarsMapping;
import com.anmi.domain.user.DepTeam;
import com.anmi.domain.user.User;
import com.anmi.domain.yunpian.SmsSend;
import com.anmi.domain.yunpian.SmsSign;
import com.anmi.domain.yunpian.SmsTemplate;
import com.github.pagehelper.Page;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/** Created by dongwang on 2019-01-07. */
@Service
@Slf4j
public class CasePlanService extends BaseService<CasePlan> {
  @Autowired private CasePlanMapper casePlanMapper;
  @Autowired private UserService userService;
  @Autowired private DeltService deltService;
  @Autowired private CaseOperationService caseOperationService;
  @Autowired private ProductService productService;
  @Autowired private CaseService caseService;
  @Autowired private DuyanManager manager;
  @Autowired private SiteRelativeService siteRelativeService;
  @Autowired private StringRedisTemplate stringRedisTemplate;
  @Autowired private DepTeamService depTeamService;
  @Autowired private MessageManager messageManager;
  @Autowired private SmsSendService smsSendService;
  @Autowired private SmsTemplateService smsTemplateService;
  @Autowired private CasePlanRelService casePlanRelService;
  @Autowired private OutBatchService outBatchService;
  @Autowired private CasePlanRelMapper casePlanRelMapper;
  @Autowired private ContactsMapper contactsMapper;
  @Autowired private CasePlanRelContactService casePlanRelContactService;
  @Autowired private CasePlanRelContactMapper casePlanRelContactMapper;
  @Autowired private OrgSwitchMapper orgSwitchMapper;
  @Autowired private RedisUtil redisUtil;
  @Autowired private NewRobotManager newRobotManager;
  @Autowired private SiteVarsMappingService siteVarsMappingService;
  @Autowired private CtrlTypeRelService ctrlTypeRelService;
  @Autowired private CaseCtrlService caseCtrlService;
  @Autowired private List<AbstractExternalStrategy> externalStrategyList;
  @Autowired private RobotPlanFilterContactRecordMapper robotPlanFilterContactRecordMapper;

  public int changeStatus(ChangePlan change) {
    Integer robotType = change.getRobotType();
    AssertUtil.isTrue(CasePlanEnums.Type.isExists(robotType), "计划类型非法");

    if (change.getPlanStatus().equalsIgnoreCase("DELETE")) { // 表示安米删除计划任务，然后度言取消任务
      casePlanMapper.deleteByDuyanPlanId(change.getDuyanPlanId());
      change.setPlanStatus("CANCELLED"); // 已取消
    }
    return manager.change(change);
  }

  /**
   * 同步创建计划调用
   *
   * @param caseVOS       计划内案件列表
   * @param casePlan      案件计划
   * @param userSession   用户会话
   * @param duyanVarsMappings 度言话术变量
   */
  @Transactional(rollbackFor = Exception.class)
  public void insertPlan(List<CaseVO> caseVOS, CasePlan casePlan,UserSession userSession,List<SiteVarsMapping> duyanVarsMappings) throws Exception {
    List<Long> caseIds = caseVOS.stream().map(CaseVO::getId).collect(Collectors.toList());

    List<Case> cases = BeanUtil.copyPropertiesFromList(caseVOS, Case.class);
    // 过滤掉的联系人需记录下来
    Map<Integer, List<CaseContact>> filterCaseContactsMap = new HashMap<>();
    List<Case> noExecCase = externalStrategyList.stream()
            .flatMap(strategy -> strategy.filter(cases, filterCaseContactsMap).stream())
            .collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(noExecCase)) {
      log.info("第三方案件校验移除，计划id{},移除案件id{}",casePlan.getId(), noExecCase.stream().map(Case::getId).collect(Collectors.toList()));
      // 移除案件id
      caseIds = caseIds.stream()
              .filter(id -> noExecCase.stream().noneMatch(c -> c.getId().equals(id)))
              .collect(Collectors.toList());
      // 移除案件
      caseVOS = caseVOS.stream()
              .filter(c1 -> noExecCase.stream().noneMatch(c2 -> ObjectUtil.equals(c1.getId(), c2.getId())))
              .collect(Collectors.toList());
    }

    if (CollectionUtils.isEmpty(caseIds) || CollectionUtils.isEmpty(caseVOS)) {
        SpringContextHolder.getBean(CasePlanService.class).insertFilterRecords(casePlan.getId(), filterCaseContactsMap, casePlan.getOrgId());
        throw new ApiException("所选案件根据第三方案件校验过滤后无剩余案件，创建任务失败，具体请查看注意事项");
    }

    List<CaseContact> caseContactsAll = contactsMapper.selectContactByCaseIds(caseIds);
    List<CaseContact> caseContacts = Lists.newArrayList();
    if (casePlan.getDebtorOwn() != null && casePlan.getDebtorOwn() == 1) {
      List<CaseContact> ownList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 1)
              .filter(contacts -> PatternUtils.isPlanPhone(contacts.getContactMobile()))
              .collect(Collectors.toList());

      List<CaseContact> ownInvalidContactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 1)
              .filter(contacts -> !PatternUtils.isPlanPhone(contacts.getContactMobile()))
              .collect(Collectors.toList());
      if (ObjectUtil.isNotEmpty(ownInvalidContactsList)) {
        List<CaseContact> ownInvalidContacts = filterCaseContactsMap.getOrDefault(ContactFilterTypeEnum.INVALID_NUMBER.getCode(), Lists.newArrayList());
        ownInvalidContacts.addAll(ownInvalidContactsList);
        filterCaseContactsMap.put(ContactFilterTypeEnum.INVALID_NUMBER.getCode(), ownInvalidContacts);
      }

      // 最新本人号码
      if (casePlan.getDebtorOwnType() != null &&
              Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.LAST.getCode())) {
        Map<Long, CaseContact> caseContactMap = ownList.stream().collect(Collectors.groupingBy(
                CaseContact::getCaseId, Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(CaseContact::getConUpdateTime)), Optional::get)));
        ownList.clear();
        ownList.addAll(caseContactMap.values());
      }
      // 随机本人号码
      if (casePlan.getDebtorOwnType() != null &&
              Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.RANDOM.getCode())) {
        List<CaseContact> randomCaseContacts = Lists.newArrayList();
        ownList.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> randomCaseContacts.add(v.get(0)));
        ownList.clear();
        ownList.addAll(randomCaseContacts);
      }
      caseContacts.addAll(ownList);
    }
    if (casePlan.getContacts() != null && casePlan.getContacts() == 1) {
      List<CaseContact> contactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 0)
              .filter(contacts -> PatternUtils.isPlanPhone(contacts.getContactMobile()))
              .collect(Collectors.toList());

      List<CaseContact> invalidContactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 0)
              .filter(contacts -> !PatternUtils.isPlanPhone(contacts.getContactMobile()))
              .collect(Collectors.toList());
      if (ObjectUtil.isNotEmpty(invalidContactsList)) {
        List<CaseContact> invalidContacts = filterCaseContactsMap.getOrDefault(ContactFilterTypeEnum.INVALID_NUMBER.getCode(), Lists.newArrayList());
        invalidContacts.addAll(invalidContactsList);
        filterCaseContactsMap.put(ContactFilterTypeEnum.INVALID_NUMBER.getCode(), invalidContactsList);
      }
      // 除本人外的所有号码类型
      if (StringUtils.isNotBlank(casePlan.getContactTypeIds())) {
        String contactTypeIdStr = casePlan.getContactTypeIds();
        List<Long> contactTypeIds = Stream.of(contactTypeIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
        if (contactTypeIds.size() > 1) {
          contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
        }
        if (contactTypeIds.size() == 1) {
          if (!Objects.equals(contactTypeIds.get(0), CasePlanEnums.ContactTypeEnum.ALL.getCode())) {
            contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
          }
        }
      }
      caseContacts.addAll(contactsList);
    }

    //按手机号分组选择第一个手机号（相同手机号的第一个案件）
    List<CaseContact> sameMobileFirstCaseContacts = Lists.newArrayList();
    // 相同手机号过滤
    List<CaseContact> sameMobileFilterCaseContacts = Lists.newArrayList();

    // 新机器人计划的body
    List<Map<String,Object>> contents = new ArrayList<>();

    if (ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.NEWROBOT.getCode())){
      // 相同手机号，取其中第一个案子参与计划
      if (!CollectionUtils.isEmpty(caseContacts)) {
        Map<String, List<CaseContact>> collect = caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile));
        collect.forEach((k, v) -> {
          sameMobileFirstCaseContacts.add(v.get(0));
          if (v.size() > 1) {
            sameMobileFilterCaseContacts.addAll(v.subList(1, v.size()));
          }
        });
        // caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile)).forEach((k, v) -> sameMobileFirstCaseContacts.add(v.get(0)));
      }

      if (ObjectUtil.isNotEmpty(sameMobileFilterCaseContacts)) {
        filterCaseContactsMap.put(ContactFilterTypeEnum.SAME_NUMBER.getCode(), sameMobileFilterCaseContacts);
      }

      List<Long> userIds = sameMobileFirstCaseContacts.stream().filter(caseContact -> Objects.nonNull(caseContact.getUserId()))
              .map(CaseContact::getUserId)
              .distinct()
              .collect(Collectors.toList());
      List<User> userList = userService.selectByIdList(userIds);
      sameMobileFirstCaseContacts.forEach(caseContact -> {
        User u = userList.stream().filter(user -> Objects.equals(user.getId(), caseContact.getUserId()))
                .findFirst().orElse(null);
        caseContact.setDunnerCode(u == null ? null : u.getUserNo());
        caseContact.setDuyanAccountId(u == null ? null : u.getDuyanAccountId());
      });

      // 有不符合度言话术变量要求的,剔除
      List<CaseContact> varLackCaseContacts = Lists.newArrayList();
      Iterator<CaseContact> iterator = sameMobileFirstCaseContacts.iterator();
      while (iterator.hasNext()) {
        CaseContact caseContact = iterator.next();
        Map<String, Object> caseContactMap = BeanUtils.beanToMap(caseContact);

        Long caseId = caseContact.getCaseId();
        Optional<CaseVO> caseVOOptional = caseVOS.stream().filter(caseVO -> ObjectUtil.equals(caseId, caseVO.getId())).findFirst();
        CaseVO caseVO = caseVOOptional.get();
        Map<String, String> fieldJsonMap = caseVO.getFieldJson();

        boolean varValueCheck = duyanVarsMappings.stream().anyMatch(varMapping -> {
          String anmiVar = varMapping.getAnmiVar();
          SiteEnums.Var varEmum = SiteEnums.Var.getByAnmiVar(anmiVar);
          Object value = null;
          if (ObjectUtil.isNull(varEmum)){
            value = fieldJsonMap.get(anmiVar);
          } else {
            String source = varEmum.getSource();
            String key = StringUtils.substringAfterLast(source, ".");

            if (source.startsWith("CaseContact")){
              value = caseContactMap.get(key);
            }else if (source.startsWith("CaseVO.fieldJson")){
              value = fieldJsonMap.get(key);
            }
          }
          return ObjectUtil.isNull(value);
        });

        if (varValueCheck){
          log.info("话术变量缺失,剔除,案件:{},手机号:{},contact_id:{}",caseId,caseContact.getContactMobile(),caseContact.getContactId());
          varLackCaseContacts.add(caseContact);
          iterator.remove();
        }
      }
      if (ObjectUtil.isNotEmpty(varLackCaseContacts)) {
        filterCaseContactsMap.put(ContactFilterTypeEnum.VARS_LACK.getCode(), varLackCaseContacts);
      }

      Long orgId = userSession.getOrgId();
      if(ObjectUtil.isNotEmpty(sameMobileFirstCaseContacts)){
        for (int i = 0; i < sameMobileFirstCaseContacts.size(); i++) {
          CaseContact caseContact = sameMobileFirstCaseContacts.get(i);
          Long caseId = caseContact.getCaseId();
          Optional<CaseVO> caseVOOptional = caseVOS.stream().filter(caseVO -> ObjectUtil.equals(caseId, caseVO.getId())).findFirst();
          CaseVO caseVO = caseVOOptional.get();
          Map<String, String> fieldJsonMap = caseVO.getFieldJson();

          addContent(caseContact,orgId,fieldJsonMap,contents,duyanVarsMappings, casePlan);
        }
      }
    }else {
      // 先按案件id分组选择案件（每个案件的第一个联系人）
      List<CaseContact> sameCaseIdFirstCaseContacts = Lists.newArrayList();
      // 根据案件id分组查询，并取每个案件的第一个联系人参与计划
      if (!CollectionUtils.isEmpty(caseContacts)) {
        caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> sameCaseIdFirstCaseContacts.add(v.get(0)));
      }

      // 相同手机号，取其中第一个案子参与计划
      if (!CollectionUtils.isEmpty(sameCaseIdFirstCaseContacts)) {
        sameCaseIdFirstCaseContacts.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile)).forEach((k, v) -> sameMobileFirstCaseContacts.add(v.get(0)));
      }
    }

    if(CollectionUtils.isEmpty(sameMobileFirstCaseContacts)){
      SpringContextHolder.getBean(CasePlanService.class).insertFilterRecords(casePlan.getId(), filterCaseContactsMap, casePlan.getOrgId());
      throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
    }

    CasePlan aiPlan = manager.createPlan(sameMobileFirstCaseContacts, casePlan, userSession, contents);
    if (aiPlan.getDuyanPlanId() != null) {
      casePlanMapper.insertSelective(aiPlan);
      // 计划关联的案件id
      List<Long> relCaseIds = sameMobileFirstCaseContacts.stream().map(CaseContact::getCaseId).distinct().collect(Collectors.toList());
      // 计划与案件关联
      casePlanRelService.saveCasePlanRelBatch(relCaseIds, aiPlan.getId(), userSession, casePlan.getType());
      this.createCasePlanRelContact(casePlan,caseContacts,
              sameMobileFirstCaseContacts.stream().map(CaseContact::getContactMobile).collect(Collectors.toList()));

      SpringContextHolder.getBean(CasePlanService.class).insertFilterRecords(casePlan.getId(), filterCaseContactsMap, casePlan.getOrgId());
    }
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void insertFilterRecords(Long planId, Map<Integer, List<CaseContact>> filterCaseContactsMap, Long orgId) {
    if (!filterCaseContactsMap.isEmpty()) {
      List<RobotPlanFilterContactRecord> records = new ArrayList<>();
      for (Map.Entry<Integer, List<CaseContact>> entry : filterCaseContactsMap.entrySet()) {
        Integer filterType = entry.getKey();
        List<CaseContact> filterTypeCaseContacts = entry.getValue();
        for (CaseContact caseContact : filterTypeCaseContacts) {
          RobotPlanFilterContactRecord record = new RobotPlanFilterContactRecord();
          record.setOrgId(orgId);
          record.setCaseId(caseContact.getCaseId());
          record.setContactId(caseContact.getContactId());
          record.setContactName(caseContact.getContactName());
          record.setContactMobile(caseContact.getContactMobile());
          record.setFilterType(filterType);
          record.setCreateTime(new Date());
          record.setUpdateTime(new Date());
          record.setPlanId(planId);
          records.add(record);
        }
      }
      if (ObjectUtil.isNotEmpty(records)) {
        robotPlanFilterContactRecordMapper.insertList(records);
      }
    }
  }

  // https://open.duyansoft.com/api/v1/call/campaign_item 获取单条计划详情
  public String getCallDetail(Campaigns campaigns, long orgId, CasePlan casePlan) {
    if (ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.NEWROBOT.getCode()) ||
            ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.PERSONALFIFO.getCode())){
      // todo 新机器人，度言获取单条计划详情接口目前没有 通话命中标签，先使用获取计划执行结果接口进行获取
      PlanDetailQuery planDetailQuery = new PlanDetailQuery();
      planDetailQuery.setPage_num(1);
      planDetailQuery.setPage_size(100);
      planDetailQuery.setCampaign_id(campaigns.getCampaign_id());
      planDetailQuery.setOutcome(campaigns.getOutcome());
      if (ObjectUtil.equals(campaigns.getType(), "INBOUND")){
        planDetailQuery.setPhone(campaigns.getCaller());
      } else {
        planDetailQuery.setPhone(campaigns.getCallee());
      }
      //planDetailQuery.setCall_start_time(campaigns.getCall_time()-10);
      //planDetailQuery.setCall_end_time(campaigns.getCall_time()+10);
      PageOutput<PlanDetailVO> planDetails = new PageOutput<>();
      if (ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.NEWROBOT.getCode())) {
        planDetails = newRobotManager.getPlanDetails(planDetailQuery, orgId);
      }
      if (ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.PERSONALFIFO.getCode())) {
        planDetails = manager.getFifoPlanDetail(planDetailQuery, orgId);
      }
      List<PlanDetailVO> detailVOList = planDetails.getList();
      Optional<PlanDetailVO> matchOptional = detailVOList.stream().filter(detail -> ObjectUtil.equals(detail.getCall_uuid(), campaigns.getCall_uuid())).findFirst();
      if (matchOptional.isPresent()){
        List<String> voiceSiteLabels = matchOptional.get().getVoice_site_labels();
        return StringUtils.join(voiceSiteLabels, ",");
      }
      return StrUtil.EMPTY;
    }
    return manager.getCallDetail(campaigns.getCall_uuid(), orgId);
  }

  public VoiceSiteVO getDuyanSites(String module) {
    List<VoiceSite> sites = manager.getSites(module);
    VoiceSiteVO voiceSiteVO = new VoiceSiteVO();
    if (sites != null && sites.size() > 0) {
      for (VoiceSite site : sites) {
        List<SiteRelative> list = siteRelativeService.selectSiteRelativeBySiteId(site.getId());
        if (list != null && list.size() > 0) {
          List<TagAndCommentBO> collect =
              list.stream()
                  .map(temp -> new TagAndCommentBO(temp.getTag(), temp.getComment()))
                  .collect(Collectors.toList());
          site.setComments(collect);
          site.setBindingMapping(1); // 已绑定映射
        } else {
          site.setBindingMapping(0);
        }
      }
    }
    voiceSiteVO.setVoiceSites(sites);
    return voiceSiteVO;
  }

  public PageOutput<CaseMessageDetailVO> queryMessageDetailList(
      MessageDetailQueryParam param, PageParam pageParam) {

    Map<String, Object> paramMap = new HashMap<>();
    paramMap.put("planId", param.getCasePlanId());
    UserSession userSession = UserUtils.getTokenUser();
    if (userSession.getOrgId() != null) {
      paramMap.put("orgId", userSession.getOrgId());
    }
    //        if (userSession.getDepId() != null){
    //            paramMap.put("depId", userSession.getDepId());
    //        }
    //        if (userSession.getTeamId() != null){
    //            paramMap.put("teamId", userSession.getTeamId());
    //        }
    if (StringUtils.isNotBlank(param.getDebtorMobile())) {
      paramMap.put("relationMobile", param.getDebtorMobile());
    }

    if (param.getSendTime() != null && param.getSendTime().split(",").length == 2) {
      paramMap.put("sendTimeStart", convertDate(param.getSendTime().split(",")[0]));
      paramMap.put("sendTimeEnd", convertDate(param.getSendTime().split(",")[1]));
    }
    if (param.getSendStatus() != null) {
      paramMap.put("sendStatus", param.getSendStatus());
    }
    if (param.getReceiveStatus() != null) {
      paramMap.put("receiveStatus", param.getReceiveStatus());
    }
    Page total = setPage(pageParam);
    List<MessageDetailDTO> messageDetailDTOS = smsSendService.selectMessageDetail(paramMap);
    List<CaseMessageDetailVO> caseAutoPlanDetailVOList = Lists.newArrayList();
    for (MessageDetailDTO detailDTO : messageDetailDTOS) {
      CaseMessageDetailVO caseMessageDetailVO = new CaseMessageDetailVO();
      caseMessageDetailVO.setCaseId(detailDTO.getCaseId());
      caseMessageDetailVO.setOutBatchNo(outBatchService.getNames().get(detailDTO.getOutBatchId()));
      caseMessageDetailVO.setOrgDeltName(deltService.getNames().get(detailDTO.getOrgDeltId()));
      caseMessageDetailVO.setProductName(productService.getNames().get(detailDTO.getProductId()));
      caseMessageDetailVO.setOutSerialNo(detailDTO.getOutSerialTemp());
      caseMessageDetailVO.setState(detailDTO.getState());
      caseMessageDetailVO.setSendStatus(detailDTO.getSendStatus());
      caseMessageDetailVO.setReceiveStatus(detailDTO.getReceiveStatus());
      caseMessageDetailVO.setDebtorName(detailDTO.getDebtorName());
      caseMessageDetailVO.setDebtorMobile(detailDTO.getDebtorMobile());
      caseMessageDetailVO.setContent(detailDTO.getContent());
      caseMessageDetailVO.setSendTime(detailDTO.getSendTime());
      caseMessageDetailVO.setRelation(detailDTO.getRelation());
      // 号码类型
      caseMessageDetailVO.setContactTypeId(detailDTO.getContactTypeId());
      caseMessageDetailVO.setContactTypeName(detailDTO.getContactTypeName());
      caseAutoPlanDetailVOList.add(caseMessageDetailVO);
    }
    PageOutput pageOutput =
        new PageOutput(
            pageParam.getPage(),
            pageParam.getLimit(),
            total != null ? (int) total.getTotal() : caseAutoPlanDetailVOList.size(),
            caseAutoPlanDetailVOList);
    return pageOutput;
  }

  public PageOutput<CaseAutoPlanDetailVO> quertDetailList(
      PlanDetailQuery query, CasePlanDetailParam param) {
    CasePlan entity = new CasePlan();
    Integer type = param.getType();
    if (type == null) {
      // 默认机器人计划明细
      type = CasePlanEnums.Type.ROBOT.getCode();
    }
    List<CaseAutoPlanDetailVO> detailVOS = new ArrayList<>();
    entity.setDuyanPlanId(query.getCampaign_id());
    List<CasePlan> plans = super.select(entity);
    if (plans.size() != 1) {
      throw new ApiException("无法找到对应的计划");
    }
    PageOutput planDetails = manager.getPlanDetails(query,type);
    List<PlanDetailVO> list = planDetails.getList();
    if (list == null || list.size() == 0) {
      planDetails.setList(detailVOS);
      return planDetails;
    }
    // 查询出所有的caseId和已经
    List<Long> caseIds = new ArrayList<>();
    List<String> callIds = new ArrayList<>();
    List<Long> campaignIds = list.stream().map(PlanDetailVO::getCampaignId).collect(Collectors.toList());
    List<CasePlan> casePlans = selectCasePlanByDuyanPlanIds(campaignIds);
    Map<Long, CasePlan> casePlanMap = casePlans.stream().collect(Collectors.toMap(CasePlan::getDuyanPlanId, f -> f));
    for (PlanDetailVO detailVO : list) {
      Long id;
      CasePlan casePlan = casePlanMap.get(detailVO.getCampaignId());
      if (CasePlanEnums.Type.isRobot(type) ||
              (Objects.equals(CasePlanEnums.Type.PERSONALFIFO.getCode(), type) && Objects.equals(casePlan.getFifoToRobotEnabled(), 1))) {
        id = JSONObject.parseObject(detailVO.getVariables()).getLong("U_caseId");
      } else {
        String tag = JSONObject.parseObject(detailVO.getVariables()).getString("U_TAG");
        // 度言只支持中文逗号分隔
        id = Long.valueOf(tag.split("，")[0]);
      }
      detailVO.setCaseId(id);
      caseIds.add(id);
      if (!StringUtils.isEmpty(detailVO.getCall_uuid())) {
        callIds.add(detailVO.getCall_uuid());
      }
    }
    // 查询所有拨打的记录
    List<CasePlanDetailResult> caseOperations = new ArrayList<>();
    if (callIds.size() > 0) {
      CaseOperationParam map = new CaseOperationParam();
      map.setCallIds(callIds);
      caseOperations = caseOperationService.selectCasePlanDetail(map);
    }

    // 查询所有未拨打的记录
    List<CasePlanCallResult> caseInfoResults = new ArrayList<>();
    if (caseIds.size() > 0) {
      Map<String, Object> mapParam = new HashMap<>();
      mapParam.put("caseIds", caseIds);
      caseInfoResults = caseService.casePlanDetailList(mapParam);
    }
    List<CaseContact> contactList = contactsMapper.selectContactByCaseIds(caseIds);
    for (PlanDetailVO planDetailVO : list) {
      CaseAutoPlanDetailVO detailVO = convertDetailVO(planDetailVO, caseOperations, caseInfoResults,contactList);
      detailVO.setType(type);
      detailVOS.add(detailVO);
    }

    detailVOS = filterList(detailVOS, param);

    planDetails.setList(detailVOS);
    return planDetails;
  }

  private List<CaseAutoPlanDetailVO> filterList(
      List<CaseAutoPlanDetailVO> list, CasePlanDetailParam param) {
    if (StringUtils.isNotBlank(param.getOutSerialNo())) {
      list =
          list.stream()
              .filter(temp -> param.getOutSerialNo().equalsIgnoreCase(temp.getOutSerialNo()))
              .collect(Collectors.toList());
    }
    if (StringUtils.isNotBlank(param.getOrgDeltName())) {
      list =
          list.stream()
              .filter(temp -> param.getOrgDeltName().equalsIgnoreCase(temp.getOrgDeltName()))
              .collect(Collectors.toList());
    }
    if (StringUtils.isNotBlank(param.getProductName())) {
      list =
          list.stream()
              .filter(temp -> param.getProductName().equalsIgnoreCase(temp.getProductName()))
              .collect(Collectors.toList());
    }
    if (StringUtils.isNotBlank(param.getOwnMobile())) {
      list =
          list.stream()
              .filter(temp -> param.getOwnMobile().equalsIgnoreCase(temp.getDebtorMobile()))
              .collect(Collectors.toList());
    }
    // 过滤接听人
    if (StringUtils.isNotBlank(param.getAgentName())) {
      list =
              list.stream()
                      .filter(temp -> param.getAgentName().equalsIgnoreCase(temp.getAgentName()))
                      .collect(Collectors.toList());
    }
    list = list.stream().filter(temp -> temp != null).collect(Collectors.toList());
    return list;
  }

  /**
   * 机器人计划详情页-执行结果对象转换
   *
   * @param detailVO        细节vo
   * @param casePlanDetails 案例计划详细信息
   * @param caseInfoResults 案例信息结果
   * @param contactList     联系人列表
   * @return {@link CaseAutoPlanDetailVO}
   */
  private CaseAutoPlanDetailVO convertDetailVO(PlanDetailVO detailVO, List<CasePlanDetailResult> casePlanDetails, List<CasePlanCallResult> caseInfoResults,List<CaseContact> contactList) {
    CaseAutoPlanDetailVO caseAutoPlanDetailVO = new CaseAutoPlanDetailVO();
    caseAutoPlanDetailVO.setCallCount(detailVO.getCall_count());
    caseAutoPlanDetailVO.setVoiceSiteLabels(detailVO.getVoice_site_labels());
    String callUuid = detailVO.getCall_uuid();
    Long caseId = detailVO.getCaseId();
    if (!CollectionUtils.isEmpty(caseInfoResults)) {
      List<CasePlanCallResult> collect =
          caseInfoResults.stream()
              .filter(temp -> temp.getId().equals(caseId))
              .collect(Collectors.toList());
      if (collect.size() == 1) {
        CasePlanCallResult casePlanCallResult = collect.get(0);
        caseAutoPlanDetailVO.setCaseId(casePlanCallResult.getId());
        caseAutoPlanDetailVO.setOutSerialNo(casePlanCallResult.getOutSerialTemp());
        caseAutoPlanDetailVO.setOrgDeltName(casePlanCallResult.getOrgDeltName());
        caseAutoPlanDetailVO.setProductName(casePlanCallResult.getProductName());
        caseAutoPlanDetailVO.setOverdueDays(casePlanCallResult.getOverdueDays());
        caseAutoPlanDetailVO.setDebtorName(casePlanCallResult.getName());
        caseAutoPlanDetailVO.setDebtorMobile(casePlanCallResult.getOwnMobile());
        caseAutoPlanDetailVO.setCallType("");
        caseAutoPlanDetailVO.setTag("");
        caseAutoPlanDetailVO.setAutoCaseOperationRecord("");
        caseAutoPlanDetailVO.setState(casePlanCallResult.getState());
        caseAutoPlanDetailVO.setEntrustEndTime(casePlanCallResult.getEntrustEndTime());
        caseAutoPlanDetailVO.setOutBatchNo(casePlanCallResult.getOutBatchName());
        caseAutoPlanDetailVO.setCallResult("");
        caseAutoPlanDetailVO.setCallUUid("");
        caseAutoPlanDetailVO.setBelongTeamName(
            depTeamService.getNames().get(casePlanCallResult.getTeamId()));
        // 所属催员
        caseAutoPlanDetailVO.setBelongUserName(
            userService.getNames(userService.getOrgId()).get(casePlanCallResult.getUserId()));

        caseAutoPlanDetailVO.setRecovery(casePlanCallResult.getRecovery().intValue());
        caseAutoPlanDetailVO.setUserId(casePlanCallResult.getUserId());
        caseAutoPlanDetailVO.setCaseStatus(casePlanCallResult.getCaseStatus());
        caseAutoPlanDetailVO.setCaseAllotStatus(casePlanCallResult.getAllotStatus());
      }
    }
    if (!CollectionUtils.isEmpty(casePlanDetails)) {
      // callCount 不为空，uuid为空的场景。
      List<CasePlanDetailResult> collect =
          casePlanDetails.stream()
              .filter(temp -> temp.getCallUUId().equals(callUuid))
              .collect(Collectors.toList());
      if (collect.size() == 1) {
        CasePlanDetailResult casePlanOperationDetailResult = collect.get(0);
        caseAutoPlanDetailVO.setCaseOperationId(casePlanOperationDetailResult.getCaseOperationId());
        caseAutoPlanDetailVO.setTag(casePlanOperationDetailResult.getTag());
        caseAutoPlanDetailVO.setAutoCaseOperationRecord(
            casePlanOperationDetailResult.getAutoAssistRecord());
        caseAutoPlanDetailVO.setCallResult(detailVO.getOutcome());
        caseAutoPlanDetailVO.setCallUUid(casePlanOperationDetailResult.getCallUUId());
        if (casePlanOperationDetailResult.getCreateBy() != 0
            && casePlanOperationDetailResult.getCreateBy() != -1) {
          // 接听催员
          if (casePlanOperationDetailResult.getAdminSubmitter() != null) {
            caseAutoPlanDetailVO.setAgentName(
                    userService.getNames(userService.getOrgId()).get(casePlanOperationDetailResult.getAdminSubmitter()));
          }
        }
        // 机器人接听/漏接 直接展示至接听催员处
        if (Objects.equals(detailVO.getConnect_type(), "机器人接听") || Objects.equals(detailVO.getConnect_type(), "漏接")) {
          caseAutoPlanDetailVO.setAgentName(detailVO.getConnect_type());
        }
        caseAutoPlanDetailVO.setCaseOperationDesc(
            casePlanOperationDetailResult.getCaseOperationDesc());
      }
    }
    if(!CollectionUtils.isEmpty(contactList)){
      List<CaseContact> contacts = contactList.stream()
          .filter(temp -> temp.getContactMobile().equals(detailVO.getPhone()))
          .collect(Collectors.toList());
      if(contacts.size()==1){
        caseAutoPlanDetailVO.setCallName(contacts.get(0).getContactName());
        caseAutoPlanDetailVO.setContactTypeId(contacts.get(0).getContactTypeId());
        caseAutoPlanDetailVO.setContactTypeName(contacts.get(0).getContactTypeName());
      }
    }
    caseAutoPlanDetailVO.setCallMobile(detailVO.getPhone());
    caseAutoPlanDetailVO.setCaller(detailVO.getCaller());
    caseAutoPlanDetailVO.setDuration(detailVO.getDuration());
    caseAutoPlanDetailVO.setCallTime(detailVO.getCall_time());
    return caseAutoPlanDetailVO;
  }

  public String getBindingMapping(long duyanPlanId, String tag) {
    String comment = "";
    CasePlan casePlan = casePlanMapper.selectCasePlanByPlanId(duyanPlanId);
    if (casePlan != null) {
      List<SiteRelative> list =
          siteRelativeService.selectSiteRelativeBySiteId(casePlan.getSiteId());
      if (list != null && list.size() > 0) {
        for (SiteRelative temp : list) {
          if (temp.getTag().equalsIgnoreCase(tag)) {
            comment = temp.getComment();
          }
        }
      }
    }
    return comment;
  }

  /**
   * 查询机器人计划列表
   *
   * @param casePlanParam 查询参数
   * @param pageParam
   * @return
   */
  public PageOutput<CasePlanVO> queryList(CasePlanParam casePlanParam, PageParam pageParam) {
    Integer robotType = casePlanParam.getRobotType();
    AssertUtil.isTrue(CasePlanEnums.Type.isExists(robotType),"计划类型非法");

    List<String> duyanPlanStatusList =
        Lists.newArrayList("UPLOADING", "NOT_STARTED", "IN_PROGRESS", "PAUSED");
    String planStatus = casePlanParam.getPlanStatus();
    if (StringUtils.isNotBlank(planStatus) && duyanPlanStatusList.contains(planStatus)) {
      // 需到度言查找计划信息 再过滤 再分页
      return queryByDuyanStatus(casePlanParam, pageParam, casePlanParam.getRobotType());
    }
    Example example = buildRobotPlanExample(casePlanParam, planStatus, casePlanParam.getRobotType());
    PageOutput pageOutput = super.selectByPage(example, pageParam);
    List<CasePlan> casePlanList = pageOutput.getList();
    if (CollectionUtils.isEmpty(casePlanList)) {
      return pageOutput;
    }
    // 除已完成的案件外，其他案件状态需到度言进行搜索
    List<Long> duyanPlanIds =
        casePlanList.stream()
            .filter(c -> c.getStatus().equals(CasePlanEnums.Status.ING.getCode())
                ||c.getStatus().equals(CasePlanEnums.Status.FINISHED.getCode()))
            .map(CasePlan::getDuyanPlanId)
            .collect(Collectors.toList());
    List<CasePlanTmp> casePlanTmpList = manager.getPlanListByIds(duyanPlanIds, casePlanParam.getRobotType());
    Map<Long, CasePlanTmp> casePlanTmpMap =
        casePlanTmpList.stream().collect(Collectors.toMap(CasePlanTmp::getDuyanPlanId, r -> r));
    List<CasePlanVO> vos = new ArrayList<>();
    for (CasePlan casePlan : casePlanList) {
      Long duyanPlanId = casePlan.getDuyanPlanId();
      CasePlanTmp casePlanTmp = casePlanTmpMap.get(duyanPlanId);
      CasePlanVO vo = convertRobotPlanVO(casePlan, casePlanTmp);
      vos.add(vo);
    }
    pageOutput.setList(vos);
    return pageOutput;
  }

  private Example buildRobotPlanExample(CasePlanParam casePlanParam, String planStatus, Integer robotType) {
    UserSession userSession = getTokenUser();
    Example example = new Example(CasePlan.class);
    example.setOrderByClause("id desc");
    Example.Criteria planCrt = example.createCriteria();
    planCrt
        .andEqualTo("type", robotType)
        .andEqualTo("orgId", userSession.getOrgId());
    if (StringUtils.isNotBlank(planStatus)) {
      planCrt.andEqualTo("status", CasePlanEnums.Status.valueOf(planStatus).getCode());
    }
    if (!StringUtils.isBlank(casePlanParam.getUserIds())) {
      planCrt.andIn("userId", Arrays.asList(casePlanParam.getUserIds().split(",")));
    }
    if (casePlanParam.getPlanCreatedDate() != null
        && casePlanParam.getPlanCreatedDate().split(",").length == 2) {
      casePlanParam.setPlanCreatedDateStart(
          convertDate(casePlanParam.getPlanCreatedDate().split(",")[0]));
      casePlanParam.setPlanCreatedDateEnd(
          convertDate(casePlanParam.getPlanCreatedDate().split(",")[1]));
      planCrt.andGreaterThanOrEqualTo("createTime", casePlanParam.getPlanCreatedDateStart());
      planCrt.andLessThanOrEqualTo("createTime", casePlanParam.getPlanCreatedDateEnd());
    }
    if (UserUtils.likeBranchAdmin()) {
      planCrt.andEqualTo("depId", userSession.getDepId());
    }
    if (StringUtils.isNotBlank(casePlanParam.getPriority())) {
      planCrt.andEqualTo("priority", casePlanParam.getPriority());
    }
    return example;
  }

  private String convertDate(String date) {
    if (!CommonUtil.isEmpty(date) && CommonUtil.isInteger(date)) {
      return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.valueOf(date)));
    }
    // 是否是标准时间格式，否则返回空
    if (!CommonUtil.isValidDate(date)) {
      return null;
    }
    return date;
  }

  private CasePlanVO convertRobotPlanVO(CasePlan casePlan, CasePlanTmp casePlanTmp) {
    CasePlanVO vo = new CasePlanVO();
    BeanUtils.copyProperties(casePlan, vo);
    if (casePlanTmp != null) {
      vo.setProgress(casePlanTmp.getFinishedCount() + "/" + casePlanTmp.getTotalCount());
      vo.setPlanStatus(casePlanTmp.getPlanStatus()); // 计划状态
      Long roundCount = ObjectUtil.isNull(casePlanTmp.getRetryCount())?1L:casePlanTmp.getRetryCount()+1;
      vo.setNowRounds(casePlanTmp.getNowRounds());
      vo.setRoundCount(roundCount);
      vo.setFinishedCount(casePlanTmp.getFinishedCount());
      vo.setNowRoundCount(casePlanTmp.getNowRoundCount());
      // 计划已完成或已取消，同步到安米
      judgeFinishedOrCancelled(casePlan.getId(), casePlanTmp);
    } else {
      vo.setProgress(casePlan.getFinishedCount() + "/" + casePlan.getTotalCount());
      vo.setPlanStatus(
          EnumUtils.getByCode(casePlan.getStatus(), CasePlanEnums.Status.class).name()); // 计划状态
    }
    vo.setValidTotal(casePlan.getCaseTotal()); // 有效数量
    vo.setSelectedTotal(casePlan.getSelectedTotal()); // 选择数量
    vo.setCreateBy(userService.getNames(casePlan.getOrgId()).get(casePlan.getCreateBy()));
    vo.setUpdateBy(userService.getNames(casePlan.getOrgId()).get(casePlan.getUpdateBy()));
    vo.setPriority(casePlan.getPriority()); // 优先级
    vo.setName(casePlan.getName()); // 计划名称
    vo.setSiteName(casePlan.getSiteName()); // 话术名称
    vo.setDesc(casePlan.getDesc());
    vo.setCreatedUserName(userService.getNames(casePlan.getOrgId()).get(casePlan.getCreateBy()));
    return vo;
  }

  public SiteRelativeVO siteRelativeQuery(long siteId) {
    SiteRelativeVO siteRelativeVO = new SiteRelativeVO();
    List<SiteRelative> list = siteRelativeService.selectSiteRelativeBySiteId(siteId);
    if (!CollectionUtils.isEmpty(list)) {
      List<TagAndCommentBO> collect =
          list.stream()
              .map(temp -> new TagAndCommentBO(temp.getTag(), temp.getComment()))
              .collect(Collectors.toList());
      siteRelativeVO.setSiteId(list.get(0).getSiteId());
      siteRelativeVO.setSiteName(list.get(0).getSiteName());
      siteRelativeVO.setComments(collect);
    }
    return siteRelativeVO;
  }

  @Transactional
  public void addSiteRelative(SiteRelativeFO fo) {
    List<SiteRelative> list = Lists.newArrayList();
    List<TagAndCommentBO> comments = fo.getComments();
    for (TagAndCommentBO bo : comments) {
      SiteRelative siteRelative = new SiteRelative();
      siteRelative.setTag(bo.getTag());
      siteRelative.setComment(bo.getComment());
      siteRelative.setSiteId(fo.getSiteId());
      siteRelative.setSiteName(fo.getSiteName());
      list.add(siteRelative);
    }
    siteRelativeService.delete(fo.getSiteId());
    siteRelativeService.insert(list);
  }

  public CasePlan selectCasePlanByDuyanPlanId(long campaignId) {
    return casePlanMapper.selectCasePlanByPlanId(campaignId);
  }

  /**
   * 根据度言计划id列表查询计划基本信息
   *
   * @param campaignIds 度言计划id列
   * @return
   */
  public List<CasePlan> selectCasePlanByDuyanPlanIds(List<Long> campaignIds) {
    if (CollectionUtils.isEmpty(campaignIds)) {
      return Collections.emptyList();
    }
    Example example = new Example(CasePlan.class);
    example.and().andIn("duyanPlanId", campaignIds);
    return casePlanMapper.selectByExample(example);
  }

  /**
   * 异步创建计划
   *
   * @param caseMultiQuery 案件查询条件
   * @param casePlan       案件计划
   * @throws Exception exception
   */
  public void asyncAddCasePlan(CaseMultiQuery caseMultiQuery, CasePlan casePlan) throws Exception {
    // 只获取部分数据
    caseMultiQuery.setSelectField("ca.id,ca.own_mobile");
    // 区分案件智能"CaseVO.fieldJson")计划与普通案件列表的标记，0-智能计划
    caseMultiQuery.setIsPlan(0);
    // 过滤不参与计划案件的时间，当天时间+00:00:00
    caseMultiQuery.setIgnorePlanTime(
        DateUtils.formatDateFull(DateUtils.getStartTimeOfDate(new Date())));
    List<Integer> caseStatusList = ImmutableList.of(CaseEnums.CaseStatus.NORMAL.getCode(), CaseEnums.CaseStatus.DELAY.getCode());
    if (!Objects.isNull(caseMultiQuery.getCaseStatues())) {
      //如果界面查询传参已经包含了案件状态的条件，则取交集(fuck)
      List<Integer> queryCaseStatusList = Arrays.stream(caseMultiQuery.getCaseStatues().split(",")).map(Integer::valueOf).collect(Collectors.toList());
      queryCaseStatusList.retainAll(caseStatusList);
      if (CollectionUtils.isEmpty(queryCaseStatusList)) {
        throw new ApiException("查询结果为空");
      }
      caseMultiQuery.setCaseStatues(StringUtils.join(queryCaseStatusList,","));
    } else {
      caseMultiQuery.setCaseStatues(StringUtils.join(caseStatusList,","));
    }
    if (ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.FIFO.getCode()) || ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.PERSONALFIFO.getCode())) {
      List<Integer> allotStatusList = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode(), CaseEnums.AllotStatus.ALLOT_TEAM.getCode());
      if (!Objects.isNull(caseMultiQuery.getAllotStatues())) {
        //如果界面查询传参已经包含了案件分配状态的条件，则取交集
        List<Integer> queryAllotStatusList = Arrays.stream(caseMultiQuery.getAllotStatues().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        queryAllotStatusList.retainAll(allotStatusList);
        if (CollectionUtils.isEmpty(queryAllotStatusList)) {
          throw new ApiException("查询结果为空");
        }
        caseMultiQuery.setAllotStatues(StringUtils.join(queryAllotStatusList,","));
      } else {
        caseMultiQuery.setAllotStatues(StringUtils.join(allotStatusList,","));
      }
    }

    List<CaseQueryResult> caseList = selectList(caseMultiQuery);

      // 创建异步任务
      casePlanMapper.insertSelective(casePlan);
    // 任务添加到任务列表
      stringRedisTemplate
          .opsForSet()
          .add(
              KeyCache.CASE_PLAN_ROBOT_CASES + casePlan.getId(),
              caseList.stream().map(temp -> String.valueOf(temp.getId())).toArray(String[]::new));
      stringRedisTemplate
          .opsForList()
          .leftPush(KeyCache.CASE_PLAN_ROBOT_PLAN_ID_LIST, casePlan.getId().toString());
  }

  /**
   * 异步创建计划(案件查询)
   *
   * @param caseMultiQuery 查询条件
   * @return {@link List}<{@link CaseQueryResult}>
   * @throws Exception 例外
   */
  private List<CaseQueryResult> selectList(CaseMultiQuery caseMultiQuery) throws Exception {
    if (caseMultiQuery.getBeAmc()){
      caseService.listCaseAmcParamDeal(caseMultiQuery);
    } else {
      caseService.listCaseAnmiParamDeal(caseMultiQuery);
    }

    List<CaseQueryResult> caseQueryResults;
    if (systemConfig.getESSwitch()) {
      //如果有es就走es查询，解决慢sql问题
      caseMultiQuery.setIgnorePlanTime(String.valueOf(DateUtils.getStartTimeOfDate(new Date()).getTime()));
      caseMultiQuery.setFields(Lists.newArrayList("id", "ownMobile"));
      caseQueryResults = caseService.getAllCasesUsingEs(caseMultiQuery);
    } else {
      caseQueryResults = caseService.queryResultForMulti(caseMultiQuery);
    }
    log.info("批量添加案件智能计划（异步）搜索结束,数量:{}", caseQueryResults.size());

    if (CommonUtils.isEmpty(caseQueryResults)) {
      throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
    }
    return caseQueryResults;
  }

  @Transactional(rollbackFor = Exception.class)
  public void addMessagePlan(CaseMessageFO caseMessageFO) throws Exception {
    if (caseMessageFO.getCaseMultiQuery() == null || caseMessageFO.getCaseMessageParam() == null) {
      throw new ApiException("传入参数错误");
    }
    UserSession userSession = UserUtils.getTokenUser();
    CaseMultiQuery caseMultiQuery = caseMessageFO.getCaseMultiQuery();
    if (caseMultiQuery.getCaseCount() == null) {
      throw new ApiException("案件选择数量参数传入错误");
    }

    CasePlan casePlan = new CasePlan();
    BeanUtils.copyProperties(caseMessageFO.getCaseMessageParam(), casePlan);
    casePlan.setSelectedTotal(caseMultiQuery.getCaseCount());
    // 小组长可以看到包含该小组的所有计划，包括总公司和分公司创建的
    casePlan.setOrgId(userSession.getOrgId());
    casePlan.setDepId(userSession.getDepId());
    casePlan.setTeamId(userSession.getTeamId());
    casePlan.setCreateBy(userSession.getId());
    casePlan.setUpdateBy(userSession.getId());
    casePlan.setType(CasePlanEnums.Type.MESSAGE.getCode());
    OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(userSession.getOrgId());
    // 搜索条件
    caseMultiQuery.setOrgIds(userSession.getOrgId().toString());
    if (UserUtils.likeBranchAdmin(userSession.getRoleId())) {
      // 如果是分公司管理员，进行分公司隔离
      caseMultiQuery.setDepId(userSession.getDepId());
    } else if (UserUtils.likeTeamLeader(userSession.getRoleId())) {
      caseMultiQuery.setTeamIds(userSession.getTeamId().toString());
    }
    // 只有案件状态是分配完成、留案、分配至组才可以创建计划
    List<Integer> caseStatusList =  ImmutableList.of(CaseEnums.CaseStatus.DELAY.getCode(),CaseEnums.CaseStatus.NORMAL.getCode());
    caseMultiQuery.setCaseStatues(org.apache.commons.lang.StringUtils.join(caseStatusList, ","));
    // 过滤不参与计划案件
    caseMultiQuery.setIgnorePlan(CaseEnums.IgnorePlan.NO.getCode());
    //        // 判断选择数量与短信剩余条数
    //        MessageAccountDTO accountInfo = messageManager.getAccountInfo();
    //        if (accountInfo == null) {
    //            throw new ApiException("该公司不存在剩余短信金额，请联系管理员");
    //        }
    //        BigDecimal price = new BigDecimal(accountInfo.getPrice());
    //        BigDecimal leftAmount = new BigDecimal(accountInfo.getLeftAmount());
    //        BigDecimal bigDecimal = leftAmount.divideToIntegralValue(price);
    //        long leftMessageTotal = bigDecimal.longValue();
    //        if (caseMultiQuery.getCaseCount() > leftMessageTotal) {
    //            throw new ApiException("短信剩余条数不足，创建计划失败");
    //        }
    if (caseMultiQuery.getAllSelect()) {
      // 全部操作
      casePlan.setStatus(CasePlanEnums.Status.NEW.getCode());
      casePlanMapper.insertSelective(casePlan);
      asyncAddCaseMessage(caseMultiQuery, casePlan, orgSwitch);
    } else {
      // 区分案件智能计划与普通案件列表的标记，0-智能计划
      caseMultiQuery.setIsPlan(0);
      // 过滤不参与计划案件的时间，当天时间+00:00:00
      caseMultiQuery.setIgnorePlanTime(
          DateUtils.formatDateFull(DateUtils.getStartTimeOfDate(new Date())));
      List<CaseQueryResult> caseList = caseService.queryResultForMulti(caseMultiQuery);
      // 过滤掉符合全局管控短信限制的案件
      filterGlobalCtrlSmsCase(caseList, orgSwitch);
      if (CollectionUtils.isEmpty(caseList)) {
        throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
      }
      //            // 案件催收结果为承诺还款且未到达承诺还款时间不参与计划
      //            List<Long> searchCaseIds = caseList.stream().map(c ->
      // c.getId()).collect(Collectors.toList());
      //            Map<String, Object> map = new HashMap<>();
      //            map.put("caseIds", searchCaseIds);
      //            List<Long> caseIds = caseOperationService.selectWithPtpTime(map);
      //            caseList = caseList.stream().filter(temp ->
      // !caseIds.contains(temp.getId())).collect(Collectors.toList());
      //            if (CollectionUtils.isEmpty(caseList)) {
      //                throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
      //            }
      casePlan.setStatus(CasePlanEnums.Status.ING.getCode());
      createMessage(caseList, casePlan,UserUtils.getTokenUser(), orgSwitch);
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void addFifoPlan(CaseFifoFO caseFifoFO) throws Exception {
    if (caseFifoFO.getCaseMultiQuery() == null || caseFifoFO.getCaseFifoParam() == null) {
      throw new ApiException("传入参数错误");
    }
    UserSession userSession = UserUtils.getTokenUser();
    CaseMultiQuery caseMultiQuery = caseFifoFO.getCaseMultiQuery();
    if (caseMultiQuery.getCaseCount() == null) {
      throw new ApiException("案件选择数量参数传入错误");
    }
    Long teamId = null;
    if (caseMultiQuery.getTeamIds() != null && caseMultiQuery.getTeamIds().split(",").length != 1) {
      throw new ApiException("仅可选择一个小组创建预测式外呼计划！");
    }
    if (caseMultiQuery.getTeamIds() != null) {
      teamId = Long.valueOf(caseMultiQuery.getTeamIds());
    } else {
      teamId = userSession.getTeamId();
    }
    // 创建团队预测式外呼计划选择转机器人接入，需要进行话术变量校验
    List<SiteVarsMapping> duyanVarsMapping = new ArrayList<>();
    if (ObjectUtil.equals(caseFifoFO.getCaseFifoParam().getFifoToRobotEnabled(), CasePlanEnums.FifoToRobotEnabled.OPEN.getCode())) {
      duyanVarsMapping = checkSiteVars(caseFifoFO.getCaseFifoParam().getSiteId(), userSession.getOrgId());
    }
    CasePlan casePlan = new CasePlan();
    BeanUtils.copyProperties(caseFifoFO.getCaseFifoParam(), casePlan);
    casePlan.setSelectedTotal(caseMultiQuery.getCaseCount());
    // 小组长可以看到包含该小组的所有计划，包括总公司和分公司创建的
    casePlan.setOrgId(userSession.getOrgId());
    casePlan.setDepId(userSession.getDepId());
    casePlan.setTeamId(teamId);
    casePlan.setCreateBy(userSession.getId());
    casePlan.setUpdateBy(userSession.getId());
    casePlan.setType(CasePlanEnums.Type.FIFO.getCode());
    casePlan.setContacts(caseFifoFO.getCaseFifoParam().getContacts());
    casePlan.setDebtorOwn(caseFifoFO.getCaseFifoParam().getDebtorOwn());
    // 搜索条件
    caseMultiQuery.setOrgIds(userSession.getOrgId().toString());
    caseMultiQuery.setTeamIds(teamId.toString());
    // 只有案件状态是分配完成、留案、分配至组才可以创建计划
    List<Integer> allotStatusList = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_TEAM.getCode(), CaseEnums.AllotStatus.ALLOT_USER.getCode());
    List<Integer> caseStatusList =  ImmutableList.of(CaseEnums.CaseStatus.DELAY.getCode(),CaseEnums.CaseStatus.NORMAL.getCode());
    caseMultiQuery.setAllotStatues(org.apache.commons.lang.StringUtils.join(allotStatusList, ","));
    caseMultiQuery.setCaseStatues(org.apache.commons.lang.StringUtils.join(caseStatusList, ","));
    // 过滤不参与计划案件
    caseMultiQuery.setIgnorePlan(CaseEnums.IgnorePlan.NO.getCode());
    // 查询公司全局管控开关是否开启
    OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(userSession.getOrgId());
    if (caseMultiQuery.getAllSelect()) {
      // 全部操作
      casePlan.setStatus(CasePlanEnums.Status.NEW.getCode());
      casePlanMapper.insertSelective(casePlan);
      asyncAddCaseFifo(caseMultiQuery, casePlan, orgSwitch);
    } else {
      // 区分案件智能计划与普通案件列表的标记，0-智能计划
      caseMultiQuery.setIsPlan(0);
      // 过滤不参与计划案件的时间，当天时间+00:00:00
      caseMultiQuery.setIgnorePlanTime(
          DateUtils.formatDateFull(DateUtils.getStartTimeOfDate(new Date())));
      List<CaseQueryResult> caseList = caseService.queryResultForMulti(caseMultiQuery);
      // 过滤掉符合全局管控电催案件限制的案件
      filterGlobalCtrlCase(caseList, orgSwitch);
      if (CollectionUtils.isEmpty(caseList)) {
        throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
      }
      casePlan.setStatus(CasePlanEnums.Status.ING.getCode());
      createFifo(caseList, casePlan, orgSwitch, duyanVarsMapping);
    }
  }

    @Transactional(rollbackFor = Exception.class)
    public void createFifo(List<CaseQueryResult> list, CasePlan casePlan, OrgSwitch orgSwitch, List<SiteVarsMapping> duyanVarsMappings) {
        List<Long> caseIds = list.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
        List<CaseContact> caseContactsAll = contactsMapper.selectContactByCaseIds(caseIds);
        List<CaseContact> caseContacts = Lists.newArrayList();
        if (casePlan.getDebtorOwn() != null && casePlan.getDebtorOwn() == 1) {
            //过滤掉不符合规则手机号
            List<CaseContact> ownList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 1)
                    .filter(caseContact -> PatternUtils.isPlanPhone(caseContact.getContactMobile()))
                    .collect(Collectors.toList());

          // 最新本人号码
          if (casePlan.getDebtorOwnType() != null &&
                  Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.LAST.getCode())) {
            Map<Long, CaseContact> caseContactMap = ownList.stream().collect(Collectors.groupingBy(
                    CaseContact::getCaseId, Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(CaseContact::getConUpdateTime)), Optional::get)));
            ownList.clear();
            ownList.addAll(caseContactMap.values());
          }
          // 随机本人号码
          if (casePlan.getDebtorOwnType() != null &&
                  Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.RANDOM.getCode())) {
            List<CaseContact> randomCaseContacts = Lists.newArrayList();
            ownList.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> randomCaseContacts.add(v.get(0)));
            ownList.clear();
            ownList.addAll(randomCaseContacts);
          }
          caseContacts.addAll(ownList);
        }
        if (casePlan.getContacts() != null && casePlan.getContacts() == 1) {
            List<CaseContact> contactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 0)
                    .filter(caseContact -> PatternUtils.isPlanPhone(caseContact.getContactMobile()))
                    .collect(Collectors.toList());
            // 除本人外的所有号码类型
            if (StringUtils.isNotBlank(casePlan.getContactTypeIds())) {
              String contactTypeIdStr = casePlan.getContactTypeIds();
              List<Long> contactTypeIds = Stream.of(contactTypeIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
              if (contactTypeIds.size() > 1) {
                contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
              }
              if (contactTypeIds.size() == 1) {
                if (!Objects.equals(contactTypeIds.get(0), CasePlanEnums.ContactTypeEnum.ALL.getCode())) {
                  contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
                }
              }
            }
            caseContacts.addAll(contactsList);
        }

        // 过滤掉符合全局管控电催号码限制的案件号码
      List<CaseContact> delContact = getMobileLimitContact(caseContacts, orgSwitch);
      if (!CollectionUtils.isEmpty(delContact)) {
        caseContacts.removeAll(delContact);
      }

      // 管控池管控 管控联系人、本人拨打频率
      List<CaseContact> ctrlContacts = getCaseCtrlLimit(caseContacts, orgSwitch);
      if (!CollectionUtils.isEmpty(ctrlContacts)) {
        caseContacts.removeAll(ctrlContacts);
      }

      // 先按手机号分组选择案件
      List<CaseContact> param1 = Lists.newArrayList();
      // 根据案件筛选出第一个手机号联系人
      if (!CollectionUtils.isEmpty(caseContacts)) {
          caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> {
                  param1.add(v.get(0));
          });
      }
      // 按案件分组选择第一个手机号
      List<CaseContact> param = Lists.newArrayList();
      // 相同手机号，随机选其中一个案子参与计划
      if (!CollectionUtils.isEmpty(caseContacts)) {
          param1.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile)).forEach((k, v) -> {
                  param.add(v.get(0));
          });
      }
      if(CollectionUtils.isEmpty(param)){
        throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
      }
      // 创建团队预测式外呼计划的body
      List<Map<String, Object>> contents = new ArrayList<>();
      if (ObjectUtil.equals(casePlan.getFifoToRobotEnabled(), CasePlanEnums.FifoToRobotEnabled.OPEN.getCode())) {
        Iterator<CaseContact> iterator = param.iterator();
        while (iterator.hasNext()) {
          CaseContact caseContact = iterator.next();
          Map<String, Object> caseContactMap = BeanUtils.beanToMap(caseContact);
          Long caseId = caseContact.getCaseId();
          Optional<CaseQueryResult> caseResultOptional = list.stream().filter(CaseQueryResult -> ObjectUtil.equals(caseId, CaseQueryResult.getId())).findFirst();
          CaseQueryResult caseQueryResult = caseResultOptional.get();
          Map<String, String> fieldJsonMap = caseQueryResult.getFieldJson();

          boolean varValueCheck = duyanVarsMappings.stream().anyMatch(varMapping -> {
            String anmiVar = varMapping.getAnmiVar();
            SiteEnums.Var varEmum = SiteEnums.Var.getByAnmiVar(anmiVar);
            Object value = null;
            if (ObjectUtil.isNull(varEmum)){
              value = fieldJsonMap.get(anmiVar);
            } else {
              String source = varEmum.getSource();
              String key = StringUtils.substringAfterLast(source, ".");

              if (source.startsWith("CaseContact")){
                value = caseContactMap.get(key);
              }else if (source.startsWith("CaseVO.fieldJson")){
                value = fieldJsonMap.get(key);
              }
            }
            return ObjectUtil.isNull(value);
          });

          if (varValueCheck){
            log.info("话术变量缺失,剔除,案件:{},手机号:{},contact_id:{}",caseId,caseContact.getContactMobile(),caseContact.getContactId());
            iterator.remove();
          }
        }
        // 变量过滤、全局管控过滤可能导致案件号码为空
        if(CollectionUtils.isEmpty(param)){
          throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
        }
        if (ObjectUtil.isNotNull(param)) {
          for (CaseContact caseContact : param) {
            Long caseId = caseContact.getCaseId();
            Optional<CaseQueryResult> CaseQueryResultOptional = list.stream().filter(CaseQueryResult -> ObjectUtil.equals(caseId, CaseQueryResult.getId())).findFirst();
            CaseQueryResult caseQueryResult = CaseQueryResultOptional.get();
            Map<String, String> fieldJsonMap = caseQueryResult.getFieldJson();
            addContent(caseContact,casePlan.getOrgId(),fieldJsonMap, contents,duyanVarsMappings, casePlan);
          }
        }
      }
      DepTeam depTeam = depTeamService.selectByPrimaryKey(casePlan.getTeamId());
      Long duyanPlanId = manager.createNewFifo(param, casePlan, depTeam.getDuyanReferId(), contents);
      if (duyanPlanId == null) {
          throw new ApiException("调用创建预测式计划接口失败");
      }
      casePlan.setDuyanPlanId(duyanPlanId);
      casePlanMapper.insertSelective(casePlan);
      List<Long> caseList=param.stream().map(CaseContact::getCaseId).collect(Collectors.toList());
      // 计划与案件关联
      casePlanRelService.saveCasePlanRelBatch(caseList, casePlan.getId(), getTokenUser(), CasePlanRelEnums.Type.FIFO.getCode());
      createCasePlanRelContact(casePlan,caseContacts, param.stream().map(CaseContact::getContactMobile).collect(Collectors.toList()));

    }
    @Transactional(rollbackFor = Exception.class)
    public void createCasePlanRelContact(CasePlan casePlan,List<CaseContact> caseContacts,List<String> mobileList){
      Example example=new Example(CasePlanRel.class);
      example.and().andEqualTo("planId",casePlan.getId());
      List<CasePlanRel> casePlanRelList=casePlanRelService.selectByExample(example);
      List<CasePlanRelContact> casePlanRelContactList= Lists.newArrayList();
      for(CasePlanRel casePlanRel:casePlanRelList){
        List<CaseContact> caseContactList = caseContacts.stream().filter(obj->obj.getCaseId().equals(casePlanRel.getCaseId())).collect(Collectors.toList());
        caseContactList.forEach(obj->{
          CasePlanRelContact casePlanRelContact=new CasePlanRelContact();
          casePlanRelContact.setCasePlanId(casePlan.getId());
          casePlanRelContact.setContactId(obj.getContactId());
          casePlanRelContact.setCasePlanRelId(casePlanRel.getId());
          casePlanRelContact.setCreateTime(new Date());
          casePlanRelContact.setUpdateTime(new Date());
          casePlanRelContact.setPhoneNum(obj.getContactMobile());
          int callFlag=0;
          if(mobileList.contains(obj.getContactMobile())){
            callFlag=1;
          }
          casePlanRelContact.setCallFlag(callFlag);
          casePlanRelContact.setOwnFlag(obj.getOwnSign());
          casePlanRelContactList.add(casePlanRelContact);
        });
      }
      casePlanRelContactService.insertBatch(casePlanRelContactList);
    }

  @Transactional(rollbackFor = Exception.class)
  public void createMessage(List<CaseQueryResult> list, CasePlan casePlan, UserSession userSession, OrgSwitch orgSwitch) {
    List<Long> caseIdList = list.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
    List<CaseContact> caseContactsList = contactsMapper.selectContactByCaseIds(caseIdList);

    List<CaseContact> caseContacts = Lists.newArrayList();
    // 选择债务人本人
    if (casePlan.getDebtorOwn() != null && casePlan.getDebtorOwn() == 1) {
      List<CaseContact> ownContactsList = caseContactsList.stream().filter(obj-> obj.getOwnSign() == 1)
              .filter(contacts -> PatternUtils.isPlanMessage(contacts.getContactMobile()))
              .collect(Collectors.toList());

      // 最新本人号码
      if (casePlan.getDebtorOwnType() != null &&
              Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.LAST.getCode())) {
        Map<Long, CaseContact> caseContactMap = ownContactsList.stream().collect(Collectors.groupingBy(
                CaseContact::getCaseId, Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(CaseContact::getConUpdateTime)), Optional::get)));
        ownContactsList.clear();
        ownContactsList.addAll(caseContactMap.values());
      }
      // 随机本人号码
      if (casePlan.getDebtorOwnType() != null &&
              Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.RANDOM.getCode())) {
        List<CaseContact> randomCaseContacts = Lists.newArrayList();
        ownContactsList.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> randomCaseContacts.add(v.get(0)));
        ownContactsList.clear();
        ownContactsList.addAll(randomCaseContacts);
      }
      caseContacts.addAll(ownContactsList);
    }
    if (casePlan.getContacts() != null && casePlan.getContacts() == 1) { // 如果选择案件联系人
      List<CaseContact> contactsList = caseContactsList.stream().filter(obj -> obj.getOwnSign() == 0)
              .filter(caseContact -> PatternUtils.isPlanMessage(caseContact.getContactMobile()))
              .collect(Collectors.toList());
      // 除本人外的所有号码类型
      if (StringUtils.isNotBlank(casePlan.getContactTypeIds())) {
        String contactTypeIdStr = casePlan.getContactTypeIds();
        List<Long> contactTypeIds = Stream.of(contactTypeIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
        if (contactTypeIds.size() > 1) {
          contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
        }
        if (contactTypeIds.size() == 1) {
          if (!Objects.equals(contactTypeIds.get(0), CasePlanEnums.ContactTypeEnum.ALL.getCode())) {
            contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
          }
        }
      }
      caseContacts.addAll(contactsList);
    }

    // 全局管控-获取已达案件号码短信发送成功上限的号码信息，进行移除
    List<CaseContact> delContacts = getMobileSmsLimitContact(caseContacts, orgSwitch);
    if (!CollectionUtils.isEmpty(delContacts)) {
      caseContacts.removeAll(delContacts);
    }

    // 按手机号分组，随机选择一个案件
    List<CaseContact> param = Lists.newArrayList();
    if (!CollectionUtils.isEmpty(caseContacts)) {
      caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile))
              .forEach((k, v) -> param.add(v.get(0)));
    }

    if(CollectionUtils.isEmpty(param)){
      throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
    }

    SmsTemplate smsTemplate = smsTemplateService.selectByPrimaryKey(casePlan.getSmsTplId());
    JSONArray jsonArray = getAssembleJsonArray(casePlan, param, smsTemplate);

    // 添加计划
    Long duyanPlanId =
        messageManager.createMessagePlan(
            jsonArray, smsTemplate, casePlan.getStartType(), casePlan.getPlanStartTime(),userSession.getOrgId());

    if (duyanPlanId == null) {
      throw new ApiException("调用创建短信计划接口失败");
    }
    casePlan.setDuyanPlanId(duyanPlanId);
    casePlanMapper.insertSelective(casePlan);
    assembleData(jsonArray, param, casePlan, smsTemplate);
    casePlanRelService.saveCasePlanRelBatch(
        param.stream().map(CaseContact::getCaseId).distinct().collect(Collectors.toList()),
        casePlan.getId(), userSession, CasePlanRelEnums.Type.MESSAGE.getCode());
  }

  public JSONArray getAssembleJsonArray(
      CasePlan casePlan, List<CaseContact> list, SmsTemplate smsTemplate) {
    JSONArray jsonArray = new JSONArray();

    if (CollectionUtils.isEmpty(list)) {
      throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
    }

    for (CaseContact caseContact : list) {
      JSONObject jsonObject = new JSONObject();
      jsonObject.put("caseId", caseContact.getCaseId());
      jsonObject.put("relationMobile", caseContact.getContactMobile());
      jsonObject.put("uuid", UUID.randomUUID());
      jsonObject.put("relation", caseContact.getRelationType());
      // 号码类型
      jsonObject.put("contactTypeId", caseContact.getContactTypeId());
      jsonObject.put("contactName", caseContact.getContactName());
      jsonObject.put(
              "extParam", smsSendService.getExtParam(smsTemplate.getVarJson(), caseContact, casePlan.getContact()));
      jsonArray.add(jsonObject);
    }
    return jsonArray;
  }

  public void assembleData(
      JSONArray jsonArray,
      List<CaseContact> param,
      CasePlan casePlan,
      SmsTemplate smsTemplate) {
    Map<Long, List<CaseContact>> collect = param.stream().collect(Collectors.groupingBy(CaseContact::getCaseId));
    List<SmsSend> smsSendList = Lists.newArrayList();
    for (int i = 0; i < jsonArray.size(); i++) {
      if (collect.containsKey(jsonArray.getJSONObject(i).getLong("caseId"))) {
        CaseContact caseContact = collect.get(jsonArray.getJSONObject(i).getLong("caseId")).get(0);
        SmsSend smsSend = new SmsSend();
        smsSend.setOrgId(casePlan.getOrgId());
        smsSend.setDepId(casePlan.getDepId());
        smsSend.setTeamId(casePlan.getTeamId());
        smsSend.setCaseId(caseContact.getCaseId());
        smsSend.setPlanId(casePlan.getId());
        smsSend.setUuid(jsonArray.getJSONObject(i).getString("uuid"));
        smsSend.setOrgDeltId(caseContact.getOrgDeltId());
        smsSend.setPlanName(casePlan.getName());
        smsSend.setContactName(jsonArray.getJSONObject(i).getString("contactName"));
        smsSend.setRelation(jsonArray.getJSONObject(i).getString("relation"));
        smsSend.setRelationMobile(jsonArray.getJSONObject(i).getString("relationMobile"));
        smsSend.setContactTypeId(jsonArray.getJSONObject(i).getLong("contactTypeId"));
        smsSend.setOutSerialTemp(caseContact.getOutSerialTemp());
        smsSend.setProductId(caseContact.getProductId());
        smsSend.setOutBatchId(caseContact.getOutBatchId());
        smsSend.setSendStatus(-1); // 初始状态 发送中
        smsSend.setReceiveStatus(-1); // 初始状态 接收中
        smsSend.setCreateTime(new Date());
        smsSend.setUpdateTime(new Date());
        smsSend.setCreateBy(casePlan.getCreateBy());
        smsSend.setUpdateBy(casePlan.getUpdateBy());
        smsSend.setStatus(0);
        smsSend.setSmsTplId(smsTemplate.getId());
        smsSend.setFee(new BigDecimal("0"));
        smsSend.setCount(0);
        smsSendList.add(smsSend);
      }
    }
    smsSendService.insertBatch(smsSendList);
  }

  public void asyncAddCaseFifo(CaseMultiQuery caseMultiQuery, CasePlan casePlan, OrgSwitch orgSwitch) throws Exception {
    // 只获取部分数据
    caseMultiQuery.setSelectField("ca.id,ca.own_mobile,ca.org_delt_id,ca.team_id");
    // 区分案件智能计划与普通案件列表的标记，0-智能计划
    caseMultiQuery.setIsPlan(0);
    // 过滤不参与计划案件的时间，当天时间+00:00:00
    caseMultiQuery.setIgnorePlanTime(
        DateUtils.formatDateFull(DateUtils.getStartTimeOfDate(new Date())));
    List<CaseQueryResult> caseList = new ArrayList<>();
    if (systemConfig.getESSwitch()) {
      //如果有es就走es查询，解决慢sql问题
      caseMultiQuery.setIgnorePlanTime(String.valueOf(DateUtils.getStartTimeOfDate(new Date()).getTime()));
      caseMultiQuery.setFields(Lists.newArrayList("id", "ownMobile","orgDeltId","teamId"));
      caseList = caseService.getAllCasesUsingEs(caseMultiQuery);
    } else {
      caseList = caseService.queryResultForMulti(caseMultiQuery);
    }
    // 过滤掉符合全局管控电催案件限制的案件
    filterGlobalCtrlCase(caseList, orgSwitch);
    if (CollectionUtils.isEmpty(caseList)) {
      throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
    }

      // 任务添加到任务列表
    redisUtil.sSet(KeyCache.CASE_FIFO_CASE_IDS + casePlan.getId(),24*60*60, caseList.stream().map(c -> c.getId().toString()).toArray(String[]::new));
    stringRedisTemplate
        .opsForList()
        .leftPush(KeyCache.CASE_FIFO_PLAN_ID_LIST, casePlan.getId().toString());
  }

  public void asyncAddCaseMessage(CaseMultiQuery caseMultiQuery, CasePlan casePlan, OrgSwitch orgSwitch)
      throws Exception {
    // 只获取部分数据
    caseMultiQuery.setSelectField("ca.id,ca.own_mobile,ca.org_delt_id,ca.team_id");

    // 区分案件智能计划与普通案件列表的标记，0-智能计划
    caseMultiQuery.setIsPlan(0);
    // 过滤不参与计划案件的时间，当天时间+00:00:00
    caseMultiQuery.setIgnorePlanTime(
        DateUtils.formatDateFull(DateUtils.getStartTimeOfDate(new Date())));
    List<CaseQueryResult> caseList = new ArrayList<>();
    if (systemConfig.getESSwitch()) {
      //如果有es就走es查询，解决慢sql问题
      caseMultiQuery.setIgnorePlanTime(String.valueOf(DateUtils.getStartTimeOfDate(new Date()).getTime()));
      caseMultiQuery.setFields(Lists.newArrayList("id", "ownMobile","orgDeltId","teamId"));
      caseList = caseService.getAllCasesUsingEs(caseMultiQuery);
    } else {
      caseList = caseService.queryResultForMulti(caseMultiQuery);
    }
    // 全局管控过滤掉符合短信管控条件的案件
    filterGlobalCtrlSmsCase(caseList, orgSwitch);
    if (CollectionUtils.isEmpty(caseList)) {
      throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
    }

    List<String> caseIdList = caseList.stream().map(c -> c.getId().toString()).collect(Collectors.toList());
    List<List<String>> subIdLists = CmUtil.splitList(caseIdList,5000);
    for (int i = 0; i < subIdLists.size(); i++) {
      List<String> subIdList = subIdLists.get(i);
      if (i == 0) {
        stringRedisTemplate.opsForSet().add(KeyCache.CASE_MESSAGE_CASE_IDS + casePlan.getId(),
          subIdList.toArray(new String[0]));
        stringRedisTemplate.opsForList()
          .leftPush(KeyCache.CASE_MESSAGE_PLAN_ID_LIST, casePlan.getId().toString());
      } else {
        CasePlan casePlanNew = new CasePlan();
        org.springframework.beans.BeanUtils.copyProperties(casePlan,casePlanNew);
        casePlanNew.setId(null);
        casePlanNew.setName(casePlan.getName() + "-" + (i + 1));
        casePlanMapper.insertSelective(casePlanNew);
        stringRedisTemplate.opsForSet().add(KeyCache.CASE_MESSAGE_CASE_IDS + casePlanNew.getId(),
          subIdList.toArray(new String[0]));
        stringRedisTemplate.opsForList()
          .leftPush(KeyCache.CASE_MESSAGE_PLAN_ID_LIST, casePlanNew.getId().toString());
      }
    }
    if (subIdLists.size() > 1) {
      casePlan.setName(casePlan.getName() + "-" + 1);
      casePlanMapper.updateByPrimaryKeySelective(casePlan);
    }
  }

  /**
   * 查询预测式计划列表
   *
   * @param casePlanParam
   * @param pageParam
   * @return
   */
  public PageOutput<CasePlanVO> queryFifoList(CasePlanParam casePlanParam, PageParam pageParam) {
    List<String> duyanPlanStatusList =
        Lists.newArrayList("UPLOADING", "NOT_STARTED", "IN_PROGRESS", "PAUSED");
    String planStatus = casePlanParam.getPlanStatus();
    if (StringUtils.isNotBlank(planStatus) && duyanPlanStatusList.contains(planStatus)) {
      // 需到度言查找计划信息 再过滤 再分页
      return queryByDuyanStatus(casePlanParam, pageParam, CasePlanEnums.Type.FIFO.getCode());
    }
    Page page = this.setPage(pageParam);
    Example example = buildFiFoPlanExample(casePlanParam, planStatus);
    PageOutput pageOutput = super.selectByPage(example, pageParam);
    List<CasePlan> casePlanList = pageOutput.getList();
    if (CollectionUtils.isEmpty(casePlanList)) {
      return pageOutput;
    }
    // 除已完成的案件外，其他案件状态需到度言进行搜索
    List<Long> duyanPlanIds =
        casePlanList.stream().filter(c -> c.getStatus().equals(CasePlanEnums.Status.ING.getCode())
            ||c.getStatus().equals(CasePlanEnums.Status.FINISHED.getCode()))
            .map(CasePlan::getDuyanPlanId).collect(Collectors.toList());
    List<CasePlanTmp> casePlanTmpList = manager.getNewPlanListByIds(duyanPlanIds);
    Map<Long, CasePlanTmp> casePlanTmpMap =
        casePlanTmpList.stream().collect(Collectors.toMap(CasePlanTmp::getDuyanPlanId, r -> r));
    List<CasePlanVO> vos = new ArrayList<>();
    for (CasePlan casePlan : casePlanList) {
      CasePlanTmp casePlanTmp = casePlanTmpMap.get(casePlan.getDuyanPlanId());
      CasePlanVO vo = convertFiFoPlanVO(casePlan, casePlanTmp);
      vos.add(vo);
    }
    pageOutput.setList(vos);
    return pageOutput;
  }

  public PageOutput<CasePlanVO> queryMessageList(CasePlanParam casePlanParam, PageParam pageParam) {
    List<String> duyanPlanStatusList =
        Lists.newArrayList("UPLOADING", "NOT_STARTED", "IN_PROGRESS");
    String planStatus = casePlanParam.getPlanStatus();
    if (StringUtils.isNotBlank(planStatus) && duyanPlanStatusList.contains(planStatus)) {
      // 需到度言查找计划信息 再过滤 再分页
      return queryByPlatformMessageStatus(casePlanParam, pageParam);
    }
    Example example = buildMessageExample(casePlanParam, planStatus);
    PageOutput pageOutput = super.selectByPage(example, pageParam);
    List<CasePlan> casePlanList = pageOutput.getList();
    if (CollectionUtils.isEmpty(casePlanList)) {
      return pageOutput;
    }
    // 除已完成的案件外，其他案件状态需到度言进行搜索
    List<Long> duyanPlanIds =
        casePlanList.stream().filter(c -> c.getStatus().equals(CasePlanEnums.Status.ING.getCode()))
            .map(CasePlan::getDuyanPlanId).collect(Collectors.toList());
    List<MessagePlanTmp> planListByIds = messageManager.getPlanListByIds(duyanPlanIds);
    Map<Long, MessagePlanTmp> collect =
        planListByIds.stream().collect(Collectors.toMap(MessagePlanTmp::getId, r -> r));
    List<CasePlanVO> vos = new ArrayList<>();
    for (CasePlan casePlan : casePlanList) {
      MessagePlanTmp messagePlanTmp = collect.get(casePlan.getDuyanPlanId());
      CasePlanVO vo = convertMessageToVO(casePlan, messagePlanTmp);
      vos.add(vo);
    }
    pageOutput.setList(vos);
    return pageOutput;
  }

  public PageOutput<CasePlanVO> queryByDuyanStatus(
      CasePlanParam casePlanParam, PageParam pageParam, Integer type) {
    Example example;
    if (CasePlanEnums.Type.isRobot(type)) {
      example = buildRobotPlanExample(casePlanParam, CasePlanEnums.Status.ING.name(), type);
    } else if (CasePlanEnums.Type.PERSONALFIFO.getCode().equals(type)) {
      example = buildPersonalFiFoPlanExample(casePlanParam, CasePlanEnums.Status.ING.name());
    } else {
      example = buildFiFoPlanExample(casePlanParam, CasePlanEnums.Status.ING.name());
    }
    Page page = this.setPage(pageParam);
    List<CasePlan> casePlanList = selectByExample(example);
    if (CollectionUtils.isEmpty(casePlanList)) {
      new PageOutput(pageParam.getPage(), pageParam.getLimit());
    }
    List<Long> duyanPlanIds =
        casePlanList.stream().map(CasePlan::getDuyanPlanId).collect(Collectors.toList());
    List<CasePlanTmp> casePlanTmpList = manager.getPlanListByIds(duyanPlanIds, type);
    // 过滤案件
    String duyanPlanStatus = casePlanParam.getPlanStatus();
    casePlanTmpList =
        casePlanTmpList.stream()
            .filter(c -> c.getPlanStatus().equals(duyanPlanStatus))
            .collect(Collectors.toList());
    if (CollectionUtils.isEmpty(casePlanTmpList)) {
      new PageOutput(pageParam.getPage(), pageParam.getLimit());
    }
    Map<Long, CasePlan> casePlanMap =
        casePlanList.stream().collect(Collectors.toMap(CasePlan::getDuyanPlanId, r -> r));
    // 处理分页
    Integer total = casePlanTmpList.size();
    Integer limit = pageParam.getLimit();
    Integer pageCount = limit > total ? 1 : pageParam.getPage();
    Integer start = (pageCount - 1) * limit;
    Integer end = pageCount * limit > total ? total : pageCount * limit;
    casePlanTmpList = casePlanTmpList.subList(start, end);
    List<CasePlanVO> vos = new ArrayList<>();
    for (CasePlanTmp casePlanTmp : casePlanTmpList) {
      CasePlan casePlan = casePlanMap.get(casePlanTmp.getDuyanPlanId());
      CasePlanVO vo;
      if (CasePlanEnums.Type.isRobot(type)) {
        vo = convertRobotPlanVO(casePlan, casePlanTmp);
      } else {
        vo = convertFiFoPlanVO(casePlan, casePlanTmp);
      }
      vos.add(vo);
    }
    PageOutput pageOutput = new PageOutput(pageCount, limit, total, vos);
    pageOutput.setList(vos);
    return pageOutput;
  }

  // 短信平台 未开始NOT_STARTED((byte) 0),
  // 进行中 PROCESSING((byte) 1),
  // 已完成FINISHED((byte) 2),
  // 创建中CREATING((byte) -1),
  // 已取消 STOP((byte)3);
  private String convertCodeToString(int code) {
    String codeStr = "";
    switch (code) {
      case 0:
        codeStr = "NOT_STARTED";
        break;
      case 1:
        codeStr = "IN_PROGRESS";
        break;
      case 2:
        codeStr = "FINISHED";
        break;
      case 3:
        codeStr = "CANCELLED";
        break;
      case -1:
        codeStr = "UPLOADING";
        break;
      case 4:
        codeStr = "DUYAN_FAIL";
        break;
    }
    return codeStr;
  }

  public PageOutput<CasePlanVO> queryByPlatformMessageStatus(
      CasePlanParam casePlanParam, PageParam pageParam) {
    Example example = buildMessageExample(casePlanParam, CasePlanEnums.Status.ING.name());
    List<CasePlan> casePlanList = selectByExample(example);
    if (CollectionUtils.isEmpty(casePlanList)) {
      new PageOutput(pageParam.getPage(), pageParam.getLimit());
    }
    List<Long> duyanPlanIds =
        casePlanList.stream().map(CasePlan::getDuyanPlanId).collect(Collectors.toList());
    List<MessagePlanTmp> messagePlanTmpList = messageManager.getPlanListByIds(duyanPlanIds);
    // 过滤案件
    String duyanPlanStatus = casePlanParam.getPlanStatus();
    messagePlanTmpList =
        messagePlanTmpList.stream()
            .filter(c -> convertCodeToString(c.getState()).equals(duyanPlanStatus))
            .collect(Collectors.toList());

    if (CollectionUtils.isEmpty(messagePlanTmpList)) {
      new PageOutput(pageParam.getPage(), pageParam.getLimit());
    }
    Map<Long, CasePlan> casePlanMap =
        casePlanList.stream().collect(Collectors.toMap(CasePlan::getDuyanPlanId, r -> r));
    // 处理分页
    Integer total = messagePlanTmpList.size();
    Integer limit = pageParam.getLimit();
    Integer page = limit > total ? 1 : pageParam.getPage();
    Integer start = (page - 1) * limit;
    Integer end = page * limit > total ? total : page * limit;
    messagePlanTmpList = messagePlanTmpList.subList(start, end);
    List<CasePlanVO> vos = new ArrayList<>();
    for (MessagePlanTmp messagePlanTmp : messagePlanTmpList) {
      CasePlan casePlan = casePlanMap.get(messagePlanTmp.getId());
      CasePlanVO vo = convertMessageToVO(casePlan, messagePlanTmp);
      vos.add(vo);
    }
    PageOutput pageOutput = new PageOutput(page, limit, total, vos);
    pageOutput.setList(vos);
    return pageOutput;
  }

  private Example buildFiFoPlanExample(CasePlanParam casePlanParam, String planStatus) {
    UserSession userSession = getTokenUser();
    Example example = new Example(CasePlan.class);
    Example.Criteria planCrt = example.createCriteria();
    example.setOrderByClause("id desc");
    planCrt
        .andEqualTo("orgId", userSession.getOrgId())
        .andEqualTo("type", CasePlanEnums.Type.FIFO.getCode());
    if (StringUtils.isNotBlank(planStatus)) {
      planCrt.andEqualTo("status", CasePlanEnums.Status.valueOf(planStatus).getCode());
    }
    if (UserUtils.likeBranchAdmin()) {
      planCrt.andEqualTo("depId", userSession.getDepId());
    }
    if (UserUtils.likeTeamLeader()) {
      planCrt.andEqualTo("teamId", userSession.getTeamId());
    }
    if (!StringUtils.isBlank(casePlanParam.getUserIds())) {
      planCrt.andIn("userId", Arrays.asList(casePlanParam.getUserIds().split(",")));
    }
    if (casePlanParam.getPlanCreatedDate() != null
        && casePlanParam.getPlanCreatedDate().split(",").length == 2) {
      casePlanParam.setPlanCreatedDateStart(
          convertDate(casePlanParam.getPlanCreatedDate().split(",")[0]));
      casePlanParam.setPlanCreatedDateEnd(
          convertDate(casePlanParam.getPlanCreatedDate().split(",")[1]));
      planCrt.andGreaterThanOrEqualTo("createTime", casePlanParam.getPlanCreatedDateStart());
      planCrt.andLessThanOrEqualTo("createTime", casePlanParam.getPlanCreatedDateEnd());
    }
    if (StringUtils.isNotBlank(casePlanParam.getPriority())) {
      planCrt.andEqualTo("priority", casePlanParam.getPriority());
    }
    return example;
  }

  private Example buildMessageExample(CasePlanParam casePlanParam, String planStatus) {
    UserSession userSession = getTokenUser();
    Example example = new Example(CasePlan.class);
    Example.Criteria planCrt = example.createCriteria();
    example.setOrderByClause("id desc");
    planCrt
        .andEqualTo("orgId", userSession.getOrgId())
        .andEqualTo("type", CasePlanEnums.Type.MESSAGE.getCode());
    if (StringUtils.isNotBlank(planStatus)) {
      planCrt.andEqualTo("status", CasePlanEnums.Status.valueOf(planStatus).getCode());
    }
    if (UserUtils.likeBranchAdmin()) {
      planCrt.andEqualTo("depId", userSession.getDepId());
    }
    if (UserUtils.likeTeamLeader()) {
      planCrt.andEqualTo("teamId", userSession.getTeamId());
    }
    if (casePlanParam.getPlanCreatedDate() != null
        && casePlanParam.getPlanCreatedDate().split(",").length == 2) {
      casePlanParam.setPlanCreatedDateStart(
          convertDate(casePlanParam.getPlanCreatedDate().split(",")[0]));
      casePlanParam.setPlanCreatedDateEnd(
          convertDate(casePlanParam.getPlanCreatedDate().split(",")[1]));
      planCrt.andGreaterThanOrEqualTo("createTime", casePlanParam.getPlanCreatedDateStart());
      planCrt.andLessThanOrEqualTo("createTime", casePlanParam.getPlanCreatedDateEnd());
    }

    if (casePlanParam.getPlanDate() != null && casePlanParam.getPlanDate().split(",").length == 2) {
      casePlanParam.setPlanStartDateStr(convertDate(casePlanParam.getPlanDate().split(",")[0]));
      casePlanParam.setPlanEndDateStr(convertDate(casePlanParam.getPlanDate().split(",")[1]));
      planCrt.andGreaterThanOrEqualTo("planStartTime", casePlanParam.getPlanStartDateStr());
      planCrt.andLessThanOrEqualTo("planStartTime", casePlanParam.getPlanEndDateStr());
    }
    if (StringUtils.isNotBlank(casePlanParam.getPriority())) {
      planCrt.andEqualTo("priority", casePlanParam.getPriority());
    }
    return example;
  }

  private CasePlanVO convertFiFoPlanVO(CasePlan casePlan, CasePlanTmp casePlanTmp) {
    CasePlanVO vo = new CasePlanVO();
    BeanUtils.copyProperties(casePlan, vo);
    if (casePlanTmp != null) {
      vo.setProgress(casePlanTmp.getFinishedCount() + "/" + casePlanTmp.getTotalCount());
      vo.setPlanStatus(casePlanTmp.getPlanStatus()); // 计划状态
      // 计划已完成或已取消，同步到安米
      judgeFinishedOrCancelled(casePlan.getId(), casePlanTmp);
    } else {
      vo.setProgress(casePlan.getFinishedCount() + "/" + casePlan.getTotalCount());
      vo.setPlanStatus(
          EnumUtils.getByCode(casePlan.getStatus(), CasePlanEnums.Status.class).name()); // 计划状态
    }
    vo.setValidTotal(casePlan.getCaseTotal()); // 有效数量
    vo.setSelectedTotal(casePlan.getSelectedTotal()); // 选择数量
    vo.setCreateBy(userService.getNames(casePlan.getOrgId()).get(casePlan.getCreateBy()));
    vo.setUpdateBy(userService.getNames(casePlan.getOrgId()).get(casePlan.getUpdateBy()));
    vo.setPriority(casePlan.getPriority()); // 优先级
    vo.setName(casePlan.getName()); // 计划名称
    vo.setSiteName(casePlan.getSiteName()); // 话术名称
    vo.setDesc(casePlan.getDesc());
    vo.setCreatedUserName(userService.getNames(casePlan.getOrgId()).get(casePlan.getCreateBy()));
    vo.setFifoTeamId(casePlan.getTeamId());
    vo.setFifoTeamName(depTeamService.getNames().get(casePlan.getTeamId()));
    vo.setChannelLimit(casePlan.getChannelLimit());// 外呼比例
    vo.setCheckPhoneNo(casePlan.getCheckPhoneNo());
    List<String> callTarget = new ArrayList<String>(2);
    if (1 == casePlan.getDebtorOwn()) {
      callTarget.add("债务人本人");
    }
    if (1 == casePlan.getContacts()) {
      callTarget.add("紧急联系人");
    }
    vo.setCallTarget(String.join(",", callTarget));
    return vo;
  }

  private CasePlanVO convertMessageToVO(CasePlan casePlan, MessagePlanTmp messagePlanTmp) {
    CasePlanVO vo = new CasePlanVO();
    BeanUtils.copyProperties(casePlan, vo);
    Example example = new Example(CasePlanRel.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("planId", casePlan.getId());
    int validTotal = casePlanRelService.selectCountByExample(example);
    if (messagePlanTmp != null) {
      Integer totalCount = messagePlanTmp.getTotalCount(); // 总数
      Integer unhandleCount = messagePlanTmp.getUnhandleCount(); // 未处理总数
      Integer finishCount = totalCount - unhandleCount; //
      vo.setProgress(finishCount + "/" + totalCount);
      vo.setValidTotal(validTotal); // 有效数量
      vo.setPlanStatus(convertCodeToString(messagePlanTmp.getState())); // 计划状态
      // 计划已完成或已取消，同步到安米
      judgeMessageFinishedOrCandle(casePlan.getId(), messagePlanTmp);
    } else {
      vo.setProgress(casePlan.getFinishedCount() + "/" + casePlan.getTotalCount());
      vo.setValidTotal(validTotal); // 有效数量
      vo.setPlanStatus(
          EnumUtils.getByCode(casePlan.getStatus(), CasePlanEnums.Status.class).name()); // 计划状态
    }
    vo.setMessageTotal(casePlan.getTotalCount().intValue());
    vo.setCreateBy(userService.getNames(casePlan.getOrgId()).get(casePlan.getCreateBy()));
    vo.setUpdateBy(userService.getNames(casePlan.getOrgId()).get(casePlan.getUpdateBy()));
    vo.setPriority(casePlan.getPriority()); // 优先级
    vo.setSelectedTotal(casePlan.getSelectedTotal()); // 选择数量
    vo.setName(casePlan.getName()); // 计划名称
    vo.setSiteName(casePlan.getSiteName()); // 话术名称
    vo.setDesc(casePlan.getDesc());
    vo.setCreatedUserName(userService.getNames(casePlan.getOrgId()).get(casePlan.getCreateBy()));
    vo.setFifoTeamName(depTeamService.getNames().get(casePlan.getTeamId()));
    SmsTemplate smsTemplate = smsTemplateService.selectByPrimaryKey(casePlan.getSmsTplId());
    vo.setSmsSignName(smsTemplate.getName());
    vo.setSmsTplContent(smsTemplate.getContent());
    return vo;
  }

  private void judgeFinishedOrCancelled(Long casePlanId, CasePlanTmp casePlanTmp) {
    CasePlan casePlan = new CasePlan();
    if (casePlanTmp.getPlanStatus().equals(CasePlanEnums.Status.FINISHED.name())) {
      casePlan.setStatus(CasePlanEnums.Status.FINISHED.getCode());
    } else if (casePlanTmp.getPlanStatus().equals(CasePlanEnums.Status.CANCELLED.name())) {
      casePlan.setStatus(CasePlanEnums.Status.CANCELLED.getCode());
    } else if ("PAUSED".equals(casePlanTmp.getPlanStatus())) {
      casePlan.setStatus(CasePlanEnums.Status.ING.getCode());
    } else {
      return;
    }
    casePlan.setId(casePlanId);
    casePlan.setFinishedCount(casePlanTmp.getFinishedCount());
    casePlan.setTotalCount(casePlanTmp.getTotalCount());
    casePlan.setPlanStartTime(casePlanTmp.getPlanStartTime());
    if (casePlanTmp.getPlanEndTime() != null) {
      casePlan.setPlanEndTime(casePlanTmp.getPlanEndTime());
    }
    super.updateByPrimaryKeySelective(casePlan);
  }

  public List<Long> getFailPlanIds(List<Long> casePlanIds) {
    List<Long> failPlanIds = new ArrayList<>();
    if (CollectionUtils.isEmpty(casePlanIds)) {
      return failPlanIds;
    }
    Example example = new Example(CasePlan.class);
    example.and().andIn("id", casePlanIds);
    List<CasePlan> casePlanList = casePlanMapper.selectByExample(example);
    List<Long> duyanPlanIds =
            casePlanList.stream().filter(c -> c.getStatus().equals(CasePlanEnums.Status.ING.getCode()))
                    .map(CasePlan::getDuyanPlanId).collect(Collectors.toList());
    List<MessagePlanTmp> planListByIds = messageManager.getPlanListByIds(duyanPlanIds);
    Map<Long, MessagePlanTmp> map =
            planListByIds.stream().collect(Collectors.toMap(MessagePlanTmp::getId, r -> r));
    casePlanList.forEach(casePlan -> {
      // 如果已经是度言执行失败了，直接加入
      if (Objects.equals(casePlan.getStatus(), CasePlanEnums.Status.DUYAN_FAIL.getCode())) {
        failPlanIds.add(casePlan.getId());
      } else {
        MessagePlanTmp messagePlanTmp = map.get(casePlan.getDuyanPlanId());
        if (messagePlanTmp != null) {
          if (convertCodeToString(messagePlanTmp.getState()).equals(CasePlanEnums.Status.DUYAN_FAIL.name())) {
            failPlanIds.add(casePlan.getId());
          }
        }
      }
    });
    return failPlanIds;
  }

  private void judgeMessageFinishedOrCandle(Long casePlanId, MessagePlanTmp messagePlanTmp) {
    CasePlan casePlan = new CasePlan();
    if (convertCodeToString(messagePlanTmp.getState()).equals(CasePlanEnums.Status.FINISHED.name())) {
      casePlan.setStatus(CasePlanEnums.Status.FINISHED.getCode());
    } else if (convertCodeToString(messagePlanTmp.getState()).equals(CasePlanEnums.Status.CANCELLED.name())) {
      casePlan.setStatus(CasePlanEnums.Status.CANCELLED.getCode());
    } else if (convertCodeToString(messagePlanTmp.getState()).equals(CasePlanEnums.Status.DUYAN_FAIL.name())) {
      casePlan.setStatus(CasePlanEnums.Status.DUYAN_FAIL.getCode());
    } else {
      return;
    }
    casePlan.setId(casePlanId);
    Integer totalCount = messagePlanTmp.getTotalCount();
    Integer unhandleCount = messagePlanTmp.getUnhandleCount();
    int finishCount = totalCount - unhandleCount;
    casePlan.setFinishedCount((long) finishCount);
    casePlan.setTotalCount(Long.valueOf(messagePlanTmp.getTotalCount()));
    super.updateByPrimaryKeySelective(casePlan);
  }

  public List<TtsTypeVO> ttsTypeList() {
    JSONArray jsonArray = manager.ttsTypeList();
    List<TtsTypeVO> vos = new ArrayList<>();
    if (jsonArray == null || CommonUtils.isEmpty(jsonArray)) {
      return vos;
    }
    for (int i = 0; i < jsonArray.size(); i++) {
      JSONObject jsonObject = jsonArray.getJSONObject(i);
      TtsTypeVO vo = new TtsTypeVO();
      vo.setShowName(jsonObject.getString("showName"));
      vo.setCnName(jsonObject.getString("cnName"));
      vo.setGender(jsonObject.getIntValue("gender"));
      vo.setPitchRate(jsonObject.getIntValue("pitchRate"));
      vo.setSpeechRate(jsonObject.getIntValue("speechRate"));
      vo.setVolume(jsonObject.getString("volume"));
      vos.add(vo);
    }
    return vos;
  }

  public boolean cancelMessagePlan(Long casePlanId) {
    CasePlan temp = super.selectByPrimaryKey(casePlanId);
    boolean flag = messageManager.planSmsCancel(temp.getDuyanPlanId());
    if (flag) {
      temp.setStatus(CasePlanEnums.Status.CANCELLED.getCode()); // 更新为已取消状态
      super.updateByPrimaryKey(temp);
      return true;
    }
    return false;
  }

  public MessageAccountDTO getAccountInfo() {
    MessageAccountDTO accountInfo = messageManager.getAccountInfo();
    if (accountInfo == null) {
      throw new ApiException("获取剩余金额和单价失败");
    }
    return accountInfo;
  }

  //移除案件最后添加的联系人--
  public void removeCase(List<CaseQueryResult> caseList, UserSession userSession) {
    if (CollectionUtils.isEmpty(caseList)) {
      return;
    }
    List<Long> caseIds = caseList.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
    List<CaseDuyanPlanRel> relList = selectPlanCaseIds(caseIds, CasePlanRelEnums.Status.NORMAL.getCode());
    if (CollectionUtils.isEmpty(relList)) {
      return;
    }
    Map<Long, List<CaseDuyanPlanRel>> duyanPlanMap = relList.stream().collect(Collectors.groupingBy(CaseDuyanPlanRel::getDuyanPlanId));
    for (Map.Entry<Long, List<CaseDuyanPlanRel>> entry : duyanPlanMap.entrySet()) {
      Long duyanPlanId = entry.getKey();

      Long orgId = userSession != null ? userSession.getOrgId() : entry.getValue().get(0).getOrgId();
      Long updateBy = userSession != null ? userSession.getId() : 0L;
      Integer type = entry.getValue().get(0).getType();

      List<Long> relIds = entry.getValue().stream().map(CaseDuyanPlanRel::getId).collect(Collectors.toList());
      List<Long> lastPlanRelContactIds = this.casePlanRelContactMapper.selectLastPlanRelContactIds(relIds);
      List<CasePlanRelContact> casePlanRelContacts = this.casePlanRelContactService.selectByIdList(lastPlanRelContactIds);
      if(!CollectionUtils.isEmpty(casePlanRelContacts)) {
        if (CasePlanEnums.Type.ROBOT.getCode().equals(type)) {
          //老的机器人计划不支持批量删除,单条删除
          for (CasePlanRelContact casePlanRelContact : casePlanRelContacts) {
            try {
              casePlanRelService.removeCaseItem(casePlanRelContact.getCasePlanRelId(), orgId, duyanPlanId,
                casePlanRelContact.getPhoneNum(), updateBy);
            } catch (Exception e) {
              log.error("移除失败", e);
            }
          }
        } else {
          try {
            casePlanRelService.removeCaseItemBatch(relIds, orgId, duyanPlanId, casePlanRelContacts, updateBy);
          } catch (Exception e) {
            log.error("移除失败", e);
          }
        }
      }
      //未发送到度言的联系人信息也删除
      Example delExample = new Example(CasePlanRelContact.class);
      delExample.and().andIn("casePlanRelId", relIds);
      delExample.and().andEqualTo("callFlag", 0);
      casePlanRelContactService.deleteByExample(delExample);
    }
  }

  public List<CaseDuyanPlanRel> selectPlanCaseIds(List<Long> caseIdList, Integer status) {
    Map<String, Object> params = new HashMap<>();
    params.put("caseIdList", caseIdList);
    params.put("status", status);
    return casePlanRelMapper.selectPlanCaseIds(params);
  }

  public void addByPlanRel(List<CaseContact> caseList, List<CaseDuyanPlanRel> relList, UserSession userSession) {
    Map<Long, List<CaseDuyanPlanRel>> planMap = relList.stream().collect(Collectors.groupingBy(CaseDuyanPlanRel::getDuyanPlanId));
    for (Long duyanPlanId : planMap.keySet()) {
      try {
        List<CaseDuyanPlanRel> subRelList = planMap.get(duyanPlanId);
        List<Long> planCaseIdList = subRelList.stream().map(CaseDuyanPlanRel::getCaseId).collect(Collectors.toList());
        // 对应案件
        List<CaseContact> subCaseList = caseList.stream().filter(c -> planCaseIdList.contains(c.getCaseId())).collect(Collectors.toList());
        CasePlan casePlan = selectCasePlanByDuyanPlanId(duyanPlanId);
        casePlanRelService.addCaseItem(subRelList, userSession.getOrgId(), duyanPlanId, subCaseList, casePlan);
      } catch (Exception e) {
        log.error("再添加失败", e);
      }
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void addPersonalFifoPlan(CaseFifoFO caseFifoFO) throws Exception {
    if (caseFifoFO.getCaseMultiQuery() == null || caseFifoFO.getCaseFifoParam() == null) {
      throw new ApiException("传入参数错误");
    }
    UserSession userSession = UserUtils.getTokenUser();
    CaseMultiQuery caseMultiQuery = caseFifoFO.getCaseMultiQuery();
    if (caseMultiQuery.getCaseCount() == null) {
      throw new ApiException("案件选择数量参数传入错误");
    }
    // 创建个人预测式计划选择转机器人接入，需要进行话术变量校验
    List<SiteVarsMapping> duyanVarsMappings = new ArrayList<>();
    CaseFifoParam caseFifoParam = caseFifoFO.getCaseFifoParam();
    if (ObjectUtil.equals(caseFifoParam.getFifoToRobotEnabled(), 1)){
      AssertUtil.notNull(caseFifoParam.getSiteId(),"话术变量id不可为空");

      List<String> duyanSiteVars = manager.getSiteVars(caseFifoParam.getSiteId(),userSession.getOrgId());
      if (ObjectUtil.isNotEmpty(duyanSiteVars)){
        // “U_”开头且变量名为4-16位英文或数字的变量
        boolean varRuleCheck = duyanSiteVars.stream().allMatch(PatternUtils::duyanSiteVar);
        AssertUtil.isTrue(varRuleCheck,"话术变量命名不合规范");

        // 话术变量映射绑定校验
        Long orgId = userSession.getOrgId();
        List<SiteVarsMapping> siteVarsMappings = siteVarsMappingService.getSiteVarsMappings(orgId,caseFifoParam.getSiteId());
        boolean isAllMapping = duyanSiteVars.stream().allMatch(duyanSiteVar ->
                siteVarsMappings.stream().anyMatch(siteVarsMapping ->
                        ObjectUtil.equals(duyanSiteVar, siteVarsMapping.getDuyanVar()) && StrUtil.isNotBlank(siteVarsMapping.getAnmiVar()))
        );
        AssertUtil.isTrue(isAllMapping,"存在话术变量未做映射绑定");

        duyanVarsMappings = siteVarsMappings.stream().filter(siteVarsMapping -> StrUtil.isNotBlank(siteVarsMapping.getAnmiVar()) && duyanSiteVars.stream().anyMatch(duyanSiteVar ->  ObjectUtil.equals(duyanSiteVar, siteVarsMapping.getDuyanVar())))
                .collect(Collectors.toList());
      }
    }

    CasePlan casePlan = new CasePlan();
    BeanUtils.copyProperties(caseFifoFO.getCaseFifoParam(), casePlan);
    casePlan.setSelectedTotal(caseMultiQuery.getCaseCount());
    // 小组长可以看到包含该小组的所有计划，包括总公司和分公司创建的
    casePlan.setOrgId(userSession.getOrgId());
    casePlan.setDepId(userSession.getDepId());
    casePlan.setTeamId(userSession.getTeamId());
    casePlan.setUserId(userSession.getId());
    casePlan.setCreateBy(userSession.getId());
    casePlan.setUpdateBy(userSession.getId());
    // 个人预测式外呼
    casePlan.setType(CasePlanEnums.Type.PERSONALFIFO.getCode());
    // 搜索条件
    caseMultiQuery.setOrgIds(userSession.getOrgId().toString());
    caseMultiQuery.setTeamIds(userSession.getTeamId().toString());
    if (caseMultiQuery.getAction() != null && caseMultiQuery.getAction() == 9) {
      OrgSwitch orgSwitch = this.orgSwitchMapper.selectOrgSwitchByOrgId(userSession.getOrgId());
      Assert.notNull(orgSwitch, "找不到OrgSwitch");
      if (!OrgSwitchEnums.TeamUrgeSwitch.OPEN.getCode().equals(orgSwitch.getTeamUrgeSwitch())) {
        throw new ApiException("公司不支持团队催收，查询失败");
      }
      if (orgSwitch.getTeamUrgeIds() == null) {
        throw new ApiException("您所属的团队未开启团队催收，查询失败");
      }
      if (!JSON.parseArray(orgSwitch.getTeamUrgeIds()).toJavaList(Long.class).contains(UserUtils.getTokenUser().getTeamId())) {
        throw new ApiException("您所属的团队未开启团队催收，查询失败");
      }
      caseMultiQuery.setTeamIds(String.valueOf(UserUtils.getTokenUser().getTeamId()));
      //解决案件添加个人预测式外呼时案件数量不对问题
      //caseMultiQuery.setUserIds(null);
    } else {
      caseMultiQuery.setUserIds(String.valueOf(userSession.getId()));
    }
    // 只有案件状态是分配完成、留案、分配至组才可以调整
    List<Integer> allotStatusList = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_TEAM.getCode(), CaseEnums.AllotStatus.ALLOT_USER.getCode());
    List<Integer> caseStatusList =  ImmutableList.of(CaseEnums.CaseStatus.DELAY.getCode(),CaseEnums.CaseStatus.NORMAL.getCode());
    caseMultiQuery.setAllotStatues(org.apache.commons.lang.StringUtils.join(allotStatusList, ","));
    caseMultiQuery.setCaseStatues(org.apache.commons.lang.StringUtils.join(caseStatusList, ","));
    // 过滤不参与计划案件
    caseMultiQuery.setIgnorePlan(CaseEnums.IgnorePlan.NO.getCode());

    // 区分案件智能计划与普通案件列表的标记，0-智能计划
    caseMultiQuery.setIsPlan(0);
    // 过滤不参与计划案件的时间，当天时间+00:00:00
    caseMultiQuery.setIgnorePlanTime(
        DateUtils.formatDateFull(DateUtils.getStartTimeOfDate(new Date())));
    // 查询公司全局管控开关是否开启
    OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(userSession.getOrgId());
    if (caseMultiQuery.getAllSelect()) {
      // 全部勾选，异步处理
      casePlan.setStatus(CasePlanEnums.Status.NEW.getCode());
      casePlanMapper.insertSelective(casePlan);
      asyncAddPersonalFifo(caseMultiQuery, casePlan, orgSwitch);
    } else {
      List<CaseQueryResult> caseList = caseService.queryResultForMulti(caseMultiQuery);
      // 过滤掉符合全局管控电催案件限制的案件
      filterGlobalCtrlCase(caseList, orgSwitch);
      if (CollectionUtils.isEmpty(caseList)) {
        throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
      }
      casePlan.setStatus(CasePlanEnums.Status.ING.getCode());
      createPersonalFifo(caseList, casePlan, duyanVarsMappings, orgSwitch);
    }
  }

  /**
   * 过滤掉符合全局管控电催案件限制的案件
   *
   * @param caseList  案件id
   * @param orgSwitch 开关
   */
  public void filterGlobalCtrlCase(List<CaseQueryResult> caseList, OrgSwitch orgSwitch) {
    // 案件列表为空，全局管控开关为空满足其一，不进行处理
    if (CollectionUtils.isEmpty(caseList) ||
            Objects.equals(orgSwitch.getGlobalCtrlSwitch(), OrgSwitchEnums.GlobalCtrlSwitch.CLOSE.getCode())) {
      return;
    }
    // 查询案件是否进入到全局管控,过滤掉已满足电催管控案件限制次数的案件
    List<Long> ctrlCaseIds = new ArrayList<>();
    Date startDate = DateUtil.beginOfDay(new Date());
    // 根据管控维度将案件进行分组
    if (Objects.equals(orgSwitch.getGlobalCtrlType(), OrgSwitchEnums.GlobalCtrlType.TEAM.getCode())) {
      Map<Long, List<CaseQueryResult>> caseMap = caseList.stream().collect(Collectors.groupingBy(CaseQueryResult::getTeamId));
      List<Long> teamIds = new ArrayList<>(caseMap.keySet());
      List<CtrlTypeRelResult> ctrlTypeRelResults = ctrlTypeRelService.queryCtrlByTeamType(teamIds, 1, null);
      Map<Long, CtrlTypeRelResult> ctrlMap = ctrlTypeRelResults.stream().collect(Collectors.toMap(CtrlTypeRelResult::getTeamId, f -> f));
      for (Long teamId : caseMap.keySet()) {
        if (ctrlMap.containsKey(teamId)) {
          int start = 0;
          int end = 0;
          List<CaseQueryResult> caseQueryResults = caseMap.get(teamId);
          while (start < caseQueryResults.size()) {
            end = start + 500;
            if (end > caseQueryResults.size()) {
              end = caseQueryResults.size();
            }
            List<CaseQueryResult> subCaseList = caseQueryResults.subList(start, end);
            if (CollectionUtils.isEmpty(subCaseList)) {
              continue;
            }
            List<Long> caseIds = subCaseList.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
            Map<Long, Integer> countMap = new HashMap<>();
            if (systemConfig.getESSwitch()) {
              countMap = caseOperationService.selectCountByCaseIdsUsingEs(startDate, caseIds, null);
            } else {
              countMap = caseOperationService.selectCountByCaseIds(startDate, caseIds, null);
            }
            CtrlTypeRelResult ctrlResult = ctrlMap.get(teamId);
            Map<Long, Integer> finalCountMap = countMap;
            subCaseList.forEach(c -> {
              int count = finalCountMap.get(c.getId()) == null ? 0 : finalCountMap.get(c.getId());
              if (count >= ctrlResult.getTelCaseLimitDayTime()) {
                ctrlCaseIds.add(c.getId());
              }
            });
            start = end;
          }
        }
      }

    } else {
      Map<Long, List<CaseQueryResult>> caseMap = caseList.stream().collect(Collectors.groupingBy(CaseQueryResult::getOrgDeltId));
      List<Long> orgDeltIds = new ArrayList<>(caseMap.keySet());
      List<CtrlTypeRelResult> ctrlTypeRelResults = ctrlTypeRelService.queryCtrlByDeltType(orgDeltIds, 1, null);
      Map<Long, CtrlTypeRelResult> ctrlMap = ctrlTypeRelResults.stream().collect(Collectors.toMap(CtrlTypeRelResult::getOrgDeltId, f -> f));

      for (Long deltId : caseMap.keySet()) {
        List<CaseQueryResult> caseQueryResults = caseMap.get(deltId);
        if (ctrlMap.containsKey(deltId)) {
          int start = 0;
          int end = 0;
          while (start < caseQueryResults.size()) {
            end = start + 500;
            if (end > caseQueryResults.size()) {
              end = caseQueryResults.size();
            }
            List<CaseQueryResult> subCaseList = caseQueryResults.subList(start, end);
            if (CollectionUtils.isEmpty(subCaseList)) {
              continue;
            }
            List<Long> caseIds = subCaseList.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
            Map<Long, Integer> countMap = new HashMap<>();
            if (systemConfig.getESSwitch()) {
              countMap = caseOperationService.selectCountByCaseIdsUsingEs(startDate, caseIds, null);
            } else {
              countMap = caseOperationService.selectCountByCaseIds(startDate, caseIds, null);
            }
            CtrlTypeRelResult ctrlResult = ctrlMap.get(deltId);
            Map<Long, Integer> finalCountMap = countMap;
            subCaseList.forEach(c -> {
              int count = finalCountMap.get(c.getId()) == null ? 0 : finalCountMap.get(c.getId());
              if (count >= ctrlResult.getTelCaseLimitDayTime()) {
                ctrlCaseIds.add(c.getId());
              }
            });
            start = end;
          }
        }
      }
    }

    List<CaseQueryResult> delCaseList = caseList.stream().filter(item -> ctrlCaseIds.contains(item.getId())).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(delCaseList)) {
      caseList.removeAll(delCaseList);
    }
  }

  public void asyncAddPersonalFifo(CaseMultiQuery caseMultiQuery, CasePlan casePlan, OrgSwitch orgSwitch) throws Exception {
    // 只获取部分数据
    List<CaseQueryResult> caseList = new ArrayList<>();
    if (systemConfig.getESSwitch()) {
      //如果有es就走es查询，解决慢sql问题
      caseMultiQuery.setIgnorePlanTime(String.valueOf(DateUtils.getStartTimeOfDate(new Date()).getTime()));
      caseMultiQuery.setFields(Lists.newArrayList("id", "ownMobile","orgDeltId","teamId"));
      caseList = caseService.getAllCasesUsingEs(caseMultiQuery);
    } else {
      caseMultiQuery.setSelectField("ca.id,ca.own_mobile,ca.org_delt_id,ca.team_id");
      caseList = caseService.queryResultForMulti(caseMultiQuery);
    }
    // 过滤掉符合全局管控电催案件限制的案件
    filterGlobalCtrlCase(caseList, orgSwitch);
    if (CollectionUtils.isEmpty(caseList)) {
      throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
    }
    // 任务添加到任务列表
    redisUtil.sSet(KeyCache.CASE_FIFO_CASE_IDS + casePlan.getId(),24*60*60, caseList.stream().map(c -> c.getId().toString()).toArray(String[]::new));
    stringRedisTemplate.opsForList()
            .leftPush(KeyCache.CASE_FIFO_PLAN_ID_LIST, casePlan.getId().toString());
  }

  public void createPersonalFifo(List<CaseQueryResult> list, CasePlan casePlan,List<SiteVarsMapping> duyanVarsMappings, OrgSwitch orgSwitch) {
    List<Long> caseIds = list.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
    List<CaseContact> caseContactsAll = contactsMapper.selectContactByCaseIds(caseIds);
    List<CaseContact> caseContacts = Lists.newArrayList();
    if (casePlan.getDebtorOwn() != null && casePlan.getDebtorOwn() == 1) {
      List<CaseContact> ownList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 1)
              .filter(caseContact -> PatternUtils.isPlanPhone(caseContact.getContactMobile()))
              .collect(Collectors.toList());
      // 最新本人号码
      if (casePlan.getDebtorOwnType() != null &&
              Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.LAST.getCode())) {
        Map<Long, CaseContact> caseContactMap = ownList.stream().collect(Collectors.groupingBy(
                CaseContact::getCaseId, Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(CaseContact::getConUpdateTime)), Optional::get)));
        ownList.clear();
        ownList.addAll(caseContactMap.values());
      }
      // 随机本人号码
      if (casePlan.getDebtorOwnType() != null &&
              Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.RANDOM.getCode())) {
        List<CaseContact> randomCaseContacts = Lists.newArrayList();
        ownList.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> randomCaseContacts.add(v.get(0)));
        ownList.clear();
        ownList.addAll(randomCaseContacts);
      }
      caseContacts.addAll(ownList);
    }
    if (casePlan.getContacts() != null && casePlan.getContacts() == 1) {
      List<CaseContact> contactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 0)
              .filter(caseContact -> PatternUtils.isPlanPhone(caseContact.getContactMobile()))
              .collect(Collectors.toList());
      // 除本人外的所有号码类型
      if (StringUtils.isNotBlank(casePlan.getContactTypeIds())) {
        String contactTypeIdStr = casePlan.getContactTypeIds();
        List<Long> contactTypeIds = Stream.of(contactTypeIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
        if (contactTypeIds.size() > 1) {
          contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
        }
        if (contactTypeIds.size() == 1) {
          if (!Objects.equals(contactTypeIds.get(0), CasePlanEnums.ContactTypeEnum.ALL.getCode())) {
            contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
          }
        }
      }
      caseContacts.addAll(contactsList);
    }

    // 全局管控-获取已达案件号码拨打上限的号码信息，并进行移除
    List<CaseContact> delContacts = getMobileLimitContact(caseContacts, orgSwitch);
    if (!CollectionUtils.isEmpty(delContacts)) {
      caseContacts.removeAll(delContacts);
    }

    // 管控池管控 管控联系人、本人拨打频率
    List<CaseContact> ctrlContacts = getCaseCtrlLimit(caseContacts, orgSwitch);
    if (!CollectionUtils.isEmpty(ctrlContacts)) {
      caseContacts.removeAll(ctrlContacts);
    }

    // 先按手机号分组选择案件
    List<CaseContact> param1 = Lists.newArrayList();
    // 相同手机号，随机选其中一个案子参与计划
    if (!CollectionUtils.isEmpty(caseContacts)) {
      caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> param1.add(v.get(0)));
    }
    // 按案件分组选择第一个手机号
    List<CaseContact> param = Lists.newArrayList();
    // 相同手机号，随机选其中一个案子参与计划
    if (!CollectionUtils.isEmpty(caseContacts)) {
      param1.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile)).forEach((k, v) -> param.add(v.get(0)));
    }

    User user = userService.selectByPrimaryKey(casePlan.getUserId());
    if(CollectionUtils.isEmpty(param)){
      throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
    }

    // 创建个人预测式外呼计划的body
    List<Map<String,Object>> contents = new ArrayList<>();
    if (Objects.equals(casePlan.getFifoToRobotEnabled(), 1)) {
      Iterator<CaseContact> iterator = param.iterator();
      while (iterator.hasNext()) {
        CaseContact caseContact = iterator.next();
        Map<String, Object> caseContactMap = BeanUtils.beanToMap(caseContact);

        Long caseId = caseContact.getCaseId();
        Optional<CaseQueryResult> caseResultOptional = list.stream().filter(CaseQueryResult -> ObjectUtil.equals(caseId, CaseQueryResult.getId())).findFirst();
        CaseQueryResult caseQueryResult = caseResultOptional.get();
        Map<String, String> fieldJsonMap = caseQueryResult.getFieldJson();

        boolean varValueCheck = duyanVarsMappings.stream().anyMatch(varMapping -> {
          String anmiVar = varMapping.getAnmiVar();
          SiteEnums.Var varEmum = SiteEnums.Var.getByAnmiVar(anmiVar);
          Object value = null;
          if (ObjectUtil.isNull(varEmum)){
            value = fieldJsonMap.get(anmiVar);
          } else {
            String source = varEmum.getSource();
            String key = StringUtils.substringAfterLast(source, ".");

            if (source.startsWith("CaseContact")){
              value = caseContactMap.get(key);
            }else if (source.startsWith("CaseVO.fieldJson")){
              value = fieldJsonMap.get(key);
            }
          }
          return ObjectUtil.isNull(value);
        });

        if (varValueCheck){
          log.info("话术变量缺失,剔除,案件:{},手机号:{},contact_id:{}",caseId,caseContact.getContactMobile(),caseContact.getContactId());
          iterator.remove();
        }
      }

      // 变量过滤、全局管控过滤可能导致案件号码为空
      if (CollectionUtils.isEmpty(param)) {
        throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
      }
      if (ObjectUtil.isNotEmpty(param)) {
        for (int i = 0; i< param.size(); i++) {
          CaseContact caseContact = param.get(i);
          Long caseId = caseContact.getCaseId();
          Optional<CaseQueryResult> CaseQueryResultOptional = list.stream().filter(CaseQueryResult -> ObjectUtil.equals(caseId, CaseQueryResult.getId())).findFirst();
          CaseQueryResult caseQueryResult = CaseQueryResultOptional.get();
          Map<String, String> fieldJsonMap = caseQueryResult.getFieldJson();

          addContent(caseContact,casePlan.getOrgId(),fieldJsonMap, contents,duyanVarsMappings, casePlan);
        }
      }
    }

    Long duyanPlanId = manager.createNewPersonalFifo(param, casePlan, user.getDuyanAccountId(), contents);
    if (duyanPlanId == null) {
      throw new ApiException("调用创建预测式计划接口失败");
    }
    casePlan.setDuyanPlanId(duyanPlanId);
    casePlanMapper.insertSelective(casePlan);
    // 计划与案件关联
    casePlanRelService.saveCasePlanRelBatch(param.stream().map(CaseContact::getCaseId).collect(Collectors.toList()),
        casePlan.getId(), getTokenUser(), CasePlanRelEnums.Type.PERSONALFIFO.getCode());
    this.createCasePlanRelContact(casePlan,caseContacts,
        param.stream().map(CaseContact::getContactMobile).collect(Collectors.toList()));
  }

  /**
   * 获取全局管控上限联系人信息
   *
   * @param caseContacts 全部联系人信息
   * @param orgSwitch    开关信息
   */
  public List<CaseContact> getMobileLimitContact(List<CaseContact> caseContacts, OrgSwitch orgSwitch) {
    List<CaseContact> delContacts = new ArrayList<>();
    // 查询案件号码是否进入到全局管控，过滤掉已满足电催管控号码限制次数的案件
    if (Objects.equals(orgSwitch.getGlobalCtrlSwitch(), OrgSwitchEnums.GlobalCtrlSwitch.CLOSE.getCode())
            || CollectionUtils.isEmpty(caseContacts)) {
      return delContacts;
    }
    Map<Long, List<CaseContact>> contactMap = new HashMap<>();
    Map<Long, CtrlTypeRelResult> ctrlMap = new HashMap<>();
    // 根据管控维度将案件进行分组
    if (Objects.equals(orgSwitch.getGlobalCtrlType(), OrgSwitchEnums.GlobalCtrlType.TEAM.getCode())) {
      contactMap = caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getTeamId));
      List<Long> teamIds = new ArrayList<>(contactMap.keySet());
      List<CtrlTypeRelResult> ctrlTypeRelResults = ctrlTypeRelService.queryCtrlByTeamType(teamIds, 1, null);
      ctrlMap = ctrlTypeRelResults.stream().collect(Collectors.toMap(CtrlTypeRelResult::getTeamId, f -> f));
    }

    if (Objects.equals(orgSwitch.getGlobalCtrlType(), OrgSwitchEnums.GlobalCtrlType.DELT.getCode())) {
      contactMap = caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getOrgDeltId));
      List<Long> orgDeltIds = new ArrayList<>(contactMap.keySet());
      List<CtrlTypeRelResult> ctrlTypeRelResults = ctrlTypeRelService.queryCtrlByDeltType(orgDeltIds, 1, null);
      ctrlMap = ctrlTypeRelResults.stream().collect(Collectors.toMap(CtrlTypeRelResult::getOrgDeltId, f -> f));
    }
    for (Long typeId : contactMap.keySet()) {
      if (ctrlMap.containsKey(typeId)) {
        List<CaseContact> caseContactList = contactMap.get(typeId);
        int start = 0;
        int end = 0;
        while (start < caseContactList.size()) {
          end = start + 500;
          if (end > caseContactList.size()) {
            end = caseContactList.size();
          }
          List<CaseContact> subContactList = caseContactList.subList(start, end);
          if (CollectionUtils.isEmpty(subContactList)) {
            continue;
          }
          List<Long> caseIdList = subContactList.stream().map(CaseContact::getCaseId).distinct().collect(Collectors.toList());
          List<String> conMobiles = subContactList.stream().map(CaseContact::getContactMobile).collect(Collectors.toList());
          CtrlTypeRelResult ctrlResult = ctrlMap.get(typeId);
          Date startDate = DateUtil.beginOfDay(new Date());
          if (Objects.equals(ctrlResult.getTelMobileLimitType(), 1)) {
            startDate = DateUtil.beginOfHour(new Date());
          }
          Map<Long, List<CaseLimitCountResult>> limitCountMap = new HashMap<>();
          if (systemConfig.getESSwitch()) {
            limitCountMap = caseOperationService.selectCountByCaseMobilesUsingEs(startDate, caseIdList, conMobiles);
          } else {
            limitCountMap = caseOperationService.selectCountByCaseMobiles(startDate, caseIdList, conMobiles);
          }

          Map<Long, List<CaseLimitCountResult>> finalLimitCountMap = limitCountMap;
          subContactList.forEach(c -> {
            List<CaseLimitCountResult> caseLimitCountResults = finalLimitCountMap.get(c.getCaseId());
            if (!CollectionUtils.isEmpty(caseLimitCountResults)) {
              CaseLimitCountResult caseLimitCountResult = caseLimitCountResults.stream().filter(r -> StringUtils.equals(r.getConMobile(), c.getContactMobile())).findFirst().orElse(null);
              if (caseLimitCountResult != null && caseLimitCountResult.getCount() >= ctrlResult.getTelMobileLimitTime()) {
                log.info("案件号码达到上限,进行过滤,管控维度:{},案件:{},手机号:{},contact_id:{}",
                        OrgSwitchEnums.GlobalCtrlType.getGlobalTypeInfo(orgSwitch.getGlobalCtrlType()),
                        c.getCaseId(),c.getContactMobile(),c.getContactId());
                delContacts.add(c);
              }
            }
          });
          start = end;
        }
      }
    }
    return delContacts;
  }

  public PageOutput<CasePlanVO> queryPersonalFifoList(
      CasePlanParam casePlanParam, PageParam pageParam) {
    List<String> duyanPlanStatusList =
        Lists.newArrayList("UPLOADING", "NOT_STARTED", "IN_PROGRESS", "PAUSED");
    String planStatus = casePlanParam.getPlanStatus();
    if (StringUtils.isNotBlank(planStatus) && duyanPlanStatusList.contains(planStatus)) {
      // 需到度言查找计划信息 再过滤 再分页
      return queryByDuyanStatus(
          casePlanParam, pageParam, CasePlanEnums.Type.PERSONALFIFO.getCode());
    }
    Example example = buildPersonalFiFoPlanExample(casePlanParam, planStatus);
    PageOutput pageOutput = super.selectByPage(example, pageParam);
    List<CasePlan> casePlanList = pageOutput.getList();
    if (CollectionUtils.isEmpty(casePlanList)) {
      return pageOutput;
    }
    // 除已完成的案件外，其他案件状态需到度言进行搜索
    List<Long> duyanPlanIds =
        casePlanList.stream()
            .filter(c -> c.getStatus().equals(CasePlanEnums.Status.ING.getCode())
                ||c.getStatus().equals(CasePlanEnums.Status.FINISHED.getCode()))
            .map(CasePlan::getDuyanPlanId)
            .collect(Collectors.toList());
    List<CasePlanTmp> casePlanTmpList = manager.getNewPlanListByIds(duyanPlanIds);
    Map<Long, CasePlanTmp> casePlanTmpMap =
        casePlanTmpList.stream().collect(Collectors.toMap(CasePlanTmp::getDuyanPlanId, r -> r));
    List<CasePlanVO> vos = new ArrayList<>();
    for (CasePlan casePlan : casePlanList) {
      CasePlanTmp casePlanTmp = casePlanTmpMap.get(casePlan.getDuyanPlanId());
      CasePlanVO vo = convertFiFoPlanVO(casePlan, casePlanTmp);
      vos.add(vo);
    }
    pageOutput.setList(vos);
    return pageOutput;
  }

  private Example buildPersonalFiFoPlanExample(CasePlanParam casePlanParam, String planStatus) {
    UserSession userSession = getTokenUser();
    Example example = new Example(CasePlan.class);
    Example.Criteria planCrt = example.createCriteria();
    example.setOrderByClause("id desc");
    planCrt
        .andEqualTo("orgId", userSession.getOrgId())
        .andEqualTo("type", CasePlanEnums.Type.PERSONALFIFO.getCode());
    if (StringUtils.isNotBlank(planStatus)) {
      planCrt.andEqualTo("status", CasePlanEnums.Status.valueOf(planStatus).getCode());
    }
    if (UserUtils.likeBranchAdmin()) {
      planCrt.andEqualTo("depId", userSession.getDepId());
    }
    if (UserUtils.likeTeamLeader()) {
      planCrt.andEqualTo("teamId", userSession.getTeamId());
    }
    if (CasePlanEnums.Type.PERSONALFIFO.getCode().equals(casePlanParam.getType())) {
      planCrt.andEqualTo("userId", userSession.getId());
    }
    if (!StringUtils.isBlank(casePlanParam.getUserIds())) {
      planCrt.andIn("userId", Arrays.asList(casePlanParam.getUserIds().split(",")));
    }
    if (casePlanParam.getPlanCreatedDate() != null
        && casePlanParam.getPlanCreatedDate().split(",").length == 2) {
      casePlanParam.setPlanCreatedDateStart(
          convertDate(casePlanParam.getPlanCreatedDate().split(",")[0]));
      casePlanParam.setPlanCreatedDateEnd(
          convertDate(casePlanParam.getPlanCreatedDate().split(",")[1]));
      planCrt.andGreaterThanOrEqualTo("createTime", casePlanParam.getPlanCreatedDateStart());
      planCrt.andLessThanOrEqualTo("createTime", casePlanParam.getPlanCreatedDateEnd());
    }
    if (StringUtils.isNotBlank(casePlanParam.getPriority())) {
      planCrt.andEqualTo("priority", casePlanParam.getPriority());
    }
    return example;
  }

  public CasePlanBaseInfoVO baseInfo(Long campaignId) {
    CasePlanBaseInfo casePlanBaseInfo = newRobotManager.casePlanBaseInfo(campaignId);
    CasePlanBaseInfoVO casePlanBaseInfoVO = BeanUtils.copyProperties(casePlanBaseInfo, CasePlanBaseInfoVO.class);
    Long roundCount = ObjectUtil.isNull(casePlanBaseInfo.getRetryCount())?1L:casePlanBaseInfo.getRetryCount()+1;
    casePlanBaseInfoVO.setRoundCount(roundCount);
    return casePlanBaseInfoVO;
  }

  public List<RobotInfoTemp> getNewRobotList() {
    return newRobotManager.robotList();
  }

  /**
   * 新机器人计划的body
   *
   * @param caseContact       caseContact
   * @param orgId             orgId
   * @param fieldJsonMap      fieldJsonMap
   * @param contents          contents
   * @param duyanVarsMappings duyanVarsMappings
   * @param casePlan          casePlan
   */
  public void addContent(CaseContact caseContact, Long orgId, Map<String, String> fieldJsonMap, List<Map<String, Object>> contents, List<SiteVarsMapping> duyanVarsMappings, CasePlan casePlan) {
    Map<String, Object> content = new HashMap<>();
    if (!PatternUtils.isPlanPhone(caseContact.getContactMobile())){
      return;
    }

    Map<String, Object> caseContactMap = BeanUtils.beanToMap(caseContact);

    duyanVarsMappings.forEach(varMapping -> {
      String anmiVar = varMapping.getAnmiVar();
      try {
        SiteEnums.Var varEmum = SiteEnums.Var.getByAnmiVar(anmiVar);
        String key;
        Object value = null;
        if (ObjectUtil.isNull(varEmum)){
          key = anmiVar;
          value = fieldJsonMap.get(anmiVar);
        } else {
          String source = varEmum.getSource();
          key = StringUtils.substringAfterLast(source, ".");

          if (source.startsWith("CaseContact")){
            value = caseContactMap.get(key);
          }else if (source.startsWith("CaseVO.fieldJson")){
            value = fieldJsonMap.get(key);
          }
        }

        List<String> amountFields = Arrays.asList("amount","payAmount","capital","rest_capital","overdue_rate","overdue_punish_rate","penalty",
                "overdue_management_fee","late_fee","settled_amt","amount_per_period","repay_min_amt","amount_unpaid","pro_price","down_payment",
                "total_amount_paid","load_amount","rate");

        if(ObjectUtil.equals(key,"overdueDate") || ObjectUtil.equals(key,"entrustEndTime")){
          Date dateValue = (Date)value;
          content.put(varMapping.getDuyanVar(),DateUtils.formatDate(dateValue, DateUtils.SIMPLE_DATE_FORMAT));
        }else if(amountFields.contains(key)){
          String valueStr = String.valueOf(value);
          content.put(varMapping.getDuyanVar(),valueStr);
          if (PatternUtils.isAnmiAmount(valueStr)){
            Long amountValue = Long.valueOf(valueStr);
            content.put(varMapping.getDuyanVar(),amountValue / 1000.00);
          }
        }else if(ObjectUtil.equals(key,"orgDeltId")){
          Long orgDeltId = Long.valueOf(String.valueOf(value));
          content.put(varMapping.getDuyanVar(),deltService.getNames().get(orgDeltId));
        }else {
          content.put(varMapping.getDuyanVar(),value);
        }
      }  catch (Exception e){
        log.error("新机器人计划的body组装异常,anmiVar:{},{}",anmiVar, ExceptionUtil.stacktraceToString(e));
      }
    });
    if (Objects.equals(casePlan.getType(), CasePlanEnums.Type.PERSONALFIFO.getCode())) {
      content.put("U_TAG", caseContact.getCaseId() + "，" + CasePlanEnums.AllotAgent.PERSONAL.getCode());
    }
    if (Objects.equals(casePlan.getType(), CasePlanEnums.Type.FIFO.getCode())) {
      content.put("U_TAG", caseContact.getCaseId() + "，" + casePlan.getAllotAgent());
    }
    if (Objects.equals(casePlan.getType(), CasePlanEnums.Type.NEWROBOT.getCode())) {
      content.put("U_TAG", caseContact.getCaseId() + "，" + casePlan.getAllotAgent());
    }

    // 以下为固定项
    content.put("U_orgId", orgId);
    content.put("U_caseId",caseContact.getCaseId());
    content.put("U_phone", caseContact.getContactMobile());

    contents.add(content);
  }

  /**
   * 单手机号的呼叫轮次（新机器人）
   *
   * @param campaignId 度言计划id
   * @param phone      客户号码
   * @return {@link List}<{@link CallRoundInfoVO}>
   */
  public List<CallRoundInfoVO> getPhoneCallRounds(Long campaignId, String phone) {
      return newRobotManager.getPhoneCallRounds(campaignId,phone);
  }

  /**
   * 查询预测式外呼计划详情图表数据
   *
   * @param campaignId 度言计划id
   * @return
   */
  public StatisticsPlanVO queryFifoChart(Long campaignId) {
    StatisticsPlanRes fifoStatistics = manager.getFifoStatistics(campaignId);
    StatisticsPlanVO statisticsPlanVO = new StatisticsPlanVO();
    BeanUtils.copyProperties(fifoStatistics, statisticsPlanVO);
    return statisticsPlanVO;
  }

  /**
   * 新版机器人计划和预测式外呼计划在 度言那边是同一个接口，这里做声明
   * @param campaignId
   * @return
   */
  public StatisticsPlanVO statisticsPlan(Long campaignId, Integer round) {
    StatisticsPlanRes statisticsPlanRes = manager.statisticsPlan(campaignId, round);
    return AnmiBeanutils.copy(statisticsPlanRes, StatisticsPlanVO.class);
  }

  /**
   * 查询预测式外呼计划基本信息
   *
   * @param campaignId 度言计划id
   * @return
   */
  public CasePlanBaseInfoVO queryFifoPlanBaseInfo(Long campaignId) {
    CasePlan casePlan = selectCasePlanByDuyanPlanId(campaignId);
    CasePlanBaseInfo fifoPlanInfo = manager.getFifoPlanInfo(campaignId);
    CasePlanBaseInfoVO vo = BeanUtils.copyProperties(fifoPlanInfo, CasePlanBaseInfoVO.class);
    if (ObjectUtil.isNotNull(vo) && ObjectUtil.isNotNull(vo.getAccountId())) {
      User user = userService.selectUserByAccountId(vo.getAccountId());
      if (user != null) {
        vo.setAccountName(user.getName());
      }
    }
    if (ObjectUtil.isNotNull(vo) && ObjectUtil.isNotNull(vo.getTeamId())) {
      DepTeam depTeam = depTeamService.selectDepTeamByDuyanId(vo.getTeamId());
      if (depTeam != null) {
        vo.setTeamName(depTeam.getName());
      }
    }
    // 转接话术
    vo.setSiteName(casePlan.getSiteName());
    vo.setFailRetry(casePlan.getFailRetry());
    vo.setFifoToRobotEnabled(casePlan.getFifoToRobotEnabled());
    return vo;
  }

  /**
   * 获取案例计划
   *
   * @param casePlanIds 案例计划ID
   * @return {@link List}<{@link CasePlan}>
   */
  public List<CasePlan> selectCasePlans(List<Long> casePlanIds) {
    Example example = new Example(CasePlan.class);
    example.selectProperties("id","orgId");
    example.setOrderByClause("id asc");

    Example.Criteria criteria = example.createCriteria();
    criteria.andIn("id", casePlanIds);

    return selectByExample(example);
  }
  /**
   * 过滤掉符合全局管控短信发送成功数量的案件
   *
   * @param caseList   案件列表
   * @param orgSwitch  开关
   */
  public void filterGlobalCtrlSmsCase(List<CaseQueryResult> caseList, OrgSwitch orgSwitch) {
    if (CollectionUtils.isEmpty(caseList) ||
            Objects.equals(orgSwitch.getGlobalCtrlSwitch(), OrgSwitchEnums.GlobalCtrlSwitch.CLOSE.getCode())) {
      return;
    }
    List<Long> ctrlCaseIds = new ArrayList<>();
    Date startDate = DateUtil.beginOfDay(new Date());
    if (Objects.equals(orgSwitch.getGlobalCtrlType(), OrgSwitchEnums.GlobalCtrlType.TEAM.getCode())) {
      Map<Long, List<CaseQueryResult>> caseMap = caseList.stream().filter(c -> ObjectUtil.isNotNull(c.getTeamId())).collect(Collectors.groupingBy(CaseQueryResult::getTeamId));
      List<Long> teamIds = new ArrayList<>(caseMap.keySet());
      List<CtrlTypeRelResult> ctrlTypeRelResults = ctrlTypeRelService.queryCtrlByTeamType(teamIds, null, 1);
      Map<Long, CtrlTypeRelResult> ctrlMap = ctrlTypeRelResults.stream().collect(Collectors.toMap(CtrlTypeRelResult::getTeamId, f -> f));
      for (Long teamId : caseMap.keySet()) {
        if (ctrlMap.containsKey(teamId)) {
          List<CaseQueryResult> caseQueryResults = caseMap.get(teamId);
          int start = 0;
          int end = 0;
          while (start < caseQueryResults.size()) {
            end = start + 500;
            if (end > caseQueryResults.size()) {
              end = caseQueryResults.size();
            }
            List<CaseQueryResult> subCaseList = caseQueryResults.subList(start, end);
            List<Long> caseIds = subCaseList.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
            Map<Long, Integer> countMap = smsSendService.selectCountByCaseIds(startDate, caseIds);
            CtrlTypeRelResult ctrlResult = ctrlMap.get(teamId);
            subCaseList.forEach(c -> {
              int count = countMap.get(c.getId()) == null ? 0 : countMap.get(c.getId());
              if (count >= ctrlResult.getSmsCaseLimitDayTime()) {
                ctrlCaseIds.add(c.getId());
              }
            });
            start = end;
          }
        }
      }
    }
    if (Objects.equals(orgSwitch.getGlobalCtrlType(), OrgSwitchEnums.GlobalCtrlType.DELT.getCode())) {
      Map<Long, List<CaseQueryResult>> caseMap = caseList.stream().filter(c -> ObjectUtil.isNotNull(c.getOrgDeltId())).collect(Collectors.groupingBy(CaseQueryResult::getOrgDeltId));
      List<Long> deltIds = new ArrayList<>(caseMap.keySet());
      List<CtrlTypeRelResult> ctrlTypeRelResults = ctrlTypeRelService.queryCtrlByDeltType(deltIds, null, 1);
      Map<Long, CtrlTypeRelResult> ctrlMap = ctrlTypeRelResults.stream().collect(Collectors.toMap(CtrlTypeRelResult::getOrgDeltId, f -> f));
      for (Long deltId : caseMap.keySet()) {
        if (ctrlMap.containsKey(deltId)) {
          List<CaseQueryResult> caseQueryResults = caseMap.get(deltId);
          int start = 0;
          int end = 0;
          while (start < caseQueryResults.size()) {
            end = start + 500;
            if (end > caseQueryResults.size()) {
              end = caseQueryResults.size();
            }
            List<CaseQueryResult> subCaseList = caseQueryResults.subList(start, end);
            if (CollectionUtils.isEmpty(subCaseList)) {
              continue;
            }
            List<Long> caseIds = subCaseList.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
            Map<Long, Integer> countMap = smsSendService.selectCountByCaseIds(startDate, caseIds);
            CtrlTypeRelResult ctrlResult = ctrlMap.get(deltId);
            subCaseList.forEach(c -> {
              int count = countMap.get(c.getId()) == null ? 0 : countMap.get(c.getId());
              if (count >= ctrlResult.getSmsCaseLimitDayTime()) {
                ctrlCaseIds.add(c.getId());
              }
            });
            start = end;
          }
        }
      }
    }
    List<CaseQueryResult> delCaseList = caseList.stream().filter(item -> ctrlCaseIds.contains(item.getId())).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(delCaseList)) {
      caseList.removeAll(delCaseList);
    }
  }

  /**
   * 获取全局管控短信上限联系人信息
   *
   * @param caseContacts 联系人信息
   * @param orgSwitch    开关
   * @return
   */
  public List<CaseContact> getMobileSmsLimitContact(List<CaseContact> caseContacts, OrgSwitch orgSwitch) {
    List<CaseContact> delContacts = new ArrayList<>();
    // 查询案件号码是否进入到全局管控，过滤掉已满足电催管控号码限制次数的案件
    if (Objects.equals(orgSwitch.getGlobalCtrlSwitch(), OrgSwitchEnums.GlobalCtrlSwitch.CLOSE.getCode())) {
      return delContacts;
    }
    Map<Long, List<CaseContact>> contactMap = new HashMap<>();
    Map<Long, CtrlTypeRelResult> ctrlMap = new HashMap<>();
    // 根据管控维度将案件进行分组
    if (Objects.equals(orgSwitch.getGlobalCtrlType(), OrgSwitchEnums.GlobalCtrlType.TEAM.getCode())) {
      contactMap = caseContacts.stream().filter(c -> ObjectUtil.isNotNull(c.getTeamId())).collect(Collectors.groupingBy(CaseContact::getTeamId));
      List<Long> teamIds = new ArrayList<>(contactMap.keySet());
      List<CtrlTypeRelResult> ctrlTypeRelResults = ctrlTypeRelService.queryCtrlByTeamType(teamIds, null, 1);
      ctrlMap = ctrlTypeRelResults.stream().collect(Collectors.toMap(CtrlTypeRelResult::getTeamId, f -> f));
    }
    if (Objects.equals(orgSwitch.getGlobalCtrlType(), OrgSwitchEnums.GlobalCtrlType.DELT.getCode())) {
      contactMap = caseContacts.stream().filter(c -> ObjectUtil.isNotNull(c.getOrgDeltId())).collect(Collectors.groupingBy(CaseContact::getOrgDeltId));
      List<Long> orgDeltIds = new ArrayList<>(contactMap.keySet());
      List<CtrlTypeRelResult> ctrlTypeRelResults = ctrlTypeRelService.queryCtrlByDeltType(orgDeltIds, null, 1);
      ctrlMap = ctrlTypeRelResults.stream().collect(Collectors.toMap(CtrlTypeRelResult::getOrgDeltId, f -> f));
    }
    Date startDate = DateUtil.beginOfDay(new Date());
    for (Long typeId : contactMap.keySet()) {
      if (ctrlMap.containsKey(typeId)) {
        List<CaseContact> caseContactList = contactMap.get(typeId);
        List<Long> caseIdList = caseContactList.stream().map(CaseContact::getCaseId).distinct().collect(Collectors.toList());
        List<String> conMobiles = caseContactList.stream().map(CaseContact::getContactMobile).collect(Collectors.toList());
        CtrlTypeRelResult ctrlResult = ctrlMap.get(typeId);
        Map<Long, List<MobileLimitCountResult>> limitCountMap = smsSendService.selectCountByCaseMobiles(startDate, caseIdList, conMobiles);
        caseContactList.forEach(c -> {
          List<MobileLimitCountResult> mobileLimitCountResults = limitCountMap.get(c.getCaseId());
          MobileLimitCountResult result = null;
          if (!CollectionUtils.isEmpty(mobileLimitCountResults)) {
            result = mobileLimitCountResults.stream().filter(r -> StringUtils.equals(r.getRelationMobile(), c.getContactMobile())).findFirst().orElse(null);
          }
          if (result != null && result.getCount() >= ctrlResult.getSmsMobileLimitDayTime()) {
            log.info("案件号码达到上限,进行过滤,管控维度:{},案件:{},手机号:{},contact_id:{}",
                    OrgSwitchEnums.GlobalCtrlType.getGlobalTypeInfo(orgSwitch.getGlobalCtrlType()),
                    c.getCaseId(),c.getContactMobile(),c.getContactId());
            delContacts.add(c);
          }
        });
      }
    }
    return delContacts;
  }

  /**
   * 查询已进入管控池的案件达到管控限制
   *
   * @param caseContacts 案件信息
   * @param orgSwitch    开关
   * @return
   */
  public List<CaseContact> getCaseCtrlLimit(List<CaseContact> caseContacts, OrgSwitch orgSwitch) {
    List<CaseContact> delContacts = new ArrayList<>();
    // 查询案件号码是否进入到全局管控，过滤掉已满足电催管控号码限制次数的案件
    if (Objects.equals(orgSwitch.getCtrlSwitch(), OrgSwitchEnums.CaseCtrlSwitch.CLOSE.getCode())) {
      return delContacts;
    }
    List<CaseContact> caseContactList = caseContacts.stream().filter(c->Objects.nonNull(c.getCtrlId())).collect(Collectors.toList());
    List<Long> ctrlIds = caseContactList.stream().map(CaseContact::getCtrlId).distinct().collect(Collectors.toList());
    List<CaseCtrlPool> caseCtrlPools = caseCtrlService.selectByIdList(ctrlIds);
    if (CollectionUtils.isEmpty(caseCtrlPools)) {
      return delContacts;
    }
    Map<Long, CaseCtrlPool> ctrlPoolMap = caseCtrlPools.stream().collect(Collectors.toMap(CaseCtrlPool::getId, f -> f));
    Map<Long, List<CaseContact>> ctrlMap = caseContactList.stream().collect(Collectors.groupingBy(CaseContact::getCtrlId));
    // 本人管控池校验
    for (Long ctrlId : ctrlMap.keySet()) {
      List<CaseContact> contactList = ctrlMap.get(ctrlId);
      CaseCtrlPool caseCtrlPool = ctrlPoolMap.get(ctrlId);
      List<CaseContact> ownList = contactList.stream().filter(obj -> obj.getOwnSign() == 1).collect(Collectors.toList());
      List<CaseContact> conList = contactList.stream().filter(obj -> obj.getOwnSign() == 0).collect(Collectors.toList());

      // 本人呼叫次数
      if (Objects.equals(caseCtrlPool.getDebtorOwn(), 1) && !CollectionUtils.isEmpty(ownList)) {
        List<Long> ownCaseIds = ownList.stream().map(CaseContact::getCaseId).distinct().collect(Collectors.toList());
        // 本人天数限制内的呼叫次数
        Date date = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1 - caseCtrlPool.getOwnLimitDay()));
        Map<Long, Integer> countDayMap = new HashMap<>();
        if (systemConfig.getESSwitch()) {
          countDayMap = caseOperationService.selectCountByCaseIdsUsingEs(date, ownCaseIds, 0);
        } else {
          countDayMap = caseOperationService.selectCountByCaseIds(date, ownCaseIds, 0);
        }
        Iterator<CaseContact> iterator1 = ownList.iterator();
        while (iterator1.hasNext()) {
          CaseContact caseContact = iterator1.next();
          if (countDayMap.containsKey(caseContact.getCaseId())) {
            int count = countDayMap.get(caseContact.getCaseId()) == null ? 0 : countDayMap.get(caseContact.getCaseId());
            if (count >= caseCtrlPool.getOwnLimitDayTime()) {
              delContacts.add(caseContact);
              log.info("管控池管控移除，本人{}天内的呼叫号码已达拨打上限，案件:{},联系人:{}",
                      caseCtrlPool.getOwnLimitDay(),caseContact.getCaseId(), caseContact.getContactId());
              iterator1.remove();
            }
          }
        }

        // 本人小时内的呼叫次数
        Date startHourDate = DateUtil.beginOfHour(DateUtil.offsetHour(new Date(), 1 - caseCtrlPool.getOwnLimitHour()));
        Map<Long, Integer> countHourMap = new HashMap<>();
        if (systemConfig.getESSwitch()) {
          countHourMap = caseOperationService.selectCountByCaseIdsUsingEs(startHourDate, ownCaseIds, 0);
        } else {
          countHourMap = caseOperationService.selectCountByCaseIds(startHourDate, ownCaseIds, 0);
        }
        Iterator<CaseContact> iterator2 = ownList.iterator();
        while (iterator2.hasNext()) {
          CaseContact caseContact = iterator2.next();
          if (countHourMap.containsKey(caseContact.getCaseId())) {
            int count = countHourMap.get(caseContact.getCaseId()) == null ? 0 : countHourMap.get(caseContact.getCaseId());
            if (count >= caseCtrlPool.getOwnLimitHourTime()) {
              delContacts.add(caseContact);
              log.info("管控池管控移除，本人{}小时内的呼叫号码已达拨打上限，案件:{},联系人:{}",
                      caseCtrlPool.getOwnLimitHour(),caseContact.getCaseId(), caseContact.getContactId());
              iterator2.remove();
            }
          }
        }
      }


      if (Objects.equals(caseCtrlPool.getContacts(), 1) && !CollectionUtils.isEmpty(conList)) {
        List<Long> contactCaseIds = conList.stream().map(CaseContact::getCaseId).distinct().collect(Collectors.toList());
        // 联系人限制天内的呼叫次数
        Date date = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), 1 - caseCtrlPool.getConLimitDay()));
        // 联系人全部号码-天频次
        if (Objects.equals(caseCtrlPool.getConLimitType(), 0)) {
          Map<Long, Integer> countDayMap = new HashMap<>();
          if (systemConfig.getESSwitch()) {
            countDayMap = caseOperationService.selectCountByCaseIdsUsingEs(date, contactCaseIds, 1);
          } else {
            countDayMap = caseOperationService.selectCountByCaseIds(date, contactCaseIds, 1);
          }
          Iterator<CaseContact> conIterator1 = conList.iterator();
          while (conIterator1.hasNext()) {
            CaseContact caseContact = conIterator1.next();
            if (countDayMap.containsKey(caseContact.getCaseId())) {
              int count = countDayMap.get(caseContact.getCaseId()) == null ? 0 : countDayMap.get(caseContact.getCaseId());
              if (count >= caseCtrlPool.getConLimitDayTime()) {
                delContacts.add(caseContact);
                log.info("管控池管控移除，联系人{}天内的呼叫号码已达拨打上限，案件:{},联系人:{}",
                        caseCtrlPool.getConLimitDay(), caseContact.getCaseId(), caseContact.getContactId());
                conIterator1.remove();
              }
            }
          }
        }

        // 联系人同一号码-天频次/小时频次
        if (Objects.equals(caseCtrlPool.getConLimitType(), 1)) {
          List<String> mobiles = conList.stream().map(CaseContact::getContactMobile).distinct().collect(Collectors.toList());
          // 同一号码-天限制频次
          Map<Long,List<CaseLimitCountResult>> countDayMap = new HashMap<>();
          if (systemConfig.getESSwitch()) {
            countDayMap = caseOperationService.selectCountByCaseMobilesUsingEs(date, contactCaseIds, mobiles);
          } else {
            countDayMap = caseOperationService.selectCountByCaseMobiles(date, contactCaseIds, mobiles);
          }
          Iterator<CaseContact> conIterator1 = conList.iterator();
          while (conIterator1.hasNext()) {
            CaseContact caseContact = conIterator1.next();
            if (countDayMap.containsKey(caseContact.getCaseId())) {
              List<CaseLimitCountResult> caseLimitCountResults = countDayMap.get(caseContact.getCaseId());
              if (!CollectionUtils.isEmpty(caseLimitCountResults)) {
                CaseLimitCountResult caseLimitCountResult = caseLimitCountResults.stream().filter(r -> StringUtils.equals(r.getConMobile(), caseContact.getContactMobile())).findFirst().orElse(null);
                if (caseLimitCountResult != null && caseLimitCountResult.getCount() >= caseCtrlPool.getConLimitDayTime()) {
                  delContacts.add(caseContact);
                  log.info("管控池管控移除，联系人{}天内的呼叫号码已达拨打上限，案件:{},联系人:{}",
                          caseCtrlPool.getConLimitDay(), caseContact.getCaseId(), caseContact.getContactId());
                  conIterator1.remove();
                }
              }
            }
          }

          // 同一号码-小时限制频次
          Date startHourDate = DateUtil.beginOfHour(DateUtil.offsetHour(new Date(), 1 - caseCtrlPool.getConLimitHour()));
          Map<Long, List<CaseLimitCountResult>> countHourMap = new HashMap<>();
          if (systemConfig.getESSwitch()) {
            countHourMap = caseOperationService.selectCountByCaseMobilesUsingEs(startHourDate, contactCaseIds, mobiles);
          } else {
            countHourMap = caseOperationService.selectCountByCaseMobiles(startHourDate, contactCaseIds, mobiles);
          }
          Iterator<CaseContact> conIterator2 = conList.iterator();
          while (conIterator2.hasNext()) {
            CaseContact caseContact = conIterator2.next();
            if (countHourMap.containsKey(caseContact.getCaseId())) {
              List<CaseLimitCountResult> caseLimitCountResults = countHourMap.get(caseContact.getCaseId());
              if (!CollectionUtils.isEmpty(caseLimitCountResults)) {
                CaseLimitCountResult caseLimitCountResult = caseLimitCountResults.stream().filter(r -> StringUtils.equals(r.getConMobile(), caseContact.getContactMobile())).findFirst().orElse(null);
                if (caseLimitCountResult != null && caseLimitCountResult.getCount() >= caseCtrlPool.getConLimitHourTime()) {
                  delContacts.add(caseContact);
                  log.info("管控池管控移除，联系人{}小时内的呼叫号码已达拨打上限，案件:{},联系人:{}",
                          caseCtrlPool.getConLimitHour(), caseContact.getCaseId(), caseContact.getContactId());
                  conIterator2.remove();
                }
              }
            }
          }
        }

      }
    }
    return delContacts;
  }

  /**
   * 查询符合全局管控电催案件限制的案件
   *
   * @param caseList  案件id
   * @param orgSwitch 开关
   */
  public List<Long> getGlobalCtrlLimitCase(List<Case> caseList, OrgSwitch orgSwitch) {
    // 案件列表为空，全局管控开关为空满足其一，不进行处理
    if (CollectionUtils.isEmpty(caseList) ||
            Objects.equals(orgSwitch.getGlobalCtrlSwitch(), OrgSwitchEnums.GlobalCtrlSwitch.CLOSE.getCode())) {
      return Collections.emptyList();
    }
    // 查询案件是否进入到全局管控,过滤掉已满足电催管控案件限制次数的案件
    List<Long> ctrlCaseIds = new ArrayList<>();
    Date startDate = DateUtil.beginOfDay(new Date());
    // 根据管控维度将案件进行分组
    if (Objects.equals(orgSwitch.getGlobalCtrlType(), OrgSwitchEnums.GlobalCtrlType.TEAM.getCode())) {
      Map<Long, List<Case>> caseMap = caseList.stream().collect(Collectors.groupingBy(Case::getTeamId));
      List<Long> teamIds = new ArrayList<>(caseMap.keySet());
      List<CtrlTypeRelResult> ctrlTypeRelResults = ctrlTypeRelService.queryCtrlByTeamType(teamIds, 1, null);
      Map<Long, CtrlTypeRelResult> ctrlMap = ctrlTypeRelResults.stream().collect(Collectors.toMap(CtrlTypeRelResult::getTeamId, f -> f));
      for (Long teamId : caseMap.keySet()) {
        if (ctrlMap.containsKey(teamId)) {
          List<Case> cases = caseMap.get(teamId);
          int start = 0;
          int end = 0;
          while (start < cases.size()) {
            end = start + 500;
            if (end > cases.size()) {
              end = cases.size();
            }
            List<Case> subCaseList = cases.subList(start, end);
            if (CollectionUtils.isEmpty(subCaseList)) {
              continue;
            }
            List<Long> caseIds = subCaseList.stream().map(Case::getId).collect(Collectors.toList());
            Map<Long, Integer> countMap = new HashMap<>();
            if (systemConfig.getESSwitch()) {
              countMap = caseOperationService.selectCountByCaseIdsUsingEs(startDate, caseIds, null);
            } else {
              countMap = caseOperationService.selectCountByCaseIds(startDate, caseIds, null);
            }
            CtrlTypeRelResult ctrlResult = ctrlMap.get(teamId);
            Map<Long, Integer> finalCountMap = countMap;
            subCaseList.forEach(c -> {
              int count = finalCountMap.get(c.getId()) == null ? 0 : finalCountMap.get(c.getId());
              if (count >= ctrlResult.getTelCaseLimitDayTime()) {
                ctrlCaseIds.add(c.getId());
              }
            });
            start = end;
          }
        }
      }

    } else {
      Map<Long, List<Case>> caseMap = caseList.stream().collect(Collectors.groupingBy(Case::getOrgDeltId));
      List<Long> orgDeltIds = new ArrayList<>(caseMap.keySet());
      List<CtrlTypeRelResult> ctrlTypeRelResults = ctrlTypeRelService.queryCtrlByDeltType(orgDeltIds, 1, null);
      Map<Long, CtrlTypeRelResult> ctrlMap = ctrlTypeRelResults.stream().collect(Collectors.toMap(CtrlTypeRelResult::getOrgDeltId, f -> f));

      for (Long deltId : caseMap.keySet()) {
        if (ctrlMap.containsKey(deltId)) {
          List<Case> cases = caseMap.get(deltId);
          int start = 0;
          int end = 0;
          while (start < cases.size()) {
            end = start + 500;
            if (end > cases.size()) {
              end = cases.size();
            }
            List<Case> subCaseList = cases.subList(start, end);
            if (CollectionUtils.isEmpty(subCaseList)) {
              continue;
            }
            List<Long> caseIds = subCaseList.stream().map(Case::getId).collect(Collectors.toList());
            Map<Long, Integer> countMap = new HashMap<>();
            if (systemConfig.getESSwitch()) {
              countMap = caseOperationService.selectCountByCaseIdsUsingEs(startDate, caseIds, null);
            } else {
              countMap = caseOperationService.selectCountByCaseIds(startDate, caseIds, null);
            }
            CtrlTypeRelResult ctrlResult = ctrlMap.get(deltId);
            Map<Long, Integer> finalCountMap = countMap;
            subCaseList.forEach(c -> {
              int count = finalCountMap.get(c.getId()) == null ? 0 : finalCountMap.get(c.getId());
              if (count >= ctrlResult.getTelCaseLimitDayTime()) {
                ctrlCaseIds.add(c.getId());
              }
            });
            start = end;
          }
        }
      }
    }
    return ctrlCaseIds;
  }


  /**
   * 话术变量校验
   *
   * @param siteId 话术id
   * @param orgId  公司id
   */
  public List<SiteVarsMapping> checkSiteVars(Long siteId, Long orgId) {
    AssertUtil.notNull(siteId,"话术变量id不可为空");
    List<SiteVarsMapping> duyanVarsMappings = new ArrayList<>();
    List<String> duyanSiteVars = manager.getSiteVars(siteId,orgId);
    if (ObjectUtil.isNotEmpty(duyanSiteVars)){
      // “U_”开头且变量名为4-16位英文或数字的变量
      boolean varRuleCheck = duyanSiteVars.stream().allMatch(PatternUtils::duyanSiteVar);
      AssertUtil.isTrue(varRuleCheck,"话术变量命名不合规范");

      // 话术变量映射绑定校验
      List<SiteVarsMapping> siteVarsMappings = siteVarsMappingService.getSiteVarsMappings(orgId,siteId);
      boolean isAllMapping = duyanSiteVars.stream().allMatch(duyanSiteVar ->
              siteVarsMappings.stream().anyMatch(siteVarsMapping ->
                      ObjectUtil.equals(duyanSiteVar, siteVarsMapping.getDuyanVar()) && StrUtil.isNotBlank(siteVarsMapping.getAnmiVar()))
      );
      AssertUtil.isTrue(isAllMapping,"存在话术变量未做映射绑定");

      duyanVarsMappings = siteVarsMappings.stream()
              .filter(siteVarsMapping -> StrUtil.isNotBlank(siteVarsMapping.getAnmiVar())
                      && duyanSiteVars.stream().anyMatch(duyanSiteVar ->
                                      ObjectUtil.equals(duyanSiteVar, siteVarsMapping.getDuyanVar())))
              .collect(Collectors.toList());
    }
    return duyanVarsMappings;
  }

  /**
   * 组装变量，过滤掉话术缺失的案件
   *
   * @param casePlan          计划参数
   * @param param             联系人信息列表
   * @param list              案件列表
   * @param duyanVarsMappings 变量
   * @return
   */
  public List<Map<String,Object>> assembleBody(CasePlan casePlan, List<CaseContact> param, List<CaseQueryResult> list, List<SiteVarsMapping> duyanVarsMappings) {
    // 创建个人预测式外呼计划的body
    List<Map<String,Object>> contents = new ArrayList<>();
    if (Objects.equals(casePlan.getFifoToRobotEnabled(), 1)) {
      Iterator<CaseContact> iterator = param.iterator();
      while (iterator.hasNext()) {
        CaseContact caseContact = iterator.next();
        Map<String, Object> caseContactMap = BeanUtils.beanToMap(caseContact);

        Long caseId = caseContact.getCaseId();
        Optional<CaseQueryResult> caseResultOptional = list.stream().filter(CaseQueryResult -> ObjectUtil.equals(caseId, CaseQueryResult.getId())).findFirst();
        CaseQueryResult caseQueryResult = caseResultOptional.get();
        Map<String, String> fieldJsonMap = caseQueryResult.getFieldJson();

        boolean varValueCheck = duyanVarsMappings.stream().anyMatch(varMapping -> {
          String anmiVar = varMapping.getAnmiVar();
          SiteEnums.Var varEmum = SiteEnums.Var.getByAnmiVar(anmiVar);
          Object value = null;
          if (ObjectUtil.isNull(varEmum)){
            value = fieldJsonMap.get(anmiVar);
          } else {
            String source = varEmum.getSource();
            String key = StringUtils.substringAfterLast(source, ".");

            if (source.startsWith("CaseContact")){
              value = caseContactMap.get(key);
            }else if (source.startsWith("CaseVO.fieldJson")){
              value = fieldJsonMap.get(key);
            }
          }
          return ObjectUtil.isNull(value);
        });

        if (varValueCheck) {
          log.info("话术变量缺失,剔除,案件:{},手机号:{},contact_id:{}", caseId, caseContact.getContactMobile(), caseContact.getContactId());
          iterator.remove();
        }
      }
      // 变量过滤、全局管控过滤可能导致案件号码为空
      if (CollectionUtils.isEmpty(param)) {
        throw new ApiException("所选案件根据规则过滤后无剩余案件，创建任务失败，具体请查看注意事项");
      }
      if (ObjectUtil.isNotEmpty(param)) {
        for (int i = 0; i< param.size(); i++) {
          CaseContact caseContact = param.get(i);
          Long caseId = caseContact.getCaseId();
          Optional<CaseQueryResult> CaseQueryResultOptional = list.stream().filter(CaseQueryResult -> ObjectUtil.equals(caseId, CaseQueryResult.getId())).findFirst();
          CaseQueryResult caseQueryResult = CaseQueryResultOptional.get();
          Map<String, String> fieldJsonMap = caseQueryResult.getFieldJson();

          addContent(caseContact,casePlan.getOrgId(),fieldJsonMap, contents,duyanVarsMappings, casePlan);
        }
      }
    }
    return contents;
  }
}
