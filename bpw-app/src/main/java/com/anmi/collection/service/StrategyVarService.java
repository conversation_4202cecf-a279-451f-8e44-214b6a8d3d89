package com.anmi.collection.service;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.entity.requset.query.strategy.StrategyVarQuery;
import com.anmi.collection.entity.requset.strategy.StrategyVarParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.strategy.StrategyVarVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.StrategyAttrRelMapper;
import com.anmi.collection.mapper.StrategyVarMapper;
import com.anmi.collection.service.flow.FlowStrategyVarRelService;
import com.anmi.collection.utils.AnmiBeanutils;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.decision.StrategyAttrRel;
import com.anmi.domain.decision.StrategyVar;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/15 17:13
 */
@Service
public class StrategyVarService extends BaseService<StrategyVar> {

  @Resource private StrategyVarMapper strategyVarMapper;
  @Resource private StrategyAttrRelMapper strategyAttrRelMapper;
  @Resource private FlowStrategyVarRelService flowStrategyVarRelService;


  @Transactional(rollbackFor = Exception.class)
  public void addStrategyVar(StrategyVarParam param) {
    StrategyVar strategyVar = AnmiBeanutils.copy(param, StrategyVar.class);
    UserSession userSession = UserUtils.getTokenUser();
    if (checkRepeat(userSession.getOrgId(), param.getValue())) {
      throw new ApiException("已存在变量{0}，不能重复添加", param.getValue());
    }
    assert strategyVar != null;
    strategyVar.setOrgId(userSession.getOrgId());
    strategyVar.setCreateBy(userSession.getId());
    strategyVar.setUpdateBy(userSession.getId());
    strategyVar.setCreateTime(new Date());
    strategyVar.setUpdateTime(new Date());
    strategyVarMapper.insertSelective(strategyVar);
  }

  private Boolean checkRepeat(Long orgId, String value) {
    Example example = new Example(StrategyVar.class);
    example.and().andEqualTo("orgId", orgId);
    example.and().andEqualTo("value", value);
    List<StrategyVar> list = this.selectByExample(example);
    return !CollectionUtils.isEmpty(list);
  }

  @Transactional(rollbackFor = Exception.class)
  public void updateStrategyVar(Long id, StrategyVarParam param) {
    if (Objects.isNull(id)) {
      throw new ApiException("id不能为空");
    }
    UserSession userSession = UserUtils.getTokenUser();
    StrategyVar strategyVar = AnmiBeanutils.copy(param, StrategyVar.class);
    strategyVar.setId(id);
    strategyVar.setUpdateBy(userSession.getId());
    strategyVar.setUpdateTime(new Date());
    strategyVarMapper.updateByPrimaryKeySelective(strategyVar);
  }

  public List<StrategyVarVO> getList(StrategyVarQuery query) {
    UserSession userSession = UserUtils.getTokenUser();
    Long orgId = userSession.getOrgId();
    Example example = new Example(StrategyVar.class);
    example.and().andEqualTo("orgId", orgId);
    if (Objects.nonNull(query.getType())) {
      example.and().andEqualTo("type", query.getType());
    }
    if (StringUtils.isNotBlank(query.getName())) {
      example.and().andLike("name", query.getName() + "%");
    }
    example.orderBy("id").desc();
    List<StrategyVar> strategyVars = strategyVarMapper.selectByExample(example);
    List<StrategyVarVO> strategyVarVOS = new ArrayList<>();
    strategyVars.forEach(strategyVar -> {
      StrategyVarVO strategyVarVO = AnmiBeanutils.copy(strategyVar, StrategyVarVO.class);
      strategyVarVOS.add(strategyVarVO);
    });
    return strategyVarVOS;
  }

  @Transactional(rollbackFor = Exception.class)
  public void addDefaultStrategyVar(Long orgId) {
    List<StrategyVar> systemStrategyVar = defaultSystemStrategyVar();
    systemStrategyVar.forEach(strategyVar -> {
      strategyVar.setId(null);
      strategyVar.setOrgId(orgId);
      strategyVar.setCreateTime(new Date());
      strategyVar.setUpdateTime(new Date());
    });
    strategyVarMapper.insertList(systemStrategyVar);
  }

  @Transactional(rollbackFor = Exception.class)
  public void deleteStrategyVar(Long id) {
    UserSession userSession = UserUtils.getTokenUser();
    StrategyVar strategyVar = this.selectByPrimaryKey(id);
    if (Objects.isNull(strategyVar) || !Objects.equals(strategyVar.getOrgId(), userSession.getOrgId())) {
      throw new ApiException("找不到id为{0}的变量", id);
    }
    List<StrategyAttrRel> check = strategyAttrRelMapper.check(userSession.getOrgId(), strategyVar.getValue());
    if (!CollectionUtils.isEmpty(check)) {
      throw new ApiException("该变量正在被策略引用,不能删除");
    }

    Integer flowStrategyVarCheck = flowStrategyVarRelService.check(userSession.getOrgId(), id);
    AssertUtil.isEquals(flowStrategyVarCheck,0,"该变量正在被审批流引用,不能删除");

    strategyVarMapper.deleteByPrimaryKey(id);
  }

  public List<StrategyVar> defaultSystemStrategyVar() {
    Example example = new Example(StrategyVar.class);
    example.and().andEqualTo("orgId", 0);
    return strategyVarMapper.selectByExample(example);
  }


}
