package com.anmi.collection.strategy;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 策略表 规则节点
 *
 * <AUTHOR>
 * @date 2024/04/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DecisionAttr extends Node{
  private String attr;
  private Integer execAllBranch;
  private List<DecisionCond> conditions;

  private Boolean isRuleGroup;
  private Long ruleGroupId;
}
