package com.anmi.collection.strategy.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.AsyncTaskService;
import com.anmi.collection.strategy.CaseStrategyBO;
import com.anmi.collection.utils.SpringContextHolder;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.collection.utils.dict.UserUtils;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.decision.StrategyExecLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Stack;

/**
 * 案件状态 action
 * -1 ：删除
 * 0:正常,1:留案,2:停催,3:结案,4:作废 （暂未支持）
 */
@Slf4j
public class CaseDelAction implements AnmiAction<CaseStrategyBO> {

    private final StringRedisTemplate stringRedisTemplate;
    private final AsyncTaskService asyncTaskService;
    private final RedisUtil redisUtil;

    /**
     * 案件状态和对应的asyncTaskId
     */
    private final Map<Integer, Long> caseStatusMap = new HashMap<>();

    public CaseDelAction() {
        this.stringRedisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
        this.asyncTaskService = SpringContextHolder.getBean(AsyncTaskService.class);
        this.redisUtil = SpringContextHolder.getBean(RedisUtil.class);
    }

    @Override
    public Object doAction(CaseStrategyBO data, JSONObject actionParam, Map<?, ?> execContext, Stack<String> matchPathNodes) {
        try {
            log.info("CaseDelAction ：data:{},param:{},execContext:{}", data, actionParam, execContext);
            log.info("加入列表,案件信息[{}],参数[{}]", JSON.toJSONString(data), actionParam);
            String key = StringUtils.trimAllWhitespace(actionParam.getString("resultCaseStatus"));
            StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
            UserSession session = (UserSession) execContext.get(USER_SESSION);
            if (StringUtils.isEmpty(key)) {
                log.warn("不符合案件状态修改条件");
                saveExecLog(strategyExecLog, data, session, null, actionParam, matchPathNodes);
                return null;
            }
            Integer resultCaseStatus = Integer.parseInt(key);
            if (!caseStatusMap.containsKey(resultCaseStatus)) {
                AsyncTask caseStatusTask = createCaseStatusTask(resultCaseStatus, execContext);
                caseStatusMap.put(resultCaseStatus, caseStatusTask.getId());
            }
            Long asyncTaskId = caseStatusMap.get(resultCaseStatus);
            Boolean isMember = stringRedisTemplate.opsForSet().isMember(KeyCache.CASE_PROTECT_EXIST_IDS + session.getOrgId(), data.getId().toString());
            saveExecLog(strategyExecLog, data, session, asyncTaskId, actionParam, matchPathNodes);
            if (!Objects.equals(isMember, Boolean.TRUE)) {
                redisUtil.sSet(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + asyncTaskId, 24 * 60 * 60, data.getId().toString());
                stringRedisTemplate.opsForSet().add(KeyCache.CASE_PROTECT_EXIST_IDS + session.getOrgId(), data.getId().toString());
            } else {
                log.info("案件{}当前已经被锁定，无法进行案件状态变更", data.getId().toString());
            }
            return asyncTaskId;
        } catch (Exception ex) {
            log.error("执行失败:" + data, ex);
        }
        return null;
    }

    @Override
    public String actionName() {
        return "caseStatus";
    }

    @Override
    public void begin(Map<?, ?> execContext) {
        caseStatusMap.clear();
    }

    @Override
    public void commit(Map<?, ?> execContext) {
        try {
            StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
            UserSession session = (UserSession) execContext.get(USER_SESSION);
            for (Map.Entry<Integer, Long> entry : caseStatusMap.entrySet()) {
                Long asyncTaskId = entry.getValue();
                Long size = redisUtil.sGetSize(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + asyncTaskId);
                AsyncTask update = new AsyncTask();
                update.setId(asyncTaskId);
                update.setTotal(size);
                asyncTaskService.updateByPrimaryKeySelective(update);
                stringRedisTemplate.opsForList().leftPush(KeyCache.CASE_CHANGE_STATUS_TASK_ID_LIST, asyncTaskId.toString());
                saveExecResult(strategyExecLog, session, size.intValue(), (long) entry.getKey(), asyncTaskId);
            }
        } catch (Exception ex) {
            log.error("任务添加失败", ex);
            throw new ApiException("任务添加失败", ex);
        } finally {
            caseStatusMap.clear();
        }
    }

    /*
    创建异步任务
     */
    private AsyncTask createCaseStatusTask(Integer resultCaseStatus, Map<?, ?> execContext) {
        UserSession session = (UserSession) execContext.get(USER_SESSION);
        AsyncTask asyncTask = new AsyncTask();
        asyncTask.setCreateBy(session.getId());
        asyncTask.setOrgId(session.getOrgId());
        if (UserUtils.likeBranchAdmin(session.getRoleId())) {
            asyncTask.setDepId(session.getDepId());
        } else if (UserUtils.likeTeamLeader(session.getRoleId())) {
            asyncTask.setDepId(session.getDepId());
            asyncTask.setTeamId(session.getTeamId());
        }
        asyncTask.setType(AsyncTaskEnums.Type.DELETE.getCode());
        asyncTask.setStatus(AsyncTaskEnums.Status.ING.getCode());
        asyncTask.setTotal(0L);
        asyncTask.setSuccessAmt(0L);
        asyncTask.setIgnoreCount(0L);
        asyncTaskService.insertSelective(asyncTask);
        return asyncTask;
    }

}
