package com.anmi.collection.strategy.comparator;

import com.anmi.collection.strategy.AttrType;
import com.anmi.collection.strategy.exception.CheckException;

/**
 * <AUTHOR>
 */
public class IsTrueComparator implements OperComparator{

  @Override
  public boolean compare(Object data, String value) {
    return "true".equalsIgnoreCase(String.valueOf(data));
  }

  @Override
  public void check(AttrType attrType, String value) throws CheckException {
    if (attrType != AttrType.BOOL) {
      throw new CheckException("isTrue仅支持布尔类型的属性");
    }
  }

  @Override
  public String operator() {
    return "isTrue";
  }
}
