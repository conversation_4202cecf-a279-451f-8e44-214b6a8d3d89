package com.anmi.collection.strategy.comparator;


import com.anmi.collection.strategy.AttrType;
import com.anmi.collection.strategy.exception.CheckException;

/**
 * 条件表达式执行器
 *
 * <AUTHOR>
 */
public interface OperComparator {

  /**
   * 条件表达式执行
   *
   * @param data 数据
   * @param value 表达式的值，统一为String类型
   * @return 表达式成立返回true，否则返回false
   */
  boolean compare(Object data, String value);

  /**
   * 条件表达式校验，主要校验类型的数据是否合法
   *
   * @param attrType 属性类型
   * @param value 表达式右侧数据
   * @exception CheckException 数据类型校验失败时抛出异常
   */
  void check(AttrType attrType, String value) throws CheckException;

  /**
   * 运算符
   *
   * @return 返回操作符名称>,<,=等
   */
  String operator();
}
