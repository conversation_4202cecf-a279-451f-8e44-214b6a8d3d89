package com.anmi.collection.strategy.comparator.base;

import cn.hutool.core.util.NumberUtil;
import com.anmi.collection.strategy.AttrType;
import com.anmi.collection.strategy.comparator.OperComparator;
import com.anmi.collection.strategy.exception.CheckException;

/**
 * 数字或日期比较器
 *
 * <AUTHOR>
 */
public abstract class DateNumComparator implements OperComparator {

  @Override
  public void check(AttrType attrType, String value) throws CheckException {
    if (attrType != AttrType.NUMBER && attrType != AttrType.DATE) {
      throw new CheckException("大于条件仅使用于数字或日期");
    }
    if (!NumberUtil.isNumber(value)) {
      throw new CheckException("数据格式不匹配");
    }
  }
}
