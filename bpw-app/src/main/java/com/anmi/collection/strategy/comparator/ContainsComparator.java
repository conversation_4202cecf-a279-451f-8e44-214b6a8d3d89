package com.anmi.collection.strategy.comparator;

import com.anmi.collection.strategy.AttrType;
import com.anmi.collection.strategy.exception.CheckException;
import com.anmi.collection.strategy.utils.ClassUtil;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ContainsComparator implements OperComparator {
  @Override
  public boolean compare(Object data, String value) {
    if (data == null || value == null) {
      return false;
    }
    if (ClassUtil.isString(data.getClass())) {
      return data.toString().contains(value);
    }
    if (ClassUtil.isCollection(data.getClass())) {
      return ((List<String>) data).contains(value);
    }
    return false;
  }

  @Override
  public void check(AttrType attrType, String value) throws CheckException {
    if (attrType != AttrType.STRING && attrType != AttrType.COLLECTION) {
      throw new CheckException("包含条件仅试用于文本或集合");
    }
  }

  @Override
  public String operator() {
    return "包含";
  }
}
