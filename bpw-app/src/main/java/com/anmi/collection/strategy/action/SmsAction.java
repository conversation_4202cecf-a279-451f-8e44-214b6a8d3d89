package com.anmi.collection.strategy.action;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.CasePlanEnums;
import com.anmi.collection.common.enums.CasePlanTemplateEnums;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CasePlanService;
import com.anmi.collection.service.CasePlanTemplateService;
import com.anmi.collection.strategy.CaseStrategyBO;
import com.anmi.collection.utils.SpringContextHolder;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.domain.cases.CasePlan;
import com.anmi.domain.cases.CasePlanTemplate;
import com.anmi.domain.decision.StrategyExecLog;
import com.anmi.domain.decision.StrategyInfo;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class SmsAction implements AnmiAction<CaseStrategyBO> {

  private final CasePlanTemplateService casePlanTemplateService;

  private final CasePlanService casePlanService;
  private final StringRedisTemplate stringRedisTemplate;

  /**
   * 存储模板id和对应的planId
   */
  private final Map<Long, Long> casePlanTemplateMap = new HashMap<>();

  public SmsAction() {
    this.casePlanTemplateService = SpringContextHolder.getBean(CasePlanTemplateService.class);
    this.casePlanService = SpringContextHolder.getBean(CasePlanService.class);
    this.stringRedisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
  }

  @Override
  public Object doAction(CaseStrategyBO data, JSONObject param, Map<?, ?> execContext, Stack<String> matchPathNodes) {
    log.info("SmsAction ：data:{},param:{},execContext:{}", data, param, execContext);
    StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
    UserSession session = (UserSession) execContext.get(USER_SESSION);
    if (CaseEnums.IgnorePlan.YES.getCode().equals(data.getIgnorePlan())) {
      log.info("案件[{}]不参与智能计划,自动跳过", JSON.toJSONString(data));
      saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
      return null;
    }
    // 只有案件状态是分配完成、留案、分配至组才可以创建计划
    List<Integer> caseStatusLIst =   ImmutableList.of(CaseEnums.CaseStatus.NORMAL.getCode(), CaseEnums.CaseStatus.DELAY.getCode());
    if (!(caseStatusLIst.contains(data.getCaseStatus()))) {
      log.info("案件[{}]状态不满足短信计划，已过滤掉", JSON.toJSONString(data));
      saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
      return null;
    }
    // 案件催收结果为承诺还款且未到达承诺还款时间,不参与计划
    Date ptpTime = data.getPtpTime();
    if (Objects.nonNull(ptpTime) && ptpTime.after(new Date())) {
      log.info("案件[{}]承诺还款时间未到达，已过滤掉", JSON.toJSONString(data));
      saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
      return null;
    }
    log.info("加入短信计划,案件信息[{}],参数[{}]", JSON.toJSONString(data), param);
    String key = StringUtils.trimAllWhitespace(param.getString("templateId"));
    if (StringUtils.isEmpty(key)) {
      throw new ApiException("加入短信计划模板参数不能为空");
    }
    Long templateId = Long.parseLong(key);
    if (!casePlanTemplateMap.containsKey(templateId)) {
      CasePlan casePlan = createCasePlan(templateId, execContext);
      casePlanTemplateMap.put(templateId, casePlan.getId());
    }
    Long planId = casePlanTemplateMap.get(templateId);
    saveExecLog(strategyExecLog, data, session, planId, param, matchPathNodes);
    //短信计划手机号去重
    Boolean exist = stringRedisTemplate.opsForSet().isMember(KeyCache.CASE_MESSAGE_CASE_MOBILES + planId, data.getOwnMobile());
    if (Objects.equals(exist, true)) {
      return null;
    }
    stringRedisTemplate.opsForSet().add(KeyCache.CASE_MESSAGE_CASE_MOBILES + planId, data.getOwnMobile());
    //设置过期时间，防止永久存储，计划提交时删除
    stringRedisTemplate.expire(KeyCache.CASE_MESSAGE_CASE_MOBILES + planId, 10, TimeUnit.MINUTES);
    stringRedisTemplate.opsForSet().add(KeyCache.CASE_MESSAGE_CASE_IDS + planId, data.getId().toString());

    return planId;
  }

  @Override
  public String actionName() {
    return "sms";
  }

  @Override
  public void begin(Map<?, ?> execContext) {
    casePlanTemplateMap.clear();
  }

  @Override
  public void commit(Map<?, ?> execContext) {
    try {
      StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
      UserSession session = (UserSession) execContext.get(USER_SESSION);
      for (Map.Entry<Long, Long> entry : casePlanTemplateMap.entrySet()) {
        Long planId = entry.getValue();
        Long size = stringRedisTemplate.opsForSet().size(KeyCache.CASE_MESSAGE_CASE_IDS + planId);
        CasePlan update = new CasePlan();
        update.setId(planId);
        update.setSelectedTotal(size);
        saveExecResult(strategyExecLog, session, size == null ? null : size.intValue(), entry.getKey(), planId);
        casePlanService.updateByPrimaryKeySelective(update);
        stringRedisTemplate.opsForList().leftPush(KeyCache.CASE_MESSAGE_PLAN_ID_LIST, planId.toString());
        stringRedisTemplate.delete(KeyCache.CASE_MESSAGE_CASE_MOBILES + planId);
      }
    } catch (Exception ex) {
      log.error("计划添加失败", ex);
      throw new ApiException("计划添加失败", ex);
    } finally {
      casePlanTemplateMap.clear();
    }
  }

  private CasePlan createCasePlan(Long planTemplateId, Map<?, ?> execContext) {
    CasePlanTemplate template = casePlanTemplateService.selectByPrimaryKey(planTemplateId);
    if (Objects.isNull(template) || !CasePlanEnums.Type.MESSAGE.getCode().equals(template.getType())) {
      throw new ApiException("短信计划模板不存在");
    }
    CasePlan casePlan = convertCasePlanTemplate(template, execContext);
    casePlan.setSelectedTotal(0L);
    casePlanService.insertSelective(casePlan);
    return casePlan;
  }

  private CasePlan convertCasePlanTemplate(CasePlanTemplate template, Map<?, ?> execContext) {
    CasePlan casePlan = new CasePlan();
    BeanUtils.copyProperties(template, casePlan);
    casePlan.setId(null);
    casePlan.setName("策略生成-" + DateUtil.format(new Date(), "yyyyMMddHHmm"));
    casePlan.setStatus(CasePlanEnums.Status.NEW.getCode());
    if (CasePlanTemplateEnums.StartType.IMMEDIATELY.getCode().equals(template.getStartType())) {
      casePlan.setIsStartNow(1);
      casePlan.setStartType(1);
      casePlan.setPlanStartTime(new Date());
    }
    if (execContext != null && execContext.containsKey(USER_SESSION)) {
      Long sessionUserId = ((UserSession) execContext.get(USER_SESSION)).getId();
      casePlan.setCreateBy(sessionUserId);
      casePlan.setUpdateBy(sessionUserId);
    } else {
      casePlan.setCreateBy(0L);
      casePlan.setUpdateBy(0L);
    }
    if (template.getStartType() == 1) {
      casePlan.setStartType(0);
    } else {
      casePlan.setStartType(1);
    }
    if (execContext != null && execContext.containsKey(STRATEGY_NAME)) {
      casePlan.setName(((StrategyInfo) execContext.get(STRATEGY_NAME)).getName() + "-" + template.getName() + "-" + DateUtil.format(new Date(), "yyyyMMddHHmm"));
    }
    casePlan.setCreateTime(new Date());
    casePlan.setUpdateTime(new Date());
    casePlan.setDesc("策略生成-" + DateUtil.format(new Date(), "yyyyMMddHHmm"));
    return casePlan;
  }
}