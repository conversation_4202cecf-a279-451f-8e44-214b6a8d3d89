package com.anmi.collection.strategy;


import java.lang.reflect.Field;

/**
 * 元数据接口 <br/>
 * 所有的策略引擎的实体必须实现此接口来获取属性的值<br/>
 * 默认使用反射的方式获取，反射的方式获取属性值会比较慢<br/>
 * 子类可重写此方法
 *
 * <AUTHOR>
 */
public interface MetaInfo {

  /**
   * 获取对象的属性
   * @param attrName 属性名
   * @return 返回属性值
   */
  default Object getAttrObject(String attrName) {
    Class<?> clazz = this.getClass();
    while (clazz != null) {
      try {
        Field field = clazz.getDeclaredField(attrName);
        field.setAccessible(true);
        return field.get(this);
      } catch (NoSuchFieldException | IllegalAccessException e) {
        e.printStackTrace();
      }
      clazz = clazz.getSuperclass();
    }
    return null;
  }
}
