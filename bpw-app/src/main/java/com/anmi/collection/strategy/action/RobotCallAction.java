package com.anmi.collection.strategy.action;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.StrategyEnums;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.service.CustomExecTimeService;
import com.anmi.collection.service.RobotQueueService;
import com.anmi.collection.service.RobotTemplateService;
import com.anmi.collection.strategy.CaseStrategyBO;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.IpUtil;
import com.anmi.collection.utils.SpringContextHolder;
import com.anmi.domain.decision.StrategyExecLog;
import com.anmi.domain.robot.CustomExecTime;
import com.anmi.domain.robot.RobotQueue;
import com.anmi.domain.robot.RobotTemplate;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 机器人点呼动作
 *
 * <AUTHOR>
 */
@Slf4j
public class RobotCallAction implements AnmiAction<CaseStrategyBO> {

  private final RobotTemplateService robotTemplateService;
  private final RobotQueueService robotQueueService;
  private final CustomExecTimeService customExecTimeService;
  private static final String PTP_TIME = "ptp_time";
  private static final String OPERATION_NEXT_TIME = "operation_next_time";
  private static final String LAST_FOLLOW_TIME = "last_follow_time";
  private static final String QUEUE_ENTRY_TIME = "queue_entry_time";

  public RobotCallAction() {
    this.robotTemplateService = SpringContextHolder.getBean(RobotTemplateService.class);
    this.robotQueueService = SpringContextHolder.getBean(RobotQueueService.class);
    this.customExecTimeService = SpringContextHolder.getBean(CustomExecTimeService.class);
  }

  @Override
  public void begin(Map<?, ?> execContext) {

  }

  @Override
  public Object doAction(CaseStrategyBO data, JSONObject param, Map<?, ?> execContext, Stack<String> matchPathNodes) {
    StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
    UserSession session = (UserSession) execContext.get(USER_SESSION);
    log.info("机器人点呼,案件id:{},参数:{}", data.getId(), param);
    String key = StringUtils.trimAllWhitespace(param.getString("templateId"));
    if (StringUtils.isEmpty(key)) {
      throw new ApiException("加入机器人计划模板参数不能为空");
    }
    Long templateId = Long.parseLong(key);
    RobotTemplate robotTemplate = robotTemplateService.selectByPrimaryKey(templateId);
    if (Objects.isNull(robotTemplate)) {
      saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
      log.warn("找不到模板信息，无法执行机器人策略");
      return null;
    }
    List<Integer> caseStatusList =   ImmutableList.of(CaseEnums.CaseStatus.NORMAL.getCode(), CaseEnums.CaseStatus.DELAY.getCode());
    if (!(caseStatusList.contains(data.getCaseStatus()))) {
      saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
      log.warn("案件已经作废,不能继续执行机器人点呼");
      return null;
    }
    Date execTime = getRobotExecTime(data, robotTemplate);
    log.info("执行时间{}", execTime);
    //多加一秒判断，防止立即执行的策略无法执行
    if (DateUtil.offsetSecond(execTime, 1).compareTo(new Date()) < 0) {
      saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
      //执行时间小于当前时间,不执行
      log.info("机器人点呼时间小于当前时间，不继续执行,当前时间{},执行时间{}", new Date(), execTime);
      return null;
    }
    //超过1小时后执行的不发送到mq(mq消息队列有时长限制)
    int sendStatus = 1;
    if (DateUtil.compare(DateUtil.offsetHour(new Date(), 1), execTime) < 0) {
      log.info("机器人执行时间大于1小时，不发送到mq");
      sendStatus = 0;
    }
    String macAddress = IpUtil.ip;
    RobotQueue robotQueue = robotQueueService.saveCaseRobot(data, robotTemplate, execTime, session, strategyExecLog, sendStatus,macAddress);
    if (sendStatus == 1) {
      //暂时不考虑发送失败的情况
      sendToMq(robotQueue.getId(), execTime);
    }
    saveExecLog(strategyExecLog, data, session, robotQueue.getId(), param, matchPathNodes);
    return robotQueue;
  }

  private Date getRobotExecTime(CaseStrategyBO bo, RobotTemplate robotTemplate) {
    if (Objects.equals(robotTemplate.getExecType(), StrategyEnums.RobotExecTimeType.IMMEDIATELY.getCode())) {
      //立即执行的也延迟两秒，防止主从延迟查不到数据
      return DateUtil.offsetSecond(new Date(),2);
    } else if (Objects.equals(robotTemplate.getExecType(), StrategyEnums.RobotExecTimeType.STATIC.getCode())) {
      Date now = new Date();
      String[] timeRange = robotTemplate.getExecTime().split(":");
      int hour = Integer.parseInt(timeRange[0]);
      int minute = Integer.parseInt(timeRange[1]);
      return DateUtil.offsetMinute(DateUtils.getStartTimeOfDate(now), hour * 60 + minute);
    } else {
      CustomExecTime customExecTime = customExecTimeService.selectByPrimaryKey(robotTemplate.getExecCustomId());
      String baseTimeField = customExecTime.getBaseTimeField();
      Date baseTime = null;
      if (Objects.equals(baseTimeField, PTP_TIME)) {
        baseTime = bo.getPtpTime();
      } else if (Objects.equals(baseTimeField, OPERATION_NEXT_TIME)) {
        baseTime = bo.getOperationNextTime();
      } else if (Objects.equals(baseTimeField, LAST_FOLLOW_TIME)) {
        baseTime = bo.getLastFollowTime();
      } else if (Objects.equals(baseTimeField, QUEUE_ENTRY_TIME)) {
        baseTime = new Date();
      } else {
        throw new ApiException("不支持的时间类型:" + baseTimeField);
      }
      Integer offset = customExecTime.getBaseTimeOffset();
      String type = customExecTime.getBaseTimeOffsetType();
      if (Objects.isNull(baseTime)) {
        baseTime = new Date();
        offset = customExecTime.getOffsetNull();
        type = customExecTime.getOffsetNullType();
      }
      switch (type) {
        case "D":
          return DateUtil.offsetDay(baseTime, offset);
        case "M":
          return DateUtil.offsetMinute(baseTime, offset);
        case "S":
          return DateUtil.offsetSecond(baseTime, offset);
        case "H":
          return DateUtil.offsetHour(baseTime, offset);
        default:
          throw new ApiException("获取执行时间异常:不支持的类型:" + type);
      }
    }
  }

  private void sendToMq(Long robotQueueId, Date execTime) {
    log.info("发送到mq,id:{},execTime:{}", robotQueueId, execTime);
    robotQueueService.sendDelayTime(robotQueueId, execTime);
  }



  @Override
  public String actionName() {
    return "robotCall";
  }

  @Override
  public void commit(Map<?, ?> execContext) {

  }
}
