package com.anmi.collection.strategy.action;

import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.AsyncTaskService;
import com.anmi.collection.service.CaseAllotTemplateService;
import com.anmi.collection.strategy.CaseStrategyBO;
import com.anmi.collection.utils.SpringContextHolder;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.CaseAllotTemplate;
import com.anmi.domain.decision.StrategyExecLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Stack;

/**
 * <AUTHOR>
 */
@Slf4j
public class AllotAction implements AnmiAction<CaseStrategyBO> {

  private final StringRedisTemplate stringRedisTemplate;
  private final AsyncTaskService asyncTaskService;
  private final CaseAllotTemplateService caseAllotTemplateService;
  private final RedisUtil redisUtil;


  /**
   * 存储模板id和对应的asyncTaskId
   */
  private final Map<Long, Long> caseAllotTemplateMap = new HashMap<>();

  public AllotAction() {
    this.stringRedisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
    this.asyncTaskService = SpringContextHolder.getBean(AsyncTaskService.class);
    this.caseAllotTemplateService = SpringContextHolder.getBean(CaseAllotTemplateService.class);
    this.redisUtil = SpringContextHolder.getBean(RedisUtil.class);
  }

  @Override
  public Object doAction(CaseStrategyBO data, JSONObject param, Map<?, ?> execContext, Stack<String> matchPathNodes) {
    log.info("加入分案列表,案件id[{}],参数[{}]", data.getId(), param);
    String key = StringUtils.trimAllWhitespace(param.getString("templateId"));
    if (StringUtils.isEmpty(key)) {
      throw new ApiException("分案模板参数不能为空");
    }
    Long templateId = Long.parseLong(key);
    if (!caseAllotTemplateMap.containsKey(templateId)) {
      AsyncTask caseAllotTask = createAsyncCaseAllot(templateId, execContext);
      caseAllotTemplateMap.put(templateId, caseAllotTask.getId());
    }
    UserSession session = (UserSession) execContext.get(USER_SESSION);
    StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
    Long asyncTaskId = caseAllotTemplateMap.get(templateId);
    saveExecLog(strategyExecLog, data, session, asyncTaskId, param, matchPathNodes);
    Boolean isMember = stringRedisTemplate.opsForSet().isMember(KeyCache.CASE_PROTECT_EXIST_IDS + session.getOrgId(), data.getId().toString());
    if (!Objects.equals(isMember, Boolean.TRUE)) {
      redisUtil.sSet(KeyCache.CASE_ALLOT_TASK_CASES + asyncTaskId, 24 * 60 * 60, data.getId().toString());
      stringRedisTemplate.opsForSet().add(KeyCache.CASE_PROTECT_EXIST_IDS + session.getOrgId(), data.getId().toString());
    } else {
      log.info("案件{}当前已经被锁定，无法进行分案", data.getId().toString());
    }
    return null;

  }


  @Override
  public String actionName() {
    return "allot";
  }

  @Override
  public void begin(Map<?, ?> execContext) {
    caseAllotTemplateMap.clear();
  }

  @Override
  public void commit(Map<?, ?> execContext) {
    try {
      StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
      UserSession session = (UserSession) execContext.get(USER_SESSION);
      for (Map.Entry<Long, Long> entry : caseAllotTemplateMap.entrySet()) {
        Long asyncTaskId = entry.getValue();
        Long size = redisUtil.sGetSize(KeyCache.CASE_ALLOT_TASK_CASES + asyncTaskId);
        AsyncTask update = new AsyncTask();
        update.setId(asyncTaskId);
        update.setTotal(size);
        asyncTaskService.updateByPrimaryKeySelective(update);
        stringRedisTemplate
          .opsForList()
          .leftPush(KeyCache.CASE_ALLOT_TASK_ID_LIST, asyncTaskId.toString());
        saveExecResult(strategyExecLog, session, size.intValue(), entry.getKey(), asyncTaskId);
      }
    } catch (Exception ex) {
      log.error("计划添加失败", ex);
      throw new ApiException("计划添加失败", ex);
    } finally {
      caseAllotTemplateMap.clear();
    }
  }

  private AsyncTask createAsyncCaseAllot(Long templateId, Map<?, ?> execContext) {
    UserSession session = (UserSession) execContext.get(USER_SESSION);
    CaseAllotTemplate template = caseAllotTemplateService.selectByPrimaryKey(templateId);
    if (Objects.isNull(template) || !Objects.equals(template.getOrgId(), session.getOrgId())) {
      throw new ApiException("分案模板不存在");
    }

    AsyncTask asyncTask = new AsyncTask();
    asyncTask.setCreateBy(session.getId());
    asyncTask.setOrgId(session.getOrgId());
    asyncTask.setType(AsyncTaskEnums.Type.ALLOT.getCode());
    asyncTask.setStatus(AsyncTaskEnums.Status.ING.getCode());
    asyncTask.setTotal(0L);
    asyncTask.setIgnoreCount(0L);
    Map<String, String> fieldJson = new HashMap<>();
    fieldJson.put("type", template.getType().toString());
    fieldJson.put("isConjoint", Objects.equals(template.getIsConjoint(), 1) ? "true" : "false");
    fieldJson.put("allotToTeamMembers", Objects.equals(template.getToMembers(), 1) ? "true" : "false");
    fieldJson.put("userIdList", template.getUserIds());
    fieldJson.put("teamIdList", template.getTeamIds());
    fieldJson.put("depIdList", template.getDepIds());
    fieldJson.put("allotProportions", template.getAllotProportions());
    fieldJson.put("allotObject", template.getAllotObject().toString());
    fieldJson.put("isAllAgents", Objects.equals(template.getAllAgents(), 1) ? "true" : "false");
    fieldJson.put("autoRecovery", Objects.equals(template.getAutoRecovery(), 1) ? "true" : "false");
    fieldJson.put("operType", "1");
    if (Objects.equals(template.getAutoRecovery(), 1)) {
      if (Objects.nonNull(template.getAutoRecoveryDate())) {
        fieldJson.put("autoRecoveryDate", String.valueOf(template.getAutoRecoveryDate().getTime()));
      }
      if (Objects.nonNull(template.getAutoRecoveryDay())) {
        fieldJson.put("autoRecoveryDay", String.valueOf(template.getAutoRecoveryDay()));
      }
    }
    fieldJson.put("switchType", "1");
    fieldJson.put("resultOperationWays", template.getOperationWay().toString());
    fieldJson.put("isRule", template.getIsRule().toString());
    fieldJson.put("ruleId", Objects.isNull(template.getRuleId()) ? null : template.getRuleId().toString());
    asyncTask.setFieldJson(fieldJson);
    asyncTaskService.insertSelective(asyncTask);
    return asyncTask;
  }
}
