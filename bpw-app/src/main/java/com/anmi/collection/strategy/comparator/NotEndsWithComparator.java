package com.anmi.collection.strategy.comparator;

import com.anmi.collection.strategy.AttrType;
import com.anmi.collection.strategy.exception.CheckException;

/**
 * <AUTHOR>
 */
public class NotEndsWithComparator implements OperComparator {
  @Override
  public boolean compare(Object data, String value) {
    if (data == null || value == null) {
      return false;
    }
    return !String.valueOf(data).endsWith(value);
  }

  @Override
  public void check(AttrType attrType, String value) throws CheckException {
    if (attrType != AttrType.STRING) {
      throw new CheckException("类型不匹配，仅支持文本");
    }
  }

  @Override
  public String operator() {
    return "结尾不是";
  }
}
