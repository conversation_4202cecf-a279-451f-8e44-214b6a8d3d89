package com.anmi.collection.strategy.comparator;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.anmi.collection.strategy.AttrType;
import com.anmi.collection.strategy.exception.CheckException;
import com.anmi.collection.strategy.utils.ClassUtil;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AnyMatchComparator implements OperComparator {
  @Override
  public boolean compare(Object data, String value) {
    if (data == null || value == null) {
      return false;
    }
    if (ClassUtil.isCollection(data.getClass())) {
      List<String> tmp;
      if (JSONUtil.isJsonArray(value)){
        tmp = JSON.parseArray(value, String.class);
      } else {
        tmp = StrUtil.split(value, ",");
      }
      return ((List<String>) data).stream().anyMatch(tmp::contains);
    }
    return false;
  }

  @Override
  public void check(AttrType attrType, String value) throws CheckException {
    if (attrType != AttrType.COLLECTION) {
      throw new CheckException("包含条件仅适用于集合");
    }
    try {
      List<String> tmp = JSON.parseArray(value, String.class);
    } catch (Exception exception) {
      throw new CheckException("集合数据格式不匹配");
    }
  }

  @Override
  public String operator() {
    return "任意包含";
  }
}
