package com.anmi.collection.strategy.utils;

import com.alibaba.fastjson2.JSON;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/** <AUTHOR> */
public class ClassUtil {

  public static Boolean isLong(Class<?> clazz) {
    return Long.class.equals(clazz) || clazz == long.class;
  }

  public static Boolean isInteger(Class<?> clazz) {
    return Integer.class.equals(clazz) || clazz == int.class;
  }

  public static Boolean isFloat(Class<?> clazz) {
    return Float.class.equals(clazz) || clazz == float.class;
  }

  public static Boolean isDouble(Class<?> clazz) {
    return Double.class.equals(clazz) || clazz == double.class;
  }

  public static Boolean isBigDecimal(Class<?> clazz) {
    return BigDecimal.class.equals(clazz);
  }

  public static Boolean isString(Class<?> clazz) {
    return String.class.equals(clazz) || clazz == CharSequence.class;
  }

  public static Boolean isCollection(Class<?> clazz) {
    return Collection.class.isAssignableFrom(clazz);
  }

  public static Boolean isByte(Class<?> clazz) {
    return Byte.class.equals(clazz) || byte.class == clazz;
  }

  public static Boolean isShort(Class<?> clazz) {
    return Short.class.equals(clazz) || short.class == clazz;
  }

  public static Boolean isChar(Class<?> clazz) {
    return Character.class.equals(clazz) || char.class == clazz;
  }

  public static Boolean isBoolean(Class<?> clazz) {
    return Boolean.class.equals(clazz) || boolean.class == clazz;
  }

  public static Boolean isDate(Class<?> clazz) {
    return Date.class.equals(clazz);
  }

  public static Boolean isLocalDate(Class<?> clazz) {
    return LocalDate.class.equals(clazz);
  }

  public static Boolean isLocalDateTime(Class<?> clazz) {
    return LocalDateTime.class.equals(clazz);
  }

  public static Boolean isNumber(Class<?> clazz) {
    return isLong(clazz)
        || isInteger(clazz)
        || isDouble(clazz)
        || isFloat(clazz)
        || isByte(clazz)
        || isBigDecimal(clazz)
        || isShort(clazz);
  }

//  /**
//   * 特殊处理，文本类型，有可能是数组
//   * @param jsonStr
//   * @return
//   */
//  private List<String> toStringList(String jsonStr) {
//    try {
//      return JSON.parseArray(jsonStr, String.class);
//    } catch (Exception ex) {
//      return Arrays.asList(jsonStr.split(","));
//    }
//  }
//
//  /**
//   * 字符串的匹配方式是集合与集合之间对比，源字符串和目标字符串用逗号分隔，有任意一个匹配均表示匹配成功
//   *
//   * @param src    源字符串集合
//   * @param values 目标字符串集合
//   * @return
//   */
//  private boolean contains(List<String> src, List<String> values) {
//    src.stream().anyMatch()
//    for (String val : values) {
//      if (src.contains(val)) {
//        return true;
//      }
//    }
//    return false;
//  }
}
