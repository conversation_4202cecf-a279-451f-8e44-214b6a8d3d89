package com.anmi.collection.strategy;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 规则 条件节点
 *
 * <AUTHOR>
 * @date 2024/04/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DecisionCond extends Node{
  private String operator;
  private String value;
  private Integer valueType;
  private List<DecisionAttr> attrs;
  private List<DecisionAction> actions;
}
