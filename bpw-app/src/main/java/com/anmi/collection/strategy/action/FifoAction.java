package com.anmi.collection.strategy.action;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.CasePlanEnums;
import com.anmi.collection.common.enums.CasePlanTemplateEnums;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CasePlanService;
import com.anmi.collection.service.CasePlanTemplateService;
import com.anmi.collection.service.OrgSwitchService;
import com.anmi.collection.strategy.CaseStrategyBO;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.SpringContextHolder;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.domain.cases.CasePlan;
import com.anmi.domain.cases.CasePlanTemplate;
import com.anmi.domain.cases.CaseQueryResult;
import com.anmi.domain.cases.OrgSwitch;
import com.anmi.domain.decision.StrategyExecLog;
import com.anmi.domain.decision.StrategyInfo;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/27 10:22
 */
@Slf4j
public class FifoAction implements AnmiAction<CaseStrategyBO> {
    private final CasePlanTemplateService casePlanTemplateService;
    private final CasePlanService casePlanService;
    private final StringRedisTemplate stringRedisTemplate;
    private final OrgSwitchService orgSwitchService;

    public FifoAction() {
        this.casePlanTemplateService = SpringContextHolder.getBean(CasePlanTemplateService.class);
        this.casePlanService = SpringContextHolder.getBean(CasePlanService.class);
        this.stringRedisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
        this.orgSwitchService = SpringContextHolder.getBean(OrgSwitchService.class);
    }

    /**
     * 存储模板id_团队id和对应的planId
     */
    private final Map<String, Long> casePlanTemplateMap = new HashMap<>();

    @Override
    public void begin(Map<?, ?> execContext) {
        casePlanTemplateMap.clear();
    }

    @Override
    public Object doAction(CaseStrategyBO data, JSONObject param, Map<?, ?> execContext, Stack<String> matchPathNodes) {
        StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
        UserSession session = (UserSession) execContext.get(USER_SESSION);
        log.info("FifoAction ：data:{},param:{},execContext:{}", data, param, execContext);
        if (CaseEnums.IgnorePlan.YES.getCode().equals(data.getIgnorePlan())) {
            log.info("案件[{}]不参与团队预测式外呼计划,自动跳过", JSON.toJSONString(data));
            saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
            return null;
        }
        // 只有案件状态是分配完成、留案、分配至组才可以创建计划
        List<Integer> allotStatusList =  ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode(),CaseEnums.AllotStatus.ALLOT_TEAM.getCode());
        List<Integer> caseStatusList =   ImmutableList.of(CaseEnums.CaseStatus.NORMAL.getCode(), CaseEnums.CaseStatus.DELAY.getCode());
        if (!(allotStatusList.contains(data.getAllotStatus()) && caseStatusList.contains(data.getCaseStatus()))) {
            log.info("案件[{}]状态不满足预测式外呼计划，已过滤掉", JSON.toJSONString(data));
            saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
            return null;
        }
        // 案件催收结果为承诺还款且未到达承诺还款时间,不参与计划
        Date ptpTime = data.getPtpTime();
        if (Objects.nonNull(ptpTime) && ptpTime.after(new Date())) {
            log.info("案件[{}]承诺还款时间未到达，已过滤掉", JSON.toJSONString(data));
            saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
            return null;
        }
        // 过滤掉符合全局管控案件限制的案件
        OrgSwitch orgSwitch = orgSwitchService.selectOrgSwitchByOrgId(session.getOrgId());
        List<CaseQueryResult> caseList = new ArrayList<>();
        CaseQueryResult caseQueryResult = new CaseQueryResult();
        caseQueryResult.setId(data.getId());
        caseQueryResult.setTeamId(data.getTeamId());
        caseQueryResult.setOrgDeltId(data.getOrgDeltId());
        caseList.add(caseQueryResult);
        casePlanService.filterGlobalCtrlCase(caseList, orgSwitch);
        if (CollectionUtils.isEmpty(caseList)) {
            log.info("案件[{}]已经达到管控上线，已过滤掉", JSON.toJSONString(data));
            saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
            return null;
        }
        log.info("加入团队预测式外呼计划,案件信息[{}],参数[{}]", JSON.toJSONString(data), param);
        String key = StringUtils.trimAllWhitespace(param.getString("templateId"));
        if (StringUtils.isEmpty(key)) {
            throw new ApiException("加入团队预测试外呼计划模板参数不能为空");
        }
        String k = key + "_" + data.getTeamId();
        if (!casePlanTemplateMap.containsKey(k)) {
            CasePlan casePlan = createCasePlan(Long.parseLong(key), data.getTeamId(), execContext);
            casePlanTemplateMap.put(k, casePlan.getId());
        }
        Long planId = casePlanTemplateMap.get(k);
        stringRedisTemplate.opsForSet().add(KeyCache.CASE_FIFO_CASE_IDS + planId, data.getId().toString());
        saveExecLog(strategyExecLog, data, session, planId, param, matchPathNodes);
        return planId;
    }


    @Override
    public String actionName() {
        return "fifo";
    }

    private CasePlan createCasePlan(Long planTemplateId, Long teamId, Map<?, ?> execContext) {
        CasePlanTemplate template = casePlanTemplateService.selectByPrimaryKey(planTemplateId);
        if (Objects.isNull(template) || !CasePlanEnums.Type.FIFO.getCode().equals(template.getType())) {
            throw new ApiException("团队预测式计划模板不存在");
        }
        CasePlan casePlan = convertCasePlanTemplate(template, teamId, execContext);
        casePlan.setSelectedTotal(0L);
        casePlanService.insertSelective(casePlan);
        return casePlan;
    }

    @Override
    public void commit(Map<?, ?> execContext) {
        try {
            StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
            UserSession session = (UserSession) execContext.get(USER_SESSION);
            for (Map.Entry<String, Long> entry : casePlanTemplateMap.entrySet()) {
                Long planId = entry.getValue();
                Long size = stringRedisTemplate.opsForSet().size(KeyCache.CASE_PLAN_ROBOT_CASES + planId);
                CasePlan update = new CasePlan();
                update.setId(planId);
                update.setSelectedTotal(size);
                casePlanService.updateByPrimaryKeySelective(update);
                stringRedisTemplate.opsForList().leftPush(KeyCache.CASE_FIFO_PLAN_ID_LIST, planId.toString());
                Long templateId = Long .parseLong(entry.getKey().substring(0, entry.getKey().indexOf("_")));
                saveExecResult(strategyExecLog, session, size == null ? null : size.intValue(), templateId, planId);
            }
        } catch (Exception ex) {
            log.error("团队预测式外呼计划添加失败", ex);
            throw new ApiException("团队预测式外呼计划添加失败", ex);
        } finally {
            casePlanTemplateMap.clear();
        }
    }

    private CasePlan convertCasePlanTemplate(CasePlanTemplate template, Long teamId, Map<?, ?> execContext) {
        CasePlan casePlan = new CasePlan();
        BeanUtils.copyProperties(template, casePlan);
        casePlan.setId(null);
        casePlan.setStatus(CasePlanEnums.Status.NEW.getCode());
        casePlan.setName("策略生成-" + DateUtil.format(new Date(), "yyyyMMddHHmm"));
        if (CasePlanTemplateEnums.StartType.IMMEDIATELY.getCode().equals(template.getStartType())) {
            casePlan.setIsStartNow(1);
            casePlan.setStartType(1);
            casePlan.setPlanStartTime(new Date());
        }
        if (execContext != null && execContext.containsKey(USER_SESSION)) {
            UserSession userSession = ((UserSession) execContext.get(USER_SESSION));
            casePlan.setCreateBy(userSession.getId());
            casePlan.setUpdateBy(userSession.getId());
            casePlan.setOrgId(userSession.getOrgId());
            casePlan.setDepId(userSession.getDepId());
        } else {
            casePlan.setCreateBy(0L);
            casePlan.setUpdateBy(0L);
        }
        if (execContext != null && execContext.containsKey(STRATEGY_NAME)) {
            casePlan.setName(((StrategyInfo) execContext.get(STRATEGY_NAME)).getName() + "-" + template.getName() + "-" + DateUtil.format(new Date(), "yyyyMMddHHmm"));
        }
        casePlan.setTeamId(teamId);
        casePlan.setCreateTime(new Date());
        casePlan.setUpdateTime(new Date());
        casePlan.setDesc("策略生成-" + DateUtil.format(new Date(), "yyyyMMddHHmm"));
        return casePlan;
    }
}
