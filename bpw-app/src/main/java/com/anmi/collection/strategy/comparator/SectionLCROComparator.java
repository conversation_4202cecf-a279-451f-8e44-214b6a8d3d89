package com.anmi.collection.strategy.comparator;

import cn.hutool.core.util.NumberUtil;
import com.anmi.collection.strategy.comparator.base.SectionComparator;
import com.anmi.collection.strategy.utils.ClassUtil;
import com.anmi.collection.utils.DateUtils;

import java.util.Date;

/** <AUTHOR> 区间操作符左闭右开，Left Close Right Open */
public class SectionLCROComparator extends SectionComparator {

  @Override
  public boolean compare(Object data, String value) {
    if (data == null || value == null) {
      return false;
    }
    try {
      String[] val = value.split(",");
      long start = Long.parseLong(val[0]);
      long end = Long.parseLong(val[1]);
      if (ClassUtil.isNumber(data.getClass())) {
        return NumberUtil.toBigDecimal(data.toString()).compareTo(NumberUtil.toBigDecimal(val[0]))
                >= 0
            && NumberUtil.toBigDecimal(data.toString()).compareTo(NumberUtil.toBigDecimal(val[1]))
                < 0;
      }
      if (ClassUtil.isDate(data.getClass())) {
        long time = DateUtils.getStartTimeOfDate((Date) data).getTime();
        start = DateUtils.getStartTimeOfDate(new Date(start)).getTime();
        end = DateUtils.getStartTimeOfDate(new Date(end)).getTime();
        return time >= start && time < end;
      }
    } catch (Exception ex) {
      return false;
    }
    return false;
  }

  @Override
  public String operator() {
    return "[,)";
  }
}
