package com.anmi.collection.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.strategy.bo.ExecResult;
import com.anmi.collection.strategy.bo.ExecStatistic;
import com.anmi.collection.strategy.bo.TestResult;
import com.anmi.collection.strategy.bo.TestStatistic;
import com.anmi.collection.strategy.comparator.*;
import com.anmi.collection.strategy.enums.ComparatorValueType;
import com.anmi.collection.strategy.enums.ExecAllBranch;
import com.anmi.collection.strategy.exception.CheckException;
import com.anmi.collection.strategy.exception.NoSuchAction;
import com.anmi.collection.strategy.exception.NoSuchOperator;
import com.anmi.collection.strategy.utils.ClassUtil;

import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class DecisionBaseTraversal<T extends MetaInfo> implements DecisionTraversal<T> {

  protected Map<String, OperComparator> operComparatorMap;

  protected Map<String, Action<T>> actionMap = new HashMap<>();
  protected DecisionRuleComparator<T> decisionRuleComparator;
  protected Map<Long, DecisionRuleGroup> decisionRuleGroupMap = new HashMap<>();

  private final List<OperComparator> operComparatorList = new ArrayList<>();

  {
    operComparatorList.add(new BiggerThanComparator());
    operComparatorList.add(new BiggerThanOrEqualsComparator());
    operComparatorList.add(new SmallThanComparator());
    operComparatorList.add(new SmallThanOrEqualsComparator());
    operComparatorList.add(new SectionCloseComparator());
    operComparatorList.add(new SectionOpenComparator());
    operComparatorList.add(new SectionLCROComparator());
    operComparatorList.add(new SectionLORCComparator());
    operComparatorList.add(new EqualsComparator());
    operComparatorList.add(new NotEqualsComparator());
    operComparatorList.add(new AnyMatchComparator());
    operComparatorList.add(new NoMatchComparator());
    operComparatorList.add(new EndsWithComparator());
    operComparatorList.add(new NotEndsWithComparator());
    operComparatorList.add(new InComparator());
    operComparatorList.add(new NotInComparator());
    operComparatorList.add(new StartWithComparator());
    operComparatorList.add(new NotStartWithComparator());
    operComparatorList.add(new RegexComparator());
    operComparatorList.add(new ContainsComparator());
    operComparatorList.add(new NotContainsComparator());
    operComparatorList.add(new IsTrueComparator());
    operComparatorMap =
      operComparatorList.stream()
        .collect(Collectors.toMap(OperComparator::operator, Function.identity()));
    decisionRuleComparator = new DecisionRuleComparator<>(operComparatorMap);
  }

  @Override
  public void executeList(List<T> dataList, DecisionTree decisionTree, Map<?, ?> execContext, Map<String, ExecStatistic> strategyExecStatisticMap) {
    for (Map.Entry<String, Action<T>> actionEntry : actionMap.entrySet()) {
      actionEntry.getValue().begin(execContext);
    }
    for (T data : dataList) {
      ExecResult execResult = new ExecResult();
      Stack<String> matchPathNodes = new Stack<>();
      exec(data, decisionTree, execContext, execResult, matchPathNodes, strategyExecStatisticMap);
    }

    for (Map.Entry<String, Action<T>> actionEntry : actionMap.entrySet()) {
      actionEntry.getValue().commit(execContext);
    }
  }

  private void exec(T data, DecisionTree decisionTree, Map<?, ?> execContext, ExecResult execResult, Stack<String> nodes, Map<String, ExecStatistic> strategyExecStatisticMap) {
    if (CollectionUtil.isEmpty(decisionTree.getAttrList())) {
      throw new CheckException("决策树根部属性不能为空");
    }
    for (DecisionAttr attr : decisionTree.getAttrList()) {
      nodes.push(attr.getId());
      exec(data, attr, execContext, execResult, nodes, strategyExecStatisticMap);
      nodes.pop();
    }
  }

  private Boolean exec(T data, DecisionAttr attr, Map<?, ?> execContext, ExecResult execResult, Stack<String> nodes, Map<String, ExecStatistic> strategyExecStatisticMap) {
    Object value = getAttrObject(attr.getAttr(), data);
    if (Boolean.TRUE.equals(attr.getIsRuleGroup())) {
      DecisionRuleGroup decisionRuleGroup = this.decisionRuleGroupMap.get(attr.getRuleGroupId());
      Assert.notNull(decisionRuleGroup, "规则组{}不存在", attr.getRuleGroupId());
      value = decisionRuleComparator.compareRuleGroup(data, decisionRuleGroup);
    }
    if (CollectionUtil.isEmpty(attr.getConditions())) {
      throw new CheckException("属性{0}下的条件不能为空", attr.getAttr());
    }
    execResult.getNodeIds().add(attr.getId());
    boolean matchResult = false;
    for (DecisionCond condition : attr.getConditions()) {
      if (!strategyExecStatisticMap.containsKey(condition.getId())) {
        ExecStatistic execStatistic = new ExecStatistic();
        execStatistic.setAllCnt(0);
        execStatistic.setFailCnt(0);
        execStatistic.setSuccessCnt(0);
        execStatistic.setNodeId(condition.getId());
        execStatistic.setAttrName(attr.getAttr());
        strategyExecStatisticMap.put(condition.getId(), execStatistic);
      }
      ExecStatistic execStatistic = strategyExecStatisticMap.get(condition.getId());
      execStatistic.setAllCnt(execStatistic.getAllCnt() + 1);
      if (!operComparatorMap.containsKey(condition.getOperator())) {
        throw new NoSuchOperator(condition.getOperator());
      }
      OperComparator comparator = operComparatorMap.get(condition.getOperator());
      String compareValue = getCompareValue(condition, data);
      if (!comparator.compare(value, compareValue)) {
        execResult.getEndNodeIds().add(condition.getId());
        execStatistic.setFailCnt(execStatistic.getFailCnt() + 1);
        continue;
      }
      execStatistic.setSuccessCnt(execStatistic.getSuccessCnt() + 1);
      execResult.getNodeIds().add(condition.getId());
      nodes.push(condition.getId());
      if (!CollectionUtil.isEmpty(condition.getActions())) {
        for (DecisionAction action : condition.getActions()) {
          if (!actionMap.containsKey(action.getAction())) {
            throw new NoSuchAction(action.getAction());
          }
          execResult.getNodeIds().add(action.getId());
          execResult.getActionNodeIds().add(action.getId());
          nodes.push(action.getId());
          actionMap.get(action.getAction()).doAction(data, action.getActionParam(), execContext, nodes);
          nodes.pop();
        }
        matchResult = true;
      } else if (!CollectionUtil.isEmpty(condition.getAttrs())) {
        for (DecisionAttr subAttr : condition.getAttrs()) {
          nodes.push(subAttr.getId());
          if (exec(data, subAttr, execContext, execResult, nodes, strategyExecStatisticMap)) {
            matchResult = true;
          }
          nodes.pop();
        }
      } else {
        String message =
          MessageFormat.format(
            "动作和嵌套条件至少有一个{0}{1}{2}", attr, condition.getOperator(), condition.getValue());
        throw new CheckException(message);
      }
      nodes.pop();
      //不指定是否执行全部分支的话默认执行全部分支
      if (matchResult && Objects.equals(attr.getExecAllBranch(), ExecAllBranch.NO.getCode())) {
        break;
      }
    }
    return matchResult;
  }

  @Override
  public ExecResult execute(T data, DecisionTree decisionTree, Map<?, ?> execContext, Stack<String> nodes, Map<String, ExecStatistic> strategyExecStatisticMap) {
    ExecResult execResult = new ExecResult();
    exec(data, decisionTree, execContext, execResult, nodes, strategyExecStatisticMap);
    return execResult;
  }

  @Override
  public void check(DecisionTree decisionTree, Map<String, AttrType> attrTypeMap) {
    check(decisionTree.getAttrList(), attrTypeMap);
  }

  private void check(List<DecisionAttr> attrList, Map<String, AttrType> attrTypeMap) {
    for (DecisionAttr decisionAttr : attrList) {
      String attr = decisionAttr.getAttr();
      AttrType type = attrTypeMap.get(attr);
      if (Boolean.TRUE.equals(decisionAttr.getIsRuleGroup())) {
        //如果是规则组，直接跳过校验
        type = AttrType.BOOL;
      }
      if (type == null) {
        throw new CheckException("找不到属性:{0}", attr);
      }
      for (DecisionCond condition : decisionAttr.getConditions()) {
        if (!operComparatorMap.containsKey(condition.getOperator())) {
          throw new NoSuchOperator(condition.getOperator());
        }
        OperComparator comparator = operComparatorMap.get(condition.getOperator());
        try {
          if (Objects.equals(ComparatorValueType.STATIC.getCode(), condition.getValueType())) {
            comparator.check(type, condition.getValue());
          } else if (Objects.equals(ComparatorValueType.SYS_VAR.getCode(), condition.getValueType())) {
            if (!sysVarNameList.contains(condition.getValue())) {
              throw new CheckException("系统变量{0}不存在", condition.getValue());
            }
          }else if(Objects.equals(ComparatorValueType.SYS_DATE.getCode(),condition.getValueType())){
            //系统时间的check刚好可以使用原来的check
            comparator.check(type, condition.getValue());
          }
        } catch (CheckException e) {
          throw new CheckException("属性{0}校验失败:{1}", attr, e.getMessage());
        }
        if (!CollectionUtil.isEmpty(condition.getActions())) {
          for (DecisionAction action : condition.getActions()) {
            if (!actionMap.containsKey(action.getAction())) {
              throw new NoSuchAction(action.getAction());
            }
          }
        } else if (!CollectionUtil.isEmpty(condition.getAttrs())) {
          check(condition.getAttrs(), attrTypeMap);
        } else {
          String message = MessageFormat.format("动作和嵌套条件至少有一个{0}{1}{2}", attr, condition.getOperator(), condition.getValue());
          throw new CheckException(message);
        }
      }
    }
  }

  public void registerAction(Action<T> action) {
    actionMap.put(action.actionName(), action);
  }

  public void registerComparator(OperComparator comparator) {
    operComparatorMap.put(comparator.operator(), comparator);
  }

  protected Object getAttrObject(String attrName, T data) {
    return data.getAttrObject(attrName);
  }

  @Override
  public boolean integrityChecking(DecisionTree decisionTree) {
    if (CollectionUtil.isEmpty(decisionTree.getAttrList())) {
      return false;
    }
    for (DecisionAttr attr : decisionTree.getAttrList()) {
      if (!integrityChecking(attr)) {
        return false;
      }
    }
    return true;
  }

  public boolean integrityChecking(DecisionAttr attr) {
    if (CollectionUtil.isEmpty(attr.getConditions())) {
      return false;
    }
    for (DecisionCond condition : attr.getConditions()) {
      if (CollectionUtil.isEmpty(condition.getActions()) && CollectionUtil.isEmpty(condition.getAttrs())) {
        return false;
      }
      if (!CollectionUtil.isEmpty(condition.getAttrs())) {
        for (DecisionAttr subAttr : condition.getAttrs()) {
          if (!integrityChecking(subAttr)) {
            return false;
          }
        }
      }
    }
    return true;
  }

  public void registerRuleGroup(Long id,DecisionRuleGroup  decisionRuleGroup){
    this.decisionRuleGroupMap.put(id,decisionRuleGroup);
  }

  @Override
  public Map<String, List<JSONObject>> getActionMap(DecisionTree decisionTree) {
    Map<String, List<JSONObject>> result = new HashMap<>();
    fillActionMap(decisionTree.getAttrList(), null, result);
    return result;
  }

  private void fillActionMap(List<DecisionAttr> attrs, List<DecisionAction> actions, Map<String, List<JSONObject>> actionMap) {
    if (!CollectionUtil.isEmpty(actions)) {
      for (DecisionAction action : actions) {
        if (!actionMap.containsKey(action.getAction())) {
          actionMap.put(action.getAction(), new ArrayList<>());
        }
        actionMap.get(action.getAction()).add(action.getActionParam());
      }
    }
    if (!CollectionUtil.isEmpty(attrs)) {
      for (DecisionAttr decisionAttr : attrs) {
        fillActionMap(decisionAttr.getConditions(), actionMap);
      }
    }
  }

  private void fillActionMap(List<DecisionCond> decisionCondList, Map<String, List<JSONObject>> actionMap) {
    if (!CollectionUtil.isEmpty(decisionCondList)) {
      for (DecisionCond con : decisionCondList) {
        fillActionMap(con.getAttrs(), con.getActions(), actionMap);
      }
    }
  }

  @Override
  public Map<String, Integer> getAttrMap(DecisionTree decisionTree) {
    Map<String, Integer> result = new HashMap<>();
    fillAttrMap(decisionTree.getAttrList(), result);
    return result;
  }

  private void fillAttrMap(List<DecisionAttr> attrs, Map<String, Integer> attrMap) {
    if (!CollectionUtil.isEmpty(attrs)) {
      for (DecisionAttr decisionAttr : attrs) {
        if(!Objects.equals(decisionAttr.getIsRuleGroup(),Boolean.TRUE)) {
          Integer cnt = attrMap.getOrDefault(decisionAttr.getAttr(), 0);
          attrMap.put(decisionAttr.getAttr(), cnt + 1);
        }
        fillAttrCondMap(decisionAttr.getConditions(), attrMap);
      }
    }
  }

  private void fillAttrCondMap(List<DecisionCond> decisionCondList, Map<String, Integer> attrMap) {
    if (!CollectionUtil.isEmpty(decisionCondList)) {
      for (DecisionCond con : decisionCondList) {
        if (Objects.equals(con.getValueType(), ComparatorValueType.ATTR.getCode())) {
          Integer cnt = attrMap.getOrDefault(con.getValue(), 0);
          attrMap.put(con.getValue(), cnt + 1);
        }
        fillAttrMap(con.getAttrs(), attrMap);
      }
    }
  }

  @Override
  public Map<Long, Integer> getRuleGroupMap(DecisionTree decisionTree) {
    Map<Long, Integer> result = new HashMap<>();
    fillRuleGroupMap(decisionTree.getAttrList(), result);
    return result;
  }

  private void fillRuleGroupMap(List<DecisionAttr> attrs, Map<Long, Integer> ruleGroupMap) {
    if (!CollectionUtil.isEmpty(attrs)) {
      for (DecisionAttr decisionAttr : attrs) {
        if(Objects.equals(decisionAttr.getIsRuleGroup(),Boolean.TRUE)){
          Integer cnt = ruleGroupMap.getOrDefault(decisionAttr.getRuleGroupId(), 0);
          ruleGroupMap.put(decisionAttr.getRuleGroupId(), cnt + 1);
        }
        fillRuleGroupCondMap(decisionAttr.getConditions(), ruleGroupMap);
      }
    }
  }

  private void fillRuleGroupCondMap(List<DecisionCond> decisionCondList, Map<Long, Integer> ruleGroupMap) {
    if (!CollectionUtil.isEmpty(decisionCondList)) {
      for (DecisionCond con : decisionCondList) {
        fillRuleGroupMap(con.getAttrs(), ruleGroupMap);
      }
    }
  }

  @Override
  public TestResult test(T testData, DecisionTree decisionTree, Map<String, TestStatistic> strategyTestStatisticMap) {
    TestResult result = new TestResult();
    if (CollectionUtil.isEmpty(decisionTree.getAttrList())) {
      throw new CheckException("决策树根部属性不能为空");
    }
    for (DecisionAttr attr : decisionTree.getAttrList()) {
      recursionTestAttr(testData, attr, result, strategyTestStatisticMap);
    }
    return result;
  }

  private Boolean recursionTestAttr(T data, DecisionAttr attr, TestResult result, Map<String, TestStatistic> strategyTestStatisticMap) {
    result.getNodeIds().add(attr.getId());
    Object value = getAttrObject(attr.getAttr(), data);
    if (Boolean.TRUE.equals(attr.getIsRuleGroup())) {
      DecisionRuleGroup decisionRuleGroup = this.decisionRuleGroupMap.get(attr.getRuleGroupId());
      Assert.notNull(decisionRuleGroup, "规则组{}不存在", attr.getRuleGroupId());
      value = decisionRuleComparator.compareRuleGroup(data, decisionRuleGroup);
    }
    if (CollectionUtil.isEmpty(attr.getConditions())) {
      throw new CheckException("属性{0}下的条件不能为空", attr.getAttr());
    }

    boolean matchResult = false;
    for (DecisionCond condition : attr.getConditions()) {
      if (!strategyTestStatisticMap.containsKey(condition.getId())) {
        TestStatistic testStatistic = new TestStatistic();
        testStatistic.setAllCnt(0);
        testStatistic.setFailCnt(0);
        testStatistic.setSuccessCnt(0);
        testStatistic.setNodeId(condition.getId());
        testStatistic.setAttrName(attr.getAttr());
        strategyTestStatisticMap.put(condition.getId(), testStatistic);
      }
      TestStatistic testStatistic = strategyTestStatisticMap.get(condition.getId());
      testStatistic.setAllCnt(testStatistic.getAllCnt() + 1);
      if (!operComparatorMap.containsKey(condition.getOperator())) {
        throw new NoSuchOperator(condition.getOperator());
      }
      OperComparator comparator = operComparatorMap.get(condition.getOperator());
      String compareValue = getCompareValue(condition, data);
      if (!comparator.compare(value, compareValue)) {
        result.getEndNodeIds().add(condition.getId());
        testStatistic.setFailCnt(testStatistic.getFailCnt() + 1);
        continue;
      }
      testStatistic.setSuccessCnt(testStatistic.getSuccessCnt() + 1);
      result.getNodeIds().add(condition.getId());
      if (!CollectionUtil.isEmpty(condition.getActions())) {
        for (DecisionAction action : condition.getActions()) {
          result.getNodeIds().add(action.getId());
          result.getActionNodeIds().add(action.getId());
        }
        matchResult = true;
      } else if (!CollectionUtil.isEmpty(condition.getAttrs())) {
        for (DecisionAttr subAttr : condition.getAttrs()) {
          if (recursionTestAttr(data, subAttr, result, strategyTestStatisticMap)) {
            matchResult = true;
          }
        }
      } else {
        String message =
          MessageFormat.format(
            "动作和嵌套条件至少有一个{0}{1}{2}", attr, condition.getOperator(), condition.getValue());
        throw new CheckException(message);
      }
      if (matchResult && Objects.equals(attr.getExecAllBranch(), ExecAllBranch.NO.getCode())) {
        break;
      }
    }
    return matchResult;
  }

  private String getCompareValue(DecisionCond condition, T data) {
    if (Objects.equals(condition.getValueType(), ComparatorValueType.ATTR.getCode())) {
      Object val = getAttrObject(condition.getValue(), data);
      if (Objects.isNull(val)) {
        return null;
      }
      if (ClassUtil.isDate(val.getClass())) {
        return String.valueOf(((Date)val).getTime());
      }
      return val.toString();
    } else if (Objects.equals(condition.getValueType(), ComparatorValueType.SYS_VAR.getCode())) {
      String sysVarName = condition.getValue();
      Object val = getSysVar(sysVarName);
      if (Objects.isNull(val)) {
        return null;
      }
      if (ClassUtil.isDate(val.getClass())) {
        return String.valueOf(((Date)val).getTime());
      }
      return val.toString();
    } else if (Objects.equals(condition.getValueType(), ComparatorValueType.SYS_DATE.getCode())) {
      int offset = Integer.parseInt(condition.getValue());
      Date date = DateUtil.offsetDay(new Date(), offset);
      return String.valueOf((date).getTime());
    } else {
      return condition.getValue();
    }
  }

  private List<String> sysVarNameList = Collections.singletonList("sysdate");

  private Object getSysVar(String varName) {
    if ("sysdate".equalsIgnoreCase(varName)) {
      return new Date();
    }
    return null;
  }

}
