package com.anmi.collection.strategy;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSON;
import com.anmi.collection.entity.requset.cases.CaseMultiQuery;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.mapper.CaseOperationMapper;
import com.anmi.collection.mapper.CaseTagMapper;
import com.anmi.collection.strategy.exception.CheckException;
import com.anmi.collection.utils.*;
import com.anmi.domain.base.DictionaryEntity;
import com.anmi.domain.cases.CaseQueryResult;
import com.anmi.domain.cases.CaseRobotCountResult;
import com.anmi.domain.cases.CaseTemplate;
import com.anmi.domain.cases.CaseTemplateResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CaseStrategyBO extends HashMap<String, Object> implements MetaInfo {

  private Long id;
  private Long amount;
  private Integer ignorePlan;
  private String ownMobile;
  private Date overdueDate;
  private Integer overdueDays;
  private Integer allotStatus;
  private Integer caseStatus;
  private Integer allotAgentState;
  private String outSerialNo;
  private Date ptpTime;
  private Date operationNextTime;
  private Date lastFollowTime;
  private Long teamId;
  private Long orgDeltId;

  public static CaseStrategyBO loadFromMap(Map<String, Object> data, Map<String, AttrType> typeMap) {
    if (data == null || data.isEmpty()) {
      return null;
    }
    CaseStrategyBO bo = new CaseStrategyBO();
    for (Map.Entry<String, Object> entry : data.entrySet()) {
      AttrType type = typeMap.get(entry.getKey());
      if (type == null) {
        throw new CheckException("未知的属性名:{0}", entry.getKey());
      }
      bo.put(entry.getKey(), convert(type, entry.getValue().toString(), false));
    }
    return bo;
  }

  private static Object convert(CaseQueryResult caseQueryResult, String key, AttrType type, Boolean autoUpdateOverdueDays, Boolean isMoney) {
    try {
      switch (key) {
        case "productType":
          return caseQueryResult.getProductType();
        case "productName":
          return caseQueryResult.getProductId();
        case "amount":
          return BigDecimal.valueOf(caseQueryResult.getAmount() / 1000.00);
        case "outBatchNo":
          return caseQueryResult.getOutBatchId();
        case "autoAssistResult":
          return caseQueryResult.getAutoAssistResult();
        case "overdueDays":
          if (autoUpdateOverdueDays) {
            return (int) DateUtil.betweenDay(new Date(), caseQueryResult.getOverdueDate(), true) * new Date().compareTo(caseQueryResult.getOverdueDate());
          }
          return caseQueryResult.getOverdueDays();
        case "operationState":
          return caseQueryResult.getOperationState();
        case "actionType":
          return caseQueryResult.getOperStatus();
        case "callType":
          return caseQueryResult.getCallType();
        case "autoAssistDate":
          return caseQueryResult.getAutoAssistDate();
        case "debtFollowCount":
          return caseQueryResult.getDebtFollowCount();
        case "divisionTime":
          return caseQueryResult.getDivisionTime();
        case "tag":
          return caseQueryResult.getTag();
        case "color":
          return caseQueryResult.getColor();
        case "allotStatus":
          return caseQueryResult.getAllotStatus();
        case "caseStatus":
          return caseQueryResult.getCaseStatus();
        case "wayAllotState":
          return caseQueryResult.getWayAllotState();
        case "operationWay":
          return caseQueryResult.getOperationWay();
        case "outAgent":
          return caseQueryResult.getDepId();
        case "orgDelt":
          return caseQueryResult.getOrgDeltId();
        case "outsourceCount":
          return caseQueryResult.getOutsourceCount();
        case "autoAssistRecord":
          return StrUtil.split(caseQueryResult.getAutoAssistRecord(),",");
        case "name":
          return caseQueryResult.getName();
        case "id_card":
          return caseQueryResult.getIdCard();
        case "own_mobile":
          return caseQueryResult.getOwnMobile();
        case "out_serial_no":
          return caseQueryResult.getOutSerialTemp();
        case "overdue_date":
          return caseQueryResult.getOverdueDate();
        case "caseAge":
          if (autoUpdateOverdueDays) {
            return convertCaseAge((int) DateUtil.betweenDay(new Date(), caseQueryResult.getOverdueDate(), true) * new Date().compareTo(caseQueryResult.getOverdueDate()));
          }
          return convertCaseAge(caseQueryResult.getOverdueDays());
        default:
          return convert(type, caseQueryResult.getFieldJson().get(key), isMoney);
      }
    } catch (Exception ex) {
      return null;
    }
  }

  private static Object convert(AttrType attrType, String val, Boolean isMoney) {
    if (StringUtils.isBlank(val)) {
      return null;
    }
    if (Objects.equals(AttrType.STRING, attrType)) {
      return val;
    }
    try {
      //数字类型一律转换成小数
      if (Objects.equals(AttrType.NUMBER, attrType)) {
        if (isMoney) {
          return new BigDecimal(val).divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP);
        }
        return new BigDecimal(val);
      }
      if (Objects.equals(AttrType.DATE, attrType)) {
        return DateUtils.tryParseDate(val);
      }
      if (Objects.equals(AttrType.COLLECTION, attrType)) {
        return JSON.parseArray(val, String.class);
      }

    } catch (Exception ignore) {
      return null;
    }
    throw new ApiException("未知的类型：" + attrType);
  }

  private static String convertCaseAge(Integer overdueDays) {
    if (overdueDays > 180) {
      return "M6+";
    } else {
      int round = overdueDays % 30 == 0 ? overdueDays / 30 : overdueDays / 30 + 1;
      return "M" + round;
    }
  }

  public static CaseStrategyBO loadFromCaseId(Long caseId, Boolean autoUpdateOverdueDays, Map<String, AttrType> orgAttrList, String language) {
    if (ObjectUtil.isNull(caseId)){
      return null;
    }
    List<Long> caseIds = Arrays.asList(caseId);
    List<CaseStrategyBO> caseStrategyBOS = loadFromCaseIds(caseIds, autoUpdateOverdueDays, orgAttrList, language);
    if (ObjectUtil.isEmpty(caseStrategyBOS)){
      return null;
    }
    return caseStrategyBOS.get(0);
  }

  public static List<CaseStrategyBO> loadFromCaseIds(List<Long> caseIds, Boolean autoUpdateOverdueDays, Map<String, AttrType> typeMap, String language) {
    if (CollectionUtils.isEmpty(caseIds)) {
      return new ArrayList<>();
    }
    CaseMapper caseMapper = SpringContextHolder.getBean(CaseMapper.class);
    CaseTagMapper caseTagMapper = SpringContextHolder.getBean(CaseTagMapper.class);
    StringRedisTemplate stringRedisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
    CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
    caseMultiQuery.setCaseIds(caseIds);
    List<CaseQueryResult> caseQueryResultList = caseMapper.queryResultForMulti(caseMultiQuery);
    List<Map<String, Object>> caseTags = caseTagMapper.selectTagNamesByCaseIds(caseIds);
    Map<Long, List<Map<String, Object>>> caseTagNames = caseTags.stream().collect(Collectors.groupingBy(t -> (Long) t.get("id")));
    List<CaseStrategyBO> result = new ArrayList<>();
    Map<Long, CaseRobotCountResult> caseRobotCountMap = getRobotCountByCaseIds(caseIds);
    List<CaseTemplateResult> caseTemplateResults = caseMapper.selectCaseTemp(caseIds);
    Map<Long,Long> caseTempMap = caseTemplateResults.stream().collect(Collectors.toMap(CaseTemplateResult::getCaseId,CaseTemplateResult::getTemplateId));

    TypeReference<Map<String, DictionaryEntity>> typeReference = new TypeReference<Map<String, DictionaryEntity>>() {
    };
    Map<String, DictionaryEntity> allFields = JSONObject.parseObject(stringRedisTemplate.opsForValue().get("all_fields" + getLanguageKey(language)), typeReference);
    assert allFields != null;
    for (CaseQueryResult caseQueryResult : caseQueryResultList) {
      CaseStrategyBO bo = new CaseStrategyBO();
      bo.setId(caseQueryResult.getId());
      bo.setAmount(caseQueryResult.getAmount());
      bo.setOverdueDays(caseQueryResult.getOverdueDays());
      bo.setIgnorePlan(caseQueryResult.getIgnorePlan());
      bo.setOwnMobile(caseQueryResult.getOwnMobile());
      bo.setOverdueDate(caseQueryResult.getOverdueDate());
      bo.setAllotStatus(caseQueryResult.getAllotStatus());
      bo.setCaseStatus(caseQueryResult.getCaseStatus());
      bo.setAllotAgentState(caseQueryResult.getAllotAgentState());
      bo.setOutSerialNo(caseQueryResult.getOutSerialTemp());
      bo.setPtpTime(caseQueryResult.getPtpTime());
      bo.setOperationNextTime(caseQueryResult.getOperationNextTime());
      bo.setLastFollowTime(caseQueryResult.getLastFollowTime());
      bo.setTeamId(caseQueryResult.getTeamId());
      bo.setOrgDeltId(caseQueryResult.getOrgDeltId());
      for (Map.Entry<String, AttrType> entry : typeMap.entrySet()) {
        boolean isMoney = false;
        if (allFields.containsKey(entry.getKey())) {
          if ("Money".equalsIgnoreCase(allFields.get(entry.getKey()).getType())) {
            isMoney = true;
          }
        }
        bo.put(entry.getKey(), convert(caseQueryResult, entry.getKey(), entry.getValue(), autoUpdateOverdueDays, isMoney));
      }
      List<String> tagIds = caseTagNames.getOrDefault(bo.getId(), new ArrayList<>()).stream().map(t -> String.valueOf(t.get("tag_id"))).collect(Collectors.toList());
      bo.put("caseTags", tagIds);
      bo.put("robotCount", 0);
      bo.put("robotSuccessCount", 0);
      bo.put("robotFailCount", 0);
      bo.put("templateId",caseTempMap.get(bo.getId()));
      CaseRobotCountResult caseRobotCountResult = caseRobotCountMap.get(bo.getId());
      if (Objects.nonNull(caseRobotCountResult)) {
        bo.put("robotCount", caseRobotCountResult.getCount());
        bo.put("robotSuccessCount", caseRobotCountResult.getSuccessCount());
        bo.put("robotFailCount", caseRobotCountResult.getFailCount());
      }
      result.add(bo);
    }
    return result;
  }

  private static Map<Long, CaseRobotCountResult> getRobotCountByCaseIds(List<Long> caseIds) {
    List<List<Long>> subCaseIdLists = CmUtil.splitList(caseIds, 100);
    Map<Long, CaseRobotCountResult> caseCountMap = new HashMap<>();
    Date startOfToday = DateUtil.beginOfDay(new Date());
    Date endOfToday = DateUtil.endOfDay(new Date());
    for (List<Long> subCaseIds : subCaseIdLists) {
      CaseOperationMapper caseOperationMapper = SpringContextHolder.getBean(CaseOperationMapper.class);
      List<CaseRobotCountResult> result = caseOperationMapper.selectRobotCountByCaseIds(startOfToday, endOfToday, subCaseIds);
      Map<Long, CaseRobotCountResult> map = result.stream().collect(Collectors.toMap(CaseRobotCountResult::getCaseId, Function.identity()));
      caseCountMap.putAll(map);
    }
    return caseCountMap;
  }


  private static String getLanguageKey(String language) {
    if (StringUtils.isBlank(language)) {
      return "";
    }
    return "_" + language;
  }

  @Override
  public Object getAttrObject(String attrName) {
    return this.get(attrName);
  }
}
