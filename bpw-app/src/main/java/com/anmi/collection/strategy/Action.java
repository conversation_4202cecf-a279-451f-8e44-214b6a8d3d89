package com.anmi.collection.strategy;

import com.alibaba.fastjson.JSONObject;

import java.util.Map;
import java.util.Stack;

/**
 * 决策树动作执行器
 *
 * <AUTHOR>
 */
public interface Action<T extends MetaInfo> {

  /**
   * 初始化，主要是为了给批量执行决策树的时候用的
   *
   * @param execContext 决策树上下文,由调用方自定义,可以传入执行方式，执行人等信息，具体的action里面可以获取到
   */
  void begin(Map<?, ?> execContext);

  /**
   * 执行动作
   *
   * @param data 原始数据
   * @param param 动作参数，主要是预测式外呼、机器人等的模板id
   * @param execContext 决策树上下文,由调用方自定义,可以传入执行方式，执行人等信息，具体的action里面可以获取到
   * @param matchPathNodes 执行路径
   * @return 返回策略执行的结果
   */
  Object doAction(T data, JSONObject param, Map<?, ?> execContext, Stack<String> matchPathNodes);

  /**
   * 执行器名称，fifo/sms/robot/personalFifo/letter等
   *
   * @return 返回执行器名称
   */
  String actionName();

  /**
   * 批量提交时使用
   *
   * @param execContext 决策树上下文,由调用方自定义,可以传入执行方式，执行人等信息，具体的action里面可以获取到
   */
  void commit(Map<?, ?> execContext);
}
