package com.anmi.collection.strategy.comparator;

import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.common.enums.Operator;
import com.anmi.collection.utils.AssertUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 2024/3/19
 * Holder
 *
 * <AUTHOR>
 */
public class ComparatorHolder {

    private static final List<OperComparator> operComparatorList = Arrays.asList(
        new BiggerThanComparator(),
        new BiggerThanOrEqualsComparator(),
        new SmallThanComparator(),
        new SmallThanOrEqualsComparator(),
        new SectionCloseComparator(),
        new SectionOpenComparator(),
        new SectionLCROComparator(),
        new SectionLORCComparator(),
        new EqualsComparator(),
        new NotEqualsComparator(),
        new AnyMatchComparator(),
        new NoMatchComparator(),
        new EndsWithComparator(),
        new NotEndsWithComparator(),
        new InComparator(),
        new NotInComparator(),
        new StartWithComparator(),
        new NotStartWithComparator(),
        new RegexComparator(),
        new ContainsComparator(),
        new NotContainsComparator(),
        new IsTrueComparator()
    );

    public static OperComparator select(String operator){
        Boolean operatorExists = Operator.isExists(operator);
        AssertUtil.isTrue(operatorExists,"运算符【"+operator+"】未知");
        Optional<OperComparator> first = operComparatorList.stream().filter(a -> ObjectUtil.equals(a.operator(), operator)).findFirst();
        AssertUtil.isTrue(first.isPresent(),"运算策略未匹配");
        return first.get();
    }
}
