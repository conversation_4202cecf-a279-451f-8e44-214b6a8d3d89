package com.anmi.collection.strategy.comparator;

import cn.hutool.core.util.NumberUtil;
import com.anmi.collection.strategy.AttrType;
import com.anmi.collection.strategy.exception.CheckException;
import com.anmi.collection.strategy.utils.ClassUtil;
import com.anmi.collection.utils.DateUtils;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class EqualsComparator implements OperComparator {

  @Override
  public boolean compare(Object data, String value) {
    if (data == null || value == null) {
      return false;
    }
    try {
      if (ClassUtil.isDate(data.getClass())) {
        return DateUtils.getStartTimeOfDate((Date) data).getTime()
          == DateUtils.getStartTimeOfDate(new Date(Long.parseLong(value))).getTime();
      }
      if (ClassUtil.isNumber(data.getClass())) {
        return NumberUtil.toBigDecimal(data.toString()).compareTo(NumberUtil.toBigDecimal(value))
          == 0;
      }
      return data.toString().equals(value);
    } catch (Exception ex) {
      return false;
    }
  }


  @Override
  public void check(AttrType attrType, String value) throws CheckException {
    if (attrType == AttrType.DATE || attrType == AttrType.NUMBER) {
      if (!NumberUtil.isNumber(value)) {
        throw new CheckException("数据格式不匹配");
      }
    }
    if (attrType == AttrType.COLLECTION) {
      throw new CheckException("集合匹配不能用等于");
    }
  }

  @Override
  public String operator() {
    return "=";
  }
}
