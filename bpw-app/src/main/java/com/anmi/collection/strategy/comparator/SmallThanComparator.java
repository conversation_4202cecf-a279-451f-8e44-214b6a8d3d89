package com.anmi.collection.strategy.comparator;

import cn.hutool.core.util.NumberUtil;
import com.anmi.collection.strategy.comparator.base.DateNumComparator;
import com.anmi.collection.strategy.utils.ClassUtil;
import com.anmi.collection.utils.DateUtils;

import java.util.Date;

/** <AUTHOR> */
public class SmallThanComparator extends DateNumComparator {

  @Override
  public boolean compare(Object data, String value) {
    if (data == null || value == null) {
      return false;
    }
    if (!NumberUtil.isNumber(value)) {
      return false;
    }
    try {
      if (ClassUtil.isNumber(data.getClass())) {
        return NumberUtil.toBigDecimal(data.toString()).compareTo(NumberUtil.toBigDecimal(value))
            < 0;
      }
      if (ClassUtil.isDate(data.getClass())) {
        return DateUtils.getStartTimeOfDate((Date) data).getTime()
            < DateUtils.getStartTimeOfDate(new Date(Long.parseLong(value))).getTime();
      }
    } catch (Exception ex) {
      return false;
    }
    return false;
  }

  @Override
  public String operator() {
    return "<";
  }
}
