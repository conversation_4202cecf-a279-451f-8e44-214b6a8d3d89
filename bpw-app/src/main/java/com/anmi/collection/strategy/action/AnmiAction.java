package com.anmi.collection.strategy.action;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.common.enums.StrategyEnums;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.mapper.StrategyExecCaseDetailMapper;
import com.anmi.collection.mapper.StrategyExecResultMapper;
import com.anmi.collection.strategy.Action;
import com.anmi.collection.strategy.CaseStrategyBO;
import com.anmi.collection.strategy.MetaInfo;
import com.anmi.collection.utils.SpringContextHolder;
import com.anmi.domain.decision.StrategyExecCaseDetail;
import com.anmi.domain.decision.StrategyExecLog;
import com.anmi.domain.decision.StrategyExecResult;

import java.util.Date;
import java.util.Stack;

/**
 * <AUTHOR>
 * 该目录下的action为安米系统特有的action
 */
public interface AnmiAction<T extends MetaInfo> extends Action<T> {

  String USER_SESSION = "userSession";
  String STRATEGY_NAME = "strategyInfo";
  String STRATEGY_LOG = "strategy_log";

  /**
   * 保存执行详细记录
   *
   * @param strategyExecLog 执行记录
   * @param caseStrategyBo  案件数据
   * @param userSession     会话
   * @param actionPlanId    执行动作的结果(计划的id或者异步任务的id)
   * @param param           参数
   * @param matchPathNodes  匹配到的路径
   */
  default void saveExecLog(StrategyExecLog strategyExecLog, CaseStrategyBO caseStrategyBo,
                           UserSession userSession,
                           Long actionPlanId, JSONObject param, Stack<String> matchPathNodes) {
    StrategyExecCaseDetail strategyExecCaseDetail = new StrategyExecCaseDetail();
    strategyExecCaseDetail.setStrategyExecId(strategyExecLog.getId());
    strategyExecCaseDetail.setCaseId(caseStrategyBo.getId());
    strategyExecCaseDetail.setMatchResult(StrategyEnums.MatchResult.MATCH.getCode());
    strategyExecCaseDetail.setCreateBy(userSession.getId());
    strategyExecCaseDetail.setUpdateBy(userSession.getId());
    strategyExecCaseDetail.setCreateTime(new Date());
    strategyExecCaseDetail.setUpdateTime(new Date());
    strategyExecCaseDetail.setActionName(this.actionName());
    strategyExecCaseDetail.setActionParam(JSON.toJSONString(param));
    strategyExecCaseDetail.setMatchPath(JSON.toJSONString(matchPathNodes.toArray()));
    String actionNodeId = matchPathNodes.peek();
    strategyExecCaseDetail.setActionNode(actionNodeId);
    strategyExecCaseDetail.setActionResultId(actionPlanId);
    strategyExecCaseDetail.setActionResult(null);
    SpringContextHolder.getBean(StrategyExecCaseDetailMapper.class).insert(strategyExecCaseDetail);
  }

  /**
   * 保存执行结果
   *
   * @param strategyExecLog 执行记录
   * @param session         会话
   * @param caseCnt         案件数量
   * @param paramId         参数(计划模版id或者分案模版id，或者催收方式流转的类型)
   * @param relId           关联id，生成的计划id，或者异步任务的id
   */
  default void saveExecResult(StrategyExecLog strategyExecLog, UserSession session, Integer caseCnt, Long paramId, Long relId) {
    StrategyExecResult strategyExecResult = new StrategyExecResult();
    strategyExecResult.setStrategyExecId(strategyExecLog.getId());
    strategyExecResult.setStrategyName(strategyExecLog.getName());
    strategyExecResult.setActionType(this.actionName());
    strategyExecResult.setExecTime(strategyExecLog.getExecTime());
    strategyExecResult.setCreateBy(session.getId());
    strategyExecResult.setCreateTime(new Date());
    strategyExecResult.setCaseCnt(caseCnt);
    strategyExecResult.setParamId(paramId);
    strategyExecResult.setRelId(relId);
    strategyExecResult.setUpdateBy(session.getId());
    strategyExecResult.setUpdateTime(new Date());
    strategyExecResult.setOrgId(session.getOrgId());
    SpringContextHolder.getBean(StrategyExecResultMapper.class).insert(strategyExecResult);
  }

}
