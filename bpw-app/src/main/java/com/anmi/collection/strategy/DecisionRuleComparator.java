package com.anmi.collection.strategy;

import cn.hutool.core.lang.Assert;
import com.anmi.collection.strategy.comparator.OperComparator;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class DecisionRuleComparator<T extends MetaInfo> {

  private final Map<String, OperComparator> comparatorMap;

  public DecisionRuleComparator(Map<String, OperComparator> comparatorMap) {
    this.comparatorMap = comparatorMap;
  }

  /**
   * 条件测试
   *
   * @param data             需要测试的数据
   * @param decisionRuleCond 测试条件
   * @return 测试通过则返回true，失败返回false
   */
  private boolean compareCond(T data, DecisionRuleCond decisionRuleCond) {
    Object val = data.getAttrObject(decisionRuleCond.getAttrName());
    OperComparator operComparator = comparatorMap.get(decisionRuleCond.getOperator());
    Assert.notNull(operComparator, "不支持的操作符{}", decisionRuleCond.getOperator());
    return operComparator.compare(val, decisionRuleCond.getValue());
  }

  /**
   * 规则测试
   *
   * @param data         需要测试的数据
   * @param decisionRule 测试规则，规则是包含一系列的条件和匹配模式，如果是匹配模式为全匹配则需要每个条件都匹配成功，如果匹配模式为任意匹配则只需要匹配一条成功的即可
   * @return 测试通过则返回true，失败返回false
   */
  private boolean compareRule(T data, DecisionRule decisionRule) {
    boolean predict = Objects.equals(decisionRule.getMatchType(), MatchType.ALL.getCode());
    for (DecisionRuleCond cond : decisionRule.getCondList()) {
      if (predict && !compareCond(data, cond)) {
        return false;
      }
      if (!predict && compareCond(data, cond)) {
        return true;
      }
    }
    return predict;
  }

  /**
   * 规则组测试
   *
   * @param data              需要测试的数据
   * @param decisionRuleGroup 测试规则组，规则组是包含一系列的规则和匹配模式，如果是匹配模式为全匹配则需要每个规则都匹配成功，如果匹配模式为任意匹配则只需要匹配一条成功的即可
   * @return 测试通过则返回true，失败返回false
   */
  public boolean compareRuleGroup(T data, DecisionRuleGroup decisionRuleGroup) {
    boolean predict = Objects.equals(decisionRuleGroup.getMatchType(), MatchType.ALL.getCode());
    for (DecisionRule rule : decisionRuleGroup.getRules()) {
      if (predict && !compareRule(data, rule)) {
        return false;
      }
      if (!predict && compareRule(data, rule)) {
        return true;
      }
    }
    return predict;
  }

}
