package com.anmi.collection.strategy;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

/** <AUTHOR> 决策树，前端传值 */
@Data
public class DecisionNode {
  private String id;
  private String attr;

  /**
   * 标识属性是否为规则组
   */
  private Integer isRuleGroup;
  private Long ruleGroupId;

  private String label;
  private String operator;
  private Integer execAllBranch;
  private String value;
  /**
   * 值类型，0表示静态值，1表示动态值(属性)，该值为1时value为属性名称
   */
  private Integer valueType;
  private String action;
  private String type;
  private JSONObject actionParam;
  private List<DecisionNode> children;
  private Boolean visited;
}
