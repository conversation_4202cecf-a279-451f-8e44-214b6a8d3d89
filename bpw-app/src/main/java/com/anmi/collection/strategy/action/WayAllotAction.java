package com.anmi.collection.strategy.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.entity.requset.amc.CaseSwitchWayParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.AsyncTaskService;
import com.anmi.collection.strategy.CaseStrategyBO;
import com.anmi.collection.utils.SpringContextHolder;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.decision.StrategyExecLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 催收手段流转
 *
 * <AUTHOR>
 */
@Slf4j
public class WayAllotAction implements AnmiAction<CaseStrategyBO> {

  private final StringRedisTemplate stringRedisTemplate;
  private final AsyncTaskService asyncTaskService;
  private final RedisUtil redisUtil;


  /**
   * 存储催收手段id和对应的asyncTaskId
   */
  private final Map<Integer, Long> caseSwitchWayMap = new HashMap<>();

  public WayAllotAction() {
    this.stringRedisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
    this.asyncTaskService = SpringContextHolder.getBean(AsyncTaskService.class);
    this.redisUtil = SpringContextHolder.getBean(RedisUtil.class);
  }

  @Override
  public Object doAction(CaseStrategyBO data, JSONObject actionParam, Map<?, ?> execContext, Stack<String> matchPathNodes) {
    try {
      log.info("WayAllotAction ：data:{},param:{},execContext:{}", data, actionParam, execContext);
      log.info("加入列表,案件信息[{}],参数[{}]", JSON.toJSONString(data), actionParam);
      String key = StringUtils.trimAllWhitespace(actionParam.getString("resultOperationWay"));
      StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
      UserSession session = (UserSession) execContext.get(USER_SESSION);
      if (StringUtils.isEmpty(key) || !Objects.equals(data.getAllotAgentState(), 1)) {
        log.warn("不符合催收手段流转条件");
        saveExecLog(strategyExecLog, data, session, null, actionParam, matchPathNodes);
        return null;
      }
      Integer resultOperationWay = Integer.parseInt(key);
      if (!caseSwitchWayMap.containsKey(resultOperationWay)) {
        AsyncTask switchWayTask = createSwitchWayTask(resultOperationWay, execContext);
        caseSwitchWayMap.put(resultOperationWay, switchWayTask.getId());
      }
      Long asyncTaskId = caseSwitchWayMap.get(resultOperationWay);
      Boolean isMember = stringRedisTemplate.opsForSet().isMember(KeyCache.CASE_PROTECT_EXIST_IDS + session.getOrgId(), data.getId().toString());
      saveExecLog(strategyExecLog, data, session, asyncTaskId, actionParam, matchPathNodes);
      if (!Objects.equals(isMember, Boolean.TRUE)) {
        redisUtil.sSet(KeyCache.CASE_WAY_TASK_CASES + asyncTaskId, 24 * 60 * 60, data.getId().toString());
        stringRedisTemplate.opsForSet().add(KeyCache.CASE_PROTECT_EXIST_IDS + session.getOrgId(), data.getId().toString());
      } else {
        log.info("案件{}当前已经被锁定，无法进行手段流转", data.getId().toString());
      }
      return asyncTaskId;
    } catch (Exception ex) {
      log.error("执行失败:" + data, ex);
    }
    return null;
  }

  @Override
  public String actionName() {
    return "wayAllot";
  }

  @Override
  public void begin(Map<?, ?> execContext) {
    caseSwitchWayMap.clear();
  }

  @Override
  public void commit(Map<?, ?> execContext) {
    try {
      StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
      UserSession session = (UserSession) execContext.get(USER_SESSION);
      for (Map.Entry<Integer, Long> entry : caseSwitchWayMap.entrySet()) {
        Long asyncTaskId = entry.getValue();
        Long size = redisUtil.sGetSize(KeyCache.CASE_WAY_TASK_CASES + asyncTaskId);
        AsyncTask update = new AsyncTask();
        update.setId(asyncTaskId);
        update.setTotal(size);
        asyncTaskService.updateByPrimaryKeySelective(update);
        stringRedisTemplate.opsForList().leftPush(KeyCache.CASE_WAY_TASK_ID_LIST, asyncTaskId.toString());
        saveExecResult(strategyExecLog, session, size.intValue(), (long) entry.getKey(), asyncTaskId);
      }
    } catch (Exception ex) {
      log.error("任务添加失败", ex);
      throw new ApiException("任务添加失败", ex);
    } finally {
      caseSwitchWayMap.clear();
    }
  }

  private AsyncTask createSwitchWayTask(Integer resultOperationWay, Map<?, ?> execContext) {
    UserSession session = (UserSession) execContext.get(USER_SESSION);
    CaseSwitchWayParam param = new CaseSwitchWayParam();
    param.setSwitchType(1);
    param.setResultOperationWays(Arrays.asList(resultOperationWay));
    Long taskId = asyncTaskService.createAsyncTask(param, session, 0L, AsyncTaskEnums.Status.ING.getCode(), AsyncTaskEnums.Type.SWITCH_WAY.getCode());
    return asyncTaskService.selectByPrimaryKey(taskId);
  }
}
