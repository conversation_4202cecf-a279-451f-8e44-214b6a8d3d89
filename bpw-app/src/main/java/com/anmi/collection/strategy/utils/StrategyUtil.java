package com.anmi.collection.strategy.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.anmi.collection.strategy.*;
import com.anmi.collection.strategy.enums.ComparatorValueType;
import com.anmi.collection.strategy.exception.CheckException;

import java.util.*;

/**
 * <AUTHOR>
 */
public class StrategyUtil {
  /**
   * 递归校验前端的树节点是否符合决策树的语法规则
   * 默认从第0级开始校验，递归时层级+1
   *
   * @param decisionNodes 树节点列表
   * @param lvl           层级
   */
  public static boolean checkDecisionTreeNodes(List<DecisionNode> decisionNodes, int lvl) {
    if (CollectionUtil.isEmpty(decisionNodes)) {
      return false;
    }
    if (lvl % 2 == 0) {
      for (DecisionNode node : decisionNodes) {
        if(Objects.equals(node.getType(),"dynoTag")){
          continue;
        }
        if (StrUtil.isBlank(node.getAttr()) && !Objects.equals(node.getIsRuleGroup(),1) && StrUtil.isBlank(node.getAction())) {
          return false;
        }
        if (!CollectionUtil.isEmpty(node.getChildren())) {
          if (!checkDecisionTreeNodes(node.getChildren(), lvl + 1)) {
            return false;
          }
        }
      }
    } else {
      for (DecisionNode node : decisionNodes) {
        if(Objects.equals(node.getType(),"dynoTag")){
          continue;
        }
        if (StrUtil.isBlank(node.getOperator())) {
          return false;
        }
        if (!CollectionUtil.isEmpty(node.getChildren())) {
          if (!checkDecisionTreeNodes(node.getChildren(), lvl + 1)) {
            return false;
          }
        }
      }
    }
    return true;
  }

  /**
   * 交叉递归将前端树结构节点转换成决策树
   *
   * @param decisionNodes 树形节点
   * @param conditions    转换成目标条件
   */
  public static void convertToCondOrActions(List<DecisionNode> decisionNodes, List<DecisionCond> conditions, String currentNodeId) {
    if (CollectionUtil.isEmpty(decisionNodes)) {
      throw new CheckException("数据校验失败,格式不正确");
    }
    for (DecisionNode node : decisionNodes) {
      if(Objects.equals(node.getType(),"dynoTag")){
        continue;
      }
      if (StrUtil.isBlank(node.getOperator())) {
        throw new CheckException("数据校验失败,格式不正确");
      }
      DecisionCond condition = new DecisionCond();
      condition.setOperator(node.getOperator());
      condition.setValue(node.getValue());
      condition.setValueType(Objects.isNull(node.getValueType()) ? ComparatorValueType.STATIC.getCode() : node.getValueType());
      condition.setId(node.getId());
      if (!CollectionUtil.isEmpty(node.getChildren())) {
        List<DecisionAttr> attrs = new ArrayList<>();
        List<DecisionAction> actions = new ArrayList<>();
        convertToAttrsOrActions(node.getChildren(), attrs, actions, condition.getId());
        condition.setActions(actions);
        condition.setAttrs(attrs);
      }
      conditions.add(condition);
    }
  }

  /**
   * 交叉递归将前端树结构节点转换成决策树
   *
   * @param decisionNodes 树形节点
   * @param attrs         属性集合
   * @param actions       动作集合
   */
  public static void convertToAttrsOrActions(List<DecisionNode> decisionNodes, List<DecisionAttr> attrs, List<DecisionAction> actions, String currentNodeId) {
    if (CollectionUtil.isEmpty(decisionNodes)) {
      throw new CheckException("数据校验失败,格式不正确");
    }
    for (DecisionNode node : decisionNodes) {
      if(Objects.equals(node.getType(),"dynoTag")){
        continue;
      }
      if (!StrUtil.isBlank(node.getAction())) {
        DecisionAction action = new DecisionAction();
        action.setAction(node.getAction());
        action.setActionParam(node.getActionParam());
        action.setId(node.getId());
        actions.add(action);
      } else if (!StrUtil.isBlank(node.getAttr()) || Objects.equals(node.getIsRuleGroup(), 1)) {
        DecisionAttr decisionAttr = new DecisionAttr();
        decisionAttr.setAttr(node.getAttr());
        decisionAttr.setRuleGroupId(node.getRuleGroupId());
        decisionAttr.setIsRuleGroup(Objects.equals(node.getIsRuleGroup(),1));
        decisionAttr.setExecAllBranch(node.getExecAllBranch());
        decisionAttr.setId(node.getId());
        if (!CollectionUtil.isEmpty(node.getChildren())) {
          List<DecisionCond> conditions = new ArrayList<>();
          convertToCondOrActions(node.getChildren(), conditions, decisionAttr.getId());
          decisionAttr.setConditions(conditions);
        }
        attrs.add(decisionAttr);
      } else {
        throw new CheckException("数据校验失败,格式不正确");
      }

    }
  }

  public static DecisionTree convertToDecisionTree(List<DecisionNode> decisionNodes) {
    if (CollectionUtil.isEmpty(decisionNodes)) {
      throw new CheckException("数据校验失败,格式不正确");
    }
    DecisionTree decisionTree = new DecisionTree();
    List<DecisionAttr> attrs = new ArrayList<>();
    convertToAttrsOrActions(decisionNodes, attrs, null, null);
    decisionTree.setAttrList(attrs);
    return decisionTree;
  }

  /**
   * 解析决策树里面所有的属性id和名称的关系
   *
   * @return 所有的属性id和名称的关系
   */
  public static Map<String, String> parseDecisionTreeAttrs(DecisionTree decisionTree) {
    Map<String, String> result = new HashMap<>();
    parseDecisionTreeAttrs(decisionTree.getAttrList(), result);
    return result;
  }

  private static void parseDecisionTreeAttrs(List<DecisionAttr> decisionAttrs, Map<String, String> result) {
    if (CollectionUtil.isEmpty(decisionAttrs)) {
      return;
    }
    for (DecisionAttr attr : decisionAttrs) {
      result.put(attr.getId(), attr.getAttr());
      if (!CollectionUtil.isEmpty(attr.getConditions())) {
        for (DecisionCond cond : attr.getConditions()) {
          parseDecisionTreeAttrs(cond.getAttrs(), result);
        }
      }
    }
  }

//  /**
//   * 逆向转换，将决策树交叉递归转换成前端可识别的树结构
//   *
//   * @param decisionTree 决策树
//   * @return 返回前端可展示的树结构
//   */
//  public static List<DecisionNode> convert(DecisionTree decisionTree) {
//    List<DecisionNode> list = new ArrayList<>();
//    DecisionNode decisionNode = new DecisionNode();
//    decisionNode.setId(decisionTree.getId());
//    decisionNode.setLabel(decisionTree.getLabel());
//    decisionNode.setVisited(decisionTree.getVisited());
//    decisionNode.setAttr(decisionTree.getAttr());
//    if (!CollectionUtil.isEmpty(decisionTree.getConditions())) {
//      decisionNode.setChildren(convert(decisionTree.getConditions()));
//    }
//    list.add(decisionNode);
//    return list;
//  }

//  /**
//   * 逆向转换，将决策树交叉递归转换成前端可识别的树结构
//   *
//   * @param conditionList 条件列表
//   * @return 返回前端可展示的树结构
//   */
//  public static List<DecisionNode> convert(List<DecisionCond> conditionList) {
//    List<DecisionNode> list = new ArrayList<>();
//    for (DecisionCond condition : conditionList) {
//      DecisionNode decisionNode = new DecisionNode();
//      decisionNode.setAction(condition.getAction());
//      decisionNode.setOperator(condition.getOperator());
//      decisionNode.setValue(condition.getValue());
//      decisionNode.setActionParam(condition.getActionParam());
//      decisionNode.setVisited(condition.getVisited());
//      decisionNode.setId(condition.getId());
//      decisionNode.setLabel(condition.getLabel());
//      if (!Objects.isNull(condition.getDecision())) {
//        decisionNode.setChildren(convert(condition.getDecision()));
//      }
//      list.add(decisionNode);
//    }
//    return list;
//  }

  //    /**
//     * 测试决策树和节点树互相转换
//     * just for test
//     */
//    @PostConstruct
//    public void test() throws ParseException {
//
//        CaseQueryResult caseInfo = new CaseQueryResult();
//        caseInfo.setAmount(50L);
//        caseInfo.setName("王五");
//        caseInfo.setOverdueDate(new SimpleDateFormat("yyyy-MM-dd").parse("2022-01-01"));
//        caseInfo.setOwnMobile("18762536478");
//
//        String json = "[{\"id\":\"node0\",\"attr\":\"amount\",\"label\":\"金额\",\"children\":[{\"id\":\"node1\",\"operator\":\">=\",\"value\":100,\"action\":\"fifo\",\"label\":\">=\",\"actionParam\":\"fifo-1234\"},{\"id\":\"node2\",\"operator\":\"<\",\"label\":\"<\",\"value\":10,\"action\":\"fifo\",\"actionParam\":\"fifo-小于10\"},{\"id\":\"node3\",\"operator\":\"<\",\"label\":\"<\",\"value\":100,\"children\":[{\"id\":\"node4\",\"attr\":\"name\",\"label\":\"ddddd\",\"children\":[{\"id\":\"node5\",\"operator\":\"contains\",\"value\":\"张三\",\"label\":\"contains\",\"action\":\"sms\",\"actionParam\":\"sms-1234\"},{\"id\":\"node6\",\"operator\":\"contains\",\"label\":\"contains\",\"value\":\"李四\",\"action\":\"sms\",\"actionParam\":\"sms-2345\"},{\"id\":\"node7\",\"operator\":\"=\",\"label\":\"=\",\"value\":\"王五\",\"children\":[{\"id\":\"node8\",\"attr\":\"overdueDate\",\"label\":\"逾期日期\",\"children\":[{\"id\":\"node9\",\"operator\":\"before\",\"value\":\"2023-01-01\",\"action\":\"robot\",\"label\":\"2023-01-01\",\"actionParam\":\"robot-1234\"}]}]}]}]}]}]";
//        List<DecisionNode> decisionNodes = JSON.parseArray(json, DecisionNode.class);
//        log.info("反序列化的节点树:{}", decisionNodes);
//        DecisionTree decisionTree = new DecisionTree();
//        convert(decisionNodes, decisionTree);
//        log.info("节点树转换成决策树:{}", decisionTree);
//        decisionService.testVisitorPath(caseInfo,decisionTree);
//        List<DecisionNode> list = convert(decisionTree);
//        log.info("决策树转换成节点树:{}", list);
//        System.out.println(decisionService.integrityChecking(decisionTree));
//        Map<String,List<String>> actionMap=decisionService.getActionMap(decisionTree);
//
//    }

  public static void main(String[] args) {
    String json = "[{\"id\":\"node4428\",\"attr\":\"orgDeltId\",\"label\":\"委案公司\",\"children\":[{\"x\":410,\"y\":60,\"id\":\"node54313\",\"size\":80,\"type\":\"dyattr\",\"index\":1,\"label\":\"=振兴银行\",\"style\":{\"hover\":{\"stroke\":\"#FF4F1F\",\"lineWidth\":2},\"cursor\":\"pointer\",\"selected\":{\"stroke\":\"#FF4F1F\",\"lineWidth\":2,\"shadowBlur\":10,\"shadowColor\":\"rgba(255, 56, 0, 0.35)\",\"shadowOffsetX\":2,\"shadowOffsetY\":5},\"shadowBlur\":10,\"shadowColor\":\"rgba(203,203,203,.15)\",\"shadowOffsetX\":2,\"shadowOffsetY\":5},\"value\":754,\"_order\":0,\"label_\":\"委案产品\",\"children\":[{\"id\":\"node65032\",\"attr\":\"productId\",\"label\":\"委案产品\",\"children\":[{\"x\":750,\"y\":60,\"id\":\"node98094\",\"size\":80,\"type\":\"dyattr\",\"index\":1,\"label\":\"=振兴银行产品\",\"style\":{\"hover\":{\"stroke\":\"#FF4F1F\",\"lineWidth\":2},\"cursor\":\"pointer\",\"selected\":{\"stroke\":\"#FF4F1F\",\"lineWidth\":2,\"shadowBlur\":10,\"shadowColor\":\"rgba(255, 56, 0, 0.35)\",\"shadowOffsetX\":2,\"shadowOffsetY\":5},\"shadowBlur\":10,\"shadowColor\":\"rgba(203,203,203,.15)\",\"shadowOffsetX\":2,\"shadowOffsetY\":5},\"value\":1654,\"_order\":0,\"action\":\"sms\",\"children\":[{\"id\":\"node57846\",\"over\":true,\"type\":\"dyrobot\",\"label\":\"短信计划\",\"action\":\"sms\",\"trigger\":1,\"parentId\":\"node98094\",\"planLabel\":\"0504测试短信...\",\"actionParam\":90}],\"operator\":\"=\",\"parentId\":\"node65032\",\"planLabel\":\"0504测试短信...\",\"labelValue\":\"振兴银行产品\",\"parentName\":\"委案产品\",\"actionParam\":90,\"layoutOrder\":0}],\"parentId\":\"node54313\"}],\"operator\":\"=\",\"parentId\":\"node4428\",\"labelValue\":\"振兴银行\",\"parentName\":\"委案公司\",\"layoutOrder\":0},{\"x\":410,\"y\":200,\"id\":\"node32497\",\"size\":80,\"type\":\"dyattr\",\"index\":2,\"label\":\"=浦发测试\",\"style\":{\"hover\":{\"stroke\":\"#FF4F1F\",\"lineWidth\":2},\"cursor\":\"pointer\",\"selected\":{\"stroke\":\"#FF4F1F\",\"lineWidth\":2,\"shadowBlur\":10,\"shadowColor\":\"rgba(255, 56, 0, 0.35)\",\"shadowOffsetX\":2,\"shadowOffsetY\":5},\"shadowBlur\":10,\"shadowColor\":\"rgba(203,203,203,.15)\",\"shadowOffsetX\":2,\"shadowOffsetY\":5},\"value\":751,\"_order\":1,\"label_\":\"贷款金额\",\"children\":[{\"id\":\"node8304\",\"attr\":\"amount\",\"label\":\"贷款金额\",\"children\":[{\"x\":750,\"y\":200,\"id\":\"node10265\",\"size\":80,\"type\":\"dyattr\",\"index\":1,\"label\":\">6666\",\"style\":{\"hover\":{\"stroke\":\"#FF4F1F\",\"lineWidth\":2},\"cursor\":\"pointer\",\"selected\":{\"stroke\":\"#FF4F1F\",\"lineWidth\":2,\"shadowBlur\":10,\"shadowColor\":\"rgba(255, 56, 0, 0.35)\",\"shadowOffsetX\":2,\"shadowOffsetY\":5},\"shadowBlur\":10,\"shadowColor\":\"rgba(203,203,203,.15)\",\"shadowOffsetX\":2,\"shadowOffsetY\":5},\"value\":\"6666\",\"_order\":1,\"action\":\"robot\",\"children\":[{\"id\":\"node69063\",\"over\":true,\"type\":\"dyrobot\",\"label\":\"机器人计划\",\"action\":\"robot\",\"trigger\":0,\"parentId\":\"node10265\",\"planLabel\":\"1\",\"actionParam\":89}],\"operator\":\">\",\"parentId\":\"node8304\",\"planLabel\":\"1\",\"labelValue\":\"6666\",\"parentName\":\"贷款金额\",\"actionParam\":89,\"layoutOrder\":0},{\"x\":750,\"y\":340,\"id\":\"node40003\",\"size\":80,\"type\":\"dyattr\",\"index\":2,\"label\":\"<66\",\"style\":{\"hover\":{\"stroke\":\"#FF4F1F\",\"lineWidth\":2},\"cursor\":\"pointer\",\"selected\":{\"stroke\":\"#FF4F1F\",\"lineWidth\":2,\"shadowBlur\":10,\"shadowColor\":\"rgba(255, 56, 0, 0.35)\",\"shadowOffsetX\":2,\"shadowOffsetY\":5},\"shadowBlur\":10,\"shadowColor\":\"rgba(203,203,203,.15)\",\"shadowOffsetX\":2,\"shadowOffsetY\":5},\"value\":\"66\",\"_order\":2,\"action\":\"allot\",\"children\":[{\"id\":\"node43409\",\"over\":true,\"type\":\"dyallot\",\"label\":\"策略分案\",\"action\":\"allot\",\"trigger\":1,\"parentId\":\"node40003\",\"planLabel\":\"分案策略ab\",\"actionParam\":46}],\"operator\":\"<\",\"parentId\":\"node8304\",\"planLabel\":\"分案策略ab\",\"labelValue\":\"66\",\"parentName\":\"贷款金额\",\"actionParam\":46,\"layoutOrder\":0}],\"parentId\":\"node32497\"}],\"operator\":\"=\",\"parentId\":\"node4428\",\"labelValue\":\"浦发测试\",\"parentName\":\"委案公司\",\"layoutOrder\":0}],\"parentId\":\"node0\"}]";
    DecisionTree decisionTree = convertToDecisionTree(JSON.parseArray(json, DecisionNode.class));
    System.out.println(decisionTree);
  }
}
