package com.anmi.collection.strategy.action;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.CasePlanEnums;
import com.anmi.collection.common.enums.CasePlanTemplateEnums;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CasePlanService;
import com.anmi.collection.service.CasePlanTemplateService;
import com.anmi.collection.strategy.CaseStrategyBO;
import com.anmi.collection.utils.SpringContextHolder;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.domain.cases.CasePlan;
import com.anmi.domain.cases.CasePlanTemplate;
import com.anmi.domain.decision.StrategyExecLog;
import com.anmi.domain.decision.StrategyInfo;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public class RobotAction implements AnmiAction<CaseStrategyBO> {

  private final CasePlanTemplateService casePlanTemplateService;
  private final CasePlanService casePlanService;
  private final StringRedisTemplate stringRedisTemplate;

  public RobotAction() {
    this.casePlanTemplateService = SpringContextHolder.getBean(CasePlanTemplateService.class);
    this.casePlanService = SpringContextHolder.getBean(CasePlanService.class);
    this.stringRedisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
  }

  /**
   * 存储模板id和对应的planId
   */
  private final Map<Long, Long> casePlanTemplateMap = new HashMap<>();

  @Override
  public void begin(Map<?, ?> execContext) {
    casePlanTemplateMap.clear();
  }

  @Override
  public Object doAction(CaseStrategyBO data, JSONObject param, Map<?, ?> execContext, Stack<String> matchPathNodes) {
    StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
    UserSession session = (UserSession) execContext.get(USER_SESSION);
    log.info("RobotAction ：data:{},param:{},execContext:{}", data, param, execContext);
    if (CaseEnums.IgnorePlan.YES.getCode().equals(data.getIgnorePlan())) {
      log.info("案件[{}]不参与智能计划,自动跳过", JSON.toJSONString(data));
      saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
      return null;
    }
    // 只有案件状态是分配完成、留案、分配至组才可以创建计划
    List<Integer> caseStatusList =   ImmutableList.of(CaseEnums.CaseStatus.NORMAL.getCode(), CaseEnums.CaseStatus.DELAY.getCode());
    if (!(caseStatusList.contains(data.getCaseStatus()))) {
      log.info("案件[{}]状态不满足机器人智能计划，已过滤掉", JSON.toJSONString(data));
      saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
      return null;
    }

    // 案件催收结果为承诺还款且未到达承诺还款时间,不参与计划
    Date ptpTime = data.getPtpTime();
    if (Objects.nonNull(ptpTime) && ptpTime.after(new Date())) {
      log.info("案件[{}]承诺还款时间未到达，已过滤掉", JSON.toJSONString(data));
      saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
      return null;
    }
    log.info("加入机器人计划,案件信息[{}],参数[{}]", JSON.toJSONString(data), param);
    String key = StringUtils.trimAllWhitespace(param.getString("templateId"));
    if (StringUtils.isEmpty(key)) {
      throw new ApiException("加入机器人计划模板参数不能为空");
    }
    Long templateId = Long.parseLong(key);
    String robotActionCaseLocker = "robot_action_case_locker::";
    String separator = "_";
    Boolean exists = stringRedisTemplate.opsForSet().isMember(robotActionCaseLocker + strategyExecLog.getId(), data.getId() + separator + templateId);
    if (Boolean.TRUE.equals(exists)) {
      log.info("案件[{}]已经由其他分支执行机器人计划，不再继续执行", data.getId());
      saveExecLog(strategyExecLog, data, session, null, param, matchPathNodes);
      return null;
    }
    if (!casePlanTemplateMap.containsKey(templateId)) {
      CasePlan casePlan = createCasePlan(templateId, execContext);
      casePlanTemplateMap.put(templateId, casePlan.getId());
    }
    Long planId = casePlanTemplateMap.get(templateId);
    stringRedisTemplate.opsForSet().add(KeyCache.CASE_PLAN_ROBOT_CASES + planId, data.getId().toString());
    stringRedisTemplate.opsForSet().add(robotActionCaseLocker + strategyExecLog.getId(), data.getId() + separator + templateId);
    stringRedisTemplate.expire(robotActionCaseLocker + strategyExecLog.getId(), 1, TimeUnit.MINUTES);
    saveExecLog(strategyExecLog, data, session, planId, param, matchPathNodes);
    Long size = stringRedisTemplate.opsForSet().size(KeyCache.CASE_PLAN_ROBOT_CASES + planId);
    int robotPlanMaxSize = 50000;
    if (size != null && size >= robotPlanMaxSize) {
      CasePlan update = new CasePlan();
      update.setId(planId);
      update.setSelectedTotal(size);
      casePlanService.updateByPrimaryKeySelective(update);
      stringRedisTemplate.opsForList().leftPush(KeyCache.CASE_PLAN_ROBOT_PLAN_ID_LIST, planId.toString());
      saveExecResult(strategyExecLog, session, size.intValue(), templateId, planId);
      casePlanTemplateMap.remove(templateId);
    }
    return planId;
  }


  @Override
  public String actionName() {
    return "robot";
  }

  private CasePlan createCasePlan(Long planTemplateId, Map<?, ?> execContext) {
    CasePlanTemplate template = casePlanTemplateService.selectByPrimaryKey(planTemplateId);
    if (Objects.isNull(template) || !CasePlanEnums.Type.NEWROBOT.getCode().equals(template.getType())) {
      throw new ApiException("机器人计划模板不存在");
    }
    CasePlan casePlan = convertCasePlanTemplate(template, execContext);
    if (casePlan.getStartType() == 1) {
      if (casePlan.getPlanStartTime() == null) {
        throw new ApiException("定时模式需要传计划开始时间");
      }
      if (casePlan.getPlanStartTime() != null && casePlan.getPlanEndTime() != null) {
        if (casePlan.getPlanStartTime().getTime() > casePlan.getPlanEndTime().getTime()) {
          throw new ApiException("开始时间应该小于结束时间");
        }
      }
    }
    casePlan.setSelectedTotal(0L);
    casePlanService.insertSelective(casePlan);
    return casePlan;
  }

  @Override
  public void commit(Map<?, ?> execContext) {
    try {
      StrategyExecLog strategyExecLog = (StrategyExecLog) execContext.get(STRATEGY_LOG);
      UserSession session = (UserSession) execContext.get(USER_SESSION);
      for (Map.Entry<Long, Long> entry : casePlanTemplateMap.entrySet()) {
        Long planId = entry.getValue();
        Long size = stringRedisTemplate.opsForSet().size(KeyCache.CASE_PLAN_ROBOT_CASES + planId);
        CasePlan update = new CasePlan();
        update.setId(planId);
        update.setSelectedTotal(size);
        casePlanService.updateByPrimaryKeySelective(update);
        stringRedisTemplate.opsForList().leftPush(KeyCache.CASE_PLAN_ROBOT_PLAN_ID_LIST, planId.toString());
        saveExecResult(strategyExecLog, session, size == null ? null : size.intValue(), entry.getKey(), planId);
      }
    } catch (Exception ex) {
      log.error("计划添加失败", ex);
      throw new ApiException("计划添加失败", ex);
    } finally {
      casePlanTemplateMap.clear();
    }
  }

  private CasePlan convertCasePlanTemplate(CasePlanTemplate template, Map<?, ?> execContext) {
    CasePlan casePlan = new CasePlan();
    BeanUtils.copyProperties(template, casePlan);
    casePlan.setId(null);
    casePlan.setStatus(CasePlanEnums.Status.NEW.getCode());
    casePlan.setName("策略生成-" + DateUtil.format(new Date(), "yyyyMMddHHmm"));
    if (CasePlanTemplateEnums.StartType.IMMEDIATELY.getCode().equals(template.getStartType())) {
      casePlan.setIsStartNow(1);
      casePlan.setStartType(1);
      casePlan.setPlanStartTime(new Date());
    }
    if (execContext != null && execContext.containsKey(USER_SESSION)) {
      Long sessionUserId = ((UserSession) execContext.get(USER_SESSION)).getId();
      casePlan.setCreateBy(sessionUserId);
      casePlan.setUpdateBy(sessionUserId);
    } else {
      casePlan.setCreateBy(0L);
      casePlan.setUpdateBy(0L);
    }
    if (execContext != null && execContext.containsKey(STRATEGY_NAME)) {
      casePlan.setName(((StrategyInfo) execContext.get(STRATEGY_NAME)).getName() + "-" + template.getName() + "-" + DateUtil.format(new Date(), "yyyyMMddHHmm"));
    }
    casePlan.setCreateTime(new Date());
    casePlan.setUpdateTime(new Date());
    casePlan.setDesc("策略生成-" + DateUtil.format(new Date(), "yyyyMMddHHmm"));
    return casePlan;
  }
}
