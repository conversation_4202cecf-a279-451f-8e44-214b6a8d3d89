package com.anmi.collection.strategy.comparator;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.anmi.collection.strategy.AttrType;
import com.anmi.collection.strategy.exception.CheckException;

import java.util.List;

/**
 * <AUTHOR>
 */
public class NotInComparator implements OperComparator {
  @Override
  public boolean compare(Object data, String value) {
    if (data == null || StrUtil.isBlank(value)) {
      return false;
    }
    List<String> tmp;
    if (JSONUtil.isJsonArray(value)){
      tmp = JSON.parseArray(value, String.class);
    } else {
      tmp = StrUtil.split(value, ",");
    }
    return !tmp.contains(String.valueOf(data));
  }

  @Override
  public void check(AttrType attrType, String value) throws CheckException {
    if (attrType == AttrType.COLLECTION) {
      throw new CheckException("类型不匹配，不支持集合运算");
    }
    try {
      List<String> tmp = JSON.parseArray(value, String.class);
    } catch (Exception exception) {
      throw new CheckException("集合数据格式不匹配");
    }
  }

  @Override
  public String operator() {
    return "NOT IN";
  }
}
