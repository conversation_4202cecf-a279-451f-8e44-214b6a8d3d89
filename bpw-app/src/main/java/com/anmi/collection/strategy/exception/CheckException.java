package com.anmi.collection.strategy.exception;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 */
public class CheckException extends RuntimeException {

  public CheckException() {
    super();
  }

  public CheckException(String msg) {
    super(msg);
  }

  public CheckException(String pattern, Object... arguments) {
    super(MessageFormat.format(pattern, arguments));
  }

  public CheckException(String message, Throwable cause) {
    super(message, cause);
  }

  public CheckException(String pattern, Throwable cause, Object... arguments) {
    super(MessageFormat.format(pattern, arguments), cause);
  }
}
