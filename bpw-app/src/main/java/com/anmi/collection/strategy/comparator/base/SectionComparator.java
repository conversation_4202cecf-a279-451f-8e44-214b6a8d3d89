package com.anmi.collection.strategy.comparator.base;

import cn.hutool.core.util.NumberUtil;
import com.anmi.collection.strategy.AttrType;
import com.anmi.collection.strategy.comparator.OperComparator;
import com.anmi.collection.strategy.exception.CheckException;

/** <AUTHOR> */
public abstract class SectionComparator implements OperComparator {

  @Override
  public void check(AttrType type, String value) throws CheckException {
    if (type != AttrType.DATE && type != AttrType.NUMBER) {
      throw new CheckException("区间条件仅使用于数字或日期");
    }
    String[] values = value.split(",");
    if (values.length != 2) {
      throw new CheckException("数据格式不匹配");
    }
    if (!NumberUtil.isNumber(values[0]) || !NumberUtil.isNumber(values[1])) {
      throw new CheckException("数据格式不匹配");
    }
  }
}
