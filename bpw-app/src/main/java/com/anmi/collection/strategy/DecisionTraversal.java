package com.anmi.collection.strategy;

import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.strategy.bo.ExecResult;
import com.anmi.collection.strategy.bo.ExecStatistic;
import com.anmi.collection.strategy.bo.TestResult;
import com.anmi.collection.strategy.bo.TestStatistic;

import java.util.List;
import java.util.Map;
import java.util.Stack;

/** <AUTHOR> */
public interface DecisionTraversal<T extends MetaInfo> {

  /**
   * 批量执行决策树
   *
   * @param dataList 实体列表
   * @param decisionTree 决策树
   * @param execContext 上下文
   */
  void executeList(List<T> dataList, DecisionTree decisionTree, Map<?, ?> execContext, Map<String, ExecStatistic> nodeExecStatistic);

  /**
   * 单条数据执行决策树
   *
   * @param data 数据实体
   * @param decisionTree 决策树
   * @param execContext 上下文
   */
  ExecResult execute(T data, DecisionTree decisionTree, Map<?, ?> execContext, Stack<String> matchPathNodes, Map<String, ExecStatistic> nodeExecStatistic);

  /**
   * 检验决策树的合规性
   *
   * @param decisionTree 决策树
   */
  void check(DecisionTree decisionTree,Map<String,AttrType> attrTypeMap);

  /**
   * 决策树完整性测试，如果叶子节点没有具体的action则认为决策树不完整
   *
   * @param decisionTree 决策树
   * @return 如果决策树完整，则返回true，否则返回false
   */
  boolean integrityChecking(DecisionTree decisionTree);

  /**
   * 获取决策树的action列表，以及action参数列表，用于遍历查询决策树中使用了哪些动作，及动作的参数
   *
   * @param decisionTree 决策树
   * @return 返回值map中的key为actionName，value为决策树中某个action的所有参数
   */
  Map<String, List<JSONObject>> getActionMap(DecisionTree decisionTree);

  /**
   * 获取决策树的属性列表，以及每个属性的引用次数
   * @param decisionTree 决策树
   * @return 返回map对象，其中的key为属性名称，value为该属性在决策树中出现的次数
   */
  Map<String, Integer> getAttrMap(DecisionTree decisionTree);

  /**
   * 获取决策树的依赖规则组列表，以及每个规则组的引用次数
   * @param decisionTree 决策树
   * @return 返回map对象，其中的key为规则组id，value为该规则组id在决策树中出现的次数
   */
  Map<Long, Integer> getRuleGroupMap(DecisionTree decisionTree);

  /**
   * 决策树测试
   * @param testData 测试数据
   * @param decisionTree 决策树
   * @return 返回决策树的每个节点id
   */
  TestResult test(T testData, DecisionTree decisionTree,Map<String, TestStatistic> strategyTestStatisticMap);

}
