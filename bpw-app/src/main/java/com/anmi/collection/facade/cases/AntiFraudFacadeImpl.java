package com.anmi.collection.facade.cases;

import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.encrypt.EncryptProperties;
import com.anmi.collection.encrypt.EncryptService;
import com.anmi.collection.entity.requset.cases.AntiFraudParam;
import com.anmi.collection.entity.requset.cases.AntiFraudRemarkParam;
import com.anmi.collection.entity.requset.query.cases.AntiFraudQuery;
import com.anmi.collection.entity.response.cases.AntiFraudRemarkVO;
import com.anmi.collection.entity.response.cases.AntiFraudVO;
import com.anmi.collection.service.AntiFraudRemarkService;
import com.anmi.collection.service.AntiFraudService;
import com.anmi.collection.utils.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/13 21:11
 */
@RestController
public class AntiFraudFacadeImpl implements AntiFraudFacade{
    @Resource private AntiFraudService antiFraudService;
    @Resource private AntiFraudRemarkService antiFraudRemarkService;
    @Resource private EncryptProperties encryptProperties;
    @Resource private EncryptService encryptService;


    @Override
    public ResultMessage addAntiFraudApply(AntiFraudParam param) throws Exception {
        antiFraudService.addAntiFraudApply(param);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage auditAntiFraud(AntiFraudParam param) throws Exception{
        antiFraudService.auditAntiFraud(param);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage cancelAntiFraud(Long id) {
        antiFraudService.cancelAntiFraud(id);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<PageOutput<AntiFraudVO>> getList(AntiFraudQuery query) {
        encryptQueryData(query);
        PageOutput<AntiFraudVO> result = antiFraudService.getList(query);
        return ResultMessage.success(result);
    }

    @Override
    public ResultMessage addAntiFraudRemark(AntiFraudRemarkParam param) {
        antiFraudRemarkService.addRemark(param);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage deleteAntiFraudRemark(Long id) {
        antiFraudRemarkService.deleteRemark(id);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<List<AntiFraudRemarkVO>> getAntiFraudRemarkList(Long antiFraudId) {
        List<AntiFraudRemarkVO> list = antiFraudRemarkService.getList(antiFraudId);
        return ResultMessage.success(list);
    }

    void encryptQueryData(AntiFraudQuery query) {
        if (!encryptProperties.getEnable()) {
            return;
        }
        List<String> names = query.getNames();
        if (!CollectionUtils.isEmpty(names)) {
            query.setNames(names.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
        }
        List<String> idCards = query.getIdCards();
        if (!CollectionUtils.isEmpty(idCards)) {
            query.setIdCards(idCards.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
        }
        List<String> mobiles = query.getMobiles();
        if (!CollectionUtils.isEmpty(mobiles)) {
            query.setMobiles(mobiles.stream().map(s -> encryptService.encrypt(s)).collect(Collectors.toList()));
        }
    }

}
