package com.anmi.collection.facade.wp;

import cn.duyan.thread.DuyanThreadExecutor;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.entity.requset.cases.CaseOperationOperate;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.requset.wp.*;
import com.anmi.collection.entity.response.sys.dep.DepVO;
import com.anmi.collection.entity.response.sys.user.UserVO;
import com.anmi.collection.entity.response.wp.*;
import com.anmi.collection.service.wp.*;
import com.anmi.collection.utils.dict.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/24 14:39
 */
@RestController
@Slf4j
public class WorkPhoneFacadeImpl implements WorkPhoneFacade {
    @Resource
    private UserWorkPhoneService userWorkPhoneService;
    @Resource
    private WpWechatTemplateService wpWechatTemplateService;
    @Resource
    private WpSmsTemplateService wpSmsTemplateService;
    @Resource
    private WpSmsSendService wpSmsSendService;
    @Resource
    private WpWechatService wpWechatService;
    @Resource
    private WorkPhoneDeviceService workPhoneDeviceService;

    private DuyanThreadExecutor threadExecutor=new DuyanThreadExecutor("anmi-work-phone-callback");



    @Override
    public ResultMessage<PageOutput<UserWorkPhoneVO>> getAgentList(UserWorkPhoneQuery query) {
        PageOutput<UserWorkPhoneVO> agentList = userWorkPhoneService.getAgentList(query);
        return ResultMessage.success(agentList);
    }

    @Override
    public ResultMessage<PageOutput<UserVO>> getUserList(UserWorkPhoneQuery query) {
        PageOutput<UserVO> userList = userWorkPhoneService.getUserList(query);
        return ResultMessage.success(userList);
    }

    @Override
    public ResultMessage addUserWorkPhone(List<Long> userIds) {
        userWorkPhoneService.addUserWorkPhone(userIds);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage refreshStatus() {
        UserSession userSession = UserUtils.getTokenUser();
        userWorkPhoneService.refreshStatus(userSession);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage delUserWorkPhone(List<Long> ids) {
        userWorkPhoneService.delUserWorkPhone(ids);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<String> callByWorkPhone(CaseOperationOperate operate) {
        String uuid = userWorkPhoneService.callByWorkPhone(operate);
        return ResultMessage.success(uuid);
    }

    @Override
    public ResultMessage workPhoneCallback(List<WpCallback> wpCallbackList) {
        log.info("工作手机通话记录推送：{}", wpCallbackList);
        threadExecutor.execute(() -> {
            userWorkPhoneService.workPhoneCallback(wpCallbackList);
        });

        return ResultMessage.success();
    }

    @Override
    public ResultMessage<String> getVoiceUrlByUuid(String uuid) {
        UserSession userSession = UserUtils.getTokenUser();
        String url = userWorkPhoneService.getVoiceUrlByUuid(uuid, userSession);
        return ResultMessage.success(url);
    }


    @Override
    public ResultMessage<?> editWorkPhoneAuth(WorkPhoneAuthParam workPhoneAuthParam) {
        userWorkPhoneService.editWorkPhoneAuth(workPhoneAuthParam);
        return ResultMessage.success();
    }


    @Override
    public ResultMessage<?> addWechatTemplate(WechatTemplateParam param) {
        wpWechatTemplateService.addWechatTemplate(param);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<?> delWechatTemplate(Long id) {
        wpWechatTemplateService.delWechatTemplate(id);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<?> editWechatTemplate(WechatTemplateParam param) {
        wpWechatTemplateService.editWechatTemplate(param);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<?> wechatTemplateBatchAuth(WechatTemplateAuthParam param) {
        wpWechatTemplateService.batchAuth(param);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<List<DepVO>> queryAuthList(WpTemplateParam param) {
        List<DepVO> vos = new ArrayList<>();
        if (Objects.equals(param.getType(), 1)) {
            // 短信话术
            vos = wpSmsTemplateService.queryAuthList(param);
        } else {
            // 微信话术
            vos = wpWechatTemplateService.queryAuthList(param);
        }
        return ResultMessage.success(vos);
    }

    @Override
    public ResultMessage<PageOutput<WpWechatTemplateVO>> queryWechatTemplateByPage(PageParam pageParam) {
        return ResultMessage.success(wpWechatTemplateService.queryWechatTemplateByPage(pageParam));
    }

    @Override
    public ResultMessage<List<WpWechatTemplateVO>> queryWechatTemplateList() {
        return ResultMessage.success(wpWechatTemplateService.queryWechatTemplateList());
    }

    @Override
    public ResultMessage<?> addSmsTemplate(SmsTemplateParam param) {
        wpSmsTemplateService.addSmsTemplate(param);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<?> delSmsTemplate(Long id) {
        wpSmsTemplateService.delSmsTemplate(id);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<?> editSmsTemplate(SmsTemplateParam param) {
        wpSmsTemplateService.editSmsTemplate(param);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<?> smsTemplateBatchAuth(SmsTemplateAuthParam param){
        wpSmsTemplateService.batchAuth(param);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<PageOutput<WpSmsTemplateVO>> querySmsTemplateByPage(PageParam pageParam) {
        return ResultMessage.success(wpSmsTemplateService.querySmsTemplateByPage(pageParam));
    }

    @Override
    public ResultMessage<List<WpSmsTemplateVO>> querySmsTemplateList() {
        return ResultMessage.success(wpSmsTemplateService.querySmsTemplateList());
    }


    @Override
    public ResultMessage<?> sendSms(@RequestBody @Validated WpSmsSendParam param) {
        wpSmsSendService.sendSmsByWorkPhone(param);
        return ResultMessage.success();
    }


    @Override
    public ResultMessage<PageOutput<WpSmsInfoVO>> smsListByPage(WpSmsListParam wpSmsListParam) {
        return ResultMessage.success(wpSmsSendService.querySmsListByPage(wpSmsListParam));
    }

    @Override
    public ResultMessage<PageOutput<WpWechatInfoVO>> wechatListByPage(WpWechatListParam wpWechatListParam){
        return ResultMessage.success(wpWechatService.queryWechatListByPage(wpWechatListParam));
    }

    @Override
    public ResultMessage<?> addContactWechat(AddContactWechatParam param) {
        wpWechatService.addContactWechat(param);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<WorkPhoneDeviceVO> queryUserBindPhone(Long userId) {
        return ResultMessage.success(workPhoneDeviceService.queryUserBindPhone(userId));
    }

    @Override
    public ResultMessage<List<UserWorkPhoneVO>> queryUserBindWx(Long userId) {
        return ResultMessage.success(userWorkPhoneService.queryUserBindWx(userId));
    }

}
