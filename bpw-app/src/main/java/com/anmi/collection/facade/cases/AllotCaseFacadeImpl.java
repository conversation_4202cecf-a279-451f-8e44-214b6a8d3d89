package com.anmi.collection.facade.cases;

import com.anmi.collection.base.BaseService;
import com.anmi.collection.common.BeanUtil;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.common.enums.AllotCaseEnums;
import com.anmi.collection.entity.requset.cases.AdjustCaseParam;
import com.anmi.collection.entity.requset.cases.AllotCase;
import com.anmi.collection.entity.response.cases.AllotCaseResultVO;
import com.anmi.collection.entity.response.cases.AllotCaseVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.facade.BaseActionFacade;
import com.anmi.collection.service.AllotCaseService;
import com.anmi.collection.service.UserService;
import com.anmi.collection.service.businessobject.AllotCaseBO;
import com.anmi.collection.service.businessobject.AllotCaseResultListBO;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseAllot;
import com.anmi.domain.user.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
public class AllotCaseFacadeImpl extends BaseActionFacade<CaseAllot> implements AllotCaseFacade {
    private static final Logger LOGGER = LoggerFactory.getLogger(AllotCaseFacadeImpl.class);

    @Autowired
    private AllotCaseService allotCaseService;
    @Autowired
    private UserService userService;

    @Override
    public ResultMessage adjustCase(AdjustCaseParam param) throws Exception {
        if (null == param) {
            throw new ApiException("参数不合法,请联系管理员");
        }
        if (param.getToOperator() != null) {
            User user = userService.selectByPrimaryKey(param.getToOperator());
            if (user == null || user.getStatus() != User.Status.NORMAL.getCode() || user.getTeamId() == null) {
                throw new ApiException("所选催员状态异常，请刷新后重试");
            }
            param.setTeamId(user.getTeamId());
        }
        if (null == param.getTeamId()) {
            throw new ApiException("参数不合法,请联系管理员");
        }

        return allotCaseService.adjust(param);
    }

    @Override
    public ResultMessage<List<AllotCaseVO>> allotSubmit(@RequestBody AllotCase allotCase) throws Exception {
        allotCaseService.allotSubmit(allotCase);
        return ResultMessage.success();
    }

    @Override
    public ResultMessage<AllotCaseResultVO> preview(@RequestBody AllotCase allotCase) throws Exception {
        allotCase.setSelectField("ca.id,ca.debt_id,ca.amount,ca.allot_status,ca.case_status");
        List<Case> caseList = allotCaseService.selectByAllot(allotCase, allotCase.getAllSelect());
        caseList = allotCaseService.filterCaseByAllot(caseList,allotCase);
        if (allotCase.getCaseCountLimit() != null) {
            if (caseList.size() < allotCase.getCaseCountLimit()) {
                throw new ApiException("输入的数量不可大于已选案件数量");
            }
            caseList = caseList.subList(0, allotCase.getCaseCountLimit().intValue());
        }

        AllotCaseResultListBO allotCaseResultListBO = allotCaseService.preAllot(allotCase, caseList);
        //按催员顺序包装数据
        List<AllotCaseBO> allotCaseBOS = allotCaseResultListBO.getAllotCaseBOS();
        List<AllotCaseBO> conjointBOS = allotCaseResultListBO.getConjointBOS();
        // 比例分配的key，有可能是userId或者teamId
        Map<Long, AllotCaseBO> map = new HashMap<>();
        if(allotCaseBOS.size()>0) {
            if (allotCaseBOS.get(0).getAllotToTeamMembers()) {
                // 允许分配到催员的话，以userId为分组
                map = allotCaseBOS.stream().collect(Collectors.toMap(AllotCaseBO::getUserId, r -> r));
            } else if (allotCase.getAllotObject() == 2 || allotCase.getAllotObject() == 3) {
                //分配至分公司或者委外机构
                map = allotCaseBOS.stream().collect(Collectors.toMap(AllotCaseBO::getDepId, r -> r));
            } else {
                // 不允许分配到催员的话，以teamId为分组
                map = allotCaseBOS.stream().collect(Collectors.toMap(AllotCaseBO::getTeamId, r -> r));
            }
        }
        // 共债案件可以对userId进行分组
        Map<Long, AllotCaseBO> map2 = conjointBOS.stream().collect(Collectors.toMap(AllotCaseBO::getUserId, r -> r));
        AllotCaseResultVO allotCaseResultVO = new AllotCaseResultVO();
        List<AllotCaseVO> allotCaseVOList = new ArrayList<>();
        List<AllotCaseVO> conjointVOList = new ArrayList<>();
        for (Long targetId : map.keySet()) {
            AllotCaseBO allotCaseBO = map.get(targetId);
            AllotCaseVO allotCaseVO = BeanUtil.copyProperties(allotCaseBO, AllotCaseVO.class);
            allotCaseVOList.add(allotCaseVO);
        }
        for (Long targetId : map2.keySet()) {
            AllotCaseBO allotCaseBO = map2.get(targetId);
            AllotCaseVO allotCaseVO = BeanUtil.copyProperties(allotCaseBO, AllotCaseVO.class);
            conjointVOList.add(allotCaseVO);
        }
        allotCaseResultVO.setAllotCaseVOS(allotCaseVOList);
        allotCaseResultVO.setConjointVOS(conjointVOList);
        allotCaseResultVO.setTotal(allotCaseBOS.stream().mapToInt(AllotCaseBO::getCaseCount).sum()
                + conjointBOS.stream().mapToInt(AllotCaseBO::getCaseCount).sum());
        return ResultMessage.success(allotCaseResultVO);
    }

    @Override
    public ResultMessage autoAllotCaseByCallIn(Long caseId) throws Exception {
        allotCaseService.autoAllotCaseByCallIn(caseId);
        return ResultMessage.success();
    }

    @Override
    public Logger getLog() {
        return LOGGER;
    }

    @Override
    public BaseService<CaseAllot> getBaseService() {
        return this.allotCaseService;
    }

    @Override
    public String getTableName() {
        return "allot_case";
    }
}
