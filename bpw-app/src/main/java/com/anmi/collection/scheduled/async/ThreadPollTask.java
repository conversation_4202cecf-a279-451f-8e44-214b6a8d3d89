package com.anmi.collection.scheduled.async;

import java.util.Map;

public abstract class ThreadPollTask {

    private Map<String,String> contextMap;

    public abstract void run();

    public void successCallback() { }

    public void failCallback(Exception ex) {
        ex.printStackTrace();
    }

    public Map<String, String> getContextMap() {
        return contextMap;
    }

    public void setContextMap(Map<String, String> contextMap) {
        this.contextMap = contextMap;
    }

}
