package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 空间不足邮件发送
 *
 * <AUTHOR>
 * @date 2022/11/24
 */
public class SpaceLessMailSendJob extends BaseTask {

  /**
   * @Scheduled(cron = "0 0 10 * * ?")
   */
  @XxlJob("spaceLessMailSendHandler")
  public ReturnT<String> spaceLessMailSendHandler() {
    super.autoSendSpaceLessMail();
    return ReturnT.SUCCESS;
  }
}
