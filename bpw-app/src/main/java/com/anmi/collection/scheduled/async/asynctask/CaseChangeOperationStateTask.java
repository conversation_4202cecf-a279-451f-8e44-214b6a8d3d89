package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.CaseLogEnums;
import com.anmi.collection.entity.requset.cases.ChangeOperationStateParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseService;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseQueryResult;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class CaseChangeOperationStateTask extends BaseAsyncTask {

  @Autowired private CaseService caseService;
  @Autowired private CaseMapper caseMapper;
  @Autowired private ApplicationContext applicationContext;

  @XxlJob("caseChangeOperationStateHandler")
  public ReturnT<String> caseChangeOperationStateHandler() {
    try {
      startAsyncTask(KeyCache.CASE_CHANGE_OPERATION_STATE_TASK_ID_LIST);
    } catch (Exception e) {
      log.error("调整案件催收进程出现全局异常！", e);
    }
    return ReturnT.SUCCESS;
  }

  @Override
  protected void execute(Long taskId) {
    AsyncTask task = asyncTaskService.selectByPrimaryKey(taskId);
    task.setSuccessAmt(0L);
    try {
      String queryString = task.getFieldJson().get("query");
      ChangeOperationStateParam param = JsonUtils.fromJson(queryString, ChangeOperationStateParam.class);
      assert param != null;
      param.setOrgIds(task.getOrgId().toString());
      List<CaseQueryResult> list = null;
      if (systemConfig.getESSwitch()) {
        list = caseService.getAllCasesUsingEs(param);
      } else {
        list = caseService.queryResultForMulti(param);
      }
      List<Long> caseIds = list.stream().map(CaseQueryResult::getId).collect(Collectors.toList());
      //同步同一委案公司下的共债案件的催收进程
      assert param != null;
      List<Long> allCaseList = new ArrayList<>();
      assert param != null;
      if (param.getSyncConjoint() != null && param.getSyncConjoint() && caseIds.size() > 0) {
        List<Long> conjointList = caseMapper.selectConjointByCaseIds(caseIds);
        if (conjointList.size() > 0) {
          allCaseList.addAll(conjointList);
        }
      }
      caseIds.forEach(caseId->{
        if (!allCaseList.contains(caseId)) {
          allCaseList.add(caseId);
        }
      });
      List<Case> caseList = caseService.selectByIdList(allCaseList, Case.class);
      if (allCaseList.size() > 0) {
        Case caseInfo = new Case();
        caseInfo.setOperationState(param.getOperationState());
        caseInfo.setUpdateBy(task.getCreateBy());
        caseInfo.setUpdateTime(new Date());
        Example example = new Example(Case.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", allCaseList);
        caseService.updateByExampleSelective(caseInfo, example);
      }
      task.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
      task.setTotal((long) allCaseList.size());
      task.setSuccessAmt((long) allCaseList.size());
      asyncTaskService.updateByPrimaryKeySelective(task);
      if(!CommonUtils.isEmpty(caseList)){
        caseList.forEach(item->{item.setOperationState(param.getOperationState());});
        String userSessionStr=task.getFieldJson().get("userSession");
        UserSession userSession=JsonUtils.fromJson(userSessionStr, UserSession.class);
        CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
        caseBatchUpdateEvent.setType(CaseLogEnums.Type.CHANGE_OPERATION_STATE.getCode());
        caseBatchUpdateEvent.setUserSession(userSession);
        caseBatchUpdateEvent.setCases(caseList);
        caseBatchUpdateEvent.setTaskId(taskId);
        applicationContext.publishEvent(caseBatchUpdateEvent);
      }
    } catch (Exception e) {
      log.error("调整案件催收进程,taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
      // 任务失败
      asyncTaskService.updateTaskFinish(task, e.getMessage());
    } finally {
      redisUtil.lRemove(KeyCache.CASE_CHANGE_OPERATION_STATE_TASK_ID_LIST, 0, taskId.toString());
    }
  }
}
