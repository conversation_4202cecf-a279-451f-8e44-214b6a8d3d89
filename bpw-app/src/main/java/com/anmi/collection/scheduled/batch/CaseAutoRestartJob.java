package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/24 14:57
 */
public class CaseAutoRestartJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 30 0 * * ?")
     *
     * @throws InterruptedException 中断异常
     */
    @XxlJob("caseAutoRestartHandler")
    public ReturnT<String> caseAutoRestartHandler() throws InterruptedException {
        super.caseAutoRestart();
        return ReturnT.SUCCESS;
    }
}
