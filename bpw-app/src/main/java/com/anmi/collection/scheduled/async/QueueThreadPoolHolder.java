package com.anmi.collection.scheduled.async;

import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.LogbackUtil;
import org.slf4j.MDC;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedDeque;

/**
 * 自定义线程池 Holder
 *
 * <AUTHOR>
 * @date 2023/04/12
 */
public class QueueThreadPoolHolder {

    /**
     * 默认 线程池
     */
    private QueueThreadPool defaultThreadPool;
    /**
     * 大数据量情况 使用的 线程池
     */
    private QueueThreadPool bigDataThreadPool;
    /**
     * 大数据量阈值，大于此值切换为 bigDataThreadPool
     */
    private Integer threshold;

    /**
     * QueueThreadPoolHolder
     *
     * @param threadName      池名称
     * @param queueCapacity 队列容量
     */
    public QueueThreadPoolHolder(String threadName, Integer queueCapacity) {
        this(threadName,queueCapacity,10000);
    }

    public QueueThreadPoolHolder(String threadName, Integer queueCapacity, Integer threshold) {
        AssertUtil.notBlank(threadName,"池名称不能为空");
        AssertUtil.isTrue(ObjectUtil.isNotNull(queueCapacity) && queueCapacity > 0,"队列容量必须为大于0的正整数");
        AssertUtil.isTrue(ObjectUtil.isNotNull(threshold) && threshold > 1,"阈值必须为大于1的正整数");

        QueueThreadPool defaultThreadPool = new QueueThreadPool(threadName, queueCapacity);
        QueueThreadPool bigDataThreadPool = new QueueThreadPool(threadName+"_BigData", queueCapacity);
        this.defaultThreadPool = defaultThreadPool;
        this.bigDataThreadPool = bigDataThreadPool;
        this.threshold = threshold;
    }

    /**
     * 尝试提交（根据预估数据量，阶梯式处理）
     *
     * @param task       任务
     * @param dataVolume 预估数据量
     * @return boolean
     */
    public boolean trySubmit(ThreadPollTask task, Integer dataVolume) {
        QueueThreadPool choice = defaultThreadPool;
        if (ObjectUtil.isNotNull(bigDataThreadPool) && ObjectUtil.isNotNull(dataVolume) && dataVolume > threshold){
            choice = bigDataThreadPool;
        }
        return choice.trySubmit(task);
    }

    /**
     * <AUTHOR>
     */
    public static class QueueThreadPool {
        /**
         * 队列容量
         */
        private int queueCapacity;
        private QueueThread queueThread;

        public QueueThreadPool(String threadName, int queueCapacity) {
            this.queueThread = new QueueThread(threadName);
            this.queueCapacity = queueCapacity;
            this.queueThread.start();
        }

        private int getUndoTaskCount() {
            return this.queueThread.getUndoTaskCount();
        }

        public boolean trySubmit(ThreadPollTask task) {
            if (getUndoTaskCount() >= this.queueCapacity) {
                return false;
            }
            task.setContextMap(MDC.getCopyOfContextMap());
            this.queueThread.submit(task);
            return true;
        }

        class QueueThread extends Thread {

            private final Object locker = new Object();

            private final Queue<ThreadPollTask> queue = new ConcurrentLinkedDeque<>();

            public int getUndoTaskCount() {
                return this.queue.size();
            }

            public QueueThread(String threadName){
                setName(threadName);
            }

            @Override
            public void run() {
                while (true) {
                    while (!this.queue.isEmpty()) {
                        ThreadPollTask task = this.queue.poll();
                        if (task != null) {
                            try {
                                if (task.getContextMap() != null) {
                                    MDC.setContextMap(task.getContextMap());
                                }
                                LogbackUtil.getTrace();
                                task.run();
                                task.successCallback();
                            } catch (Exception ex) {
                                task.failCallback(ex);
                            } finally {
                                MDC.clear();
                            }
                        }
                    }
                    synchronized (locker) {
                        try {
                            locker.wait();
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }

            public void submit(ThreadPollTask task) {
                this.queue.add(task);
                synchronized (locker) {
                    locker.notify();
                }
            }
        }
    }
}
