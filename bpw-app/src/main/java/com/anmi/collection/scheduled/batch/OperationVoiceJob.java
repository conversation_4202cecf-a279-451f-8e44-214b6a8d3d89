package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.transaction.annotation.Transactional;

/**
 * 催记录音回填补偿
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class OperationVoiceJob extends BaseTask {

    /**
     * @Scheduled(fixedDelay = 10*60*1000)
     */
    @Transactional(rollbackFor = Exception.class)
    @XxlJob("operationVoiceHandler")
    public ReturnT<String> operationVoiceHandler() {
        super.handleCaseOperationVoice();
        return ReturnT.SUCCESS;
    }
}
