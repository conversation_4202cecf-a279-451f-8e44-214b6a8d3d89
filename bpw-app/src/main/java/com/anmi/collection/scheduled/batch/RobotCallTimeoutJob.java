package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.transaction.annotation.Transactional;

/**
 * 定时处理未回调的催记
 */
public class RobotCallTimeoutJob extends BaseTask {

  /**
   * @Scheduled(cron = "0/10 * * * * ? ")
   */
  @Transactional(rollbackFor = Exception.class)
  @XxlJob("robotCallTimeoutTaskHandler")
  public ReturnT<String> robotCallTimeoutJob() {
    super.robotCallTimeout();
    return ReturnT.SUCCESS;
  }
}
