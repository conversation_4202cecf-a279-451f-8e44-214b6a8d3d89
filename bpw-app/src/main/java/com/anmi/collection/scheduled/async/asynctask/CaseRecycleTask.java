package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.CaseLogEnums;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.domain.cases.AsyncTask;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 回收案件异步任务
 *
 * <AUTHOR>
 * @date 2023/06/14
 */
@Slf4j
public class CaseRecycleTask extends BaseAsyncTask {

  @Resource private CaseMapper caseMapper;
  @Resource private ApplicationContext applicationContext;

  @XxlJob("caseRecycleHandler")
  public ReturnT<String> caseRecycleHandler() {
    try {
      startAsyncTask(KeyCache.CASE_RECYCLE_TASK_ID_LIST);
    } catch (Exception e) {
      log.error("回收案件出现全局异常！", e);
    }
    return ReturnT.SUCCESS;
  }

  @Override
  protected void execute(Long taskId) {
    AsyncTask asyncTask = asyncTaskService.selectByPrimaryKey(taskId);
    asyncTask.setSuccessAmt(0L);
    Set<Long> caseIds = redisUtil.sGet(KeyCache.CASE_RECYCLE_TASK_CASES + taskId).stream().map(Long::parseLong).collect(Collectors.toSet());
    try {
      String sessionUserStr = asyncTask.getFieldJson().get("userSession");
      UserSession userSession = JsonUtils.fromJson(sessionUserStr, UserSession.class);
      Assert.notNull(userSession, "userSession不能为空");

      List<List<Long>> caseIdLists = CollUtil.split(caseIds, 2000);
      caseIdLists.forEach(caseIdList->{
        CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
        caseBatchUpdateEvent.setType(CaseLogEnums.Type.RECYCLE.getCode());
        caseBatchUpdateEvent.setUserSession(userSession);
        caseBatchUpdateEvent.setCaseIds(caseIdList);
        caseBatchUpdateEvent.setTaskId(taskId);
        applicationContext.publishEvent(caseBatchUpdateEvent);

        caseMapper.recycleCase(caseIdList);

        asyncTask.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
        asyncTask.setSuccessAmt(asyncTask.getSuccessAmt() + caseIdList.size());
        asyncTaskService.updateByPrimaryKeySelective(asyncTask);
      });
    } catch (Exception e) {
      log.error("回收案件,taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
      asyncTaskService.updateTaskFinish(asyncTask, e.getMessage());
    } finally {
      redisUtil.lRemove(KeyCache.CASE_RECYCLE_TASK_ID_LIST, 0, taskId.toString());
      redisUtil.sRemove(KeyCache.CASE_PROTECT_EXIST_IDS+asyncTask.getOrgId(),caseIds.stream().map(String::valueOf).toArray(String[]::new));
      redisUtil.del(KeyCache.CASE_RECYCLE_TASK_CASES+taskId);
    }
  }
}
