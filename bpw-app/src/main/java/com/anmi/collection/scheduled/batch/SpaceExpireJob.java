package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 存储空间过期
 *
 * <AUTHOR>
 * @date 2022/11/24
 */
public class SpaceExpireJob extends BaseTask {

  /**
   * @Scheduled(cron = "0 30 2 * * ?")
   */
  @XxlJob("spaceExpireHandler")
  public ReturnT<String> spaceExpireHandler() {
    super.spaceExpire();
    return ReturnT.SUCCESS;
  }
}
