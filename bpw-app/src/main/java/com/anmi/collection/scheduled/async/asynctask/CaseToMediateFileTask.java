package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.common.enums.flow.ApplyEnums;
import com.anmi.collection.dto.CaseDTO;
import com.anmi.collection.entity.requset.mediate.CreateMediateLetterParam;
import com.anmi.collection.entity.requset.mediate.MediateAuditParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseService;
import com.anmi.collection.service.letter.LetterSignerService;
import com.anmi.collection.service.mediate.LetterMediateService;
import com.anmi.collection.service.mediate.MediateAuditService;
import com.anmi.collection.utils.AssertUtil;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.letter.LetterSigner;
import com.anmi.domain.mediate.LetterMediate;
import com.anmi.domain.mediate.MediateAudit;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CaseToMediateFileTask extends BaseAsyncTask {

    @Resource private CaseService caseService;
    @Resource private LetterMediateService letterMediateService;
    @Resource private LetterSignerService letterSignerService;
    @Resource private MediateAuditService mediateAuditService;
    @Resource private ApplicationContext applicationContext;

    @XxlJob("caseToMediateFileHandler")
    public ReturnT<String> caseToMediateFileHandler() {
        try {
            startAsyncTask(KeyCache.CASE_ADD_TO_MEDIATE_TASK_ID_LIST);
        } catch (Exception e) {
            log.error("案件批量创建调解文书出现全局异常！", e);
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long taskId) {
        AsyncTask asyncTask = asyncTaskService.selectByPrimaryKey(taskId);
        asyncTask.setSuccessAmt(0L);
        try {
            String queryString= asyncTask.getFieldJson().get("query");
            CreateMediateLetterParam param = JsonUtils.fromJson(queryString, CreateMediateLetterParam.class);
            AssertUtil.notNull(param, "参数不能为空");
            String userSessionStr = asyncTask.getFieldJson().get("userSession");
            UserSession userSession = JsonUtils.fromJson(userSessionStr, UserSession.class);
            AssertUtil.notNull(userSession, "userSession不能为空");
            execCreateMediateFile(param, userSession, asyncTask);

        } catch (Exception e) {
            log.error("批量创建调解文书，taskId:{}, 失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
        } finally {
            redisUtil.lRemove(KeyCache.CASE_ADD_TO_MEDIATE_TASK_ID_LIST, 0, taskId.toString());
        }
    }

    public void execCreateMediateFile(CreateMediateLetterParam param, UserSession userSession, AsyncTask asyncTask) {
        // 查询案件状态
        List<CaseDTO> cases = caseService.getCasesForLetter(param.getCaseMultiQuery());
        String msg = null;
        if (CollectionUtils.isEmpty(cases)) {
            msg = "未选择任何案件信息";
        }
        int total = cases.size();
        Example example = new Example(LetterSigner.class);
        example.and().andEqualTo("letterTemplateId", param.getTemplateId());
        List<LetterSigner> signerList = letterSignerService.selectByExample(example);
        List<String> signerNames = signerList.stream().map(LetterSigner::getName).collect(Collectors.toList());
        List<CaseDTO> failedCase = new ArrayList<>();
        if (signerNames.contains(SignerEnums.Type.MEDIATOR.getMessage())) {
            // 不存在催员的案件不可生成带有调解员签名的文书
            failedCase = cases.stream().filter(caseDTO -> !Objects.isNull(caseDTO.getUserId())).collect(Collectors.toList());
            cases.removeAll(failedCase);
            if (!CollectionUtils.isEmpty(failedCase)) {
                msg = "文书需要调解员签署，该案件未分配到指定调解员，不支持生成文书";
            }
        }
        if (!CollectionUtils.isEmpty(cases)) {
            // 校验模板参数、催员/调解员 文书功能状态（部门启用如何处理）   设置调解文书信息，填充案件变量、自定义变量
            List<LetterMediate> letterMediates = cases.stream().map(dto -> {
                MediateAuditParam mediateAuditParam = new MediateAuditParam();
                mediateAuditParam.setLetterTemplateId(param.getTemplateId());
                mediateAuditParam.setCaseId(dto.getId());
                return mediateAuditService.setMediateLetter(dto, mediateAuditParam, userSession);
            }).collect(Collectors.toList());
            letterMediateService.insertBatch(letterMediates);
            //  设置调解文书申请  默认申请通过 mediate_audit
            Date now = new Date();
            Map<Long, CaseDTO> caseMap = cases.stream().collect(Collectors.toMap(CaseDTO::getId, f -> f));
            List<MediateAudit> mediateAudits = letterMediates.stream().map(lm -> {
                MediateAudit mediateAudit = new MediateAudit();
                mediateAudit.setAuditBy(userSession.getId());
                mediateAudit.setApplyBy(userSession.getId());
                mediateAudit.setApplyByName(userSession.getName());
                mediateAudit.setAuditByName(userSession.getName());
                mediateAudit.setUpdateTime(now);
                mediateAudit.setCreateTime(now);
                mediateAudit.setAuditTime(now);
                mediateAudit.setApplyTime(now);
                mediateAudit.setState(MediateAuditEnums.State.PASS.getCode());
                mediateAudit.setLetterId(lm.getId());
                CaseDTO caseDTO = caseMap.get(lm.getCaseId());
                mediateAudit.setAgentType(ObjectUtil.equals(caseDTO.getAllotAgent(), CaseEnums.AllotAgent.WW.getCode())? ApplyEnums.AgentType.OUTER.getCode():ApplyEnums.AgentType.INNER.getCode());
                return mediateAudit;
            }).collect(Collectors.toList());
            mediateAuditService.insertBatch(mediateAudits);

            // 文书申请成功，上传文书至e签宝
            mediateAudits.forEach(ma -> {
                try {
                    letterMediateService.uploadFile(ma.getLetterId());
                } catch (Exception e) {
                    log.error("上传文书至e签宝时出现异常");
                }
            });
        }

        Integer taskStatus = AsyncTaskEnums.Status.SUCCESS.getCode();
        if (CollectionUtils.isEmpty(cases)) {
            taskStatus = AsyncTaskEnums.Status.FAIL.getCode();
        } else if (CollectionUtils.isEmpty(failedCase)){
            taskStatus = AsyncTaskEnums.Status.PART_FAIL.getCode();
        }

        asyncTask.setStatus(taskStatus);
        asyncTask.setTotal(asyncTask.getTotal()+ total);
        asyncTask.setSuccessAmt(asyncTask.getSuccessAmt() + cases.size());
        asyncTask.setIgnoreCount(asyncTask.getIgnoreCount() + failedCase.size());
        asyncTask.setUpdateTime(new Date());
        asyncTask.setDesc(msg);
        asyncTaskService.updateByPrimaryKeySelective(asyncTask);

        // 添加案件操作日志
        CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
        caseBatchUpdateEvent.setType(CaseLogEnums.Type.CASE_TO_MEDIATE_FILE.getCode());
        caseBatchUpdateEvent.setUserSession(userSession);
        caseBatchUpdateEvent.setCaseIds(cases.stream().map(CaseDTO::getId).collect(Collectors.toList()));
        caseBatchUpdateEvent.setTaskId(asyncTask.getId());
        applicationContext.publishEvent(caseBatchUpdateEvent);
    }
}
