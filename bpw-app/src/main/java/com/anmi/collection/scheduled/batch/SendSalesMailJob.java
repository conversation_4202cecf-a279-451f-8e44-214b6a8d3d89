package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 销售邮箱邮件发送
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class SendSalesMailJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 0 9 ? * MON-FRI")
     */
    @XxlJob("sendSalesMailHandler")
    public ReturnT<String> sendSalesMailHandler(){
        super.autoSendSalesMail();
        return ReturnT.SUCCESS;
    }
}

