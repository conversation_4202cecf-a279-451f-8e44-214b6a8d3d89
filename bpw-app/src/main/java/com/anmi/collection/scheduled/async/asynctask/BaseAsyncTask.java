package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.service.AsyncTaskService;
import com.anmi.collection.service.CompanyService;
import com.anmi.collection.utils.JSONUtil;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.domain.cases.AsyncTask;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 2023/3/28
 * BaseAsyncTask
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseAsyncTask {

    @Autowired protected RedisUtil redisUtil;
    @Autowired protected AsyncTaskService asyncTaskService;
    @Autowired protected SystemConfig systemConfig;
    @Autowired protected CompanyService companyService;
    @Autowired protected RedissonClient redissonClient;

    /**
     * 执行异步任务，入口
     */
    protected void startAsyncTask(String redisKey) throws Exception{
        List<AsyncTask> needExecuteAsyncTasks = needExecuteAsyncTasks(redisKey);

        needExecuteAsyncTasks.forEach(asyncTask->{
            Long taskId = asyncTask.getId();
            log.info("======={}=======taskId:{} start", redisKey, taskId);
            try {
                execute(taskId);
            } catch (Exception e) {
                log.error("======={}=======taskId:{} 异常:{}", redisKey, taskId, ExceptionUtil.stacktraceToString(e));
            }
            log.info("======={}=======taskId:{} end", redisKey, taskId);
        });
    }

    /**
     * 获取需要执行的任务
     *
     * @param redisKey redis键
     * @return {@link List}<{@link AsyncTask}>
     */
    private List<AsyncTask> needExecuteAsyncTasks(String redisKey) throws Exception{
        List<String> taskIdStrs = redisUtil.lGet(redisKey, 0, -1);
        if (ObjectUtil.isEmpty(taskIdStrs)){
            return new ArrayList<>();
        }

        Thread.sleep(1000);
        List<Long> taskIds = taskIdStrs.stream().map(Long::parseLong).collect(Collectors.toList());
        List<AsyncTask> asyncTasks = asyncTaskService.selectTasks(taskIds);

        // 清除redis多余的任务id
        cleanRedisSurplusAsyncTaskId(redisKey, taskIdStrs, asyncTasks);

        return asyncTasks;
    }

    /**
     * 清除redis多余的任务id
     *
     * @param redisKey   redis键
     * @param taskIdStrs 任务id字符串
     * @param tasks      任务
     */
    private void cleanRedisSurplusAsyncTaskId(String redisKey, List<String> taskIdStrs, List<AsyncTask> tasks) {
        // 清理redis中存在，mysql中不存在的数据
        List<String> needCleans = taskIdStrs.stream().filter(taskIdStr -> tasks.stream().noneMatch(task -> ObjectUtil.equal(Long.parseLong(taskIdStr), task.getId()))).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(needCleans)){
            log.info("{},redis中存在，mysql中不存在的异步任务:{}", redisKey, JSONUtil.toJsonStr(needCleans));
            needCleans.forEach(taskIdStr->redisUtil.lRemove(redisKey,0,taskIdStr));
        }
    }

    /**
     * 单条异步任务的执行，具体逻辑在子类中实现
     *
     * @param taskId 异步任务id
     */
    protected abstract void execute(Long taskId);
}
