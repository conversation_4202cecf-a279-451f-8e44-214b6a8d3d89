package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.FileStorageEnums;
import com.anmi.collection.dto.fileStorage.UploadCreatedFileInfo;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.CaseFileMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseService;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.utils.AnmiSnowFlowUtil;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.OSSClientUtil;
import com.anmi.collection.utils.easyexcel.CaseAttachImportWriteHandler;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.CaseFile;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 案例附件导入任务
 *
 * <AUTHOR>
 * @date 2022/11/28
 */
@Slf4j
public class CaseAttachImportTask extends BaseAsyncTask {

    @Autowired private CaseService caseService;
    @Autowired private CaseFileMapper caseFileMapper;
    @Autowired private FileStorageStrategyFactory fileStorageStrategyFactory;

    @XxlJob("caseAttachImportHandler")
    public ReturnT<String> caseAttachImportHandler() {
        try {
            startAsyncTask(KeyCache.CASE_ATTACH_IMPORT_LIST);
        } catch (Exception e) {
            log.error("案例附件导入出现全局异常！",e);
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long taskId) {
        String filePath = null;
        File unzip = null;
        String caseAttachUrl = null;
        String fileName = null;
        try {
            AsyncTask task  = asyncTaskService.selectByPrimaryKey(taskId);
            if (task == null || !AsyncTaskEnums.Status.ING.getCode().equals(task.getStatus())) {
                throw new ApiException("任务不存在或者任务状态不是执行中");
            }

            Map<String, String> fieldJson = task.getFieldJson();
            String orgDeltId = fieldJson.get("orgDeltId");
            caseAttachUrl = task.getFileUrl();
            fileName = task.getFileName();
            Long createBy = task.getCreateBy();
            String nameNoExpand = StringUtils.substringBeforeLast(fileName,".");

            // 下载
            filePath = systemConfig.getCaseAttachPath() + File.separator + fileName;
            if (caseAttachUrl.startsWith("http")){
                HttpUtil.downloadFile(caseAttachUrl,filePath);
            }

            List<Charset> charsets = Arrays.asList(CharsetUtil.CHARSET_UTF_8,CharsetUtil.CHARSET_GBK,CharsetUtil.CHARSET_ISO_8859_1);

            for (int i = 0; i < charsets.size(); i++) {
                if (ObjectUtil.isNotNull(unzip)) {
                    break;
                }
                Charset charset = charsets.get(i);
                // 解压
                try {
                    unzip = ZipUtil.unzip(filePath, charset);
                }catch (IllegalArgumentException e){
                    log.error("{}使用{}字符集解压失败",taskId,charset.name());
                }
            }

            if (ObjectUtil.isNull(unzip)) {
                throw new ApiException("压缩包解压失败");
            }

            String unzipPath = unzip.getAbsolutePath();

            // 递归解压包下所有文件
            List<File> files = FileUtil.loopFiles(unzip).stream().filter(file->!(file.getName().startsWith(".")||file.getAbsolutePath().contains("/__MACOSX"))).collect(Collectors.toList());

            StringBuffer sb = new StringBuffer();
            // 剔除不符合目录结构的文件
            List<File> effectiveFiles = files.stream().filter(file -> {
                File firstParentFile = file.getParentFile();
                if (ObjectUtil.isNull(firstParentFile)) {
                    return false;
                }
                File secondParentFile = firstParentFile.getParentFile();
                if (ObjectUtil.isNull(secondParentFile)) {
                    return false;
                }
                File thirdParentFile = secondParentFile.getParentFile();
                if (ObjectUtil.isNull(thirdParentFile)) {
                    return false;
                }
                String absolutePath = thirdParentFile.getAbsolutePath();
                if (ObjectUtil.equal(absolutePath, unzipPath)) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            int firstFilterSize = effectiveFiles.size();
            if (files.size() > firstFilterSize){
                sb.append("目录结构不符合规范;");
            }

            // 案件编号
            List<String> outSerialNos = effectiveFiles.stream().map(file -> {
                String name = file.getParentFile().getName().trim();
                return name+"#"+orgDeltId;
            }).distinct().collect(Collectors.toList());

            Map<String, Long> finalExistOutSerialNoMap = new HashMap<>();
            // 剔除不存在的案件编号的文件
            if (ObjectUtil.isNotEmpty(outSerialNos)){
                Map<String, Long> existOutSerialNoMap = caseService.getOutSerialNoAndIdMap(outSerialNos);
                finalExistOutSerialNoMap = existOutSerialNoMap;
                effectiveFiles = effectiveFiles.stream().filter(file -> {
                    String name = file.getParentFile().getName().trim();
                    String outSerialNo = name+"#"+orgDeltId;
                    Long caseId = existOutSerialNoMap.get(outSerialNo);
                    return ObjectUtil.isNotNull(caseId);
                }).collect(Collectors.toList());
            }

            int secondFilterSize = effectiveFiles.size();
            if (firstFilterSize > secondFilterSize){
                sb.append("委案公司或案件编号未匹配;");
            }

            List<File> finalEffectiveFiles = effectiveFiles;
            // 无效文件
            List<File> invalidFiles = files.stream()
                    .filter(file -> finalEffectiveFiles.stream().noneMatch(effectiveFile->ObjectUtil.equal(file.getPath(),effectiveFile.getPath())))
                    .sorted(Comparator.comparing(File::getParent))
                    .collect(Collectors.toList());

            // 解压后的文件上传、与case绑定
            for (int i = 0; i < finalEffectiveFiles.size(); i++) {
                try {
                    File file = finalEffectiveFiles.get(i);
                    String name = file.getParentFile().getName().trim();
                    String outSerialNo = name+"#"+orgDeltId;
                    Long caseId = finalExistOutSerialNoMap.get(outSerialNo);

                    uploadFile(caseId,file,createBy);
                }catch (Exception e){
                    log.error("案例附件解压后上传失败：{}", ExceptionUtil.stacktraceToString(e));
                }
            }

            String desc = StringUtils.substringBeforeLast(sb.toString(),";");
            task.setDesc(desc);
            task.setFileUrl(StrUtil.EMPTY);
            // 无效案件信息，写入excel输出
            if(ObjectUtil.isNotEmpty(invalidFiles)){
                List<List<String>> erorData = new ArrayList<>();
                List<String> firstRow = new ArrayList<>();
                firstRow.add("注意：\n" +
                        "1.导入失败的文件仅显示失败文件名；\n" +
                        "2.导入失败原因有：\n" +
                        "·文件夹命名不符合标准“案件编号”格式，则整个文件夹内的所有文件默认失败，失败文件显示文件夹名称及文件名称；\n" +
                        "·同一案件下的文件，必须打包到独立文件夹下，未打包文件默认失败，并显示文件名称；" );
                List<String> secondRow = new ArrayList<>();
                secondRow.add("文件夹名称");
                secondRow.add("文件名称");
                erorData.add(firstRow);
                erorData.add(secondRow);
                invalidFiles.stream().forEach(invalidFile -> {
                    List<String> row = new ArrayList<>();
                    String parentName = invalidFile.getParentFile().getName();
                    if (ObjectUtil.equals(nameNoExpand,parentName)){
                        row.add(StrUtil.EMPTY);
                    }else {
                        row.add(parentName);
                    }
                    row.add(invalidFile.getName());
                    erorData.add(row);
                });

                String name = "资料导入失败数据_" + AnmiSnowFlowUtil.getIdStr() + ".xlsx";
                Date expireDate = DateUtils.addDays(new Date(), 3);
                File file = new File(name);
                EasyExcel.write(file)
                        .registerWriteHandler(new CaseAttachImportWriteHandler())
                        .sheet()
                        .doWrite(erorData);
                UploadCreatedFileInfo uploadCreatedFileInfo = new UploadCreatedFileInfo();
                uploadCreatedFileInfo.setFile(file)
                        .setFileName(name)
                        .setExpireDate(expireDate)
                        .setBucket(systemConfig.getTemporaryFileBucket())
                        .setLocalUrl(systemConfig.getLocalFilePath() + DateUtils.formatDate(new Date()) + File.separator + name);
                FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
                String errFilePath = fileStorageStrategy.uploadCreatedFile(uploadCreatedFileInfo);
                task.setFileUrl(errFilePath);
                task.setExpireTime(expireDate);
            }

            task.setTotal(new Long(String.valueOf(files.size())));
            task.setSuccessAmt(new Long(String.valueOf(effectiveFiles.size())));
            if (ObjectUtil.isEmpty(invalidFiles)){
                task.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
            }else if (ObjectUtil.isEmpty(finalEffectiveFiles)){
                task.setStatus(AsyncTaskEnums.Status.FAIL.getCode());
            }else {
                task.setStatus(AsyncTaskEnums.Status.PART_FAIL.getCode());
            }
            task.setUpdateTime(new Date());
            asyncTaskService.updateByPrimaryKeySelective(task);
        } catch (Exception e) {
            log.error("案例附件导入任务，taskId:{}，失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
            AsyncTask taskUpdate = new AsyncTask();
            taskUpdate.setId(taskId);
            taskUpdate.setStatus(AsyncTaskEnums.Status.FAIL.getCode());
            taskUpdate.setUpdateTime(new Date());
            taskUpdate.setDesc(StrUtil.isBlank(e.getMessage()) ? "程序异常，请联系管理员" : StringUtils.substring(e.getMessage(), 0, 100) + "，请联系管理员");
            asyncTaskService.updateByPrimaryKeySelective(taskUpdate);
        } finally {
            try {
                redisUtil.lRemove(KeyCache.CASE_ATTACH_IMPORT_LIST, 0, taskId.toString());
                FileUtil.del(filePath);
                FileUtil.del(unzip);
                if (StringUtils.equals(systemConfig.getChannelCode(), FileStorageEnums.Type.OSS.getCode())
                        &&StrUtil.isNotBlank(caseAttachUrl)&&StrUtil.isNotBlank(fileName)){
                    OSSClientUtil.deleteFileByFileName(fileName,caseAttachUrl);
                }
            }catch (Exception e){
                log.error("案例附件异步任务finally失败：{}", ExceptionUtil.stacktraceToString(e));
            }
        }
    }

    /**
     * 上传文件
     *
     * @param caseId   案件id
     * @param file     文件
     * @param uploadBy 上传者
     */
    private void uploadFile(Long caseId, File file, Long uploadBy) {
        Date currentDate = new Date();
        Date expireDate = DateUtils.addDays(currentDate, 10 * 365);
        String fileName = file.getName();
        fileName = StringUtils.substringBeforeLast(fileName, ".")
                + "_"
                + AnmiSnowFlowUtil.getIdStr()
                + "."
                + StringUtils.substringAfterLast(fileName, ".");
        UploadCreatedFileInfo uploadCreatedFileInfo = new UploadCreatedFileInfo();
        uploadCreatedFileInfo.setFile(file)
                .setFileName(fileName)
                .setExpireDate(expireDate)
                .setBucket(systemConfig.getCaseFilesBucket())
                .setLocalUrl(systemConfig.getCaseFilePath() + File.separator + fileName);
        FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
        String caseFilesUrl = fileStorageStrategy.uploadCreatedFile(uploadCreatedFileInfo);

        CaseFile caseFile = new CaseFile();
        caseFile.setCaseId(caseId);
        caseFile.setUploadBy(uploadBy);
        caseFile.setUploadTime(currentDate);
        caseFile.setFileName(file.getName());
        caseFile.setFileUrl(caseFilesUrl);
        caseFileMapper.insertSelective(caseFile);
    }
}
