package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.CaseLogEnums;
import com.anmi.collection.entity.requset.cases.CaseAddTagParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.CaseTagMapper;
import com.anmi.collection.mapper.CaseTagRelMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseService;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.domain.cases.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class CaseAddTagTask extends BaseAsyncTask {

  @Autowired private CaseService caseService;
  @Autowired private ApplicationContext applicationContext;
  @Autowired private CaseTagRelMapper caseTagRelMapper;
  @Autowired private CaseTagMapper caseTagMapper;

  @XxlJob("caseAddTagHandler")
  public ReturnT<String> caseAddTagHandler() {
    try {
      startAsyncTask(KeyCache.CASE_ADD_TAG_TASK_ID_LIST);
    } catch (Exception e) {
      log.error("添加案件标签出现全局异常！", e);
    }
    return ReturnT.SUCCESS;
  }

  @Override
  protected void execute(Long taskId) {
    AsyncTask asyncTask = asyncTaskService.selectByPrimaryKey(taskId);
    asyncTask.setSuccessAmt(0L);
    try {
      String queryString = asyncTask.getFieldJson().get("query");
      CaseAddTagParam param = JsonUtils.fromJson(queryString, CaseAddTagParam.class);
      Assert.notNull(param, "参数不能为空");
      String sessionUserStr = asyncTask.getFieldJson().get("userSession");
      UserSession userSession = JsonUtils.fromJson(sessionUserStr, UserSession.class);
      Assert.notNull(userSession, "userSession不能为空");
      param.setOrgIds(userSession.getOrgId().toString());
      List<CaseTag> validTag = new ArrayList<>();
      if(!CollectionUtils.isEmpty(param.getAddTagIds())) {
        Example caseValidTagQuery = new Example(CaseTag.class);
        caseValidTagQuery.and().andEqualTo("state", CaseEnums.TagState.OPEN.getCode());
        caseValidTagQuery.and().andIn("id", param.getAddTagIds());
        validTag = this.caseTagMapper.selectByExample(caseValidTagQuery);
        if (CollectionUtils.isEmpty(validTag)) {
          throw new ApiException("所选标签均已禁用，请刷新后再操作");
        }
      }
      List<CaseTag> finalValidTag = validTag;

      if (systemConfig.getESSwitch()) {
        caseService.getAllCasesUsingEs(param, (pageInfo) -> {
          execAddCaseTag(pageInfo, userSession, param, asyncTask, finalValidTag, taskId);
        });
      } else {
        caseService.queryResultForMulti(param, (pageInfo -> {
          execAddCaseTag(pageInfo, userSession, param, asyncTask, finalValidTag, taskId);
        }));
      }
      asyncTaskService.updateByPrimaryKeySelective(asyncTask);
    } catch (Exception e) {
      log.error("添加案件标签,taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
      asyncTaskService.updateTaskFinish(asyncTask, e.getMessage());
    } finally {
      redisUtil.lRemove(KeyCache.CASE_ADD_TAG_TASK_ID_LIST, 0, taskId.toString());
    }
  }

  public void execAddCaseTag(PageOutput<CaseQueryResult> pageInfo,
                             UserSession userSession,
                             CaseAddTagParam param,
                             AsyncTask asyncTask,
                             List<CaseTag> validTag,Long taskId) {
    List<Long> caseIds = pageInfo.getList().stream().map(CaseQueryResult::getId).collect(Collectors.toList());
    if (CollectionUtils.isEmpty(caseIds)) {
      return;
    }
    if (Objects.equals(param.getDelCaseTag(), Boolean.TRUE)) {
      Example exampleDelCasTagRel = new Example(CaseTagRel.class);
      Example.Criteria criteriaDelCasTagRel = exampleDelCasTagRel.createCriteria();
      criteriaDelCasTagRel.andEqualTo("orgId", asyncTask.getOrgId());
      criteriaDelCasTagRel.andIn("caseId", caseIds);
      caseTagRelMapper.deleteByExample(exampleDelCasTagRel);
    }

    if (!CollectionUtils.isEmpty(validTag)) {
      List<CaseTagRel> caseTagRels = new ArrayList<>();
      for (Long caseId : caseIds) {
        for (CaseTag tag : validTag) {
          CaseTagRel caseTagRel = new CaseTagRel();
          caseTagRel.setCaseId(caseId);
          caseTagRel.setTagId(tag.getId());
          caseTagRel.setOrgId(asyncTask.getOrgId());
          caseTagRels.add(caseTagRel);
        }
      }

      Example existExample = new Example(CaseTagRel.class);
      existExample.and().andEqualTo("orgId", userSession.getOrgId());
      existExample.and().andIn("caseId", caseIds);
      List<CaseTagRel> existCaseTagRel = caseTagRelMapper.selectByExample(existExample);
      caseTagRels = caseTagRels.stream().filter(t -> {
        for (CaseTagRel tmp : existCaseTagRel) {
          if (Objects.equals(tmp.getCaseId(), t.getCaseId()) &&
            Objects.equals(tmp.getTagId(), t.getTagId())) {
            return false;
          }
        }
        return true;
      }).collect(Collectors.toList());
      if (!CollectionUtils.isEmpty(caseTagRels)) {
        this.caseTagRelMapper.insertList(caseTagRels);
      }
    }

    Case caseInfo = new Case();
    caseInfo.setUpdateBy(userSession.getId());
    caseInfo.setUpdateTime(new Date());
    Example example = new Example(Case.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("orgId", asyncTask.getOrgId());
    criteria.andIn("id", caseIds);
    caseService.updateByExampleSelective(caseInfo, example);

    asyncTask.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
    asyncTask.setTotal(asyncTask.getTotal()+caseIds.size());
    asyncTask.setSuccessAmt(asyncTask.getSuccessAmt() + caseIds.size());


    CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
    caseBatchUpdateEvent.setType(CaseLogEnums.Type.CASE_ADD_TAG.getCode());
    caseBatchUpdateEvent.setUserSession(userSession);
    caseBatchUpdateEvent.setCaseIds(caseIds);
    caseBatchUpdateEvent.setTaskId(taskId);
    applicationContext.publishEvent(caseBatchUpdateEvent);
  }
}
