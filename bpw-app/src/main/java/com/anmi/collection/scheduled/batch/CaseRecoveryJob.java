package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 案件自动回收
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class CaseRecoveryJob extends BaseTask {

  /**
   * @Scheduled(cron = "0 0 1 * * ?")
   */
  @XxlJob("caseRecoveryHandler")
  public ReturnT<String> caseRecoveryHandler() throws Exception {
    super.autoRecoveryCase();
    return ReturnT.SUCCESS;
  }
}
