package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.dto.EndInfoDTO;
import com.anmi.collection.entity.requset.cases.CaseMultiQuery;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.scheduled.async.QueueThreadPoolHolder;
import com.anmi.collection.scheduled.async.ThreadPollTask;
import com.anmi.collection.service.*;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.SpringContextHolder;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseQueryResult;
import com.anmi.domain.user.User;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CaseChangeStatusTask extends BaseAsyncTask {

    private static QueueThreadPoolHolder threadPoolHolder = new QueueThreadPoolHolder("CaseChangeStatusTask", 1);

    @XxlJob("caseChangeStatusHandler")
    public ReturnT<String> caseChangeStatusHandler() {
        try {
            startAsyncTask(KeyCache.CASE_CHANGE_STATUS_TASK_ID_LIST);
        } catch (Exception e) {
            log.error("案件状态变更出现全局异常！", e);
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long taskId) {
        Set<String> caseIds = redisUtil.sGet(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + taskId);
        Integer dataVolume = null;
        if (ObjectUtil.isNotEmpty(caseIds)){
            dataVolume = caseIds.size();
        }

        if (threadPoolHolder.trySubmit(new CaseChangeStatusTaskThread(taskId), dataVolume)) {
            // 任务列表移除任务id
            redisUtil.lRemove(KeyCache.CASE_CHANGE_STATUS_TASK_ID_LIST, 0, taskId.toString());
        }
    }
}

class CaseChangeStatusTaskThread extends ThreadPollTask {

    private Logger log = LoggerFactory.getLogger(CaseChangeStatusTaskThread.class);
    private Long taskId;
    private CaseService caseService;
    private AsyncTaskService asyncTaskService;
    private UserService userService;
    private CaseMapper caseMapper;
    private AllotCaseService allotCaseService;
    private CaseCooperationService caseCooperationService;
    private RedisUtil redisUtil;

    public CaseChangeStatusTaskThread(Long taskId) {
        this.caseService = SpringContextHolder.getBean(CaseService.class);
        this.asyncTaskService = SpringContextHolder.getBean(AsyncTaskService.class);
        this.userService = SpringContextHolder.getBean(UserService.class);
        this.caseMapper = SpringContextHolder.getBean(CaseMapper.class);
        this.allotCaseService = SpringContextHolder.getBean(AllotCaseService.class);
        this.caseCooperationService = SpringContextHolder.getBean(CaseCooperationService.class);
        this.redisUtil = SpringContextHolder.getBean(RedisUtil.class);
        this.taskId = taskId;
    }

    @Override
    public void run() {
        log.info("案件批量操作任务-开始" + taskId);
        if (taskId == null) {
            return;
        }
        Set<String> caseIdList = redisUtil.sGet(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + taskId);
        AsyncTask asyncTask = null;
        try {
            if (caseIdList == null || caseIdList.isEmpty()) {
                throw new ApiException("任务对应案件为空");
            }
            asyncTask = asyncTaskService.selectByPrimaryKey(taskId);
            if (asyncTask == null) {
                throw new ApiException("案件批量操作任务不存在");
            }
            if (asyncTask.getType().equals(AsyncTaskEnums.Type.CHANGE_STATUS.getCode())) {
                changeStatus(asyncTask, Lists.newArrayList(caseIdList));
            } else if (asyncTask.getType().equals(AsyncTaskEnums.Type.ADJUST.getCode())) {
                adjustCases(asyncTask, Lists.newArrayList(caseIdList));
            } else if (asyncTask.getType().equals(AsyncTaskEnums.Type.DELETE.getCode())) {
                delCases(asyncTask, Lists.newArrayList(caseIdList));
            } else if (asyncTask.getType().equals(AsyncTaskEnums.Type.ALLOT_RESET.getCode())) {
                allotReset(asyncTask, Lists.newArrayList(caseIdList));
            } else if (asyncTask.getType().equals(AsyncTaskEnums.Type.ALLOT_COOPERATOR.getCode())) {
                allotCooperator(asyncTask, Lists.newArrayList(caseIdList));
            } else {
                throw new ApiException("任务类别错误");
            }
            // 更新任务成功
            asyncTaskService.updateTaskFinish(asyncTask, null);
        } catch (Exception e) {
            // 任务失败
            asyncTaskService.updateTaskFinish(asyncTask, e.getMessage());
            log.error("案件批量操作任务,taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
        } finally {
            if (caseIdList != null && !caseIdList.isEmpty()) {
                // 解除状态保护
                redisUtil.sRemove(KeyCache.CASE_PROTECT_EXIST_IDS + asyncTask.getOrgId(), caseIdList.toArray());
            }
            redisUtil.expireDays(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + asyncTask.getId(), 1);
        }
    }

    private void changeStatus(AsyncTask asyncTask, List<String> caseIdList) {
        Map<String, String> fieldJson = asyncTask.getFieldJson();
        Integer changeStatus = Integer.valueOf(fieldJson.get("changeStatus"));
        Date delayTime = null;
        String delayTimeStr = fieldJson.get("delayTime");
        if (delayTimeStr != null) {
            delayTime = DateUtils.parseDate(delayTimeStr);
        }
        Date restartTime = null;
        String restartTimeStr = fieldJson.get("restartTime");
        if (restartTimeStr != null) {
            restartTime = DateUtils.parseDate(restartTimeStr);
        }
        String reason = fieldJson.get("reason");

        // 结案类型
        EndInfoDTO endInfoDTO = new EndInfoDTO();
        if (fieldJson.get("endType") != null) {
            endInfoDTO.setEndType(Integer.valueOf(fieldJson.get("endType")));
        }
        if (fieldJson.get("endConfigId") != null) {
            endInfoDTO.setEndConfigId(Long.valueOf(fieldJson.get("endConfigId")));
        }
        Date autoRestartDate = null;
        if (StringUtils.isNotBlank(fieldJson.get("autoRestartDate"))) {
            autoRestartDate = DateUtils.parseDate(fieldJson.get("autoRestartDate"));
        }

        UserSession userSession = new UserSession();
        userSession.setId(asyncTask.getCreateBy());
        userSession.setOrgId(asyncTask.getOrgId());
        userSession.setName(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));
        Integer start = 0;
        Integer end = 0;
        while (start < caseIdList.size()) {
            try {
                end = start + 1000;
                if (end > caseIdList.size()) {
                    end = caseIdList.size();
                }
                List<String> subIdList = caseIdList.subList(start, end);
                List<CaseQueryResult> caseList = caseService.selectChangeListByIds(subIdList, changeStatus, delayTime, restartTime);
                if (CollectionUtils.isEmpty(caseList)) {
                    continue;
                }
                // 批量执行修改案件信息、log记录
                caseService.changeStatusUpdate(
                    caseList, changeStatus, reason, delayTime, null,
                    userSession, restartTime, asyncTask.getId(), endInfoDTO,
                    autoRestartDate);
                // 更新任务成功数
                asyncTaskService.updateTaskSuccessAmt(asyncTask, Long.valueOf(caseList.size()));
                // 成功的案件移（留下的则是失败的案件）
                redisUtil.sRemove(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + asyncTask.getId(), subIdList.toArray(new String[subIdList.size()]));
                if (AsyncTaskEnums.CreateType.OPEN_API.getCode().equals(asyncTask.getCreateType())) {
                    List<String> outSerialNoList = caseList.stream().map(CaseQueryResult::getOutSerialTemp).collect(Collectors.toList());
                    redisUtil.sRemove(KeyCache.ASYNC_OUT_SERIAL_NO_LIST + asyncTask.getId(), outSerialNoList.toArray(new String[outSerialNoList.size()]));
                }
            } catch (Exception e) {
                log.error("案件批量操作任务,taskId:{},失败！{}", asyncTask.getId(), ExceptionUtil.stacktraceToString(e));
            } finally {
                start = end;
            }
        }
    }

    private void adjustCases(AsyncTask asyncTask, List<String> caseIdList) {
        Map<String, String> fieldJson = asyncTask.getFieldJson();
        String toOperatorStr = fieldJson.get("toOperator");
        Long toOperator = StringUtils.isBlank(toOperatorStr) ? null : Long.valueOf(toOperatorStr);
        Long teamId = Long.valueOf(fieldJson.get("teamId"));
        String reason = fieldJson.get("reason");
        UserSession userSession = new UserSession();
        userSession.setId(asyncTask.getCreateBy());
        userSession.setOrgId(asyncTask.getOrgId());
        userSession.setName(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));
        Integer start = 0;
        Integer end = 0;
        while (start < caseIdList.size()) {
            try {
                end = start + 1000;
                if (end > caseIdList.size()) {
                    end = caseIdList.size();
                }
                List<String> subIdList = caseIdList.subList(start, end);
                List<CaseQueryResult> caseList = selectAdjustListByIds(subIdList);
                if (CollectionUtils.isEmpty(caseList)) {
                    continue;
                }
                // 批量执行修改案件信息、log记录
                allotCaseService.updateAdjustUser(caseList, teamId, toOperator, reason, userSession, asyncTask.getId());
                // 更新任务成功数
                asyncTaskService.updateTaskSuccessAmt(asyncTask, Long.valueOf(caseList.size()));
                // 成功的案件移（留下的则是失败的案件）
                redisUtil.sRemove(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + asyncTask.getId(), subIdList.toArray(new String[subIdList.size()]));
            } catch (Exception e) {
                log.error("案件批量操作任务,taskId:{},失败！{}", asyncTask.getId(), ExceptionUtil.stacktraceToString(e));
            } finally {
                start = end;
            }
        }
    }

    private List<CaseQueryResult> selectAdjustListByIds(List<String> caseIdList) {
        CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
        List<Long> idList = caseIdList.stream().map(Long::valueOf).collect(Collectors.toList());
        List<Integer> allotStatusList = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode(),CaseEnums.AllotStatus.ALLOT_TEAM.getCode());
        List<Integer> caseStatusList = ImmutableList.of(CaseEnums.CaseStatus.STOP.getCode(), CaseEnums.CaseStatus.NORMAL.getCode(), CaseEnums.CaseStatus.DELAY.getCode());

        caseMultiQuery.setCaseIds(idList);
        caseMultiQuery.setAllotStatues(StringUtils.join(allotStatusList, ","));
        caseMultiQuery.setCaseStatues(StringUtils.join(caseStatusList, ","));
        List<CaseQueryResult> caseQueryResultList = caseMapper.queryResultForMulti(caseMultiQuery);
        return caseQueryResultList;
    }

    private void delCases(AsyncTask asyncTask, List<String> caseIdList) {
        Long orgId = asyncTask.getOrgId();
        Long taskId = asyncTask.getId();
        UserSession userSession = new UserSession();
        userSession.setId(asyncTask.getCreateBy());
        userSession.setOrgId(orgId);
        userSession.setName(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));
        Integer start = 0;
        Integer end = 0;
        Set<Long> debtorIdSet = new HashSet<>();
        List<Long> notConjointCaseIds = new ArrayList<>();
        while (start < caseIdList.size()) {
            try {
                end = start + 1000;
                if (end > caseIdList.size()) {
                    end = caseIdList.size();
                }
                List<String> subIdList = caseIdList.subList(start, end);
                List<CaseQueryResult> caseList = selectDelListByIds(subIdList);
                if (CollectionUtils.isEmpty(caseList)) {
                    continue;
                }
                // 批量执行修改案件信息、log记录
                caseService.batchDelCase(caseList, userSession, taskId);
                Set<Long> debtorIdSub = caseList.stream().map(CaseQueryResult::getDebtId).collect(Collectors.toSet());
                debtorIdSet.addAll(debtorIdSub);
                List<Long> caseIdSub =
                    caseList.stream().filter(c -> c.getDebtId() == null).map(Case::getId).collect(Collectors.toList());
                notConjointCaseIds.addAll(caseIdSub);

                // 更新任务成功数
                asyncTaskService.updateTaskSuccessAmt(asyncTask, Long.valueOf(caseList.size()));
                // 成功的案件移（留下的则是失败的案件）
                redisUtil.sRemove(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + asyncTask.getId(), subIdList.toArray(new String[subIdList.size()]));
            } catch (Exception e) {
                log.error("案件批量操作任务,taskId:{},失败！{}", asyncTask.getId(), ExceptionUtil.stacktraceToString(e));
            } finally {
                start = end;
            }
        }
        if (!debtorIdSet.isEmpty()) {
            caseService.delContacts(asyncTask.getOrgId(), debtorIdSet, notConjointCaseIds);
        }
    }

    private List<CaseQueryResult> selectDelListByIds(List<String> caseIdList) {
        CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
        List<Long> idList = caseIdList.stream().map(Long::valueOf).collect(Collectors.toList());
        caseMultiQuery.setCaseIds(idList);
        List<CaseQueryResult> caseQueryResultList = caseMapper.queryResultForMulti(caseMultiQuery);
        return caseQueryResultList;
    }

    private void allotReset(AsyncTask asyncTask, List<String> caseIdList) {
        Long taskId = asyncTask.getId();
        UserSession userSession = new UserSession();
        userSession.setId(asyncTask.getCreateBy());
        userSession.setOrgId(asyncTask.getOrgId());
        userSession.setName(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));
        Integer revokeTo = asyncTask.getFieldJson() == null ? 0 : Integer.valueOf(asyncTask.getFieldJson().get("revokeTo"));

        Integer start = 0;
        Integer end = 0;
        while (start < caseIdList.size()) {
            try {
                end = start + 1000;
                if (end > caseIdList.size()) {
                    end = caseIdList.size();
                }
                List<String> subIdList = caseIdList.subList(start, end);
                List<CaseQueryResult> caseList = selectAllotResetListByIds(subIdList);
                if (CollectionUtils.isEmpty(caseList)) {
                    continue;
                }
                // 批量执行修改案件信息、log记录
                caseService.allotResetUpdate(caseList, userSession, revokeTo, taskId);
                // 更新任务成功数
                asyncTaskService.updateTaskSuccessAmt(asyncTask, Long.valueOf(caseList.size()));
                // 成功的案件移（留下的则是失败的案件）
                redisUtil.sRemove(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + asyncTask.getId(), subIdList.toArray(new String[subIdList.size()]));
            } catch (Exception e) {
                log.error("案件批量操作任务,taskId:{},失败！{}", asyncTask.getId(), ExceptionUtil.stacktraceToString(e));
            } finally {
                start = end;
            }
        }
    }

    private List<CaseQueryResult> selectAllotResetListByIds(List<String> caseIdList) {
        CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
        List<Long> idList = caseIdList.stream().map(i -> Long.valueOf(i)).collect(Collectors.toList());
        caseMultiQuery.setCaseIds(idList);

        List<Integer> allotStatusList = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode(),
          CaseEnums.AllotStatus.ALLOT_TEAM.getCode(),
          CaseEnums.AllotStatus.ALLOT_BRANCH.getCode());
        caseMultiQuery.setAllotStatues(StringUtils.join(allotStatusList, ","));
        return caseMapper.queryResultForMulti(caseMultiQuery);
    }

    private void allotCooperator(AsyncTask asyncTask, List<String> caseIdList) {
        Long taskId = asyncTask.getId();
        Map<String, String> fieldJson = asyncTask.getFieldJson();
        Long cooperatorId = Long.valueOf(fieldJson.get("cooperatorId"));
        User cooperator = userService.selectByPrimaryKey(cooperatorId);
        UserSession userSession = new UserSession();
        userSession.setId(asyncTask.getCreateBy());
        userSession.setOrgId(asyncTask.getOrgId());
        userSession.setName(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));
        Integer start = 0;
        Integer end = 0;
        while (start < caseIdList.size()) {
            try {
                end = start + 300;
                if (end > caseIdList.size()) {
                    end = caseIdList.size();
                }
                List<String> subIdList = caseIdList.subList(start, end);
                List<CaseQueryResult> caseList = selectCooCaseListByIds(subIdList);
                if (CollectionUtils.isEmpty(caseList)) {
                    continue;
                }
                // 批量执行分配、log记录
                Integer successAmt = caseCooperationService.handleAllot(taskId, caseList, cooperator, userSession, null);
                // 更新任务成功数
                asyncTaskService.updateTaskSuccessAmt(asyncTask, Long.valueOf(successAmt));
                // 成功的案件移（留下的则是失败的案件）
                redisUtil.sRemove(KeyCache.CASE_CHANGE_STATUS_TASK_CASES + asyncTask.getId(), subIdList.toArray(new String[subIdList.size()]));
            } catch (Exception e) {
                log.error("案件批量操作任务,taskId:{},失败！{}", asyncTask.getId(), ExceptionUtil.stacktraceToString(e));
            } finally {
                start = end;
            }
        }
    }

    private List<CaseQueryResult> selectCooCaseListByIds(List<String> caseIdList) {
        CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
        List<Long> idList = caseIdList.stream().map(i -> Long.valueOf(i)).collect(Collectors.toList());
        caseMultiQuery.setCaseIds(idList);
        // 只有分按完成、留案案件可以重新分配
        List<Integer> allotStatusList = ImmutableList.of(CaseEnums.AllotStatus.ALLOT_USER.getCode());
        List<Integer> caseStatusList = ImmutableList.of(CaseEnums.CaseStatus.DELAY.getCode(),CaseEnums.CaseStatus.NORMAL.getCode());
        caseMultiQuery.setAllotStatues(StringUtils.join(allotStatusList, ","));
        caseMultiQuery.setCaseStatues(StringUtils.join(caseStatusList, ","));
        return caseMapper.queryResultForMulti(caseMultiQuery);
    }
}
