package com.anmi.collection.scheduled.async.caseplan;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSONArray;
import com.anmi.collection.common.enums.CasePlanEnums;
import com.anmi.collection.common.enums.CasePlanRelEnums;
import com.anmi.collection.dto.CaseContact;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.MessageManager;
import com.anmi.collection.mapper.ContactsMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CasePlanRelService;
import com.anmi.collection.service.OrgSwitchService;
import com.anmi.collection.service.SmsTemplateService;
import com.anmi.collection.service.UserService;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.PatternUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.domain.cases.CasePlan;
import com.anmi.domain.cases.OrgSwitch;
import com.anmi.domain.yunpian.SmsTemplate;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class CasePlanMessageTask extends BaseCasePlanTask {

    @Autowired private MessageManager messageManager;
    @Autowired private UserService userService;
    @Autowired private SmsTemplateService smsTemplateService;
    @Autowired private CasePlanRelService casePlanRelService;
    @Autowired private ContactsMapper contactsMapper;
    @Autowired private OrgSwitchService orgSwitchService;

    @XxlJob("casePlanMessageHandler")
    public ReturnT<String> casePlanMessageHandler() {
        try {
            LogbackUtil.insertTrace(LogbackUtil.generateTrace());
            startPlan(KeyCache.CASE_MESSAGE_PLAN_ID_LIST);
        } catch (Exception e) {
            log.error("处理短信计划出现全局异常！", e);
        }finally {
            LogbackUtil.removeTrace();
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long planId) {
        Set<String> caseIdList = redisUtil.sGet(KeyCache.CASE_MESSAGE_CASE_IDS + planId);
        CasePlan casePlan = null;
        try {
            if (caseIdList == null || caseIdList.isEmpty()) {
                throw new ApiException("计划对应案件为空");
            }
            casePlan = casePlanService.selectByPrimaryKey(planId);
            if (casePlan == null) {
                throw new ApiException("计划不存在");
            }
            Long duyanPlanId = insertCasePlan(casePlan, Lists.newArrayList(caseIdList));
            if (duyanPlanId != null) {
                casePlan.setDuyanPlanId(duyanPlanId);
                casePlan.setStatus(CasePlanEnums.Status.ING.getCode());
            } else {
                casePlan.setStatus(CasePlanEnums.Status.FAIL.getCode());
            }
            casePlanService.updateByPrimaryKeySelective(casePlan);
        } catch (Exception e) {
            log.error("短信计划,planId:{},失败！{}", planId, ExceptionUtil.stacktraceToString(e));
            casePlan.setStatus(CasePlanEnums.Status.FAIL.getCode());
            casePlanService.updateByPrimaryKeySelective(casePlan);
        } finally {
            //任务列表移除任务id
            redisUtil.lRemove(KeyCache.CASE_MESSAGE_PLAN_ID_LIST, 0, planId.toString());
            redisUtil.expireDays(KeyCache.CASE_MESSAGE_CASE_IDS + casePlan.getId(), 3);
        }
    }

    private Long insertCasePlan(CasePlan casePlan, List<String> caseIdList) {
        Long orgId = casePlan.getOrgId();
        UserSession userSession = new UserSession();
        userSession.setId(casePlan.getCreateBy());
        userSession.setOrgId(orgId);
        userSession.setName(userService.getNames(userSession.getOrgId()).get(casePlan.getCreateBy()));
        userSession.setDepId(casePlan.getDepId());
        userSession.setTeamId(casePlan.getTeamId());

        OrgSwitch orgSwitch = orgSwitchService.selectOrgSwitchByOrgId(orgId);
        List<Long> caseIds = caseIdList.stream().map(Long::valueOf).collect(Collectors.toList());
        List<CaseContact> caseContactsAll = contactsMapper.selectContactByCaseIds(caseIds);
        List<CaseContact> caseContacts = Lists.newArrayList();
        if (casePlan.getDebtorOwn() != null && casePlan.getDebtorOwn() == 1) {
            List<CaseContact> ownList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 1)
                    .filter(caseContact -> PatternUtils.isPlanMessage(caseContact.getContactMobile()))
                    .collect(Collectors.toList());

            // 最新本人号码
            if (casePlan.getDebtorOwnType() != null &&
                    Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.LAST.getCode())) {
                Map<Long, CaseContact> caseContactMap = ownList.stream().collect(Collectors.groupingBy(
                        CaseContact::getCaseId, Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(CaseContact::getConUpdateTime)), Optional::get)));
                ownList.clear();
                ownList.addAll(caseContactMap.values());
            }
            // 随机本人号码
            if (casePlan.getDebtorOwnType() != null &&
                    Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.RANDOM.getCode())) {
                List<CaseContact> randomCaseContacts = Lists.newArrayList();
                ownList.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> randomCaseContacts.add(v.get(0)));
                ownList.clear();
                ownList.addAll(randomCaseContacts);
            }
            caseContacts.addAll(ownList);
        }
        if (casePlan.getContacts() != null && casePlan.getContacts() == 1) {
            List<CaseContact> contactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 0)
                    .filter(caseContact -> PatternUtils.isPlanMessage(caseContact.getContactMobile()))
                    .collect(Collectors.toList());
            // 除本人外的所有号码类型
            if (StringUtils.isNotBlank(casePlan.getContactTypeIds())) {
                String contactTypeIdStr = casePlan.getContactTypeIds();
                List<Long> contactTypeIds = Stream.of(contactTypeIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
                if (contactTypeIds.size() > 1) {
                    contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
                }
                if (contactTypeIds.size() == 1) {
                    if (!Objects.equals(contactTypeIds.get(0), CasePlanEnums.ContactTypeEnum.ALL.getCode())) {
                        contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
                    }
                }
            }
            caseContacts.addAll(contactsList);
        }

        // 过滤掉已达案件号码发送达上限全局管控的号码信息，进行移除
        List<CaseContact> delContacts = casePlanService.getMobileSmsLimitContact(caseContacts, orgSwitch);
        if (!CollectionUtils.isEmpty(delContacts)) {
            caseContacts.removeAll(delContacts);
        }

        // 按手机号分组，随机选择一个案件
        List<CaseContact> param = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(caseContacts)) {
            caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile))
                    .forEach((k, v) -> param.add(v.get(0)));
        }

        if(param.size()<1){
            log.warn("创建短信计划计划失败-查询结果为0");
            throw new ApiException("创建短信计划失败-查询结果为0");
        }

        Long duyanPlanId = null;
        Integer start = 0;
        Integer end = 0;
        while (start < param.size()) {
            try {
                end = start + 5000;
                if (end > param.size()) {
                    end = param.size();
                }
                List<CaseContact> subIdList = param.subList(start, end);
                if (CollectionUtils.isEmpty(subIdList)) {
                    continue;
                }

                SmsTemplate smsTemplate = smsTemplateService.selectByPrimaryKey(casePlan.getSmsTplId());
                JSONArray jsonArray = casePlanService.getAssembleJsonArray(casePlan, subIdList, smsTemplate);
                if (start == 0) {
                    duyanPlanId = messageManager.createMessagePlan(jsonArray,smsTemplate, casePlan.getStartType(), casePlan.getPlanStartTime(), orgId);
                    if (duyanPlanId != null){
                        casePlanService.assembleData(jsonArray, subIdList, casePlan, smsTemplate);
                        casePlanRelService.saveCasePlanRelBatch(subIdList.stream().map(CaseContact::getCaseId).distinct().collect(Collectors.toList()),
                            casePlan.getId(), userSession, CasePlanRelEnums.Type.MESSAGE.getCode());
                        log.info("公司{}调用短信平台创建计划成功,duyanPlanId:{}", orgId, duyanPlanId);
                    }
                } else {
                    if (duyanPlanId != null){
                        Thread.sleep(5000);
                        messageManager.appendMessagePlanInfo(jsonArray, smsTemplate, casePlan.getStartType(), casePlan.getPlanStartTime().getTime(), duyanPlanId, orgId);
                        casePlanService.assembleData(jsonArray, subIdList, casePlan, smsTemplate);
                        casePlanRelService.saveCasePlanRelBatch(subIdList.stream().map(CaseContact::getCaseId).distinct().collect(Collectors.toList()),
                            casePlan.getId(), userSession, CasePlanRelEnums.Type.MESSAGE.getCode());
                        log.info("公司{}调用短信平台批量添加号码成功,duyanPlanId:{}", orgId, duyanPlanId);
                    }
                }

                //成功的案件移（留下的则是失败的案件）
                redisUtil.sRemove(KeyCache.CASE_MESSAGE_CASE_IDS + casePlan.getId(),
                        subIdList.stream().map(obj -> String.valueOf(obj.getCaseId())).toArray(String[]::new));
            } catch (Exception e) {
                log.error("短信计划,planId:{},失败！{}", casePlan.getId(), ExceptionUtil.stacktraceToString(e));
                return duyanPlanId;
            } finally {
                start = end;
            }
        }
        return duyanPlanId;
    }
}
