package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 策略执行
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class StrategyExecteJob extends BaseTask {

    /**
     * @Scheduled(cron = "0/10 * * * * ? ")
     */
    @XxlJob("strategyExecteHandler")
    public ReturnT<String> strategyExecteHandler() {
        super.strategyExecute();
        return ReturnT.SUCCESS;
    }
}
