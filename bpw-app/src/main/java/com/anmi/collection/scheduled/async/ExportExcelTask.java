package com.anmi.collection.scheduled.async;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.common.enums.CaseTemplateEnums;
import com.anmi.collection.common.enums.ContactEnums;
import com.anmi.collection.common.enums.template.TemplateFieldEnums;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.constant.SystemConst;
import com.anmi.collection.dto.OpenCaseContactDTO;
import com.anmi.collection.dto.fileStorage.ReadFileInfo;
import com.anmi.collection.entity.requset.file.FileUpload;
import com.anmi.collection.entity.requset.open.CaseImportParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.template.TemplateFieldVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.*;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.service.template.TemplateFieldService;
import com.anmi.collection.utils.CmUtil;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.JSONUtil;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.collection.utils.easyexcel.CaseImportListener;
import com.anmi.collection.utils.easyexcel.EasyExcelUtils;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseTemplate;
import com.anmi.domain.sys.FileStore;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ExportExcelTask {
  @Autowired private StringRedisTemplate stringRedisTemplate;
  @Autowired private CaseTemplateService caseTemplateService;
  @Autowired private FileStoreService fileStoreService;
  @Autowired private CaseService caseService;
  @Autowired private AsyncTaskService asyncTaskService;
  @Autowired protected SystemConfig systemConfig;
  @Autowired private I18nService i18nService;
  @Autowired private RedisUtil redisUtil;
  @Resource private TemplateFieldService templateFieldService;
  @Resource private FileStorageStrategyFactory fileStorageStrategyFactory;

  @Async("taskExecutor")
  public void insertBDB(FileStore store, UserSession user, FileUpload fileUpload) throws IOException {
    InputStream is = null;
    try {
      log.info("storeId:{} BDB插入任务进行中...", store.getId());
      String f = KeyCache.FILE_STORE + store.getId();
      // 关键信息存到redis，以便后面线程任务有依据
      createFileStore(f, user, fileUpload);
      List<String> cellNameList = caseTemplateService.getTemplateNames(store.getTemplateId(),store.getTemplateType(),user.getOrgId(),user.getLanguage());
      FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
      ReadFileInfo readFileInfo = new ReadFileInfo();
      readFileInfo.setAlias(store.getName())
              .setUrl(store.getPath())
              .setBucket(systemConfig.getTemporaryFileBucket());
      is = fileStorageStrategy.readFileStream(readFileInfo);
      EasyExcel.read(
              is,
              new CaseImportListener(redisUtil, store.getId(), cellNameList))
              .sheet(0)
              .headRowNumber(2)
              .doRead();
      redisUtil.lSetFromLeftExpireDays(KeyCache.FILE_LIST, f, 3);
      log.info("storeId:{} 已存到redis文件队列等待下载...", store.getId());
    } catch (Exception e) {
      log.error("storeId:{} 解析文件失败", store.getId(), e);
      fileStoreService.updateFileFail(store.getId());
    } finally {
      if (is != null) {
        is.close();
      }
    }
  }

  public void insertBDBOpen(
      FileStore store, CaseImportParam caseImportParam, UserSession user, FileUpload fileUpload) {
    try {
      log.info("storeId:{} BDB插入任务进行中...", store.getId());
      String f = KeyCache.FILE_STORE + store.getId();
      CaseTemplate caseTemplate =
          caseTemplateService.selectByPrimaryKey(caseImportParam.getTemplate_id());
      i18nService.convertCaseTemplate(Collections.singletonList(caseTemplate),user.getLanguage());
      Map<String, String> fieldMap =
          caseTemplateService.getFieldKeyList(caseTemplate.getFieldJson());
      List<String> keyList = Lists.newArrayList(fieldMap.values());
      log.info("open接口导入案件数: {}，开始存储BDB！", caseImportParam.getContent().size());
      Integer total = 0;
      for (Map<String, String> map : caseImportParam.getContent()) {
        JSONArray jsonArray = new JSONArray();
        for (String key : keyList) {
          String value = map.get(key);
          jsonArray.add(value == null ? null : value);
        }
        redisUtil.hsetExpireDays(KeyCache.FILE_STORE_LINE + store.getId(), String.valueOf(total), JSONUtil.toJsonStr(jsonArray), 3);
        total++;
      }
      log.info("存储数据库成功！");
      createOpenFileStore(f, user, fileUpload, total);
      redisUtil.lSetFromLeftExpireDays(KeyCache.FILE_LIST, f, 3);
      log.info("storeId:{} 已存到redis文件队列等待下载...", store.getId());
    } catch (Exception e) {
      log.error("storeId:{} 解析文件失败", store.getId(), e);
      fileStoreService.updateFileFail(store.getId());
    }
  }

  private void createOpenFileStore(
      String fileStoreKey, UserSession user, FileUpload fileUpload, Integer total) {
    Map map = new HashMap();
    map.put("surplus", String.valueOf(0L));
    map.put("total", String.valueOf(total));
    map.put("success", String.valueOf(0L));
    map.put("error", String.valueOf(0L));
    map.put("user", JSONObject.toJSONString(user));
    map.put("file_upload", JSONObject.toJSONString(fileUpload));
    map.put("total_amount", String.valueOf(0L));
    stringRedisTemplate.opsForHash().putAll(fileStoreKey, map);
    stringRedisTemplate.expire(fileStoreKey, 3, TimeUnit.DAYS);
  }

  @Async("taskExecutor")
  public void insertRedis(FileStore store, UserSession user, FileUpload fileUpload, String ipAddr) throws IOException {
    InputStream is = null;
    try {
      log.info("storeId:{} 开始解析文件...", store.getId());
      String fileStoreKey = getFileStoreName(store.getTemplateType()) + store.getId();
      // 关键信息存到redis，以便后面线程任务有依据
      createFileStore(fileStoreKey, user, fileUpload,ipAddr);
      // 插入redis
      String fileVauleKey = getFileValueKeyName(store.getTemplateType()) + store.getId();
      List<String> cellNameList = caseTemplateService.getTemplateNames(fileUpload.getTemplateId(),store.getTemplateType(),user.getOrgId(),user.getLanguage());
      AnalysisEventListener redisListener = EasyExcelUtils.getListener(insertRedis(store, cellNameList, fileVauleKey), 100);

      FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
      ReadFileInfo readFileInfo = new ReadFileInfo();
      readFileInfo.setAlias(store.getName())
              .setUrl(store.getPath())
              .setBucket(systemConfig.getTemporaryFileBucket());
      is = fileStorageStrategy.readFileStream(readFileInfo);
      EasyExcel.read(
              is,
              redisListener)
              .sheet(0)
              .headRowNumber(2)
              .doRead();
      // 更新总数
      Long total = stringRedisTemplate.opsForList().size(fileVauleKey);
      stringRedisTemplate.opsForHash().put(fileStoreKey, "total", String.valueOf(total));
      // 添加至redis文件队列等待处理
      String fileKeyListName = getFileKeyListName(store.getTemplateType());
      stringRedisTemplate.opsForList().leftPush(fileKeyListName, fileStoreKey);
      log.info("storeId:{} 已添加至redis文件队列等待处理...", store.getId());
    } catch (Exception e) {
      log.error("storeId:{} 解析文件失败", store.getId(), e);
      fileStoreService.updateFileFail(store.getId());
    } finally {
      if (is != null) {
        is.close();
      }
    }
  }

  private Consumer<List<Object>> insertRedis(FileStore store, List<String> cellNameList, String fileVauleKey) {
    return list -> {
      Integer maxCellName = cellNameList == null ? 0 : cellNameList.size();
      List<String> valueStrList = new ArrayList<>();
      for (Object data : list) {
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(data));
        JSONArray values = new JSONArray();
        if (SystemConst.TEMPLATE_TYPE_CASE_CONTACTS == store.getTemplateType()) {
          Map<Integer, String> map = JsonUtils.fromJson(JSON.toJSONString(data), Map.class);
          maxCellName = map.keySet().stream().max(Comparator.comparing(i -> i)).get() + 1;
          maxCellName = maxCellName < 5 ? 5 : maxCellName;
        }
        Boolean allNull = true;
        for (int i = 0; i < maxCellName; i++) {
          Object value = jsonObject.get(i);
          if (value != null) {
            allNull = false;
          }
          values.add(value == null ? null : String.valueOf(value));
        }
        if (allNull) {
          continue;
        }
        valueStrList.add(JSONArray.toJSONString(values));
      }
      redisUtil.lSet(fileVauleKey, valueStrList,24*60*60);
      log.info("存储数据库成功！");
    };
  }

  private void createFileStore(String fileStoreKey, UserSession user, FileUpload fileUpload) {
    createFileStore(fileStoreKey, user, fileUpload, StrUtil.EMPTY);
  }

  private void createFileStore(String fileStoreKey, UserSession user, FileUpload fileUpload, String ipAddr) {
    Map map = new HashMap();
    map.put("surplus", String.valueOf(0L));
    map.put("total", String.valueOf(0L));
    map.put("success", String.valueOf(0L));
    map.put("error", String.valueOf(0L));
    map.put("user", JSONObject.toJSONString(user));
    map.put("file_upload", JSONObject.toJSONString(fileUpload));
    map.put("total_amount", String.valueOf(0L));
    if (StrUtil.isNotBlank(ipAddr)){
      map.put("ip", ipAddr);
    }
    stringRedisTemplate.opsForHash().putAll(fileStoreKey, map);
    stringRedisTemplate.expire(fileStoreKey, 3, TimeUnit.DAYS);
  }

  private String getFileKeyListName(Integer templateType) {
    String fileKey = "";
    if (SystemConst.TEMPLATE_TYPE_CASE_UPDATE == templateType) {
      fileKey = KeyCache.FILE_UPDATE_KEY_LIST;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_REPAYMENT == templateType) {
      fileKey = KeyCache.FILE_REPAYMENT_KEY_LIST;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_CONTACTS == templateType) {
      fileKey = KeyCache.FILE_CONTACTS_KEY_LIST;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_OPERATION_IMPORT == templateType) {
      fileKey = KeyCache.FILE_OPERATION_KEY_LIST;
    } else if (CaseTemplateEnums.TempType.USER_IMPORT.getCode() == templateType) {
      fileKey = KeyCache.FILE_USER_KEY_LIST;
    } else {
      throw new ApiException("模板错误");
    }
    return fileKey;
  }

  private String getFileValueKeyName(Integer templateType) {
    String fileKey = "";
    if (SystemConst.TEMPLATE_TYPE_CASE_UPDATE == templateType) {
      fileKey = KeyCache.FILE_UPDATE_VALUE_LIST;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_REPAYMENT == templateType) {
      fileKey = KeyCache.FILE_REPAYMENT_VALUE_LIST;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_CONTACTS == templateType) {
      fileKey = KeyCache.FILE_CONTACTS_VALUE_LIST;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_OPERATION_IMPORT == templateType) {
      fileKey = KeyCache.FILE_OPERATION_VALUE_LIST;
    } else if (CaseTemplateEnums.TempType.USER_IMPORT.getCode() == templateType) {
      fileKey = KeyCache.FILE_USER_VALUE_LIST;
    }  else {
      throw new ApiException("模板错误");
    }
    return fileKey;
  }

  private String getFileStoreName(Integer templateType) {
    String fileKey = "";
    if (SystemConst.TEMPLATE_TYPE_CASE_UPDATE == templateType) {
      fileKey = KeyCache.FILE_UPDATE_STORE;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_REPAYMENT == templateType) {
      fileKey = KeyCache.FILE_REPAYMENT_STORE;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_CONTACTS == templateType) {
      fileKey = KeyCache.FILE_CONTACTS_STORE;
    } else if (SystemConst.TEMPLATE_TYPE_CASE_OPERATION_IMPORT == templateType) {
      fileKey = KeyCache.FILE_OPERATION_STORE;
    }else if (CaseTemplateEnums.TempType.USER_IMPORT.getCode() == templateType) {
      fileKey = KeyCache.FILE_USER_STORE;
    }  else {
      throw new ApiException("模板错误");
    }
    return fileKey;
  }

  @Async("taskExecutor")
  public void insertRedis(
      FileStore store, UserSession user, FileUpload fileUpload, List<Map<String, String>> content) {
    try {
      String fileStoreKey = getFileStoreName(store.getTemplateType()) + store.getId();
      // 关键信息存到redis，以便后面线程任务有依据
      createFileStore(fileStoreKey, user, fileUpload);
      // 插入redis
      String fileValueKey = getFileValueKeyName(store.getTemplateType()) + store.getId();
      List<String> valueStrList = getValuesFromContent(fileUpload.getTemplateId(),store.getTemplateType(),user.getOrgId(), content,user.getLanguage());
      redisUtil.lSet(fileValueKey,valueStrList, 24*60*60);
      // 更新总数
      Long total = stringRedisTemplate.opsForList().size(fileValueKey);
      stringRedisTemplate.opsForHash().put(fileStoreKey, "total", String.valueOf(total));
      // 添加至redis文件队列等待处理
      String fileKeyListName = getFileKeyListName(store.getTemplateType());
      stringRedisTemplate.opsForList().leftPush(fileKeyListName, fileStoreKey);
      log.info("storeId:{} 已添加至redis文件队列等待处理...", store.getId());
    } catch (Exception e) {
      log.error("storeId:{} 解析文件失败", store.getId(), e);
      fileStoreService.updateFileFail(store.getId());
    }
  }

  private List<String> getValuesFromContent(Long templateId, Integer templateType, Long orgId, List<Map<String, String>> content,String language) {
    if (ObjectUtil.equals(templateId, 0L)){
      Integer type = null;
      if (ObjectUtil.equals(templateType, CaseTemplateEnums.TempType.REPAYMENT_IMPORT.getCode())){
        type = TemplateFieldEnums.Type.REPAYMENT.getCode();
      }
      List<TemplateFieldVO> fieldList = templateFieldService.getFieldList(type,orgId);
      List<String> keyList = fieldList.stream().map(TemplateFieldVO::getField).collect(Collectors.toList());
      return content.stream().map(p->{
        JSONArray valueList = new JSONArray();
        for (String key : keyList) {
          String value = p.get(key);
          valueList.add(value == null ? null : value);
        }
        return JSONArray.toJSONString(valueList);
      }).collect(Collectors.toList());
    } else {
      List<String> valueStrList = new ArrayList<>();
      CaseTemplate caseTemplate = caseTemplateService.selectByPrimaryKey(templateId);
      i18nService.convertCaseTemplate(Collections.singletonList(caseTemplate), language);
      Map<String, String> fieldMap = caseTemplateService.getFieldKeyList(caseTemplate.getFieldJson());
      List<String> keyList = Lists.newArrayList(fieldMap.values());
      for (Map<String, String> map : content) {
        JSONArray jsonArray = new JSONArray();
        for (String key : keyList) {
          String value = map.get(key);
          jsonArray.add(value == null ? null : value);
        }
        valueStrList.add(JSONArray.toJSONString(jsonArray));
      }
      return valueStrList;
    }
  }

  @Async("taskExecutor")
  public void insertAsyncTaskRedis(
      AsyncTask asyncTask, Long orgId, Long orgDeltId, List<String> content) {
    try {
      // 案件编号保存（用作错误数据返回）
      String outSerialNoKey = KeyCache.ASYNC_OUT_SERIAL_NO_LIST + asyncTask.getId();
      stringRedisTemplate
          .opsForSet()
          .add(outSerialNoKey, content.toArray(new String[content.size()]));
      stringRedisTemplate.expire(outSerialNoKey, 3, TimeUnit.DAYS);
      // 查找案件
      List<String> outSerialNoList =
          content.stream().map(s -> s + "#" + orgDeltId).collect(Collectors.toList());
      List<Case> caseList = selectCaseStrList(outSerialNoList);
      List<String> caseIdList =
              caseList.stream().map(c -> c.getId().toString()).collect(Collectors.toList());
      if (CollectionUtils.isEmpty(caseIdList)) {
        asyncTaskService.updateTaskFinish(asyncTask, "案件编号错误，查不到案件");
        return;
      }
      // 加锁，状态保护
      stringRedisTemplate
          .opsForSet()
          .add(
              KeyCache.CASE_PROTECT_EXIST_IDS + orgId,
              caseIdList.toArray(new String[caseIdList.size()]));
      // 任务添加到任务列表
      stringRedisTemplate
          .opsForSet()
          .add(
              KeyCache.CASE_CHANGE_STATUS_TASK_CASES + asyncTask.getId(),
              caseIdList.toArray(new String[caseIdList.size()]));
      stringRedisTemplate
          .opsForList()
          .leftPush(KeyCache.CASE_CHANGE_STATUS_TASK_ID_LIST, asyncTask.getId().toString());
    } catch (Exception e) {
      // 更新任务失败
      log.error("案件批量操作任务,taskId:{},失败！", asyncTask.getId(), e);
      asyncTaskService.updateTaskFinish(asyncTask, null);
    }
  }

  private List<Case> selectCaseStrList(List<String> outSerialNoList) {
    List<List<String>> groupList = CmUtil.splitList(outSerialNoList, 1000);
    List<Case> result = new ArrayList<>();
    for (List<String> group : groupList) {
      Example example = new Example(Case.class);
      example.selectProperties("id,outSerialNo,outSerialTemp");
      example
          .createCriteria()
          .andEqualTo("recovery", CaseEnums.Recovery.NORMAL.getCode())
          .andIn("outSerialNo", group);
      List<Case> caseList = caseService.selectByExample(example);
      result.addAll(caseList);
    }
    return result;
  }

  @Async("taskExecutor")
  public void insertRedisForInvalidContacts(
          AsyncTask asyncTask, Long orgId, Long orgDeltId, List<String> content, List<OpenCaseContactDTO> mobiles) {
    try {
      // 案件编号保存（用作错误数据返回）
      String outSerialNoKey = KeyCache.ASYNC_OUT_SERIAL_NO_LIST + asyncTask.getId();
      stringRedisTemplate
              .opsForSet()
              .add(outSerialNoKey, content.toArray(new String[content.size()]));
      stringRedisTemplate.expire(outSerialNoKey, 3, TimeUnit.DAYS);
      Map<String, String> fieldJson = asyncTask.getFieldJson();
      Map<String, List<OpenCaseContactDTO>> caseContactMap = new HashMap<>();
      Integer type = Integer.parseInt(fieldJson.get("type"));
      // 校验是否存在类型
      List<Integer> typeList =
              ImmutableList.of(
                      ContactEnums.Type.SELF.getCode(),
                      ContactEnums.Type.NOTSELF.getCode(),
                      ContactEnums.Type.APPOINT.getCode());
      if (!typeList.contains(type)) {
        asyncTaskService.updateTaskFinish(asyncTask, "type值不在指定类型中");
        return;
      }
      // 查找案件
      List<String> outSerialNoList =
              content.stream().map(s -> s + "#" + orgDeltId).collect(Collectors.toList());
      List<Case> caseList = selectCaseStrList(outSerialNoList);
      List<String> caseIdList =
              caseList.stream().map(c -> c.getId().toString()).collect(Collectors.toList());
      if (CollectionUtils.isEmpty(caseList)) {
        asyncTaskService.updateTaskFinish(asyncTask, "案件编号错误，查不到案件");
        return;
      }
      if (Objects.equals(type, ContactEnums.Type.APPOINT.getCode())) {
        if (CollectionUtils.isEmpty(mobiles)) {
          asyncTaskService.updateTaskFinish(asyncTask, "类型指定手机号，联系人数组不可为空");
          return;
        }
        caseContactMap = mobiles.stream().collect(Collectors.groupingBy(OpenCaseContactDTO::getOutSerialNo));

        for (Case aCase : caseList) {
          if (caseContactMap.containsKey(aCase.getOutSerialTemp())) {
            List<OpenCaseContactDTO> openCaseContactDTOS = caseContactMap.get(aCase.getOutSerialTemp());
            openCaseContactDTOS.forEach(dto -> dto.setCaseId(aCase.getId()));
          } else {
            // 案件编号有，联系人数组没有
            asyncTaskService.updateTaskFinish(asyncTask, "类型指定手机号，案件编号对应联系人不存在联系人数组中");
            return;
          }
        }
        // 联系人数组有，案件编号没有
        Set<String> outSerialNoSet1 = caseContactMap.keySet();
        Set<String> outSerialNoSet2 = caseList.stream().map(Case::getOutSerialTemp).collect(Collectors.toSet());
        if (!outSerialNoSet1.equals(outSerialNoSet2)) {
          asyncTaskService.updateTaskFinish(asyncTask, "联系人数组中案件编号不存在案件编号数组中");
          return;
        }
        Map<Long, List<OpenCaseContactDTO>> contactMap = new HashMap<>();
        for (Map.Entry<String, List<OpenCaseContactDTO>> entry : caseContactMap.entrySet()) {
          Case aCase = caseList.stream().filter(obj -> obj.getOutSerialTemp().equals(entry.getKey())).findFirst().orElse(null);
          if (aCase != null) {
            contactMap.put(aCase.getId(), entry.getValue());
          }
        }
        String jsonStr = JSONObject.toJSONString(contactMap);
        stringRedisTemplate.opsForValue().set(KeyCache.CASE_REF_CONTACT_TASK + asyncTask.getId(), jsonStr, 3, TimeUnit.DAYS);
      }
      // 任务添加到任务列表
      stringRedisTemplate
              .opsForSet()
              .add(
                      KeyCache.CONTACTS_INVALID_TASK_CASES + asyncTask.getId(),
                      caseIdList.toArray(new String[caseIdList.size()]));
      stringRedisTemplate
              .opsForList()
              .leftPush(KeyCache.CONTACTS_INVALID_TASK_ID_LIST, asyncTask.getId().toString());
    } catch (Exception e) {
      // 更新任务失败
      log.error("联系人无效批量操作任务,taskId:{},失败！", asyncTask.getId(), e);
      asyncTaskService.updateTaskFinish(asyncTask, null);
    }
  }

}
