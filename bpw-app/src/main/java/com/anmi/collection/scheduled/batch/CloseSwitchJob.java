package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 定时关闭外访开关和小程序开关
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class CloseSwitchJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 5 0 * * ?")
     */
    @XxlJob("closeSwitchHandler")
    public ReturnT<String> closeSwitchHandler() throws Exception {
        super.autoCloseSwitch();
        return ReturnT.SUCCESS;
    }
}
