package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.constant.ContactsConstant;
import com.anmi.collection.entity.requset.sys.contacts.ContactsParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.contacts.ContactsVO;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.ContactsMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.ContactsOperationRecordService;
import com.anmi.collection.service.ContactsService;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.user.Contacts;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ContactUpdateStatusTask extends BaseAsyncTask {

    @Autowired private ContactsService contactsService;
    @Autowired private ContactsMapper contactsMapper;
    @Autowired private ContactsOperationRecordService contactsOperationRecordService;

    @XxlJob("contactUpdateStatusHandler")
    public ReturnT<String> contactUpdateStatusHandler() {
        try {
            startAsyncTask(KeyCache.CONTACT_UPDATE_STATUS_TASK_ID_LIST);
        } catch (Exception e) {
            log.error("联系人状态批量操作出现全局异常！", e);
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long taskId) {
        AsyncTask task = asyncTaskService.selectByPrimaryKey(taskId);
        task.setSuccessAmt(0L);
        try {
            String queryString = task.getFieldJson().get("query");
            ContactsParam param = JsonUtils.fromJson(queryString, ContactsParam.class);
            Assert.notNull(param, "参数不能为空");
            String sessionUserStr = task.getFieldJson().get("userSession");
            UserSession userSession = JsonUtils.fromJson(sessionUserStr, UserSession.class);
            Assert.notNull(userSession, "userSession不能为空");
            //1.根据条件查询适合的所有的数据
            param.setOrgId(userSession.getOrgId());
            List<ContactsVO> contactsVOList = contactsService.getContactsListNoPage(param);
            Assert.notEmpty(contactsVOList, "批量修改联系人状态，联系人列表不能为空");
            //获取所有list的id
            List<Long> ids = contactsVOList.stream().map(ContactsVO::getId).collect(Collectors.toList());
            //更新联系人状态
            Example example = new Example(Contacts.class);
            //设置org_id
            example.and().andEqualTo("orgId", userSession.getOrgId());
            //所有要失效的ids
            if (!CollectionUtils.isEmpty(ids)) {
                example.and().andIn("id", ids);
            }
            Contacts contacts = new Contacts();
            //设置更新时间
            contacts.setUpdateTime(new Date());
            //设置更新状态
            contacts.setStatus(param.getNewStatus());
            int count = contactsMapper.updateByExampleSelective(contacts, example);
            if (count < 1) {
                throw new ApiException("根据条件失效联系人数据0条");
            }
            //添加联系人操作记录
            contactsOperationRecordService.addRecord(userSession.getOrgId(),
                        userSession.getId(), count, ContactsConstant.ACTION_STATUS);

            task.setId(taskId);
            task.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
            task.setTotal((long) contactsVOList.size());
            task.setSuccessAmt((long) contactsVOList.size());
            asyncTaskService.updateByPrimaryKeySelective(task);
        } catch (Exception e) {
            log.error("批量操作联系人状态，taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
            asyncTaskService.updateTaskFinish(task, e.getMessage());
        } finally {
            redisUtil.lRemove(KeyCache.CONTACT_UPDATE_STATUS_TASK_ID_LIST, 0, taskId.toString());
        }
    }
}
