package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 申请自动超时
 *
 * <AUTHOR>
 * @date 2024/5/20
 */
public class ApplyAutoTimeoutJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 * * * * ?")
     *
     * @return {@link ReturnT}<{@link String}>
     * @throws Exception 例外
     */
    @XxlJob("applyAutoTimeoutHandler")
    public ReturnT<String> applyAutoTimeoutHandler() throws Exception {
        super.autoApplyTimeout();
        return ReturnT.SUCCESS;
    }
}
