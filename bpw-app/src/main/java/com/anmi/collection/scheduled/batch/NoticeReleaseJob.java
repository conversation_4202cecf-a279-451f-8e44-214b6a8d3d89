package com.anmi.collection.scheduled.batch;

import com.anmi.collection.service.notice.NoticeTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 公告定时发布
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class NoticeReleaseJob {

    @Autowired private NoticeTaskService noticeTaskService;

    /**
     * @Scheduled(cron = "0 *|3 * * * ?")
     */
    @Transactional(rollbackFor = Exception.class)
    @XxlJob("noticeReleaseHandler")
    public ReturnT<String> noticeReleaseHandler() {
        Date date = new Date();
        noticeTaskService.releaseDelayNoticeTask(date);
        return ReturnT.SUCCESS;
    }
}
