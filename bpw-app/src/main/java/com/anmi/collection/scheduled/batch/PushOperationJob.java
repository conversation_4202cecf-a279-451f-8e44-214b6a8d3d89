package com.anmi.collection.scheduled.batch;

import com.alibaba.fastjson.JSON;
import com.anmi.collection.common.enums.CaseOperationEnums;
import com.anmi.collection.dto.PushRule;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseOperationService;
import com.anmi.collection.service.CompanyService;
import com.anmi.collection.service.PushOperationService;
import com.anmi.collection.service.admin.PushStrategyService;
import com.anmi.collection.utils.CmUtil;
import com.anmi.collection.utils.DateUtils;
import com.anmi.domain.cases.CaseOperation;
import com.anmi.domain.sys.PushStrategy;
import com.anmi.domain.user.Company;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/25 18:39
 */@Slf4j
public class PushOperationJob {

    @Resource
    private CompanyService companyService;
    @Resource
    private PushStrategyService pushStrategyService;
    @Resource
    private CaseOperationService caseOperationService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private PushOperationService pushOperationService;

    @XxlJob("pushOperationHandler")
    public ReturnT<String> pushOperationHandler() {
        try {
            handlePushOperation();
        } catch (Exception e) {
            log.error("推送催记处理异常：", e);
        }
        handlePushOperation();
        return ReturnT.SUCCESS;
    }

    public void handlePushOperation() {
        long current = System.currentTimeMillis();
        Set<Long> caseOperationIdSetAll = redisTemplate.opsForZSet().rangeByScore(KeyCache.PUSH_CASE_OPERATION,
          0, current);
        if (CollectionUtils.isEmpty(caseOperationIdSetAll)) {
            return;
        }
        List<List<Long>> subList = CmUtil.splitList(new ArrayList<>(caseOperationIdSetAll), 1000);
        for (List<Long> caseOperationIdSet : subList) {
            log.info("处理推送催记id集合：{}", caseOperationIdSet);
            Example example = new Example(CaseOperation.class);
            example.and().andIn("id", caseOperationIdSet);
            example.selectProperties("id", "orgId");
            List<CaseOperation> operationList = caseOperationService.selectByExample(example);
            List<Long> orgIds = operationList.parallelStream().map(CaseOperation::getOrgId).distinct().collect(Collectors.toList());
            List<Company> companyList = companyService.getCompanyList(orgIds);
            Map<Long, Company> orgMap = companyList.parallelStream().collect(Collectors.toMap(Company::getId, Function.identity()));
            List<Long> strategyIds = companyList.parallelStream().filter(company -> Objects.nonNull(company.getPushStrategyId()))
              .map(Company::getPushStrategyId).distinct().collect(Collectors.toList());
            List<PushStrategy> strategyList = pushStrategyService.getPushStrategyList(strategyIds);
            Map<Long, PushStrategy> strategyMap = strategyList.parallelStream().collect(Collectors.toMap(PushStrategy::getId, Function.identity()));
            List<CaseOperation> pushList = new ArrayList<>();
            operationList.forEach(caseOperation -> {
                // 这里需要实时查询获取最条催记最新的推送状态
                caseOperation = caseOperationService.selectByPrimaryKey(caseOperation.getId());
                // 这条催记处理过了
                if (Objects.equals(caseOperation.getIsHandlePush(), 1)) {
                    return;
                }
                Long orgId = caseOperation.getOrgId();
                Long caseId = caseOperation.getCaseId();
                Company company = orgMap.get(orgId);
                // 有可能在该公司推送已经关闭
                if (Objects.isNull(company.getPushStrategyId())) {
                    return;
                }
                PushStrategy strategy = strategyMap.get(company.getPushStrategyId());
                PushRule pushRule = JSON.parseObject(strategy.getRule(), PushRule.class);
                Integer period = strategy.getPeriod();
                Date startTime = caseOperation.getCreateTime();
                Date endTime = DateUtils.addMinutes(startTime, period);
                List<CaseOperation> periodList = periodCaseOperation(caseId, startTime, endTime);
                List<Long> ids = periodList.stream().map(CaseOperation::getId).collect(Collectors.toList());
                // 更新区间内催记是否处理过推送的标识
                setOperationPushSign(ids, 0);
                // 最新成功的催记
                CaseOperation successOperation = periodList.stream().
                  filter(operation -> Objects.equals(operation.getOutcome(), "SUCCESS"))
                  .findFirst().orElse(null);
                // 最新失败的催记
                CaseOperation failOperation = periodList.stream().
                  filter(operation -> !Objects.equals(operation.getOutcome(), "SUCCESS"))
                  .findFirst().orElse(null);
                // 按照要求时间区间内有成功的就推送最新成功的，没有就推送最新失败
                if (Objects.nonNull(successOperation)) {
                    Integer daySuccessLimit = pushRule.getDaySuccessLimit();
                    Integer monthSuccessLimit = pushRule.getMonthSuccessLimit();
                    Integer daySuccessCount = statisticsPush(0, 0, caseId);
                    Integer monthSuccessCount = statisticsPush(1, 0, caseId);
                    if (monthSuccessCount >= monthSuccessLimit) {
                        // 超过月次数限制，不做推送
                        return;
                    }
                    if (daySuccessCount >= daySuccessLimit) {
                        // 超过日推送次数，不做推送
                        return;
                    }
                    // 没有达到推送限制，进行推送。
                    pushList.add(successOperation);
                    return;
                }
                if (Objects.nonNull(failOperation)) {
                    Integer dayFailLimit = pushRule.getDayFailLimit();
                    Integer monthFailLimit = pushRule.getMonthFailLimit();
                    Integer dayFailCount = statisticsPush(0, 1, caseId);
                    Integer monthFailCount = statisticsPush(1, 1, caseId);
                    if (monthFailCount >= monthFailLimit) {
                        return;
                    }
                    if (dayFailCount >= dayFailLimit) {
                        return;
                    }
                    pushList.add(failOperation);
                }
            });

            // 推送至中间表，打上推送标识，方便统计推送次数
            if (!CollectionUtils.isEmpty(pushList)) {
                pushOperationService.addPushOperation(pushList);
                List<Long> ids = pushList.stream().map(CaseOperation::getId).collect(Collectors.toList());
                setOperationPushSign(ids, 1);
            }
        }

        redisTemplate.opsForZSet().removeRangeByScore(KeyCache.PUSH_CASE_OPERATION, 0, current);
    }

    void setOperationPushSign(List<Long> caseOperationIds, Integer signType) {
        if (CollectionUtils.isEmpty(caseOperationIds)) {
            return;
        }
        Example example = new Example(CaseOperation.class);
        example.and().andIn("id", caseOperationIds);
        CaseOperation operation = new CaseOperation();
        if (signType == 0) {
            operation.setIsHandlePush(1);
        }
        if (signType == 1) {
            operation.setIsPush(1);
        }
        operation.setUpdateTime(new Date());
        caseOperationService.updateByExampleSelective(operation, example);
    }

    List<CaseOperation> periodCaseOperation(Long caseId, Date startTime, Date endTime) {
        Example example = new Example(CaseOperation.class);
        example.and().andEqualTo("caseId", caseId)
                .andIn("submitType", Lists.newArrayList(CaseOperationEnums.SubmitType.ROBOT_POINT.getCode(),
                        CaseOperationEnums.SubmitType.ROBOT.getCode()))
                .andGreaterThanOrEqualTo("createTime", startTime)
                .andLessThan("createTime", endTime)
                .andIsNotNull("caller")
                .andIsNull("isHandlePush");
        example.orderBy("id").desc();
        List<CaseOperation> caseOperations = caseOperationService.selectByExample(example);
        return caseOperations;
    }

    Integer statisticsPush(Integer dateType, Integer resultType, Long caseId) {
        Date startTime = null;
        Date endTime = null;
        // 按日统计
        if (dateType == 0) {
            startTime = DateUtils.getStartDateTime(new Date());
            endTime = DateUtils.getEndTimeOfDay(new Date());
        }
        // 按月统计
        if (dateType == 1) {
            startTime = DateUtils.getStartTimeOfMonth(new Date());
            endTime = DateUtils.getEndTimeOfMonth(new Date());
        }
        Example example = new Example(CaseOperation.class);
        example.and().andEqualTo("caseId", caseId)
                .andIn("submitType", Lists.newArrayList(CaseOperationEnums.SubmitType.ROBOT_POINT.getCode(),
                        CaseOperationEnums.SubmitType.ROBOT.getCode()))
                .andGreaterThanOrEqualTo("createTime", startTime)
                .andLessThanOrEqualTo("createTime", endTime)
                .andEqualTo("isPush", 1);
        if (resultType == 0) {
            example.and().andEqualTo("outcome", "SUCCESS");
        }
        if (resultType == 1) {
            example.and().andNotEqualTo("outcome", "SUCCESS");
        }
        int count = caseOperationService.selectCountByExample(example);
        return count;
    }
}
