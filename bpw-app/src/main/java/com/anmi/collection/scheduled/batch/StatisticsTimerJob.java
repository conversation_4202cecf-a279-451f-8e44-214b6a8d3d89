package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 用户数据统计
 *
 * <AUTHOR>
 * @date 2024/5/20
 */
public class StatisticsTimerJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 5 0 * * ?")
     *
     * @return {@link ReturnT}<{@link String}>
     * @throws Exception 例外
     */
    @XxlJob("statisticsTimerHandler")
    public ReturnT<String> statisticsTimerHandler() throws Exception {
        super.autoStatistics();
        return ReturnT.SUCCESS;
    }
}
