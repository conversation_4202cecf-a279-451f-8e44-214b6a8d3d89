package com.anmi.collection.scheduled.async.filestore;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.anmi.collection.common.enums.CaseTemplateEnums;
import com.anmi.collection.common.enums.template.TemplateFieldEnums;
import com.anmi.collection.entity.requset.file.FileUpload;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.entity.response.template.TemplateFieldVO;
import com.anmi.collection.mapper.OrgSwitchMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseService;
import com.anmi.collection.service.template.TemplateFieldService;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.JSONUtil;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.domain.base.DictionaryEntity;
import com.anmi.domain.cases.CaseRepayment;
import com.anmi.domain.cases.CaseTemplate;
import com.anmi.domain.cases.OrgSwitch;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class CaseRepaymentTask extends BaseFileStoreTask {

  public static final int READ_SIZE = 300; // 读取bdb的个数

  @Autowired private OrgSwitchMapper orgSwitchMapper;
  @Autowired private CaseService caseService;
  @Resource private TemplateFieldService templateFieldService;

  @XxlJob("caseRepaymentHandler")
  public ReturnT<String> caseRepaymentHandler() {
    try {
      LogbackUtil.insertTrace(LogbackUtil.generateTrace());
      startFileStore(KeyCache.FILE_REPAYMENT_KEY_LIST);
    } catch (Exception e) {
      log.error("还款导入出现全局异常！", e);
    }finally {
      LogbackUtil.removeTrace();
    }
    return ReturnT.SUCCESS;
  }

  @Override
  protected void execute(String fileKey) {
    Long storeId = Long.valueOf(StringUtils.substringAfterLast(fileKey, "_"));
    log.info("=======开始处理还款导入数据：=======storeId:{}", storeId);
    // 读取文件信息
    Map<Object, Object> storeMap = redisUtil.hget(fileKey);
    Integer success = Integer.valueOf(storeMap.get("success").toString());
    Integer error = Integer.valueOf(storeMap.get("error").toString());
    Integer surplus = Integer.valueOf(storeMap.get("surplus").toString());
    Integer total = Integer.valueOf(storeMap.get("total").toString());
    try {
      UserSession userSession = null;
      FileUpload fileUpload = null;
      String userMsg = storeMap.get("user").toString();
      if (!com.anmi.collection.utils.StringUtils.isEmpty(userMsg)) {
        userSession = JSON.parseObject(userMsg, UserSession.class);
      }
      String fileUploadMsg = storeMap.get("file_upload").toString();
      if (!CommonUtils.isEmpty(fileUploadMsg)) {
        fileUpload = JSON.parseObject(fileUploadMsg, FileUpload.class);
      }

      List<TemplateFieldVO> fieldList = templateFieldService.getFieldList(TemplateFieldEnums.Type.REPAYMENT.getCode(), userSession.getOrgId());
      List<DictionaryEntity> dictionaryEntitys = fieldList.stream().map(p -> {
        DictionaryEntity entity = new DictionaryEntity();
        entity.setName(p.getName());
        entity.setValue(p.getField());
        entity.setRequired(ObjectUtil.equals(p.getRequired(), TemplateFieldEnums.Required.YES.getCode()) ? "true" : "false");
        if (ObjectUtil.equals(TemplateFieldEnums.FieldType.TXT.getCode(), p.getFieldType())) {
          entity.setType("String");
        } else if (ObjectUtil.equals(TemplateFieldEnums.FieldType.NUM.getCode(), p.getFieldType())) {
          entity.setType("Money");
        } else if (ObjectUtil.equals(TemplateFieldEnums.FieldType.DATE.getCode(), p.getFieldType())) {
          entity.setType("Date");
        }

        if (ObjectUtil.equals("repayment_type", p.getField())||ObjectUtil.equals("repayment_from", p.getField())) {
          entity.setMaxLength(100);
        } else if (ObjectUtil.equals("capital", p.getField())||ObjectUtil.equals("interest", p.getField())
                ||ObjectUtil.equals("repayment_amount", p.getField())) {
          entity.setMaxLength(20);
        } else if (ObjectUtil.equals("repayment_card_no", p.getField())) {
          entity.setMaxLength(30);
        }
        return entity;
      }).collect(Collectors.toList());
      Map<String, DictionaryEntity> fieldsMap = new HashMap<>();
      dictionaryEntitys.forEach(p->fieldsMap.put(p.getValue(), p));

      OrgSwitch orgSwitch = orgSwitchMapper.selectOrgSwitchByOrgId(userSession.getOrgId());

      CaseTemplate caseTemplate = new CaseTemplate();
      caseTemplate.setId(0L);
      caseTemplate.setTempType(CaseTemplateEnums.TempType.REPAYMENT_IMPORT.getCode());
      caseTemplate.setOrgId(userSession.getOrgId());

      List<String> results;
      String valueListKey = KeyCache.FILE_REPAYMENT_VALUE_LIST + storeId;
      while (surplus < total) {
        Integer end = surplus + READ_SIZE - 1;
        if (end > total - 1) {
          end = total - 1;
        }
        results = redisUtil.lGet(valueListKey, surplus, end);

        // 案件更新数据处理
        List<CaseRepayment> repaymentList =
            fileStoreService.caseRepaymentHandle(
                storeId, results, userSession, caseTemplate, fieldsMap, fileUpload.getOrgDeltId());
        // 更新redis的file的扩展数据
        if (!CollectionUtils.isEmpty(repaymentList)) {
          success = success + repaymentList.size();
          error = error + results.size() - repaymentList.size();
          Map<Object,Object> values = new HashMap<>();
          values.put("success", String.valueOf(success));
          values.put("error", String.valueOf(error));
          redisUtil.hset(fileKey, values);
        }
        surplus = end + 1;
        redisUtil.hset(fileKey, "surplus", String.valueOf(surplus));
        // 还款金额大于等于贷款金额，自动结案
        if (orgSwitch.getAutoEndSwitch() == 1 && !CollectionUtils.isEmpty(repaymentList)) {
          Set<Long> caseIdSet =
              repaymentList.stream().map(c -> c.getCaseId()).collect(Collectors.toSet());
          log.info("还款金额大于等于贷款金额，自动结案,storeId:{},caseIdSet:{}",storeId, JSONUtil.toJsonStr(caseIdSet));
          caseService.autoEnd(userSession, Lists.newArrayList(caseIdSet));
        }
        // 睡眠一下
        Thread.sleep(100);
      }
      // 数据全部更新完成，生成错误excel
      fileStoreService.handleFileStore(storeId, caseTemplate, success, 0l, total, StrUtil.EMPTY);
      // 删除redis数组缓存
      redisUtil.lRemove(KeyCache.FILE_REPAYMENT_KEY_LIST, 0, fileKey);
      redisUtil.expireDays(fileKey, 3);
      redisUtil.del(valueListKey);
      log.info("=======结束处理还款导入数据：=======storeId:{}", storeId);
    } catch (Exception e) {
      log.error("storeId:{} 还款导入数据出现异常！{}", storeId, ExceptionUtil.stacktraceToString(e));
      // 处理失败文件转存到另一个list
      redisUtil.lRemove(KeyCache.FILE_REPAYMENT_KEY_LIST, 0, fileKey);
      redisUtil.lSetFromLeft(KeyCache.FILE_REPAYMENT_FAIL_LIST, fileKey);
      // 更新fileStore状态为失败
      fileStoreService.updateFileFail(storeId, total, success, 0l);
    }
  }
}
