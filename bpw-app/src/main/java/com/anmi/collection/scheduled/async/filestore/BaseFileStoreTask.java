package com.anmi.collection.scheduled.async.filestore;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.service.CompanyService;
import com.anmi.collection.service.FileStoreService;
import com.anmi.collection.utils.JSONUtil;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.domain.sys.FileStore;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 2023/3/30
 * BaseFileStoreTask
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseFileStoreTask {

    @Autowired protected RedisUtil redisUtil;
    @Autowired protected FileStoreService fileStoreService;
    @Autowired protected SystemConfig systemConfig;
    @Autowired protected CompanyService companyService;
    @Autowired protected RedissonClient redissonClient;

    /**
     * 文件导入任务，入口
     */
    protected void startFileStore(String redisKey) throws Exception{
        List<String> needExecuteFileStoreIdsWithPrefix = needExecuteFileStores(redisKey);

        needExecuteFileStoreIdsWithPrefix.forEach(fileStoreIdWithPrefix->{
            log.info("======={}=======fileStoreId:{} start", redisKey, fileStoreIdWithPrefix);
            try {
                execute(fileStoreIdWithPrefix);
            } catch (Exception e) {
                log.error("======={}=======fileStoreId:{} 异常:{}", redisKey, fileStoreIdWithPrefix, ExceptionUtil.stacktraceToString(e));
            }
            log.info("======={}=======fileStoreId:{} end", redisKey, fileStoreIdWithPrefix);
        });
    }

    /**
     * 获取需要执行的文件导入任务id
     *
     * @param redisKey redis键
     * @return {@link List}<{@link String}>
     */
    private List<String> needExecuteFileStores(String redisKey) throws Exception{
        List<String> fileStoreIdsWithPrefix = redisUtil.lGet(redisKey, 0, -1);
        if (ObjectUtil.isEmpty(fileStoreIdsWithPrefix)){
            return new ArrayList<>();
        }

        Thread.sleep(1000);
        List<Long> fileStoreIds = fileStoreIdsWithPrefix.stream().map(fileStoreIdStr->Long.valueOf(StringUtils.substringAfterLast(fileStoreIdStr, "_"))).collect(Collectors.toList());
        List<FileStore> fileStores = fileStoreService.selectFileStores(fileStoreIds);

        // 清除redis多余的文件导入id
        cleanRedisSurplusFileStoreId(redisKey, fileStoreIdsWithPrefix, fileStores);

        if (ObjectUtil.isEmpty(fileStores)){
            return new ArrayList<>();
        }

        return fileStoreIdsWithPrefix;
    }

    /**
     * 清除redis多余的文件导入id
     *
     * @param redisKey   redis键
     * @param fileStoreIdsWithPrefix 文件导入id(带前缀)
     * @param fileStores  文件导入记录
     */
    private void cleanRedisSurplusFileStoreId(String redisKey, List<String> fileStoreIdsWithPrefix, List<FileStore> fileStores) {
        // 清理redis中存在，mysql中不存在的数据
        List<String> needCleans = fileStoreIdsWithPrefix.stream().filter(fileStoreIdStr -> fileStores.stream().noneMatch(fileStore -> ObjectUtil.equal(Long.valueOf(StringUtils.substringAfterLast(fileStoreIdStr, "_")), fileStore.getId()))).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(needCleans)){
            log.info("{},redis中存在，mysql中不存在的文件上传:{}", redisKey, JSONUtil.toJsonStr(needCleans));
            needCleans.forEach(fileStoreIdStr->redisUtil.lRemove(redisKey,0,fileStoreIdStr));
        }
    }

    /**
     * 单条文件导入的执行，具体逻辑在子类中实现
     *
     * @param fileStoreIdWithPrefix 文件导入id(带前缀)
     */
    protected abstract void execute(String fileStoreIdWithPrefix);
}
