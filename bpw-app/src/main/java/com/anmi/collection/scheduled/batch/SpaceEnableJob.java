package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 存储空间启用
 *
 * <AUTHOR>
 * @date 2022/11/24
 */
public class SpaceEnableJob extends BaseTask {

  /**
   * @Scheduled(cron = "0 30 1 * * ?")
   */
  @XxlJob("spaceEnableHandler")
  public ReturnT<String> spaceEnableHandler() {
    super.spaceEnable();
    return ReturnT.SUCCESS;
  }
}
