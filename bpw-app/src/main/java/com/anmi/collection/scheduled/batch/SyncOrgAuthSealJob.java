package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 同步渠道商印章信息
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class SyncOrgAuthSealJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 0 1 * * ?")
     */
    @XxlJob("syncOrgAuthSealHandler")
    public ReturnT<String> syncOrgAuthSealHandler() {
        super.autoTransferOrgAuthSeal();
        return ReturnT.SUCCESS;
    }
}
