package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 清空已停止合作的总公司callBack回调
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class CleanOrgCallbackJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 5 0 * * ?")
     */
    @XxlJob("cleanOrgCallbackHandler")
    public ReturnT<String> cleanOrgCallbackHandler() {
        super.autoCleanOrgCallback();
        return ReturnT.SUCCESS;
    }
}
