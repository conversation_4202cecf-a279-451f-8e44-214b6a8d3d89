package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.ContactEnums;
import com.anmi.collection.dto.OpenCaseContactDTO;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseService;
import com.anmi.collection.service.ContactsService;
import com.anmi.collection.service.UserService;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.Case;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public class ContactsInvalidTask extends BaseAsyncTask {

    @Autowired private UserService userService;
    @Autowired private CaseService caseService;
    @Autowired private ContactsService contactsService;

    @XxlJob("contactsInvalidHandler")
    public ReturnT<String> contactsInvalidHandler() {
        try{
            startAsyncTask(KeyCache.CONTACTS_INVALID_TASK_ID_LIST);
        } catch (Exception e) {
            log.error("处理联系人无效更新出现全局异常！", e);
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long taskId) {
        AsyncTask asyncTask = asyncTaskService.selectByPrimaryKey(taskId);
        List<String> caseIdListTemp = Lists.newArrayList(redisUtil.sGet(KeyCache.CONTACTS_INVALID_TASK_CASES + taskId));
        List<Long> caseIdList = caseIdListTemp.stream().map(Long::valueOf).collect(Collectors.toList());
        Map<String, String> fieldJson = asyncTask.getFieldJson();
        Integer type = Integer.valueOf(fieldJson.get("type"));
        Map<Long, List<OpenCaseContactDTO>> caseContactMap = new HashMap<>();
        if (Objects.equals(type, ContactEnums.Type.APPOINT.getCode())) {
            caseContactMap =  JSONObject.parseObject(
                    redisUtil.get((KeyCache.CASE_REF_CONTACT_TASK + taskId)), new TypeReference<Map<Long, List<OpenCaseContactDTO>>>(){});
        }

        UserSession userSession = new UserSession();
        userSession.setOrgId(asyncTask.getOrgId());
        userSession.setId(asyncTask.getCreateBy());
        userSession.setName(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));

        try {
            int start = 0;
            int end = 0;
            while (start < caseIdList.size()) {
                end = start + 500;
                if (end > caseIdList.size()) {
                    end = caseIdList.size();
                }
                List<Long> subIdList = caseIdList.subList(start, end);
                List<Case> caseList = caseService.selectByIdList(subIdList);
                if (CollectionUtils.isEmpty(caseList)) {
                    continue;
                }
                // 设置联系人无效
                contactsService.contactsInvalid(caseList, caseContactMap, asyncTask);
                // 更新任务成功数
                asyncTaskService.updateTaskSuccessAmt(asyncTask, (long) caseList.size());
                // 成功的案件移（留下的则是失败的案件）
                List<String> ids = subIdList.stream().map(String::valueOf).collect(Collectors.toList());
                redisUtil.sRemove(KeyCache.CONTACTS_INVALID_TASK_CASES + asyncTask.getId(),
                                    ids.toArray(new String[subIdList.size()]));
                if (AsyncTaskEnums.CreateType.OPEN_API.getCode().equals(asyncTask.getCreateType())) {
                    List<String> outSerialNoList =
                            caseList.stream().map(Case::getOutSerialTemp).collect(Collectors.toList());
                    redisUtil.sRemove(KeyCache.ASYNC_OUT_SERIAL_NO_LIST + asyncTask.getId(),
                                    outSerialNoList.toArray(new String[outSerialNoList.size()]));
                }
                if (Objects.equals(type, ContactEnums.Type.APPOINT.getCode()) && !org.springframework.util.CollectionUtils.isEmpty(caseContactMap)) {
                    subIdList.forEach(caseContactMap.keySet()::remove);
                    String jsonStr = JSONObject.toJSONString(caseContactMap);
                    redisUtil.set(KeyCache.CASE_REF_CONTACT_TASK + taskId, jsonStr, 3, TimeUnit.DAYS);
                }
                start = end;
            }
            asyncTaskService.updateTaskFinish(asyncTask, null);
        } catch (Exception ex) {
            asyncTaskService.updateTaskFinish(asyncTask, ex.getMessage());
            log.error("联系人无效批量操作任务，taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(ex));
        } finally {
            // 全部完成后删除联系人号码
            if (CollectionUtil.isEmpty(caseContactMap)) {
                redisUtil.del(KeyCache.CASE_REF_CONTACT_TASK + taskId);
            }
            redisUtil.lRemove(KeyCache.CONTACTS_INVALID_TASK_ID_LIST, 0, taskId.toString());
        }
    }
}
