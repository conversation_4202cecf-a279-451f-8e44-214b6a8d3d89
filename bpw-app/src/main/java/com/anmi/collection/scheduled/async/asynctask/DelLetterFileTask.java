package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.SpaceEnums;
import com.anmi.collection.constant.LetterConstant;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.OrgSpaceService;
import com.anmi.collection.service.letter.LetterService;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.OSSClientUtil;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.letter.Letter;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 删除函件
 */
@Slf4j
public class DelLetterFileTask extends BaseAsyncTask {

    @Resource private LetterService letterService;
    @Resource private OrgSpaceService orgSpaceService;

    @XxlJob("delLetterFileHandler")
    public ReturnT<String> delLetterFileHandler() {
        try {
            startAsyncTask(KeyCache.DEL_LETTER_FILE_TASK);
        } catch (Exception e) {
            log.error("删除函件出现全局异常！",e);
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long taskId) {
        try {
            AsyncTask task  = asyncTaskService.selectByPrimaryKey(taskId);
            if (task == null || !AsyncTaskEnums.Status.ING.getCode().equals(task.getStatus())) {
                throw new ApiException("任务不存在或者任务状态不是执行中");
            }
            Set<String> fileIdsStr = redisUtil.sGet(KeyCache.DEL_LETTER_FILE_ID_PREFIX + taskId);
            assert fileIdsStr != null;
            List<Long> fileIds = fileIdsStr.stream().map(Long::valueOf).collect(Collectors.toList());
            Example example = new Example(Letter.class);
            example.and().andIn("id", fileIds);
            List<Letter> letters = letterService.selectByExample(example);
            long successCount = 0L;
            for (Letter letter : letters) {
                boolean delResult = OSSClientUtil.deleteFileByFileName(letter.getAlias(), letter.getUrl());
                if (delResult) {
                    // 重新计算使用存储空间、函件存储空间
                    orgSpaceService.subtractUseSpace(letter.getOrgId(), letter.getFileSize(), SpaceEnums.Type.LETTER_FILE.getCode());
                    letter.setUpdateTime(new Date());
                    letter.setUrl(null);
                    letter.setFileSize(null);
                    letter.setShortFileName(null);
                    letter.setAlias(null);
                    letter.setIsDelete(LetterConstant.IS_COMPLETE_DEL);
                    letterService.updateByPrimaryKey(letter);
                    successCount++;
                }
            }
            task.setSuccessAmt(successCount);
            task.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
            task.setUpdateTime(new Date());
            asyncTaskService.updateByPrimaryKeySelective(task);
        } catch (Exception e) {
            log.error("删除函件，taskId:{}，失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
        } finally {
            redisUtil.lRemove(KeyCache.DEL_LETTER_FILE_TASK, 0, taskId.toString());
            redisUtil.del(KeyCache.DEL_LETTER_FILE_ID_PREFIX + taskId);
        }
    }
}
