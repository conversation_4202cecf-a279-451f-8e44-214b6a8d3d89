package com.anmi.collection.scheduled.async.filestore;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.anmi.collection.entity.requset.file.FileUpload;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.init.CacheLoader;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseTemplateService;
import com.anmi.collection.service.CustomSearchFieldService;
import com.anmi.collection.service.I18nService;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.domain.base.DictionaryEntity;
import com.anmi.domain.cases.CaseTemplate;
import com.anmi.domain.cases.CaseUpdate;
import com.anmi.domain.sys.CustomSearchField;
import com.anmi.domain.user.Company;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Slf4j
public class CaseUpdateTask extends BaseFileStoreTask {

    public static final int READ_SIZE = 300;// 读取bdb的个数

    @Autowired private CaseTemplateService caseTemplateService;
    @Autowired private I18nService i18nService;
    @Autowired private CustomSearchFieldService customSearchFieldService;

    @XxlJob("caseUpdateHandler")
    public ReturnT<String> caseUpdateHandler() {
        try{
            LogbackUtil.insertTrace(LogbackUtil.generateTrace());
            startFileStore(KeyCache.FILE_UPDATE_KEY_LIST);
        } catch (Exception e) {
            log.error("处理案件更新出现全局异常！", e);
        }finally {
            LogbackUtil.removeTrace();
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(String fileKey){
        Long storeId = Long.valueOf(StringUtils.substringAfterLast(fileKey, "_"));
        log.info("=======开始处理案件更新数据：=======" + "storeId：" + storeId);
        //读取文件信息
        Map<Object,Object> storeMap = redisUtil.hget(fileKey);
        Integer success=Integer.valueOf(storeMap.get("success").toString());
        Integer error=Integer.valueOf(storeMap.get("error").toString());
        Integer surplus = Integer.valueOf(storeMap.get("surplus").toString());
        Integer total = Integer.valueOf(storeMap.get("total").toString());
        Long totalAmount = Long.valueOf(storeMap.get("total_amount").toString());
        try {
            // 拿到redis中的file清单，再拿到每一个file，根据key去bdb中找对应的值，默认100条
            UserSession userSession = null;
            FileUpload fileUpload = null;
            String userMsg = storeMap.get("user").toString();
            if (!com.anmi.collection.utils.StringUtils.isEmpty(userMsg)) {
                userSession = JSON.parseObject(userMsg, UserSession.class);
            }
            String fileUploadMsg = storeMap.get("file_upload").toString();
            if (!CommonUtils.isEmpty(fileUploadMsg)) {
                fileUpload = JSON.parseObject(fileUploadMsg, FileUpload.class);
            }
            CaseTemplate caseTemplate = caseTemplateService.selectByPrimaryKey(fileUpload.getTemplateId());
            Company company = companyService.selectByPrimaryKey(userSession.getOrgId());
            i18nService.convertCaseTemplate(Collections.singletonList(caseTemplate),company.getLanguage());
            Map<String, DictionaryEntity> fieldsMap = CacheLoader.getAllUpdateFields(userSession.getOrgId(),company.getLanguage());
            //搜索分类字段
            Map<String, Long> searchKeyIdMap=getFieldSortKeyIdMap(caseTemplate.getFieldJson(),userSession.getOrgId());
            Map<Long, Set<String>> searchEnumMap=new HashMap<>();

            List<String> results;
            String valueListKey = KeyCache.FILE_UPDATE_VALUE_LIST + storeId;
            while (surplus < total) {
                Integer end = surplus + READ_SIZE - 1;
                if (end > total - 1) {
                    end = total - 1;
                }
                results = redisUtil.lGet(valueListKey, surplus, end);

                // 案件更新数据处理
                List<CaseUpdate> caseUpdateList = fileStoreService.caseUpdateBatchHandle(storeId, results, userSession,
                        caseTemplate, fileUpload.getOrgDeltId(), fieldsMap);
                // 更新redis的file的扩展数据
                if (!CollectionUtils.isEmpty(caseUpdateList)) {
                    success = success + caseUpdateList.size();
                    error = error + results.size() - caseUpdateList.size();
                    totalAmount = totalAmount + caseUpdateList.stream().filter(l -> l.getAmount() != null).mapToLong(CaseUpdate::getAmount).sum();
                    Map<Object,Object> values = new HashMap<>();
                    values.put("success", String.valueOf(success));
                    values.put("error", String.valueOf(error));
                    values.put("total_amount", String.valueOf(totalAmount));
                    redisUtil.hset(fileKey, values);
                    caseUpdateList.stream().forEach(c->c.getList().stream().forEach(l->{
                        if(l.getKey().startsWith("search_key")){
                            Long searchKeyId=searchKeyIdMap.get(l.getKey());
                            if(searchKeyId!=null){
                                if(searchEnumMap.get(searchKeyId)==null){
                                    searchEnumMap.put(searchKeyId,new HashSet<>());
                                }
                                searchEnumMap.get(searchKeyId).add(l.getValue());
                            }
                        }
                    }));
                }
                surplus = end + 1;
                redisUtil.hset(fileKey, "surplus", String.valueOf(surplus));
                // 睡眠一下
                Thread.sleep(100);
            }
            //搜索枚举保存
            customSearchFieldService.insertEnumsByMap(searchEnumMap);
            //数据全部更新完成，生成错误excel
            fileStoreService.handleFileStore(storeId, caseTemplate, success, totalAmount, total,company.getLanguage());
            //删除redis数组缓存
            redisUtil.lRemove(KeyCache.FILE_UPDATE_KEY_LIST, 0, fileKey);
            redisUtil.expireDays(fileKey, 3);
            redisUtil.del(valueListKey);
            log.info("=======结束处理案件更新导入数据：=======storeId:{}",storeId);
        } catch (Exception e) {
            log.error("storeId:{} 案件更新导入数据出现异常！{}", storeId , ExceptionUtil.stacktraceToString(e));
            // 处理失败文件转存到另一个list
            redisUtil.lRemove(KeyCache.FILE_UPDATE_KEY_LIST, 0, fileKey);
            redisUtil.lSetFromLeft(KeyCache.FILE_UPDATE_FAIL_LIST, fileKey);
            // 更新fileStore状态为失败
            fileStoreService.updateFileFail(storeId,total,success,totalAmount);
        }
    }

    private Map<String, Long> getFieldSortKeyIdMap(Map<String, String> fieldJson,Long orgId){
        Map<String, Long> searchKeyIdMap=new HashMap<>();
        for(String value:fieldJson.values()){
            if(value.startsWith("search_key")){
                CustomSearchField customSearchField =customSearchFieldService.selectBySearchKey(orgId,value);
                if(customSearchField!=null){
                    searchKeyIdMap.put(value,customSearchField.getId());
                }
            }
        }
        return searchKeyIdMap;
    }
}
