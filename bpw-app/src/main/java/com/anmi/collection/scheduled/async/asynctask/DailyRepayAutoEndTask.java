package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.anmi.alfred.dto.CaseRepaymentQueryDto;
import com.anmi.alfred.response.concrete.RepaymentStatisticsResponse;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.CaseEndConfigEnums;
import com.anmi.collection.common.enums.CaseEnums;
import com.anmi.collection.dto.EndInfoDTO;
import com.anmi.collection.entity.requset.cases.CaseMultiQuery;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.init.CacheLoader;
import com.anmi.collection.mapper.StrategyVarMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseService;
import com.anmi.collection.service.OrgSwitchService;
import com.anmi.collection.service.remote.RemoteAlfredService;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.domain.base.DictionaryEntity;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.CaseQueryResult;
import com.anmi.domain.cases.OrgSwitch;
import com.anmi.domain.decision.StrategyVar;
import com.anmi.domain.user.Company;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 每日还款自动结案
 *
 * <AUTHOR>
 */
@Slf4j
public class DailyRepayAutoEndTask extends BaseAsyncTask {
    @Resource private CaseService caseService;
    @Resource private RemoteAlfredService remoteAlfredService;
    @Resource private OrgSwitchService orgSwitchService;
    @Resource private StrategyVarMapper strategyVarMapper;

    @XxlJob("dailyRepayAutoEndHandler")
    public ReturnT<String> dailyRepayAutoEndHandler() {
        try {
            startAsyncTask(KeyCache.DAILY_REPAY_AUTO_END_TASK);
        } catch (Exception e) {
            log.error("每日还款自动结案出现全局异常！",e);
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long taskId) {
        AsyncTask task  = asyncTaskService.selectByPrimaryKey(taskId);
        Map<String, String> fieldJson = task.getFieldJson();
        String repaymentDateStr = fieldJson.get("repaymentTime");
        Date repaymentDate = DateUtil.date(Long.parseLong(repaymentDateStr));
        try {
            if (ObjectUtil.isNull(task) || !AsyncTaskEnums.Status.ING.getCode().equals(task.getStatus())) {
                throw new ApiException("任务不存在或者任务状态不是执行中");
            }
            Set<String> outSerialTempStr = redisUtil.sGet(KeyCache.DAILY_REPAY_AUTO_END_TASK_CASES + taskId);
            assert outSerialTempStr != null;
            List<String> outSerialTempList = new ArrayList<>(outSerialTempStr);
            List<String> failCases = new ArrayList<>();
            OrgSwitch orgSwitch = orgSwitchService.selectOrgSwitchByOrgId(task.getOrgId());
            Long endCompareField = orgSwitch.getEndCompareField();
            StrategyVar strategyVar = strategyVarMapper.selectByPrimaryKey(endCompareField);
            Company company = companyService.selectByPrimaryKey(task.getOrgId());

            int successCount = 0;
            int start = 0;
            int end = 0;
            while (start < outSerialTempList.size()) {
                end = start + 1000;
                if (end > outSerialTempStr.size()) {
                    end = outSerialTempStr.size();
                }
                List<String> subList = outSerialTempList.subList(start, end);

                List<CaseQueryResult> caseQueryResultList = queryCaseInfo(task.getOrgId(), subList);
                if (CollectionUtils.isEmpty(caseQueryResultList)) {
                    failCases.addAll(subList);
                    start = end;
                    continue;
                }
                List<String> notExistSubList = subList.stream()
                        .filter(outSerialTemp -> caseQueryResultList.stream()
                                .noneMatch(caseQueryResult -> Objects.equals(caseQueryResult.getOutSerialTemp(), outSerialTemp)))
                        .collect(Collectors.toList());
                failCases.addAll(notExistSubList);

                Map<Long, CaseQueryResult> caseMap = caseQueryResultList.stream().collect(Collectors.toMap(CaseQueryResult::getId, f -> f));
                List<String> outSerialTemps = caseQueryResultList.stream().map(CaseQueryResult::getOutSerialTemp).collect(Collectors.toList());
                List<RepaymentStatisticsResponse> resultList = queryRepaymentInfo(task.getOrgId(), outSerialTemps, repaymentDate);
                // 查询满足前一日还款金额大于指定比较金额的案件信息
                List<CaseQueryResult> needEndCaseList = queryNeedEndCase(strategyVar, caseMap, resultList, company);
                // 自动结案 结案类型为成功
                if (!CollectionUtils.isEmpty(needEndCaseList)) {
                    UserSession userSession = new UserSession();
                    userSession.setOrgId(task.getOrgId());
                    userSession.setId(0L);
                    EndInfoDTO endInfoDTO = new EndInfoDTO();
                    endInfoDTO.setEndType(CaseEndConfigEnums.EndType.SUCCESS.getCode());
                    caseService.changeStatusUpdate(
                            needEndCaseList, CaseEnums.ChangeStatus.END.getCode(), "open传参还款日还款金额大于等于当前比较金额，自动结案",
                            null, null, userSession,
                            null, task.getId(), endInfoDTO, null);
                }
                caseQueryResultList.removeAll(needEndCaseList);
                failCases.addAll(caseQueryResultList.stream().map(CaseQueryResult::getOutSerialTemp).collect(Collectors.toList()));
                successCount += needEndCaseList.size();
                start = end;
            }

            if (successCount == task.getTotal()){
                task.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
            } else {
                task.setStatus(AsyncTaskEnums.Status.FAIL.getCode());
                task.setDesc("传参还款日累计还款金额小于比较金额或案件不存在");
                Date expireDate = DateUtils.addDays(new Date(), 3);
                task.setExpireTime(expireDate);
                redisUtil.sSet(KeyCache.DAILY_REPAY_AUTO_END_TASK_ERROR_LIST  + task.getId(), 3*24*60*60, failCases.toArray(new String[failCases.size()]));
            }
            task.setSuccessAmt((long)successCount);
            task.setUpdateTime(new Date());
            asyncTaskService.updateByPrimaryKeySelective(task);
        } catch (Exception e) {
            asyncTaskService.updateTaskFinish(task, e.getMessage());
            log.error("每日还款自动结案任务，taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
        } finally {
            redisUtil.lRemove(KeyCache.DAILY_REPAY_AUTO_END_TASK, 0, taskId.toString());
            redisUtil.del(KeyCache.DAILY_REPAY_AUTO_END_TASK_CASES + taskId);
        }
    }

    /**
     * 查询案件信息
     *
     * @param orgId             公司id
     * @param outSerialTemps  委案编号
     * @return 案件信息
     */
    private List<CaseQueryResult> queryCaseInfo(Long orgId, List<String> outSerialTemps) {
        if (CollectionUtils.isEmpty(outSerialTemps)) {
            return Collections.emptyList();
        }
        CaseMultiQuery caseMultiQuery = new CaseMultiQuery();
        caseMultiQuery.setOrgId(orgId);
        caseMultiQuery.setOutSerialNos(outSerialTemps);
        List<CaseQueryResult> caseQueryResultList = caseService.getAllCasesUsingEs(caseMultiQuery);
        for (CaseQueryResult ca : caseQueryResultList) {
            // 自定义字段数据
            if (!CollectionUtils.isEmpty(ca.getFieldSearch())) {
                Map<String, String> fieldJsonMap = new HashMap<>();
                for (String item : ca.getFieldSearch()) {
                    int index = item.indexOf("#");
                    if (index != -1) {
                        String key = item.substring(0, index);
                        String value = item.substring(index + 1);
                        fieldJsonMap.put(key, value);
                    }
                }
                ca.setFieldJson(fieldJsonMap);
            }
        }
        return caseQueryResultList;
    }

    /**
     * 查询指定还款日期的还款信息
     *
     * @param orgId           公司id
     * @param outSerialTemps  委案编号
     * @param repaymentDate  还款时间
     * @return 还款信息
     */
    private List<RepaymentStatisticsResponse> queryRepaymentInfo(Long orgId, List<String> outSerialTemps, Date repaymentDate) {
        if (CollectionUtils.isEmpty(outSerialTemps)) {
            return Collections.emptyList();
        }
        Date repaymentStart = DateUtil.beginOfDay(repaymentDate);
        Date repaymentEnd = DateUtil.endOfDay(repaymentDate);
        CaseRepaymentQueryDto dto = new CaseRepaymentQueryDto();
        dto.setOrgId(orgId);
        dto.setRepaymentStart(repaymentStart);
        dto.setRepaymentEnd(repaymentEnd);
        dto.setStatus(0);
        dto.setApplyStatus(Lists.newArrayList(1,2));
        dto.setOutSerialNos(outSerialTemps);
        return remoteAlfredService.fetchCaseRepaymentByCaseIdFromRemote(dto);
    }

    /**
     * 还款金额大于比较金额的案件信息
     *
     * @param strategyVar 策略变量
     * @param caseMap     案件
     * @param resultList  昨日还款
     * @param company     公司
     * @return 案件信息
     */
    private List<CaseQueryResult> queryNeedEndCase(StrategyVar strategyVar, Map<Long, CaseQueryResult> caseMap, List<RepaymentStatisticsResponse> resultList, Company company) {
        List<CaseQueryResult> caseList = new ArrayList<>();
        for (RepaymentStatisticsResponse result : resultList) {
            CaseQueryResult caseQueryResult = caseMap.get(result.getCaseId());
            if (ObjectUtil.equals(strategyVar.getIsSys(), 1)) {
                try {
                    Class<? extends CaseQueryResult> c = caseQueryResult.getClass();
                    Field[] fields = ReflectUtil.getFields(c);
                    for (Field field : fields) {
                        field.setAccessible(true);
                        String fieldName = field.getName();
                        if (ObjectUtil.equals(fieldName, strategyVar.getValue())) {
                            long fieldValue = (long)field.get(caseQueryResult);
                            if (fieldValue <= result.getRepAmount()) {
                                caseList.add(caseQueryResult);
                                break;
                            }
                        }
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            } else {
                Map<String, String> fieldJson = caseQueryResult.getFieldJson();
                if (fieldJson.containsKey(strategyVar.getValue())) {
                    String valueStr = fieldJson.get(strategyVar.getValue());
                    BigDecimal value = new BigDecimal(valueStr);
                    // 判断当前字段是否为系统字段，系统金额字段乘以1000，非系统金额字段未做处理
                    Map<String, DictionaryEntity> commonFields = CacheLoader.getCommonFields(company.getLanguage());
                    if (!commonFields.containsKey(strategyVar.getValue())) {
                        value = value.multiply(new BigDecimal(1000));
                    }
                    BigDecimal repAmount = new BigDecimal(result.getRepAmount());
                    if (value.compareTo(repAmount) <= 0) {
                        caseList.add(caseQueryResult);
                    }
                }
            }
        }
        return caseList;
    }
}
