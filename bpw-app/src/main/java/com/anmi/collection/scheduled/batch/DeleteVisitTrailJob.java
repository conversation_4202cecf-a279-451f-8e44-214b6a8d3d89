package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 删除超过30天的外访轨迹信息
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class DeleteVisitTrailJob extends BaseTask {

  /**
   * @Scheduled(cron = "0 0 0 * * ?")
   */
  @XxlJob("deleteVisitTrailHandler")
  public ReturnT<String> deleteVisitTrailHandler() throws Exception {
    super.autoDeleteVisitTrail();
    return ReturnT.SUCCESS;
  }
}
