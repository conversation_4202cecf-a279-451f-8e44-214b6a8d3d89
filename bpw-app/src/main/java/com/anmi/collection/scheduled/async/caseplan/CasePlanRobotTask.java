package com.anmi.collection.scheduled.async.caseplan;

import cn.duyan.utils.DuyanCommonUtils;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.common.enums.CasePlanEnums;
import com.anmi.collection.common.enums.ContactFilterTypeEnum;
import com.anmi.collection.common.enums.SiteEnums;
import com.anmi.collection.dto.CaseContact;
import com.anmi.collection.entity.requset.cases.casePlan.CasePlanTmp;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.manager.NewRobotManager;
import com.anmi.collection.manager.dorest.GroupNotifyInvoker;
import com.anmi.collection.manager.dorest.request.WeChatNotifyModel;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.mapper.ContactsMapper;
import com.anmi.collection.mapper.RobotPlanFilterContactRecordMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CasePlanRelService;
import com.anmi.collection.service.SiteVarsMappingService;
import com.anmi.collection.service.external.AbstractExternalStrategy;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.PatternUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CasePlan;
import com.anmi.domain.cases.RobotPlanFilterContactRecord;
import com.anmi.domain.site.SiteVarsMapping;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class CasePlanRobotTask extends BaseCasePlanTask {

    @Autowired private ContactsMapper contactsMapper;
    @Autowired private DuyanManager duyanManager;
    @Autowired private CasePlanRelService casePlanRelService;
    @Autowired private CaseMapper caseMapper;
    @Autowired private SiteVarsMappingService siteVarsMappingService;
    @Autowired private NewRobotManager newRobotManager;
    @Autowired private List<AbstractExternalStrategy> externalStrategyList;
    @Autowired private GroupNotifyInvoker groupNotifyInvoker;
    @Autowired private RobotPlanFilterContactRecordMapper robotPlanFilterContactRecordMapper;

    @XxlJob("casePlanRobotHandler")
    public ReturnT<String> casePlanRobotHandler() {
        try {
            LogbackUtil.insertTrace(LogbackUtil.generateTrace());
            startPlan(KeyCache.CASE_PLAN_ROBOT_PLAN_ID_LIST);
        } catch (Exception e) {
            log.error("处理机器人计划出现全局异常！", e);
        }finally {
            LogbackUtil.removeTrace();
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long planId) {
        Set<String> caseIdList = redisUtil.sGet(KeyCache.CASE_PLAN_ROBOT_CASES + planId);
        CasePlan casePlan = null;
        try {
            if (caseIdList == null || caseIdList.isEmpty()) {
                throw new ApiException("任务对应案件为空");
            }
            casePlan = casePlanService.selectByPrimaryKey(planId);
            if (casePlan == null) {
                throw new ApiException("计划不存在");
            }
            List<String> caseList = Lists.newArrayList(caseIdList);
            Long duyanPlanId = insertCasePlan(casePlan, caseList);
            casePlan.setSelectedTotal((long) caseList.size());
            if (duyanPlanId != null) {
                casePlan.setDuyanPlanId(duyanPlanId);
                casePlan.setStatus(CasePlanEnums.Status.ING.getCode());
                if (ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.NEWROBOT.getCode())){
                    casePlan.setCaseTotal(caseList.size());
                }else {
                    CasePlanTmp casePlanTmp=duyanManager.getPlanInfo(casePlan.getOrgId(),duyanPlanId);
                    casePlan.setCaseTotal(casePlanTmp.getTotalCount().intValue());
                }
            } else {
                casePlan.setStatus(CasePlanEnums.Status.FAIL.getCode());
            }
            casePlanService.updateByPrimaryKeySelective(casePlan);
            log.info("机器人推送结束casePlanId:{}", planId);
        } catch (Exception e) {
            // 任务失败
            log.error("机器人计划,planId:{},失败！{}", planId, ExceptionUtil.stacktraceToString(e));
            casePlan.setStatus(CasePlanEnums.Status.FAIL.getCode());
            casePlanService.updateByPrimaryKeySelective(casePlan);
        } finally {
            //任务列表移除任务id
            redisUtil.expireDays(KeyCache.CASE_PLAN_ROBOT_CASES + casePlan.getId(), 3);
            redisUtil.expireDays(KeyCache.CASE_PLAN_IDCARD_SET + casePlan.getId(), 3);
        }
    }

    /**
     * 异步创建计划调用
     *
     * @param casePlan   案件计划
     * @param caseIdList 案件id列表
     * @return {@link Long}
     */
    private Long insertCasePlan(CasePlan casePlan, List<String> caseIdList) {
        List<Long> caseIds = caseIdList.stream().map(Long::valueOf).collect(Collectors.toList());

        List<Case> cases = caseMapper.selectFieldJson(caseIds);
        List<Case> finalCases = cases;
        // 过滤掉的联系人需记录下来
        Map<Integer, List<CaseContact>> filterCaseContactsMap = new HashMap<>();
        List<Case> noExecCase = externalStrategyList.stream()
                .flatMap(strategy -> strategy.filter(finalCases, filterCaseContactsMap).stream())
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(noExecCase)) {
            log.info("第三方案件校验移除，计划id{},移除案件id{}",casePlan.getId(), noExecCase.stream().map(Case::getId).collect(Collectors.toList()));
            // 移除案件id
            caseIds = caseIds.stream()
                    .filter(id -> noExecCase.stream().noneMatch(c -> c.getId().equals(id)))
                    .collect(Collectors.toList());
            // 移除案件
            cases = cases.stream()
                    .filter(c1 -> noExecCase.stream().noneMatch(c2 -> ObjectUtil.equals(c1.getId(), c2.getId())))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(caseIds) || CollectionUtils.isEmpty(cases)) {
            insertFilterRecords(casePlan.getId(), filterCaseContactsMap, casePlan.getOrgId());
            log.warn("第三方案件校验过滤后无剩余案件，创建机器人计划失败-查询结果为0");
            throw new ApiException("第三方案件校验过滤后无剩余案件，创建机器人计划失败-查询结果为0");
        }

        List<CaseContact> caseContactsAll = contactsMapper.selectContactByCaseIds(caseIds);
        List<CaseContact> caseContacts = Lists.newArrayList();
        if (casePlan.getDebtorOwn() != null && casePlan.getDebtorOwn() == 1) {
            List<CaseContact> ownList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 1)
                    .filter(contacts -> PatternUtils.isPlanPhone(contacts.getContactMobile()))
                    .collect(Collectors.toList());

            List<CaseContact> ownInvalidContactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 1)
                    .filter(contacts -> !PatternUtils.isPlanPhone(contacts.getContactMobile()))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(ownInvalidContactsList)) {
                List<CaseContact> ownInvalidContacts = filterCaseContactsMap.getOrDefault(ContactFilterTypeEnum.INVALID_NUMBER.getCode(), Lists.newArrayList());
                ownInvalidContacts.addAll(ownInvalidContactsList);
                filterCaseContactsMap.put(ContactFilterTypeEnum.INVALID_NUMBER.getCode(), ownInvalidContacts);
            }
            // 最新本人号码
            if (casePlan.getDebtorOwnType() != null &&
                    Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.LAST.getCode())) {
                Map<Long, CaseContact> caseContactMap = ownList.stream().collect(Collectors.groupingBy(
                        CaseContact::getCaseId, Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(CaseContact::getConUpdateTime)), Optional::get)));
                ownList.clear();
                ownList.addAll(caseContactMap.values());
            }
            // 随机本人号码
            if (casePlan.getDebtorOwnType() != null &&
                    Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.RANDOM.getCode())) {
                List<CaseContact> randomCaseContacts = Lists.newArrayList();
                ownList.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> randomCaseContacts.add(v.get(0)));
                ownList.clear();
                ownList.addAll(randomCaseContacts);
            }
            caseContacts.addAll(ownList);
        }
        if (casePlan.getContacts() != null && casePlan.getContacts() == 1) {
            List<CaseContact> contactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 0)
                    .filter(contacts -> PatternUtils.isPlanPhone(contacts.getContactMobile()))
                    .collect(Collectors.toList());

            List<CaseContact> invalidContactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 0)
                    .filter(contacts -> !PatternUtils.isPlanPhone(contacts.getContactMobile()))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(invalidContactsList)) {
                List<CaseContact> invalidContacts = filterCaseContactsMap.getOrDefault(ContactFilterTypeEnum.INVALID_NUMBER.getCode(), Lists.newArrayList());
                invalidContacts.addAll(invalidContactsList);
                filterCaseContactsMap.put(ContactFilterTypeEnum.INVALID_NUMBER.getCode(), invalidContactsList);
            }
            // 除本人外的所有号码类型
            if (StringUtils.isNotBlank(casePlan.getContactTypeIds())) {
                String contactTypeIdStr = casePlan.getContactTypeIds();
                List<Long> contactTypeIds = Stream.of(contactTypeIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
                if (contactTypeIds.size() > 1) {
                    contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
                }
                if (contactTypeIds.size() == 1) {
                    if (!Objects.equals(contactTypeIds.get(0), CasePlanEnums.ContactTypeEnum.ALL.getCode())) {
                        contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
                    }
                }
            }
            caseContacts.addAll(contactsList);
        }

        //按手机号分组选择第一个手机号（相同手机号的第一个案件）
        List<CaseContact> sameMobileFirstCaseContacts = Lists.newArrayList();
        // 相同手机号过滤
        List<CaseContact> sameMobileFilterCaseContacts = Lists.newArrayList();

        // 新机器人需要先获取话术变量，并做相应的校验（此处校验不合理，应该度言来校验）
        List<SiteVarsMapping> duyanVarsMappings = new ArrayList<>();

        Long orgId = casePlan.getOrgId();

        if (ObjectUtil.equals(casePlan.getType(), CasePlanEnums.Type.NEWROBOT.getCode())){
            // 相同手机号，取其中第一个案子参与计划
            if (!CollectionUtils.isEmpty(caseContacts)) {
                Map<String, List<CaseContact>> collect = caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile));
                collect.forEach((k, v) -> {
                    sameMobileFirstCaseContacts.add(v.get(0));
                    if (v.size() > 1) {
                        sameMobileFilterCaseContacts.addAll(v.subList(1, v.size()));
                    }
                });
                // caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile)).forEach((k, v) -> sameMobileFirstCaseContacts.add(v.get(0)));
            }

            if (ObjectUtil.isNotEmpty(sameMobileFilterCaseContacts)) {
                filterCaseContactsMap.put(ContactFilterTypeEnum.SAME_NUMBER.getCode(), sameMobileFilterCaseContacts);
            }

            List<String> duyanSiteVars = newRobotManager.getSiteVars(casePlan.getSiteId(),orgId);
            List<SiteVarsMapping> siteVarsMappings = siteVarsMappingService.getSiteVarsMappings(casePlan.getOrgId(),casePlan.getSiteId());
            duyanVarsMappings = siteVarsMappings.stream().filter(siteVarsMapping -> StrUtil.isNotBlank(siteVarsMapping.getAnmiVar()) && duyanSiteVars.stream().anyMatch(duyanSiteVar ->  ObjectUtil.equals(duyanSiteVar, siteVarsMapping.getDuyanVar())))
                    .collect(Collectors.toList());

            // 有不符合度言话术变量要求的,剔除
            List<CaseContact> varLackCaseContacts = Lists.newArrayList();
            Iterator<CaseContact> iterator = sameMobileFirstCaseContacts.iterator();
            while (iterator.hasNext()) {
                CaseContact caseContact = iterator.next();
                Map<String, Object> caseContactMap = BeanUtils.beanToMap(caseContact);

                Long caseId = caseContact.getCaseId();
                Optional<Case> caseOptional = cases.stream().filter(c -> ObjectUtil.equals(caseId, c.getId())).findFirst();
                Case caseDb = caseOptional.get();
                Map<String, String> fieldJsonMap = caseDb.getFieldJson();

                boolean varValueCheck = duyanVarsMappings.stream().anyMatch(varMapping -> {
                    String anmiVar = varMapping.getAnmiVar();
                    SiteEnums.Var varEmum = SiteEnums.Var.getByAnmiVar(anmiVar);
                    Object value = null;
                    if (ObjectUtil.isNull(varEmum)){
                        value = fieldJsonMap.get(anmiVar);
                    } else {
                        String source = varEmum.getSource();
                        String key = StringUtils.substringAfterLast(source, ".");

                        if (source.startsWith("CaseContact")){
                            value = caseContactMap.get(key);
                        }else if (source.startsWith("CaseVO.fieldJson")){
                            value = fieldJsonMap.get(key);
                        }
                    }
                    return ObjectUtil.isNull(value);
                });

                if (varValueCheck){
                    log.info("话术变量缺失,剔除,案件:{},手机号:{},contact_id:{}",caseId,caseContact.getContactMobile(),caseContact.getContactId());
                    varLackCaseContacts.add(caseContact);
                    iterator.remove();
                }
            }
            if (ObjectUtil.isNotEmpty(varLackCaseContacts)) {
                filterCaseContactsMap.put(ContactFilterTypeEnum.VARS_LACK.getCode(), varLackCaseContacts);
            }
        }else {
            // 先按案件id分组选择案件（每个案件的第一个联系人）
            List<CaseContact> sameCaseIdFirstCaseContacts = Lists.newArrayList();
            // 根据案件id分组查询，并取每个案件的第一个联系人参与计划
            if (!CollectionUtils.isEmpty(caseContacts)) {
                caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> sameCaseIdFirstCaseContacts.add(v.get(0)));
            }

            // 相同手机号，取其中第一个案子参与计划
            if (!CollectionUtils.isEmpty(sameCaseIdFirstCaseContacts)) {
                sameCaseIdFirstCaseContacts.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile)).forEach((k, v) -> sameMobileFirstCaseContacts.add(v.get(0)));
            }
        }

        if (sameMobileFirstCaseContacts.size() < 1) {
            insertFilterRecords(casePlan.getId(), filterCaseContactsMap, casePlan.getOrgId());
            log.warn("创建预测式外呼计划失败-查询结果为0");
            throw new ApiException("创建预测式外呼计划失败-查询结果为0");
        }

        UserSession userSession = new UserSession();
        userSession.setId(casePlan.getCreateBy());
        userSession.setOrgId(orgId);
        userSession.setDepId(casePlan.getDepId());
        userSession.setTeamId(casePlan.getTeamId());
        int start = 0;
        int end = 0;
        Long duyanPlanId = null;


        // 计划关联的案件id
        List<Long> reledCaseIds = new ArrayList<>();

        while (start < sameMobileFirstCaseContacts.size()) {
            String robotPlanCreateError = null;
            try {
                end = start + 2000;
                if (end > sameMobileFirstCaseContacts.size()) {
                    end = sameMobileFirstCaseContacts.size();
                }
                List<CaseContact> subIdList = sameMobileFirstCaseContacts.subList(start, end);
                if (CollectionUtils.isEmpty(subIdList)) {
                    continue;
                }

                List<Long> subRelCaseIds = subIdList.stream().map(CaseContact::getCaseId).distinct().collect(Collectors.toList());
                // 需要新增计划、案件对应关系
                List<Long> needRelCaseIds = subRelCaseIds.stream().filter(subRelCaseId -> reledCaseIds.stream().noneMatch(reledCaseId -> ObjectUtil.equals(subRelCaseId, reledCaseId))).collect(Collectors.toList());


                // 新机器人计划的body
                List<Map<String,Object>> contents = new ArrayList<>();
                if(ObjectUtil.isNotEmpty(subIdList)){
                    for (int i = 0; i < subIdList.size(); i++) {
                        CaseContact caseContact = subIdList.get(i);
                        Long caseId = caseContact.getCaseId();
                        Optional<Case> caseOptional = cases.stream().filter(c -> ObjectUtil.equals(caseId, c.getId())).findFirst();
                        Case caseDb = caseOptional.get();
                        Map<String, String> fieldJsonMap = caseDb.getFieldJson();

                        casePlanService.addContent(caseContact,orgId,fieldJsonMap,contents,duyanVarsMappings, casePlan);
                    }
                }

                if (start >= 0 && start < 2000) {
                    try{
                        duyanPlanId = duyanManager.createPlan(subIdList, casePlan, orgId, contents);
                    }catch (ApiException e){
                        log.info("create plan failed notify...");
                        robotPlanCreateError = e.getMessage();
                        throw e;
                    }
                    //立即回写duyanPlanId，防止计划立即执行的时候回调回来的数据找不到对应的计划
                    casePlan.setDuyanPlanId(duyanPlanId);
                    casePlanService.updateByPrimaryKeySelective(casePlan);
                    if (ObjectUtil.isNotEmpty(needRelCaseIds)){
                        casePlanRelService.saveCasePlanRelBatch(needRelCaseIds, casePlan.getId(), userSession, casePlan.getType());
                        reledCaseIds.addAll(needRelCaseIds);
                    }
                    log.info("公司{}调用度言创建计划成功，duyanPlanId:{}", orgId, duyanPlanId);
                } else {
                    Thread.sleep(5000);
                    duyanManager.batchAdd(orgId, duyanPlanId, subIdList, casePlan.getType(), casePlan, contents);
                    if (ObjectUtil.isNotEmpty(needRelCaseIds)){
                        casePlanRelService.saveCasePlanRelBatch(needRelCaseIds, casePlan.getId(), userSession, casePlan.getType());
                        reledCaseIds.addAll(needRelCaseIds);
                    }
                    log.info("公司{}调用度言批量添加号码成功,duyanPlanId:{}", orgId, duyanPlanId);
                }
                //成功的案件移（留下的则是失败的案件）
                redisUtil.sRemove(KeyCache.CASE_PLAN_ROBOT_CASES + casePlan.getId(),
                    subIdList.stream().map(obj -> String.valueOf(obj.getCaseId())).toArray(String[]::new));
            } catch (Exception e) {
                if(!DuyanCommonUtils.isEmpty(robotPlanCreateError)){
                    if(DuyanCommonUtils.isEmpty(systemConfig.getInternalWxGroupNotifyUrl())){
                        log.error("未配置企业微信群提醒地址：internal.wx.group.notify.url");
                    }else{
                        try{
                            WeChatNotifyModel markdown = WeChatNotifyModel.markdown("机器计划创建失败提醒",
                                    Collections.singletonList(new WeChatNotifyModel.Item(
                                            "机器计划创建失败原因", robotPlanCreateError)));
                            groupNotifyInvoker.invoker(systemConfig.getInternalWxGroupNotifyUrl(),
                                    markdown);
                        }catch (Exception e2){
                            log.error("发送企业微信消息通知失败，duyanPlanId:{}", duyanPlanId);
                        }
                    }
                }
                log.error("insertCasePlan案件计划批量操作任务,planId:{},失败！{}", casePlan.getId(), ExceptionUtil.stacktraceToString(e));
                return duyanPlanId;
            } finally {
                start = end;
            }
        }
        casePlanService.createCasePlanRelContact(casePlan,caseContacts,
            sameMobileFirstCaseContacts.stream().map(CaseContact::getContactMobile).collect(Collectors.toList()));

        insertFilterRecords(casePlan.getId(), filterCaseContactsMap, orgId);
        return duyanPlanId;
    }

    private void insertFilterRecords(Long planId, Map<Integer, List<CaseContact>> filterCaseContactsMap, Long orgId) {
        if (!filterCaseContactsMap.isEmpty()) {
            List<RobotPlanFilterContactRecord> records = new ArrayList<>();
            for (Map.Entry<Integer, List<CaseContact>> entry : filterCaseContactsMap.entrySet()) {
                Integer filterType = entry.getKey();
                List<CaseContact> filterTypeCaseContacts = entry.getValue();
                for (CaseContact caseContact : filterTypeCaseContacts) {
                    RobotPlanFilterContactRecord record = new RobotPlanFilterContactRecord();
                    record.setOrgId(orgId);
                    record.setCaseId(caseContact.getCaseId());
                    record.setContactId(caseContact.getContactId());
                    record.setContactName(caseContact.getContactName());
                    record.setContactMobile(caseContact.getContactMobile());
                    record.setFilterType(filterType);
                    record.setCreateTime(new Date());
                    record.setUpdateTime(new Date());
                    record.setPlanId(planId);
                    records.add(record);
                }
            }
            robotPlanFilterContactRecordMapper.insertList(records);
        }
    }
}
