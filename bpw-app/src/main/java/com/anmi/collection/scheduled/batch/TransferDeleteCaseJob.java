package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 迁移彻底删除的案件
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class TransferDeleteCaseJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 0 2 * * ?")
     */
    @XxlJob("transferDeleteCaseHandler")
    public ReturnT<String> transferDeleteCaseHandler() throws InterruptedException {
        super.autoTransferDeleteCase();
        return ReturnT.SUCCESS;
    }
}
