package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 清理案件表碎片
 *
 * <AUTHOR>
 * @date 2024/5/20
 */
public class CleanFragmentJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 0 3 ? * SUN")
     *
     * @return {@link ReturnT}<{@link String}>
     * @throws Exception 例外
     */
    @XxlJob("cleanFragmentHandler")
    public ReturnT<String> cleanFragmentHandler() throws Exception {
//        super.autoCleanFragment();
        return ReturnT.SUCCESS;
    }
}
