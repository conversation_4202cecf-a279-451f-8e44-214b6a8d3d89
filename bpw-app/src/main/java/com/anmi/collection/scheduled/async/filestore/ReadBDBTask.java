package com.anmi.collection.scheduled.async.filestore;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.anmi.collection.entity.requset.file.FileUpload;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.init.CacheLoader;
import com.anmi.collection.mapper.CaseTemplateMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.scheduled.async.QueueThreadPoolHolder;
import com.anmi.collection.scheduled.async.ThreadPollTask;
import com.anmi.collection.service.CompanyService;
import com.anmi.collection.service.CustomSearchFieldService;
import com.anmi.collection.service.FileStoreService;
import com.anmi.collection.service.I18nService;
import com.anmi.collection.utils.JSONUtil;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.SpringContextHolder;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.domain.base.DictionaryEntity;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseTemplate;
import com.anmi.domain.sys.CustomSearchField;
import com.anmi.domain.sys.FileStore;
import com.anmi.domain.user.Company;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@Slf4j
public class ReadBDBTask extends BaseFileStoreTask {

    private static QueueThreadPoolHolder threadPoolHolder = new QueueThreadPoolHolder("ReadBDBTask", 1,30000);

    @XxlJob("readBDBHandler")
    public ReturnT<String> readBDBHandler() {
        try {
            LogbackUtil.insertTrace(LogbackUtil.generateTrace());
            startFileStore(KeyCache.FILE_LIST);
        } catch (Exception e) {
            log.error("案件导入出现全局异常！", e);
        }finally {
            LogbackUtil.removeTrace();
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(String fileKey){
        Map<Object, Object> storeMap = redisUtil.hget(fileKey);
        Integer dataVolume = Integer.valueOf(storeMap.get("total").toString());

        if (threadPoolHolder.trySubmit(new ReadBDBTaskThread(fileKey), dataVolume)) {
            redisUtil.lRemove(KeyCache.FILE_LIST, 0, fileKey);
        }
    }
}

class ReadBDBTaskThread extends ThreadPollTask {

    private Logger log = LoggerFactory.getLogger(ReadBDBTaskThread.class);

    public static final int READ_BDB = 300;

    private String fileKey;
    private RedisUtil redisUtil;
    private CaseTemplateMapper caseTemplateMapper;
    private FileStoreService fileStoreService;
    private CustomSearchFieldService customSearchFieldService;
    private CompanyService companyService;
    private I18nService i18nService;
    public ReadBDBTaskThread(String fileKey) {
        this.fileKey = fileKey;
        this.redisUtil = SpringContextHolder.getBean(RedisUtil.class);
        this.caseTemplateMapper = SpringContextHolder.getBean(CaseTemplateMapper.class);
        this.fileStoreService = SpringContextHolder.getBean(FileStoreService.class);
        this.customSearchFieldService = SpringContextHolder.getBean(CustomSearchFieldService.class);
        this.companyService = SpringContextHolder.getBean(CompanyService.class);
        this.i18nService = SpringContextHolder.getBean(I18nService.class);
    }

    @Override
    public void run() {
        LogbackUtil.getTrace();
        if (CommonUtils.isEmpty(fileKey)) {
            return;
        }
        try {
            List<List<String>> lines = new ArrayList<>();
            Long storeId = Long.valueOf(StringUtils.substringAfterLast(fileKey, "_"));
            log.info("=======案件导入开始校验错误数据：=======storeId:{}", storeId);
            // 读取文件信息
            Map<Object, Object> storeMap = redisUtil.hget(fileKey);
            Integer success = Integer.valueOf(storeMap.get("success").toString());
            Integer error = Integer.valueOf(storeMap.get("error").toString());
            Integer surplus = Integer.valueOf(storeMap.get("surplus").toString());
            Integer total = Integer.valueOf(storeMap.get("total").toString());
            Long totalAmount = Long.valueOf(storeMap.get("total_amount").toString());
            // 统一从redis读取字段
            FileStore store = fileStoreService.selectByPrimaryKey(storeId);
            Company company = companyService.selectByPrimaryKey(store.getOrgId());
            Map<String, DictionaryEntity> commonFields = CacheLoader.getCommonFields(company.getLanguage());

            try {
                int targetIndex = READ_BDB;
                Map<Long, Set<String>> searchKeyMap = new HashMap<>();
                // 得到对应redis中file的扩展数据
                UserSession userSession = null;
                FileUpload fileUpload = null;
                String userMsg = storeMap.get("user").toString();
                if (!StringUtils.isEmpty(userMsg)) {
                    userSession = JSON.parseObject(userMsg, UserSession.class);
                }
                String fileUploadMsg = storeMap.get("file_upload").toString();
                if (!CommonUtils.isEmpty(fileUploadMsg)) {
                    fileUpload = JSON.parseObject(fileUploadMsg, FileUpload.class);
                }
                // 拿到模版
                // 将模版字段的正确顺序存到redis
                Long tmplId = store.getTemplateId();
                CaseTemplate tmpl = caseTemplateMapper.selectByPrimaryKey(tmplId);
                i18nService.convertCaseTemplate(Collections.singletonList(tmpl),company.getLanguage());
                Map<String, String> values = tmpl.getFieldJson();
                // 对values排个序
                Map<String, String> newValues = new TreeMap<>(Comparator.comparingInt(Integer::valueOf));
                newValues.putAll(values); // 模版正确的顺序
                Map<String, Long> searchFieldIdMap = getSearchFieldIdMap(userSession.getOrgId(), newValues);
                Set<String> insertOutSerialNoSet = new HashSet<>();
                // 循环读取校验
                while (surplus < total) {
                    lines.clear();
                    // 要等一个文件做完再做下一个
                    // 还没处理完
                    if ((total - surplus) < READ_BDB) {
                        targetIndex = total - surplus;
                    }
                    for (int i = 0; i < targetIndex; i++) {
                        String line = (String)redisUtil.hget(KeyCache.FILE_STORE_LINE + storeId, String.valueOf(surplus + i));
                        List<String> lineValues = JSONUtil.parseJsonArray(line,String.class);
                        lines.add(lineValues);
                    }
                    surplus = surplus + targetIndex;
                    // 校验数据
                    List<Case> cases =
                        fileStoreService.parallelHandle(
                            store,
                            fileUpload,
                            userSession,
                            lines,
                            commonFields,
                            searchKeyMap,
                            searchFieldIdMap,
                            newValues,
                            insertOutSerialNoSet);
                    // 处理完成之后，更新已处理数
                    success = success + cases.size();
                    error = error + (lines.size() - cases.size());
                    Long tmpAmount = cases.stream().mapToLong(Case::getAmount).sum();
                    totalAmount = totalAmount + tmpAmount;

                    Map<Object,Object> valueMaps = new HashMap<>();
                    valueMaps.put("success", String.valueOf(success));
                    valueMaps.put("error", String.valueOf(error));
                    valueMaps.put("total_amount", String.valueOf(totalAmount));
                    valueMaps.put("surplus", String.valueOf(surplus));
                    redisUtil.hset(fileKey, valueMaps);

                    // 睡眠一下
                    Thread.sleep(100);
                }
                // 生成错误Excel，更新fileStore
                fileStoreService.handleFileStore(store.getId(), tmpl, success, totalAmount, total, company.getLanguage());
                // 插入自定义字段
                customSearchFieldService.insertEnumsByMap(searchKeyMap);
                log.info("=======结束处理案件导入数据：=======storeId:{}", storeId);
            } catch (Exception e) {
                log.error("=======案件导入数据出现异常：=======storeId:{},{}", storeId, ExceptionUtil.stacktraceToString(e));
                fileStoreService.updateFileFail(storeId, total, success, totalAmount);
                redisUtil.lSetFromLeft(KeyCache.FILE_FAIL_LIST, fileKey);
            }
        } catch (Exception ex) {
            log.error("案件导入异常", ex);
        }
    }

    private Map<String, Long> getSearchFieldIdMap(Long orgId, Map<String, String> newValues) {
        Map<String, Long> searchFieldMap = new HashMap<>();
        if (CommonUtils.isEmpty(newValues) || orgId == null) {
            return searchFieldMap;
        }
        for (String searchKey : newValues.values()) {
            if (searchKey.startsWith("search_key")) {
                CustomSearchField query = new CustomSearchField();
                query.setOrgId(orgId);
                query.setSearchKey(searchKey);
                CustomSearchField localField = customSearchFieldService.selectOne(query);
                if (localField != null) {
                    searchFieldMap.put(searchKey, localField.getId());
                }
            }
        }
        return searchFieldMap;
    }
}
