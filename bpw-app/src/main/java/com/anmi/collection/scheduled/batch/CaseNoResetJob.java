package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 定时重置案件编号序号
 *
 * <AUTHOR>
 */
public class CaseNoResetJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 50 23 31 12 ?")
     */
    @XxlJob("caseNoResetHandler")
    public ReturnT<String> caseNoResetHandler() throws Exception {
        super.caseNoReset();
        return ReturnT.SUCCESS;
    }
}
