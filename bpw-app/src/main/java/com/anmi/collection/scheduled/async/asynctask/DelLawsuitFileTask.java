package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.SpaceEnums;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.OrgSpaceService;
import com.anmi.collection.service.lawsuit.LawsuitFileService;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.OSSClientUtil;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.lawsuit.LawsuitFile;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 删除诉讼材料文件
 */
@Slf4j
public class DelLawsuitFileTask extends BaseAsyncTask {

    @Resource private LawsuitFileService lawsuitFileService;
    @Resource private OrgSpaceService orgSpaceService;

    @XxlJob("delLawsuitFileHandler")
    public ReturnT<String> delLawsuitFileHandler() {
        try {
            startAsyncTask(KeyCache.DEL_LAWSUIT_FILE_TASK);
        } catch (Exception e) {
            log.error("删除诉讼材料出现全局异常！",e);
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long taskId) {
        try {
            AsyncTask task  = asyncTaskService.selectByPrimaryKey(taskId);
            if (task == null || !AsyncTaskEnums.Status.ING.getCode().equals(task.getStatus())) {
                throw new ApiException("任务不存在或者任务状态不是执行中");
            }
            Set<String> fileIdsStr = redisUtil.sGet(KeyCache.DEL_LAWSUIT_FILE_ID_PREFIX + taskId);
            assert fileIdsStr != null;
            List<Long> fileIds = fileIdsStr.stream().map(Long::valueOf).collect(Collectors.toList());
            Example example = new Example(LawsuitFile.class);
            example.and().andIn("id", fileIds);
            List<LawsuitFile> lawsuitFiles = lawsuitFileService.selectByExample(example);
            long successCount = 0L;
            for (LawsuitFile lawsuitFile : lawsuitFiles) {
                boolean delResult = OSSClientUtil.deleteFileByFileName(lawsuitFile.getAlias(), lawsuitFile.getUrl());
                if (delResult) {
                    // 重新计算已使用空间大小、法诉空间大小
                    orgSpaceService.subtractUseSpace(lawsuitFile.getOrgId(), lawsuitFile.getFileSize(), SpaceEnums.Type.LAWSUIT_FILE.getCode());
                    lawsuitFileService.deleteByPrimaryKey(lawsuitFile.getId());
                    successCount++;
                }
            }
            task.setSuccessAmt(successCount);
            task.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
            task.setUpdateTime(new Date());
            asyncTaskService.updateByPrimaryKeySelective(task);
        } catch (Exception e) {
            log.error("删除诉讼材料，taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
        } finally {
            redisUtil.lRemove(KeyCache.DEL_LAWSUIT_FILE_TASK, 0, taskId.toString());
            redisUtil.del(KeyCache.DEL_LAWSUIT_FILE_ID_PREFIX + taskId);
        }
    }
}
