package com.anmi.collection.scheduled.async.caseplan;

import cn.duyan.thread.DuyanThreadExecutor;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CasePlanService;
import com.anmi.collection.service.CompanyService;
import com.anmi.collection.utils.JSONUtil;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.domain.cases.CasePlan;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 2023/3/29
 * BaseCasePlanTask
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseCasePlanTask {

    @Autowired protected RedisUtil redisUtil;
    @Autowired protected CasePlanService casePlanService;
    @Autowired protected SystemConfig systemConfig;
    @Autowired protected CompanyService companyService;
    @Autowired protected RedissonClient redissonClient;
    DuyanThreadExecutor duyanThreadExecutor = new DuyanThreadExecutor(2, 2, "case_plan_push_robot");

    /**
     * 执行计划，入口
     */
    protected void startPlan(String redisKey) throws Exception{
        List<CasePlan> needExecutePlans = needExecutePlans(redisKey);

        needExecutePlans.forEach(casePlan->{
            Long casePlanId = casePlan.getId();
            log.info("======={}=======casePlanId:{} start", redisKey, casePlanId);
            try {
                duyanThreadExecutor.submit(()-> execute(casePlanId));
                redisUtil.lRemove(KeyCache.CASE_PLAN_ROBOT_PLAN_ID_LIST, 0, casePlanId.toString());
            } catch (Exception e) {
                log.error("======={}=======casePlanId:{} 异常:{}", redisKey, casePlanId, ExceptionUtil.stacktraceToString(e));
            }
        });
    }

    /**
     * 获取需要执行的案件计划
     *
     * @param redisKey redis键
     * @return {@link List}<{@link CasePlan}>
     */
    private List<CasePlan> needExecutePlans(String redisKey) throws Exception{
        List<String> casePlanIdStrs = redisUtil.lGet(redisKey, 0, -1);
        if (ObjectUtil.isEmpty(casePlanIdStrs)){
            return new ArrayList<>();
        }

        Thread.sleep(1000);
        List<Long> casePlanIds = casePlanIdStrs.stream().map(Long::parseLong).collect(Collectors.toList());
        List<CasePlan> casePlans = casePlanService.selectCasePlans(casePlanIds);

        // 清除redis多余的案件计划id
        cleanRedisSurplusCasePlanId(redisKey, casePlanIdStrs, casePlans);

        return casePlans;
    }

    /**
     * 清除redis多余的案件计划id
     *
     * @param redisKey   redis键
     * @param planIdStrs 案件计划id字符串
     * @param casePlans  案件计划
     */
    private void cleanRedisSurplusCasePlanId(String redisKey, List<String> planIdStrs, List<CasePlan> casePlans) {
        // 清理redis中存在，mysql中不存在的数据
        List<String> needCleans = planIdStrs.stream().filter(planIdStr -> casePlans.stream().noneMatch(casePlan -> ObjectUtil.equal(Long.parseLong(planIdStr), casePlan.getId()))).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(needCleans)){
            log.info("{},redis中存在，mysql中不存在的案件计划:{}", redisKey, JSONUtil.toJsonStr(needCleans));
            needCleans.forEach(planIdStr->redisUtil.lRemove(redisKey,0,planIdStr));
        }
    }

    /**
     * 单条案件计划的执行，具体逻辑在子类中实现
     *
     * @param casePlanId 案件计划id
     */
    protected abstract void execute(Long casePlanId);
}
