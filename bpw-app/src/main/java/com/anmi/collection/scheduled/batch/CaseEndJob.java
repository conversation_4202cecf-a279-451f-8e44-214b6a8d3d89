package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 案件自动结案
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class CaseEndJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 5 0 * * ?")
     */
    @XxlJob("caseEndHandler")
    public ReturnT<String> caseEndHandler() throws Exception {
        super.autoEndCase();
        return ReturnT.SUCCESS;
    }
}
