package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.anmi.collection.common.enums.AllotCaseEnums;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.DepTeamEnums;
import com.anmi.collection.constant.SystemConst;
import com.anmi.collection.entity.requset.cases.AllotCase;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.mapper.UserMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.scheduled.async.QueueThreadPoolHolder;
import com.anmi.collection.scheduled.async.ThreadPollTask;
import com.anmi.collection.service.*;
import com.anmi.collection.service.businessobject.AllotCaseBO;
import com.anmi.collection.service.businessobject.AllotCaseResultListBO;
import com.anmi.collection.service.businessobject.CommonAllotBO;
import com.anmi.collection.service.businessobject.StrategyByOverdueDaysBO;
import com.anmi.collection.utils.*;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.collection.utils.dict.RedisUtil;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CaseAllot;
import com.anmi.domain.cases.OrgSwitch;
import com.anmi.domain.user.DepTeam;
import com.anmi.domain.user.User;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 修改于2022.07
 */
@Slf4j
public class CaseAllotTask extends BaseAsyncTask {

  private static QueueThreadPoolHolder threadPoolHolder = new QueueThreadPoolHolder("CaseAllotTask", 1);

  @XxlJob("caseAllotHandler")
  public ReturnT<String> caseAllotHandler() {
    try {
      startAsyncTask(KeyCache.CASE_ALLOT_TASK_ID_LIST);
    } catch (Exception e) {
      log.error("案件异步分配出现全局异常！", e);
    }
    return ReturnT.SUCCESS;
  }

  @Override
  protected void execute(Long taskId) {
    Set<String> caseIds = redisUtil.sGet(KeyCache.CASE_ALLOT_TASK_CASES + taskId);
    Integer dataVolume = null;
    if (ObjectUtil.isNotEmpty(caseIds)){
      dataVolume = caseIds.size();
    }

    if (threadPoolHolder.trySubmit(new CaseAllotTaskThread(taskId), dataVolume)) {
      redisUtil.lRemove(KeyCache.CASE_ALLOT_TASK_ID_LIST, 0, taskId.toString());
    }
  }
}

class CaseAllotTaskThread extends ThreadPollTask {

  private final Logger log = LoggerFactory.getLogger(CaseAllotTaskThread.class);
  private final AllotCaseService allotCaseService;
  private final CaseService caseService;
  private final UserService userService;
  private final AsyncTaskService asyncTaskService;
  private final AllotStrategyService allotStrategyService;
  private final UserMapper userMapper;
  private final DepTeamService depTeamService;
  private final Long taskId;
  private final RedisUtil redisUtil;
  private final OrgSwitchService orgSwitchService;

  public CaseAllotTaskThread(Long taskId) {
    this.taskId = taskId;
    this.caseService = SpringContextHolder.getBean(CaseService.class);
    this.allotCaseService = SpringContextHolder.getBean(AllotCaseService.class);
    this.userService = SpringContextHolder.getBean(UserService.class);
    this.asyncTaskService = SpringContextHolder.getBean(AsyncTaskService.class);
    this.allotStrategyService = SpringContextHolder.getBean(AllotStrategyService.class);
    this.userMapper = SpringContextHolder.getBean(UserMapper.class);
    this.depTeamService = SpringContextHolder.getBean(DepTeamService.class);
    this.redisUtil = SpringContextHolder.getBean(RedisUtil.class);
    this.orgSwitchService = SpringContextHolder.getBean(OrgSwitchService.class);
  }

  @Override
  public void run() {
    caseAllot(taskId);
  }

  public void caseAllot(Long taskId) {
    AsyncTask asyncTask = null;
    Set<String> caseSet = redisUtil.sGet(KeyCache.CASE_ALLOT_TASK_CASES + taskId);
    try {
      asyncTask = asyncTaskService.selectByPrimaryKey(taskId);
      if (asyncTask == null) {
        throw new ApiException("异步分配任务不存在");
      }
      if (caseSet == null || caseSet.isEmpty()) {
        throw new ApiException("任务对应案件为空");
      }
      List<String> caseIdList = Lists.newArrayList(caseSet);
      if (AsyncTaskEnums.Type.ALLOT.getCode().equals(asyncTask.getType())) {
        allotByCommon(asyncTask, caseIdList);
      } else {
        // 按策略模板分配（逾期天数）
        allotByStrategy(asyncTask, caseIdList);
      }
      // 更新任务成功
      asyncTaskService.updateTaskFinish(asyncTask, null);
      log.info("=======案件异步分配 end：=======taskId:{}", taskId);
    } catch (Exception e) {
      // 更新任务失败
      assert asyncTask != null;
      asyncTaskService.updateTaskFinish(asyncTask, e.getMessage());
      log.error("案件异步分配任务,taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
    } finally {
      if (caseSet != null && !caseSet.isEmpty()) {
        // 解除状态保护
        assert asyncTask != null;
        redisUtil.sRemove(KeyCache.CASE_PROTECT_EXIST_IDS + asyncTask.getOrgId(), caseSet.toArray());
      }
      // 任务列表移除任务id
      redisUtil.lRemove(KeyCache.CASE_ALLOT_TASK_ID_LIST, 0, taskId.toString());
    }
  }

  private void allotByCommon(AsyncTask asyncTask, List<String> caseIdList) {
    User creator = userService.selectByPrimaryKey(asyncTask.getCreateBy());
    AllotCase allotCase = new AllotCase();
    Map<String, String> fieldJson = asyncTask.getFieldJson();
    allotCase.setType(Integer.parseInt(fieldJson.get("type")));
    allotCase.setIsConjoint(Boolean.parseBoolean(fieldJson.get("isConjoint")));
    allotCase.setAllotToTeamMembers(Boolean.parseBoolean(fieldJson.get("allotToTeamMembers")));
    String userIdStr = asyncTask.getFieldJson().get("userIdList");
    String teamIdStr = asyncTask.getFieldJson().get("teamIdList");
    String depIdStr = asyncTask.getFieldJson().get("depIdList");
    allotCase.setUserIdList(JsonUtils.toList(userIdStr, Long.class));
    allotCase.setTeamIdList(JsonUtils.toList(teamIdStr, Long.class));
    allotCase.setDepList(JsonUtils.toList(depIdStr, Long.class));
    TypeReference<Map<Long, Integer>> typeRef = new TypeReference<Map<Long, Integer>>() {
    };
    allotCase.setAllotProportions(JSON.parseObject(asyncTask.getFieldJson().get("allotProportions"), typeRef));
    allotCase.setAllotObject(Integer.parseInt(fieldJson.get("allotObject")));
    allotCase.setIsAllAgents(Boolean.parseBoolean(fieldJson.get("isAllAgents")));
    allotCase.setAutoRecovery(Boolean.parseBoolean(fieldJson.get("autoRecovery")));
    Integer switchType = StrUtil.isBlank(fieldJson.get("switchType"))? 1: Integer.parseInt(fieldJson.get("switchType"));
    allotCase.setSwitchType(switchType);
    String resultOperationWayStr = fieldJson.get("resultOperationWays");
    List<Integer> resultOperationWays = null;
    if (StrUtil.isNotBlank(resultOperationWayStr)){
      resultOperationWays = StrUtil.split(resultOperationWayStr, ",").stream().map(p -> Integer.valueOf(p)).collect(Collectors.toList());
    }
    allotCase.setResultOperationWays(resultOperationWays);
    if(allotCase.getAutoRecovery()){
      if (StringUtils.isNotBlank(fieldJson.get("autoRecoveryDay"))) {
        Integer autoRecoveryDay =  Integer.parseInt(fieldJson.get("autoRecoveryDay"));
        Date autoRecoveryDate = DateUtils.addDays(DateUtils.getStartTimeOfDate(new Date()), autoRecoveryDay + 1);
        allotCase.setAutoRecoveryDate(autoRecoveryDate.getTime());
      } else {
        allotCase.setAutoRecoveryDate(Long.parseLong(fieldJson.get("autoRecoveryDate")));
      }
    }
    Integer isRule = fieldJson.get("isRule") == null ? 0 : Integer.parseInt(fieldJson.get("isRule"));
    allotCase.setIsRule(isRule);
    allotCase.setRuleId(fieldJson.get("ruleId") == null ? null : Long.parseLong(fieldJson.get("ruleId")));
    List<Case> caseList = selectByTaskId(caseIdList, isRule);
    AllotCaseResultListBO allotCaseBOList = allotCaseService.preAllot(allotCase, caseList);
    Integer allotCaseSize = allotCaseBOList.getAllotCaseBOS().stream().mapToInt(AllotCaseBO::getCaseCount).sum();
    Integer conjointSize = allotCaseBOList.getConjointBOS().stream().mapToInt(AllotCaseBO::getCaseCount).sum();
    Integer allotTotal = allotCaseSize + conjointSize;
    Date autoRecoveryDate = allotCase.getAutoRecovery() ? new Date(allotCase.getAutoRecoveryDate()) : null;
    allotCaseService.doAllotCase(allotCaseBOList.getAllotCaseBOS(), creator, allotCase.getType(), taskId, autoRecoveryDate, allotCase.getResultOperationWays(), allotCase.getSwitchType());
    allotCaseService.doAllotCase(allotCaseBOList.getConjointBOS(), creator, allotCase.getType(), taskId, autoRecoveryDate, allotCase.getResultOperationWays(), allotCase.getSwitchType());
    asyncTask.setSuccessAmt((long)allotTotal);
    if ((allotTotal < caseIdList.size()) && Objects.equals(isRule, 1)) {
      asyncTask.setIgnoreCount((long)(caseIdList.size() - allotTotal));
      asyncTask.setDesc("属地/排名规则与分案对象不匹配；分案算法金额项对应案件信息为空");
    }
  }

  private void allotByCommon(
          AsyncTask asyncTask,
          List<Case> caseList,
          User creator,
          List<Long> userIdList,
          List<Long> teamIdList,
          List<Long> depIdList,
          Boolean allotByTeam,
          Boolean allotByDep,
          Boolean allotToTeamMembers,
          Boolean isConjoint,
          Integer type,
          Map<Long, String> allotProportions,
          Boolean isAllAgents) {
    // 共债案件分配
    Map<Long, List<Case>> allotConjointMap =
            allotStrategyService.getConjointUserCaseMap(
                    caseList, userIdList, isConjoint, creator.getOrgId(), isAllAgents);
    // 按分配方法分配
    // 共债这一步之后，看是否需要在小组之间分案
    Map<Long, List<Case>> idCaseListMap = null;
    if (allotByTeam) {
      // 小组之间需要先进行比例分配
      idCaseListMap =
              allotStrategyService.getIdCaseMapByType(caseList, teamIdList, type, allotProportions);
      // 小组之间按照逻辑分配完后，小组内部继续分配
      Map<Long, List<Case>> tmpMap = new HashMap<>();
      if (type.equals(AllotCaseEnums.Type.COUNT_RATE.getCode())) {
        // 按照数量比例分配时，小组内的成员默认按照数量均分
        type = AllotCaseEnums.Type.COUNT.getCode();
      }
      if (type.equals(AllotCaseEnums.Type.AMOUNT_RATE.getCode())) {
        // 按照金额比例分配时，小组内成员默认按照金额均分
        type = AllotCaseEnums.Type.AMOUNT.getCode();
      }
      if (allotToTeamMembers) {
        for (Long teamId : idCaseListMap.keySet()) {
          List<Long> uIds = userMapper.listUserIdByTeamIds(Arrays.asList(teamId));
          // 组内成员进行分配
          Map<Long, List<Case>> uidCaseMap =
                  allotStrategyService.getIdCaseMapByType(
                          idCaseListMap.get(teamId), uIds, type, allotProportions);
          for (Map.Entry<Long, List<Case>> entry : uidCaseMap.entrySet()) {
            tmpMap.put(entry.getKey(), entry.getValue());
          }
        }
        idCaseListMap = tmpMap;
      }
    } else if (allotByDep) {
      idCaseListMap =
              allotStrategyService.getIdCaseMapByType(caseList, depIdList, type, allotProportions);
    } else {
      idCaseListMap =
              allotStrategyService.getIdCaseMapByType(caseList, userIdList, type, allotProportions);
    }
    // 计算成功数
    Long sum = 0l;
    for (Map.Entry<Long, List<Case>> entry : idCaseListMap.entrySet()) {
      Long targetId = entry.getKey();
      List<Case> list = new ArrayList<>();
      list.addAll(entry.getValue());
      List<Case> conjointList = allotConjointMap.get(targetId);
      if (!CollectionUtils.isEmpty(conjointList)) {
        list.addAll(conjointList);
        allotConjointMap.remove(targetId);
      }
      sum += list.size(); // 共债案件的所有有效案件数
      // 分批分配
      doAllot(
              asyncTask,
              targetId,
              list,
              creator,
              type,
              allotToTeamMembers,
              allotByDep,
              CollectionUtils.isEmpty(conjointList) ? 0 : conjointList.size());
    }
    for (Map.Entry<Long, List<Case>> entry : allotConjointMap.entrySet()) {
      Long targetId = entry.getKey();
      List<Case> list = new ArrayList<>();
      list.addAll(entry.getValue());
      sum += list.size();
      // 分批分配
      doAllot(asyncTask, targetId, list, creator, type, true, allotByDep, list.size());
    }
  }

  private void allotByStrategy(AsyncTask asyncTask, List<String> caseIdList) {
    // 获取任务信息
    User creator = userService.selectByPrimaryKey(asyncTask.getCreateBy());
    Map<String, String> fieldJson = asyncTask.getFieldJson();
    Integer type = Integer.valueOf(fieldJson.get("type"));
    String strategyRule = fieldJson.get("strategyRule");
    String residueRule = fieldJson.get("residueRule");
    // 按逾期天数分配
    // 全部案件
    List<Case> caseList = new ArrayList<>();
    if (AllotCaseEnums.Type.OVERDUE_DAYS.getCode().equals(type)) {
      caseList = selectForOverdueDays(caseIdList);
      overdueDaysStrategy(asyncTask, caseList, strategyRule);
    }
    // 剩余案件分配
    if (StringUtils.isBlank(residueRule)) {
      asyncTask.setIgnoreCount((long) caseList.size());
      return;
    }
    CommonAllotBO commonAllotBO = JsonUtils.fromJson(residueRule, CommonAllotBO.class);
    assert commonAllotBO != null;
    if (commonAllotBO.getType() != null && commonAllotBO.getType() == -1) {
      asyncTask.setIgnoreCount((long) caseList.size());
      return;
    }
    allotByCommon(
            asyncTask,
            caseList,
            creator,
            commonAllotBO.getUserIdList(),
            commonAllotBO.getTeamIdList(),
            commonAllotBO.getDepIdList(),
            false,
            false,
            true,
            commonAllotBO.getIsConjoint(),
            AllotCaseEnums.Type.OVERDUE_DAYS.getCode(),
            null,
            commonAllotBO.getIsAllAgents());
  }

  private void overdueDaysStrategy(AsyncTask asyncTask, List<Case> caseList, String strategyRule) {
    User creator = userService.selectByPrimaryKey(asyncTask.getCreateBy());
    List<StrategyByOverdueDaysBO> list =
            JsonUtils.toList(strategyRule, StrategyByOverdueDaysBO.class);
    OrgSwitch orgSwitch = orgSwitchService.selectOrgSwitchByOrgId(asyncTask.getOrgId());
    Boolean isAutoUpdateOverdueDays = ObjectUtil.isNull(orgSwitch)?false:Boolean.valueOf(orgSwitch.getAutoUpdateOverdueDays());
    for (StrategyByOverdueDaysBO strategyByOverdueDaysBO : list) {
      Integer lowerLimit = strategyByOverdueDaysBO.getLowerLimit();
      Integer upperLimit = strategyByOverdueDaysBO.getUpperLimit();
      Boolean isDesignate = strategyByOverdueDaysBO.getIsDesignate();
      List<Long> userIdList = strategyByOverdueDaysBO.getUserIdList();
      List<Long> teamIdList = strategyByOverdueDaysBO.getTeamIdList();
      List<Long> depIdList = strategyByOverdueDaysBO.getDepIdList();
      List<Case> filterCaseList =
              allotStrategyService.filterByOverdueDays(
                      caseList, lowerLimit, upperLimit, isAutoUpdateOverdueDays, isDesignate);
      if (CollectionUtils.isEmpty(filterCaseList)) {
        continue;
      }
      allotByCommon(
              asyncTask,
              filterCaseList,
              creator,
              userIdList,
              teamIdList,
              depIdList,
              false,
              false,
              true,
              false,
              AllotCaseEnums.Type.OVERDUE_DAYS.getCode(),
              null,
              null);
    }
  }

  private void doAllot(
          AsyncTask asyncTask,
          Long targetId,
          List<Case> list,
          User creator,
          Integer type,
          Boolean allotToTeamMembers,
          Boolean allotToDep,
          Integer conjointCount) {
    if (CollectionUtils.isEmpty(list)) {
      return;
    }
    Long taskId = asyncTask.getId();
    User dunner = null;
    if (allotToTeamMembers) {
      // 允许分配到催员的话，targetId就是userId
      dunner = userService.selectByPrimaryKey(targetId);
      if (null == dunner) {
        throw new ApiException("请选择需要分配的催员！");
      }
    }
    CaseAllot caseAllot = null;
    if (allotToTeamMembers) {
      caseAllot = allotCaseService.saveCaseAllot(
              asyncTask.getId(), dunner.getId(), dunner.getName(), type, creator.getId(), conjointCount, dunner.getTeamId(), dunner.getDepId(), 0L, 0);
    } else if (allotToDep) {
      caseAllot = allotCaseService.saveCaseAllot(
              asyncTask.getId(), null, null, type, creator.getId(), conjointCount, null, targetId, 0L, 0);
    } else {
      DepTeam team = depTeamService.selectByPrimaryKey(targetId);
      //按要求，案件分配小组如果是直属小组，就不要把team的parentId(公司id)值传给后续分配表、案件表的depId
      if (team.getUnderTeam() == DepTeamEnums.UnderTeam.YES.getCode()) {
        team.setParentId(null);
      }
      caseAllot = allotCaseService.saveCaseAllot(
              asyncTask.getId(), null, null, type, creator.getId(), conjointCount, team.getId(), team.getParentId(), 0L, 0);
    }
    Integer start = 0;
    Integer end = 0;
    long successCnt = 0;
    while (start < list.size()) {
      end = start + 1000;
      if (end > list.size()) {
        end = list.size();
      }
      List<Case> caseList = list.subList(start, end);
      List<String> caseIds =
              caseList.stream().map(c -> c.getId().toString()).collect(Collectors.toList());
      List<Long> caseIdList = caseIds.stream().map(Long::valueOf).collect(Collectors.toList());
      try {
        // 更新数据库，生成log
        if (allotToTeamMembers) {
          allotCaseService.caseAllotToUser(caseIdList, dunner, creator, taskId, null, null);
        } else if (allotToDep) {
          Date autoRecoveryDate = asyncTask.getFieldJson().containsKey("autoRecoveryDate") ? new Date(Long.parseLong(asyncTask.getFieldJson().get("autoRecoveryDate"))) : null;
          allotCaseService.caseAllotToDepTeam(caseIdList, targetId, null, creator, taskId, autoRecoveryDate, null, null);
        } else {
          DepTeam team = depTeamService.selectByPrimaryKey(targetId);
          //按要求，案件分配小组如果是直属小组，就不要把team的parentId(公司id)值传给后续分配表、案件表的depId
          if (team.getUnderTeam() == DepTeamEnums.UnderTeam.YES.getCode()) {
            team.setParentId(null);
          }
          allotCaseService.caseAllotToDepTeam(caseIdList, team.getParentId(), targetId, creator, taskId, null, null, null);
        }

        // 分配成功的案件移除（留下的则是分配失败的案件）
        redisUtil.sRemove(KeyCache.CASE_ALLOT_TASK_CASES + asyncTask.getId(),caseIds.toArray(new String[caseIds.size()]));
        successCnt += caseIdList.size();
        caseAllot.setCount((int)successCnt);
        Long amount = caseList.stream().mapToLong(Case::getAmount).sum();
        caseAllot.setAmount(caseAllot.getAmount() + amount);
      } catch (Exception e) {
        assert dunner != null;
        log.error("分配失败,催员id:{},name:{},size:{},{}", dunner.getId(), dunner.getName(), end - start, ExceptionUtil.stacktraceToString(e));
      } finally {
        start = end;
      }
      caseAllot.setUpdateTime(new Date());
      allotCaseService.updateByPrimaryKey(caseAllot);
      asyncTaskService.updateTaskSuccessAmt(asyncTask, successCnt);
    }
  }

  private List<Case> selectByTaskId(List<String> caseIdList, Integer isRule) {
    List<List<String>> groupList = CmUtil.splitList(caseIdList, SystemConst.GROUP_SIZE);
    Example example = new Example(Case.class);
    List<Case> caseList = new ArrayList<>();
    for (List<String> subList : groupList) {
      example.clear();
      example.createCriteria().andIn("id", subList);
      // 规则分案，需要查询案件所有信息；基础分案不需要查询所有字段，可以有效提高性能
      if (!Objects.equals(isRule, 1)) {
        example.selectProperties("id");
        example.selectProperties("debtId");
        example.selectProperties("amount");
      }
      List<Case> list = caseService.selectByExample(example);
      caseList.addAll(list);
    }
    return caseList;
  }

  public List<Case> selectForOverdueDays(List<String> caseIdList) {
    List<List<String>> groupList = CmUtil.splitList(caseIdList, SystemConst.GROUP_SIZE);
    Example example = new Example(Case.class);
    List<Case> caseList = new ArrayList<>();
    for (List<String> subList : groupList) {
      example.clear();
      example.createCriteria().andIn("id", subList);
      example.selectProperties("id");
      example.selectProperties("debtId");
      example.selectProperties("amount");
      example.selectProperties("overdueDate");
      example.selectProperties("overdueDays");
      List<Case> list = caseService.selectByExample(example);
      caseList.addAll(list);
    }
    return caseList;
  }
}
