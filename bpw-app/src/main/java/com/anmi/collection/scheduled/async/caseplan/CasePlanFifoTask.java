package com.anmi.collection.scheduled.async.caseplan;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.common.enums.CasePlanEnums;
import com.anmi.collection.common.enums.SiteEnums;
import com.anmi.collection.dto.CaseContact;
import com.anmi.collection.entity.requset.cases.casePlan.CasePlanTmp;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.mapper.ContactsMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.*;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.PatternUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.BeanUtils;
import com.anmi.domain.cases.Case;
import com.anmi.domain.cases.CasePlan;
import com.anmi.domain.cases.OrgSwitch;
import com.anmi.domain.site.SiteVarsMapping;
import com.anmi.domain.user.DepTeam;
import com.anmi.domain.user.User;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class CasePlanFifoTask extends BaseCasePlanTask {

  @Autowired private DuyanManager duyanManager;
  @Autowired private DepTeamService depTeamService;
  @Autowired private CasePlanRelService casePlanRelService;
  @Autowired private UserService userService;
  @Autowired private ContactsMapper contactsMapper;
  @Autowired private CaseMapper caseMapper;
  @Autowired private SiteVarsMappingService siteVarsMappingService;
  @Autowired private OrgSwitchService orgSwitchService;

  @XxlJob("casePlanFifoHandler")
  public ReturnT<String> casePlanFifoHandler() {
    try {
      LogbackUtil.insertTrace(LogbackUtil.generateTrace());
      startPlan(KeyCache.CASE_FIFO_PLAN_ID_LIST);
    } catch (Exception e) {
      log.error("处理预测式外呼计划出现全局异常！", e);
    }finally {
      LogbackUtil.removeTrace();
    }
    return ReturnT.SUCCESS;
  }

  @Override
  protected void execute(Long planId) {
    Set<String> caseIdList = redisUtil.sGet(KeyCache.CASE_FIFO_CASE_IDS + planId);
    CasePlan casePlan = null;
    try {
      if (caseIdList == null || caseIdList.isEmpty()) {
        throw new ApiException("计划对应案件为空");
      }
      casePlan = casePlanService.selectByPrimaryKey(planId);
      if (casePlan == null) {
        throw new ApiException("计划不存在");
      }
      Long duyanPlanId = insertCasePlan(casePlan, Lists.newArrayList(caseIdList));
      casePlan.setSelectedTotal((long) caseIdList.size());
      if (duyanPlanId != null) {
        casePlan.setDuyanPlanId(duyanPlanId);
        casePlan.setStatus(CasePlanEnums.Status.ING.getCode());
        CasePlanTmp casePlanTmp=duyanManager.getPlanInfo(casePlan.getOrgId(),duyanPlanId);
        casePlan.setCaseTotal(casePlanTmp.getTotalCount().intValue());
      } else {
        casePlan.setStatus(CasePlanEnums.Status.FAIL.getCode());
      }
      casePlanService.updateByPrimaryKeySelective(casePlan);
    } catch (Exception e) {
      log.error("预测式外呼计划,planId:{},失败！{}", planId, ExceptionUtil.stacktraceToString(e));
      casePlan.setStatus(CasePlanEnums.Status.FAIL.getCode());
      casePlanService.updateByPrimaryKeySelective(casePlan);
    } finally {
      // 任务列表移除任务id
      redisUtil.lRemove(KeyCache.CASE_FIFO_PLAN_ID_LIST, 0, planId.toString());
      redisUtil.expireDays(KeyCache.CASE_FIFO_CASE_IDS + casePlan.getId(), 3);
    }
  }

  private Long insertCasePlan(CasePlan casePlan, List<String> caseIdLists) {
    UserSession userSession = new UserSession();
    userSession.setId(casePlan.getCreateBy());
    userSession.setOrgId(casePlan.getOrgId());
    userSession.setDepId(casePlan.getDepId());
    userSession.setTeamId(casePlan.getTeamId());
    List<Long> caseIds = caseIdLists.stream().map(Long::valueOf).collect(Collectors.toList());
    OrgSwitch orgSwitch = orgSwitchService.selectOrgSwitchByOrgId(casePlan.getOrgId());

    List<Case> cases = caseMapper.selectFieldJson(caseIds);
    List<CaseContact> caseContactsAll = contactsMapper.selectContactByCaseIds(caseIds);
    List<CaseContact> caseContacts = Lists.newArrayList();
    if (casePlan.getDebtorOwn() != null && casePlan.getDebtorOwn() == 1) {
      List<CaseContact> ownList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 1)
              .filter(caseContact -> PatternUtils.isPlanPhone(caseContact.getContactMobile()))
              .collect(Collectors.toList());

      // 最新本人号码
      if (casePlan.getDebtorOwnType() != null &&
              Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.LAST.getCode())) {
        Map<Long, CaseContact> caseContactMap = ownList.stream().collect(Collectors.groupingBy(
                CaseContact::getCaseId, Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(CaseContact::getConUpdateTime)), Optional::get)));
        ownList.clear();
        ownList.addAll(caseContactMap.values());
      }
      // 随机本人号码
      if (casePlan.getDebtorOwnType() != null &&
              Objects.equals(casePlan.getDebtorOwnType(), CasePlanEnums.DebtorOwnType.RANDOM.getCode())) {
        List<CaseContact> randomCaseContacts = Lists.newArrayList();
        ownList.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> randomCaseContacts.add(v.get(0)));
        ownList.clear();
        ownList.addAll(randomCaseContacts);
      }
      caseContacts.addAll(ownList);
    }
    if (casePlan.getContacts() != null && casePlan.getContacts() == 1) {
      List<CaseContact> contactsList = caseContactsAll.stream().filter(obj -> obj.getOwnSign() == 0)
              .filter(caseContact -> PatternUtils.isPlanPhone(caseContact.getContactMobile()))
              .collect(Collectors.toList());
      // 除本人外的所有号码类型
      if (StringUtils.isNotBlank(casePlan.getContactTypeIds())) {
        String contactTypeIdStr = casePlan.getContactTypeIds();
        List<Long> contactTypeIds = Stream.of(contactTypeIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
        if (contactTypeIds.size() > 1) {
          contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
        }
        if (contactTypeIds.size() == 1) {
          if (!Objects.equals(contactTypeIds.get(0), CasePlanEnums.ContactTypeEnum.ALL.getCode())) {
            contactsList = contactsList.stream().filter(obj -> contactTypeIds.contains(obj.getContactTypeId())).collect(Collectors.toList());
          }
        }
      }
      caseContacts.addAll(contactsList);
    }

    // 过滤掉已达案件号码拨打上限的号码信息，进行移除
    List<CaseContact> delContacts = casePlanService.getMobileLimitContact(caseContacts, orgSwitch);
    if (!CollectionUtils.isEmpty(delContacts)) {
      caseContacts.removeAll(delContacts);
    }

    // 管控池管控 管控联系人、本人拨打频率
    List<CaseContact> ctrlContacts = casePlanService.getCaseCtrlLimit(caseContacts, orgSwitch);
    if (!CollectionUtils.isEmpty(ctrlContacts)) {
      caseContacts.removeAll(ctrlContacts);
    }

    // 先按案件id分组选择案件
    List<CaseContact> param1 = Lists.newArrayList();
    // 根据案件id分组查询
    if (!CollectionUtils.isEmpty(caseContacts)) {
      caseContacts.stream().collect(Collectors.groupingBy(CaseContact::getCaseId)).forEach((k, v) -> param1.add(v.get(0)));
    }
    // 按手机号分组选择第一个手机号
    List<CaseContact> param = Lists.newArrayList();
    // 相同手机号，随机选其中一个案子参与计划
    if (!CollectionUtils.isEmpty(caseContacts)) {
      param1.stream().collect(Collectors.groupingBy(CaseContact::getContactMobile)).forEach((k, v) -> param.add(v.get(0)));
    }
    if(param.size()<1){
      log.warn("创建预测式外呼计划失败-查询结果为0");
      throw new ApiException("创建预测式外呼计划失败-查询结果为0");
    }

    List<SiteVarsMapping> duyanVarsMappings = new ArrayList<>();
    if (Objects.equals(casePlan.getFifoToRobotEnabled(), 1)) {
      List<String> duyanSiteVars = duyanManager.getSiteVars(casePlan.getSiteId(), casePlan.getOrgId());
      List<SiteVarsMapping> siteVarsMappings = siteVarsMappingService.getSiteVarsMappings(casePlan.getOrgId(), casePlan.getSiteId());
      duyanVarsMappings = siteVarsMappings.stream().filter(siteVarsMapping -> StrUtil.isNotBlank(siteVarsMapping.getAnmiVar()) && duyanSiteVars.stream().anyMatch(duyanSiteVar -> ObjectUtil.equals(duyanSiteVar, siteVarsMapping.getDuyanVar())))
              .collect(Collectors.toList());

      // 有不符合度言话术变量要求的,剔除
      Iterator<CaseContact> iterator = param.iterator();
      while (iterator.hasNext()) {
        CaseContact caseContact = iterator.next();
        Map<String, Object> caseContactMap = BeanUtils.beanToMap(caseContact);

        Long caseId = caseContact.getCaseId();
        Optional<Case> caseOptional = cases.stream().filter(c -> ObjectUtil.equals(caseId, c.getId())).findFirst();
        Case caseDb = caseOptional.get();
        Map<String, String> fieldJsonMap = caseDb.getFieldJson();

        boolean varValueCheck = duyanVarsMappings.stream().anyMatch(varMapping -> {
          String anmiVar = varMapping.getAnmiVar();
          SiteEnums.Var varEmum = SiteEnums.Var.getByAnmiVar(anmiVar);
          Object value = null;
          if (ObjectUtil.isNull(varEmum)){
            value = fieldJsonMap.get(anmiVar);
          } else {
            String source = varEmum.getSource();
            String key = StringUtils.substringAfterLast(source, ".");

            if (source.startsWith("CaseContact")){
              value = caseContactMap.get(key);
            }else if (source.startsWith("CaseVO.fieldJson")){
              value = fieldJsonMap.get(key);
            }
          }
          return ObjectUtil.isNull(value);
        });

        if (varValueCheck) {
          log.info("话术变量缺失,剔除,案件:{},手机号:{},contact_id:{}", caseId, caseContact.getContactMobile(), caseContact.getContactId());
          iterator.remove();
        }
      }
    }

    Long duyanPlanId = null;
    Integer start = 0;
    Integer end = 0;
    Integer casePlanType = casePlan.getType();
    while (start < param.size()) {
      try {
        end = start + 5000;
        if (end > param.size()) {
          end = param.size();
        }
        List<CaseContact> subIdList = param.subList(start, end);
        if (CollectionUtils.isEmpty(subIdList)) {
          continue;
        }

        List<Map<String,Object>> contents = new ArrayList<>();
        if (Objects.equals(casePlan.getFifoToRobotEnabled(), 1)) {
          if(ObjectUtil.isNotEmpty(subIdList)){
            for (int i = 0; i < subIdList.size(); i++) {
              CaseContact caseContact = subIdList.get(i);
              Long caseId = caseContact.getCaseId();
              Optional<Case> caseOptional = cases.stream().filter(c -> ObjectUtil.equals(caseId, c.getId())).findFirst();
              Case caseDb = caseOptional.get();
              Map<String, String> fieldJsonMap = caseDb.getFieldJson();

              casePlanService.addContent(caseContact,casePlan.getOrgId(),fieldJsonMap,contents,duyanVarsMappings, casePlan);
            }
          }
        }
        if (start == 0) {
          if (CasePlanEnums.Type.FIFO.getCode().equals(casePlanType)) {
            DepTeam depTeam = depTeamService.selectByPrimaryKey(casePlan.getTeamId());
            duyanPlanId = duyanManager.createNewFifo(subIdList, casePlan, depTeam.getDuyanReferId(), contents);
          } else {
            User user = userService.selectByPrimaryKey(casePlan.getUserId());
            duyanPlanId = duyanManager.createNewPersonalFifo(subIdList, casePlan, user.getDuyanAccountId(), contents);
          }
          List<Long> caseList=subIdList.stream().map(CaseContact::getCaseId).collect(Collectors.toList());
          casePlanRelService.saveCasePlanRelBatch(caseList, casePlan.getId(), userSession, casePlanType);
          log.info("公司{}调用度言创建计划成功,duyanPlanId:{}", casePlan.getOrgId(), duyanPlanId);
        } else {
          Thread.sleep(5000);
          duyanManager.batchAdd(casePlan.getOrgId(), duyanPlanId, subIdList, casePlanType, casePlan, contents);
          List<Long> caseList=subIdList.stream().map(CaseContact::getCaseId).collect(Collectors.toList());
          casePlanRelService.saveCasePlanRelBatch(caseList, casePlan.getId(), userSession, casePlanType);
          log.info("公司{}调用度言批量添加号码成功,duyanPlanId:{}",casePlan.getOrgId(), duyanPlanId);
        }
        // 成功的案件移（留下的则是失败的案件）
        redisUtil.sRemove(KeyCache.CASE_FIFO_CASE_IDS + casePlan.getId(), subIdList.stream().map(obj -> String.valueOf(obj.getCaseId())).toArray(String[]::new));
      } catch (Exception e) {
        log.error("预测式外呼计划批量操作任务,planId:{},失败！{}", casePlan.getId(), ExceptionUtil.stacktraceToString(e));
        return duyanPlanId;
      } finally {
        start = end;
      }
    }
    casePlanService.createCasePlanRelContact(casePlan,caseContacts,
        param.stream().map(CaseContact::getContactMobile).collect(Collectors.toList()));
    return duyanPlanId;
  }
}
