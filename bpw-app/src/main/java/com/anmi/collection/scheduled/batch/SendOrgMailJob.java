package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 公司邮箱邮件发送
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class SendOrgMailJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 0 9 * * ?")
     */
    @XxlJob("sendOrgMailHandler")
    public ReturnT<String> sendOrgMailHandler(){
        super.autoSendOrgMail();
        return ReturnT.SUCCESS;
    }
}

