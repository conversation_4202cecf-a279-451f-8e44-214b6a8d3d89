package com.anmi.collection.scheduled.async.downloadtask;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.service.DownloadTaskService;
import com.anmi.collection.service.OperationStatisticsService;
import com.anmi.collection.service.UserStatisticsService;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.domain.sys.DownloadTask;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Slf4j
public class FileDownloadTask {

    @Autowired private DownloadTaskService downloadTaskService;
    @Autowired private UserStatisticsService userStatisticsService;
    @Autowired private OperationStatisticsService operationStatisticsService;

    @XxlJob("fileDownloadHandler")
    public ReturnT<String> fileDownloadHandler() {
        try{
            LogbackUtil.insertTrace(LogbackUtil.generateTrace());
            startDownloadTask();
        } catch (Exception e) {
            log.error("处理下载任务出现全局异常！", e);
        }finally {
            LogbackUtil.removeTrace();
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 执行计划，入口
     */
    private void startDownloadTask() throws Exception{
        List<DownloadTask> needExecuteDownloadTasks = selectTask();

        for(DownloadTask downloadTask:needExecuteDownloadTasks){
            Long downloadTaskId = downloadTask.getId();
            log.info("=======下载任务=======downloadTaskId:{} start",downloadTaskId);
            try {
                execute(downloadTask);
            } catch (Exception e) {
                log.error("=======下载任务=======downloadTaskId:{} 异常:{}" ,downloadTaskId, ExceptionUtil.stacktraceToString(e));
                downloadTask.setStatus(DownloadTask.Status.FAILED.getCode());
                String error=e.getMessage();
                if(StrUtil.isBlank(error)){
                    error="任务失败";
                } else {
                    error = StrUtil.subWithLength(error,0,50);
                }
                downloadTask.setFailedReason(error);
                downloadTaskService.updateByPrimaryKey(downloadTask);
            }
            log.info("=======下载任务=======downloadTaskId:{} end",downloadTaskId);
        }
    }

    /**
     * 获取需要执行的下载任务
     *
     * @return {@link List}<{@link DownloadTask}>
     */
    private List<DownloadTask> selectTask(){
        Example example=new Example(DownloadTask.class);
        example.createCriteria().andEqualTo("status",DownloadTask.Status.WAIT.getCode()).
                andIn("type",Lists.newArrayList(DownloadTask.Type.REPAYMENT_STATISTICS_EXPORT.getCode(),
                        DownloadTask.Type.OPERATION_STATISTICS_EXPORT.getCode()));
        return downloadTaskService.selectByExample(example);
    }

    /**
     * 单条下载任务的执行
     *
     * @param downloadTask 下载任务
     */
    private void execute(DownloadTask downloadTask) throws Exception{
        if(DownloadTask.Type.REPAYMENT_STATISTICS_EXPORT.getCode()==downloadTask.getType()){
            userStatisticsService.repaymentDownload(downloadTask);
        }else{
            operationStatisticsService.operStatisticsDownload(downloadTask);
        }
    }
}
