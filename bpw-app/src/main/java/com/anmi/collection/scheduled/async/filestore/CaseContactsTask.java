package com.anmi.collection.scheduled.async.filestore;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.anmi.collection.common.enums.CompanyEnums;
import com.anmi.collection.entity.requset.file.FileUpload;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseTemplateService;
import com.anmi.collection.service.ContactTypeConfigService;
import com.anmi.collection.service.I18nService;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.CommonUtils;
import com.anmi.domain.cases.CaseTemplate;
import com.anmi.domain.user.Company;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class CaseContactsTask extends BaseFileStoreTask {

    public static final int READ_SIZE = 300;

    @Autowired private CaseTemplateService caseTemplateService;
    @Autowired private I18nService i18nService;
    @Autowired private ContactTypeConfigService contactTypeConfigService;

    @XxlJob("caseContactsHandler")
    public ReturnT<String> caseContactsHandler() {
        try{
            LogbackUtil.insertTrace(LogbackUtil.generateTrace());
            startFileStore(KeyCache.FILE_CONTACTS_KEY_LIST);
        } catch (Exception e) {
            log.error("通讯录导入出现全局异常！", e);
        }finally {
            LogbackUtil.removeTrace();
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(String fileKey){
        Long storeId = Long.valueOf(StringUtils.substringAfterLast(fileKey, "_"));
        log.info("=======开始处理通讯录导入数据：=======storeId:{}",storeId);
        //读取文件信息
        Map<Object,Object> storeMap= redisUtil.hget(fileKey);
        Integer success=Integer.valueOf(storeMap.get("success").toString());
        Integer error=Integer.valueOf(storeMap.get("error").toString());
        Integer surplus = Integer.valueOf(storeMap.get("surplus").toString());
        Integer total = Integer.valueOf(storeMap.get("total").toString());
        try {
            UserSession userSession = null;
            FileUpload fileUpload = null;
            String userMsg = storeMap.get("user").toString();
            if (!com.anmi.collection.utils.StringUtils.isEmpty(userMsg)) {
                userSession = JSON.parseObject(userMsg, UserSession.class);
            }
            String fileUploadMsg = storeMap.get("file_upload").toString();
            if (!CommonUtils.isEmpty(fileUploadMsg)) {
                fileUpload = JSON.parseObject(fileUploadMsg, FileUpload.class);
            }
            Company company = companyService.selectByPrimaryKey(userSession.getOrgId());
            CaseTemplate caseTemplate = caseTemplateService.selectByPrimaryKey(fileUpload.getTemplateId());
            i18nService.convertCaseTemplate(Collections.singletonList(caseTemplate),company.getLanguage());
            Long parentOrgId=company.getType() == CompanyEnums.Type.HEAD_OFFICE.getCode() ? userSession.getOrgId() : company.getParentId();
            // 查询对应公司下的号码类型配置
            Map<String, Long> contactTypeMap = contactTypeConfigService.selectContactTypeMapByOrgId(parentOrgId);
            List<String> results;
            String valueListKey = KeyCache.FILE_CONTACTS_VALUE_LIST + storeId;
            while (surplus < total) {
                Integer end = surplus + READ_SIZE - 1;
                if (end > total - 1) {
                    end = total - 1;
                }
                results = redisUtil.lGet(valueListKey, surplus, end);

                // 通讯录数据处理
                Integer subSuccessAmt =fileStoreService.contactsHandle(storeId,results,userSession,parentOrgId,contactTypeMap);
                // 更新redis的file的扩展数据
                success = success + subSuccessAmt;
                error = error + results.size() - subSuccessAmt;
                surplus = end + 1;
                Map<Object,Object> values = new HashMap<>();
                values.put("success", String.valueOf(success));
                values.put("error", String.valueOf(error));
                values.put("surplus", String.valueOf(surplus));
                redisUtil.hset(fileKey, values);
                // 睡眠一下
                Thread.sleep(100);
            }
            //数据全部更新完成，生成错误excel
            fileStoreService.handleFileStore(storeId, caseTemplate, success, 0l, total, company.getLanguage());
            //删除redis数组缓存
            redisUtil.lRemove(KeyCache.FILE_CONTACTS_KEY_LIST, 0, fileKey);
            redisUtil.expireDays(fileKey, 3);
            redisUtil.del(valueListKey);
            log.info("=======结束处理通讯录导入数据：=======storeId:{}",storeId);
        } catch (Exception e) {
            log.error("storeId:{} 通讯录导入数据出现异常！{}", storeId , ExceptionUtil.stacktraceToString(e));
            // 处理失败文件转存到另一个list
            redisUtil.lRemove(KeyCache.FILE_CONTACTS_KEY_LIST, 0, fileKey);
            redisUtil.lSetFromLeft(KeyCache.FILE_CONTACTS_FAIL_LIST, fileKey);
            // 更新fileStore状态为失败
            fileStoreService.updateFileFail(storeId,total,success,0l);
        }
    }
}
