package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.CaseLogEnums;
import com.anmi.collection.entity.requset.amc.CaseToWaitAllotParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.mapper.CaseMapper;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.domain.cases.AsyncTask;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 案件移入委外待分配任务
 *
 * <AUTHOR>
 * @date 2023/06/20
 */
@Slf4j
public class CaseToWaitAllotAgentTask extends BaseAsyncTask {

  @Resource private CaseMapper caseMapper;
  @Resource private ApplicationContext applicationContext;

  @XxlJob("caseToWaitAllotAgentHandler")
  public ReturnT<String> caseToWaitAllotAgentHandler() {
    try {
      startAsyncTask(KeyCache.CASE_TO_WAIT_ALLOT_AGENT_TASK_ID_LIST);
    } catch (Exception e) {
      log.error("案件移入委外待分配出现全局异常！", e);
    }
    return ReturnT.SUCCESS;
  }

  @Override
  protected void execute(Long taskId) {
    AsyncTask asyncTask = asyncTaskService.selectByPrimaryKey(taskId);
    asyncTask.setSuccessAmt(0L);
    Set<Long> caseIds = redisUtil.sGet(KeyCache.CASE_TO_WAIT_ALLOT_AGENT_TASK_CASES + taskId).stream().map(Long::parseLong).collect(Collectors.toSet());
    try {
      String queryString = asyncTask.getFieldJson().get("query");
      CaseToWaitAllotParam param = JsonUtils.fromJson(queryString, CaseToWaitAllotParam.class);
      Assert.notNull(param, "参数不能为空");
      String sessionUserStr = asyncTask.getFieldJson().get("userSession");
      UserSession userSession = JsonUtils.fromJson(sessionUserStr, UserSession.class);
      Assert.notNull(userSession, "userSession不能为空");

      List<List<Long>> caseIdLists = CollUtil.split(caseIds, 2000);
      caseIdLists.forEach(caseIdList->{
        CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
        caseBatchUpdateEvent.setType(CaseLogEnums.Type.TO_WAIT_ALLOT_AGENT.getCode());
        caseBatchUpdateEvent.setUserSession(userSession);
        caseBatchUpdateEvent.setCaseIds(caseIdList);
        caseBatchUpdateEvent.setTaskId(taskId);
        applicationContext.publishEvent(caseBatchUpdateEvent);

        caseMapper.toWaitAllotAgent(caseIdList);

        asyncTask.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
        asyncTask.setSuccessAmt(asyncTask.getSuccessAmt() + caseIdList.size());
        asyncTaskService.updateByPrimaryKeySelective(asyncTask);
      });
    } catch (Exception e) {
      log.error("案件移入委外待分配,taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
      asyncTaskService.updateTaskFinish(asyncTask, e.getMessage());
    } finally {
      redisUtil.lRemove(KeyCache.CASE_TO_WAIT_ALLOT_AGENT_TASK_ID_LIST, 0, taskId.toString());
      redisUtil.sRemove(KeyCache.CASE_PROTECT_EXIST_IDS+asyncTask.getOrgId(),caseIds.stream().map(String::valueOf).toArray(String[]::new));
      redisUtil.del(KeyCache.CASE_TO_WAIT_ALLOT_AGENT_TASK_CASES+taskId);
    }
  }
}
