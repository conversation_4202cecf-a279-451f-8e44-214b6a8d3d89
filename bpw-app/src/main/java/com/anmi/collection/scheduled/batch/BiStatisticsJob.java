package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * bi报表数据任务统计
 *
 * <AUTHOR>
 * @date 2024/5/20
 */
public class BiStatisticsJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 0 2 * * ?")
     *
     * @return {@link ReturnT}<{@link String}>
     * @throws Exception 例外
     */
    @XxlJob("biStatisticsHandler")
    public ReturnT<String> biStatisticsHandler() throws Exception {
        super.biStatistics();
        return ReturnT.SUCCESS;
    }
}
