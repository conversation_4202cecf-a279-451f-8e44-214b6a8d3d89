package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 函件状态更新
 * 每天早上6点执行任务,更新超过三天的函件状态
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class UpdateLetterStatusJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 0 6 * * ?")
     */
    @XxlJob("updateLetterStatusHandler")
    public ReturnT<String> updateLetterStatusHandler() {
        super.autoChangeLetterStatus();
        return ReturnT.SUCCESS;
    }
}
