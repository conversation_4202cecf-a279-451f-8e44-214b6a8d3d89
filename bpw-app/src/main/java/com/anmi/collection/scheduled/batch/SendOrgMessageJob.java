package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 公司手机短信发送
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class SendOrgMessageJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 0 9 * * ?")
     */
    @XxlJob("sendOrgMessageHandler")
    public ReturnT<String> sendOrgMessageHandler(){
        super.autoSendOrgMessage();
        return ReturnT.SUCCESS;
    }
}

