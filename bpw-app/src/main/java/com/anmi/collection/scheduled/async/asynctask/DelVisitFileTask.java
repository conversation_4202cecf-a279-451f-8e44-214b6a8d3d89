package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.SpaceEnums;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.OrgSpaceService;
import com.anmi.collection.service.VisitFileService;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.visit.VisitFile;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class DelVisitFileTask extends BaseAsyncTask {

    @Resource private VisitFileService visitFileService;
    @Resource private OrgSpaceService orgSpaceService;
    @Resource private FileStorageStrategyFactory fileStorageStrategyFactory;

    @XxlJob("delVisitFileHandler")
    public ReturnT<String> delVisitFileHandler() {
        try {
            startAsyncTask(KeyCache.DEL_VISIT_FILE_TASK);
        } catch (Exception e) {
            log.error("删除外访附件出现全局异常！", e);
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long taskId) {
        try {
            AsyncTask task = asyncTaskService.selectByPrimaryKey(taskId);
            if (task == null || !AsyncTaskEnums.Status.ING.getCode().equals(task.getStatus())) {
                throw new ApiException("任务不存在或者任务状态不是执行中");
            }
            Set<String> fileIdsStr = redisUtil.sGet(KeyCache.DEL_VISIT_FILE_ID_PREFIX + taskId);
            assert fileIdsStr != null;
            List<Long> fileIds=fileIdsStr.stream().map(Long::valueOf).collect(Collectors.toList());
            Example example=new Example(VisitFile.class);
            example.and().andIn("id",fileIds);
            List<VisitFile> visitFileList=this.visitFileService.selectByExample(example);
            long successCount=0L;
            FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
            for (VisitFile visitFile : visitFileList) {
                boolean delResult = fileStorageStrategy.delFile(visitFile.getAlias(), visitFile.getUrl());
                if (delResult) {
                    successCount++;
                    visitFileService.deleteByPrimaryKey(visitFile.getId());
                    // 重新计算存储空间和外访存储空间
                    orgSpaceService.subtractUseSpace(visitFile.getOrgId(), visitFile.getFileSize(), SpaceEnums.Type.VISIT_FILE.getCode());
                }
            }
            task.setSuccessAmt(successCount);
            task.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
            task.setUpdateTime(new Date());
            this.asyncTaskService.updateByPrimaryKeySelective(task);
        } catch (Exception ex) {
            log.error("删除外访附件,taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(ex));
        } finally {
            redisUtil.lRemove(KeyCache.DEL_VISIT_FILE_TASK, 0, taskId.toString());
            redisUtil.del(KeyCache.DEL_VISIT_FILE_ID_PREFIX + taskId);
        }
    }
}
