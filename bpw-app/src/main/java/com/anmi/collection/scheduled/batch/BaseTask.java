package com.anmi.collection.scheduled.batch;

import cn.duyan.dto.eqianbao.OrgOwnSealDto;
import cn.duyan.dto.eqianbao.SealAuthorizedInfoDto;
import cn.duyan.signature.provider.EqianbaoSignatureProvider;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.enums.*;
import com.anmi.collection.common.enums.flow.FlowEnums;
import com.anmi.collection.configs.SystemConfig;
import com.anmi.collection.constant.LetterConstant;
import com.anmi.collection.constant.SmsContants;
import com.anmi.collection.constant.WorkOrderConstant;
import com.anmi.collection.dto.SpaceRecordDTO;
import com.anmi.collection.dto.fileStorage.UploadCreatedFileStream;
import com.anmi.collection.entity.requset.cases.Campaigns;
import com.anmi.collection.entity.requset.sys.MailParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.manager.DuyanManager;
import com.anmi.collection.manager.MailManager;
import com.anmi.collection.manager.NewRobotManager;
import com.anmi.collection.manager.YunpianManager;
import com.anmi.collection.mapper.*;
import com.anmi.collection.mapper.workorder.WorkOrderMapper;
import com.anmi.collection.service.*;
import com.anmi.collection.service.bi.BiStatisticsService;
import com.anmi.collection.service.fileStorage.FileStorageStrategy;
import com.anmi.collection.service.fileStorage.FileStorageStrategyFactory;
import com.anmi.collection.service.flow.FlowHandleRecordService;
import com.anmi.collection.service.flow.FlowNodeImageService;
import com.anmi.collection.service.lawsuit.LawsuitApplyService;
import com.anmi.collection.service.letter.LetterApplyService;
import com.anmi.collection.service.mediate.AuthOrgInfoService;
import com.anmi.collection.service.mediate.MediateAuditService;
import com.anmi.collection.service.workorder.WorkOrderService;
import com.anmi.collection.utils.CmUtil;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.DateUtils;
import com.anmi.collection.utils.StringUtils;
import com.anmi.collection.utils.dict.PageUtils;
import com.anmi.domain.cases.*;
import com.anmi.domain.decision.StrategyInfo;
import com.anmi.domain.flow.FlowHandleRecord;
import com.anmi.domain.letter.Letter;
import com.anmi.domain.letter.LetterMediationSeal;
import com.anmi.domain.mediate.AuthOrgInfo;
import com.anmi.domain.space.SpaceRecord;
import com.anmi.domain.user.Company;
import com.anmi.domain.workorder.WorkOrder;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.core.util.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 2022/11/11
 * BaseAsyncTask
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseTask {

    @Autowired protected SystemConfig systemConfig;

    @Autowired private OrgSwitchMapper orgSwitchMapper;
    @Autowired private BiStatisticsService biStatisticsService;
    @Autowired private CaseService caseService;
    @Autowired private CompanyService companyService;
    @Autowired private ApplicationContext applicationContext;
    @Autowired private CompanyMapper companyMapper;
    @Autowired private CaseMapper caseMapper;
    @Autowired private CaseOperationMapper caseOperationMapper;
    @Autowired private CaseOperationService caseOperationService;
    @Autowired private LetterMapper letterMapper;
    @Autowired private MailManager mailManager;
    @Autowired private SalesMapper salesMapper;
    @Autowired private DuyanManager duyanManager;
    @Autowired private StringRedisTemplate stringRedisTemplate;
    @Autowired private RedisTemplate redisTemplate;
    @Autowired private LetterMediationSealMapper letterMediationSealMapper;
    @Autowired private EqianbaoSignatureProvider eqianbaoSignatureProvider;
    @Autowired private UserMapper userMapper;
    @Autowired private AuthOrgInfoService authOrgInfoService;
    @Autowired private UserStatisticsService userStatisticsService;
    @Autowired private StrategyInfoService strategyInfoService;
    @Autowired private VisitService visitService;
    @Autowired private SpaceRecordService spaceRecordService;
    @Autowired private OrgSpaceService orgSpaceService;
    @Autowired private NewRobotManager newRobotManager;
    @Autowired private MediationStatisticsService mediationStatsService;
    @Autowired private DepTeamService depTeamService;
    @Autowired private MessageStatisticsService messageStatisticsService;
    @Autowired private SmsReplyService smsReplyService;
    @Autowired private CommissionCaseDetailService commissionCaseDetailService;
    @Autowired private WorkOrderService workOrderService;
    @Autowired private WorkOrderMapper workOrderMapper;
    @Autowired private AntiFraudService antiFraudService;
    @Autowired private LetterApplyService letterApplyService;
    @Autowired private CaseCooperationApplyService caseCooperationApplyService;
    @Autowired private VisitAuditService visitAuditService;
    @Autowired private LawsuitApplyService lawsuitApplyService;
    @Autowired private MediateAuditService mediateAuditService;
    @Autowired private VerifyApplyService verifyApplyService;
    @Autowired private AssistApplyService assistApplyService;
    @Autowired private RepaymentService repaymentService;
    @Autowired private ReductionService reductionService;
    @Autowired private CaseApplyService caseApplyService;
    @Autowired private FlowNodeImageService flowNodeImageService;
    @Autowired private FlowHandleRecordService flowHandleRecordService;
    @Autowired private FileStorageStrategyFactory fileStorageStrategyFactory;
    @Autowired private YunpianManager yunpianManager;

    private static final String OPERATION_CALL_UUID = "operation_call_uuid";

    /**
     * 关闭外访开关和小程序开关
     */
    protected void autoCloseSwitch() {
        try{
            log.info("定时任务-批量关闭小程序开关");
            int cnt = orgSwitchMapper.closeMiniProgramsSwitchExpire(null);
            log.info("定时任务-批量关闭小程序开关-执行结束，影响行数:{}",cnt);
        }catch (Exception ex){
            log.error("定时任务-批量关闭小程序开关-执行失败:{}", ExceptionUtil.stacktraceToString(ex));
        }
        try{
            log.info("定时任务-批量关闭外访开关");
            int cnt = orgSwitchMapper.closeVisitSwitchExpire(null);
            log.info("定时任务-批量关闭外访开关-执行结束，影响行数:{}",cnt);
        }catch (Exception ex){
            log.error("定时任务-批量关闭外访开关-执行失败:{}",ExceptionUtil.stacktraceToString(ex));
        }
    }

    /**
     * bi报表统计
     */
    protected void biStatistics() {
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>开始进行bi报表统计");
        Date end = DateUtils.getTodayDateFormat();
        Date start = DateUtils.addDays(end,-1);
        biStatisticsService.caseStatisticsData(start, end);
        biStatisticsService.callCenterStatisticsData(start, end);
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>结束bi报表统计");
    }

    /**
     * 自动结案
     */
    protected void autoEndCase() {
        List<Long> orgIdList = orgSwitchMapper.selectAutoEndOrgIds();
        Date start = new Date();
        log.info(
                "定时任务-自动结案-任务开始时间:{},开启自动结案公司个数:{}",
                DateUtils.formatDate(start, DateUtils.FULL_DATE_FORMAT),
                orgIdList.size());
        for (Long orgId : orgIdList) {
            try {
                caseService.caseAutoEnd(orgId);
                log.info("定时任务-自动结案-成功,orgId:{}", orgId);
            } catch (Exception e) {
                log.error("定时任务-自动结案-失败,orgId:{},{}", orgId, ExceptionUtil.stacktraceToString(e));
            }
        }
        Date end = new Date();
        log.info(
                "定时任务-自动结案-任务结束时间:{},用时:{} 毫秒",
                DateUtils.formatDate(end, DateUtils.FULL_DATE_FORMAT),
                end.getTime() - start.getTime());
    }

    /**
     * 案件自动回收
     */
    protected void autoRecoveryCase() {
        try {
            log.info("定时任务-案件自动回收");
            //为了避免案件全表扫描，先查询甲方公司列表，然后循环回收
            Company query = new Company();
            query.setStatus(CompanyEnums.Status.NORMAL.getCode());
            query.setIsAmc(1);
            List<Company> amcList = this.companyService.select(query);
            for (Company company : amcList) {
                log.info("开始回收公司[{}]的案件", company.getName());
                Example example = new Example(Case.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("orgId", company.getId());
                criteria.andCondition("auto_recovery_date<now()");
                List<Case> caseList = this.caseService.selectByExample(example);
                if(CollectionUtils.isEmpty(caseList)){
                    log.info("公司[{}]可回收案件为空", company.getName());
                    continue;
                }

                CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
                caseBatchUpdateEvent.setType(CaseLogEnums.Type.CASE_AUTO_RECOVERY.getCode());
                caseBatchUpdateEvent.setUserSession(null);
                caseBatchUpdateEvent.setCaseIds(caseList.stream().map(Case::getId).collect(Collectors.toList()));
                caseBatchUpdateEvent.setTaskId(null);
                applicationContext.publishEvent(caseBatchUpdateEvent);
                
                for (Case caseInfo : caseList) {
                    caseInfo.setUpdateTime(new Date());
                    caseInfo.setPreDepId(caseInfo.getDepId());
                    caseInfo.setDepId(null);
                    caseInfo.setTeamId(null);
                    caseInfo.setUserId(null);
                    caseInfo.setAllotAgent(CaseEnums.AllotStatus.NOT_ALLOT.getCode());
                    caseInfo.setAutoRecoveryDate(null);
                    caseInfo.setRecycleFlag(CaseEnums.RecycleFlag.YES.getCode());
                    caseInfo.setRecycleDate(new Date());
                    this.caseService.updateByPrimaryKey(caseInfo);
                }
                log.info("回收公司[{}]的案件结束,回收数量:{}", company.getName(),caseList.size());
            }
        } catch (Exception ex) {
            log.error("定时任务-案件自动回收完成-执行失败", ex);
        }
        log.info("定时任务-案件自动回收-结束");
    }

    /**
     * 迁移彻底删除的案件
     *
     * @throws InterruptedException 中断异常
     */
    protected void autoTransferDeleteCase() throws InterruptedException {
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>开始执行移动彻底删除的数据到历史表");
        int count = 0;
        long endTime = DateUtils.addHour(DateUtils.getStartDateTime(new Date()), 7).getTime();
        while(true) {
            // 执行到7点还没有完成就停止任务
            long currentTime = System.currentTimeMillis();
            if (currentTime >= endTime) {
                break;
            }
            try {
                List<Long> caseIds = caseMapper.getDelCaseIdList(null);
                if (org.springframework.util.CollectionUtils.isEmpty(caseIds)) {
                    break;
                }
                int cnt = caseService.backupCaseAndLog(caseIds);
                count = count +cnt;
            } catch (Exception e) {
                log.error("执行移动彻底删除的数据到历史表异常",e);
            }
            log.info("案件转移成功数量：{}", count);
            TimeUnit.MILLISECONDS.sleep(200);
        }
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>结束执行移动彻底删除的数据到历史表,备份删除案件count={}", count);
    }

    /**
     * 催记冷数据[180天前]迁移
     *
     * @throws InterruptedException 中断异常
     */
    protected void autoTransferCaseOperation() throws InterruptedException {
        Date now = new Date();
        log.info("当前时间:"+ DateUtils.formatDate(now, DateUtils.FULL_DATE_FORMAT)+"=====>>开始执行催记转移任务");
        int count = 0;
        long endTime = DateUtils.addHour(DateUtils.getStartDateTime(now), 7).getTime();
        String limitDay = DateUtils.formatDate(DateUtils.addDays(now,-180));
        while(true) {
            // 执行到7点还没有完成就停止任务
            long currentTime = System.currentTimeMillis();
            if (currentTime >= endTime) {
                break;
            }
            List<Long> caseOperationIds =  caseOperationMapper.getTransferCaseOperationIdList(limitDay);
            if (org.springframework.util.CollectionUtils.isEmpty(caseOperationIds)) {
                break;
            }
            int cnt = caseOperationService.transferCaseOperation(caseOperationIds);
            count = count +cnt;
            log.info("催记转移成功数量：{}", count);
            TimeUnit.MILLISECONDS.sleep(200);
        }
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>结束执行催记转移任务表,转移催记count={}", count);
    }

    /**
     * 函件状态更新
     */
    protected void autoChangeLetterStatus() {
        log.info("定时任务-开始函件状态更新");
        Date current = new Date();
        Date date = DateUtils.addDays(current, -3);
        Example example = new Example(Letter.class);
        example.and().andEqualTo("status", LetterConstant.LETTER_STATUS_SENDING).
                andLessThanOrEqualTo("updateTime", date);
        Letter letter = new Letter();
        letter.setStatus(LetterConstant.LETTER_STATUS_FAIL);
        letter.setUpdateTime(new Date());
        letterMapper.updateByExampleSelective(letter, example);
        log.info("定时任务-结束函件状态更新");
    }

    /**
     * 系统提醒邮件
     */
    protected void autoSendOrgMail(){
        Date start = new Date();
        log.info("定时任务-系统提醒邮箱定时发送-任务开始时间:{}", DateUtils.formatDate(start, DateUtils.FULL_DATE_FORMAT));
        // 查询合作结束时间不足等于30天的总公司
        List<Company> olist = companyMapper.selectEndCooperationByThirtyDaysOrgs();
        for (Company company:olist){
            try{
                MailParam mailParam = new MailParam();
                mailParam.setTo(company.getContactEmail());
                String text =
                        String.format(
                                SmsContants.SMS_CODE_MAIL_ORG,
                                company.getName(),
                                DateUtils.formatDate(
                                        DateUtils.getdateBefore(company.getCooperationEndTime()),
                                        DateUtils.SIMPLE_DATE_FORMAT),
                                DateUtils.daysBetween(start, company.getCooperationEndTime()));
                mailParam.setText(text);
                mailParam.setSubject("安米智能催收系统服务到期提醒");
                mailManager.sendHtmlMail(mailParam);
                log.info("总公司：{} 发送提醒邮件成功！", company.getName());
            } catch (Exception e) {
                log.error("发送总公司：{} 提醒邮件出现异常！{}", company.getName(), ExceptionUtil.stacktraceToString(e));
            }
        }
        Date end = new Date();
        log.info("定时任务-系统提醒邮箱定时发送-任务结束时间:{},用时:{} 毫秒", DateUtils.formatDate(end, DateUtils.FULL_DATE_FORMAT), end.getTime() - start.getTime());
    }

    /**
     * 系统提醒短信
     */
    protected void autoSendOrgMessage(){
        Date start = new Date();
        log.info("定时任务-系统提醒短信定时发送-任务开始时间:{}", DateUtils.formatDate(start, DateUtils.FULL_DATE_FORMAT));
        // 查询合作结束时间不足等于30天的总公司
        List<Company> olist = companyMapper.selectEndCooperationByThirtyDaysOrgs();
        for (Company company:olist){
            try{
                String text = String.format(SmsContants.SMS_CODE_MAIL_ORG,company.getName(),DateUtils.formatDate(DateUtils.getdateBefore(company.getCooperationEndTime()),DateUtils.SIMPLE_DATE_FORMAT),DateUtils.daysBetween(start,company.getCooperationEndTime()));
                yunpianManager.sendSms(company.getContactPhone(),text,false);
                log.info("总公司：{} 发送提醒短信成功！", company.getName());
            } catch (Exception e) {
                log.error("发送总公司：{} 提醒短信出现异常！{}", company.getName(), ExceptionUtil.stacktraceToString(e));
            }
        }
        Date end = new Date();
        log.info("定时任务-系统提醒短信定时发送-任务结束时间:{},用时:{} 毫秒", DateUtils.formatDate(end, DateUtils.FULL_DATE_FORMAT), end.getTime() - start.getTime());
    }

    /**
     * 销售邮件发送
     */
    protected void autoSendSalesMail(){
        Date start = new Date();
        log.info("定时任务-销售邮件发送定时发送-任务开始时间:{}", DateUtils.formatDate(start, DateUtils.FULL_DATE_FORMAT));
        // 查询合作结束时间不足等于或小于30天的总公司
        List<Company> olist = companyMapper.selectEndCooperationByLessThirtyDaysOrgs();
        for (Company company:olist){
            try{
                Sales sales = salesMapper.selectEndCooperationByThirtyDaysSales(company.getId());
                MailParam mailParam = new MailParam();
                mailParam.setTo(sales.getEmail());
                String text =
                        String.format(
                                SmsContants.SMS_CODE_MAIL_SALES,
                                sales.getName(),
                                company.getName(),
                                DateUtils.formatDate(
                                        DateUtils.getdateBefore(company.getCooperationEndTime()),
                                        DateUtils.SIMPLE_DATE_FORMAT),
                                DateUtils.daysBetween(start, company.getCooperationEndTime()));
                mailParam.setText(text);
                mailParam.setSubject("安米智能催收系统服务到期提醒");
                mailManager.sendHtmlMail(mailParam);
                log.info("总公司：{} 发送销售提醒邮件成功！", company.getName());
            } catch (Exception e) {
                log.error("发送总公司：{} 销售提醒邮件发送出现异常！{}", company.getName(), ExceptionUtil.stacktraceToString(e));
            }
        }
        Date end = new Date();
        log.info("定时任务-销售提醒邮件定时发送-任务结束时间:{},用时:{} 毫秒", DateUtils.formatDate(end, DateUtils.FULL_DATE_FORMAT), end.getTime() - start.getTime());
    }

    /**
     * 催记录音回填补偿
     */
    protected void handleCaseOperationVoice() {
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>开始对催记录音回填补偿");
        double endTime = (double) (System.currentTimeMillis() - 1000*60*10);
        // 1.获取10分钟之前度言回调安米没有匹配上的call_uuid，不获取所有是为了防止回调匹配不上刚刚放入redis，然后定时任务就启动，然后还是匹配不上，最终被删除
        Set<String> uuidSet = redisTemplate.opsForZSet().rangeByScore(OPERATION_CALL_UUID, 0, endTime);
        // 2.获取到uuid集合立即删除，防止后面错误导致累积
        redisTemplate.opsForZSet().removeRangeByScore(OPERATION_CALL_UUID, 0, endTime);
        log.info("==========查询到10分钟之前的call_uuid数据：{}", uuidSet);
        List<String> notExistUuid = new ArrayList<>();
        List<String> existUuid = new ArrayList<>();
        List<String> errorUuid = new ArrayList<>();
        uuidSet.forEach(uuid -> {
            try {
                CaseOperation operation = caseOperationService.selectOne(new CaseOperation(uuid));
                if (operation != null) {
                    Case ca=caseService.selectByPrimaryKey(operation.getCaseId());
                    Company company = companyService.selectByPrimaryKey(ca.getOrgId());
                    Long duyanReferId = company.getDuyanReferId();
                    Campaigns campaigns = duyanManager.getCallInfo(uuid, duyanReferId);
                    existUuid.add(uuid);
                    CaseOperation updateOperation = new CaseOperation();
                    updateOperation.setId(operation.getId());
                    updateOperation.setCallUuid(campaigns.getCall_uuid());
                    updateOperation.setCallTime(new Date(campaigns.getCall_time()));
                    updateOperation.setCaller(campaigns.getCaller());
                    updateOperation.setCallDurtion(campaigns.getDuration() == null ? 0 : campaigns.getDuration());
                    updateOperation.setRingDurtion(campaigns.getRing_time() == null ? 0 : campaigns.getRing_time());
                    updateOperation.setOutcome(campaigns.getOutcome() == null ? "FAIL" : campaigns.getOutcome());
                    updateOperation.setCallee(campaigns.getCallee());
                    // 电话结果为未填写时使用度言电话结果
                    if (CaseOperationEnums.CallType.NOT_FILLED.getCode() == operation.getCallType()) {
                        int outcome = caseOperationService.getOutcome(campaigns.getOutcome());
                        updateOperation.setCallType( campaigns.getOutcome() == null ? operation.getCallType() : outcome);
                    }
                    // 更新数据
                    updateOperation.setUpdateTime(new Date());
                    caseOperationService.updateByPrimaryKeySelective(updateOperation);
                    caseOperationService.syncCaseFollowInfo(operation.getCaseId());
                    caseOperationService.syncCaseDebtorFollowInfo(ca.getDebtId());
                    existUuid.add(uuid);
                } else {
                    //根据callUuid查询不到对应的催记
                    notExistUuid.add(uuid);
                }
            } catch (Exception e) {
                errorUuid.add(uuid);
                log.error("补偿call_uuid:"+ uuid + "失败：", e);
            }
        });
        if (!org.springframework.util.CollectionUtils.isEmpty(existUuid)) {
            log.info("==========成功补偿的call_uuid数据：{}", existUuid);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(notExistUuid)) {
            log.info("==========再一次没有匹配的call_uuid数据：{}", notExistUuid);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(errorUuid)) {
            log.info("==========失败补偿的call_uuid数据：{}", errorUuid);
        }
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>结束对催记录音回填补偿");
    }
    /*********************同步渠道商印章信息****************************/
    /**
     * 同步渠道商印章信息
     */
    protected void autoTransferOrgAuthSeal() {
        Date start = new Date();
        log.info("同步所有授权给渠道商的印章信息-任务开始时间:{}", DateUtils.formatDate(start, DateUtils.FULL_DATE_FORMAT));
        List<SealAuthorizedInfoDto> orgAuthorizedSeals = new ArrayList<>();
        try {
            // 获取到e签宝下所有授权给度言的的印章信息
            orgAuthorizedSeals = eqianbaoSignatureProvider.getOrgAuthorizedSeals(systemConfig.getEqianbaoOrgId());
        } catch (Exception e) {
            log.error("同步授权印章-获取授权印章信息出现异常,{}", ExceptionUtil.stacktraceToString(e));
        }
        if (CollectionUtils.isEmpty(orgAuthorizedSeals)) {
            log.info("同步授权印章-当前e签宝中授权印章为空");
            return;
        }
        covertToSeal(orgAuthorizedSeals);
        Date end = new Date();
        log.info("同步所有授权给渠道商的印章信息-任务结束时间:{},用时:{} 毫秒",
                DateUtils.formatDate(end, DateUtils.FULL_DATE_FORMAT),
                end.getTime() - start.getTime());
    }

    /**
     * 转化对象
     * 移除已不存在的数据信息，更新存在的数据
     * @param orgAuthorizedSeals
     */
    private void covertToSeal(List<SealAuthorizedInfoDto> orgAuthorizedSeals) {
        // 获取到数据库中存在的所有授权印章信息
        List<LetterMediationSeal> localAuthSeals = queryLocalAuthSeal();
        if (CollectionUtils.isEmpty(localAuthSeals)) {
            log.info("同步授权印章-安米智能系统中不存在授权印章！");
            return;
        }
        Map<String, SealAuthorizedInfoDto> sealAuthMap = orgAuthorizedSeals.stream().collect(Collectors.groupingBy(
                SealAuthorizedInfoDto::getSealId, Collectors.collectingAndThen(Collectors.maxBy(Comparator.comparing(SealAuthorizedInfoDto::getEffectiveTime)), Optional::get)));
        // 循环获取数据库中的数据 判断是否存在 不存在汇集删除 存在判断是否更新
        List<String> deleteSealList = new ArrayList<>();
        List<LetterMediationSeal> seals = new ArrayList<>();
        Date expireDate = DateUtils.addDays(new Date(), 10 * 365);
        for (LetterMediationSeal localAuthSeal : localAuthSeals) {
            if (sealAuthMap.containsKey(localAuthSeal.getSealId())) {
                // 如果存在则收集更新
                LetterMediationSeal seal = new LetterMediationSeal();
                SealAuthorizedInfoDto dto = sealAuthMap.get(localAuthSeal.getSealId());
                seal.setId(localAuthSeal.getId());
                seal.setSealId(dto.getSealId());
                seal.setSealBizType(dto.getSealBizType());
                seal.setName(dto.getAuthorizerOrgName());
                seal.setAlias(dto.getSealName());
                seal.setSealAuthBizId(dto.getSealAuthBizId());
                seal.setEffectiveTime(DateUtils.parseDateFull(DateUtils.convertDate(dto.getEffectiveTime().toString())));
                seal.setExpireTime(DateUtils.parseDateFull(DateUtils.convertDate(dto.getExpireTime().toString())));
                seal.setAuthorizerOrgId(dto.getAuthorizerOrgId());
                seal.setUpdateTime(new Date());
                String url = null;
                try {
                    url = upload(dto.getSealImageDownLoadUrl(), localAuthSeal.getSealId(), expireDate);
                } catch (Exception e) {
                    log.error("印章文件上传失败，失败原因{}", ExceptionUtil.stacktraceToString(e));
                }
                if (!StringUtils.isBlank(url)) {
                    log.info("印章文件已上传，文件名为：{} 地址为：{}", localAuthSeal.getSealId(), url);
                    seal.setImage(url);
                }
                seals.add(seal);
            } else {
                // 如果不存在则直接删除
                deleteSealList.add(localAuthSeal.getSealId());
            }
        }
        List<String> distinctIds = deleteSealList.stream().distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(distinctIds)) {
            Example example = new Example(LetterMediationSeal.class);
            example.and().andIn("sealId", distinctIds);
            example.and().andIsNotNull("depId");
            letterMediationSealMapper.deleteByExample(example);
            log.error("同步授权印章-删除已取消授权印章数量{},具体sealId为{}", deleteSealList.size(), deleteSealList);
        }
        // 更新
        if (!CollectionUtils.isEmpty(seals)) {
            for (LetterMediationSeal seal : seals)  {
                letterMediationSealMapper.updateByPrimaryKeySelective(seal);
            }
        }
    }

    private List<LetterMediationSeal> queryLocalAuthSeal() {
        Example example = new Example(LetterMediationSeal.class);
        example.and().andIsNotNull("depId");
        List<LetterMediationSeal> seals = letterMediationSealMapper.selectByExample(example);
        return seals;
    }

    private String upload(String url, String sealId, Date expireDate) throws IOException {
        InputStream inputStream = null;
        try {
            URL urls = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) urls.openConnection();
            connection.setConnectTimeout(10 * 1000);
            connection.setReadTimeout(15 * 1000);
            inputStream = connection.getInputStream();
            UploadCreatedFileStream uploadCreatedFileStream = new UploadCreatedFileStream();
            uploadCreatedFileStream.setInputStream(inputStream)
                    .setFileName(sealId + ".png")
                    .setBucket(systemConfig.getCaseFilesBucket())
                    .setExpireDate(expireDate)
                    .setLocalUrl(systemConfig.getMediationFilePath() + sealId + ".png");
            FileStorageStrategy fileStorageStrategy = fileStorageStrategyFactory.getStrategy(systemConfig.getChannelCode());
            return fileStorageStrategy.uploadCreatedFileByStream(uploadCreatedFileStream);
        } catch (Exception e) {
            log.error("根据Url获取图片的file上传,发生异常error,{}", ExceptionUtil.stacktraceToString(e));
            return null;
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    /*************************************************/

    /**
     * 删除停止合作的用户、公司案件和案件操作日志
     * @throws InterruptedException 中断异常
     */
    protected void autoDeleteUserAndCaseAndLog() throws InterruptedException {
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>开始执行删除不合作的公司案件数据");
        Date cooperationEndTime = DateUtils.addDays(new Date(), -180);
        // 逻辑删除停止合作的公司用户数据
        Integer userCount = userMapper.delUserForStopOrg(cooperationEndTime);
        int caseCount = 0;
        long endTime = DateUtils.addHour(DateUtils.getStartDateTime(new Date()), 7).getTime();
        while(true) {
            // 执行到7点还没有完成就停止任务
            long currentTime = System.currentTimeMillis();
            if (currentTime >= endTime) {
                break;
            }
            List<Long> caseIds = caseMapper.getDeleteCaseIds(cooperationEndTime,null);
            if (org.springframework.util.CollectionUtils.isEmpty(caseIds)) {
                break;
            }
            // 备份、删除案件
            int cnt = caseService.backupCaseAndLog(caseIds);
            caseCount = caseCount +cnt;
            log.info("不合作公司案件转移成功数量：{}", caseCount);
            TimeUnit.MILLISECONDS.sleep(200);
        }
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>结束执行删除不合作的公司案件" +
                "数据, 备份删除案件数量: {}，账号数量:{}", caseCount, userCount);
    }

    /**
     * 清理案件表碎片
     */
    protected void autoCleanFragment() {

    }

    /*******************同步机构自有印章**********************/
    /**
     * 同步机构自有印章
     */
    protected void autoTransferOrgOwnSeal() {
        Date start = new Date();
        log.info("同步机构自有印章信息-任务开始时间:{}", DateUtils.formatDate(start, DateUtils.FULL_DATE_FORMAT));
        // 同步已经认证授权的机构印章信息
        List<AuthOrgInfo> allAuthOrgInfo = getAllAuthOrgInfo();
        if (CollectionUtils.isEmpty(allAuthOrgInfo)) {
            log.info("暂无认证机构信息！");
            return;
        }
        // 查询对应印章信息
        List<String> organizationIds = allAuthOrgInfo.stream().map(AuthOrgInfo::getOrganizationId).collect(Collectors.toList());
        Map<String, AuthOrgInfo> orgMap = allAuthOrgInfo.stream().collect(Collectors.toMap(AuthOrgInfo::getOrganizationId, Function.identity(), (k1, k2) -> k1));
        getAllOrgOwnSeal(organizationIds, orgMap);
        Date end = new Date();
        log.info(
                "同步机构自有印章信息-任务结束时间:{},用时:{} 毫秒",
                DateUtils.formatDate(end, DateUtils.FULL_DATE_FORMAT),
                end.getTime() - start.getTime());

        if (systemConfig.getLocalDeploy()) {
            Date authStart = new Date();
            Date authEnd = new Date();
            log.info("同步机构自有印章授权信息-任务开始时间:{}", DateUtils.formatDate(authStart, DateUtils.FULL_DATE_FORMAT));
            dealOwnAuthSeal(allAuthOrgInfo);
            log.info(
                    "同步机构自有印章授权信息-任务结束时间:{},用时:{} 毫秒",
                    DateUtils.formatDate(authEnd, DateUtils.FULL_DATE_FORMAT),
                    authEnd.getTime() - authStart.getTime());
        }
    }

    /**
     * 同步自有印章授权信息
     * 只有本地化才会执行，如果不是本地化，授权信息信息会回调到saas
     * 注：只会拉取到只有api调用授权的授权印章信息，只会拉取到三种状态0-失效 1-正常 3-待生效
     * @param allAuthOrgInfo
     * @return
     */
    private void dealOwnAuthSeal(List<AuthOrgInfo> allAuthOrgInfo) {
        List<LetterMediationSeal> updateSeals = new ArrayList<>();
        List<LetterMediationSeal> resetSeals = new ArrayList<>();
        // 过滤掉未认证实名的机构信息
        List<AuthOrgInfo> authOrgInfos = allAuthOrgInfo.stream().filter(authOrgInfo -> authOrgInfo.getAuthorizedStatus() == 1).collect(Collectors.toList());

        // 查询当前机构下的所有授权给渠道商的印章信息
        for (AuthOrgInfo authOrg : authOrgInfos) {
            List<SealAuthorizedInfoDto> authSeals = eqianbaoSignatureProvider.getOrgSealExternalAuth(authOrg.getOrganizationId(), null, systemConfig.getEqianbaoOrgId());
            Map<String, List<SealAuthorizedInfoDto>> authSealMap = authSeals.stream().collect(Collectors.groupingBy(SealAuthorizedInfoDto::getSealId));
            // 查询对应机构下的所有印章信息
            Example example = new Example(LetterMediationSeal.class);
            example.and().andEqualTo("orgId", authOrg.getOrgId());
            example.and().andIsNull("depId");
            List<LetterMediationSeal> seals = letterMediationSealMapper.selectByExample(example);
            // 过滤出已申请授权的印章
            List<LetterMediationSeal> applySeals = seals.stream().filter(seal -> StringUtils.isNotBlank(seal.getSealAuthBizId())).collect(Collectors.toList());
            for (LetterMediationSeal seal : applySeals) {
                if (authSealMap.containsKey(seal.getSealId())) {
                    List<SealAuthorizedInfoDto> sealAuthorizedInfoDtos = authSealMap.get(seal.getSealId());
                    Map<String, SealAuthorizedInfoDto> map = sealAuthorizedInfoDtos.stream().collect(Collectors.toMap(SealAuthorizedInfoDto::getSealAuthBizId, f->f));
                    if (map.containsKey(seal.getSealAuthBizId())) {
                        SealAuthorizedInfoDto sealAuthorizedInfoDto = map.get(seal.getSealAuthBizId());
                        LetterMediationSeal letterMediationSeal = new LetterMediationSeal();
                        letterMediationSeal.setId(seal.getId());
                        letterMediationSeal.setAuthStatus(sealAuthorizedInfoDto.getAuthorizeStatus());
                        letterMediationSeal.setEffectiveTime(DateUtils.parseDateFull(DateUtils.convertDate(sealAuthorizedInfoDto.getEffectiveTime().toString())));
                        letterMediationSeal.setExpireTime(DateUtils.parseDateFull(DateUtils.convertDate(sealAuthorizedInfoDto.getExpireTime().toString())));
                        letterMediationSeal.setUpdateTime(new Date());
                        updateSeals.add(letterMediationSeal);
                    }else{
                        // 不存在授权信息，则将本地数据库中授权信息置空,防止本地化直接在e签宝控制台删除授权书，
                        // 调用印章授权信息时是获取不到该信息的。调用印章授权信息只能获取到0-失效，1- 正常，3- 待生效这三种状态。
                        seal.setAuthStatus(-1);
                        seal.setUpdateTime(new Date());
                        seal.setSealAuthUrl(null);
                        seal.setSealAuthBizId(null);
                        seal.setEffectiveTime(null);
                        seal.setExpireTime(null);
                        resetSeals.add(seal);
                    }
                }else{
                    // 不存在授权信息，则将本地数据库中授权信息置空,防止本地化直接在e签宝控制台删除授权书，
                    // 调用印章授权信息时是获取不到该信息的。调用印章授权信息只能获取到0-失效，1-正常，3-待生效这三种状态。
                    seal.setAuthStatus(-1);
                    seal.setUpdateTime(new Date());
                    seal.setSealAuthUrl(null);
                    seal.setSealAuthBizId(null);
                    seal.setEffectiveTime(null);
                    seal.setExpireTime(null);
                    resetSeals.add(seal);
                }
            }
        }
        if (!CollectionUtils.isEmpty(resetSeals)) {
            // 重置授权印章信息
            for (LetterMediationSeal letterMediationSeal : resetSeals) {
                letterMediationSealMapper.updateByPrimaryKey(letterMediationSeal);
            }
        }

        if (!CollectionUtils.isEmpty(updateSeals)) {
            for (LetterMediationSeal seal : updateSeals)  {
                letterMediationSealMapper.updateByPrimaryKeySelective(seal);
            }
        }
    }

    /**
     * 查询所有已认证机构下的自有印章 已存在的更新 未存在的插入
     * @param organizationIds 已认证机构id
     * @param orgInfoMap      认证信息
     */
    private void getAllOrgOwnSeal(List<String> organizationIds, Map<String, AuthOrgInfo> orgInfoMap) {
        for (String id: organizationIds) {
            AuthOrgInfo authOrgInfo = orgInfoMap.get(id);
            List<OrgOwnSealDto> orgOwnSeals;
            try {
                orgOwnSeals = eqianbaoSignatureProvider.getOrgOwnSeals(id);
            } catch (Exception e) {
                log.error("同步自有印章-机构id为{}出现异常{}",id, ExceptionUtil.stacktraceToString(e));
                throw new ApiException(e.getMessage());
            }
            if (CollectionUtils.isEmpty(orgOwnSeals)) {
                break;
            }
            Map<String, List<LetterMediationSeal>> sealMap = covertToSeal(orgOwnSeals, authOrgInfo);
            List<LetterMediationSeal> existSeals = sealMap.get("exist");
            List<LetterMediationSeal> noExistSeals = sealMap.get("noExist");
            // 更新
            if (!CollectionUtils.isEmpty(existSeals)) {
                for (LetterMediationSeal seal : existSeals)  {
                    letterMediationSealMapper.updateByPrimaryKeySelective(seal);
                }
            }
            if (!CollectionUtils.isEmpty(noExistSeals)) {
                letterMediationSealMapper.insertList(noExistSeals);
            }
        }
    }

    private Map<String, List<LetterMediationSeal>> covertToSeal(List<OrgOwnSealDto> orgOwnSealDtos, AuthOrgInfo authOrgInfo) {
        Date expireDate = DateUtils.addDays(new Date(), 10 * 365);
        // 查询已存在的印章信息
        List<String> sealIds = orgOwnSealDtos.stream().map(OrgOwnSealDto::getSealId).collect(Collectors.toList());
        Example example = new Example(LetterMediationSeal.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("sealId", sealIds);
        criteria.andIsNull("depId");
        criteria.andEqualTo("orgId", authOrgInfo.getOrgId());
        List<LetterMediationSeal> existSealList = letterMediationSealMapper.selectByExample(example);
        Map<String, Long> existMap = existSealList.stream().collect(Collectors.toMap(LetterMediationSeal::getSealId, LetterMediationSeal::getId));
        List<LetterMediationSeal> seals = orgOwnSealDtos.stream().map(dto -> {
            LetterMediationSeal seal = new LetterMediationSeal();
            seal.setOrgId(authOrgInfo.getOrgId());
            seal.setSealId(dto.getSealId());
            seal.setType(0);
            seal.setSealBizType(dto.getSealBizType());
            seal.setName(authOrgInfo.getOrgName());
            seal.setAlias(dto.getSealName());
            seal.setIsShow(1);
            seal.setUpdateTime(new Date());
            String url = null;
            try {
                url = upload(dto.getSealImageDownloadUrl(), seal.getSealId(), expireDate);
            } catch (Exception e) {
                log.error("印章文件上传失败，失败原因{}", ExceptionUtil.stacktraceToString(e));
            }
            if (StringUtils.isBlank(url)) {
                throw new ApiException("印章文件上传失败");
            }
            log.info("印章文件已上传至OSS。。。 文件名为：{} 地址为：{}", seal.getSealId(), url);
            seal.setImage(url);
            if (!existMap.containsKey(dto.getSealId())) {
                seal.setAuthStatus(-1);
                seal.setCreateTime(new Date());
            } else {
                seal.setId(existMap.get(dto.getSealId()));
            }
            return seal;
        }).collect(Collectors.toList());
        HashMap<String, List<LetterMediationSeal>> sealMap = new HashMap<>();
        // 分离存在数据数据
        if (!CollectionUtils.isEmpty(seals)) {
            List<LetterMediationSeal> existSeal = seals.stream().filter(seal -> existMap.containsKey(seal.getSealId())).collect(Collectors.toList());
            seals.removeAll(existSeal);
            sealMap.put("exist", existSeal);
            sealMap.put("noExist", seals);
            return sealMap;
        }
        return sealMap;
    }

    /**
     * 查询所有认证通过的机构信息
     * @return
     */
    private List<AuthOrgInfo> getAllAuthOrgInfo() {
        Example example = new Example(AuthOrgInfo.class);
        // 认证流程 1 : 已实名
        example.and().andEqualTo("realNamesStatus", 1);
        // 授权流程 1：已授权 2：授权中
        example.and().andIn("authorizedStatus", Arrays.asList(1,2));
        List<AuthOrgInfo> authOrgInfos = authOrgInfoService.selectByExample(example);
        return authOrgInfos;
    }
    /*****************************************/

    /**
     * 清空已停止合作的总公司callBack回调
     */
    protected void autoCleanOrgCallback() {
        Date start = new Date();
        log.info("定时任务-自动清空总公司callBack回调-任务开始时间:{}", DateUtils.formatDate(start, DateUtils.FULL_DATE_FORMAT));
        // 查询合作结束的总公司
        List<Company> companies = companyMapper.selectEndCooperationOrgs();
        for (Company company : companies) {
            try {
                company.setStatus(OrgEnums.Status.DELETE.getCode());
                companyMapper.updateByPrimaryKeySelective(company);
                duyanManager.setOrgCallback(company.getDuyanReferId(), "");
                newRobotManager.setFunctionCallback(company.getId(), StrUtil.EMPTY, CallbackEnums.Type.NOTIFY_CAMPAIGN_TAG);
                log.info("总公司：{} 设置回调地址成功！", company.getName());
            } catch (Exception e) {
                log.error("设置总公司：{} 回调地址出现异常！{}", company.getName(), ExceptionUtil.stacktraceToString(e));
            }
        }
        Date end = new Date();
        log.info("定时任务-自动清空总公司callBack回调-任务结束时间:{},用时:{} 毫秒", DateUtils.formatDate(end, DateUtils.FULL_DATE_FORMAT), end.getTime() - start.getTime());
    }

    /**
     * 用户数据统计
     *
     * @throws Exception 例外
     */
    protected void autoStatistics() throws Exception{
        Date statisticsDate= DateUtils.addDays(DateUtils.getTodayDateFormat(),-1);
        Date start=new Date();
        log.info("定时任务-统计数据-日期:{} 任务开始时间:{}", DateUtils.formatDate(statisticsDate),
                DateUtils.formatDate(start,DateUtils.FULL_DATE_FORMAT));
        userStatisticsService.statisticsTask(statisticsDate);
        Date end=new Date();
        log.info("定时任务-统计数据-日期:{} 任务结束时间:{},用时:{} 毫秒",DateUtils.formatDate(statisticsDate),
                DateUtils.formatDate(end,DateUtils.FULL_DATE_FORMAT),end.getTime()-start.getTime());
    }

    /**
     * 策略执行
     */
    protected void strategyExecute() {
        log.info("定时任务-开始策略执行");
        Example example = new Example(StrategyInfo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recovery", StrategyEnums.Recovery.NORMAL.getCode());
        criteria.andEqualTo("status", StrategyEnums.Status.ENABLE.getCode());
        criteria.andEqualTo("isDraft", StrategyEnums.Draft.NO.getCode());
        criteria.andEqualTo("type", StrategyEnums.Type.SCHEDULED.getCode());
        criteria.andLessThanOrEqualTo("nextExecTime", System.currentTimeMillis());
        List<StrategyInfo> strategyInfoList = strategyInfoService.selectByExample(example);
        if (ObjectUtil.isNotEmpty(strategyInfoList)){
            Set<Long> strategyOrgIds = strategyInfoList.stream().map(StrategyInfo::getOrgId).collect(Collectors.toSet());
            Example query = new Example(Company.class);
            query.createCriteria().andEqualTo("status", CompanyEnums.Status.NORMAL.getCode()).andIn("id",strategyOrgIds);
            List<Company> companies = companyService.selectByExample(query);

            Example switchQuery = new Example(OrgSwitch.class);
            switchQuery.createCriteria().andEqualTo("strategySwitch", OrgSwitchEnums.StrategySwitch.OPEN.getCode()).andIn("orgId",strategyOrgIds);
            List<OrgSwitch> orgSwitchs = orgSwitchMapper.selectByExample(switchQuery);

            for (StrategyInfo strategyInfo : strategyInfoList) {
                Boolean companyCheck = companies.stream().noneMatch(company -> ObjectUtil.equals(company.getId(), strategyInfo.getOrgId()));
                Boolean orgSwitchCheck = orgSwitchs.stream().noneMatch(orgSwitch -> ObjectUtil.equals(orgSwitch.getOrgId(), strategyInfo.getOrgId()));
                if (companyCheck || orgSwitchCheck) {
                    continue;
                }

                log.info("开始执行策略 id:{}", strategyInfo.getId());
                try {
                    Company company = companies.stream().filter(t->Objects.equals(t.getId(),strategyInfo.getOrgId())).findFirst().orElse(null);
                    if(company==null) {
                        continue;
                    }
                    UserSession userSession = new UserSession();
                    userSession.setId(strategyInfo.getUpdateBy());
                    userSession.setOrgId(strategyInfo.getOrgId());
                    userSession.setLanguage(company.getLanguage());
                    userSession.setRoleId(company.getDefaultSysRole());
                    strategyInfoService.execByStrategyId(strategyInfo.getId(), userSession);
                    log.info("策略执行结束 id:{}", strategyInfo.getId());
                } catch (Exception ex) {
                    log.error("策略执行异常 id:" + strategyInfo.getId(), ex);
                }

                try {
                    CronExpression expression = new CronExpression(strategyInfo.getExecTime());
                    expression.setTimeZone(TimeZone.getDefault());
                    Date nextTime = expression.getNextValidTimeAfter(new Date());
                    strategyInfo.setNextExecTime(nextTime.getTime());
                } catch (ParseException e) {
                    strategyInfo.setNextExecTime(null);
                }
                strategyInfoService.updateByPrimaryKey(strategyInfo);
            }
        }
        log.info("定时任务-结束策略执行");
    }

    /**
     * 更新外访过期状态
     */
    protected void autoUpdateVisitExpiredState() {
        Date start = new Date();
        log.info("定时任务-更新外访过期状态,任务开始时间:{}", DateUtils.formatDate(start, DateUtils.FULL_DATE_FORMAT));
        visitService.setVisitExpired();
        Date end = new Date();
        log.info(
                "定时任务-更新外访过期状态,任务结束时间:{},用时:{} 毫秒",
                DateUtils.formatDate(end, DateUtils.FULL_DATE_FORMAT),
                end.getTime() - start.getTime());
    }

    /**
     * 删除超过30天的外访轨迹信息
     */
    protected void autoDeleteVisitTrail() {
        Date start = new Date();
        log.info("定时任务-删除超过30天的外访轨迹信息,任务开始时间:{}", DateUtils.formatDate(start, DateUtils.FULL_DATE_FORMAT));
        visitService.deleteVisitTrail();
        Date end = new Date();
        log.info(
                "定时任务-删除超过30天的外访轨迹信息,任务结束时间:{},用时:{} 毫秒",
                DateUtils.formatDate(end, DateUtils.FULL_DATE_FORMAT),
                end.getTime() - start.getTime());
    }

    /**
     * 删除催记冷数据
     */
    protected void deleteOperationUseLess() throws InterruptedException {
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>开始执行删除不合作的公司催记数据");
        Date cooperationEndTime = DateUtils.addDays(new Date(), -180);
        List<Long> orgIds = companyService.getStopOrgId(cooperationEndTime);
        int totalCount = 0;
        long endTime = DateUtils.addHour(DateUtils.getStartDateTime(new Date()), 7).getTime();
        boolean flag = false;
        for (Long orgId : orgIds) {
            long startId = 0l;
            int operationCount = 0;
            while (true) {
                // 执行到7点还没有完成就停止任务
                long currentTime = System.currentTimeMillis();
                if (currentTime >= endTime) {
                    flag = true;
                    break;
                }
                // 根据公司id去es查询催记id
                List<Long> caseOperationIds = caseOperationService.getOperationIdFromEs(orgId,
                        CaseOperationEnums.DataType.COLD.getCode(), startId, 500, true);
                if (org.springframework.util.CollectionUtils.isEmpty(caseOperationIds)) {
                    break;
                }
                startId = caseOperationIds.get(caseOperationIds.size()-1) + 1;
                // 备份、删除催记
                Integer operation = caseOperationService.backupCaseOperationById(caseOperationIds);
                operationCount = operationCount + operation;
                TimeUnit.MILLISECONDS.sleep(200);
            }
            if (flag) {
                break;
            }
            totalCount = totalCount + operationCount;
            log.info("不合作公司orgId: {}，转移催记数量：{}", orgId, operationCount);
        }
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>结束执行删除不合作的公司" +
                "催记数据, 催记数量：{}", totalCount);
    }

    /**
     * 存储空间启用
     */
    protected void spaceEnable() {
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>开始启用已生效的存储空间");
        // 查询需要执行的数据
        List<SpaceRecord> spaceRecords = spaceRecordService.queryNotUsedRecord();
        log.info("需启用空间记录总数为{}", spaceRecords.size());
        if (!CollectionUtils.isEmpty(spaceRecords)) {
            enableSpace(spaceRecords);
        }
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>结束启用已生效的存储空间,启用空间记录个数为{}", spaceRecords.size());
    }

    /**
     * 启用存储空间
     * @param spaceRecords
     */
    @Transactional(rollbackFor = Exception.class)
    public void enableSpace(List<SpaceRecord> spaceRecords) {
        for (SpaceRecord spaceRecord : spaceRecords) {
            Integer size = spaceRecord.getSize();
            BigDecimal recordSize = new BigDecimal(size * 1024 * 1024);
            orgSpaceService.addTotalSpace(spaceRecord.getOrgId(), recordSize);
            spaceRecord.setStatus(SpaceEnums.Status.INUSE.getCode());
            spaceRecord.setUpdateTime(new Date());
            spaceRecordService.updateByPrimaryKeySelective(spaceRecord);
        }
    }

    /**
     * 存储空间过期
     */
    protected void spaceExpire() {
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>开始处理过期的存储空间");
        // 查询需要执行的数据
        List<SpaceRecord> spaceRecords = spaceRecordService.queryExpireRecord();
        log.info("需过期空间记录总数为{}", spaceRecords.size());
        if (!CollectionUtils.isEmpty(spaceRecords)) {
            expireSpace(spaceRecords);
        }
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>结束处理过期的存储空间,过期空间记录数量为{}", spaceRecords.size());
    }

    @Transactional(rollbackFor = Exception.class)
    public void expireSpace(List<SpaceRecord> spaceRecords) {
        for (SpaceRecord spaceRecord : spaceRecords) {
            Integer size = spaceRecord.getSize();
            BigDecimal recordSize = new BigDecimal(size * 1024 * 1024);
            orgSpaceService.subtractTotalSpace(spaceRecord.getOrgId(), recordSize);
            spaceRecord.setStatus(SpaceEnums.Status.EXPIRE.getCode());
            spaceRecord.setUpdateTime(new Date());
            spaceRecordService.updateByPrimaryKeySelective(spaceRecord);
        }
    }

    /**
     * 自动发送空间不足邮件
     */
    protected void autoSendSpaceLessMail() {
        Date start = new Date();
        log.info("定时任务-可使用存储空间不足系统提醒邮箱定时发送-任务开始时间:{}", DateUtils.formatDate(start, DateUtils.FULL_DATE_FORMAT));
        // 查询已开启提醒的公司
        List<Company> companies = companyService.querySystemEnableCompany();
        if (CollectionUtils.isEmpty(companies)) {
            log.info("定时任务-可使用存储空间不足系统提醒邮箱定时发送-不存在开启提醒公司，任务结束！");
            return;
        }
        Map<Long, Company> companyMap = companies.stream().collect(Collectors.toMap(Company::getId, f -> f));
        List<Long> orgIds = companies.stream().map(Company::getId).collect(Collectors.toList());
        List<SpaceRecordDTO> spaceRecords = spaceRecordService.queryExpireSpaceBySevenDaysSpace(orgIds);
        Map<Long, SpaceRecordDTO> spaceRecordMap = spaceRecords.stream().collect(Collectors.toMap(SpaceRecordDTO::getOrgId, f -> f));
        // 查询公司存储空间
        List<OrgSpace> orgSpaces = orgSpaceService.querySpaceByOrgIds(orgIds);

        for (OrgSpace orgSpace : orgSpaces) {
            BigDecimal freeSpace = orgSpace.getFreeSpace();
            BigDecimal totalSpace = orgSpace.getTotalSpace();
            BigDecimal useSpace = orgSpace.getUseSpace();

            BigDecimal dueSpace = BigDecimal.ZERO;
            if (spaceRecordMap.containsKey(orgSpace.getOrgId())) {
                SpaceRecordDTO spaceRecordDTO = spaceRecordMap.get(orgSpace.getOrgId());
                Integer dueSize = spaceRecordDTO.getTotalSize();
                dueSpace = new BigDecimal(dueSize * 1024 * 1024);
            }
            BigDecimal leaveSpace = freeSpace.add(totalSpace).subtract(useSpace).subtract(dueSpace);
            // 小于1g
            if (leaveSpace.compareTo(new BigDecimal(1024 * 1024)) < 0) {
                Company company = companyMap.get(orgSpace.getOrgId());
                try {
                    MailParam mailParam = new MailParam();
                    mailParam.setTo(company.getContactEmail());
                    mailParam.setText(SmsContants.SMS_CODE_MAIL_SPACE_CAPACITY);
                    mailParam.setSubject("安米智能催收系统服务可使用存储空间不足提醒");
                    mailManager.sendHtmlMail(mailParam);
                    log.info("总公司：{} 发送可使用存储空间不足提醒邮箱成功！", company.getName());
                } catch (Exception e) {
                    log.error("总公司：{} 发送可使用存储空间不足提醒邮箱失败！==>{}", company.getName(),ExceptionUtil.stacktraceToString(e));
                    throw new ApiException("发送可使用存储空间不足提醒短信失败!");
                }
            }
        }
        Date end = new Date();
        log.info("定时任务-可使用存储空间不足系统提醒邮箱定时发送-任务结束时间:{},用时:{} 毫秒", DateUtils.formatDate(end, DateUtils.FULL_DATE_FORMAT), end.getTime() - start.getTime());
    }

    /**
     * 自动发送空间不足短信
     */
    protected void autoSendSpaceLessMessage(){
        Date start = new Date();
        log.info("定时任务-可使用存储空间不足系统提醒短信定时发送-任务开始时间:{}", DateUtils.formatDate(start, DateUtils.FULL_DATE_FORMAT));
        List<Company> companies = companyService.querySystemEnableCompany();
        if (CollectionUtils.isEmpty(companies)) {
            log.info("定时任务-可使用存储空间不足系统提醒邮箱定时发送-不存在开启提醒公司，任务结束！");
            return;
        }
        Map<Long, Company> companyMap = companies.stream().collect(Collectors.toMap(Company::getId, f -> f));
        List<Long> orgIds = companies.stream().map(Company::getId).collect(Collectors.toList());
        List<SpaceRecordDTO> spaceRecords = spaceRecordService.queryExpireSpaceBySevenDaysSpace(orgIds);
        Map<Long, SpaceRecordDTO> spaceRecordMap = spaceRecords.stream().collect(Collectors.toMap(SpaceRecordDTO::getOrgId, f -> f));
        // 查询公司存储空间
        List<OrgSpace> orgSpaces = orgSpaceService.querySpaceByOrgIds(orgIds);

        for (OrgSpace orgSpace : orgSpaces) {
            BigDecimal freeSpace = orgSpace.getFreeSpace();
            BigDecimal totalSpace = orgSpace.getTotalSpace();
            BigDecimal useSpace = orgSpace.getUseSpace();

            BigDecimal dueSpace = BigDecimal.ZERO;
            if (spaceRecordMap.containsKey(orgSpace.getOrgId())) {
                SpaceRecordDTO spaceRecordDTO = spaceRecordMap.get(orgSpace.getOrgId());
                Integer dueSize = spaceRecordDTO.getTotalSize();
                dueSpace = new BigDecimal(dueSize * 1024 * 1024);
            }
            BigDecimal leaveSpace = freeSpace.add(totalSpace).subtract(useSpace).subtract(dueSpace);
            // 小于1g
            if (leaveSpace.compareTo(new BigDecimal(1024 * 1024)) < 0) {
                Company company = companyMap.get(orgSpace.getOrgId());
                try {
                    yunpianManager.sendSms(company.getContactPhone(),SmsContants.SMS_CODE_MAIL_SPACE_CAPACITY,false);
                    log.info("总公司：{} 发送可使用存储空间不足提醒短信成功！", company.getName());
                } catch (Exception e) {
                    log.error("总公司：{} 发送可使用存储空间不足提醒短信失败！==>{}", company.getName(),ExceptionUtil.stacktraceToString(e));
                    throw new ApiException("发送可使用存储空间不足提醒短信失败!");
                }
            }
        }
        Date end = new Date();
        log.info("定时任务-可使用存储空间不足系统提醒短信定时发送-任务结束时间:{},用时:{} 毫秒", DateUtils.formatDate(end, DateUtils.FULL_DATE_FORMAT), end.getTime() - start.getTime());
    }

    /**
     * 调解统计
     */
    protected void mediationStatistics() {
        log.info("当前时间:{} =====>> 开始进行调解报表统计",DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT));
        Date end = DateUtils.getTodayDateFormat();
        Date start = DateUtils.addDays(end,-1);
        mediationStatsService.mediationStatsData(start, end);
        log.info("当前时间:{} =====>> 结束调解报表统计",DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT));
    }

    /** 每天凌晨操作停催自动恢复 */
    public void caseAutoRestart() throws InterruptedException {
        Date autoRestartDate = DateUtils.getTodayDateFormat();
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>开始停催自动恢复");
        while (true) {
            List<Long> caseIds = caseMapper.getAutoRestartCaseId(autoRestartDate, 500,null);
            if (org.springframework.util.CollectionUtils.isEmpty(caseIds)) {
                break;
            }
            Example example = new Example(Case.class);
            example.and().andIn("id", caseIds);
            Case caseInfo = new Case();
            caseInfo.setCaseStatus(CaseEnums.CaseStatus.NORMAL.getCode());
            caseInfo.setUpdateTime(new Date());
            caseMapper.updateByExampleSelective(caseInfo, example);

            CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
            caseBatchUpdateEvent.setType(CaseLogEnums.Type.RESTART.getCode());
            caseBatchUpdateEvent.setUserSession(null);
            caseBatchUpdateEvent.setCaseIds(caseIds);
            caseBatchUpdateEvent.setTaskId(null);
            applicationContext.publishEvent(caseBatchUpdateEvent);

            TimeUnit.SECONDS.sleep(1);
        }
        log.info("当前时间:"+ DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT)+"=====>>结束停催自动恢复");
    }

    /**
     * 团队编号重置
     */
    protected void caseNoReset() {
        log.info("当前时间:{} =====>> 开始进行重置团队编号",DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT));
        depTeamService.resetDepSeq();
        log.info("当前时间:{} =====>> 结束团队编号",DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT));
    }

    protected void smsStatistics() {
        log.info("当前时间:{} =====>> 开始短信消费报表统计",DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT));
        Date end = DateUtils.getTodayDateFormat();
        Date start = DateUtils.addDays(end,-1);
        messageStatisticsService.smsSendStatsData(start, end);
        log.info("当前时间:{} =====>> 结束短信消费报表统计",DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT));
    }

    protected void pullSmsReply() {
        log.info("当前时间:{} =====>> 开始拉取度言短信回复信息",DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT));
        // 查询开启短信开关正常启用的公司信息
        List<Long> smsOrgIds = companyService.getSmsOrgId();
        if (ObjectUtil.isEmpty(smsOrgIds)) {
            log.info("定时任务-拉取度言短信回复信息-暂无公司数据需处理");
            return;
        }
        Date end = DateUtils.getTodayDateFormat();
        Date start = DateUtils.addDays(end,-1);
        for (Long orgId : smsOrgIds) {
            log.info("定时任务-拉取度言短信回复信息,公司id{}开始", orgId);
            Company company = companyService.selectByPrimaryKey(orgId);
            Date pullTime = company.getSmsReplyPullTime() == null ? start : company.getSmsReplyPullTime();
            if (DateUtil.compare(pullTime,end) >= 0) {
                continue;
            }
            smsReplyService.callSyncSmsReply(pullTime, end, orgId);
            log.info("定时任务-拉取度言短信回复信息,公司id{}结束", orgId);
        }

        log.info("当前时间:{} =====>> 结束拉取度言短信回复信息",DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT));
    }

    /**
     * 委外公司佣金统计
     */
    protected void commissionStatistics() {
        log.info("当前时间:{} =====>> 开始委外公司佣金统计",DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT));
        commissionCaseDetailService.commissionStatsData();
        log.info("当前时间:{} =====>> 结束委外公司佣金统计",DateUtils.formatDate(new Date(), DateUtils.FULL_DATE_FORMAT));
    }

    protected void robotCallTimeout() {
        log.info("催记回调超时扫描");
        Set<String> caseOperationIdList = stringRedisTemplate.opsForZSet().rangeByScore(RobotQueueService.CASE_OPERATION_TIMEOUT_KEY, 0, System.currentTimeMillis());
        if (org.springframework.util.CollectionUtils.isEmpty(caseOperationIdList)) {
            return;
        }
        List<Long> caseOperationIds = caseOperationIdList.stream().map(Long::parseLong).collect(Collectors.toList());
        List<List<Long>> subIdList = CmUtil.splitList(caseOperationIds,100);
        for(List<Long> ids:subIdList){
            List<CaseOperation> caseOperations = caseOperationService.selectByIdList(ids);
            List<Long> timeoutList = caseOperations.stream().filter(t->Objects.equals(t.getCallbackFlag(),0)).map(CaseOperation::getId).collect(Collectors.toList());
            if(!org.springframework.util.CollectionUtils.isEmpty(timeoutList)){
                caseOperationMapper.updateCallbackTimeout(timeoutList);
            }
        }
    }

    protected void workOrderHandleTimeout() {
        log.info("工单处理超时扫描......");
        Example example = new Example(WorkOrder.class);
        example.and().andNotEqualTo("status", WorkOrderConstant.ORDER_STATUS_END).andEqualTo("isPush", 0)
                .andLessThanOrEqualTo("validHandleTime", new Date());
        PageParam pageParam = new PageParam(1, 200);
        Page page = PageUtils.setPage(pageParam);
        List<WorkOrder> workOrders = workOrderMapper.selectByExample(example);
        List<Long> ids = new ArrayList<>();
        workOrders.forEach(workOrder -> {
            Boolean isPush = workOrderService.sendMessage(workOrder, 1);
            if (isPush) {
                ids.add(workOrder.getId());
            }
        });
        workOrderService.setWorkOrderIsPush(ids);
        int pages = page.getPages();
        if (pages < 2) {
            return;
        }
        for (int i=2; i<=pages; i++) {
            PageUtils.setPage(new PageParam(i, 200));
            List<WorkOrder> orders = workOrderMapper.selectByExample(example);
            List<Long> idList = new ArrayList<>();
            orders.forEach(workOrder -> {
                Boolean isPush = workOrderService.sendMessage(workOrder, 1);
                if (isPush) {
                    idList.add(workOrder.getId());
                }
            });
            workOrderService.setWorkOrderIsPush(idList);
        }
    }

    /**
     * 申请自动超时
     */
    protected void autoApplyTimeout() {
        Date endTime = DateUtil.truncate(new Date(), DateField.MINUTE);
        Date startTime = DateUtil.offsetMinute(endTime,-1);
        log.info("申请自动超时,startTime:{},endTime:{}",DateUtil.formatDateTime(startTime),DateUtil.formatDateTime(endTime));
        FlowEnums.BusinessType[] values = FlowEnums.BusinessType.values();
        for (int i=0;i<values.length;i++) {
            FlowEnums.BusinessType businessType = values[i];
            try {
                findAndExecuteTimeout(businessType,startTime,endTime);
            } catch (Exception e){
                log.error("{}自动超时处理异常,{}",businessType.getMsg(),ExceptionUtil.stacktraceToString(e));
            }
        }
    }

    /**
     * 查找并执行超时
     *
     * @param businessType 业务类型
     * @param startTime    开始时间
     * @param endTime      结束时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void findAndExecuteTimeout(FlowEnums.BusinessType businessType, Date startTime, Date endTime) {
        List<Long> applyIds = new ArrayList<>();
        List<Long> flowNodeImageIds;
        List<FlowHandleRecord> flowHandleRecords;
        switch (businessType.getCode()) {
            case 0:
                applyIds = antiFraudService.timeOutRecordIds(startTime,endTime);
                antiFraudService.updateStatusWithTimeOut(applyIds);
                break;
            case 1:
                applyIds = letterApplyService.timeOutRecordIds(startTime,endTime);
                letterApplyService.updateStatusWithTimeOut(applyIds);
                break;
            case 2:
                applyIds = caseCooperationApplyService.timeOutRecordIds(startTime,endTime);
                caseCooperationApplyService.updateStateWithTimeOut(applyIds);
                break;
            case 3:
                applyIds = visitAuditService.timeOutRecordIds(startTime,endTime);
                visitAuditService.updateStateWithTimeOut(applyIds);
                break;
            case 4:
                applyIds = lawsuitApplyService.timeOutRecordIds(startTime,endTime);
                lawsuitApplyService.updateAuditStatusWithTimeOut(applyIds);
                break;
            case 5:
                applyIds = mediateAuditService.timeOutRecordIds(startTime,endTime);
                mediateAuditService.updateStateWithTimeOut(applyIds);
                break;
            case 6:
            case 11:
                applyIds = verifyApplyService.timeOutRecordIds(startTime,endTime,businessType);
                verifyApplyService.updateStatusWithTimeOut(applyIds);
                break;
            case 7:
                applyIds = assistApplyService.timeOutRecordIds(startTime,endTime);
                assistApplyService.updateStatusWithTimeOut(applyIds);
                break;
            case 8:
            case 9:
                applyIds = repaymentService.timeOutRecordIds(startTime,endTime,businessType);
                repaymentService.updateApplyStatusWithTimeOut(applyIds);
                break;
            case 10:
                applyIds = reductionService.timeOutRecordIds(startTime,endTime);
                reductionService.updateStatusWithTimeOut(applyIds);
                break;
            case 12:
                applyIds = caseApplyService.timeOutRecordIds(startTime,endTime);
                caseApplyService.updateStatusWithTimeOut(applyIds);
                break;
            default:
                break;
        }

        flowNodeImageIds = flowNodeImageService.getIngFlowNodeImageIds(applyIds, businessType);
        flowHandleRecords = flowHandleRecordService.getIngRecordGroupSingle(flowNodeImageIds);

        flowNodeImageService.updateHandleStatusWithTimeOut(flowNodeImageIds);
        flowHandleRecordService.updateStatusAndCreateTimeOutRecord(flowNodeImageIds,flowHandleRecords);
    }
}
