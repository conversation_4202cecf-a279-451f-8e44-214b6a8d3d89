package com.anmi.collection.scheduled.async.filestore;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.anmi.collection.entity.requset.file.FileUpload;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseTemplateService;
import com.anmi.collection.service.I18nService;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.domain.cases.CaseTemplate;
import com.anmi.domain.user.Company;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 员工批量导入任务
 *
 * <AUTHOR>
 * @date 2023/07/27
 */
@Slf4j
public class UserImportTask extends BaseFileStoreTask {

    public static final int READ_SIZE = 300;

    @Resource private CaseTemplateService caseTemplateService;
    @Resource private I18nService i18nService;

    @XxlJob("userImportHandler")
    public  ReturnT<String> userImportHandler() {
        log.info("开启线程循环处理员工批量导入...");
        try {
            LogbackUtil.insertTrace(LogbackUtil.generateTrace());
            startFileStore(KeyCache.FILE_USER_KEY_LIST);
        } catch (Exception e) {
            log.error("员工批量导入出现全局异常！", e);
        } finally {
            LogbackUtil.removeTrace();
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(String fileKey){
        Long storeId = Long.valueOf(StringUtils.substringAfterLast(fileKey, "_"));
        log.info("=======开始处理员工批量导入数据：=======storeId:{}",storeId);
        //读取文件信息
        Map<Object,Object> storeMap= redisUtil.hget(fileKey);
        Integer success=Integer.valueOf(storeMap.get("success").toString());
        Integer error=Integer.valueOf(storeMap.get("error").toString());
        Integer surplus = Integer.valueOf(storeMap.get("surplus").toString());
        Integer total = Integer.valueOf(storeMap.get("total").toString());
        String ipAddr = storeMap.get("ip").toString();
        try {
            UserSession userSession = null;
            FileUpload fileUpload = null;
            String userMsg = storeMap.get("user").toString();
            if (StrUtil.isNotBlank(userMsg)) {
                userSession = JSON.parseObject(userMsg, UserSession.class);
            }
            String fileUploadMsg = storeMap.get("file_upload").toString();
            if (StrUtil.isNotBlank(fileUploadMsg)) {
                fileUpload = JSON.parseObject(fileUploadMsg, FileUpload.class);
            }

            Company company = companyService.selectByPrimaryKey(userSession.getOrgId());
            CaseTemplate caseTemplate = caseTemplateService.selectByPrimaryKey(fileUpload.getTemplateId());
            i18nService.convertCaseTemplate(Collections.singletonList(caseTemplate),company.getLanguage());

            List<String> dataRows;
            String valueListKey = KeyCache.FILE_USER_VALUE_LIST + storeId;
            while (surplus < total) {
                Integer end = surplus + READ_SIZE - 1;
                if (end > total - 1) {
                    end = total - 1;
                }
                dataRows = redisUtil.lGet(valueListKey, surplus, end);

                // 员工数据处理
                Integer subSuccessAmt = fileStoreService.usersHandle(storeId,dataRows,userSession,ipAddr);

                // 更新redis的file的扩展数据
                success = success + subSuccessAmt;
                error = error + dataRows.size() - subSuccessAmt;
                surplus = end + 1;
                Map<Object,Object> values = new HashMap<>();
                values.put("success", String.valueOf(success));
                values.put("error", String.valueOf(error));
                values.put("surplus", String.valueOf(surplus));
                redisUtil.hset(fileKey, values);
                // 睡眠一下
                Thread.sleep(100);
            }
            //数据全部更新完成，生成错误excel
            fileStoreService.handleFileStore(storeId, caseTemplate, success, 0L, total, company.getLanguage());
            //删除redis数组缓存
            redisUtil.lRemove(KeyCache.FILE_USER_KEY_LIST, 0, fileKey);
            redisUtil.expireDays(fileKey, 3);
            redisUtil.del(valueListKey);
            log.info("=======结束员工批量导入数据：=======storeId:{}",storeId);
        } catch (Exception e) {
            // 处理失败文件转存到另一个list
            redisUtil.lRemove(KeyCache.FILE_USER_KEY_LIST, 0, fileKey);
            redisUtil.lSetFromLeft(KeyCache.FILE_USER_FAIL_LIST, fileKey);
            // 更新fileStore状态为失败
            fileStoreService.updateFileFail(storeId,total,success,0L);
            log.error("storeId:{} 员工批量导入数据出现异常！{}", storeId , ExceptionUtil.stacktraceToString(e));
        }
    }
}
