package com.anmi.collection.scheduled.batch;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 同步机构自有印章
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
public class SyncOrgOwnSealJob extends BaseTask {

    /**
     * @Scheduled(cron = "0 0/10 * * * ? ")
     */
    @XxlJob("syncOrgOwnSealHandler")
    public ReturnT<String> syncOrgOwnSealHandler() {
        super.autoTransferOrgOwnSeal();
        return ReturnT.SUCCESS;
    }
}
