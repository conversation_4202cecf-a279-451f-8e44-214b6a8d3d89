package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.common.enums.CaseOperationEnums;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.exception.ApiException;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseOperationService;
import com.anmi.collection.service.CaseOperationUseLessService;
import com.anmi.collection.service.UserService;
import com.anmi.collection.utils.CollectionUtils;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.domain.cases.AsyncTask;
import com.anmi.domain.cases.CaseOperation;
import com.anmi.domain.cases.CaseOperationUseLess;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class CaseOperationDelTask extends BaseAsyncTask {

    @Autowired private CaseOperationService caseOperationService;
    @Autowired private UserService userService;
    @Autowired private CaseOperationUseLessService caseOperationUseLessService;

    @XxlJob("caseOperationDelHandler")
    public ReturnT<String> caseOperationDelHandler() {
        try {
            startAsyncTask(KeyCache.CASE_OPERATION_DEL_TASK_ID_LIST);
        } catch (Exception e) {
            log.error("催记删除出现全局异常！", e);
        }
        return ReturnT.SUCCESS;
    }

    @Override
    protected void execute(Long taskId) {
        Set<String> caseOperationIdList = redisUtil.sGet(KeyCache.CASE_OPERATION_DEL_TASK_VALUES + taskId);
        AsyncTask asyncTask = null;
        try {
            if (caseOperationIdList == null || caseOperationIdList.isEmpty()) {
                throw new ApiException("任务对应案件为空");
            }
            asyncTask = asyncTaskService.selectByPrimaryKey(taskId);
            if (asyncTask == null) {
                throw new ApiException("催记删除操作任务不存在");
            }
            //更新任务状态，开始处理
            operationDel(asyncTask, Lists.newArrayList(caseOperationIdList));
            //更新任务成功
            asyncTaskService.updateTaskFinish(asyncTask, null);
        } catch (Exception e) {
            // 任务失败
            asyncTaskService.updateTaskFinish(asyncTask, e.getMessage());
            log.error("催记删除操作任务,taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
        } finally {
            //解除状态保护
            redisUtil.sRemove(KeyCache.CASE_OPERATION_PROTECT_EXIST_IDS + asyncTask.getOrgId(), caseOperationIdList.toArray());
            //任务列表移除任务id
            redisUtil.lRemove(KeyCache.CASE_OPERATION_DEL_TASK_ID_LIST, 0, taskId.toString());
        }
    }

    private void operationDel(AsyncTask asyncTask, List<String> caseOperationIdList) {
        Integer start = 0;
        Integer end = 0;
        UserSession userSession = new UserSession();
        userSession.setOrgId(asyncTask.getOrgId());
        userSession.setId(asyncTask.getCreateBy());
        userSession.setName(userService.getNames(userSession.getOrgId()).get(asyncTask.getCreateBy()));
        while (start < caseOperationIdList.size()) {
            try {
                end = start + 1000;
                if (end > caseOperationIdList.size()) {
                    end = caseOperationIdList.size();
                }
                List<String> subIdList = caseOperationIdList.subList(start, end);
                List<Long> idList = subIdList.stream().map(i -> Long.valueOf(i)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(idList)) {
                    continue;
                }

                Map<String, CaseOperationEnums.DataType> dataTypeMap = new HashMap<>();
                //批量执行删除
                List<CaseOperation> caseOperationList = selectByIds(idList,dataTypeMap);
                CaseOperationEnums.DataType dataTypeEnum = dataTypeMap.get("dataType");
                caseOperationService.batchDelCaseOperation(caseOperationList, userSession, asyncTask.getId(),dataTypeEnum.getCode());
                //更新任务成功数
                asyncTaskService.updateTaskSuccessAmt(asyncTask, Long.valueOf(subIdList.size()));
                //成功的案件移（留下的则是失败的案件）
                redisUtil.sRemove(KeyCache.CASE_OPERATION_DEL_TASK_VALUES + asyncTask.getId(), subIdList.toArray(new String[subIdList.size()]));
                // 睡眠一下
                Thread.sleep(100);
            } catch (Exception e) {
                log.error("催记删除操作任务,taskId:{},失败！{}", asyncTask.getId(), ExceptionUtil.stacktraceToString(e));
            } finally {
                start = end;
            }
        }
    }

    private List<CaseOperation> selectByIds(List<Long> idList, Map<String,CaseOperationEnums.DataType> dataTypeMap) {
        Example example = new Example(CaseOperation.class);
        example.selectProperties("id");
        example.selectProperties("caseId");
        example.createCriteria().andIn("id", idList);
        List<CaseOperation> caseOperations = caseOperationService.selectByExample(example);
        dataTypeMap.put("dataType",CaseOperationEnums.DataType.HEAT);
        if (ObjectUtil.isEmpty(caseOperations)){
            Example caseExample = new Example(CaseOperationUseLess.class);
            caseExample.selectProperties("id");
            caseExample.selectProperties("caseId");
            caseExample.createCriteria().andIn("id", idList);
            List<CaseOperationUseLess> caseOperationUseLessList = caseOperationUseLessService.selectByExample(caseExample);
            caseOperations = BeanUtil.copyToList(caseOperationUseLessList,CaseOperation.class);
            dataTypeMap.put("dataType",CaseOperationEnums.DataType.COLD);
        }
        return caseOperations;
    }

}
