package com.anmi.collection.scheduled.async.asynctask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.anmi.collection.common.enums.AsyncTaskEnums;
import com.anmi.collection.common.enums.CaseLogEnums;
import com.anmi.collection.entity.requset.amc.CaseManualAllotParam;
import com.anmi.collection.entity.requset.sys.user.UserSession;
import com.anmi.collection.event.CaseBatchUpdateEvent;
import com.anmi.collection.redis.KeyCache;
import com.anmi.collection.service.CaseOperationWayRelService;
import com.anmi.collection.utils.LogbackUtil;
import com.anmi.collection.utils.dict.JsonUtils;
import com.anmi.domain.cases.AsyncTask;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 手动分案(催收机构类型、催收手段)
 *
 * <AUTHOR>
 * @date 2023/06/20
 */
@Slf4j
public class CaseManualAllotTask extends BaseAsyncTask {

  @Resource private CaseOperationWayRelService caseOperationWayRelService;
  @Resource private ApplicationContext applicationContext;

  @XxlJob("caseManualAllotHandler")
  public ReturnT<String> caseManualAllotHandler() {
    try {
      startAsyncTask(KeyCache.CASE_MANUAL_ALLOT_TASK_ID_LIST);
    } catch (Exception e) {
      log.error("手动分案(催收机构类型、催收手段)出现全局异常！", e);
    }
    return ReturnT.SUCCESS;
  }

  @Override
  protected void execute(Long taskId) {
    AsyncTask asyncTask = asyncTaskService.selectByPrimaryKey(taskId);
    asyncTask.setSuccessAmt(0L);
    Set<Long> caseIds = redisUtil.sGet(KeyCache.CASE_MANUAL_ALLOT_TASK_CASES + taskId).stream().map(Long::parseLong).collect(Collectors.toSet());
    try {
      String queryString = asyncTask.getFieldJson().get("query");
      CaseManualAllotParam param = JsonUtils.fromJson(queryString, CaseManualAllotParam.class);
      Assert.notNull(param, "参数不能为空");
      String sessionUserStr = asyncTask.getFieldJson().get("userSession");
      UserSession userSession = JsonUtils.fromJson(sessionUserStr, UserSession.class);
      Assert.notNull(userSession, "userSession不能为空");

      List<List<Long>> caseIdLists = CollUtil.split(caseIds, 2000);
      caseIdLists.forEach(caseIdList->{
        CaseBatchUpdateEvent caseBatchUpdateEvent = new CaseBatchUpdateEvent(this, null);
        caseBatchUpdateEvent.setType(CaseLogEnums.Type.MANUAL_ALLOT.getCode());
        caseBatchUpdateEvent.setUserSession(userSession);
        caseBatchUpdateEvent.setCaseIds(caseIdList);
        caseBatchUpdateEvent.setTaskId(taskId);
        applicationContext.publishEvent(caseBatchUpdateEvent);

        caseOperationWayRelService.manualAllot(caseIdList,param.getResultAllotAgent(), param.getSwitchType(), param.getResultOperationWays());

        asyncTask.setStatus(AsyncTaskEnums.Status.SUCCESS.getCode());
        asyncTask.setSuccessAmt(asyncTask.getSuccessAmt() + caseIdList.size());
        asyncTaskService.updateByPrimaryKeySelective(asyncTask);
      });
    } catch (Exception e) {
      log.error("手动分案(催收机构类型、催收手段),taskId:{},失败！{}", taskId, ExceptionUtil.stacktraceToString(e));
      asyncTaskService.updateTaskFinish(asyncTask, e.getMessage());
    } finally {
      redisUtil.lRemove(KeyCache.CASE_MANUAL_ALLOT_TASK_ID_LIST, 0, taskId.toString());
      redisUtil.sRemove(KeyCache.CASE_PROTECT_EXIST_IDS+asyncTask.getOrgId(),caseIds.stream().map(String::valueOf).toArray(String[]::new));
      redisUtil.del(KeyCache.CASE_MANUAL_ALLOT_TASK_CASES+taskId);
    }
  }
}
