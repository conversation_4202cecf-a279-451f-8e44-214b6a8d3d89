package com.anmi.collection.algorithm;


import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 期望比例平均算法 根据原对象的指标匹配目标对象 时间复杂度MAX(M*N,N*logN,M*logM) 一般 M<N and M>logN 所以
 *     时间复杂度可视为M*N
 */
public class ExptRatioAvgSourceFirst<S, T> implements AllotAlgorithm<S, T> {

  /** 目标分配比例 */
  private final Function<T, Integer> targetRatioSupplier;

  // 目标已分配的度量，该字段用于分案时需考虑分配对象已已拥有的案件，从而做到对分配对象尽量平均，而不只是针对本次所选案件数量做到平均，开关控制
  private final Function<T, BigDecimal> targetAllottedSupplier;
  // 度量提供者：如金额度量
  private final Function<S, BigDecimal> allocMetricsSupplier;

  private final BiConsumer<S, T> sourceAllocResultHolder;

  public ExptRatioAvgSourceFirst(
      Function<S, BigDecimal> allocMetricsSupplier,
      Function<T, Integer> targetRatioSupplier,
      Function<T, BigDecimal> targetAllottedSupplier,
      BiConsumer<S, T> sourceAllocResultHolder) {
    this.allocMetricsSupplier = allocMetricsSupplier;
    this.targetRatioSupplier = targetRatioSupplier;
    this.targetAllottedSupplier = targetAllottedSupplier;
    this.sourceAllocResultHolder = sourceAllocResultHolder;
  }

  @Override
  public List<S> allot(List<S> sourceList, List<T> targetList) {
    List<TargetWrapper<T>> list = new LinkedList<>();
    // 总度量
    BigDecimal totSourceMetrics = new BigDecimal(0);
    // 总比例 = 比例占比之和
    BigDecimal totRate = new BigDecimal(0);
    // 总数量 = 案件条数（如果共债分配，共债案件会合成一条）
    BigDecimal totSourceNum = new BigDecimal(sourceList.size());
    for (S s : sourceList) {
      totSourceMetrics = totSourceMetrics.add(allocMetricsSupplier.apply(s));
    }
    for (T t : targetList) {
      totRate = totRate.add(new BigDecimal(targetRatioSupplier.apply(t)));
    }
    // 平均数量
    BigDecimal avgSourceNum = totSourceNum.divide(totRate, 5, BigDecimal.ROUND_HALF_UP);
    // 平均度量
    BigDecimal avgSourceMetrics = totSourceMetrics.divide(totRate, 5, BigDecimal.ROUND_HALF_UP);
    int totExpectedSourceNum = 0;
    for (T t : targetList) {
      // 分案对象的比例占比 （分案对象：催员、部门、机构、委外机构）
      BigDecimal tarRate = new BigDecimal(targetRatioSupplier.apply(t));
      // 分案对象期望数量 = 平均数量 * 对象比例占比
      BigDecimal allocatedSourceNumExpt = avgSourceNum.multiply(tarRate);
      // 有可能平均数量不是整数，这里取整，后续会有差值处理
      int allocatedSourceNum = allocatedSourceNumExpt.intValue();
      BigDecimal diff =
          allocatedSourceNumExpt
              .subtract(BigDecimal.valueOf(allocatedSourceNum))
              .setScale(2, BigDecimal.ROUND_DOWN);
      // 分案对象期望度量（金额）= 平均度量 * 对象比例占比
      BigDecimal allocatedSourceMetrics = avgSourceMetrics.multiply(tarRate);
      // 分案对象已分配的度量，这里防止业务侧分案不需要考虑已分配的，所以需空值处理，以防空指针。
      BigDecimal allottedMetrics = targetAllottedSupplier.apply(t);
      if (Objects.isNull(allottedMetrics)) {
        allottedMetrics = BigDecimal.ZERO;
      }
      // 期望度量减去已分配的度量  这里有可能是负数了，说明当前分配对象已拥有的度量已经超过这次的期望度量了
      allocatedSourceMetrics = allocatedSourceMetrics.subtract(allottedMetrics);
      TargetWrapper<T> targetWrapper =
          new TargetWrapper<>(t, allocatedSourceMetrics, diff, allocatedSourceNum);
      list.add(targetWrapper);
      totExpectedSourceNum = totExpectedSourceNum + allocatedSourceNum;
    }
    // 处理因不能整除剩下的数量
    int remainSourceNum = sourceList.size() - totExpectedSourceNum;
    if (remainSourceNum > 0) {
      list.sort(Comparator.comparing(TargetWrapper::getExpectNumDiff, Comparator.reverseOrder()));
      for (int i = 0; i < remainSourceNum; i++) {
        int index = i % list.size();
        list.get(index).setExpectTotalNum(list.get(index).getExpectTotalNum() + 1);
      }
    }
    // 按度量逆序排序
    sourceList.sort(Comparator.comparing(allocMetricsSupplier).reversed());
    list = list.stream().filter(obj -> obj.getExpectTotalNum() > 0).collect(Collectors.toList());
    // 对目标分案进行期望度量正负拆分，先往正期望度量分，剩下的再往负期望度量分
    List<TargetWrapper<T>> positiveList = list.stream().filter(
            obj -> obj.getExpectTotalMetrics().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
    List<TargetWrapper<T>> negativeList = list.stream().filter(
            obj -> obj.getExpectTotalMetrics().compareTo(BigDecimal.ZERO) <= 0).collect(Collectors.toList());
    int allottedNum = 0;
    if (positiveList != null && positiveList.size() > 0) {
      for (S s : sourceList) {
        // 遍历案件，进行正数期望度量目标分配
        allocateSourceToTarget(s, positiveList);
        allottedNum++;
        // 每次分配之后对象的期望度量都会减去这次分配给它的度量，有可能减为负数期望度量了，这时候需要把其移入负期望度量目标对象list
        toNegativeList(positiveList, negativeList);
        if (positiveList.size() == 0) {
          break;
        }
      }
    }
    if (negativeList != null && negativeList.size() > 0) {
      // 进行负期望目标分配
      for (int i = allottedNum; i < sourceList.size(); i++) {
        S s = sourceList.get(i);
        allocateSourceToTarget(s, negativeList);
        if (negativeList.size() == 0) {
          break;
        }
      }
    }
    return sourceList;
  }

  private void toNegativeList(List<TargetWrapper<T>> positiveList, List<TargetWrapper<T>> negativeList) {
    Iterator<TargetWrapper<T>> iterator = positiveList.iterator();
    while (iterator.hasNext()){
      TargetWrapper<T> wrapper = iterator.next();
      if (wrapper.getExpectTotalMetrics().compareTo(BigDecimal.ZERO) <= 0) {
        negativeList.add(wrapper);
        iterator.remove();
      }
    }
  }

  private void allocateSourceToTarget(S s, List<TargetWrapper<T>> list) {
    TargetWrapper<T> closestTargetWrapper = getClosestTargetWrapper(s, list);
    sourceAllocResultHolder.accept(s, closestTargetWrapper.getData());
    closestTargetWrapper.setExpectTotalNum(closestTargetWrapper.getExpectTotalNum() - 1);
    closestTargetWrapper.setExpectTotalMetrics(
        closestTargetWrapper.getExpectTotalMetrics().subtract(allocMetricsSupplier.apply(s)));
    if (closestTargetWrapper.getExpectTotalNum() <= 0) {
      list.remove(closestTargetWrapper);
    }
  }

  private TargetWrapper<T> getClosestTargetWrapper(S s, List<TargetWrapper<T>> list) {
    BigDecimal distance = new BigDecimal(Long.MAX_VALUE);
    TargetWrapper<T> result = null;
    for (TargetWrapper<T> targetWrapper : list) {
      BigDecimal avgMetrics =
          targetWrapper
              .getExpectTotalMetrics()
              .divide(
                  new BigDecimal(targetWrapper.getExpectTotalNum()), 5, BigDecimal.ROUND_HALF_UP);
      BigDecimal diff =
          this.allocMetricsSupplier
              .apply(s)
              .subtract(avgMetrics)
              .setScale(5, BigDecimal.ROUND_HALF_DOWN)
              .abs();
      if (diff.compareTo(distance) < 0) {
        result = targetWrapper;
        distance = diff;
      }
    }
    return result;
  }
}
