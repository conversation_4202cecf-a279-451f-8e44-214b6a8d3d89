package com.anmi.collection.algorithm;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

/** <AUTHOR> */
@Data
public class AllotTargetBO {
  private Long id;
  private Integer rate;
  private BigDecimal allottedMetrics;

  public AllotTargetBO(Long id, Integer rate) {
    this.id = id;
    this.rate = rate;
  }
  public AllotTargetBO(Long id, Integer rate, BigDecimal allottedMetrics) {
    this.id = id;
    this.rate = rate;
    this.allottedMetrics = allottedMetrics;
  }
}
