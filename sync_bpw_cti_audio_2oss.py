# 注释：
# 每日 轮询查询cti的open接口
# 下载并上传到甲方OSS
# 由于全局解释器锁（GIL, Global Interpreter Lock）‌ 的存在，Python 的多线程在 CPU 密集型任务中并不能真正实现并行执行，支持的并行有限
# 预估录音为1M的wav格式的文件，一天2W条有效录音即接通（outcome = SUCCESS）的催记，上传约为 2W * 1M 约等于 20GB ，预估的实际催记应该 X 几十倍，可以支撑几十万级别, 如果需要大量并行请使用cython等扩展

###<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<  import  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>###
import signal
import time
from concurrent.futures import ThreadPoolExecutor, wait
from datetime import datetime
from typing import Optional
from urllib.parse import urlparse

import requests
from apscheduler.jobstores import redis
from apscheduler.schedulers import background as bg
from oss2 import Bucket

from NacosHelper import NacosHelper
from utils.base_utils import init_base_conf, heartbeat

###<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<  config  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>###
script_name = "sync_bpw_cti_audio_2oss"
config = NacosHelper().init_config()
logger, fileUtil, ossSwitch, esSwitch, encryptSwitch, encryptMode, bucket, mysql_pool, taskUtil, redis_pool = (
    init_base_conf(script_name, config))
conn = redis.Redis(connection_pool=redis_pool)

ossEndpoint = config["base"]["ossEndpoint"]
API_KEY = config["base"]["apikey"]

# === 配置参数 ===
# 使用经验值，应该设为CPU核数的1.5倍，但要考虑服务器是否还有其他负载
worker_num = 2
API_URL = "https://open.duyansoft.com/api/v2/call_log"
record_url = "https://resource.duyansoft.com/recording/{0}/{1}/{2}.wav"

start_size_key = script_name + "_pagenum"
max_id_key = script_name + "_maxid"
PAGE_SIZE = 30

# 可选参数（根据需要填写）
TEAM_ID = None
MEMBER_ID = None
TARGET = None
INCLUDE_VARS = False

HEADERS = {
    "Content-Type": "application/json"
}


###<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<  logic  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>###

#  enable scheduler heartbeat and multiply processing (suitable not really)
#  get audio from cti openApi
#  open upload file stream to OSS , action as a proxy
#  check and insert into mysql meta table
def get_start_page_key() -> Optional[int]:
    try:
        current_date = datetime.now().strftime("%Y%m%d")  # 格式化为20250707
        key = f"{start_size_key}_{current_date}"
        value = conn.get(key)
        if value is not None:
            try:
                return int(value)
            except (ValueError, UnicodeDecodeError) as e:
                logger.warning(f"Redis key {start_size_key} 解析失败: {e}")
                return None
        return None
    except Exception as e:
        logger.error(f"Redis 获取数据失败: {e}", exc_info=True)
        return None
    # 不需要手动release，连接会自动返回到池中


def get_last_id() -> Optional[int]:
    try:
        current_date = datetime.now().strftime("%Y%m%d")  # 格式化为20250707
        key = f"{max_id_key}_{current_date}"
        value = conn.get(key)
        if value is not None:
            try:
                return int(value)
            except (ValueError, UnicodeDecodeError) as e:
                logger.warning(f"Redis key {max_id_key} 解析失败: {e}")
                return 0
        return 0
    except Exception as e:
        logger.error(f"Redis 获取数据失败: {e}", exc_info=True)
        return 0


def set_start_page_key(page_num: int) -> bool:
    try:
        current_date = datetime.now().strftime("%Y%m%d")  # 格式化为20250707
        key = f"{start_size_key}_{current_date}"
        conn.set(key, str(page_num), ex=24 * 3600)
        return True
    except Exception as e:
        logger.error(f"Redis 设置数据失败: {e}", exc_info=True)
        return False


def set_last_id_key(last_id: int) -> bool:
    try:
        current_date = datetime.now().strftime("%Y%m%d")  # 格式化为20250707
        key = f"{max_id_key}_{current_date}"
        conn.set(key, str(last_id), ex=24 * 3600)
        return True
    except Exception as e:
        logger.error(f"Redis 设置数据失败: {e}", exc_info=True)
        return False


def upload_from_url_to_oss(url: str, bucket_: Bucket, endpoint: str, object_name=None):
    """
    从指定 URL 流式下载文件并上传到阿里云 OSS。

    Args:
        url (str): 要下载的文件 URL。
        bucket_ (Bucket): oss2 的 Bucket 实例。
        endpoint (str): OSS 的 endpoint 地址。
        object_name (str, optional): 上传到 OSS 后的对象名称，默认为 URL 的 basename。

    Returns:
        tuple: (success: bool, oss_url: str or None, object_name: str or None)
    """
    try:
        logger.info(f"开始下载并上传文件: {url}")

        # 使用 stream=True 流式下载
        with requests.get(url, stream=True, timeout=(3, 10)) as response:
            response.raise_for_status()

            # 获取文件名
            if not object_name:
                parsed_url = urlparse(url)
                object_name = parsed_url.path
                if object_name.startswith("/"):
                    object_name = object_name[1:]
                # object_name = os.path.basename(url)

            # 注意：response.raw 在 with 块结束后可能会被关闭，建议在 with 内完成上传
            result = bucket_.put_object(object_name, response.content)

        if result.status == 200 or result.status == 203:
            # 构建 OSS 访问地址
            clean_endpoint = endpoint.replace("https://", "").rstrip('/')
            oss_url = f"https://{bucket_.bucket_name}.{clean_endpoint}/{object_name}"
            logger.info(f"文件上传成功: {oss_url}")
            return True, oss_url, object_name
        else:
            logger.error(f"[同步录音]上传失败，状态码: {result.status}")
            return False, None, None

    except requests.exceptions.Timeout:
        logger.error('请求超时，请检查网络或调整超时设置')
        return False, None, None

    except requests.exceptions.RequestException as req_err:
        logger.error(f"HTTP 请求异常: {req_err}", exc_info=True)
        return False, None, None

    # except oss_exceptions.OssError as oss_err:
    #     logger.error(f"OSS 上传错误: {oss_err}", exc_info=True)
    #     return False, None, None

    except Exception as e:
        logger.error(f"未知错误导致上传失败: {e}", exc_info=True)
        return False, None, None


def get_org_setting(apikey: str, mysql_p) -> Optional[tuple]:
    try:
        sql = f"""
            SELECT duyan_refer_id, duyan_apikey
            FROM org 
            WHERE apikey = '{apikey}'
        """
        result = mysql_p.select_one(sql)

        if not result:
            logger.debug(f"未找到apikey对应的组织设置: {apikey}")
            return None

        refer_id = result.get("duyan_refer_id")
        org_apikey = result.get("duyan_apikey")

        if not all([refer_id, org_apikey]):
            logger.warning(f"组织设置数据不完整: {result}")
            return None

        return refer_id, org_apikey

    except Exception as ex:
        logger.error(f"获取组织设置失败, apikey: {apikey}, 错误: {ex}")
        return None


def is_uuid_exist(uuid: str, mysql_p) -> bool:
    try:
        sql = f"""
            SELECT COUNT(id) as countT
            FROM cao_cti_audio_2oss_meta 
            WHERE call_uuid = '{uuid}'
        """
        result = mysql_p.select_one(sql)
        return result["countT"] > 0 if result else False
    except Exception as ex:
        logger.error(f"[同步录音]检查UUID存在性失败: {ex}")
        return False


def insert_into_metadata(oss_url: str,
                         file_name: str,
                         call_uuid: str,
                         mysql_p
                         ) -> Optional[int]:
    try:
        sql = f"""
            INSERT INTO cao_cti_audio_2oss_meta 
            (call_uuid, file_name, oss_url) 
            VALUES ('{call_uuid}', '{file_name}', '{oss_url}')
        """
        result = mysql_p.execute(sql)
        return result.rowcount if result else None
    except Exception as ex:
        logger.error(f"[同步录音]插入元数据失败: {ex}")
        return None


def get_today_timestamp():
    """
    获取今天的开始和结束时间戳（13位毫秒级）
    """
    today = time.strftime("%Y-%m-%d", time.localtime())
    start_time = int(time.mktime(time.strptime(today, "%Y-%m-%d"))) * 1000
    end_time = start_time + 24 * 60 * 60 * 1000  # 加一天
    return start_time, end_time


# @retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
def fetch_call_logs(page_num, start_time, end_time, duyan_apikey):
    """
    调用接口获取通话记录
    """
    params = {
        "apikey": duyan_apikey,
        "page_num": page_num,
        "page_size": PAGE_SIZE,
        "start_time": start_time,
        "end_time": end_time
    }

    if TEAM_ID:
        params["team_id"] = TEAM_ID
    if MEMBER_ID:
        params["member_id"] = MEMBER_ID
    if TARGET:
        params["target"] = TARGET

    try:
        response = requests.get(API_URL, headers=HEADERS, params=params)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"请求失败: 状态码={response.status_code}, 响应={response.text}")
            return None
    except Exception as ex:
        logger.error(f"调用接口获取通话记录失败: {ex}")
        return None


def process_call_log(call_log, refer_id):
    """
    处理每一页获取到的通话记录

    """
    outcome = call_log["outcome"]
    call_uuid = call_log["call_uuid"]
    duration = call_log["duration"]
    try:
        call_time = int(call_log["call_time"]) / 1000  # 毫秒转秒，并确保是整数或浮点数
        call_time_str = datetime.fromtimestamp(call_time).strftime("%Y-%m-%d")
    except (TypeError, KeyError, OverflowError, ValueError) as e:
        logger.error(f"解析 call_time 失败: {e}, 原始值: {call_log.get('call_time')}")
        return

    if outcome == "SUCCESS" and duration > 0:
        if is_uuid_exist(call_uuid, mysql_pool):
            logger.info(call_uuid + "已经在表中存在")
            return
        cti_audio_url = record_url.format(refer_id, call_time_str, call_uuid)
        res, oss_url, object_name = upload_from_url_to_oss(cti_audio_url, bucket, ossEndpoint)
        if res:
            insert_into_metadata(oss_url, object_name, call_uuid, mysql_pool)


def main(thread_pool_: ThreadPoolExecutor):
    start_time, end_time = get_today_timestamp()
    logger.info(f"正在获取 [{start_time} - {end_time}] 的通话记录...")

    page_num = get_start_page_key()
    last_id = get_last_id()
    if page_num is None:
        set_start_page_key(1)
        page_num = 1

    org_setting = get_org_setting(API_KEY, mysql_pool)
    if org_setting is None:
        logger.error("APIKEY 错误！！！！！！")
        return False

    """
    按天查询数据：
    1. 用redis 记录pageNum和总量
    2. 定时任务开始从redis 获取上述参数
    3. 获取分页信息
    4. 如果信息总量和pageNum 大于获取信息，则说明没有新增数据
    5. 如果不是则并发处理新的数据，并设置新的redis 值
    
    """

    while True:
        logger.info(f"正在获取第 {page_num} 页数据...")

        result = fetch_call_logs(page_num, start_time, end_time, org_setting[1])

        if not result or result.get("status") != 1:
            logger.info("接口调用失败")
            break

        data = result.get("data", {})
        call_logs = data.get("call_logs", [])
        max_id_in_call_logs = len(call_logs) if call_logs else 0
        max_id_in_call_logs = (page_num - 1) * PAGE_SIZE + max_id_in_call_logs

        last_id = get_last_id()
        if not call_logs or last_id >= max_id_in_call_logs:
            logger.info("没有更多通话记录了")
            break

        # 处理通话记录
        futures = []
        for call_log in call_logs:
            future = thread_pool_.submit(process_call_log, call_log, org_setting[0])
            futures.append(future)
        wait(futures)

        if len(call_logs) >= PAGE_SIZE:
            page_num += 1
        set_start_page_key(page_num)
        set_last_id_key(max_id_in_call_logs)

        # 控制频率（可选）
        time.sleep(0.1)


###<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<  main  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>###

# main 方法入口，scheduler
if __name__ == '__main__':
    logger.info("<<<<<<<<<<<<<<<<<<<<sync_bpw_cti_audio_2oss 启动 走起>>>>>>>>>>>>>>>>>>>>")
    background_schedulers = bg.BackgroundScheduler(timezone='Asia/Shanghai')
    thread_pool = ThreadPoolExecutor(max_workers=worker_num, thread_name_prefix=script_name)

    def shutdown_handler(*args):
        logger.info("正在关闭线程池和调度器...")
        thread_pool.shutdown(wait=True)
        background_schedulers.shutdown()
        exit(0)

    signal.signal(signal.SIGINT, shutdown_handler)
    signal.signal(signal.SIGTERM, shutdown_handler)

    try:
        background_schedulers.add_job(heartbeat, 'interval', seconds=10, args=(script_name, logger))
        background_schedulers.add_job(lambda: main(thread_pool), 'interval', seconds=300)
        background_schedulers.start()
        while True:
            time.sleep(1)
    except Exception as e:
        logger.exception(e)
        shutdown_handler()
