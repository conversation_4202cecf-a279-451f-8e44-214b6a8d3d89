# 监听case_log，并同步数据到es
# 增删改 case_log 更新 case_log
import json
import time
from datasync.base_data_to_es import DataToEs
from datasync.datetime_to_json import DateEncoder
from concurrent.futures import ThreadPoolExecutor
import threading
from apscheduler.schedulers import background as bg
from es_query_builder import *
from utils.logger_util import LOG

task_topic = "bpw_anmi_case_log"

es_index = "bpw_case_log_query"
es_type = "_doc"

logger = LOG.get_logger("sync_case_log")

sync_util = DataToEs(task_topic)

# 查询转移sql
transerDataSqlMeta = "SELECT id,create_time,type,case_id,case_name,org_delt_name,org_delt_id,batch_id,product_name," \
                     "product_id,create_by,id_card,create_by_name,field_json,dunner_id,dunner_name,batch_no," \
                     "serial_no,out_serial_no,org_id,team_id,team_name,dep_id,task_id,own_mobile,out_batch_id," \
                     "out_batch_name,case_amount,case_json,repayment_amount,out_serial_temp " \
                     "FROM case_log WHERE id IN %s"


def main():
    partitions = sync_util.kafka_consumer.get_partitions()
    partitions_size = len(partitions)
    thread_pool = ThreadPoolExecutor(max_workers=partitions_size, thread_name_prefix="sync_bpw_case_log")
    futures = []
    for partition in partitions:
        future = thread_pool.submit(run, partition)
        futures.append(future)
    for future in futures:
        future.result()


def run(partition):
    while True:
        try:
            thread_name = threading.current_thread().name
            start_offset, end_offset, bin_log_datas = sync_util.get_bin_log_data_by_partition(partition)
            if bin_log_datas:
                logger.info(f'partition:{partition}消费到binlog数据 >>>')
                insert_or_update_list = []
                delete_list = []
                action_list = []
                for bin_log_data in bin_log_datas:
                    logger.info(f'{thread_name}消费到binlog数据 >>> {bin_log_data}')
                    json_bin_log_data = json.loads(bin_log_data)

                    log_type = json_bin_log_data.get('type')
                    if log_type == 'INSERT' or log_type == 'UPDATE':
                        case_log_list = json_bin_log_data.get('data')
                        for case_log in case_log_list:
                            case_log_id = int(case_log["id"])
                            insert_or_update_list.append(case_log_id)
                    elif log_type == 'DELETE':
                        case_log_list = json_bin_log_data.get('data')
                        for case_log in case_log_list:
                            case_log_id = int(case_log["id"])
                            delete_list.append(case_log_id)

                if len(insert_or_update_list) > 0:
                    action_list.extend(build_insert_or_update_action_list(insert_or_update_list))

                if len(delete_list) > 0:
                    action_list.extend(build_delete_action_list(delete_list))

                if len(action_list) > 0:
                    sync_util.es_bulk(action_list)
            else:
                time.sleep(1)
            #logger.info("<<<<<<<<<<<<<<<<<<<<" + thread_name + " == sync_case_log>>>>>>>>>>>>>>>>>>>>")
        except Exception as e:
            logger.exception(e)


def build_insert_or_update_action_list(insert_or_update_list):
    case_logs = sync_util.db.select_by_param(transerDataSqlMeta, (insert_or_update_list,))
    result_list = []
    for case_log in case_logs:
        result_list.append(build_common_source(case_log))
    return result_list


def build_delete_action_list(delete_list):
    result_list = []
    for case_log_id in delete_list:
        result_list.append(
            {
                "_op_type": 'delete',
                "_index": es_index,
                "_type": es_type,
                "_id": case_log_id
            }
        )
    return result_list


def build_common_source(case_log):
    case_log_id = case_log["id"]
    source = {}

    if case_log_id is not None:
        source["id"] = case_log_id

    create_time = case_log["create_time"]
    if create_time is not None:
        source["createTime"] = create_time

    type = case_log["type"]
    if type is not None:
        source["type"] = type

    case_id = case_log["case_id"]
    if case_id is not None:
        source["caseId"] = case_id

    case_name = case_log["case_name"]
    if case_name is not None:
        source["caseName"] = case_name

    org_delt_name = case_log["org_delt_name"]
    if org_delt_name is not None:
        source["orgDeltName"] = org_delt_name

    org_delt_id = case_log["org_delt_id"]
    if org_delt_id is not None:
        source["orgDeltId"] = org_delt_id

    batch_id = case_log["batch_id"]
    if batch_id is not None:
        source["batchId"] = batch_id

    product_name = case_log["product_name"]
    if product_name is not None:
        source["productName"] = product_name

    product_id = case_log["product_id"]
    if product_id is not None:
        source["productId"] = product_id

    create_by = case_log["create_by"]
    if create_by is not None:
        source["createBy"] = create_by

    id_card = case_log["id_card"]
    if id_card is not None:
        source["idCard"] = id_card

    create_by_name = case_log["create_by_name"]
    if create_by_name is not None:
        source["createByName"] = create_by_name

    field_json = case_log["field_json"]
    if field_json is not None:
        source["fieldJson"] = field_json

    dunner_id = case_log["dunner_id"]
    if dunner_id is not None:
        source["dunnerId"] = dunner_id

    dunner_name = case_log["dunner_name"]
    if dunner_name is not None:
        source["dunnerName"] = dunner_name

    batch_no = case_log["batch_no"]
    if batch_no is not None:
        source["batchNo"] = batch_no

    serial_no = case_log["serial_no"]
    if serial_no is not None:
        source["serialNo"] = serial_no

    out_serial_no = case_log["out_serial_no"]
    if out_serial_no is not None:
        source["outSerialNo"] = out_serial_no
        if "#" in out_serial_no:
            index = out_serial_no.index("#")
            source["outSerialNoSearch"] = out_serial_no[0:index]
        else:
            source["outSerialNoSearch"] = out_serial_no

    org_id = case_log["org_id"]
    if org_id is not None:
        source["orgId"] = org_id

    team_id = case_log["team_id"]
    if team_id is not None:
        source["teamId"] = team_id

    team_name = case_log["team_name"]
    if team_name is not None:
        source["teamName"] = team_name

    dep_id = case_log["dep_id"]
    if dep_id is not None:
        source["depId"] = dep_id

    task_id = case_log["task_id"]
    if task_id is not None:
        source["taskId"] = task_id

    own_mobile = case_log["own_mobile"]
    if own_mobile is not None:
        source["ownMobile"] = own_mobile

    out_batch_id = case_log["out_batch_id"]
    if out_batch_id is not None:
        source["outBatchId"] = out_batch_id

    out_batch_name = case_log["out_batch_name"]
    if out_batch_name is not None:
        source["outBatchName"] = out_batch_name

    case_amount = case_log["case_amount"]
    if case_amount is not None:
        source["caseAmount"] = case_amount

    case_json = case_log["case_json"]
    if case_json is not None:
        source["caseJson"] = case_json
        source["caseJsonQuery"] = json.loads(case_json)

    repayment_amount = case_log["repayment_amount"]
    if repayment_amount is not None:
        source["repaymentAmount"] = repayment_amount

    out_serial_temp = case_log["out_serial_temp"]
    if out_serial_temp is not None:
        source["outSerialTemp"] = out_serial_temp

    source_json = json.dumps(source, cls=DateEncoder)
    logger.info(f'case_log_source:{source_json} >>>')

    return {
        "_index": es_index,
        "_type": es_type,
        "_id": case_log_id,
        "_source": source_json
    }


def heartbeat():
    logger.info("-------sync_case_log_heartbeat-------")


def data_compare():
    """
    数据比对
    """
    try:
        sql_result = sync_util.db.select_one('select count(id) as sql_count from case_log')
        sql_count = sql_result['sql_count']

        _bool = BoolQuery(must_list=[], should_list=[], must_not_list=[])
        _search = Search(query=_bool, limit=0, order_list=[])
        _result = sync_util.es_client.search(index=es_index, body=_search.toMap())
        es_count = _result['hits']['total']['value']

        if sql_count != es_count:
            logger.error(f"-------sync_case_log_data_compare-------sql_count:{sql_count};es_count:{es_count}")
        else:
            logger.info(f"-------sync_case_log_data_compare-------sql_count:{sql_count};es_count:{es_count}")
    except Exception as e:
        logger.exception(e)


if __name__ == '__main__':
    try:
        background_schedulers = bg.BackgroundScheduler(timezone='Asia/Shanghai')
        background_schedulers.add_job(heartbeat, 'interval', seconds=10)
        background_schedulers.add_job(data_compare, 'cron', day_of_week='*', hour=23, minute=30)
        background_schedulers.start()
        main()
    except Exception as e:
        logger.exception(e)
