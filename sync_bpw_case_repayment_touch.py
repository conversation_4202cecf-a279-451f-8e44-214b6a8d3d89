# 手动触发同步指定范围内的case_repayment数据到es
# 脚本运行：python3 sync_case_repayment_touch.py start end
import json
from datasync.base_data_to_es import DataToEs
from datasync.datetime_to_json import DateEncoder
from concurrent.futures import ThreadPoolExecutor
import threading
import sys
import decimal

from utils.logger_util import LOG

es_index_repayment = "bpw_case_repayment_query"
es_type = "_doc"

logger = LOG.get_logger("sync_bpw_case_repayment_touch")

sync_util = DataToEs()

# 需要转移的数据id，最大、最小值
transerDataMaxAndMinId = "SELECT max(rep.id) AS max,min(rep.id) AS min " \
                         "from case_repayment rep " \
                         "INNER JOIN case_info ca ON rep.case_id=ca.id " \
                         "WHERE rep.id>= %d AND rep.id<= %d"

# 查询转移sql
transerDataRepaymentSqlMeta = "SELECT rep.type,rep.id,rep.out_serial_no,rep.debt_name,rep.debt_id_card," \
                                  "rep.repayment_time,rep.repayment_amount,rep.repayment_type,rep.repayment_card_no," \
                                  "rep.repayment_mobile,rep.STATUS,rep.create_by,rep.update_by,rep.create_time," \
                                  "rep.update_time,rep.DESC,rep.case_operator,rep.case_operator_name," \
                                  "rep.apply_status,rep.voucher_url,rep.apply_desc,rep.repayment_from," \
                                  "rep.repayment_style,rep.case_id,rep.audit_by,rep.org_id,rep.dep_id,rep.team_id," \
                                  "rep.audit_time,ca.product_id,rep.out_serial_temp,ca.user_id,rep.org_delt_id," \
                                  "rep.allot_agent,rep.ext1,rep.ext2,rep.ext3,rep.ext4,rep.ext5,rep.ext6,rep.ext7," \
                                  "rep.ext8,rep.ext9,rep.ext10,rep.flow_id," \
                                  "rep.ext11,rep.ext12,rep.ext13,rep.ext14,rep.ext15,rep.ext16,rep.ext17," \
                                  "rep.ext18,rep.ext19,rep.ext20,rep.ext21,rep.ext22," \
                                  "ca.out_batch_id,ca.inner_batch_id,ca.amount,ca.entrust_start_time," \
                                  "ca.entrust_end_time,ca.case_status,ca.allot_status AS case_allot_status,ca.recovery," \
                                  "ca.own_mobile " \
                                  "FROM case_repayment rep " \
                                  "LEFT JOIN case_info ca ON rep.case_id=ca.id " \
                                  "WHERE rep.id >= %d and rep.id <= %d"


def main():
    start = int(sys.argv[1])
    end = int(sys.argv[2])
    if start is None or end is None or start > end:
        logger.info("启动参数异常")
        return

    min_max = sync_util.db.select_one(transerDataMaxAndMinId % (start, end))

    min_id = min_max["min"]
    max_id = min_max["max"]
    if min_id is None or max_id is None:
        logger.info("没有数据需要同步")
        return

    total = max_id - min_id + 1
    fetch_size = 10000
    parts = total // fetch_size
    if total % fetch_size != 0:
        parts = parts + 1

    thread_pool = ThreadPoolExecutor(max_workers=5, thread_name_prefix="sync_bpw_case_repayment_touch")
    futures = []
    for part in range(parts):
        part_start_id = min_id + (fetch_size * part)
        part_end_id = min_id + (fetch_size * (part + 1)) - 1
        if part_end_id > max_id:
            part_end_id = max_id

        future = thread_pool.submit(run, part_start_id, part_end_id)
        futures.append(future)
    for future in futures:
        try:
            future.result(60)
        except Exception as e:
            logger.exception(e)


def run(part_start_id, part_end_id):
    try:
        thread_name = threading.current_thread().name
        transer_datas = sync_util.db.select(transerDataRepaymentSqlMeta % (part_start_id, part_end_id))
        if transer_datas:
            action_list = []
            for case_repayment in transer_datas:
                logger.info(f'{thread_name}同步数据 >>> {case_repayment}')
                action_list.append(build_common_source(case_repayment))

            if len(action_list) > 0:
                sync_util.es_bulk(action_list)

        # log.info("<<<<<<<<<<<<<<<<<<<<" + thread_name + " == sync_case_repayment_touch>>>>>>>>>>>>>>>>>>>>")
    except Exception as e:
        logger.exception(e)


def build_common_source(case_repayment):
    case_repayment_id = case_repayment["id"]
    source = {}

    if case_repayment_id is not None:
        source["id"] = case_repayment_id

    type = case_repayment["type"]
    if type is not None:
        source["type"] = type

    out_serial_no = case_repayment["out_serial_no"]
    if out_serial_no is not None:
        source["outSerialNo"] = out_serial_no
        if "#" in out_serial_no:
            index = out_serial_no.index("#")
            source["outSerialNoSearch"] = out_serial_no[0:index]
        else:
            source["outSerialNoSearch"] = out_serial_no

    debt_name = case_repayment["debt_name"]
    if debt_name is not None:
        source["debtName"] = debt_name

    debt_id_card = case_repayment["debt_id_card"]
    if debt_id_card is not None:
        source["debtIdCard"] = debt_id_card

    repayment_time = case_repayment["repayment_time"]
    if repayment_time is not None:
        source["repaymentTime"] = repayment_time

    repayment_amount = case_repayment["repayment_amount"]
    if repayment_amount is not None:
        source["repaymentAmount"] = repayment_amount

    repayment_type = case_repayment["repayment_type"]
    if repayment_type is not None:
        source["repaymentType"] = repayment_type

    repayment_card_no = case_repayment["repayment_card_no"]
    if repayment_card_no is not None:
        source["repaymentCardNo"] = repayment_card_no

    repayment_mobile = case_repayment["repayment_mobile"]
    if repayment_mobile is not None:
        source["repaymentMobile"] = repayment_mobile

    status = case_repayment["STATUS"]
    if status is not None:
        source["status"] = status

    create_by = case_repayment["create_by"]
    if create_by is not None:
        source["createBy"] = int(create_by)

    update_by = case_repayment["update_by"]
    if update_by is not None:
        source["updateBy"] = int(update_by)

    create_time = case_repayment["create_time"]
    if create_time is not None:
        source["createTime"] = create_time

    update_time = case_repayment["update_time"]
    if update_time is not None:
        source["updateTime"] = update_time

    desc = case_repayment["DESC"]
    if desc is not None:
        source["desc"] = desc

    case_operator = case_repayment["case_operator"]
    if case_operator is not None:
        source["caseOperator"] = case_operator

    case_operator_name = case_repayment["case_operator_name"]
    if case_operator_name is not None:
        source["caseOperatorName"] = case_operator_name

    apply_status = case_repayment["apply_status"]
    if apply_status is not None:
        source["applyStatus"] = apply_status

    voucher_url = case_repayment["voucher_url"]
    if voucher_url is not None:
        source["voucherUrl"] = voucher_url

    apply_desc = case_repayment["apply_desc"]
    if apply_desc is not None:
        source["applyDesc"] = apply_desc

    repayment_from = case_repayment["repayment_from"]
    if repayment_from is not None:
        source["repaymentFrom"] = repayment_from

    repayment_style = case_repayment["repayment_style"]
    if repayment_style is not None:
        source["repaymentStyle"] = repayment_style

    case_id = case_repayment["case_id"]
    if case_id is not None:
        source["caseId"] = case_id

    audit_by = case_repayment["audit_by"]
    if audit_by is not None:
        source["auditBy"] = audit_by

    org_id = case_repayment["org_id"]
    if org_id is not None:
        source["orgId"] = org_id

    dep_id = case_repayment["dep_id"]
    if dep_id is not None:
        source["depId"] = dep_id

    team_id = case_repayment["team_id"]
    if team_id is not None:
        source["teamId"] = team_id

    audit_time = case_repayment["audit_time"]
    if audit_time is not None:
        source["auditTime"] = audit_time

    product_id = case_repayment["product_id"]
    if product_id is not None:
        source["productId"] = product_id

    out_serial_temp = case_repayment["out_serial_temp"]
    if out_serial_temp is not None:
        source["outSerialTemp"] = out_serial_temp

    user_id = case_repayment["user_id"]
    if user_id is not None:
        source["userId"] = user_id

    org_delt_id = case_repayment["org_delt_id"]
    if org_delt_id is not None:
        source["orgDeltId"] = org_delt_id

    out_batch_id = case_repayment["out_batch_id"]
    if out_batch_id is not None:
        source["outBatchId"] = out_batch_id

    inner_batch_id = case_repayment["inner_batch_id"]
    if inner_batch_id is not None:
        source["innerBatchId"] = inner_batch_id

    amount = case_repayment["amount"]
    if amount is not None:
        source["amount"] = int(amount)

    entrust_start_time = case_repayment["entrust_start_time"]
    if entrust_start_time is not None:
        source["entrustStartTime"] = entrust_start_time

    entrust_end_time = case_repayment["entrust_end_time"]
    if entrust_end_time is not None:
        source["entrustEndTime"] = entrust_end_time

    case_status = case_repayment["case_status"]
    if case_status is not None:
        source["caseStatus"] = case_status

    case_allot_status = case_repayment["case_allot_status"]
    if case_allot_status is not None:
        source["caseAllotStatus"] = case_allot_status

    recovery = case_repayment["recovery"]
    if recovery is not None:
        source["recovery"] = recovery

    own_mobile = case_repayment["own_mobile"]
    if own_mobile is not None:
        source["ownMobile"] = own_mobile

    allot_agent = case_repayment["allot_agent"]
    if allot_agent is not None:
        source["allotAgent"] = allot_agent

    ext1 = case_repayment["ext1"]
    if ext1 is not None:
        source["ext1"] = ext1

    ext2 = case_repayment["ext2"]
    if ext2 is not None:
        source["ext2"] = ext2

    ext3 = case_repayment["ext3"]
    if ext3 is not None:
        source["ext3"] = ext3

    ext4 = case_repayment["ext4"]
    if ext4 is not None:
        source["ext4"] = ext4

    ext5 = case_repayment["ext5"]
    if ext5 is not None:
        source["ext5"] = ext5

    ext6 = case_repayment["ext6"]
    if ext6 is not None:
        source["ext6"] = ext6

    ext7 = case_repayment["ext7"]
    if ext7 is not None:
        source["ext7"] = str(decimal.Decimal(ext7).quantize(decimal.Decimal('0.00')))

    ext8 = case_repayment["ext8"]
    if ext8 is not None:
        source["ext8"] = str(decimal.Decimal(ext8).quantize(decimal.Decimal('0.00')))

    ext9 = case_repayment["ext9"]
    if ext9 is not None:
        source["ext9"] = ext9

    ext10 = case_repayment["ext10"]
    if ext10 is not None:
        source["ext10"] = ext10

    flow_id = case_repayment["flow_id"]
    if flow_id is not None:
        source["flowId"] = flow_id

    ext11 = case_repayment["ext11"]
    if ext11 is not None:
        source["ext11"] = ext11

    ext12 = case_repayment["ext12"]
    if ext12 is not None:
        source["ext12"] = ext12

    ext13 = case_repayment["ext13"]
    if ext13 is not None:
        source["ext13"] = ext13

    ext14 = case_repayment["ext14"]
    if ext14 is not None:
        source["ext14"] = ext14

    ext15 = case_repayment["ext15"]
    if ext15 is not None:
        source["ext15"] = str(decimal.Decimal(ext15).quantize(decimal.Decimal('0.00')))

    ext16 = case_repayment["ext16"]
    if ext16 is not None:
        source["ext16"] = str(decimal.Decimal(ext16).quantize(decimal.Decimal('0.00')))

    ext17 = case_repayment["ext17"]
    if ext17 is not None:
        source["ext17"] = str(decimal.Decimal(ext17).quantize(decimal.Decimal('0.00')))

    ext18 = case_repayment["ext18"]
    if ext18 is not None:
        source["ext18"] = str(decimal.Decimal(ext18).quantize(decimal.Decimal('0.00')))

    ext19 = case_repayment["ext19"]
    if ext19 is not None:
        source["ext19"] = ext19

    ext20 = case_repayment["ext20"]
    if ext20 is not None:
        source["ext20"] = ext20

    ext21 = case_repayment["ext21"]
    if ext21 is not None:
        source["ext21"] = ext21

    ext22 = case_repayment["ext22"]
    if ext22 is not None:
        source["ext22"] = ext22

    source_json = json.dumps(source, cls=DateEncoder)

    return {
        "_index": es_index_repayment,
        "_type": es_type,
        "_id": case_repayment_id,
        "_source": source_json
    }


if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        logger.exception(e)
