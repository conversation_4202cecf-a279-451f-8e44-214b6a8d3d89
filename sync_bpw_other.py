# 监听case_repayment、case_debtor，并同步case_repayment、case_info数据到es
# 增删改 case_repayment 更新 case_repayment
# 改 case_debtor 更新 case_info
import json
import time
import decimal
from datasync.base_data_to_es import DataToEs
from datasync.datetime_to_json import DateEncoder
from concurrent.futures import ThreadPoolExecutor
import threading
from apscheduler.schedulers import background as bg
from es_query_builder import *

# 此topic不止case_repayment
from utils.logger_util import LOG

task_topic = "bpw_anmi_other"

es_index_repayment = "bpw_case_repayment_query"
es_index_info = "bpw_case_info_query"
es_type = "_doc"

logger = LOG.get_logger("sync_bpw_other")

sync_util = DataToEs(task_topic)

# 查询转移sql
transerDataRepaymentSqlMeta = "SELECT rep.type,rep.id,rep.out_serial_no,rep.debt_name,rep.debt_id_card," \
                                  "rep.repayment_time,rep.repayment_amount,rep.repayment_type,rep.repayment_card_no," \
                                  "rep.repayment_mobile,rep.STATUS,rep.create_by,rep.update_by,rep.create_time," \
                                  "rep.update_time,rep.DESC,rep.case_operator,rep.case_operator_name," \
                                  "rep.apply_status,rep.voucher_url,rep.apply_desc,rep.repayment_from," \
                                  "rep.repayment_style,rep.case_id,rep.audit_by,rep.org_id,rep.dep_id,rep.team_id," \
                                  "rep.audit_time,ca.product_id,rep.out_serial_temp,ca.user_id,rep.org_delt_id," \
                                  "rep.allot_agent,rep.ext1,rep.ext2,rep.ext3,rep.ext4,rep.ext5,rep.ext6,rep.ext7," \
                                  "rep.ext8,rep.ext9,rep.ext10,rep.flow_id," \
                                  "rep.ext11,rep.ext12,rep.ext13,rep.ext14,rep.ext15,rep.ext16,rep.ext17," \
                                  "rep.ext18,rep.ext19,rep.ext20,rep.ext21,rep.ext22," \
                                  "ca.out_batch_id,ca.inner_batch_id,ca.amount,ca.entrust_start_time," \
                                  "ca.entrust_end_time,ca.case_status AS case_status,ca.allot_status AS case_allot_status,ca.recovery," \
                                  "ca.own_mobile " \
                                  "FROM case_repayment rep " \
                                  "LEFT JOIN case_info ca ON rep.case_id=ca.id AND ca.recovery<>-2 " \
                                  "WHERE rep.id IN %s"

transerDataInfoSqlMeta = "SELECT ca.id,ca.id_card,ca.create_time,ca.update_time,ca.create_by,ca.update_by," \
                         "ca.out_serial_no,ca.serial_no,ca.NAME,ca.oper_status,ca.overdue_date,ca.overdue_days," \
                         "ca.entrust_start_time,ca.entrust_end_time,ca.amount,ca.DESC,ca.case_status,ca.field_json," \
                         "ca.call_status,ca.allot_status,ca.repair_status,ca.recovery,ca.operation_state," \
                         "ca.last_follow_time,ca.follow_count,ca.sync_status,ca.operation_next_time," \
                         "ca.out_serial_temp,ca.pay_amount,ca.return_time,ca.own_mobile,ca.division_time,ca.color," \
                         "ca.ignore_plan,ca.cooperation_status,ca.ptp_time,ca.ptp_amount,ca.delay_time,ca.product_id," \
                         "ca.org_delt_id,ca.user_id,ca.dep_id,ca.team_id,ca.store_id,ca.org_id,ca.out_batch_id," \
                         "ca.inner_batch_id,debt_id,ca.vsearch_key1,ca.vsearch_key2,ca.auto_assist_result," \
                         "ca.auto_assist_date,ca.tag,ca.auto_assist_record,ca.call_type,ca.case_tag_id," \
                         "ca.auto_recovery_date,ca.end_time,ca.ctrl_id,ca.warning,ca.end_type,ca.end_config_id," \
                         "ca.allot_agent,ca.allot_agent_state,ca.way_allot_state,ca.important," \
                         "ca.recycle_flag,ca.outsource_count,ca.way_update_date,ca.pre_dep_id,ca.stop_date,ca.recycle_date," \
                         "ifnull(deb.last_follow_time,ca.last_follow_time) AS debt_last_follow_time," \
                         "ifnull(deb.follow_count,ca.follow_count) AS debt_follow_count," \
                         "deb.type AS conjoint_type,p.type AS product_type,ur.NAME AS user_name," \
                         "ur.STATUS AS user_status," \
                         "ifnull(dept.is_agent,team.is_agent) AS is_agent " \
                         "FROM case_info AS ca " \
                         "INNER JOIN product p ON p.id=ca.product_id " \
                         "LEFT JOIN `user` AS ur ON ur.id=ca.user_id AND ur.STATUS=0 " \
                         "LEFT JOIN case_debtor deb ON deb.id=ca.debt_id AND deb.STATUS=0 " \
                         "LEFT JOIN org_dep_team dept ON dept.id=ca.dep_id " \
                         "LEFT JOIN org_dep_team team ON team.id=ca.team_id " \
                         "WHERE deb.id IN %s"

selectTagSqlMeta = "SELECT ctr.id,ctr.tag_id,ctr.case_id,ct.NAME,ct.color,ct.state " \
                   "FROM case_tag_rel ctr " \
                   "INNER JOIN case_info ca ON ctr.org_id=ca.org_id AND ctr.case_id=ca.id " \
                   "INNER JOIN case_tag ct ON ctr.tag_id=ct.id " \
                   "WHERE ca.id IN %s"

# 催收方式
operationWaySql = "SELECT case_id,operation_way FROM case_operation_way_rel WHERE case_id IN %s "

def main():
    partitions = sync_util.kafka_consumer.get_partitions()
    partitions_size = len(partitions)
    thread_pool = ThreadPoolExecutor(max_workers=partitions_size, thread_name_prefix="sync_bpw_other")
    futures = []
    for partition in partitions:
        future = thread_pool.submit(run, partition)
        futures.append(future)
    for future in futures:
        future.result()


def run(partition):
    while True:
        try:
            thread_name = threading.current_thread().name
            start_offset, end_offset, bin_log_datas = sync_util.get_bin_log_data_by_partition(partition)
            if bin_log_datas:
                logger.info(f'partition:{partition}消费到binlog数据 >>>')
                insert_or_update_repayments = []
                update_infos = []
                action_list = []
                for bin_log_data in bin_log_datas:
                    logger.info(f'{thread_name}消费到binlog数据 >>> {bin_log_data}')
                    json_bin_log_data = json.loads(bin_log_data)

                    table_name = json_bin_log_data.get('table')
                    if table_name != 'case_repayment' and table_name != 'case_debtor':
                        continue

                    log_type = json_bin_log_data.get('type')
                    if log_type == 'INSERT' or log_type == 'UPDATE':
                        data_list = json_bin_log_data.get('data')
                        if table_name == 'case_repayment':
                            for data in data_list:
                                data_id = int(data["id"])
                                insert_or_update_repayments.append(data_id)
                        elif table_name == 'case_debtor' and log_type == 'UPDATE':
                            # case_debtor 只处理更新消息
                            for data in data_list:
                                data_id = int(data["id"])
                                update_infos.append(data_id)

                if len(insert_or_update_repayments) > 0:
                    action_list.extend(build_insert_or_update_repayment(insert_or_update_repayments))

                if len(update_infos) > 0:
                    action_list.extend(build_update_info(update_infos))

                if len(action_list) > 0:
                    sync_util.es_bulk(action_list)
            else:
                time.sleep(1)
            #logger.info("<<<<<<<<<<<<<<<<<<<<" + thread_name + " == sync_other>>>>>>>>>>>>>>>>>>>>")
        except Exception as e:
            logger.exception(e)


def build_update_info(update_list):
    case_infos = sync_util.db.select_by_param(transerDataInfoSqlMeta, (update_list,))

    result_list = []
    if case_infos:
        case_info_ids = list(map(lambda x: x["id"], filter(lambda x: x["id"] is not None, case_infos)))
        tag_rels = []
        operation_way_rels = []
        if case_info_ids:
            tag_rels = sync_util.db.select_by_param(selectTagSqlMeta, (case_info_ids,))
            operation_way_rels = sync_util.db.select_by_param(operationWaySql, (case_info_ids,))

        for case_info in case_infos:
            tags = list(filter(lambda x: x["case_id"] == case_info["id"], tag_rels))
            operation_ways = list(filter(lambda x: x["case_id"] == case_info["id"], operation_way_rels))
            result_list.append(build_source_info(case_info, tags, operation_ways))
    return result_list


def build_source_info(case_info, tags, operation_ways):
    case_info_id = case_info["id"]
    source = {}

    if case_info_id is not None:
        source["id"] = case_info_id

    id_card = case_info["id_card"]
    if id_card is not None:
        source["idCard"] = id_card

    out_serial_no = case_info["out_serial_no"]
    if out_serial_no is not None:
        source["outSerialNo"] = out_serial_no
        if "#" in out_serial_no:
            index = out_serial_no.index("#")
            source["outSerialNoSearch"] = out_serial_no[0:index]
        else:
            source["outSerialNoSearch"] = out_serial_no

    create_time = case_info["create_time"]
    if create_time is not None:
        source["createTime"] = create_time

    update_time = case_info["update_time"]
    if update_time is not None:
        source["updateTime"] = update_time

    create_by = case_info["create_by"]
    if create_by is not None:
        source["createBy"] = int(create_by)

    update_by = case_info["update_by"]
    if update_by is not None:
        source["updateBy"] = int(update_by)

    serial_no = case_info["serial_no"]
    if serial_no is not None:
        source["serialNo"] = serial_no

    name = case_info["NAME"]
    if name is not None:
        source["name"] = name

    oper_status = case_info["oper_status"]
    if oper_status is not None:
        source["operStatus"] = oper_status

    overdue_date = case_info["overdue_date"]
    if overdue_date is not None:
        source["overdueDate"] = overdue_date

    overdue_days = case_info["overdue_days"]
    if overdue_days is not None:
        source["overdueDays"] = overdue_days

    entrust_start_time = case_info["entrust_start_time"]
    if entrust_start_time is not None:
        source["entrustStartTime"] = entrust_start_time

    entrust_end_time = case_info["entrust_end_time"]
    if entrust_end_time is not None:
        source["entrustEndTime"] = entrust_end_time

    amount = case_info["amount"]
    if amount is not None:
        source["amount"] = int(amount)

    desc = case_info["DESC"]
    if desc is not None:
        source["desc"] = desc

    case_status = case_info["case_status"]
    if case_status is not None:
        source["caseStatus"] = case_status

    field_json_map = {}
    field_search = []
    field_json = case_info["field_json"]
    if field_json is not None:
        field_json_tem = json.loads(field_json)
        search_key1 = field_json_tem.get("search_key1")
        if search_key1 is not None:
            field_json_map["search_key1"] = search_key1
        search_key2 = field_json_tem.get("search_key2")
        if search_key2 is not None:
            field_json_map["search_key2"] = search_key2
        for key in field_json_tem:
            if key is not None and key != "":
                value = field_json_tem[key]
                if value is not None and value != "":
                    field_search.append(key+"#"+value)

    source["fieldJson"] = field_json_map
    source["fieldSearch"] = field_search

    call_status = case_info["call_status"]
    if call_status is not None:
        source["callStatus"] = call_status

    allot_status = case_info["allot_status"]
    if allot_status is not None:
        source["allotStatus"] = allot_status

    repair_status = case_info["repair_status"]
    if repair_status is not None:
        source["repairStatus"] = repair_status

    recovery = case_info["recovery"]
    if recovery is not None:
        source["recovery"] = recovery

    operation_state = case_info["operation_state"]
    if operation_state is not None:
        source["operationState"] = operation_state

    last_follow_time = case_info["last_follow_time"]
    if last_follow_time is not None:
        source["lastFollowTime"] = last_follow_time

    follow_count = case_info["follow_count"]
    if follow_count is not None:
        source["followCount"] = follow_count

    sync_status = case_info["sync_status"]
    if sync_status is not None:
        source["syncStatus"] = sync_status

    operation_next_time = case_info["operation_next_time"]
    if operation_next_time is not None:
        source["operationNextTime"] = operation_next_time

    out_serial_temp = case_info["out_serial_temp"]
    if out_serial_temp is not None:
        source["outSerialTemp"] = out_serial_temp

    pay_amount = case_info["pay_amount"]
    if pay_amount is not None:
        source["payAmount"] = int(pay_amount)

    return_time = case_info["return_time"]
    if return_time is not None:
        source["returnTime"] = return_time

    own_mobile = case_info["own_mobile"]
    if own_mobile is not None:
        source["ownMobile"] = own_mobile

    division_time = case_info["division_time"]
    if division_time is not None:
        source["divisionTime"] = division_time

    color = case_info["color"]
    if color is not None:
        source["color"] = color

    ignore_plan = case_info["ignore_plan"]
    if ignore_plan is not None:
        source["ignorePlan"] = ignore_plan

    cooperation_status = case_info["cooperation_status"]
    if cooperation_status is not None:
        source["cooperationStatus"] = cooperation_status

    ptp_time = case_info["ptp_time"]
    if ptp_time is not None:
        source["ptpTime"] = ptp_time

    ptp_amount = case_info["ptp_amount"]
    if ptp_amount is not None:
        source["ptpAmount"] = int(ptp_amount)

    delay_time = case_info["delay_time"]
    if delay_time is not None:
        source["delayTime"] = delay_time

    product_id = case_info["product_id"]
    if product_id is not None:
        source["productId"] = product_id

    org_delt_id = case_info["org_delt_id"]
    if org_delt_id is not None:
        source["orgDeltId"] = org_delt_id

    user_id = case_info["user_id"]
    if user_id is not None:
        source["userId"] = user_id

    dep_id = case_info["dep_id"]
    if dep_id is not None:
        source["depId"] = dep_id

    team_id = case_info["team_id"]
    if team_id is not None:
        source["teamId"] = team_id

    store_id = case_info["store_id"]
    if store_id is not None:
        source["storeId"] = store_id

    org_id = case_info["org_id"]
    if org_id is not None:
        source["orgId"] = org_id

    out_batch_id = case_info["out_batch_id"]
    if out_batch_id is not None:
        source["outBatchId"] = out_batch_id

    inner_batch_id = case_info["inner_batch_id"]
    if inner_batch_id is not None:
        source["innerBatchId"] = inner_batch_id

    debt_id = case_info["debt_id"]
    if debt_id is not None:
        source["debtId"] = debt_id

    vsearch_key1 = case_info["vsearch_key1"]
    if vsearch_key1 is not None:
        source["vsearchKey1"] = vsearch_key1

    vsearch_key2 = case_info["vsearch_key2"]
    if vsearch_key2 is not None:
        source["vsearchKey2"] = vsearch_key2

    auto_assist_result = case_info["auto_assist_result"]
    if auto_assist_result is not None:
        source["autoAssistResult"] = auto_assist_result

    auto_assist_date = case_info["auto_assist_date"]
    if auto_assist_date is not None:
        source["autoAssistDate"] = auto_assist_date

    tag = case_info["tag"]
    if tag is not None:
        source["tag"] = tag

    auto_assist_record = case_info["auto_assist_record"]
    if auto_assist_record is not None:
        source["autoAssistRecord"] = auto_assist_record

    call_type = case_info["call_type"]
    if call_type is not None:
        source["callType"] = call_type

    if tags:
        tag_ids = list(map(lambda x: x["tag_id"], filter(lambda x: x["tag_id"] is not None, tags)))
        if tag_ids:
            source["caseTagId"] = tag_ids

    auto_recovery_date = case_info["auto_recovery_date"]
    if auto_recovery_date is not None:
        source["autoRecoveryDate"] = auto_recovery_date

    end_time = case_info["end_time"]
    if end_time is not None:
        source["endTime"] = end_time

    ctrl_id = case_info["ctrl_id"]
    if ctrl_id is not None:
        source["ctrlId"] = ctrl_id

    warning = case_info["warning"]
    if warning is not None:
        source["warning"] = warning

    end_type = case_info["end_type"]
    if end_type is not None:
        source["endType"] = end_type

    end_config_id = case_info["end_config_id"]
    if end_config_id is not None:
        source["endConfigId"] = end_config_id

    debt_last_follow_time = case_info["debt_last_follow_time"]
    if debt_last_follow_time is not None:
        source["debtLastFollowTime"] = debt_last_follow_time

    debt_follow_count = case_info["debt_follow_count"]
    if debt_follow_count is not None:
        source["debtFollowCount"] = debt_follow_count

    conjoint_type = case_info["conjoint_type"]
    if conjoint_type is not None:
        source["conjointType"] = conjoint_type

    product_type = case_info["product_type"]
    if product_type is not None:
        source["productType"] = product_type

    user_name = case_info["user_name"]
    if user_name is not None:
        source["userName"] = user_name

    user_status = case_info["user_status"]
    if user_status is not None:
        source["userStatus"] = user_status

    is_agent = case_info["is_agent"]
    if is_agent is not None:
        source["isAgent"] = is_agent

    allot_agent = case_info["allot_agent"]
    if allot_agent is not None:
        source["allotAgent"] = allot_agent

    allot_agent_state = case_info["allot_agent_state"]
    if allot_agent_state is not None:
        source["allotAgentState"] = allot_agent_state

    way_allot_state = case_info["way_allot_state"]
    if way_allot_state is not None:
        source["wayAllotState"] = way_allot_state

    operation_way_values = list(map(lambda x: x["operation_way"], filter(lambda x: x["operation_way"] is not None, operation_ways)))
    source["operationWay"] = operation_way_values

    important = case_info["important"]
    if important is not None:
        source["important"] = important

    recycle_flag = case_info["recycle_flag"]
    if recycle_flag is not None:
        source["recycleFlag"] = recycle_flag

    outsource_count = case_info["outsource_count"]
    if outsource_count is not None:
        source["outsourceCount"] = outsource_count

    way_update_date = case_info["way_update_date"]
    if way_update_date is not None:
        source["wayUpdateDate"] = way_update_date

    pre_dep_id = case_info["pre_dep_id"]
    if pre_dep_id is not None:
        source["preDepId"] = pre_dep_id

    stop_date = case_info["stop_date"]
    if stop_date is not None:
        source["stopDate"] = stop_date

    recycle_date = case_info["recycle_date"]
    if recycle_date is not None:
        source["recycleDate"] = recycle_date

    source_json = json.dumps(source, cls=DateEncoder)
    logger.info(f'case_info_source:{source_json} >>>')

    return {
        "_index": es_index_info,
        "_type": es_type,
        "_id": case_info_id,
        "_source": source_json
    }


def build_insert_or_update_repayment(insert_or_update_list):
    case_repayments = sync_util.db.select_by_param(transerDataRepaymentSqlMeta, (insert_or_update_list,))
    result_list = []
    for case_repayment in case_repayments:
        result_list.append(build_source_repayment(case_repayment))
    return result_list


def build_source_repayment(case_repayment):
    case_repayment_id = case_repayment["id"]
    source = {}

    if case_repayment_id is not None:
        source["id"] = case_repayment_id

    type = case_repayment["type"]
    if type is not None:
        source["type"] = type

    out_serial_no = case_repayment["out_serial_no"]
    if out_serial_no is not None:
        source["outSerialNo"] = out_serial_no
        if "#" in out_serial_no:
            index = out_serial_no.index("#")
            source["outSerialNoSearch"] = out_serial_no[0:index]
        else:
            source["outSerialNoSearch"] = out_serial_no

    debt_name = case_repayment["debt_name"]
    if debt_name is not None:
        source["debtName"] = debt_name

    debt_id_card = case_repayment["debt_id_card"]
    if debt_id_card is not None:
        source["debtIdCard"] = debt_id_card

    repayment_time = case_repayment["repayment_time"]
    if repayment_time is not None:
        source["repaymentTime"] = repayment_time

    repayment_amount = case_repayment["repayment_amount"]
    if repayment_amount is not None:
        source["repaymentAmount"] = repayment_amount

    repayment_type = case_repayment["repayment_type"]
    if repayment_type is not None:
        source["repaymentType"] = repayment_type

    repayment_card_no = case_repayment["repayment_card_no"]
    if repayment_card_no is not None:
        source["repaymentCardNo"] = repayment_card_no

    repayment_mobile = case_repayment["repayment_mobile"]
    if repayment_mobile is not None:
        source["repaymentMobile"] = repayment_mobile

    status = case_repayment["STATUS"]
    if status is not None:
        source["status"] = status

    create_by = case_repayment["create_by"]
    if create_by is not None:
        source["createBy"] = int(create_by)

    update_by = case_repayment["update_by"]
    if update_by is not None:
        source["updateBy"] = int(update_by)

    create_time = case_repayment["create_time"]
    if create_time is not None:
        source["createTime"] = create_time

    update_time = case_repayment["update_time"]
    if update_time is not None:
        source["updateTime"] = update_time

    desc = case_repayment["DESC"]
    if desc is not None:
        source["desc"] = desc

    case_operator = case_repayment["case_operator"]
    if case_operator is not None:
        source["caseOperator"] = case_operator

    case_operator_name = case_repayment["case_operator_name"]
    if case_operator_name is not None:
        source["caseOperatorName"] = case_operator_name

    apply_status = case_repayment["apply_status"]
    if apply_status is not None:
        source["applyStatus"] = apply_status

    voucher_url = case_repayment["voucher_url"]
    if voucher_url is not None:
        source["voucherUrl"] = voucher_url

    apply_desc = case_repayment["apply_desc"]
    if apply_desc is not None:
        source["applyDesc"] = apply_desc

    repayment_from = case_repayment["repayment_from"]
    if repayment_from is not None:
        source["repaymentFrom"] = repayment_from

    repayment_style = case_repayment["repayment_style"]
    if repayment_style is not None:
        source["repaymentStyle"] = repayment_style

    case_id = case_repayment["case_id"]
    if case_id is not None:
        source["caseId"] = case_id

    audit_by = case_repayment["audit_by"]
    if audit_by is not None:
        source["auditBy"] = audit_by

    org_id = case_repayment["org_id"]
    if org_id is not None:
        source["orgId"] = org_id

    dep_id = case_repayment["dep_id"]
    if dep_id is not None:
        source["depId"] = dep_id

    team_id = case_repayment["team_id"]
    if team_id is not None:
        source["teamId"] = team_id

    audit_time = case_repayment["audit_time"]
    if audit_time is not None:
        source["auditTime"] = audit_time

    product_id = case_repayment["product_id"]
    if product_id is not None:
        source["productId"] = product_id

    out_serial_temp = case_repayment["out_serial_temp"]
    if out_serial_temp is not None:
        source["outSerialTemp"] = out_serial_temp

    user_id = case_repayment["user_id"]
    if user_id is not None:
        source["userId"] = user_id

    org_delt_id = case_repayment["org_delt_id"]
    if org_delt_id is not None:
        source["orgDeltId"] = org_delt_id

    out_batch_id = case_repayment["out_batch_id"]
    if out_batch_id is not None:
        source["outBatchId"] = out_batch_id

    inner_batch_id = case_repayment["inner_batch_id"]
    if inner_batch_id is not None:
        source["innerBatchId"] = inner_batch_id

    amount = case_repayment["amount"]
    if amount is not None:
        source["amount"] = int(amount)

    entrust_start_time = case_repayment["entrust_start_time"]
    if entrust_start_time is not None:
        source["entrustStartTime"] = entrust_start_time

    entrust_end_time = case_repayment["entrust_end_time"]
    if entrust_end_time is not None:
        source["entrustEndTime"] = entrust_end_time

    case_status = case_repayment["case_status"]
    if case_status is not None:
        source["caseStatus"] = case_status

    case_allot_status = case_repayment["case_allot_status"]
    if case_allot_status is not None:
        source["caseAllotStatus"] = case_allot_status

    recovery = case_repayment["recovery"]
    if recovery is not None:
        source["recovery"] = recovery

    own_mobile = case_repayment["own_mobile"]
    if own_mobile is not None:
        source["ownMobile"] = own_mobile

    allot_agent = case_repayment["allot_agent"]
    if allot_agent is not None:
        source["allotAgent"] = allot_agent

    ext1 = case_repayment["ext1"]
    if ext1 is not None:
        source["ext1"] = ext1

    ext2 = case_repayment["ext2"]
    if ext2 is not None:
        source["ext2"] = ext2

    ext3 = case_repayment["ext3"]
    if ext3 is not None:
        source["ext3"] = ext3

    ext4 = case_repayment["ext4"]
    if ext4 is not None:
        source["ext4"] = ext4

    ext5 = case_repayment["ext5"]
    if ext5 is not None:
        source["ext5"] = ext5

    ext6 = case_repayment["ext6"]
    if ext6 is not None:
        source["ext6"] = ext6

    ext7 = case_repayment["ext7"]
    if ext7 is not None:
        source["ext7"] = str(decimal.Decimal(ext7).quantize(decimal.Decimal('0.00')))

    ext8 = case_repayment["ext8"]
    if ext8 is not None:
        source["ext8"] = str(decimal.Decimal(ext8).quantize(decimal.Decimal('0.00')))

    ext9 = case_repayment["ext9"]
    if ext9 is not None:
        source["ext9"] = ext9

    ext10 = case_repayment["ext10"]
    if ext10 is not None:
        source["ext10"] = ext10

    flow_id = case_repayment["flow_id"]
    if flow_id is not None:
        source["flowId"] = flow_id

    ext11 = case_repayment["ext11"]
    if ext11 is not None:
        source["ext11"] = ext11

    ext12 = case_repayment["ext12"]
    if ext12 is not None:
        source["ext12"] = ext12

    ext13 = case_repayment["ext13"]
    if ext13 is not None:
        source["ext13"] = ext13

    ext14 = case_repayment["ext14"]
    if ext14 is not None:
        source["ext14"] = ext14

    ext15 = case_repayment["ext15"]
    if ext15 is not None:
        source["ext15"] = str(decimal.Decimal(ext15).quantize(decimal.Decimal('0.00')))

    ext16 = case_repayment["ext16"]
    if ext16 is not None:
        source["ext16"] = str(decimal.Decimal(ext16).quantize(decimal.Decimal('0.00')))

    ext17 = case_repayment["ext17"]
    if ext17 is not None:
        source["ext17"] = str(decimal.Decimal(ext17).quantize(decimal.Decimal('0.00')))

    ext18 = case_repayment["ext18"]
    if ext18 is not None:
        source["ext18"] = str(decimal.Decimal(ext18).quantize(decimal.Decimal('0.00')))

    ext19 = case_repayment["ext19"]
    if ext19 is not None:
        source["ext19"] = ext19

    ext20 = case_repayment["ext20"]
    if ext20 is not None:
        source["ext20"] = ext20

    ext21 = case_repayment["ext21"]
    if ext21 is not None:
        source["ext21"] = ext21

    ext22 = case_repayment["ext22"]
    if ext22 is not None:
        source["ext22"] = ext22

    source_json = json.dumps(source, cls=DateEncoder)
    logger.info(f'case_repayment_source:{source_json} >>>')

    return {
        "_index": es_index_repayment,
        "_type": es_type,
        "_id": case_repayment_id,
        "_source": source_json
    }


def heartbeat():
    logger.info("-------sync_other_heartbeat-------")


def data_compare():
    """
    数据比对
    """
    try:
        sql_result = sync_util.db.select_one('select count(id) as sql_count from case_repayment')
        sql_count = sql_result['sql_count']

        _bool = BoolQuery(must_list=[], should_list=[], must_not_list=[])
        _search = Search(query=_bool, limit=0, order_list=[])
        _result = sync_util.es_client.search(index=es_index_repayment, body=_search.toMap())
        es_count = _result['hits']['total']['value']

        if sql_count != es_count:
            logger.error(f"-------sync_other_repayment_data_compare-------sql_count:{sql_count};es_count:{es_count}")
        else:
            logger.info(f"-------sync_other_repayment_data_compare-------sql_count:{sql_count};es_count:{es_count}")
    except Exception as e:
        logger.exception(e)


if __name__ == '__main__':
    try:
        background_schedulers = bg.BackgroundScheduler(timezone='Asia/Shanghai')
        background_schedulers.add_job(heartbeat, 'interval', seconds=10)
        background_schedulers.add_job(data_compare, 'cron', day_of_week='*', hour=23, minute=30)
        background_schedulers.start()
        main()
    except Exception as e:
        logger.exception(e)
