package com.jinghang.cash.modules.manage.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.enums.AuditState;
import com.jinghang.cash.enums.AuditStatus;
import com.jinghang.cash.enums.UseState;
import com.jinghang.cash.exception.BadRequestException;
import com.jinghang.cash.mapper.OfflineRepayReduceMapper;
import com.jinghang.cash.mapper.OrderMapper;
import com.jinghang.cash.modules.manage.remote.BusinessOfflineRepayReduceService;
import com.jinghang.cash.modules.manage.service.CustomerService;
import com.jinghang.cash.modules.manage.service.OfflineRepayReduceService;
import com.jinghang.cash.modules.manage.vo.req.AuditReq;
import com.jinghang.cash.modules.manage.vo.req.OfflineRepayReduceReq;
import com.jinghang.cash.modules.manage.vo.res.OfflineRepayReduceResponse;
import com.jinghang.cash.pojo.OfflineRepayReduce;
import com.jinghang.cash.utils.SecurityUtils;

import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.jinghang.ppd.api.dto.AuditDTO;
import com.jinghang.ppd.api.dto.UpdateOfflineRepayReduceDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 减免订单
 */
@Service
public class OfflineRepayReduceServiceImpl extends ServiceImpl<OfflineRepayReduceMapper, OfflineRepayReduce> implements OfflineRepayReduceService {
    private static final Logger logger = LoggerFactory.getLogger(CustomerService.class);

    @Autowired
    private OfflineRepayReduceMapper offlineRepayReduceMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private BusinessOfflineRepayReduceService openfeignApi;

    /**
     * 查询列表
     */
    public PageInfo<OfflineRepayReduceResponse> searchPage(OfflineRepayReduceReq req) {
        logger.info("减免订单查询列表入参:{}", JsonUtil.toJsonString(req));
        List<String> orderIds = new ArrayList<>();
        if (StringUtil.isNotBlank(req.getOrderId())) {
            orderIds.add(req.getOrderId());
        }
        //根据手机号或身份证号查询查询订单号。
        if (StringUtil.isNotBlank(req.getMobile()) || StringUtil.isNotBlank(req.getCertNo())) {
            List<String> ids = orderMapper.getIdByMobileOrCertNo(req.getMobile(), req.getCertNo());
            if (CollectionUtil.isNotEmpty(ids)) {
                orderIds.addAll(ids);
            }
        }
        req.setOrderIds(orderIds);
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        Page<OfflineRepayReduceResponse> list = offlineRepayReduceMapper.searchPage(req);
        return new PageInfo<>(list);
    }

    /**
     * 审核减免订单
     */
    public void audit(AuditReq req) {
        logger.info("审核减免订单入参 audit:{}", JsonUtil.toJsonString(req));
        OfflineRepayReduce entity = offlineRepayReduceMapper.selectById(req.getReduceId());
        if (entity == null) {
            throw new BadRequestException("该记录不存在");
        }

        AuditDTO auditDTO = new AuditDTO();
        auditDTO.setAuditor(SecurityUtils.getCurrentUsername());
        auditDTO.setId(req.getReduceId());
        auditDTO.setAuditState(req.getAuditState().toString());
        logger.info("调用cash-business接口，入参:{}", JsonUtil.toJsonString(auditDTO));
        //openfeignApi.audit(auditDTO);
    }

    /**
     * 减免订单-更新使用状态
     * 审批通过的需要当天使用，未使用则致为已过期
     */
    @Override
    public void doJob() {
        OfflineRepayReduce entity = new OfflineRepayReduce();
        entity.setAuditState(AuditStatus.PASS);
        entity.setUseState(UseState.WAIT);
        LambdaQueryWrapper<OfflineRepayReduce> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OfflineRepayReduce::getUseState, UseState.WAIT)
            .eq(OfflineRepayReduce::getAuditState, AuditState.PASS)
            .select(OfflineRepayReduce::getId, OfflineRepayReduce::getUseState);
        List<OfflineRepayReduce> list = offlineRepayReduceMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> ids = list.stream().map(OfflineRepayReduce::getId).collect(Collectors.toList());
        UpdateOfflineRepayReduceDTO reqDTO = new UpdateOfflineRepayReduceDTO();
        reqDTO.setIds(ids);
//        reqDTO.setUpdateBy(SecurityUtils.getCurrentUsername());
        logger.error("减免订单-更新使用状态入参:{}", JsonUtil.toJsonString(reqDTO));
       // openfeignApi.updateBatch(reqDTO);
    }

}
