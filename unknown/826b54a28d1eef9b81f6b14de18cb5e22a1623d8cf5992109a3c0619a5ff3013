package com.jinghang.cash.modules.manage.service;

import com.github.pagehelper.PageInfo;
import com.jinghang.cash.modules.manage.vo.PageParam;
import com.jinghang.cash.modules.manage.vo.req.CapitalConfigRequest;
import com.jinghang.cash.modules.manage.vo.req.EnableCapitalConfigReq;
import com.jinghang.cash.modules.manage.vo.req.EnableFlowRouteConfigReq;
import com.jinghang.cash.modules.manage.vo.req.FlowChannelReq;
import com.jinghang.cash.modules.manage.vo.req.FlowProtocolChannelDetailReq;
import com.jinghang.cash.modules.manage.vo.req.FlowRouteConfigRequest;
import com.jinghang.cash.modules.manage.vo.res.BankChannelResponse;
import com.jinghang.cash.modules.manage.vo.res.CapitalConfigInfoResponse;
import com.jinghang.cash.modules.manage.vo.res.CapitalConfigResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowProtocolChannelResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigInfoResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigPageResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigResponse;

import java.util.List;

/**
 * <AUTHOR>
 * 轻管后台-资金流量相关操作
 */
public interface FlowRouteConfigService  {
    /**
     * 轻管后台-资金配置列表查询
     */
    PageInfo<CapitalConfigResponse> queryCapitalFlowPage(PageParam reqVO);

    void updateCapitalConfig(CapitalConfigRequest reqVO);

    void enableCapitalConfig(EnableCapitalConfigReq reqVO);

    CapitalConfigInfoResponse getCapitalConfig(String id);

    void saveCapitalConfig(CapitalConfigRequest reqVO);

    List<BankChannelResponse> getCapitalList();

    List<FlowRouteConfigResponse> getFlowRouteConfig(String flowId);

    List<BankChannelResponse> getFlowConfig();

    void updateFlowRouteConfig(FlowRouteConfigRequest reqVO);

    FlowRouteConfigInfoResponse getFlowRouteConfigInfo(String flowId);

    PageInfo<FlowRouteConfigPageResponse> queryFlowRouteConfigPage(PageParam reqVO);

    void enableFlowConfig(EnableFlowRouteConfigReq reqVO);

    List<FlowRouteConfigResponse> getAvailableCapitalConfig(String flowId);

    List<BankChannelResponse> queryCapital();

    List<BankChannelResponse> getCouponFlowConfig();

    List<FlowProtocolChannelResponse> getFlowProtocolChannelList(FlowChannelReq req);

    FlowProtocolChannelResponse getFlowProtocolChannelDetail(FlowChannelReq req);

    void saveFlowProtocolChannelDetail(FlowProtocolChannelDetailReq req);

}
