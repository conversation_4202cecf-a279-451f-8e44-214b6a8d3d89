import { defineConfig, loadEnv } from 'vite'
// import { createVuePlugin } from 'vite-plugin-vue2'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { resolve } from 'path'
import { VitePWA } from 'vite-plugin-pwa'
import { viteCommonjs } from '@originjs/vite-plugin-commonjs'
import viteCompression from 'vite-plugin-compression'
// import legacy from '@vitejs/plugin-legacy'
// import requireTransform from 'vite-plugin-require-transform'
// import { cjs2esmVitePlugin } from 'cjs2esmodule'

// import requireTransform from 'vite-plugin-require-transform';
//  通过 vite 提供的 loadEnv 方法, 将环境变量重新赋值到 import.meta.env 上
const chunk1 = ['vue-router', 'vue', 'vuex', 'axios', 'view-ui-plus', 'vxe-table', 'vue-clipboard3', 'xe-utils']
const chunk2 = ['echarts', '@antv/g6']
const chunk3 = ['vuedraggable', 'wavesurfer.js', 'xlsx', 'sortablejs']
// const dependencies = require('./package.json').dependencies
let APP_ENV
const transformIndexHtml = (code) => {
  switch (APP_ENV.VITE_NAME) {
    case 'open':
      return code.replace(/__MAIN__/, '/opensrc/main.js') // open
    default:
      return code.replace(/__MAIN__/, '/src/main.js') // src
  }
}
// 根据环境输出打包路径

const getOutDir = (env) => {
  let outDir = ''
  switch (env) {
    case 'user':
      outDir = 'webApp'
      break
    case 'open':
      outDir = 'webAppOpen'
      break
    default:
      outDir = 'webApp'
  }
  return outDir
}

export default defineConfig(({ command, mode }) => {
  // import.meta.env = loadEnv(mode, process.cwd(), '')
  APP_ENV = loadEnv(mode, process.cwd(), '')
  // console.log(import.meta.env.VITE_NAME)
  return {
    plugins: [
      {
        name: 'anmi-transform', // 针对admin open user端打包替换入口文件
        enforce: 'pre', // 指的是在createVuePlugin之前运行此插件
        // vite build is production will not invoke `transformIndexHtml`
        transform(code, id) {
          if (id.endsWith('.html')) {
            return { code: transformIndexHtml(code), map: null }
          }
        },
        transformIndexHtml
      },
      viteCompression({
        threshold: 10240 // 对大于 10.24k 的文件进行压缩
      }),
      vue(),
      vueJsx(),
      viteCommonjs({
        //   transformMixedEsModules: true,
        skipPreBuild: true,
        exclude: ['src/components/tables/tables.1.vue']
        //   include: ['pdfjs-dist']
      }),
      VitePWA({
      // includeAssets: ['favicon.svg'],
        // manifest: false,
        registerType: 'autoUpdate',
        // globPatterns: [],
        // globIgnores: [/\\.map$/, /asset-manifest\\.json$/, /index.html/, new RegExp('^/xapi'), new RegExp('^/static')],
        devOptions: {
          enabled: false
        },
        selfDestroying: APP_ENV.VITE_NAME !== 'user',
        // selfDestroying: true,
        workbox: {
          cacheId: 'anmi-cache',
          globPatterns: ['**/*.{html,js,css,png,jpg}'], // 匹配的文件
          // globIgnores: ['sw.js', '*.html'],
          clientsClaim: true,
          skipWaiting: true,
          navigateFallback: null,
          navigateFallbackDenylist: [new RegExp('^/static'), /static/], // /_app/ new RegExp('^/static')
          // exclude: [/index.html/, /\\.map$/, /asset-manifest\\.json$/, /xapi/],
          runtimeCaching: [
            // /^(?=.*hello)(?!.*world).*$/im 包含 hello，但不包括 world
            {
              urlPattern: /^(?=.*xapi)((?!.*downloadT)(?!.*static)(?!.*home)(?!.*login).)*$/im,
              // urlPattern: /^((?!downloadT)(?!static)(?!login)(?!home)(?=xapi).)+$/, // /^((?!downloadT)(?!static).)+$/
              // urlPattern: /xapi/i, // 接口缓存 此处填你想缓存的接口正则匹配
              // urlPattern: ({ url }) => url.href.indexOf('?download') !== -1,
              // urlPattern: ({ url }) => url.origin === 'http://localhost:8082',
              handler: 'NetworkFirst', // Network-First Network-Only Cache-Only StaleWhileRevalidate
              options: {
                cacheName: 'anmi-api',
                cacheableResponse: {
                  statuses: [200]
                }
              }
            },
            {
              urlPattern: /.*\.js.*/,
              handler: 'StaleWhileRevalidate',
              options: {
                cacheName: 'anmi-js',
                expiration: {
                  maxEntries: 30, // 最多缓存30个，超过的按照LRU原则删除
                  maxAgeSeconds: 30 * 24 * 60 * 60
                },
                cacheableResponse: {
                  statuses: [200]
                }
              }
            },
            {
              urlPattern: /.*\.css.*/,
              handler: 'StaleWhileRevalidate',
              options: {
                cacheName: 'anmi-css',
                expiration: {
                  maxEntries: 20,
                  maxAgeSeconds: 15 * 24 * 60 * 60
                },
                cacheableResponse: {
                  statuses: [200]
                }
              }
            },
            {
              urlPattern: /(.*?)\.(png|jpe?g|svg|gif|bmp|psd|tiff|tga|eps|woff2|ico|ttf|woff)/, // 图片字体缓存
              handler: 'StaleWhileRevalidate',
              options: {
                cacheName: 'image-font-cache',
                expiration: {
                  // 最多30个图
                  maxEntries: 30
                }
              }
            },
            {
              urlPattern: /.*\.html.*/,
              handler: 'StaleWhileRevalidate',
              options: {
                cacheName: 'anmi-html',
                expiration: {
                  maxEntries: 20,
                  maxAgeSeconds: 15 * 24 * 60 * 60
                },
                cacheableResponse: {
                  statuses: [200]
                }
              }
            }
          ]
        }
      })
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '~@': resolve(__dirname, './src'),
        '_c': resolve(__dirname, './src/components'),
        '_vc': resolve(__dirname, './src/view/components'),
        '@open': resolve(__dirname, './opensrc')
        // vue: '@vue/compat'
      },
      dedupe: [
        'vue'
      ],
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          globalVars: {
            hack: `true; @import '@/style/vars.less';` // 引入全局less变量
          }
        }
      }
    },
    optimizeDeps: {
      exclude: ['__MAIN__'], // 排除 __MAIN__
      include: ['core-js'],
      force: true
    },
    define: {
      'process.env': loadEnv(mode, process.cwd(), '')
    },
    server: {
      port: 8083,
      // host: true,
      force: true,
      hmr: true,
      host: true,
      watch: {
        usePolling: true
      },
      proxy: {
        '/xapi': {
          target: 'https://bpw-regress.anmiai.com/', // 测试环境
          changeOrigin: true,
          secure: false
        }
      }
    },
    build: {
      outDir: getOutDir(APP_ENV.VITE_NAME),
      reportCompressedSize: false, // 是否启用压缩大小报告
      target: 'es2015',
      chunkSizeWarningLimit: 2000,
      rollupOptions: {
        treeshake: true,
        output: {
          manualChunks: {
            vendor: chunk1,
            vendor1: chunk2,
            vendor2: chunk3
          }
        }
      },
      minify: 'esbuild'
    //   commonjsOptions: {
    //     // transformMixedEsModules: true
    //   }
    }
  }
})
