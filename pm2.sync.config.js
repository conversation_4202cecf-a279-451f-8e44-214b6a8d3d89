module.exports = {
    apps : [
        {
            name   : "sync_bpw_case_info.py",
            script : "./sync_bpw_case_info.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        },
         {
            name   : "sync_bpw_case_log.py",
            script : "./sync_bpw_case_log.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        },
         {
            name   : "sync_bpw_case_operation.py",
            script : "./sync_bpw_case_operation.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        },
         {
            name   : "sync_bpw_case_operation_use_less.py",
            script : "./sync_bpw_case_operation_use_less.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        }
        ,
         {
            name   : "sync_bpw_other.py",
            script : "./sync_bpw_other.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        }
        ,
         {
            name   : "sync_bpw_cti_audio_2oss.py",
            script : "./sync_bpw_cti_audio_2oss.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        }
    ]
};