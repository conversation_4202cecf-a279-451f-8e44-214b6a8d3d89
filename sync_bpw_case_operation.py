# 监听case_operation，并同步数据到es
# 增删改 case_operation 更新 case_operation
import json
import time
from datasync.base_data_to_es import DataToEs
from datasync.datetime_to_json import DateEncoder
from concurrent.futures import ThreadPoolExecutor
import threading
from apscheduler.schedulers import background as bg
from es_query_builder import *
from utils.logger_util import LOG

task_topic = "bpw_anmi_case_operation"

es_index = "bpw_case_operation_query"
es_type = "_doc"

logger = LOG.get_logger("sync_bpw_case_operation")

sync_util = DataToEs(task_topic)

# 查询转移sql
transerDataSqlMeta = "SELECT cao.create_time,cao.action_type,cao.call_type,cao.reduce_amount," \
                     "cao.DESC,cao.ptp_amount,cao.ptp_time,cao.next_time,cao.id,cao.create_by," \
                     "cao.update_by,cao.call_uuid,cao.call_style,cao.call_time,cao.ring_durtion," \
                     "cao.call_durtion,cao.outcome,cao.submit_type,cao.caller,cao.create_type," \
                     "cao.con_mobile,cao.con_name,cao.relation_type,cao.operator_name,cao.STATUS," \
                     "cao.tag,cao.operation_state,cao.is_hidden,cao.case_id,cao.org_id,cao.org_delt_id," \
                     "cao.out_serial_no,cao.out_serial_temp,cao.COMMENT,cao.admin_submitter,cao.client_label_id," \
                     "cao.callback_flag,cao.site_id,cao.site_name,cao.oper_time,cao.pool_id,cao.auto_assist_record," \
                     "cao.total_oper_time,cao.is_push,cao.intention,cao.intention_name," \
                     "ca.product_id,ca.user_id,ca.team_id,ca.dep_id,ca.out_batch_id," \
                     "ca.inner_batch_id,ca.NAME,ca.id_card,ca.own_mobile," \
                     "batch.NAME AS out_batch_no " \
                     "FROM `case_operation` cao " \
                     "LEFT JOIN `case_info` ca ON cao.case_id=ca.id AND ca.recovery<>-2 " \
                     "LEFT JOIN `out_batch` batch ON ca.out_batch_id=batch.id WHERE cao.id in %s"


def main():
    partitions = sync_util.kafka_consumer.get_partitions()
    partitions_size = len(partitions)
    thread_pool = ThreadPoolExecutor(max_workers=partitions_size, thread_name_prefix="sync_bpw_case_operation")
    futures = []
    for partition in partitions:
        future = thread_pool.submit(run, partition)
        futures.append(future)
    for future in futures:
        future.result()


def run(partition):
    while True:
        try:
            thread_name = threading.current_thread().name
            start_offset, end_offset, bin_log_datas = sync_util.get_bin_log_data_by_partition(partition)
            if bin_log_datas:
                logger.info(f'partition:{partition}消费到binlog数据 >>>')
                insert_or_update_list = []
                delete_list = []
                action_list = []
                for bin_log_data in bin_log_datas:
                    logger.info(f'{thread_name}消费到binlog数据 >>> {bin_log_data}')
                    json_bin_log_data = json.loads(bin_log_data)

                    log_type = json_bin_log_data.get('type')
                    if log_type == 'INSERT' or log_type == 'UPDATE':
                        case_operation_list = json_bin_log_data.get('data')
                        for case_operation in case_operation_list:
                            case_operation_id = int(case_operation["id"])
                            insert_or_update_list.append(case_operation_id)
                    elif log_type == 'DELETE':
                        case_operation_list = json_bin_log_data.get('data')
                        for case_operation in case_operation_list:
                            case_operation_id = int(case_operation["id"])
                            delete_list.append(case_operation_id)

                if len(insert_or_update_list) > 0:
                    action_list.extend(build_insert_or_update_action_list(insert_or_update_list))

                if len(delete_list) > 0:
                    action_list.extend(build_delete_action_list(delete_list))

                if len(action_list) > 0:
                    sync_util.es_bulk(action_list)
            else:
                time.sleep(1)
            #logger.info("<<<<<<<<<<<<<<<<<<<<" + thread_name + " == sync_case_operation>>>>>>>>>>>>>>>>>>>>")
        except Exception as e:
            logger.exception(e)


def build_insert_or_update_action_list(insert_or_update_list):
    case_operations = sync_util.db.select_by_param(transerDataSqlMeta, (insert_or_update_list,))
    result_list = []
    for case_operation in case_operations:
        result_list.append(build_common_source(case_operation))
    return result_list


def build_delete_action_list(delete_list):
    result_list = []
    for case_operation_id in delete_list:
        result_list.append(
            {
                "_op_type": 'delete',
                "_index": es_index,
                "_type": es_type,
                "_id": case_operation_id
            }
        )
    return result_list


def build_common_source(case_operation):
    case_operation_id = case_operation["id"]
    source = {}

    if case_operation_id is not None:
        source["id"] = case_operation_id

    create_time = case_operation["create_time"]
    if create_time is not None:
        source["createTime"] = create_time

    action_type = case_operation["action_type"]
    if action_type is not None:
        source["actionType"] = action_type

    call_type = case_operation["call_type"]
    if call_type is not None:
        source["callType"] = call_type

    reduce_amount = case_operation["reduce_amount"]
    if reduce_amount is not None:
        source["reduceAmount"] = reduce_amount

    desc = case_operation["DESC"]
    if desc is not None:
        source["desc"] = desc

    ptp_amount = case_operation["ptp_amount"]
    if ptp_amount is not None:
        source["ptpAmount"] = ptp_amount

    ptp_time = case_operation["ptp_time"]
    if ptp_time is not None:
        source["ptpTime"] = ptp_time

    next_time = case_operation["next_time"]
    if next_time is not None:
        source["nextTime"] = next_time

    id = case_operation["id"]
    if id is not None:
        source["id"] = id

    create_by = case_operation["create_by"]
    if create_by is not None:
        source["createBy"] = create_by

    update_by = case_operation["update_by"]
    if update_by is not None:
        source["updateBy"] = update_by

    call_uuid = case_operation["call_uuid"]
    if call_uuid is not None:
        source["callUuid"] = call_uuid

    call_style = case_operation["call_style"]
    if call_style is not None:
        source["callStyle"] = call_style

    call_time = case_operation["call_time"]
    if call_time is not None:
        source["callTime"] = call_time

    ring_durtion = case_operation["ring_durtion"]
    if ring_durtion is not None:
        source["ringDurtion"] = ring_durtion

    call_durtion = case_operation["call_durtion"]
    if call_durtion is not None:
        source["callDurtion"] = call_durtion

    outcome = case_operation["outcome"]
    if outcome is not None:
        source["outcome"] = outcome

    submit_type = case_operation["submit_type"]
    if submit_type is not None:
        source["submitType"] = submit_type

    caller = case_operation["caller"]
    if caller is not None:
        source["caller"] = caller

    create_type = case_operation["create_type"]
    if create_type is not None:
        source["createType"] = create_type

    con_mobile = case_operation["con_mobile"]
    if con_mobile is not None:
        source["conMobile"] = con_mobile

    con_name = case_operation["con_name"]
    if con_name is not None:
        source["conName"] = con_name

    relation_type = case_operation["relation_type"]
    if relation_type is not None:
        source["relationType"] = relation_type

    operator_name = case_operation["operator_name"]
    if operator_name is not None:
        source["operatorName"] = operator_name

    status = case_operation["STATUS"]
    if status is not None:
        source["status"] = status

    tag = case_operation["tag"]
    if tag is not None:
        source["tag"] = tag

    operation_state = case_operation["operation_state"]
    if operation_state is not None:
        source["operationState"] = operation_state

    is_hidden = case_operation["is_hidden"]
    if is_hidden is not None:
        source["isHidden"] = is_hidden

    case_id = case_operation["case_id"]
    if case_id is not None:
        source["caseId"] = case_id

    org_id = case_operation["org_id"]
    if org_id is not None:
        source["orgId"] = org_id

    org_delt_id = case_operation["org_delt_id"]
    if org_delt_id is not None:
        source["orgDeltId"] = org_delt_id

    out_serial_no = case_operation["out_serial_no"]
    if out_serial_no is not None:
        source["outSerialNo"] = out_serial_no

    out_serial_no_search = case_operation["out_serial_temp"]
    if out_serial_no_search is not None:
        source["outSerialNoSearch"] = out_serial_no_search

    comment = case_operation["COMMENT"]
    if comment is not None:
        source["comment"] = comment

    admin_submitter = case_operation["admin_submitter"]
    if admin_submitter is not None:
        source["adminSubmitter"] = admin_submitter

    client_label_id = case_operation["client_label_id"]
    if client_label_id is not None:
        source["clientLabelId"] = client_label_id

    product_id = case_operation["product_id"]
    if product_id is not None:
        source["productId"] = product_id

    user_id = case_operation["user_id"]
    if user_id is not None:
        source["userId"] = user_id

    team_id = case_operation["team_id"]
    if team_id is not None:
        source["teamId"] = team_id

    dep_id = case_operation["dep_id"]
    if dep_id is not None:
        source["depId"] = dep_id

    out_batch_id = case_operation["out_batch_id"]
    if out_batch_id is not None:
        source["outBatchId"] = out_batch_id

    inner_batch_id = case_operation["inner_batch_id"]
    if inner_batch_id is not None:
        source["innerBatchId"] = inner_batch_id

    name = case_operation["NAME"]
    if name is not None:
        source["name"] = name

    id_card = case_operation["id_card"]
    if id_card is not None:
        source["idCard"] = id_card

    own_mobile = case_operation["own_mobile"]
    if own_mobile is not None:
        source["ownMobile"] = own_mobile

    out_batch_no = case_operation["out_batch_no"]
    if out_batch_no is not None:
        source["outBatchNo"] = out_batch_no

    callback_flag = case_operation["callback_flag"]
    if callback_flag is not None:
        source["callbackFlag"] = callback_flag

    site_id = case_operation["site_id"]
    if site_id is not None:
        source["siteId"] = site_id

    site_name = case_operation["site_name"]
    if site_name is not None:
        source["siteName"] = site_name

    oper_time = case_operation["oper_time"]
    if oper_time is not None:
        source["operTime"] = oper_time

    is_push = case_operation["is_push"]
    if is_push is not None:
        source["isPush"] = is_push

    total_oper_time = case_operation["total_oper_time"]
    if total_oper_time is not None:
        source["totalOperTime"] = total_oper_time

    pool_id = case_operation["pool_id"]
    if pool_id is not None:
        source["poolId"] = pool_id

    auto_assist_record = case_operation["auto_assist_record"]
    if auto_assist_record is not None:
        source["autoAssistRecord"] = auto_assist_record

    intention = case_operation["intention"]
    if intention is not None:
        source["intention"] = intention

    intention_name = case_operation["intention_name"]
    if intention_name is not None:
        source["intentionName"] = intention_name

    source_json = json.dumps(source, cls=DateEncoder)
    logger.info(f'case_operation_source:{source_json} >>>')

    return {
        "_index": es_index,
        "_type": es_type,
        "_id": case_operation_id,
        "_source": source_json
    }


def heartbeat():
    logger.info("-------sync_case_operation_heartbeat-------")


def data_compare():
    """
    数据比对
    """
    try:
        sql_result = sync_util.db.select_one('select count(id) as sql_count from case_operation')
        sql_count = sql_result['sql_count']

        _bool = BoolQuery(must_list=[], should_list=[], must_not_list=[])
        _search = Search(query=_bool, limit=0, order_list=[])
        _result = sync_util.es_client.search(index=es_index, body=_search.toMap())
        es_count = _result['hits']['total']['value']

        if sql_count != es_count:
            logger.error(f"-------sync_case_operation_data_compare-------sql_count:{sql_count};es_count:{es_count}")
        else:
            logger.info(f"-------sync_case_operation_data_compare-------sql_count:{sql_count};es_count:{es_count}")
    except Exception as e:
        logger.exception(e)


if __name__ == '__main__':
    try:
        background_schedulers = bg.BackgroundScheduler(timezone='Asia/Shanghai')
        background_schedulers.add_job(heartbeat, 'interval', seconds=10)
        background_schedulers.add_job(data_compare, 'cron', day_of_week='*', hour=23, minute=30)
        background_schedulers.start()
        main()
    except Exception as e:
        logger.exception(e)
