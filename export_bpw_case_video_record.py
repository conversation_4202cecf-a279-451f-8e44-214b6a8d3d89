#!/usr/bin/python
# -*- coding: UTF-8 -*-
import importlib
import time
import json
# 调用脚本
import subprocess
# 文件
import os
import datetime as datetime
import openpyxl
from openpyxl.utils import get_column_letter
# 数据库
import pymysql
from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE
from NacosHelper import NacosHelper
from utils import base_utils
from utils.base_utils import init_base_conf, init_heartbeat
from utils import encrypt_utils

config = NacosHelper().init_config()
logger, fileUtil, ossSwitch, esSwitch, encryptSwitch, encryptMode, bucket, mysql_pool, taskUtil, redis_pool = init_base_conf("export_bpw_case_video_record", config)

zipRootPath = "../zip/"

# 联系人状态字典
contact_status_dict = {0: '正常', 1: '无效'}

videoRoomRecordDict = {
   "videoRoomRecordDict": {
       "0": {"name": "视频首次发起时间", "value": "create_time"},
       "1": {"name": "案件编号", "value": "out_serial_no"},
       "2": {"name": "姓名", "value": "case_name"},
       "3": {"name": "债务人手机号", "value": "own_mobile"},
       "4": {"name": "身份证号", "value": "id_card"},
       "5": {"name": "人脸认证结果", "value": "auth_result"},
       "6": {"name": "发起人", "value": "initiator"},
       "7": {"name": "视频通知方式", "value": "notice_type"},
       "8": {"name": "委案公司", "value": "org_delt_name"},
       "9": {"name": "委案产品", "value": "product_name"},
       "10": {"name": "催员", "value": "agent"},
       "11": {"name": "视频文件", "value": "video_url"}
   },
   "videoRoomRecordDict_tj": {
       "0": {"name": "视频首次发起时间", "value": "create_time"},
       "1": {"name": "案件编号", "value": "out_serial_no"},
       "2": {"name": "姓名", "value": "case_name"},
       "3": {"name": "被申请人手机号", "value": "own_mobile"},
       "4": {"name": "身份证号", "value": "id_card"},
       "5": {"name": "人脸认证结果", "value": "auth_result"},
       "6": {"name": "发起人", "value": "initiator"},
       "7": {"name": "视频通知方式", "value": "notice_type"},
       "8": {"name": "申请方", "value": "org_delt_name"},
       "9": {"name": "申请产品", "value": "product_name"},
       "10": {"name": "调解员", "value": "agent"},
       "11": {"name": "视频文件", "value": "video_url"}
    }
}

# 结案类型
endTypeLanguageDict = {
    "endTypeDict": {0: '催收失败', 1: '催收成功'},
    "endTypeDict_tj": {0: '调解失败', 1: '调解成功'}
}

# 催收进程字典
operationStateDict = {'-1': '未联系上', '0': '承诺还款', '1': '筹款中', '2': '联系本人中', '3': '联系家人中', '4': '联系第三方中', '5': '谈判中', '6': '要求减免', '7': '否认用款', '8': '已结清',
                      '9': '已外访', '10': '准备外访', '11': '重点跟进'}
# 催收结果字典
OperStatusDict = {'-4': '未知', '-3': '未填写', '-2': '未处理', '-1': '未联系上', '0': '承诺还款', '1': '意愿还款', '2': '谈判中', '3': '有效转告', '4': '声称已还款', '5': '要求减免',
                  '6': '未触达', '7': '非本人', '8': '无效转告', '11': '声称要投诉', '12': '分期还款', '13': '债务人失联', '14': '争议案件', '15': '查询案件'}

# 减免审核结果字典
applyStatusDict = {'0': '申请', '1': '审核通过', '2': '审核失败'}

# 人脸识别认证状态
authFaceDict = {0: '未认证', 1: '成功', 2: '失败'}

# 视频通知方式
noticeTypeDict = {0: '被邀请人自主搜索', 1: '短信通知'}

# 案件信息基本字段
caseBaseSql = " ca.id,ca.out_serial_no,ca.org_delt_id,ca.own_mobile,od.name as org_delt_name,p.name as product_name,ca.name as case_name,ca.id_card,\
            ca.entrust_start_time,ca.entrust_end_time,ca.amount,ca.pay_amount,inbat.name as batch_no,outbat.name as out_batch_no,\
            u2.name as agent,u2.user_no,ca.case_status,ca.allot_status,ca.operation_state,ca.overdue_date,ca.overdue_days,\
            dep_team1.name as dep_name,dep_team2.name as team_name,ca.call_status,ca.field_json,ca.oper_status,\
            ca.last_follow_time,cador.last_follow_time as debtor_last_follow_time,cador.follow_count as debtor_follow_count,ca.division_time,\
            ca.end_config_id,ca.end_type,cec.attached_res, ca.delay_no, ca.end_no"

selectFiledSql = " mvr.id as room_id, mvr.auth_result, mvr.create_by , u1.name as initiator, mvr.notice_type, mvr.create_time" \

baseTableSql = " from mediation_video_room mvr "

leftJoinBaseTableSql = "LEFT JOIN case_info ca on ca.id = mvr.case_id " \
                       "LEFT JOIN `user` u1 on u1.id = mvr.create_by " \
                       "LEFT JOIN org_delt od on od.id = ca.org_delt_id " \
                       "LEFT JOIN `product` p on ca.product_id = p.id " \
                       "LEFT JOIN `user` u2 on ca.user_id = u2.id "

leftJoinTableSql = "LEFT JOIN case_info ca on ca.id = mvr.case_id " \
                 "LEFT JOIN `user` u1 on u1.id = mvr.create_by " \
                 "LEFT JOIN org_delt od on od.id = ca.org_delt_id " \
                 "LEFT JOIN `product` p on ca.product_id = p.id " \
                 "LEFT JOIN `inner_batch` inbat on ca.inner_batch_id = inbat.id " \
                 "LEFT JOIN `out_batch` outbat on ca.out_batch_id = outbat.id " \
                 "LEFT JOIN `org_dep_team` dep_team1 on ca.dep_id = dep_team1.id " \
                 "LEFT JOIN `org_dep_team` dep_team2 on ca.team_id = dep_team2.id " \
                 "LEFT JOIN `user` u2 on ca.user_id = u2.id " \
                 "LEFT JOIN case_debtor cador on cador.id=ca.debt_id " \
                 "LEFT JOIN case_end_config cec on ca.end_config_id = cec.id"

# 案件标签
videoRecordSql = "select mv.room_id, mv.video_url from mediation_video_record mv where mv.room_id in %s"

# 更新进度
updateProgressSql = "update download_task set progress=%s where id=%s"

# 默认分页大小
pageSize = 500

def doTasks():
   tasks = taskUtil.getUndoTasks(25)
   for task in tasks or []:
       handleTask(task)


def handleTask(task):
    orgId, data, taskId = task["org_id"], task["data"], task["id"]
    logger.info("执行任务id:" + str(taskId) + ", data:" + repr(data))
    taskUtil.updateTaskIng(taskId)
    # 任务处理中
    logger.info("更新调解视频记录导出任务id: " + str(taskId) + " 为处理中...")

    language = taskUtil.getOrgLanguage(orgId)
    try:
        all_fields = taskUtil.getLanguageFieldDict("caseExportFields", language)
        start = datetime.datetime.now()
        export_video_record_task(data, taskId, all_fields, language)
        end = datetime.datetime.now()
        logger.info("任务id:" + str(taskId) + "开始时间:" + str(start) + ",结束时间:" + str(end))
    except Exception as ex:
        logger.exception(ex)
        taskUtil.updateFail(taskId, str(ex))


def export_video_record_task(data, taskId, allfields, language):
    if data is None:
        raise RuntimeError("任务序列化数据为空")
    if allfields is None:
        raise RuntimeError("全部案件导出字段为空")
    dataJson = json.loads(data)
    selectWhereSql = " where mvr.org_id=" + str(dataJson.get('orgId'))

    # 分公司
    if dataJson.get('depId') is not None and dataJson.get('depId') != '':
        selectWhereSql += " and ca.dep_id = " + str(dataJson.get('depId'))
    # 小组
    if dataJson.get('teamId') is not None and dataJson.get('teamId') != '':
        selectWhereSql += " and ca.team_id = " + str(dataJson.get('teamId'))
    # 通知类型
    if dataJson.get("noticeType") is not None and dataJson.get('noticeType') != '':
        selectWhereSql += " and mvr.notice_type = " + str(dataJson.get('noticeType'))
    # 日期区间
    if dataJson.get('startTime') is not None and dataJson.get('endTime') is not None:
        startTimeStr = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(dataJson.get('startTime') / 1000))
        endTimeStr = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(dataJson.get('endTime') / 1000))
        selectWhereSql += " and mvr.create_time >= '" + startTimeStr + "' and mvr.create_time <= '" + endTimeStr + "' "
    # 手机号
    if dataJson.get('mobiles') is not None:
        mobilesStr = str(dataJson['mobiles']).strip('[').strip(']')
        length = len(dataJson['mobiles'])
        if length > 1:
            selectWhereSql += " and ca.own_mobile in (" + mobilesStr + ")"
        elif length == 1:
            selectWhereSql += " and ca.own_mobile like '" + dataJson['mobiles'][0] + "%' "
    # 姓名
    if dataJson.get('names') is not None:
        namesStr = str(dataJson['names']).strip('[').strip(']')
        length = len(dataJson['names'])
        if length > 1:
            selectWhereSql += " and ca.name in (" + namesStr + ") "
        elif length == 1:
            selectWhereSql += " and ca.name like '" + dataJson['names'][0] + "%' "
    # 身份证号
    if dataJson.get('idCards') is not None:
        idCardsStr = str(dataJson['idCards']).strip('[').strip(']')
        length = len(dataJson['idCards'])
        if length > 1:
            selectWhereSql += " and ca.id_card in (" + idCardsStr + ") "
        elif length == 1:
            selectWhereSql += " and ca.id_card like '" + dataJson['idCards'][0] + "%' "
    # 案件编号
    if dataJson.get('outSerialTemps') is not None:
        outSerialTempsStr = str(dataJson['outSerialTemps']).strip('[').strip(']')
        length = len(dataJson['outSerialTemps'])
        if length > 1:
            selectWhereSql += " and ca.out_serial_temp in (" + outSerialTempsStr + ") "
        elif length == 1:
            selectWhereSql += " and ca.out_serial_temp like '" + dataJson['outSerialTemps'][0] + "%' "
    # 催员
    if dataJson.get('userIds') is not None:
        selectWhereSql += " and ca.user_id in (" + str(dataJson['userIds']).strip('[').strip(']') + ") "
    # 委案公司
    if dataJson.get('orgDeltIds') is not None:
        selectWhereSql += " and ca.org_delt_id in (" + str(dataJson['orgDeltIds']).strip('[').strip(']') + ") "
    # 委案产品
    if dataJson.get('productIds') is not None:
        selectWhereSql += " and ca.product_id in (" + str(dataJson['productIds']).strip('[').strip(']') + ") "

    # 获取模板字段
    caseEntity = {}
    operStateDict = {}
    operStatusDict = {}
    if dataJson.get('templateId') is not None:
        caseEntity, containsCaseNote = getCaseEntity(dataJson.get('templateId'), allfields)
        logger.info("案件模板字段:" + repr(caseEntity))
        orgId = dataJson.get('orgId')
        operStateDict = taskUtil.getOperaStateDict(orgId)
        operStatusDict = taskUtil.getOperStatusDict(orgId)
    # 组装视频记录的信息
    videoCaseEntiy(caseEntity, language)
    logger.info("视频记录字段：" + repr(caseEntity))
    # 导出
    export_excel(taskId, caseEntity, selectWhereSql, language, operStateDict, operStatusDict)


#def export_video_record(orgId, data, taskId, allfieldsm, language):


def videoCaseEntiy(caseEntity, language):
    key = "videoRoomRecordDict"
    if language is not None and language != "":
        key = key + "_" + language
    videoRoomRecordLanguage = videoRoomRecordDict[key]
    # 组装合并案件和视频记录字段 重复的只取一个
    for key_b, value_b in videoRoomRecordLanguage.items():
        duplicated = False
        for value_a in caseEntity.values():
            if value_a.get("value") == value_b.get("value"):
                duplicated = True
                break
        if not duplicated:
            key_a = str(len(caseEntity))
            caseEntity[key_a] = value_b

def getFieldName(keyName, allFields):
    for field in allFields:
        if keyName == field['value']:
            return field['name']

def createNewSheet(wb, sheetNum, temFieldJson, allfieldJson):
    if sheetNum == 1:
        ws = wb.active
        ws.title = "视频记录" + str(sheetNum)
    else:
        ws = wb.create_sheet("视频记录" + str(sheetNum))
    # 创建表头
    rows = len(temFieldJson)
    for i in range(1, rows + 1):
        ws.cell(row=1, column=i, value=getFieldName(temFieldJson[str(i - 1)], allfieldJson))
    return ws

def export_excel(taskId, caseEntity, selectWhereSql, language, operStateDict, operStatusDict) :
    try:
        global num
        wbk = openpyxl.Workbook(write_only=True)
        # 组装视频信息表格
        videoSql = "select count(0) as cnt " + baseTableSql + leftJoinBaseTableSql + selectWhereSql
        logger.info("我看看videoSql: " + videoSql)
        count = mysql_pool.select_one(videoSql)
        # 根据count计算需要分页数量
        if count["cnt"] == 0:
            raise RuntimeError("查询视频记录信息为空")
        # 每一页数量设置为500
        page = int(count["cnt"] / pageSize) if count["cnt"] % pageSize == 0 else int(count["cnt"] / pageSize) + 1
        sheet = wbk.create_sheet("视频记录")
        num = 1
        for p in range(0, page):
            limitSql = "select " + caseBaseSql + "," + selectFiledSql + baseTableSql +  \
                       " inner join (" + "select" + selectFiledSql + baseTableSql + leftJoinBaseTableSql + selectWhereSql + " limit " \
                       + str(p * pageSize) + "," + str(pageSize) + ") tmp on tmp.room_id = mvr.id " \
                       + leftJoinTableSql
            print("我看看: " + limitSql)
            isCreateRow = False
            if num == 1:
                isCreateRow = True
            write_to_excel_sheet(sheet, "视频记录", caseEntity, limitSql, isCreateRow, language, operStatusDict, operStateDict)
            progress = p / page * 100
            taskUtil.updateProgress(taskId, progress)
        videoCount = num - 1
        doSave(wbk, taskId, videoCount)
    except Exception as err:
        raise RuntimeError(err)


def doSave(wbk, taskId, dataNums):
    logger.info("表格已生成，保存中")
    dateStr = time.strftime('%Y-%m-%d', time.localtime(int(time.time())))
    excelName = "视频记录数据导出_" + dateStr + "_" + str(taskId) + ".xlsx"
    wbk.save(excelName)
    downloadUrl, fileSize = base_utils.upload_file(ossSwitch, bucket, fileUtil, excelName)
    taskUtil.updateSuccess(taskId, downloadUrl, dataNums)


def getCaseEntity(templateId, allfields):
    temFields = taskUtil.getTemplateFields(templateId)
    allFieldJson = json.loads(allfields)
    temFieldJson = json.loads(temFields)
    temFieldJson, containsCaseNote = resortJsonObj(temFieldJson)
    # 组装模板所有字段
    caseEntity = {}
    for k in range(len(temFieldJson)):
        fieldValue = temFieldJson[str(k)]
        for field in allFieldJson:
            if fieldValue == field['value']:
                caseEntity[str(k)] = field
                break
        else:
            # 自定义字段
            sql = f"select name from custom_field cuf where cuf.value='{fieldValue}'"
            fieldName = mysql_pool.select_one(sql)
            if fieldName is None:
                # 自定义搜索字段
                sql = "select name from custom_search_field cef where cef.search_key='" + str(fieldValue) + "'"
                fieldName = mysql_pool.select_one(sql)
            if fieldName is None:
                fieldName = fieldValue
            else:
                fieldName = fieldName["name"]
            fieldEntity = {'name': fieldName, 'value': fieldValue}
            caseEntity[str(k)] = fieldEntity
    return caseEntity, containsCaseNote


# 重新排序json对象，确保case_note字段放在最后--蛋疼的需求。。
def resortJsonObj(jsonObject):
    key = -1
    for index in range(len(jsonObject)):
        if jsonObject[str(index)] == "case_note":
            key = index
            break
    if key != -1:
        jsonTmp = json.loads('{}')
        index = 0
        for p in range(len(jsonObject)):
            if p != key:
                jsonTmp[str(index)] = jsonObject[str(p)]
                index = index + 1
        jsonTmp[str(index)] = jsonObject[str(key)]
        return jsonTmp, True
    else:
        return jsonObject, False


def getTemplateFields(templateId):
    if templateId is None:
        logger.debug("模板id为空")
        return
    sql = "select field_json from case_template where id=" + str(templateId)
    logger.info("执行模板查询:" + sql)
    fields = mysql_pool.select_one(sql)
    return fields[0]


def write_to_excel_sheet(sheet, sheetName, excelEntity, sql, isCreateRow, language, operStatusDict, operStateDict):
    errorMsg = None
    global num
    try:
        # 创建表头
        rows = len(excelEntity)
        if isCreateRow:
            data = []
            for i in range(rows):
                sheet.column_dimensions[get_column_letter(i + 1)].width = 20
                data.append(excelEntity[str(i)]['name'])
            sheet.append(data)
        # 组装数据
        logger.info(f"执行{sheetName}查询语句:{sql}")
        resultRows = mysql_pool.select(sql)

        # 视频地址组装
        jsonRecord = json.loads('{}')
        roomIds = [-1]
        for i in range(len(resultRows)):
            roomIds.append(resultRows[i]["room_id"])
        videoRecordRows = mysql_pool.select_by_param(videoRecordSql, (roomIds,))
        for i in range(len(videoRecordRows)):
            if jsonRecord.get(videoRecordRows[i]["room_id"]) is None:
                jsonRecord[videoRecordRows[i]["room_id"]] = videoRecordRows[i]["video_url"]
            else:
                jsonRecord[videoRecordRows[i]["room_id"]] = jsonRecord[videoRecordRows[i]["room_id"]] + "," + videoRecordRows[i]["video_url"]

        if len(resultRows) == 0:
            num = num + 1
        for i in range(len(resultRows)):
            resultRow = resultRows[i]
            if not resultRow:
                if num == 1:
                    if sheetName == "案件信息":
                        errorMsg = "查询案件信息为空"
                        raise RuntimeError(errorMsg)
                    else:
                        sheet.append([sheetName + "数据为空"])
                        return
                else:
                    return
            data = []
            for j in range(rows):
                data.append("")
                typeVal = getTypeVal(resultRow, excelEntity[str(j)], language, operStatusDict, operStateDict)
                if typeVal is None:
                    if excelEntity[str(j)]["value"] == 'video_url':
                        typeVal = jsonRecord.get(resultRow["room_id"])
                        if typeVal is None:
                            continue
                    else:
                        continue
                typeVal = ILLEGAL_CHARACTERS_RE.sub(r'', typeVal)
                data[len(data) - 1] = typeVal
            num = num + 1
            sheet.append(data)
    except Exception:
        if errorMsg is None:
            errorMsg = "查询" + sheetName + "失败"
        raise RuntimeError(errorMsg)


def getEndTypeLanguageDict(language):
    key = "endTypeDict"
    if language is not None and language != "":
        key = key + "_" + language
    return endTypeLanguageDict[key]


def getTypeVal(dataResult, fieldEntry, languageType, operStatusDict, operationStateDict):
    if dataResult is None or fieldEntry is None:
        logger.debug("数据或者字段名称为空")
        return
    fieldValue = fieldEntry.get('value')
    fieldType = fieldEntry.get('type')
    if fieldValue is None:
        logger.debug("字段名称为空")
        return None
    typeVal = dataResult.get(fieldValue)
    if fieldValue == 'end_config_type':
        endTypeStr = ""
        endTypeDict = getEndTypeLanguageDict(languageType)
        if dataResult.get('end_type') and dataResult.get('end_type') in endTypeDict:
            endTypeStr = endTypeDict.get(dataResult.get('end_type'))
        if dataResult.get('attached_res'):
            endTypeStr = endTypeStr + "-" + dataResult.get('attached_res')
        typeVal = endTypeStr
    if fieldValue == 'reduction_after_amount':
        if dataResult.get('amount') and dataResult.get('reduction_amount'):
            typeVal = float(dataResult.get('amount')) - float(dataResult.get('reduction_amount'))
    if fieldValue == 'case_status':
        typeVal = taskUtil.getCaseStatus(dataResult.get('case_status'), languageType)
    if fieldValue == 'allot_status':
        typeVal = taskUtil.getCaseAllotStatus(dataResult.get('allot_status'))
    # 联系人状态
    if fieldValue == 'contact_status':
        return contact_status_dict.get(typeVal)
    # 人脸识别状态
    if fieldValue == 'auth_result':
        return authFaceDict.get(typeVal)
    # 通知状态
    if fieldValue == 'notice_type':
        return noticeTypeDict.get(typeVal)
    if typeVal is None:
        # json字段里面
        caseJson = json.loads(dataResult['field_json'])
        typeVal = caseJson.get(fieldValue)
        if typeVal is None:
            return typeVal
    if type(typeVal).__name__ == 'unicode':
        typeVal = typeVal.encode("utf-8")
    else:
        typeVal = str(typeVal)
    if fieldValue == 'out_serial_no':
        return typeVal[0:typeVal.rindex("#")]
    # 枚举转化
    if fieldValue == 'apply_status':
        return applyStatusDict.get(typeVal)
    if fieldValue == 'operation_state':
        return operationStateDict.get(typeVal)
    if fieldValue == 'oper_status':
        return operStatusDict.get(typeVal)
    # 时间转化
    if fieldValue in ['entrust_start_time', 'entrust_end_time', 'overdue_date', 'repayment_time', 'apply_time']:
        return base_utils.formatDate(typeVal)
    # 金额转化
    if fieldType and fieldType == 'Money':
        return str(float(typeVal) / 1000)
    if (fieldValue == 'case_name' or fieldValue == 'id_card' or fieldValue == 'own_mobile') and encryptSwitch:
        typeVal = encrypt_utils.decrypt(encryptMode, typeVal)
    return typeVal


if __name__ == '__main__':
    # 初始化心跳
    init_heartbeat("export_case_video_record", logger)
    while True:
        try:
            doTasks()
        except Exception as e:
            logger.exception(e)
        time.sleep(10)