# 手动触发同步指定范围内的case_log数据到es
# 脚本运行：python3 sync_case_log_touch.py start end
import json
from datasync.base_data_to_es import DataToEs
from datasync.datetime_to_json import DateEncoder
from concurrent.futures import ThreadPoolExecutor
import threading
import sys

from utils.logger_util import LOG

es_index = "bpw_case_log_query"
es_type = "_doc"

logger = LOG.get_logger("sync_bpw_case_log_touch")

sync_util = DataToEs()

# 需要转移的数据id，最大、最小值
transerDataMaxAndMinId = "SELECT max(cal.id) AS max,min(cal.id) AS min " \
                         "FROM `case_log` cal " \
                         "WHERE cal.id >= %d and cal.id <= %d"

# 查询转移sql
transerDataSqlMeta = "SELECT id,create_time,type,case_id,case_name,org_delt_name,org_delt_id,batch_id,product_name," \
                     "product_id,create_by,id_card,create_by_name,field_json,dunner_id,dunner_name,batch_no," \
                     "serial_no,out_serial_no,org_id,team_id,team_name,dep_id,task_id,own_mobile,out_batch_id," \
                     "out_batch_name,case_amount,case_json,repayment_amount,out_serial_temp " \
                     "FROM case_log WHERE id >= %d and id <= %d"


def main():
    start = int(sys.argv[1])
    end = int(sys.argv[2])
    if start is None or end is None or start > end:
        logger.info("启动参数异常")
        return

    min_max = sync_util.db.select_one(transerDataMaxAndMinId % (start, end))

    min_id = min_max["min"]
    max_id = min_max["max"]
    if min_id is None or max_id is None:
        logger.info("没有数据需要同步")
        return

    total = max_id - min_id + 1
    fetch_size = 10000
    parts = total // fetch_size
    if total % fetch_size != 0:
        parts = parts + 1

    thread_pool = ThreadPoolExecutor(max_workers=5, thread_name_prefix="sync_bpw_case_log_touch")
    futures = []
    for part in range(parts):
        part_start_id = min_id + (fetch_size * part)
        part_end_id = min_id + (fetch_size * (part + 1)) - 1
        if part_end_id > max_id:
            part_end_id = max_id

        future = thread_pool.submit(run, part_start_id, part_end_id)
        futures.append(future)
    for future in futures:
        try:
            future.result(60)
        except Exception as e:
            logger.exception(e)


def run(part_start_id, part_end_id):
    try:
        thread_name = threading.current_thread().name
        transer_datas = sync_util.db.select(transerDataSqlMeta % (part_start_id, part_end_id))
        if transer_datas:
            action_list = []
            for case_log in transer_datas:
                logger.info(f'{thread_name}同步数据 >>> {case_log}')
                action_list.append(build_common_source(case_log))

            if len(action_list) > 0:
                sync_util.es_bulk(action_list)

        # log.info("<<<<<<<<<<<<<<<<<<<<" + thread_name + " == sync_case_log_touch>>>>>>>>>>>>>>>>>>>>")
    except Exception as e:
        logger.exception(e)


def build_common_source(case_log):
    case_log_id = case_log["id"]
    source = {}

    if case_log_id is not None:
        source["id"] = case_log_id

    create_time = case_log["create_time"]
    if create_time is not None:
        source["createTime"] = create_time

    type = case_log["type"]
    if type is not None:
        source["type"] = type

    case_id = case_log["case_id"]
    if case_id is not None:
        source["caseId"] = case_id

    case_name = case_log["case_name"]
    if case_name is not None:
        source["caseName"] = case_name

    org_delt_name = case_log["org_delt_name"]
    if org_delt_name is not None:
        source["orgDeltName"] = org_delt_name

    org_delt_id = case_log["org_delt_id"]
    if org_delt_id is not None:
        source["orgDeltId"] = org_delt_id

    batch_id = case_log["batch_id"]
    if batch_id is not None:
        source["batchId"] = batch_id

    product_name = case_log["product_name"]
    if product_name is not None:
        source["productName"] = product_name

    product_id = case_log["product_id"]
    if product_id is not None:
        source["productId"] = product_id

    create_by = case_log["create_by"]
    if create_by is not None:
        source["createBy"] = create_by

    id_card = case_log["id_card"]
    if id_card is not None:
        source["idCard"] = id_card

    create_by_name = case_log["create_by_name"]
    if create_by_name is not None:
        source["createByName"] = create_by_name

    field_json = case_log["field_json"]
    if field_json is not None:
        source["fieldJson"] = field_json

    dunner_id = case_log["dunner_id"]
    if dunner_id is not None:
        source["dunnerId"] = dunner_id

    dunner_name = case_log["dunner_name"]
    if dunner_name is not None:
        source["dunnerName"] = dunner_name

    batch_no = case_log["batch_no"]
    if batch_no is not None:
        source["batchNo"] = batch_no

    serial_no = case_log["serial_no"]
    if serial_no is not None:
        source["serialNo"] = serial_no

    out_serial_no = case_log["out_serial_no"]
    if out_serial_no is not None:
        source["outSerialNo"] = out_serial_no
        if "#" in out_serial_no:
            index = out_serial_no.index("#")
            source["outSerialNoSearch"] = out_serial_no[0:index]
        else:
            source["outSerialNoSearch"] = out_serial_no

    org_id = case_log["org_id"]
    if org_id is not None:
        source["orgId"] = org_id

    team_id = case_log["team_id"]
    if team_id is not None:
        source["teamId"] = team_id

    team_name = case_log["team_name"]
    if team_name is not None:
        source["teamName"] = team_name

    dep_id = case_log["dep_id"]
    if dep_id is not None:
        source["depId"] = dep_id

    task_id = case_log["task_id"]
    if task_id is not None:
        source["taskId"] = task_id

    own_mobile = case_log["own_mobile"]
    if own_mobile is not None:
        source["ownMobile"] = own_mobile

    out_batch_id = case_log["out_batch_id"]
    if out_batch_id is not None:
        source["outBatchId"] = out_batch_id

    out_batch_name = case_log["out_batch_name"]
    if out_batch_name is not None:
        source["outBatchName"] = out_batch_name

    case_amount = case_log["case_amount"]
    if case_amount is not None:
        source["caseAmount"] = case_amount

    case_json = case_log["case_json"]
    if case_json is not None:
        source["caseJson"] = case_json
        source["caseJsonQuery"] = json.loads(case_json)

    repayment_amount = case_log["repayment_amount"]
    if repayment_amount is not None:
        source["repaymentAmount"] = repayment_amount

    out_serial_temp = case_log["out_serial_temp"]
    if out_serial_temp is not None:
        source["outSerialTemp"] = out_serial_temp

    source_json = json.dumps(source, cls=DateEncoder)

    return {
        "_index": es_index,
        "_type": es_type,
        "_id": case_log_id,
        "_source": source_json
    }


if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        logger.exception(e)
