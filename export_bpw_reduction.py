#!/usr/bin/python
import json
import os
import time
import urllib

import requests
from openpyxl import Workbook

from NacosHelper import NacosHelper
from utils import base_utils
from utils.base_utils import init_base_conf, init_heartbeat
from utils import encrypt_utils

config = NacosHelper().init_config()
logger, fileUtil, ossSwitch, esSwitch, encryptSwitch, encryptMode, bucket, mysql_pool, taskUtil, redis_pool = init_base_conf("export_bpw_reduction", config)

# 案件信息基本字段，涵盖了案件扩展信息表里的全部字段了
caseBaseSql = " ca.id,ca.own_mobile,delt.name as org_delt_name,p.name as product_name,ca.name as case_name,ca.id_card as id_card,\
            ca.entrust_start_time,ca.entrust_end_time,ca.amount,inbat.name as batch_no,outbat.name as out_batch_no,\
            u.user_no,ca.case_status,ca.allot_status,ca.operation_state,ca.overdue_date,ca.overdue_days,\
            dep_team1.name as dep_name,dep_team2.name as team_name,ca.call_status,ca.field_json,ca.oper_status,\
            ca.last_follow_time,cador.last_follow_time as debtor_last_follow_time,cador.follow_count as debtor_follow_count "
# 减免信息基本字段
reductionBaseSql = "u.name as case_operator_name,re.out_serial_no,re.reduce_amount reduction_amount,re.desc,re.apply_desc," \
                   "re.create_time apply_time,re.voucher_url,re.status apply_status,re.create_by as apply_name,re.update_by as approval_name"

# 基本关联
leftJoinReduceBaseSql = " left join `case_info` as ca on re.case_id = ca.id\
        left join `org_delt` delt on delt.id = re.org_delt_id\
        left join `product` p on p.id = ca.product_id\
        left join `inner_batch` inbat on ca.inner_batch_id = inbat.id\
        left join `out_batch` outbat on ca.out_batch_id = outbat.id\
        left join `org_dep_team` dep_team1 on ca.dep_id = dep_team1.id\
        left join `org_dep_team` dep_team2 on ca.team_id = dep_team2.id\
        left join user u on ca.user_id = u.id\
        left join case_debtor cador on cador.id=ca.debt_id "


def getTasks():
    # 查找任务
    tasks = taskUtil.getUndoTasks(6)
    # tasks = mysql_pool.select(f"select org_id,dep_id,team_id,data,id,type from download_task where id=19373")
    for task in tasks or []:
        doTask(task)


def doTask(task):
    orgId, data, taskId = task["org_id"], task["data"], task["id"]
    language = taskUtil.getOrgLanguage(orgId)
    all_fields = taskUtil.getLanguageFieldDict("caseInfoExtendsFields", language)
    logger.info(f"执行减免导出任务id:{taskId}")
    taskUtil.updateTaskIng(taskId)
    getResults(orgId, data, taskId, all_fields, language)


# 根据data里的数据查询出所有的还款信息
def getResults(orgId, data, taskId, allFields, language):
    if not data:
        raise RuntimeError('任务序列化数据为空')
    if not allFields:
        raise RuntimeError('导出字段不存在')
    if not orgId:
        raise RuntimeError('总公司id为空')
    fieldSql = "select " + caseBaseSql
    # 减免导出
    fieldSql = fieldSql + "," + reductionBaseSql
    # 拼接join的sql
    leftJoinSql = " from `apply_reduction` re " + leftJoinReduceBaseSql
    orderSql = " order by re.update_time desc"
    # 拼接查询条件的sql
    whereSql = createWhereSql(orgId, data)
    querySql = fieldSql + leftJoinSql + whereSql + orderSql
    logger.info("执行减免导出查询sql: " + querySql)
    results = mysql_pool.select(querySql)
    if not results:
        raise RuntimeError(f"减免导出任务id: {taskId}查询数据为空")
    dataJson = json.loads(data)
    templateId = dataJson.get('templateId', None)
    excelEntity = getExcelEntity(templateId, allFields, language)
    logger.info("减免导出生成的excelEntity字段" + str(excelEntity))
    listType = dataJson.get("reducListType")
    voucherFile = dataJson.get('voucherFile', 0)
    operationStateDict = taskUtil.getOperaStateDict(orgId)
    operStatusDict = taskUtil.getOperStatusDict(orgId)
    withVoucher = voucherFile == 1
    language = taskUtil.getOrgLanguage(orgId)
    export_excel(excelEntity, results, taskId, listType, withVoucher, operationStateDict, operStatusDict, language)


def export_excel(excelEntity, results, taskId, listType, withVoucher, operationStateDict, operStatusDict, language):
    # 申请人姓名
    applyNameKeys = getKeySets(results, "apply_name")
    applyNameDicts = getUserDicts(applyNameKeys)
    # 批准人姓名
    approvalNameKeys = getKeySets(results, "approval_name")
    approvalNameDicts = getUserDicts(approvalNameKeys)
    wbk = Workbook()
    sheet = wbk.create_sheet("减免信息", index=0)
    # 创建表头
    columns = len(excelEntity)
    # 组装所有的列名
    for i in range(columns):
        titleCell = sheet.cell(row=1, column=i + 1)  # row和column都需要从1开始
        titleCell.value = excelEntity[str(i)]['name']
    # 组装数据
    for i in range(2, len(results) + 2):
        for j in range(columns):
            typeVal = getTypeVal(results[i - 2], excelEntity[str(j)], applyNameDicts, approvalNameDicts, 1, operationStateDict, operStatusDict, language)
            if typeVal is None:
                continue
            sheet.cell(row=i, column=j + 1).value = typeVal
    downloadurl, dataNums = save_file(wbk, taskId, listType, results, withVoucher)
    taskUtil.updateSuccess(taskId, downloadurl, dataNums)


def save_file(wbk, taskId, listType, results, withVoucher):
    if listType == 0:
        fileName = f"减免申请导出_{taskId}.xlsx"
    else:
        fileName = f"减免记录导出_{taskId}.xlsx"
    # 生成临时文件目录，存放减免数据execl和凭证文件
    tmpFilePath = f"./reduction_file_export_{taskId}/"
    os.makedirs(tmpFilePath, exist_ok=True)
    zipName = f"减免导出_{taskId}.zip"
    # 减免excel数据文件保存在临时目录下面
    excelFileName = tmpFilePath + fileName
    wbk.save(excelFileName)
    if withVoucher:
        voucherFilePath = tmpFilePath + "凭证/"
        os.makedirs(name=voucherFilePath, exist_ok=True)
        # 遍历导出数据，有凭证文件的下载
        for row in results:
            voucher_url = row["voucher_url"]
            if voucher_url:
                urlFile = requests.get(voucher_url)
                if urlFile.status_code == 200:
                    voucher_file_name = getVoucherFileName(voucher_url)
                    voucher_file_name = voucherFilePath + voucher_file_name
                    open(voucher_file_name, 'wb').write(urlFile.content)
        logger.info(f"任务taskId:{taskId},下载凭证文件结束")
    fileUtil.zip(zipName, tmpFilePath, rate=1)
    fileUtil.delete(tmpFilePath)
    downloadurl, fileSize = base_utils.upload_file(ossSwitch, bucket, fileUtil, zipName)
    logger.info("下载地址:" + downloadurl)
    dataNums = len(results)
    return downloadurl, dataNums


def getVoucherFileName(voucher_url):
    if voucher_url is None:
        return None
    url = urllib.parse.unquote(voucher_url)
    # 提取凭证文件名
    sub_url = url[0: url.rfind(".zip")]
    return url[sub_url.rfind("/") + 1: url.rfind(".zip") + 4]


def getTypeVal(result, fieldEntry, applyNameDicts, approvalNameDicts, voucherFile, operationStateDict, operStatusDict, language):
    # 减免审核结果字典
    applyStatusDict = {'0': '申请', '1': '审核通过', '2': '审核失败'}
    if result is None or fieldEntry is None:
        logger.debug("数据或者字段名称为空")
        return
    engFieldValue = fieldEntry.get('value')  # 英文key
    fieldType = fieldEntry.get('type')
    if engFieldValue is None:
        logger.debug("字段名称为空")
        return None
    realVal = result.get(engFieldValue)
    if engFieldValue == 'voucher_url' and voucherFile == 1:
        return getVoucherFileName(realVal)
    if engFieldValue == 'reduction_after_amount':
        if result.get('amount') and result.get('reduction_amount'):
            realVal = float(result.get('amount')) - float(result.get('reduction_amount'))
    if engFieldValue == 'case_status':
        realVal = taskUtil.getCaseStatus(result.get('case_status'), language)
    if engFieldValue == 'allot_status':
        realVal = taskUtil.getCaseAllotStatus(result.get('allot_status'))
    if realVal is None:
        # json字段里面
        # 查询相关案件字段值为空，并且自定义字段集合field_json为空，说明此案件已被物理删除
        if result['field_json'] is None:
            return None
        caseJson = json.loads(result['field_json'])
        realVal = caseJson.get(engFieldValue)
        if realVal is None:
            return realVal
    if engFieldValue == 'apply_name':
        return applyNameDicts.get(realVal)
    if engFieldValue == 'approval_name':
        return approvalNameDicts.get(realVal)
    # 如果编码是unicode，那么转成utf8形式
    if type(realVal).__name__ == 'unicode':
        realVal = realVal.encode("utf-8")
    else:
        realVal = str(realVal)
    if engFieldValue == 'out_serial_no':
        return realVal[0:realVal.rindex("#")]
        # 枚举转化
    if engFieldValue == 'apply_status':
        return applyStatusDict.get(realVal)
    if engFieldValue == 'operation_state':
        return operationStateDict.get(realVal)
    if engFieldValue == 'oper_status':
        return operStatusDict.get(realVal)
        # 时间转化
    if (engFieldValue == 'entrust_start_time' or
            engFieldValue == 'entrust_end_time' or
            engFieldValue == 'repayment_time'):
        return base_utils.formatDate(realVal)
    # 金额转化
    if fieldType and fieldType == 'Money':
        return str(float(realVal) / 1000)
    if (engFieldValue == 'case_name' or engFieldValue == 'id_card' or engFieldValue == 'own_mobile') and encryptSwitch:
        realVal = encrypt_utils.decrypt(encryptMode, realVal)
    return realVal


def getExcelEntity(templateId, allFields, language):
    tplFields = taskUtil.getTemplateFields(templateId)
    allFieldJson = json.loads(allFields)
    tplFieldJson = json.loads(tplFields)
    # 组装模板所有字段
    excelEntity = {}
    for i in range(len(tplFieldJson)):
        engFieldValue = tplFieldJson[str(i)]  # 拿到英文key
        for field in allFieldJson:
            if engFieldValue == field['value']:
                excelEntity[str(i)] = field
                break
        else:
            # 自定义字段里找
            sql = "select name from custom_field cuf where cuf.value = '" + engFieldValue + "'"
            logger.info("查询自定义字段:" + sql)
            cnFieldName = mysql_pool.select_one(sql)
            if cnFieldName is None:
                # 需要到自定义搜索标签找
                sql = "select name from custom_search_field cef where cef.search_key='" + str(engFieldValue) + "'"
                logger.info("查询自定义搜索字段:" + sql)
                cnFieldName = mysql_pool.select_one(sql)
            fieldEntity = {'name': cnFieldName["name"], 'value': engFieldValue}
            excelEntity[str(i)] = fieldEntity
    # 开始拼接还款或者减免字段
    entityLen = len(excelEntity)
    for i in range(len(getReductionEntityLanguage(language))):
        excelEntity[str(entityLen + i)] = getReductionEntityLanguage(language)[str(i)]
    return excelEntity


def getReductionEntityLanguage(language):
    # 减免信息模板
    reductionEntityLanguage = {
        "reductionEntity": {'0': {'value': 'case_operator_name', 'name': '催员'},
                            '1': {'value': 'apply_name', 'name': '申请人'},
                            '2': {'value': 'approval_name', 'name': '批准人'},
                            '3': {'value': 'reduction_amount', 'name': '减免金额', 'type': 'Money'},
                            '4': {'value': 'reduction_after_amount', 'name': '减免后金额', 'type': 'Money'},
                            '5': {'value': 'apply_time', 'name': '申请时间'},
                            '6': {'value': 'apply_desc', 'name': '申请备注'},
                            '7': {'value': 'apply_status', 'name': '申请结果'},
                            '8': {'value': 'desc', 'name': '申请说明'},
                            '9': {'value': 'voucher_url', 'name': '减免凭证文件名'}},
        "reductionEntity_tj": {'0': {'value': 'case_operator_name', 'name': '调解员'},
                               '1': {'value': 'apply_name', 'name': '申请人'},
                               '2': {'value': 'approval_name', 'name': '批准人'},
                               '3': {'value': 'reduction_amount', 'name': '减免金额', 'type': 'Money'},
                               '4': {'value': 'reduction_after_amount', 'name': '减免后金额', 'type': 'Money'},
                               '5': {'value': 'apply_time', 'name': '申请时间'},
                               '6': {'value': 'apply_desc', 'name': '申请备注'},
                               '7': {'value': 'apply_status', 'name': '申请结果'},
                               '8': {'value': 'desc', 'name': '申请说明'},
                               '9': {'value': 'voucher_url', 'name': '减免凭证文件名'}}
    }
    key = "reductionEntity"
    if language:
        key = key + "_" + language
    return reductionEntityLanguage[key]


def createWhereSql(orgId, data):
    queryJson = json.loads(data)
    whereSql = " where re.org_id = " + str(orgId)
    names = queryJson.get("names", None)  # 数组
    outSerialNos = queryJson.get("outSerialNos", None)  # 数组
    orgDeltIds = queryJson.get("orgDeltIds", None)
    productIds = queryJson.get("productIds", None)
    innerBatchIds = queryJson.get("innerBatchIds", None)
    statuses = queryJson.get("statuses", None)
    applyTimeRange = queryJson.get("applyTimeRange", None)
    # 分公司和小组
    depId = queryJson.get("depId", None)
    teamId = queryJson.get("teamId", None)
    if depId:
        whereSql = whereSql + " and ca.dep_id = " + str(depId)
    if teamId:
        whereSql = whereSql + " and ca.team_id = " + str(teamId)
    if names:
        whereSql = whereSql + " and ca.name in ("
        for name in names:
            whereSql = whereSql + "'" + name + "',"
        whereSql = whereSql + "'')"
    if outSerialNos:
        if len(outSerialNos) > 1:
            whereSql = whereSql + " and re.out_serial_temp in ("
            for outSerialNo in outSerialNos:
                whereSql = whereSql + "'" + outSerialNo + "',"
            whereSql = whereSql + "'')"
        else:
            whereSql = whereSql + " and re.out_serial_temp like '" + outSerialNos[0] + "%'"
    if orgDeltIds:
        whereSql = whereSql + " and re.org_delt_id in (" + orgDeltIds + ") "
    if productIds:
        whereSql = whereSql + " and ca.product_id in (" + productIds + ") "
    if innerBatchIds:
        whereSql = whereSql + " and ca.inner_batch_id in (" + innerBatchIds + ") "
    if statuses:
        whereSql = whereSql + " and re.status in (" + statuses + ") "
    if applyTimeRange:
        applyStart = applyTimeRange.split(",")[0]
        applyEnd = applyTimeRange.split(",")[1]
        base_utils.formatDateTime(int(applyStart))
        whereSql = whereSql + f" and re.create_time >= '{base_utils.formatDateTime(int(applyStart))}' and re.create_time <= '{base_utils.formatDateTime(int(applyEnd))}'"
    return whereSql


def getKeySets(results, keyName):
    keySets = set()
    for data in results:
        key = data.get(keyName)
        if key:
            keySets.add(key)
    return keySets


def getUserDicts(userIds):
    userDicts = {}
    if not userIds:
        return userDicts
    sql = "select id,name from user where id in %s"
    results = mysql_pool.select_by_param(sql, [userIds])
    for data in results:
        userDicts[str(data["id"])] = data["name"]
    return userDicts


if __name__ == '__main__':
    # 初始化心跳
    init_heartbeat("export_reduction", logger)
    while True:
        try:
            getTasks()
        except Exception as e:
            logger.exception(e)
        # 休息10秒
        time.sleep(10)
