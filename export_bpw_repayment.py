#!/usr/bin/python
import json
import os
import time
import urllib

import requests
from elasticsearch import Elasticsearch
from openpyxl import Workbook

from NacosHelper import <PERSON>cosHelper
from es_query_builder import *
from utils import base_utils
from utils.base_utils import init_base_conf, init_heartbeat
from utils import encrypt_utils

config = NacosHelper().init_config()
logger, fileUtil, ossSwitch, esSwitch, encryptSwitch, encryptMode, bucket, mysql_pool, taskUtil, redis_pool = init_base_conf("export_bpw_repayment", config)

# 案件信息基本字段，涵盖了案件扩展信息表里的全部字段了
caseBaseSql = " ca.id,ca.own_mobile,delt.name as org_delt_name,p.name as product_name,ca.name as case_name,ca.id_card as id_card,\
            ca.entrust_start_time,ca.entrust_end_time,ca.amount,inbat.name as batch_no,outbat.name as out_batch_no,\
            u.user_no,ca.case_status,ca.allot_status,ca.operation_state,ca.overdue_date,ca.overdue_days,\
            dep_team1.name as dep_name,dep_team2.name as team_name,ca.call_status,ca.field_json,ca.oper_status,\
            ca.last_follow_time,cador.last_follow_time as debtor_last_follow_time,cador.follow_count as debtor_follow_count "

# 还款信息基本字段
repaymentBaseSql = "rep.out_serial_no, rep.case_operator_name,rep.repayment_time,rep.repayment_amount,rep.repayment_type,rep.repayment_style,rep.repayment_card_no,rep.repayment_mobile," \
                   "rep.desc,rep.apply_desc,rep.voucher_url,rep.repayment_from,rep.create_by apply_name,rep.create_time apply_time,rep.apply_status"
# 基本关联
leftJoinRepBaseSql = " left join `case_info` as ca on rep.case_id = ca.id\
        left join `org_delt` delt on delt.id = rep.org_delt_id\
        left join `product` p on p.id = ca.product_id\
        left join `inner_batch` inbat on ca.inner_batch_id = inbat.id\
        left join `out_batch` outbat on ca.out_batch_id = outbat.id\
        left join `org_dep_team` dep_team1 on ca.dep_id = dep_team1.id\
        left join `org_dep_team` dep_team2 on ca.team_id = dep_team2.id\
        left join user u on ca.user_id = u.id\
        left join case_debtor cador on cador.id=ca.debt_id "

# 还款信息模板
repaymentEntityLanguage = {
    "repaymentEntity": {'0': {'value': 'case_operator_name', 'name': '催员'},
                        '1': {'value': 'repayment_time', 'name': '还款时间'},
                        '2': {'value': 'repayment_from', 'name': '还款人'},
                        '3': {'value': 'repayment_amount', 'name': '还款金额', 'type': 'Money'},
                        '4': {'value': 'repayment_type', 'name': '还款类型'},
                        '5': {'value': 'repayment_style', 'name': '还款方式'},
                        '6': {'value': 'apply_name', 'name': '申请人'},
                        '7': {'value': 'apply_time', 'name': '申请时间'},
                        '8': {'value': 'desc', 'name': '申请说明'},
                        '9': {'value': 'apply_desc', 'name': '审核意见'},
                        '10': {'value': 'voucher_url', 'name': '还款凭证文件名'},
                        '11': {'value': 'repayment_card_no', 'name': '还款卡号'},
                        '12': {'value': 'repayment_mobile', 'name': '还款手机号'}},
    "repaymentEntity_tj": {'0': {'value': 'case_operator_name', 'name': '调解员'},
                           '1': {'value': 'repayment_time', 'name': '还款时间'},
                           '2': {'value': 'repayment_from', 'name': '还款人'},
                           '3': {'value': 'repayment_amount', 'name': '还款金额', 'type': 'Money'},
                           '4': {'value': 'repayment_type', 'name': '还款类型'},
                           '5': {'value': 'repayment_style', 'name': '还款方式'},
                           '6': {'value': 'apply_name', 'name': '申请人'},
                           '7': {'value': 'apply_time', 'name': '申请时间'},
                           '8': {'value': 'desc', 'name': '申请说明'},
                           '9': {'value': 'apply_desc', 'name': '审核意见'},
                           '10': {'value': 'voucher_url', 'name': '还款凭证文件名'},
                           '11': {'value': 'repayment_card_no', 'name': '还款卡号'},
                           '12': {'value': 'repayment_mobile', 'name': '还款手机号'}}
}

pageSize = 500


def getTasks():
    # 查找任务
    tasks = taskUtil.getUndoTasks(5)
    # tasks = mysql_pool.select(f"select org_id,dep_id,team_id,data,id,type from download_task where id=19376")
    for task in tasks or []:
        doTask(task)


def doTask(task):
    orgId, data, taskId = task["org_id"], task["data"], task["id"]
    language = taskUtil.getOrgLanguage(orgId)
    all_fields = taskUtil.getLanguageFieldDict("caseInfoExtendsFields", language)
    logger.info(f"执行还款导出任务id:{taskId}")
    # 任务处理中
    taskUtil.updateTaskIng(taskId)
    getResults(orgId, data, taskId, all_fields, language)


# 根据data里的数据查询出所有的还款信息
def getResults(orgId, data, taskId, allFields, language):
    if not data:
        raise RuntimeError('任务序列化数据为空')
    if not allFields:
        raise RuntimeError('导出字段不存在')
    if not orgId:
        raise RuntimeError('总公司id为空')
    queryJson = json.loads(data)
    total = {"value": 1}
    if esSwitch:
        repIdsList = get_rep_ids_list_from_es(orgId, queryJson, total)
    else:
        repIdsList = get_rep_ids_list_from_mysql(orgId, queryJson, total)
    dataJson = json.loads(data)
    templateId = dataJson.get('templateId', None)
    excelEntity = getExcelEntity(templateId, allFields, language, orgId)
    logger.info("还款导出生成的excelEntity字段: " + str(excelEntity))
    # 导出数据
    listType = dataJson.get("repListType")
    voucherFile = dataJson.get('voucherFile', 0)
    operationStateDict = taskUtil.getOperaStateDict(orgId)
    operStatusDict = taskUtil.getOperStatusDict(orgId)
    withVoucher = voucherFile == 1
    language = taskUtil.getOrgLanguage(orgId)
    export_excel(orgId, excelEntity, repIdsList, taskId, listType, withVoucher, operationStateDict, operStatusDict, total, language)


def get_rep_ids_list_from_es(orgId, queryJson, total):
    # 还款导出
    applyStatus = queryJson.get("applyStatus", None)
    repListType = queryJson.get("repListType", None)
    idCards = queryJson.get("idCards", None)  # 数组
    ownMobiles = queryJson.get("ownMobiles", None)
    allotAgent = queryJson.get("allotAgent", None)
    # 债务人姓名
    names = queryJson.get("names", None)  # 数组
    caseOperators = queryJson.get("caseOperators", None)
    orgDeltIds = queryJson.get("orgDeltIds", None)
    repaymentTime = queryJson.get("repaymentTime", None)
    applyTimeRange = queryJson.get("applyTimeRange", None)
    # 委案编号
    outSerialNos = queryJson.get("outSerialNos", None)  # 数组
    # 分公司和小组
    depId = queryJson.get("depId", None)
    teamId = queryJson.get("teamId", None)

    teamIds = queryJson.get("teamIds", None)
    depIds = queryJson.get("depIds", None)
    innerBatchIds = queryJson.get("innerBatchIds", None)
    outBatchIds = queryJson.get("outBatchIds", None)

    _must = [TermQuery("status", 0), TermQuery("orgId", orgId)]

    if applyStatus is not None and len(applyStatus.split(",")) > 0:
        if repListType == 1:
            _must.append(TermQuery("type", 0))
        if repListType == 3:
            _must.append(TermQuery("type", 1))
        _must.append(TermsQuery("applyStatus", applyStatus.split(",")))
    else:
        if repListType == 0:
            _must.append(TermsQuery("applyStatus", [1, 2]))
        if repListType == 1:
            _must.append(TermQuery("type", 0))
            _must.append(TermsQuery("applyStatus", [-1, 0, 1]))
        if repListType == 2:
            _must.append(BoolQuery([], [], [TermQuery("type", 1),
                                            BoolQuery([TermQuery("type", 1), TermQuery("applyStatus", 1)], [], [])]))
        if repListType == 3:
            _must.append(TermQuery("type", 1))
            _must.append(TermsQuery("applyStatus", [-1, 0, 1]))
    if depId is not None:
        _must.append(TermQuery("depId", depId))
    if teamId is not None:
        _must.append(TermQuery("teamId", teamId))
    if idCards is not None:
        _must.append(TermsQuery("debtIdCard", idCards))
    if names is not None:
        _must.append(TermsQuery("debtName", names))
    if ownMobiles is not None:
        _must.append(TermsQuery("ownMobile.keyword", ownMobiles))
    if allotAgent is not None:
        _must.append(TermQuery("allotAgent", allotAgent))
    if outSerialNos is not None:
        if len(outSerialNos) > 1:
            _must.append(TermsQuery("outSerialTemp", outSerialNos))
        else:
            _must.append(WildcardQuery("outSerialTemp", outSerialNos[0] + "*"))
    if caseOperators is not None:
        _must.append(TermsQuery("caseOperator", caseOperators.split(',')))
    if teamIds is not None:
        _must.append(TermsQuery("teamId", teamIds.split(',')))
    if depIds is not None:
        _must.append(TermsQuery("depId", depIds.split(',')))
    if innerBatchIds is not None:
        _must.append(TermsQuery("innerBatchId", innerBatchIds.split(',')))
    if outBatchIds is not None:
        _must.append(TermsQuery("outBatchId", outBatchIds.split(',')))
    if orgDeltIds is not None:
        _must.append(TermsQuery("orgDeltId", orgDeltIds.split(',')))
    if repaymentTime is not None:
        start = repaymentTime.split(",")[0]
        end = repaymentTime.split(",")[1]
        _must.append(RangeQuery(key="repaymentTime",
                                _from=str(base_utils.formatDateTime(int(start))),
                                to=str(base_utils.formatDateTime(int(end))),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=True,
                                include_upper=True))
    if applyTimeRange is not None:
        applyStart = applyTimeRange.split(",")[0]
        applyEnd = applyTimeRange.split(",")[1]
        _must.append(RangeQuery(key="createTime",
                                _from=str(base_utils.formatDateTime(int(applyStart))),
                                to=str(base_utils.formatDateTime(int(applyEnd))),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=True,
                                include_upper=True))
    _sortList = [Sort(key="updateTime", order="desc")]
    _bool = BoolQuery(must_list=_must, must_not_list=[], should_list=[])
    _search = Search(query=_bool, order_list=_sortList, limit=pageSize)
    es = Elasticsearch([{'host': config["anmi_es"]["host"], 'port': config["anmi_es"]["port"]}], timeout=3600,
                       http_auth=(config["anmi_es"]["user"], config["anmi_es"]["password"]))
    logger.info("开始es搜索" + json.JSONEncoder().encode(o=_search.toMap()))
    result = es.search(index="bpw_case_repayment_query", body=_search.toMap(), _source_includes=["_id"], scroll="5m")
    total["value"] = result["hits"]["total"]["value"]
    scroll_id = result["_scroll_id"]
    while len(result["hits"]["hits"]) != 0:
        case_rep_ids = []
        for data in result["hits"]["hits"]:
            case_rep_ids.append(int(data["_id"]))
        yield case_rep_ids
        result = es.scroll(scroll_id=scroll_id, scroll='5m')
        if result is None or result["timed_out"]:
            raise RuntimeError("查询错误")


def get_rep_ids_list_from_mysql(orgId, queryJson, total):
    orderSql = " order by rep.update_time desc"
    whereSql = f" where rep.status = 0 and rep.org_id = {orgId}"
    applyStatus = queryJson.get("applyStatus", None)
    repListType = queryJson.get("repListType", None)
    idCards = queryJson.get("idCards", None)  # 数组
    # 债务人姓名
    names = queryJson.get("names", None)  # 数组
    ownMobiles = queryJson.get("ownMobiles", None)
    caseOperators = queryJson.get("caseOperators", None)
    orgDeltIds = queryJson.get("orgDeltIds", None)
    repaymentTime = queryJson.get("repaymentTime", None)
    applyTimeRange = queryJson.get("applyTimeRange", None)
    # 委案编号
    outSerialNos = queryJson.get("outSerialNos", None)  # 数组
    # 分公司和小组
    depId = queryJson.get("depId", None)
    teamId = queryJson.get("teamId", None)
    if applyStatus:
        if repListType == 1:
            whereSql = whereSql + " and rep.type = 0 "
        if repListType == 3:
            whereSql = whereSql + " and rep.type = 1 "
        whereSql = whereSql + " and rep.apply_status in (" + str(applyStatus) + ") "
    else:
        if repListType == 0:
            whereSql = whereSql + " and rep.apply_status in (1,2) "
        if repListType == 1:
            whereSql = whereSql + " and rep.type = 0 and rep.apply_status in (-1,0,1) "
        if repListType == 2:
            whereSql = whereSql + " and (rep.type = 0 or (rep.type = 1 and rep.apply_status = 1)) "
        if repListType == 3:
            whereSql = whereSql + " and rep.type = 1 and rep.apply_status in (-1,0,1) "
    if depId:
        whereSql = whereSql + " and ca.dep_id = " + str(depId)
    if teamId:
        whereSql = whereSql + " and ca.team_id = " + str(teamId)
    if idCards:
        whereSql = whereSql + " and rep.debt_id_card in ("
        for ic in idCards:
            whereSql = whereSql + "'" + ic + "',"
        whereSql = whereSql + "'')"
    if names:
        whereSql = whereSql + " and rep.debt_name in ("
        for name in names:
            whereSql = whereSql + "'" + name + "',"
        whereSql = whereSql + "'')"
    if ownMobiles:
        whereSql = whereSql + " and ca.own_mobile in ("
        for ownMobile in ownMobiles:
            whereSql = whereSql + "'" + ownMobile + "',"
        whereSql = whereSql + "'')"
    if outSerialNos:
        if len(outSerialNos) > 1:
            whereSql = whereSql + " and rep.out_serial_temp in ("
            for outSerialNo in outSerialNos:
                whereSql = whereSql + "'" + outSerialNo + "',"
            whereSql = whereSql + "'')"
        else:
            whereSql = whereSql + " and rep.out_serial_temp like '" + outSerialNos[0] + "%'"
    if caseOperators:
        whereSql = whereSql + " and rep.case_operator in (" + caseOperators + ") "
    if orgDeltIds:
        whereSql = whereSql + " and rep.org_delt_id in (" + orgDeltIds + ") "
    if repaymentTime:
        start = repaymentTime.split(",")[0]
        end = repaymentTime.split(",")[1]
        whereSql = whereSql + " and rep.repayment_time >= '" + str(
            base_utils.formatDateTime(int(start))) + "' and rep.repayment_time <= '" + str(base_utils.formatDateTime(int(end))) + "'"
    if applyTimeRange:
        applyStart = applyTimeRange.split(",")[0]
        applyEnd = applyTimeRange.split(",")[1]
        whereSql = whereSql + " and rep.create_time >= '" + str(
            base_utils.formatDateTime(int(applyStart))) + "' and rep.create_time <= '" + str(base_utils.formatDateTime(int(applyEnd))) + "'"
    countSql = "select count(*) as cnt from `case_repayment` rep left join `case_info` as ca on rep.case_id = ca.id " + whereSql
    querySql = "select rep.id as id    from `case_repayment` rep left join `case_info` as ca on rep.case_id = ca.id " + whereSql + orderSql
    count = mysql_pool.select_one(countSql)["cnt"]
    total["value"] = count
    totalPage = int(count / pageSize) if count % pageSize == 0 else int(count / pageSize + 1)
    for page in range(0, totalPage):
        rep_ids = []
        limit = page * pageSize
        limitSql = querySql + f" limit {limit},{pageSize}"
        logger.info("执行还款导出查询sql: " + limitSql)
        results = mysql_pool.select(limitSql)
        if not results:
            break
        for row in results:
            rep_ids.append(int(row["id"]))
        yield rep_ids


def export_excel(orgId, excelEntity, repIdsList, taskId, listType, withVoucher, operationStateDict, operStatusDict, total, language):
    userDicts = getUserDicts(orgId)

    fieldSql = "select " + caseBaseSql + "," + repaymentBaseSql
    # 拼接join的sql
    leftJoinSql = " from `case_repayment` rep " + leftJoinRepBaseSql
    orderSql = " order by rep.update_time desc"
    whereSql = "where rep.id in %s "
    querySql = fieldSql + leftJoinSql + whereSql + orderSql
    logger.info("执行还款导出查询sql: " + querySql)
    # 实例化一个Workbook()对象(即excel文件)
    wbk = Workbook()
    sheet = wbk.create_sheet('还款信息', index=0)
    # 创建表头
    columns = len(excelEntity)
    # 组装所有的列名
    for i in range(columns):
        titleCell = sheet.cell(row=1, column=i + 1)  # row和column都需要从1开始
        titleCell.value = excelEntity[str(i)]['name']
    tmpFilePath = f"./repayment_file_export_{taskId}/"
    os.makedirs(name=tmpFilePath, exist_ok=True)
    voucherFilePath = tmpFilePath
    if withVoucher:
        voucherFilePath = tmpFilePath + "凭证/"
        os.makedirs(name=voucherFilePath, exist_ok=True)
    count = 0
    # 组装数据
    for repIds in repIdsList:
        results = mysql_pool.select_by_param(querySql, [repIds])
        for i in range(2, len(results) + 2):
            voucher_url = results[i - 2]["voucher_url"]
            if voucher_url and withVoucher:
                voucher_file_name = getVoucherFileName(voucher_url)
                voucher_file_name = voucherFilePath + voucher_file_name
                if ossSwitch:
                    base_utils.downloadFile(voucher_file_name, voucher_url, logger)
                else:
                    fileUtil.exec(f"cp '/usr/local/duyansoft{voucher_url}' '" + voucher_file_name + "'")
            for j in range(columns):
                if results[i - 2].get('apply_status') == 2 and (excelEntity[str(j)].get('value') == 'apply_name' or excelEntity[str(j)].get('value') == 'apply_time'):
                    continue
                typeVal = getTypeVal(results[i - 2], excelEntity[str(j)], userDicts, 1, operationStateDict, operStatusDict, language)
                if typeVal is None:
                    continue
                sheet.cell(row=count + 2, column=j + 1).value = typeVal
            count += 1
        progress = count / total["value"] * 100
        taskUtil.updateProgress(taskId, progress)
    downloadurl = save_file(wbk, taskId, listType, withVoucher)
    taskUtil.updateSuccess(taskId, downloadurl, count)


def save_file(wbk, taskId, listType, withVoucher):
    if listType == 0:
        excelName = f"还款记录导出_{taskId}.xlsx"
    elif listType == 1:
        excelName = f"还款申请导出_{taskId}.xlsx"
    else:
        excelName = f"划扣申请导出_{taskId}.xlsx"
    fileName = f"还款数据导出_{taskId}.xlsx"
    if not withVoucher:
        wbk.save(fileName)
    else:
        # 生成临时文件目录，存放减免数据execl和凭证文件
        tmpFilePath = f"./repayment_file_export_{taskId}/"
        os.makedirs(tmpFilePath, exist_ok=True)
        fileName = f"还款导出_{taskId}.zip"
        # 减免excel数据文件保存在临时目录下面
        excelFileName = tmpFilePath + excelName
        wbk.save(excelFileName)
        fileUtil.zip(fileName, tmpFilePath, rate=1)
        fileUtil.delete(tmpFilePath)
    downloadurl, fileSize = base_utils.upload_file(ossSwitch, bucket, fileUtil, fileName)
    logger.info(f"下载地址:{downloadurl}")
    return downloadurl


def getVoucherFileName(voucher_url):
    if voucher_url is None:
        return None
    url = urllib.parse.unquote(voucher_url)
    # 提取凭证文件名
    sub_url = url[0: url.rfind(".zip")]
    return url[sub_url.rfind("/") + 1: url.rfind(".zip") + 4]


def getTypeVal(result, fieldEntry, userDicts, voucherFile, operationStateDict, operStatusDict, language):
    applyStatusDict = {'0': '申请', '1': '审核通过', '2': '审核失败'}
    if result is None or fieldEntry is None:
        logger.debug("数据或者字段名称为空")
        return
    engFieldValue = fieldEntry.get('value')  # 英文key
    fieldType = fieldEntry.get('type')
    if engFieldValue is None:
        logger.debug("字段名称为空")
        return None
    realVal = result.get(engFieldValue)
    if engFieldValue == 'voucher_url' and voucherFile == 1:
        return getVoucherFileName(realVal)
    if engFieldValue == 'reduction_after_amount':
        if result.get('amount') and result.get('reduction_amount'):
            realVal = float(result.get('amount')) - float(result.get('reduction_amount'))
    if engFieldValue == 'case_status':
        realVal = taskUtil.getCaseStatus(result.get('case_status'), language)
    if engFieldValue == 'allot_status':
        realVal = taskUtil.getCaseAllotStatus(result.get('allot_status'))
    if realVal is None:
        # json字段里面
        # 查询相关案件字段值为空，并且自定义字段集合field_json为空，说明此案件已被物理删除
        if result['field_json'] is None:
            return None
        caseJson = json.loads(result['field_json'])
        realVal = caseJson.get(engFieldValue)
        if realVal is None:
            return realVal
    if engFieldValue == 'apply_name':
        return userDicts.get(realVal)
    if engFieldValue == 'approval_name':
        return userDicts.get(realVal)
    # 如果编码是unicode，那么转成utf8形式
    if type(realVal).__name__ == 'unicode':
        realVal = realVal.encode("utf-8")
    else:
        realVal = str(realVal)
    if engFieldValue == 'out_serial_no':
        return realVal[0:realVal.rindex("#")]
        # 枚举转化
    if engFieldValue == 'apply_status':
        return applyStatusDict.get(realVal)
    if engFieldValue == 'operation_state':
        return operationStateDict.get(realVal)
    if engFieldValue == 'oper_status':
        return operStatusDict.get(realVal)
        # 时间转化
    if (engFieldValue == 'entrust_start_time' or
            engFieldValue == 'entrust_end_time' or
            engFieldValue == 'repayment_time'):
        return base_utils.formatDate(realVal)
    # 金额转化
    if fieldType and fieldType == 'Money':
        return str(float(realVal) / 1000)
    if (engFieldValue == 'case_name' or engFieldValue == 'id_card' or engFieldValue == 'own_mobile' or
    engFieldValue == 'repayment_mobile') and encryptSwitch:
        realVal = encrypt_utils.decrypt(encryptMode, realVal)
    return realVal


def getExcelEntity(templateId, allFields, language, orgId):
    tplFields = taskUtil.getTemplateFields(templateId)
    allFieldJson = json.loads(allFields)
    tplFieldJson = json.loads(tplFields)
    # 组装模板所有字段
    excelEntity = {}
    for i in range(len(tplFieldJson)):
        engFieldValue = tplFieldJson[str(i)]  # 拿到英文key
        for field in allFieldJson:
            if engFieldValue == field['value']:
                excelEntity[str(i)] = field
                break
        else:
            # 自定义字段里找
            sql = "select name from custom_field cuf where cuf.value = '" + engFieldValue + "'"
            logger.info("查询自定义字段:" + sql)
            cnFieldName = mysql_pool.select_one(sql)
            if cnFieldName is None:
                # 需要到自定义搜索标签找
                sql = f"select name from custom_search_field cef where cef.search_key='{engFieldValue}' and org_id={orgId}"
                logger.info("查询自定义搜索字段:" + sql)
                cnFieldName = mysql_pool.select_one(sql)
            fieldEntity = {'name': cnFieldName["name"], 'value': engFieldValue}
            excelEntity[str(i)] = fieldEntity
    # 开始拼接还款或者减免字段
    entityLen = len(excelEntity)
    for i in range(len(getRepaymentEntityLanguage(language))):
        excelEntity[str(entityLen + i)] = getRepaymentEntityLanguage(language)[str(i)]
    return excelEntity


def getRepaymentEntityLanguage(language):
    key = "repaymentEntity"
    if language:
        key = key + "_" + language
    return repaymentEntityLanguage[key]


def getKeySets(results, keyName):
    keySets = set()
    for data in results:
        key = data.get(keyName)
        if key:
            keySets.add(key)
    return keySets


def getUserDicts(orgId):
    userDicts = {}
    if not orgId:
        return userDicts
    sql = f"select id,name from user where org_id = {orgId}"
    results = mysql_pool.select(sql)
    for data in results or []:
        userDicts[str(data["id"])] = data["name"]
    return userDicts


if __name__ == '__main__':
    # 初始化心跳
    init_heartbeat("export_repayment", logger)
    while True:
        try:
            getTasks()
        except Exception as e:
            logger.exception(e)
        time.sleep(10)
