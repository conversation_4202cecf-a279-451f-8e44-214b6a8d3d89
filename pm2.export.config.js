module.exports = {
    apps : [
        {
            name   : "export_bpw_case_info.py",
            script : "./export_bpw_case_info.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        },
        {
            name   : "export_bpw_case_log.py",
            script : "./export_bpw_case_log.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        },
         {
            name   : "export_bpw_case_operation.py",
            script : "./export_bpw_case_operation.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        },
         {
            name   : "export_bpw_case_operation_voice_new.py",
            script : "./export_bpw_case_operation_voice_new.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        },
        {
            name   : "export_bpw_lawsuit_file.py",
            script : "./export_bpw_lawsuit_file.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        },
        {
            name   : "export_bpw_reduction.py",
            script : "./export_bpw_reduction.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        },
         {
            name   : "export_bpw_repayment.py",
            script : "./export_bpw_repayment.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        },
        {
            name   : "export_bpw_visit.py",
            script : "./export_bpw_visit.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        },
        {
            name   : "export_bpw_visit_file.py",
            script : "./export_bpw_visit_file.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        }, {
            name   : "export_bpw_case_video_record.py",
            script : "./export_bpw_case_video_record.py",
            interpreter: "/usr/bin/python3",
            instances: 1,
            restart_delay: 10000,
            autorestart: true
        }
    ]
};