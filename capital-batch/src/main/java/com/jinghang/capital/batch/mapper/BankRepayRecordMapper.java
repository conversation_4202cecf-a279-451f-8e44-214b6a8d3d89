package com.jinghang.capital.batch.mapper;


import com.jinghang.capital.batch.entity.BankRepayRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 * 还款记录表; Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
public interface BankRepayRecordMapper extends BaseMapper<BankRepayRecord> {

    List<BankRepayRecord> findSuccessList(@Param("channel") String channel, @Param("guaranteeCompany") String guaranteeCompany,
                                          @Param("repayType") String repayType, @Param("beginTime") LocalDateTime beginTime,
                                          @Param("endTime") LocalDateTime endTime);

    int countFailBankRepayRecord(@Param("beginTime") LocalDateTime beginTime,
                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 查询还款状态不为终态和还款时间小于等于计算后的时间的还款记录信息列表
     * @param countBackTime 计算后的时间
     * @return 还款超时记录列表
     */
//    @Query("select l from BankRepayRecord l where l.repayStatus not in ('SUCCESS','FAIL') and l.repayTime <= ?1")
    List<BankRepayRecord> findByRepayStatusAndCountBackTime(String countBackTime);

}
