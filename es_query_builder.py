class TermQuery(object):

    def __init__(self, key, value):
        self.key = key
        self.value = value

    def toMap(self):
        return {"term": {
            self.key: {
                "value": self.value
            }
        }
        }


class TermsQuery(object):

    def __init__(self, key, values):
        self.key = key
        self.values = values

    def toMap(self):
        return {"terms": {
            self.key: self.values
        }}


class RangeQuery(object):

    def __init__(self, key, _from=None, to=None, otherMap=None, include_lower=True, include_upper=True):
        self.key = key
        self._from = _from
        self.to = to
        self.include_lower = include_lower
        self.include_upper = include_upper
        self.otherMap = otherMap

    def __setitem__(self, key, value):
        if self.otherMap is None:
            self.otherMap = {}
        self.otherMap[key] = value

    def toMap(self):
        resultMap = {
            "range": {
                self.key: {
                    "from": self._from,
                    "to": self.to,
                    "include_lower": self.include_lower,
                    "include_upper": self.include_upper
                }
            }
        }
        if self.otherMap is not None:
            for key in self.otherMap.keys():
                resultMap["range"][self.key][key] = self.otherMap[key]
        return resultMap


class ExistsQuery(object):

    def __init__(self, key):
        self.key = key

    def toMap(self):
        return {
            "exists": {
                "field": self.key
            }
        }


class WildcardQuery(object):
    def __init__(self, key, value):
        self.key = key
        self.value = value

    def toMap(self):
        return {"wildcard": {
            self.key: {
                "value": self.value
            }
        }
        }


class MustQuery(object):
    def __init__(self, queryList):
        self.queryList = queryList

    def toMap(self):
        mustMap = []
        for query in self.queryList:
            mustMap.append(query.toMap())
        return {
            "must": mustMap
        }

    def append(self, query):
        self.queryList.append(query)


class MustNotQuery(object):
    def __init__(self, queryList):
        self.queryList = queryList

    def toMap(self):
        mustNotMap = []
        for query in self.queryList:
            mustNotMap.append(query.toMap())
        return {
            "must_not": mustNotMap
        }

    def append(self, query):
        self.queryList.append(query)


class ShouldQuery(object):
    def __init__(self, queryList):
        self.queryList = queryList

    def toMap(self):
        shouldMap = []
        if self.queryList is not None:
            for query in self.queryList:
                shouldMap.append(query.toMap())
        return {
            "should": shouldMap
        }

    def append(self, query):
        self.queryList.append(query)


class BoolQuery(object):
    def __init__(self, must_list, must_not_list, should_list):
        self.must_list = must_list
        self.must_not_list = must_not_list
        self.should_list = should_list

    def toMap(self):
        mustMap = []
        if self.must_list is not None:
            for must in self.must_list:
                mustMap.append(must.toMap())
        mustNotMap = []
        if self.must_not_list is not None:
            for mustNot in self.must_not_list:
                mustNotMap.append(mustNot.toMap())
        shouldMap = []
        if self.should_list is not None:
            for should in self.should_list:
                shouldMap.append(should.toMap())
        return {
            "bool": {
                "must": mustMap,
                "should": shouldMap,
                "must_not": mustNotMap
            }
        }


class Sort(object):
    def __init__(self, key, order):
        self.key = key
        self.order = order

    def toMap(self):
        return {
            self.key: {
                "order": self.order
            }
        }


class Search:
    def __init__(self, query, limit, order_list, otherMap=None, track_total_hits=True):
        self.query = query
        self.limit = limit
        self.order_list = order_list
        self.otherMap = otherMap
        self.track_total_hits = track_total_hits

    def __setitem__(self, key, value):
        if self.otherMap is None:
            self.otherMap = {}
        self.otherMap[key] = value

    def toMap(self):
        sortMapList = []
        for order in self.order_list:
            sortMapList.append(order.toMap())
        resultMap = {
            "size": self.limit,
            "query": self.query.toMap(),
            "sort": sortMapList,
            "track_total_hits": self.track_total_hits
        }
        if self.otherMap is not None:
            for key in self.otherMap.keys():
                resultMap[key] = self.otherMap[key]
        return resultMap
