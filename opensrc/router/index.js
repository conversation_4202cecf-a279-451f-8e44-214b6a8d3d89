import { createRouter, createWebHistory } from 'vue-router'
import routes from './routers?t=20221202'
import store from '@/store'
import { getToken } from "@/libs/util"
import config from '@/config'
const { homeName } = config
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})
const LOGIN_PAGE_NAME = 'login'

router.beforeEach((to, from, next) => {
  window.VueMainInstance.$Loading.start()
  const token = getToken();
  if (!token && to.name !== LOGIN_PAGE_NAME) {
    // 未登录且要跳转的页面不是登录页
    next({
      name: LOGIN_PAGE_NAME // 跳转到登录页
    });
  } else if (!token && to.name === LOGIN_PAGE_NAME) {
    // 未登录且要跳转的页面是登录页
    store.commit("setToken", "");
    next(); // 跳转
  } else if (token && to.name === LOGIN_PAGE_NAME) {
    // 已登录且要跳转的页面是登录页
    next({
      name: homeName // 跳转到homeName页
    });
  } else if (token && store.getters.userId) {
    // 已经登录的情况
    next();
  } else {
    // 刷新
    store.commit("setMenuRouters", routes);
    next();
    // const userInfo = getUserInLocalstorage()
    // store.dispatch('getUserInfo', { roleId: userInfo.roleId, userId: userInfo.userId }).then(user => {
    //   // 拉取用户信息，通过用户权限和跳转的页面的name来判断是否有权限访问;access必须是一个数组，如：['super_admin'] ['super_admin', 'admin']
    //   // turnTo(to, user.access, next)
    //   next(to.path)
    // }).catch((e) => {
    //   store.dispatch('handleLogOut')
    // })
  }
});

router.afterEach(to => {
  window.VueMainInstance.$Loading.finish();
  window.scrollTo(0, 0);
});

export default router;
