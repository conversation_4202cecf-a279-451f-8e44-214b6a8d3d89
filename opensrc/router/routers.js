// import Main from '@/components/main/main.vue?t=20221115'
export const Main = () => import('@open/components/main/main.vue')

// import parentView from '@/components/parent-view'

/**
 * iview-admin中meta除了原生参数外可配置的参数:
 * meta: {
 *  title: { String|Number|Function }
 *         显示在侧边栏、面包屑和标签栏的文字
 *         使用'{{ 多语言字段 }}'形式结合多语言使用，例子看多语言的路由配置;
 *         可以传入一个回调函数，参数是当前路由对象，例子看动态路由和带参路由
 *  hideInBread: (false) 设为true后此级路由将不会出现在面包屑中
 *  hideInMenu: (false) 设为true后在左侧菜单不会显示该页面选项
 *  noCloseable: (false)  设为true后不能被关闭
 *  notCache: (false) 设为true后页面不会缓存
 *  access: (null) 可访问该页面的权限数组，当前路由设置的权限会影响子路由
 *  icon: (-) 该页面在左侧菜单、面包屑和标签导航处显示的图标，如果是自定义图标，需要在图标名称前加下划线'_'
 *  beforeCloseName: (-) 设置该字段，则在关闭当前tab页时会去'@/router/before-close.js'里寻找该字段名对应的方法，作为关闭前的钩子函数
 * }
 */

export default [
  {
    path: '/login',
    name: 'login',
    meta: {
      title: 'Login - 登录',
      hideInMenu: true
    },
    component: () => import('@open/view/login/login.vue')
  },
  {
    path: '/',
    name: '_home',
    redirect: '/home',
    component: Main,
    meta: {
      notCache: true
    },
    children: [
      {
        path: '/home',
        name: 'home',
        meta: {
          title: '管理控制台',
          notCache: true,
          icon: 'md-home'
        },
        component: () => import('@open/view/home')
      }
    ]
  },
  {
    path: '/tip',
    name: '_tip',
    component: Main,
    children: [
      {
        path: 'describe',
        name: 'describe',
        meta: {
          title: '调用说明',
          icon: 'ios-paper'
        },
        component: () => import('@open/view/tip')

      }
    ]
  },
  {
    path: '/template',
    name: '_template',
    component: Main,
    children: [
      {
        path: 'get_template',
        name: 'getTemplate',
        meta: {
          title: '获取模板参数',
          icon: 'md-albums'
        },
        component: () => import('@open/view/getTemplate')

      }
    ]
  },
  {
    path: '/import',
    name: '_import',
    component: Main,
    meta: {
      title: '案件导入',
      icon: 'ios-folder'
    },
    children: [
      {
        path: 'caseImport',
        name: 'case_import',
        meta: {
          title: '案件导入'
        },
        component: () => import('@open/view/import/caseImport.vue')
      },
      {
        path: 'getResults',
        name: 'get_results',
        meta: {
          title: '获取导入结果'
        },
        component: () => import('@open/view/import/getResults.vue')
      }
    ]
  },
  {
    path: '/caseExport',
    name: '_caseExport',
    component: Main,
    meta: {
      title: '案件导出',
      icon: 'ios-folder'
    },
    children: [
      {
        path: 'caseExport',
        name: 'case_export',
        meta: {
          title: '案件导出',
          icon: 'ios-folder'
        },
        component: () => import('@open/view/caseExport/export.vue')
      }
    ]
  },
  {
    path: '/caseStatus',
    name: '_caseStatus',
    component: Main,
    meta: {
      title: '更新案件状态',
      icon: 'md-refresh-circle'
    },
    children: [
      {
        path: 'updateStatus',
        name: 'update_status',
        meta: {
          title: '更新案件状态'
        },
        component: () => import('@open/view/update/updateStatus.vue')
      },
      {
        path: 'getStatusResults',
        name: 'get_status_results',
        meta: {
          title: '获取更新结果'
        },
        component: () => import('@open/view/update/getResults.vue')
      }
    ]
  },
  {
    path: '/update',
    name: '_update',
    component: Main,
    meta: {
      title: '案件更新',
      icon: 'md-refresh-circle'
    },
    children: [
      {
        path: 'caseUpdate',
        name: 'case_update',
        meta: {
          title: '案件更新'
        },
        component: () => import('@open/view/caseUpdate/caseUpdate.vue')
      },
      {
        path: 'getCaseResults',
        name: 'get_case_results',
        meta: {
          title: '获取更新结果'
        },
        component: () => import('@open/view/caseUpdate/getResults.vue')
      }
    ]
  },
  {
    path: '/contacts',
    name: '_contacts',
    component: Main,
    meta: {
      title: '联系人导入',
      icon: 'md-contacts'
    },
    children: [
      {
        path: 'contacts',
        name: 'contacts_import',
        meta: {
          title: '联系人导入',
        },
        component: () => import('@open/view/contacts/importContacts.vue')
      },
      {
        path: 'contactsResult',
        name: 'contacts_result',
        meta: {
          title: '获取导入结果',
        },
        component: () => import('@open/view/contacts/importResult.vue')
      }
    ]
  },
  {
    path: '/contactsUpdate',
    name: '_contactsUpdate',
    component: Main,
    meta: {
      title: '联系人状态更新',
      icon: 'md-refresh-circle'
    },
    children: [
      {
        path: 'updateContact',
        name: 'update_contact',
        meta: {
          title: '联系人状态更新'
        },
        component: () => import('@open/view/contactUpdate/update.vue')
      },
      {
        path: 'getResult',
        name: 'get_result',
        meta: {
          title: '获取更新任务结果'
        },
        component: () => import('@open/view/contactUpdate/result.vue')
      }
    ]
  },
  {
    path: '/controller',
    name: '_controller',
    component: Main,
    meta: {
      title: '还款导入',
      icon: 'ios-card'
    },
    children: [
      {
        path: 'refundImport',
        name: 'refund_import',
        meta: {
          title: '还款导入',
        },
        component: () => import('@open/view/refund/refundImport.vue')
      },
      {
        path: 'importResult',
        name: 'import_result',
        meta: {
          title: '获取导入结果'
        },
        component: () => import('@open/view/refund/result.vue')
      },
      {
        path: 'refundQuery',
        name: 'refund_query',
        meta: {
          title: '还款查询'
        },
        component: () => import('@open/view/refund/query.vue')
      }
    ]
  },
  {
    path: '/recordImport',
    name: '_recordImport',
    component: Main,
    meta: {
      title: '催记导入',
      icon: 'ios-card'
    },
    children: [
      {
        path: 'recordImport',
        name: 'record_import',
        meta: {
          title: '催记导入',
        },
        component: () => import('@open/view/recordImport/import.vue')
      },
      {
        path: 'recordResult',
        name: 'record_result',
        meta: {
          title: '获取导入结果'
        },
        component: () => import('@open/view/recordImport/getResult.vue')
      }
    ]
  },
  {
    path: '/recordExport',
    name: '_recordExport',
    component: Main,
    meta: {
      title: '催记导出',
      icon: 'ios-card'
    },
    children: [
      {
        path: 'recordExport',
        name: 'record_export',
        meta: {
          title: '催记导出',
          icon: 'ios-card'
        },
        component: () => import('@open/view/recordExport/export.vue')
      }
    ]
  },
  {
    path: '/error',
    name: 'error_code',
    component: Main,
    meta: {
      title: '返回值说明',
      icon: 'md-return-left'
    },
    children: [
      {
        path: 'backCode',
        name: 'back_code',
        meta: {
          title: '返回值说明',
          icon: 'md-return-left'
        },
        component: () => import('@open/view/error/error_code')
      }
    ]
  },
  {
    path: '/401',
    name: 'error_401',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error-page/401.vue')
  },
  {
    path: '/500',
    name: 'error_500',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error-page/500.vue')
  },
  {
    path: '/:catchAll(.*)',
    name: 'error_404',
    meta: {
      hideInMenu: true
    },
    component: () => import('@/view/error-page/404.vue')
  }
]
