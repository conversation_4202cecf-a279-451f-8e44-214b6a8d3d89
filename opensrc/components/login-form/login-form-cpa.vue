<template>
  <Form ref="loginForm" :model="form" :rules="rules" class="login-form-cpa" @keydown.enter.native="handleSubmit">
    <FormItem prop="orgNo">
      <Input v-model.trim="form.orgNo" :placeholder="langPage.plugin.loginNo" size="large">
      <template #prepend>
        <span>
          <Icon class="icons" type="iconfont icon-gongsibianhao"></Icon>
        </span>
      </template>
      </Input>
    </FormItem>
    <FormItem prop="userNo">
      <Input v-model.trim="form.userNo" placeholder="员工编号" size="large">
      <template #prepend>
        <Icon class="icons" type="iconfont icon-yuangongbianhao"></Icon>
      </template>
      </Input>
    </FormItem>
    <FormItem prop="password">
      <Input v-model="form.password" type="password" placeholder="登录密码" password size="large">
      <template #prepend>
        <Icon class="icons" type="iconfont icon-mima"></Icon>
      </template>
      </Input>
    </FormItem>
    <FormItem v-show="isVerifyCode" prop="validateCode">
      <div class="validate">
        <Input v-model.trim="form.validateCode" class="validate-code" size="large" placeholder="请输入验证码">
        <template #prepend>
          <Icon class="icons" type="iconfont icon-yanzhengma"></Icon>
        </template>
        </Input>
        <div class="imgcode-box">
          <img class="validate-img" :src="vcodeImg" @click="refreshVcodeImg">
        </div>
      </div>
    </FormItem>
    <FormItem>
      <Button class="login-btn" size="large" type="primary" long @click="handleSubmit">
        立即登录
      </Button>
    </FormItem>
  </Form>
</template>

<script>
import { mapMutations, mapGetters } from 'vuex'
import { getImageUuid } from '@/libs/util'

export default {
  name: 'LoginForm',
  props: {
    validateCodeRules: {
      type: Array,
      default: () => {
        return [
          {
            required: true,
            message: '验证码不能为空',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  data() {
    return {
      form: {
        loginName: '',
        password: '',
        oldPassword: '',
        validateCode: ''
      }
    }
  },
  computed: {
    ...mapGetters([
      'vcodeImg',
      'isVerifyCode',
      'langPage'
    ]),
    rules() {
      return {
        orgNo: [{ required: true, message: '请输入公司编号', trigger: 'blur' }],
        userNo: [{ required: true, message: '请输入员工编号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        validateCode: this.isVerifyCode ? this.validateCodeRules : [{ required: false, message: '验证码不能为空', trigger: 'blur' }]
      }
    }
  },
  methods: {
    ...mapMutations([
      'refreshVcodeImg'
    ]),
    formCancel() {
      // 外部调用
      this.$refs.loginForm.resetFields()
    },
    handleSubmit() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          const backData = {
            orgNo: this.form.orgNo,
            userNo: this.form.userNo,
            password: this.form.password,
            validateCode: this.form.validateCode,
            loginType: 1,
            imageUuid: getImageUuid()
          }

          this.$emit('on-success-valid', backData)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import './loginFormCpa.less';
</style>
