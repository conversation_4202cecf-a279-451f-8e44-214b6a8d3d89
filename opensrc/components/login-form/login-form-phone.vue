<template>
  <div class="login-form-phone">
    <Form ref="loginForm" :model="form" :rules="rules" @keydown.enter.native="handleSubmit">
      <FormItem :label="isVerifiedPhone?'修改手机号':'手机号验证'" style="margin-bottom: 10px;">
      </FormItem>
      <FormItem v-show="!isChangepwd" prop="loginName">
        <Input v-model.trim="form.loginName" placeholder="手机号码" disabled>
        <template #prepend>
          <Icon class="icons" type="iconfont icon-shoujihao"></Icon>
        </template>
        </Input>
      </FormItem>
      <FormItem v-show="isVerifiedPhone" prop="realPhone" class="realPhone">
        <Input v-model.trim="form.realPhone" placeholder="请输入新手机号">
        <template #prepend>
          <Icon class="icons" type="iconfont icon-shoujihao"></Icon>
        </template>
        </Input>
      </FormItem>
      <FormItem style="position: relative;" class="codeFormItem">
        <Input v-model.trim="form.validateCode" class="smsInput" style="width:62.1%;" type="text" placeholder="请输入验证码">
        <template #prepend>
          <Icon class="icons" type="iconfont icon-yanzhengma"></Icon>
        </template>
        </Input>
        <!-- 此处不能使用插槽形式放在input标签内，短信倒计时会影响输入框中文输入，出现闪退情况-->
        <Button class="getCode" style="width:38%;background: #f8f8f9;position:absolute;right:0;top:1px;" :loading="isPostLoading" :disabled="timeCountDown" @click="verify">
          {{ timeCountDown?(vcDownCount+'s后重新获取'):'获取验证码' }}
        </Button>
        <p v-if="vcDownCount === 60 && !(ifSendCode && voTimeCountDown)" class="voiceVertify">
          收不到短信？获取<a @click="getVoiceVertify">
            语音验证码
          </a>
        </p>
        <p v-if="ifSendCode && voTimeCountDown" class="voiceVertify">
          已发送，{{ voDownCount }}秒后重新获取
        </p>
      </FormItem>
      <FormItem v-if="isVerifiedPhone">
        <a style="color: #ff4f1f;text-decoration:underline" @click="back">
          返回上一步
        </a>
      </FormItem>
      <FormItem v-if="!isVerifiedPhone" style="margin-bottom: 19px;">
        <Button @click="isVerifiedPhone = true; ifSendCode = false">
          非本人手机号
        </Button>
      </FormItem>
      <FormItem>
        <Button type="primary" long @click="handleSubmit">
          {{ isVerifiedPhone?'修改':'登录' }}
        </Button>
      </FormItem>
    </Form>
    <Modal
      v-model="imgCodeModal"
      title="请输入图片验证码"
      :footer-hide="true"
      :closable="true"
      width="300px"
      :mask-closable="false"
    >
      <Form ref="imgCodeForm" :rules="rules1" :model="imgCodeForm">
        <FormItem prop="validateCode" style="position:relative;">
          <Input v-model.trim="imgCodeForm.validateCode" class="validate-code imgcodeInput" style="width:60%;" size="large" placeholder="请输入验证码">
          </Input>
          <!-- 此处不能使用插槽形式放在input标签内，短信倒计时会影响输入框中文输入，出现闪退情况-->
          <span class="imgcodeBox">
            <img style="height: 29px;margin-top:8px;" :src="vcodeImg" @click="refreshVcodeImg">
          </span>
        </FormItem>
        <Button type="primary" long style="margin-top:14px;" @click="confirmGet">
          确定获取
        </Button>
      </Form>
    </Modal>
  </div>
</template>
<script>
import { mapMutations, mapGetters, mapActions } from 'vuex'
import { getImageUuid } from '@/libs/util'

export default {
  name: 'loginForm',
  props: {
    phone: {
      type: String,
      default: ''
    },
    defaultpwd: {
      type: String,
      default: ''
    },
    isChangepwd: {
      type: Boolean,
      default: false
    },
    loginType: {
      type: String,
      default: ''
    },
    loginNameRules: {
      type: Array,
      default: () => {
        return [
          {
            required: true,
            message: '手机号不能为空',
            trigger: 'blur',
            validator: (rule, value) => {
              return new Promise((resolve, reject) => {
                if (!value) {
                  reject('手机号不能为空')
                } else {
                  resolve()
                }
              })
            }
          }
        ]
      }
    },
    validateCodeRules: {
      type: Array,
      default: () => {
        return [
          { required: true, message: '验证码不能为空' }
        ]
      }
    }
  },
  data() {
    return {
      form: {
        loginName: '',
        realPhone: '',
        validateCode: ''
      },
      isVerifiedPhone: false,
      ifChangePwd: false,
      isPostLoading: false,
      ifSendCode: false,
      imgCodeForm: {
        validateCode: ''
      },
      imgCodeModal: false,
      rules1: {
        validateCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters([
      'vcodeImg',
      'timeCountDown',
      'vcDownCount',
      'mobile',
      'userId',
      'userExtend',
      'voDownCount',
      'voTimeCountDown'
    ]),
    rules() {
      return {
        loginName: this.isChangepwd ? this.loginNameRules : [{ required: false, message: '手机号不能为空', trigger: 'blur' }],
        realPhone: [{
          required: true,
          trigger: 'blur',
          validator: (rule, value) => {
            return new Promise((resolve, reject) => {
              if (this.isVerifiedPhone) {
                if (!value) {
                  reject('请输入本人手机号')
                } else if (value.length !== 11) {
                  reject('请输入正确的手机号')
                } else {
                  resolve()
                }
              } else {
                resolve()
              }
            })
          }
        }],
        validateCode: this.validateCodeRules
      }
    }
  },
  methods: {
    ...mapMutations([
      'refreshVcodeImg',
      'setIsOldUser'
    ]),
    ...mapActions([
      'getVerify'
    ]),
    formCancel() {
      // 外部调用
      this.$refs.loginForm.resetFields()
    },
    back() {
      this.ifSendCode = false
      this.form.realPhone = null
      this.isVerifiedPhone = false
    },
    verify() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          const mobile = this.isVerifiedPhone ? this.form.realPhone : this.form.loginName
          const type = this.isVerifiedPhone ? 1 : 0

          if (!mobile.length) {
            this.$Message.error('请填写手机号')
            return
          }

          if (mobile.length !== 11) {
            this.$Message.error('请填写正确的手机号')
            return
          }

          this.$emit('on-vertify', { mobile, type })
        }
      })
    },
    handleSubmit() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          if (this.isVerifiedPhone && this.form.realPhone && this.form.realPhone.length !== 11) {
            this.$Message.error('请填写正确的手机号')
            return
          }

          const backData = {
            oldMobile: this.form.loginName,
            newMobile: this.form.realPhone ? this.form.realPhone : null,
            smsCode: this.form.validateCode
          }

          this.$emit('on-success-check', backData)
        }
      })
    },
    showPwdChange() {
      this.ifChangePwd = true
      this.$nextTick(() => this.$refs.pwdForm.resetFields())
    },
    confirmGet() {
      this.$refs.imgCodeForm.validate((valid) => {
        if (valid) {
          const type = this.isVerifiedPhone ? 1 : 0
          const mobile = this.isVerifiedPhone ? this.form.realPhone : this.form.loginName
          this.getVerify({ mobile: mobile, type: type, isVoice: 1, validateCode: this.imgCodeForm.validateCode, imageUuid: getImageUuid() }).then(res => {
            if (res) {
              this.$Message.success('语音验证码已发送，请注意查收！')
              this.imgCodeModal = false
              this.ifSendCode = true
            } else {
              this.imgCodeForm.validateCode = ''
              this.refreshVcodeImg()
            }
          })
        }
      })
    },
    getVoiceVertify() {
      const type = this.isVerifiedPhone ? 1 : 0
      const mobile = this.isVerifiedPhone ? this.form.realPhone : this.form.loginName

      this.$emit('on-vertify', { mobile, type, isVoice: 1 })
    }
  },
  mounted() {
    this.form.loginName = this.mobile
  }
}
</script>

  <style lang="less" scoped>
  @import './loginFormPhone.less';

     :deep(.codeFormItem div.smsInput){
      margin:0;padding:0;
    }
 :deep(.codeFormItem .smsInput input){
  border-right:0;
  border-radius:0px 0px 0px 0px !important;
}
.getCode{
  height:32px;
  border-radius:0px 4px 4px 0px !important;
}
button.getCode:hover{
  color:#ff4f1f !important;
}
 :deep(.imgcodeInput input){
  border-radius:4px 0px 0px 4px !important;
}
.imgcodeBox{
  position:absolute;
  top:0px;
  height:45px;
  display:inline-block;
  line-height:45px;
  border:solid 1px #dcdee2;
  border-radius:0px 4px 4px 0px;
  border-left:0;
}

   :deep(.realPhone .ivu-input-group-append){
    display: none;
  }

   :deep(.realPhone input){
    border-radius: 0 4px 4px 0;
  }

  .voiceVertify {
    position: absolute;
    right: 0;
    top: 26px;
    a {
      color: #FF4F1F;
    }
  }
  </style>
