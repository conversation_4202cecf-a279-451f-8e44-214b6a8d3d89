.login-form {
    padding-bottom: 1px;
    .icons {
        font-size: 16px;
        color: #818a94;
    }
    .forget-pwd {
        text-align: right;
        margin-bottom: 26px;
        >a {
            font-size: 14px;
            font-weight: 400;
            color: @primary-color;
            line-height: 20px;
        }
    }
    .login-btn {
        height: 40px;
    }
    .validate {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;

        .validate-code {
           :deep(.ivu-input) {
            padding-right: 112px;
          }
        }
        .validate-code-reset {
           :deep(.ivu-input) {
            padding-right: 92px;
          }
        }
        .imgcode-box {
          position: absolute;
          z-index: 2;
          right: 2px;
          top: 2px;
        }
        .validate-img {
            height: 36px;
        }
        .validate-imgs {
          height: 28px;
        }
    }
    .change-forms {
       :deep(.ivu-input) {
        padding-left: 12px;
        padding-right: 12px;
      }
    }
}

.sms-input {
     :deep(.ivu-input.ivu-input-default) {
        border-right: 0;
        border-radius:4px 0px 0px 4px;
    }
}
.get-code.ivu-btn {
    border-radius: 0px 4px 4px 0px;
    width: 30%;
    background: #f8f8f9;
    height: 32px;
}
.voice-vertify {
    text-align: right;
    margin-bottom: -14px;
    color: #636C78;
    >a {
        color: @primary-color;
    }
}
