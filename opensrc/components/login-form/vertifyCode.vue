<template>
  <Modal
    v-model="ui.showModal"
    title="请输入图片验证码"
    :footer-hide="true"
    :closable="true"
    :width="300"
    :mask-closable="false"
    :transfer="false"
    @on-visible-change="visibleChange"
  >
    <Form ref="form" :rules="rules" :model="forms">
      <FormItem prop="validateCode" style="position:relative;">
        <Input
          v-model.trim="forms.validateCode"
          class="validate-code imgcode-input"
          style="width:68%;"
          size="large"
          placeholder="请输入验证码"
        ></Input>
        <!-- 此处不能使用插槽形式放在input标签内，短信倒计时会影响输入框中文输入，出现闪退情况-->
        <span class="imgcode-box">
          <img
            style="height: 29px; margin-top: 8px;"
            :src="vcodeImg"
            @click="refreshVcodeImg"
          >
        </span>
      </FormItem>
      <Button
        type="primary"
        long
        class="confirm-btn"
        @click="confirmGet"
      >
        确定获取
      </Button>
    </Form>
  </Modal>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import { getImageUuid } from '@/libs/util'

export default {
  data() {
    return {
      forms: {
        validateCode: null
      },
      rules: {
        validateCode: [
          {
            required: true,
            message: '请输入验证码',
            trigger: 'blur'
          }
        ]
      },
      ui: {
        showModal: false
      }
    }
  },
  computed: {
    ...mapGetters([
      'vcodeImg'
    ])
  },
  methods: {
    ...mapMutations([
      'refreshVcodeImg'
    ]),
    confirmGet() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('on-submit', { ...this.forms, imageUuid: getImageUuid() })
        }
      })
    },
    visibleChange(bool) {
      if (!bool) {
        this.$emit('on-close')
      }
    }
  },
  created() {
    this.refreshVcodeImg()
  }
}
</script>

<style lang="less" scoped>
.validate-code {
     :deep(.ivu-input.ivu-input-large) {
        height: 45px;
    }
}
 :deep(.imgcode-input .ivu-input) {
    border-radius:4px 0px 0px 4px !important;
}
.imgcode-box {
    position:absolute;
    top:0px;
    height:45px;
    display:inline-block;
    line-height:45px;
    border:solid 1px #dcdee2;
    border-radius:0px 4px 4px 0px;
    border-left:0;
}
.confirm-btn {
    background-color: #FF6000;
    height: 32px;
    margin-top: 14px;
}
</style>
