<template>
  <div class="login-form">
    <Form v-if="isChangepwd" class="change-forms" ref="loginForm" :model="form" :rules="rules" label-position="right" :label-width="100" @keydown.enter.native="handleSubmit">
      <FormItem v-show="!defaultpwd" prop="oldPassword" label="原密码：">
        <Input v-model="form.oldPassword" type="password" placeholder="请输入原来密码" password></Input>
      </FormItem>
      <FormItem prop="password" label="新密码：">
        <Input v-model="form.password" type="password" :placeholder="userExtend.strongPasswordSwitch ? '需长度8-16位大小写字母+数字及特殊英文符号@#＄％^＆*_+-=' : '请输入新密码'" password></Input>
      </FormItem>
      <FormItem prop="rePassword" label="确认新密码：">
        <Input v-model="form.rePassword" type="password" placeholder="请确认新密码" password></Input>
      </FormItem>
      <FormItem v-show="isVerifyCode" prop="validateCode" label="验证码：">
        <div class="validate">
          <Input v-model.trim="form.validateCode" class="validate-code-reset" placeholder="请输入验证码"></Input>
          <div class="imgcode-box">
            <img class="validate-imgs" :src="vcodeImg" @click="refreshVcodeImg">
          </div>
        </div>
      </FormItem>
      <FormItem class="btns">
        <Button style="margin-right: 8px;" @click="cancel">
          取消
        </Button>
        <Button type="primary" @click="handleSubmit">
          确定
        </Button>
      </FormItem>
    </Form>
    <Form v-if="!isChangepwd" ref="loginForm" :model="form" :rules="rules" @keydown.enter.native="handleSubmit">
      <FormItem prop="loginName">
        <Input v-model.trim="form.loginName" :placeholder="loginNamePhd" size="large">
        <template #prepend>
          <Icon class="icons" type="iconfont icon-shoujihao" />
        </template>
        </Input>
      </FormItem>
      <FormItem prop="password">
        <Input v-model="form.password" type="password" :placeholder="userExtend.strongPasswordSwitch ? '请输入密码' : '请输入密码'" password size="large">
        <template #prepend>
          <Icon class="icons" type="iconfont icon-mima" />
        </template>
        </Input>
      </FormItem>
      <FormItem v-show="isVerifyCode" prop="validateCode">
        <div class="validate">
          <Input v-model.trim="form.validateCode" class="validate-code" size="large" placeholder="请输入验证码">
          <template #prepend>
            <Icon class="icons" type="iconfont icon-yanzhengma" />
          </template>
          </Input>
          <span class="imgcode-box">
            <img class="validate-img" :src="vcodeImg" @click="refreshVcodeImg">
          </span>
        </div>
      </FormItem>
      <p v-if="isForget" class="forget-pwd">
        <a @click="showPwdChange">
          忘记密码
        </a>
      </p>
      <FormItem>
        <Button size="large" long class="login-btn" @click="handleSubmit">
          立即登录
        </Button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import { mapMutations, mapGetters } from 'vuex'
import { getImageUuid } from '@/libs/util'

export default {
  name: 'loginForm',
  props: {
    defaultpwd: {
      type: String,
      default: ''
    },
    isChangepwd: {
      type: Boolean,
      default: false
    },
    loginNamePhd: {
      type: String,
      default: '请输入手机号'
    },
    loginNameRules: {
      type: Array,
      default: () => {
        return [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur'
          }
        ]
      }
    },
    validateCodeRules: {
      type: Array,
      default: () => {
        return [
          {
            required: true,
            message: '请输入验证码',
            trigger: 'blur'
          }
        ]
      }
    },
    isForget: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      form: {
        loginName: '',
        password: '',
        rePassword: '',
        oldPassword: this.defaultpwd,
        validateCode: ''
      },
      ifChangePwd: false,
      isPostLoading: false,
      submitingPwdChange: false
    }
  },
  computed: {
    ...mapGetters([
      'vcodeImg',
      'isVerifyCode',
      'userExtend'
    ]),
    rules() {
      const validatePassword = (rule, value, callback) => {
        if (value === '') {
          callback(new Error(this.isChangepwd ? '新密码不能为空' : '密码不能为空'))
        } else if (this.form.oldPassword === value) {
          callback(new Error('新旧密码不能相同'))
        } else {
          callback()
        }
      }

      return {
        oldPassword: this.isChangepwd && !this.defaultpwd ? [{ required: true, trigger: 'blur',
          validator: (rule, value) => {
            return new Promise((resolve, reject) => {
              if (!value) {
                reject(this.$t('l_noPassword'))
              } else if (value.length < 6 || value.length > 16) {
                reject('密码需为6~16位')
              } else {
                resolve()
              }
            })
          }
        }] : [{ required: false, trigger: 'blur', message: this.$t('l_noPassword') }],
        loginName: !this.isChangepwd ? this.loginNameRules : [{ required: false, message: '请输入手机号', trigger: 'blur' }],
        password: [{ required: true, trigger: 'blur',
          validator: (rule, value) => {
            return new Promise((resolve, reject) => {
              this.$Message.destroy()
              if (!value) {
                reject('请输入密码')
              } else if (this.isChangepwd) {
                if (this.userExtend.strongPasswordSwitch) {
                  const reg = /^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&*_=+-]).{8,16}$/
                  if (!reg.test(value)) {
                    this.$Message.error({
                      content: '长度需不少于8-16位数，且包含大小写字母+数字及特殊英文符号@#＄％^＆*_+-=',
                      duration: 0,
                      closable: true
                    })
                    reject('校验失败')
                  } else {
                    const validReg = /^[0-9a-zA-Z@#$%^&*_=+-]+$/
                    if (!validReg.test(value)) {
                      this.$Message.error({
                        content: '长度需不少于8-16位数，且包含大小写字母+数字及特殊英文符号@#＄％^＆*_+-=',
                        duration: 0,
                        closable: true
                      })
                      reject('校验失败')
                    } else {
                      const validReg = /^[0-9a-zA-Z@#$%^&*_=+-]+$/
                      if (!validReg.test(value)) {
                        this.$Message.error({
                          content: '长度需不少于8-16位数，且包含大小写字母+数字及特殊英文符号@#＄％^＆*_+-=',
                          duration: 0,
                          closable: true
                        })
                        reject('校验失败')
                      } else {
                        resolve()
                      }
                    }
                  }
                } else {
                  if (value.length < 6 || value.length > 16) {
                    reject('密码需为6~16位')
                  } else {
                    resolve()
                  }
                }
              } else {
                resolve()
              }
            })
          }
        }],
        rePassword: [{ required: true, trigger: 'blur',
          validator: (rule, value) => {
            return new Promise((resolve, reject) => {
              if (this.isChangepwd) {
                if (!value) {
                  reject(this.$t('l_noPassword'))
                } else if (value !== this.form.password) {
                  reject(this.$t('l_differentPwd'))
                } else {
                  resolve()
                }
              } else {
                resolve()
              }
            })
          }
        }],
        validateCode: this.isVerifyCode ? this.validateCodeRules : [{ required: false, message: '请输入密码', trigger: 'blur' }]
      }
    }
  },
  methods: {
    ...mapMutations([
      'refreshVcodeImg'
    ]),
    clearData() {
      this.$refs.loginForm.resetFields()
    },
    handleSubmit() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          if (this.form.password !== this.form.rePassword && this.isChangepwd) {
            this.$Message.error('前后密码不一致，请重新确认')
            return
          }
          const backData = {
            loginName: this.form.loginName,
            password: this.form.password,
            validateCode: this.form.validateCode,
            loginType: 0,
            imageUuid: getImageUuid()
          }

          if (this.isChangepwd) {
            backData.oldPassword = this.form.oldPassword
          }
          this.$emit('on-success-valid', backData)
        }
      })
    },
    cancel() {
      this.$emit('on-cancel')
      this.$refs.loginForm.resetFields()
    },
    showPwdChange() {
      this.$emit('on-reset')
      this.ifChangePwd = true
    }
  }
}
</script>

<style lang="less" scoped>
@import './loginForm.less';
</style>
