import TRTC from 'trtc-js-sdk'

export default {
  data() {
    return {
      client: null,
      localStream: null,
      remoteStreamList: [],
      isLeaving: false,
      isJoining: false,
      isJoined: false,
      isPublishing: false,
      isPublished: false,
      isMutedVideo: false,
      isMutedAudio: false,
      isPlayingLocalStream: false,
      speaking: null // 说话人userId
    }
  },

  methods: {
    // 初始化客户端
    async initClient() {
      this.client = TRTC.createClient({
        mode: 'rtc',
        sdkAppId: this.sdkAppId,
        userId: this.userId,
        userSig: this.userSig,
        useStringRoomId: true
      })
      this.handleClientEvents()
    },

    async initLocalStream() {
      this.localStream = TRTC.createStream({
        audio: true,
        video: true,
        userId: this.userId
        // cameraId: this.cameraId,
        // microphoneId: this.microphoneId,
      })
      try {
        await this.localStream.initialize()
      } catch (error) {
        this.localStream = null
        throw error
      }
    },

    playLocalStream() {
      this.localStream.play('localStream')
        .then(() => {
          this.isPlayingLocalStream = true
        })
        .catch((error) => {
          console.log(error)
        })
    },

    destroyLocalStream() {
      this.localStream && this.localStream.stop()
      this.localStream && this.localStream.close()
      this.localStream = null
      this.isPlayingLocalStream = false
    },

    playRemoteStream(remoteStream, element) {
      if (remoteStream.getType() === 'main' && remoteStream.getUserId().indexOf('share') >= 0) {
        remoteStream.play(element, { objectFit: 'contain' }).catch()
      } else {
        remoteStream.play(element).catch()
      }
    },

    resumeStream(stream) {
      stream.resume()
    },

    async join() {
      if (this.isJoining || this.isJoined) {
        return
      }
      this.isJoining = true
      !this.client && await this.initClient()
      try {
        await this.client.join({ roomId: this.roomId })
        this.isJoining = false
        this.isJoined = true

        this.startGetAudioLevel()
      } catch (error) {
        this.isJoining = false
        console.error('join room failed', error)
        throw error
      }
    },

    async publish() {
      if (!this.isJoined || this.isPublishing || this.isPublished) {
        return
      }
      this.isPublishing = true
      try {
        await this.client.publish(this.localStream)
        this.isPublishing = false
        this.isPublished = true
      } catch (error) {
        this.isPublishing = false
        console.error('publish localStream failed', error)
        throw error
      }
    },

    async unPublish() {
      if (!this.isPublished || this.isUnPublishing) {
        return
      }
      this.isUnPublishing = true
      try {
        await this.client.unpublish(this.localStream)
        this.isUnPublishing = false
        this.isPublished = false
      } catch (error) {
        this.isUnPublishing = false
        console.error('unpublish localStream failed', error)
        throw error
      }
    },

    async subscribe(remoteStream, config = { audio: true, video: true }) {
      try {
        await this.client.subscribe(remoteStream, {
          audio: !config.audio ? true : config.audio,
          video: !config.video ? true : config.video
        })
      } catch (error) {
        console.error(`subscribe ${remoteStream.getUserId()} with audio: ${config.audio} video: ${config.video} error`, error)
      }
    },

    async unSubscribe(remoteStream) {
      try {
        await this.client.unsubscribe(remoteStream)
      } catch (error) {
        console.error(`unsubscribe ${remoteStream.getUserId()} error`, error)
      }
    },

    async leave() {
      if (!this.isJoined || this.isLeaving) {
        return
      }
      this.isLeaving = true
      this.stopGetAudioLevel()
      this.isPublished && await this.unPublish()
      this.localStream && this.destroyLocalStream()

      try {
        await this.client.leave()
        this.isLeaving = false
        this.isJoined = false
      } catch (error) {
        this.isLeaving = false
        console.error('leave room error', error)
        throw error
      }
    },

    muteVideo() {
      if (this.localStream) {
        this.localStream.muteVideo()
        this.isMutedVideo = true
      }
    },

    muteAudio() {
      if (this.localStream) {
        this.localStream.muteAudio()
        this.isMutedAudio = true
      }
    },

    unmuteVideo() {
      if (this.localStream) {
        this.localStream.unmuteVideo()
        this.isMutedVideo = false
      }
    },

    unmuteAudio() {
      if (this.localStream) {
        this.localStream.unmuteAudio()
        this.isMutedAudio = false
      }
    },

    switchDevice(type, deviceId) {
      try {
        if (this.localStream) {
          this.localStream.switchDevice(type, deviceId)
        }
      } catch (error) {
        console.error('switchDevice failed', error)
      }
    },

    startGetAudioLevel() {
      // 文档：https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/module-ClientEvent.html#.AUDIO_VOLUME
      this.client.on('audio-volume', (event) => {
        event.result.forEach(({ userId, audioVolume }) => {
          if (audioVolume > 2) {
            this.speaking = this.speakingInfo[userId]
          } else {
            if (this.speaking === this.speakingInfo[userId]) {
              setTimeout(() => {
                // 断开更平稳点
                this.speaking = null
              }, 2000)
            }
          }
        })
      })
      this.client.enableAudioVolumeEvaluation(200)
    },

    stopGetAudioLevel() {
      this.client && this.client.enableAudioVolumeEvaluation(-1)
    },

    handleClientEvents() {
      this.client.on('error', (error) => {
        console.error(error)
      })
      this.client.on('client-banned', async(event) => {
        console.warn(`client has been banned for ${event.reason}`)

        this.isPublished = false
        this.localStream = null
        await this.leave()
      })
      // fired when a remote peer is joining the room
      this.client.on('peer-join', (event) => {
        const { userId } = event
        // 远端流切换
        this.debotor = false
        this.enableSmall = true
        console.log(`peer-join ${userId}`, event)
      })
      // fired when a remote peer is leaving the room
      this.client.on('peer-leave', (event) => {
        const { userId } = event
        this.enableSmall = false
        console.log(`peer-leave ${userId}`, event)
      })

      // fired when a remote stream is added
      this.client.on('stream-added', (event) => {
        const { stream: remoteStream } = event
        const remoteUserId = remoteStream.getUserId()
        if (remoteUserId === `share_${this.userId}`) {
          // don't need screen shared by us
          this.unSubscribe(remoteStream)
        } else {
          console.log(`remote stream added: [${remoteUserId}] type: ${remoteStream.getType()}`)
          // subscribe to this remote stream
          this.subscribe(remoteStream)
        }
      })
      // fired when a remote stream has been subscribed
      this.client.on('stream-subscribed', (event) => {
        const { stream: remoteStream } = event
        const remoteUserId = remoteStream.getUserId()
        console.log('stream-subscribed userId: ', remoteUserId)
        this.remoteStreamList.push(remoteStream)
        this.$nextTick(() => {
          this.playRemoteStream(remoteStream, remoteUserId)
        })
      })
      // fired when the remote stream is removed, e.g. the remote user called Client.unpublish()
      this.client.on('stream-removed', (event) => {
        const { stream: remoteStream } = event
        remoteStream.stop()
        const index = this.remoteStreamList.indexOf(remoteStream)
        if (index >= 0) {
          this.remoteStreamList.splice(index, 1)
        }
        console.log(`stream-removed userId: ${remoteStream.getUserId()} type: ${remoteStream.getType()}`)
      })

      this.client.on('stream-updated', (event) => {
        const { stream: remoteStream } = event
        console.log(`type: ${remoteStream.getType()} stream-updated hasAudio: ${remoteStream.hasAudio()} hasVideo: ${remoteStream.hasVideo()}`)
      })

      this.client.on('mute-audio', (event) => {
        const { userId } = event
        console.log(`${userId} mute audio`)
      })
      this.client.on('unmute-audio', (event) => {
        const { userId } = event
        console.log(`${userId} unmute audio`)
      })
      this.client.on('mute-video', (event) => {
        const { userId } = event
        console.log(`${userId} mute video`)
      })
      this.client.on('unmute-video', (event) => {
        const { userId } = event
        console.log(`${userId} unmute video`)
      })

      this.client.on('connection-state-changed', (event) => {
        console.log(`RtcClient state changed to ${event.state} from ${event.prevState}`)
      })

      this.client.on('network-quality', (event) => {
        const { uplinkNetworkQuality, downlinkNetworkQuality } = event
        console.log(`network-quality uplinkNetworkQuality: ${uplinkNetworkQuality}, downlinkNetworkQuality: ${downlinkNetworkQuality}`)
      })
    }
  }
}
