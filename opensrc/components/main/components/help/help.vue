<template>
  <div class="container">
    <Poptip
      v-model="ui.showPoptip"
      popper-class="help-poptip"
      trigger="hover"
      placement="bottom-end"
      padding="12px 4px 4px 12px"
      :offset="30"
      transfer
    >
      <Icon type="iconfont icon-bangzhu" class="icon" :class="(isAgent || isVisit) ? 'black-icon' : 'white-icon'" />
      <div class="unread"></div>

      <template #content>
        <div class="contains">
          <div class="title">
            帮助
          </div>

          <ul class="list">
            <li @click="openGuide">
              <img src="@/assets/images/guide.png" alt="">
              <span>新手引导</span>
            </li>
            <li v-if="!localDeploy" @click="openFeedback">
              <img src="@/assets/images/feedback.png" alt="">
              <span>问题反馈</span>
            </li>
            <li @click="openUrl('tips')">
              <img src="@/assets/images/tips.png" alt="">
              <span>{{ $t('collection') }}小课堂</span>
            </li>
          </ul>
        </div>
      </template>
    </Poptip>

    <Modal
      v-model="ui.showGuide"
      :footer-hide="true"
      :mask-closable="false"
      :closable="false"
      :width="716"
      class-name="guide-modal"
    >
      <template #header>
        <div class="header">
          <p>安米智能  一站式{{ $t('collection') }}管理智能解决方案</p>
          <Icon class="close" type="iconfont icon-shanchu1" :size="24" color="#636C78" @click="closeGuide" />
        </div>
      </template>
      <div class="carousel">
        <!-- 图片 -->
        <div class="imgs">
          <div class="img">
            <img
              v-for="(item, index) in picList"
              v-show="ui.current === index"
              :key="index"
              :src="item.img"
              alt=""
            >
          </div>
          <div class="left" @click="turn('last')">
            <Icon type="ios-arrow-forward" :size="16" color="#fff" />
          </div>
          <div class="right" @click="turn('next')">
            <Icon type="ios-arrow-forward" :size="16" color="#fff" />
          </div>
        </div>
        <!-- 指示器 -->
        <ul class="dots">
          <li v-for="(item, index) in picList" :key="index" :class="{ current: ui.current === index }" @click="turn(index)"></li>
        </ul>
        <!-- 文字信息 -->
        <template v-if="picList.length">
          <div v-for="(item, index) in picList" v-show="ui.current === index" :key="index" class="info">
            <p class="title">
              <span class="title">
                {{ item.desc.title }}
              </span>
              <span class="sub">
                {{ item.desc.subTitle }}
              </span>
            </p>
            <div class="content">
              <div v-for="(v, idx) in item.desc.content" :key="idx" v-html="v"></div>
            </div>
          </div>
        </template>
      </div>
    </Modal>

    <Modal
      v-model="ui.showFeeds"
      :footer-hide="true"
      :mask-closable="false"
      :closable="false"
    >
      <template #header>
        <div class="fees-header">
          <p class="title">
            问题反馈
          </p>
          <span class="history" @click="showHistory">
            <Icon type="iconfont icon-shijian" :size="16" color="#FF4F1F" />
            查看历史反馈
          </span>
        </div>
      </template>

      <Alert type="warning" class="alert no-border no-border-radius" show-icon>
        <template #icon>
          <Icon color="#FAAD14" :size="16" type="ios-alert" />
        </template>
        如您对系统使用有疑问，可查看 <a @click="openUrl('help')">
          常见问题
        </a>
      </Alert>

      <Form ref="forms" :model="forms" :rules="rules" :label-width="88">
        <FormItem label="问题类型：" prop="type">
          <RadioGroup v-model="forms.type">
            <Radio :label="0" style="margin-right: 30px;">
              功能建议
            </Radio>
            <Radio :label="1">
              问题反馈
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="问题描述：" prop="content">
          <Input v-model.trim="forms.content" type="textarea" placeholder="请描述遇到的问题和建议" :maxlength="500" show-word-limit :rows="3"></Input>
        </FormItem>
        <FormItem label="上传附件：" prop="files">
          <Upload ref="uploads" action="" :show-upload-list="false" :before-upload="beforeUploadAssistFile">
            <Button icon="iconfont icon-fujian1" :loading="ui.uploadLoading">
              {{ $t('uploadFiles') }}
            </Button>
          </Upload>
          <div class="tips">
            * 上传文件大小总共不能超过100M
          </div>
          <div v-if="forms.files.length" class="files">
            <span v-for="(item, index) in forms.files" :key="item">
              <label style="white-space:nowrap;">
                <Icon type="iconfont icon-wenjian" :size="16" color="#636C78" style="margin-right: 4px;" />
                {{ item.name }}
                <Icon type="iconfont icon-shanchu1" :size="16" color="#636C78" style="margin-left: 4px; cursor: pointer;" @click="deleteFile(index)" /></label>
            </span>
          </div>
        </FormItem>
        <FormItem label="联系方式：" prop="contact">
          <Input v-model.trim="forms.contact" placeholder="请输入联系方式" :maxlength="50"></Input>
        </FormItem>
        <FormItem class="btns">
          <Button style="margin-right: 8px;" @click="handleCancel">
            取消
          </Button>
          <Button type="primary" :loading="ui.loading" @click="handleSubmit">
            确定
          </Button>
        </FormItem>
      </Form>
    </Modal>

    <Modal
      v-model="ui.showHistory"
      :footer-hide="true"
      :mask-closable="false"
      title="历史反馈"
      @on-visible-change="historyChange"
    >
      <Tables
        v-if="ui.showHistory"
        ref="mainTable"
        :columns="columns"
        :apifun="apifun"
        :height="400"
        pageable
      ></Tables>
    </Modal>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { addIssue, getIssue } from '@/api/helpCenter'
import { dataURItoBlob, getImgInfo } from '@/libs/util'
import Tables from '_c/tables/tables.1'

export default {
  components: {
    Tables
  },
  data() {
    return {
      forms: {
        type: 0,
        content: '',
        files: [],
        contact: ''
      },
      rules: {
        type: [
          { required: true, message: '问题类型不能为空', trigger: 'change', type: 'number' }
        ],
        content: [
          { required: true, message: '问题描述不能为空', trigger: 'blur' }
        ],
        files: [
          { required: false, message: '附件不能为空', trigger: 'change', type: 'array' }
        ],
        contact: [
          { required: false, message: '联系方式不能为空', trigger: 'blur' }
        ]
      },
      columns: [
        { title: '反馈时间', key: 'createTime', bindmap: 'formatDate', width: 150 },
        { title: '问题类型', key: 'type', bindmap: 'feedbackType', width: 90 },
        { title: '问题描述', key: 'content' }
      ],
      apifun: {
        get: getIssue
      },
      ui: {
        showPoptip: false,
        showGuide: false,
        current: 0,
        showFeeds: false,
        loading: false,
        uploadLoading: false,
        showHistory: false
      }
    }
  },
  computed: {
    ...mapGetters([
      'isAgent',
      'isVisit',
      'isInspector',
      'myOrgId',
      'localDeploy'
    ]),
    picList() {
      let pic = []

      if (this.isAgent || this.isVisit || this.isInspector) {
        pic = [
          {
            img: new URL('../../../../assets/images/agent_1.png', import.meta.url).href,
            desc: {
              title: '在菜单栏选择我的案件，用于管理自己名下各类状态的案件',
              content: [
                `<p>「<strong>我的案件</strong>」下是全部分派到${this.$t('agent')}个人名下的${this.$t('collection')}案件，案件类型分为：<strong>进行中案件</strong>、<strong>小组案件</strong>、<strong>未开始案件</strong>、<strong>已结案案件</strong>、<strong>已失效案件</strong>。</p>`
              ]
            }
          }, {
            img: new URL('../../../../assets/images/agent_2.png', import.meta.url).href,
            desc: {
              title: '案件详情查看以及相关操作',
              content: [
                `<p>1.案件详情信息展示：如${this.$t('obligor')}个人基本信息、通讯录、${this.$t('collection')}记录、${this.$t('obligor')}共债案件、联系地址、还款记录等；</p>`,
                `<p>2.案件功能操作：添加案件标签、申请${this.$t('coordinate')}、申请修改案件状态、添加标色等；</p>`,
                `<p>3.案件联系人：用于拨打电话、填写${this.$t('reminder')}等操作。</p>`
              ]
            }
          }, {
            img: new URL('../../../../assets/images/agent_3.png', import.meta.url).href,
            desc: {
              title: `拨打电话以及填写${this.$t('reminder')}`,
              content: [
                `<p>1.在案件联系人列表选择联系人可以拨打电话并且填写${this.$t('reminder')}，${this.$t('reminder')}可填写内容包括${this.$t('collection')}进程、${this.$t('collection')}结果、电话结果、备注等信息；</p>`,
                '<p>2.各类信息选择项可根据业务需求由管理员配置不同内容。</p>'
              ]
            }
          }, {
            img: new URL('../../../../assets/images/agent_4.png', import.meta.url).href,
            desc: {
              title: '案件相关功能申请操作',
              content: [
                `<p>案件状态变更申请：如需变更案件状态，可提交申请至管理员审核，审核结果可在我的案件-申请记录进行查看。${this.$t('coordinate')}申请：${this.$t('agent')}可申请案件由其他${this.$t('agent')}进行协作${this.$t('collection')}。</p>`,
                '<p>还款减免申请：如有还款或者减免，可在案件详情页发起还款或减免申请至管理员审核。</p>'
              ]
            }
          }
        ]
      } else {
        pic = [
          {
            img: new URL('../../../../assets/images/manage_1.png', import.meta.url).href,
            desc: {
              title: '账号配置：',
              subTitle: `通过账号管理为员工创建不同角色类型的账号，包括管理员、${this.$t('agent')}、外访员、质检员等`,
              content: [
                '<p>1.进入「<strong>账号管理-角色管理</strong>」可添加角色和配置权限，系统已默认配置几个常用角色；</p>',
                '<p>2.进入「<strong>账号管理-组织架构-新增员工</strong>」完成账号创建。</p>'
              ]
            }
          }, {
            img: new URL('../../../../assets/images/manage_2.png', import.meta.url).href,
            desc: {
              title: '基础数据配置：',
              subTitle: `案件导入前，需先添加${this.$t('baileCompany')}和${this.$t('k_deltProduct')}`,
              content: [
                `<p>1.进入「<strong>数据管理-${this.$t('baile')}管理-${this.$t('baileCompany')}</strong>」可新增${this.$t('baileCompany')}，不同${this.$t('baileCompany')}用于不同项目数据导入以及查询；</p>`,
                `<p>2.${this.$t('baileCompany')}添加完后，可进入「<strong>数据管理-${this.$t('baile')}管理-${this.$t('k_deltProduct')}</strong>」在${this.$t('baileCompany')}下添加不同的${this.$t('k_deltProduct')}。</p>`
              ]
            }
          }, {
            img: new URL('../../../../assets/images/manage_3.png', import.meta.url).href,
            desc: {
              title: '创建数据模版：',
              subTitle: '案件导入前需创建案件导入模版以匹配不同的案件数据来源',
              content: [
                `<p>1.模板用于案件相关数据的导入和导出。模板类型包括<strong>案件导入</strong>和<strong>更新模板</strong>、<strong>${this.$t('reminder')}导出模板</strong>、<strong>案件导出模板</strong>等；</p>`,
                '<p>2.进入「<strong>数据管理-模板管理</strong>」选择自定义模版，可进行模版的新增和编辑。通过系统提供的案件字段和自定义案件字段配置成合适的案件导入模版。</p>'
              ]
            }
          }, {
            img: new URL('../../../../assets/images/manage_4.png', import.meta.url).href,
            desc: {
              title: '案件导入：',
              subTitle: '下载配置好的案件导入模版，填入对应的案件数据，即可将案件导入到系统中',
              content: [
                '<p>1.进入「<strong>数据管理-案件管理-案件导入</strong>」，选择案件导入模版下载，在excel文件中根据要求填入案件数据；</p>',
                `<p>2.填入当前批次案件相关信息，包括${this.$t('client')}，${this.$t('k_deltProduct')}、批次号、${this.$t('baile')}时间等信息；</p>`,
                '<p>3.上传excel文件提交即可导入案件，如果案件数据格式错误或者其他原因错误，可在导入记录中查看错误文件修改后重新导入。</p>'
              ]
            }
          }, {
            img: new URL('../../../../assets/images/manage_5.png', import.meta.url).href,
            desc: {
              title: '案件管理：',
              subTitle: `案件导入后，支持分案、案件状态变更、智能${this.$t('coordinate')}等操作`,
              content: [
                '<p>1.进入「<strong>数据管理-案件列表</strong>」管理所有系统中的案件，包括待分配案件、已分配案件、删除案件等；</p>',
                '<p>2.案件分配支持智能分案和策略分案两种分案模式；</p>',
                `<p>3.智能${this.$t('coordinate')}操作，包括短信${this.$t('collection')}、机器人${this.$t('collection')}、预测式外呼；</p>`,
                `<p>4.分配至${this.$t('agent')}的案件，案件状态变发生变更时可批量修改。</p>`
              ]
            }
          }, {
            img: new URL('../../../../assets/images/manage_6.png', import.meta.url).href,
            desc: {
              title: `${this.$t('collection')}管理：`,
              subTitle: `案件${this.$t('collection')}过程中产生的数据管理，包括${this.$t('reminder')}管理、还款管理、减免管理以及业务过程中相关审批`,
              content: [
                `<p>1.进入「<strong>${this.$t('collection')}管理-${this.$t('reminder')}管理-${this.$t('reminder')}列表</strong>」可查看${this.$t('collection')}过程产生的全部${this.$t('reminder')}；</p>`,
                `<p>2.进入「<strong>${this.$t('collection')}管理-还款管理-还款列表</strong>」可查看案件的全部还款数据；</p>`,
                '<p>3.还款、减免、案件状态变更都可通过此模块进行相应的审批流程操作。</p>'
              ]
            }
          }, {
            img: new URL('../../../../assets/images/manage_7.png', import.meta.url).href,
            desc: {
              title: '系统配置：',
              subTitle: '可以根据业务需求配置系统功能属性以及安全相关属性',
              content: [
                `<p>1.系统属性设置：如逾期天数递增计算、共债判断、${this.$t('collection')}信息设置、标签管理、${this.$t('coordinate')}模式、团队${this.$t('collection')}设置等；</p>`,
                '<p>2.系统安全设置：基于信息安全要求，设置信息脱敏规则、限制IP访问、水印、密码格式等。</p>'
              ]
            }
          }
        ]
      }

      return pic
    }
  },
  methods: {
    openUrl(type) {
      this.ui.showPoptip = false
      let url
      switch (type) {
        case 'help':
          url = 'https://anmi-docs.duyansoft.com/'
          break
        case 'update':
          url = 'https://anmi-update.duyansoft.com/'
          break
        case 'tips':
          url = 'https://mp.weixin.qq.com/mp/appmsgalbum?__biz=Mzg2NzEwNjA1NA==&action=getalbum&album_id=2293354312137392134#wechat_redirect'
          break
      }

      window.open(url)
    },
    openGuide() {
      this.ui.showPoptip = false
      this.ui.showGuide = true
    },
    closeGuide() {
      this.ui.showGuide = false
    },
    turn(type) {
      if (!['next', 'last'].includes(type)) {
        this.ui.current = type
        return
      }

      this.ui.current = type === 'next' ? (this.ui.current + 1) : (this.ui.current - 1)

      if (this.ui.current === this.picList.length) {
        this.ui.current = 0
      } else if (this.ui.current === -1) {
        this.ui.current = this.picList.length - 1
      }
    },
    openFeedback() {
      this.ui.showPoptip = false
      this.ui.showFeeds = true
    },
    handleCancel() {
      this.ui.showFeeds = false
      this.$refs.forms.resetFields()
      this.forms = {
        type: 0,
        content: '',
        files: [],
        contact: ''
      }
    },
    async beforeUploadAssistFile(file, _callback) {
      if (this.ui.uploadLoading) return

      if (this.forms.files.length >= 3) {
        this.$Message.error('最多上传3个附件')
        return Promise.reject(new Error(false))
      }

      this.ui.uploadLoading = true
      let size = 0
      for (const key in this.forms.files) {
        if (file.name === this.forms.files[key].name) {
          this.$Message.error('文件名重复，请更改文件名后上传')
          this.ui.uploadLoading = false
          return Promise.reject(new Error(false))
        }
        size = size + this.forms.files[key].size
      }

      size = size + file.size

      if (size > 100 * 1024 * 1024) {
        this.$Message.error('上传文件大小总共不能超过100M')
        this.ui.uploadLoading = false
        return Promise.reject(new Error(false))
      }

      const fileType = file.type
      if (fileType.indexOf('image') !== -1) {
        const img = await getImgInfo(file)
        if (img.w > 1500) {
          var image = document.createElement('img')
          image.src = img.pic
          var canvas = document.createElement('canvas')
          var imgWidth = img.w
          var imgHeight = img.h
          var fullWidth = imgWidth
          var fullHeight = imgHeight
          if (fullWidth > 1500) {
            var rate = fullHeight / fullWidth
            var originWidth = fullWidth
            fullWidth = 1500
            fullHeight = 1500 * rate
            rate = fullWidth / originWidth
            imgWidth = imgWidth * rate
            imgHeight = imgHeight * rate
          }
          canvas.setAttribute('width', fullWidth)
          canvas.setAttribute('height', fullHeight)
          var g = canvas.getContext('2d')
          g.fillStyle = '#fff'
          g.fillRect(0, 0, canvas.width, canvas.height)
          g.translate(fullWidth / 2, fullHeight / 2)
          g.drawImage(image, -imgWidth / 2, -imgHeight / 2, imgWidth, imgHeight)
          this.forms.files.push(new File([dataURItoBlob(canvas.toDataURL('image/jpeg'))], file.name, { type: 'image/jpeg', lastModified: Date.now() }))
        } else {
          this.forms.files.push(file)
        }
      } else {
        this.forms.files.push(file)
      }

      this.ui.uploadLoading = false
      await Promise.reject(new Error(false))
      _callback && _callback()
    },
    deleteFile(index) {
      this.forms.files.splice(index, 1)
    },
    handleSubmit() {
      this.$refs.forms.validate((valid) => {
        if (valid) {
          const sendData = { ...this.forms }
          sendData.orgId = this.myOrgId

          const formData = new FormData()
          for (const key in sendData) {
            if (key === 'files') {
              for (const k in sendData[key]) {
                formData.append('files', sendData[key][k])
              }
            } else {
              formData.append(key, sendData[key])
            }
          }

          this.ui.loading = true
          addIssue(formData).then(res => {
            if (res.success) {
              this.handleCancel()
            }
          }).finally(() => {
            this.ui.loading = false
          })
        }
      })
    },
    showHistory() {
      this.ui.showFeeds = false
      this.ui.showHistory = true
    },
    historyChange(bool) {
      if (!bool) {
        this.ui.showHistory = false
        this.ui.showFeeds = true
      } else {
        if (this.$refs.mainTable) {
          this.$refs.mainTable.handleSearch()
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
    width: 40px;
    height: 100%;
    position: relative;

    .icon {
        position: absolute;
        top: -14px;
        font-size: 20px;
        cursor: pointer;
    }
    .unread {
        width: 6px;
        height: 6px;
        background-color: #FF1A2E;
        border-radius: 50%;
        position: absolute;
        right: -18px;
        top: -14px;
    }
    .black-icon {
        color: #999797;
    }
    .white-icon {
        color: #9DA3AC;
    }
    &:hover {
        .icon {
            transform: scale(1.3);
        }
        .unread {
            transform: scale(1.3);
            top: -16px;
            right: -20px;
        }
        .black-icon {
            color: #DADADA;
        }
        .white-icon {
            color: #8C929B;
        }
    }
    &:active {
        .black-icon {
            color: #999797;
        }
        .white-icon {
            color: #636C78;
        }
    }
}
.contains {
    width: 236px;
    .title {
        font-weight: 600;
        line-height: 22px;
        margin-bottom: 8px;
    }
    .list {
        list-style-type: none;
        display: flex;
        flex-wrap: wrap;
        >li {
            width: 110px;
            height: 42px;
            background-color: #F2F4F7;
            border-radius: 4px;
            margin-right: 8px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            padding: 12px 10px;
            cursor: pointer;
            &:hover {
                background-color: #E3E6E9;
            }
            >img {
                width: 18px;
                height: 18px;
                margin-right: 6px;
            }
            >span {
                line-height: 22px;
            }
        }
    }
}
.header {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin: 6px 0;
    >p {
        text-align: center;
        margin-left: 18px;
        font-size: 18px;
    }
    >i {
        cursor: pointer;

        &:hover {
            color: #828EA0;
        }
        &:active {
            color: #505862;
        }
        &:disabled {
            color: #CCC;
        }
    }
}
.carousel {
    height: 595px;
    >.imgs {
        position: relative;
        width: 668px;
        height: 396px;
        background: linear-gradient(326deg, rgba(251, 248, 248, 0.5) 0%, rgba(251, 240, 240, 0.5) 100%), linear-gradient(155deg, rgba(255, 255, 255, 0) 0%, rgba(218, 101, 175, 0.08) 100%);
        border-radius: 8px;
        margin: 8px 8px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        >.img {
            display: flex;
            width: 648px;
            height: 364px;
            overflow: hidden;
            >img {
                width: 648px;
                height: 364px;
            }
        }
        >.left, >.right {
            width: 26px;
            height: 26px;
            background-color: rgba(0, 0, 0, 0.42);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            cursor: pointer;
        }
         >.left:hover, >.right:hover{
           background-color: rgba(0, 0, 0, 0.3);
         }
          >.left:active, >.right:active{
           background-color: rgba(0, 0, 0, 0.52);
         }
        >.left {
            left: -10px;
            >i {
                transform: rotate(180deg);
                margin-right: 2px;
            }
        }
        >.right {
            right: -10px;
            >i {
                margin-left: 2px;
            }
        }
    }
    >.dots {
        list-style-type: none;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        >li {
            width: 6px;
            height: 6px;
            background-color: #CCC;
            border-radius: 50%;
            margin-right: 4px;
            cursor: pointer;
            &:last-child {
                margin-right: 0;
            }
        }
        >.current {
            background-color: @primary-color;
            width: 22px;
            height: 6px;
            border-radius: 3px;
            animation: change 0.2s ease;
        }
        @keyframes change {
            0% {
                width: 6px;
                background-color: #CCC;
                border-radius: 50%;
            }
            100% {
                background-color: @primary-color;
                width: 22px;
                border-radius: 3px;
            }
        }
    }
    >.info {
        margin-bottom: 10px;
        padding: 0 8px;
        >.title {
            margin-bottom: 10px;
            >.title {
                font-size: 13px;
                font-weight: 600;
            }
            >.sub {
                font-size: 13px;
            }
        }
        >.content {
            padding: 12px;
            background-color: #F2F4F7;
            border-radius: 6px;
            >div {
                 :deep(p) {
                    color: #636C78;
                    line-height: 22px;
                }
            }
        }
    }
}
.fees-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    >.title {
        font-size: 18px;
        font-weight: 600;
    }
    >.history {
        color: @primary-color;
        white-space: nowrap;
        cursor: pointer;
        >i {
            margin-right: 2px;
        }
    }
}
.alert {
    margin: -16px -16px 16px;
    background-color: #fff3eb;
    >a {
        text-decoration: underline;
        color: @primary-color;
    }
}
.tips {
    color: #636C78;
    margin-top: 10px;
    line-height: 17px;
}
.files {
    margin-top: 16px;
    >span {
        background-color: #F2F4F7;
        border-radius: 4px;
        display: inline-block;
        padding: 8px 12px;
        line-height: 17px;
        margin-right: 10px;
        margin-bottom: 10px;
    }
}
 :deep(.ivu-modal-wrap.guide-modal .ivu-modal){
    top: 60px;
}
 :deep(.ivu-modal-wrap.guide-modal .ivu-modal-body){
    padding: 10px 16px 0 16px;
}
</style>
