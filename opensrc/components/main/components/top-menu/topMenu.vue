<template>
  <ul v-if="navList.length" class="list">
    <template v-for="(item, index) in navList">
      <li v-if="index<5" :key="index" :class="{ 'active-route': $route.path === item.path }" @click="handleTurnTo(item)">
        <Icon class="navlist-icon" :type="item.meta.icon" />
        <p class="title">
          {{ item.meta.title }}
        </p>
      </li>
    </template>

    <template v-if="moreNavList.length">
      <Poptip v-model="ui.showPoptip" trigger="click" placement="bottom-start" transfer padding="12px 4px 4px 12px" transfer-class-name="more-navlist">
        <li v-if="!moreBtn.path">
          <Icon class="navlist-icon" type="iconfont icon-gengduo1" />
          <p class="title">
            更多
          </p>
        </li>
        <li v-else :class="{ 'active-route': $route.path === moreBtn.path }">
          <Icon class="navlist-icon" :type="moreBtn.meta.icon" />
          <p class="title">
            {{ moreBtn.meta.title }}
          </p>
        </li>

        <template #content>
          <div class="more-navlist-pop-content">
            <p class="title">
              更多功能
            </p>
            <ul class="list">
              <li v-for="(item, index) in moreNavList" :key="index" :class="{ 'active-route': $route.path === item.path }" @click="handleClick(item)">
                <Icon class="navlist-icon" :type="item.meta.icon" />
                <p class="title">
                  {{ item.meta.title }}
                </p>
              </li>
            </ul>
          </div>
        </template>
      </Poptip>
    </template>
  </ul>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    list: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      navList: [],
      moreNavList: [],
      moreBtn: {},
      ui: {
        showPoptip: false
      }
    }
  },
  computed: {
    ...mapGetters([
      'isOutbounceLock',
      'isChangeCase',
      'mediating'
    ])
  },
  mounted() {
    this.$nextTick(() => {
      if (this.list.length) {
        for (const item of this.list) {
          if (item.meta && item.meta.isTopMenu) {
            this.navList.push(item)
          }
        }

        if (this.navList.length > 5) {
          this.moreNavList = this.navList.slice(5)
          this.navList.length = 5
        }

        if (localStorage.getItem('moreNav')) {
          this.moreBtn = JSON.parse(localStorage.getItem('moreNav'))
        }
      }
    })
  },
  methods: {
    handleTurnTo(item) {
      if (this.isOutbounceLock) return
      if (this.isChangeCase) return
      if (item.path === this.$route.path) return
      if (this.mediating) return this.$Message.error('请先结束当前正在进行的视频调解后才能进行操作')
      this.turnToPage(item)
    },
    turnToPage(route) {
      let { name, params, query } = {}
      if (typeof route === 'string') name = route
      else {
        name = route.name
        params = route.params
        query = route.query
      }
      if (name.indexOf('isTurnByHref_') > -1) {
        window.open(name.split('_')[1])
        return
      }
      this.$router.push({
        name,
        params,
        query
      })
    },
    handleClick(item) {
      if (this.isOutbounceLock) return
      if (this.isChangeCase) return

      this.moreBtn = { ...item }
      localStorage.setItem('moreNav', JSON.stringify(item))

      this.turnToPage(item)
    }
  }
}
</script>

<style lang="less" scoped>
@import './topMenu.less';
</style>
