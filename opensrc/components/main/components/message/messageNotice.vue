<template>
  <div class="notice-container">
    <div class="title">
      <Icon type="iconfont icon-jishixiaoxi" class="icon" />
      <span>您有一条新消息</span>
    </div>
    <div class="notice-content">
      <div class="left">
        <div class="name">{{ fromUserName }}</div>
        <div class="message">{{ subject }}</div>
      </div>
      <div class="time">{{ formatDate(createTime) }}</div>
    </div>
  </div>
</template>

<script>
import { formatDate } from '@/libs/util'

export default {
  name: '',
  components: {},
  mixins: [],
  props: {
    fromUserName: {
      type: String,
      default: ''
    },
    createTime: {
      type: Number,
      default: new Date()
    },
    subject: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    formatDate(val) {
      return formatDate(val, 'hh:mm')
    }
  }
}
</script>

<style scoped lang="less">
.notice-container {
  .title {
    display: flex;
    align-items: center;
    > span {
      width: 112px;
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #282828;
      line-height: 20px;
      margin-left: 8px;
    }
  }

  .icon {
    width: 22px;
    font-size: 16px;
    height: 22px;
    color: #ff4f1f;
    border-radius: 50%;
    background-color: rgba(255, 79, 31, 0.18);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .notice-content {
    display: flex;
    margin-top: 13px;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    .left{
        display: flex;
    }
    .name {
      font-size: 12px;
      font-weight: 500;
      color: #282828;
      margin-right: 10px;
      max-width: 60px;
      white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      &:hover{
        color: #ff4f1f;
      }
    }
    .message{
        font-size: 12px;
        font-weight: 400;
        color: #282828;
        max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .time{
        font-size: 12px;
        color: #8D959F;
    }
  }
}
</style>
