.user{
  &-avator-dropdown{
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
  }
}

.black-user {
  .name {
    color: rgba(255, 255, 255, 0.65);

    &:hover {
      color: #fff;
    }
  }
  .foot {
    color: rgba(255, 255, 255, 0.65);
  }
}
.white-user {
  .name {
    color: #636C78;
  }
  .foot {
    color: #636C78;
  }
}
#userDropdown{
  position: relative;
  .name {
    position: relative;
    top: -8px;
    right: 0;
    min-width: 102px;
    max-width: 130px;
    text-align: right;
    white-space: nowrap;
    overflow: hidden;
    text-overflow:ellipsis;
    padding-right: 18px;
    height: 50px;
  }
  .iconSty {
    position: absolute;
    right: 0;
    top: 14px;
  }
  .foot{
    position: absolute;
    height: 20px;
    line-height: 20px;
    bottom: -4px;
    width: 186px;
    right: 0;
    display: flex;
    justify-content: flex-end;

    >.time {
      >i {
        margin-right: 2px;
      }
    }
    >.lang{
      margin-left: 8px;
      a{
        color: rgb(81, 90, 110) !important;
      }
    }
  }
}

.user-dropdown {
  width: 200px;
  max-height: 500px !important;
  top: 45px !important;
  .dropsdown {
    .infos-item.ivu-dropdown-item.ivu-dropdown-item-disabled {
      padding: 0;
      margin-top: -6px;
    }
    .sub-title.ivu-dropdown-item.ivu-dropdown-item-disabled {
      margin-top: 3px;
      &:hover {
        background-color: #fff;
      }
    }
    .info {
      background: linear-gradient(229deg, #EDE7DC 0%, #F9F3EC 100%);
      padding: 12px 12px 10px;
      width: 100%;
      border-radius: 4px 4px 0px 0px;
      >.names {
        color: @text-color;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
        white-space: normal;
      }
      >.staff {
        font-size: 12px;
        color: #636C78;
        white-space: normal;
      }
      >.org {
        color: @text-color;
        font-size: 12px;
        font-weight: 400;
        line-height: 17px;
        margin-top: 4px;
        white-space: normal;
      }
      >.version {
        color: #636C78;
        font-size: 12px;
        font-weight: 400;
        line-height: 17px;
        margin-top: 4px;
      }
    }
    .role-item {
      padding-left: 34px;
      white-space: normal;
    }
    .border-item {
      border-bottom: 1px solid #DCDEE3;
      margin-bottom: 4px;
    }
    .opt {
      >.item {
        >i {
          margin-right: 8px;
        }
      }
    }
    .drop-opt {
      display: flex;
      align-items: center;
      i {
        font-size: 16px;
        margin-right: 8px;
        color: #636C78;

        &.icon-xiugaimima {
          margin-left: -1px;
        }
      }
    }
    .ivu-dropdown-item:hover {
      background-color: #F3F4F8;
    }
  }
}
