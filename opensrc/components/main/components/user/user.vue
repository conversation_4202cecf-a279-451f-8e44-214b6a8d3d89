<template>
  <div id="userDropdown" class="user-avator-dropdown" :class="(isAgent || isVisit) ? 'black-user':'white-user'">
    <Dropdown transfer-class-name="user-dropdown" transfer @on-visible-change="visibleChange">
      <div class="name">
        hi, {{ userName }}
        <Icon :size="18" type="iconfont icon-xiala" class="iconSty" :class="{'turn-around': ui.openDrop}"></Icon>
        <div class="foot">
          <div class="time">
            <Icon type="iconfont icon-shijian" :size="16" /> {{ ui.time }}
          </div>
          <language v-if="$config.useI18n" class="lang" :lang="local" @on-lang-change="setLocal" />
        </div>
      </div>
      <template #list>
        <DropdownMenu class="dropsdown">
          <DropdownItem class="infos-item" disabled>
            <div class="info">
              <p class="names">
                {{ userName }}
              </p>
              <p class="staff">编号：{{ userNo }}</p>
              <p class="org">
                {{ orgName }}
              </p>
              <p class="version">
                版本：{{ version }}
              </p>
            </div>
          </DropdownItem>
          <DropdownItem disabled class="sub-title">
            - {{ $t('switchRole') }} -
          </DropdownItem>
          <DropdownItem v-for="(role, index) in userRole" :key="role.id" class="role-item" :name="role.id" :class="{ 'border-item': index === userRole.length-1 }" @click.native="doSwitchRole(role)">
            {{ role.name }}
          </DropdownItem>
          <DropdownItem class="drop-opt" name="changepw" @click.native="openModal">
            <Icon type="iconfont icon-xiugaimima" /> {{ $t('chgPwd') }}
          </DropdownItem>
          <DropdownItem class="drop-opt" name="logout" @click.native="logout">
            <Icon type="iconfont icon-tuichudenglu" /> {{ $t('signOut') }}
          </DropdownItem>
        </DropdownMenu>
      </template>
    </Dropdown>

    <Modal
      v-model="ui.modal"
      :footer-hide="true"
      :mask-closable="false"
      :title="$t('chgPwd')"
      :closable="false"
    >
      <login-form ref="loginform" is-changepwd @on-success-valid="changepwdDone" @on-cancel="cancelPwd"></login-form>
    </Modal>
  </div>
</template>

<script>
import {
  setTagNavListInLocalstorage,
  setUserInLocalstorage,
  getImageUuid
} from '@/libs/util'

import {
  getDate
} from '@/libs/tools'

import { mapGetters, mapActions, mapMutations } from 'vuex'
import LoginForm from '_c/login-form'
import Language from '../language'

import { changePwd, inspectorLogin, logout } from '@/api/user'

export default {
  name: 'User',
  components: {
    LoginForm,
    Language
  },
  props: {
    userAvator: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ui: {
        modal: false,
        time: '',
        openDrop: false
      },
      version: anmiVersion
    }
  },
  computed: {
    ...mapGetters([
      'userRoles',
      'currentRole',
      'userName',
      'userNo',
      'isAgent',
      'isVisit',
      'userId',
      'orgName'
    ]),
    local() {
      return this.$store.state.app.local
    },
    userRole() {
      return this.userRoles.filter(c => (this.currentRole && this.currentRole.id !== c.id))
    }
  },
  methods: {
    ...mapMutations([
      'setCurrentRole',
      'refreshVcodeImg',
      'setLocal'
    ]),
    ...mapActions([
      'handleLogOut'
    ]),
    changepwdDone(backData) {
      changePwd({
        id: this.userId,
        newPassword: backData.password,
        oldPassword: backData.oldPassword,
        validateCode: backData.validateCode,
        imageUuid: getImageUuid()
      }).then((res) => {
        if (res) {
          this.ui.modal = false
          this.$refs.loginform.formCancel()
        }
        this.refreshVcodeImg()
      })
    },
    cancelPwd() {
      this.ui.modal = false
    },
    doSwitchRole(role) {
      setUserInLocalstorage({
        roleId: role.id,
        userId: this.userId
      })
      setTagNavListInLocalstorage(null)
      window.location.href = '/home'
    },
    logout() {
      logout()
      this.handleLogOut()
    },
    openModal() {
      this.refreshVcodeImg()
      this.ui.modal = true
    },
    visibleChange(visible) {
      this.ui.openDrop = visible
    }
  },
  mounted() {
    const runTimer = () => {
      const t = setTimeout(() => {
        const time = new Date().getTime() / 1000
        this.ui.time = getDate(time, null, 'second')
        clearTimeout(t)
        runTimer()
      }, 1000)
    }

    runTimer()
  }
}
</script>
<style lang="less">
@import './user.less';
</style>
