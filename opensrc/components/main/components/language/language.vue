<template>
  <div>
    <Dropdown trigger="click" @on-click="selectLang">
      <a href="javascript:void(0)">
        <Icon :size="12" type="md-globe" />
        {{ title }}
      </a>
      <DropdownMenu slot="list">
        <DropdownItem v-for="(value, key) in localList" :key="`lang-${key}`" :name="key">
          {{ value }}
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: 'Language',
  props: {
    lang: String
  },
  data() {
    return {
      langList: {
        'zh-CN': '语言',
        // 'zh-TW': '語言',
        'en-US': 'Language'
      },
      localList: {
        'zh-CN': '中文简体',
        // 'zh-TW': '中文繁体',
        'en-US': 'English'
      }
    }
  },
  computed: {
    title() {
      return this.langList[this.lang]
    }
  },
  watch: {
    lang(lang) {
      this.$i18n.locale = lang
    }
  },
  methods: {
    selectLang(name) {
      this.$emit('on-lang-change', name)
    }
  }
}
</script>
