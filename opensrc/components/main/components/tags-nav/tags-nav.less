.no-select{
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.size{
  width: 100%;
  height: 100%;
}
.tags-nav{
  position: relative;
  .no-select;
  .size;
  .close-con{
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 83px;
    background: #F2F4F7;
    text-align: center;
    z-index: 2;
    a.close-btn {
      color: #636C78;
      display: inline-flex;
      align-items: center;
      margin-top: 4px;
      >i {
        margin-left: 4px;
        font-size: 16px;
      }
    }
  }
  .btn-con{
    position: absolute;
    top: 0px;
    height: 100%;
    background: #F2F4F7;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 23px;
    >i {
      color: #636C78;
      font-size: 18px;
    }
    &.left-btn{
      left: 0px;
    }
    &.right-btn{
      right: 83px;
      border-right: 1px solid #DCDEE3;
    }

    .disabled-btn {
      color: #ccc;
    }
  }
  .scroll-outer{
    position: absolute;
    left: 23px;
    right: 106px;
    top: 0;
    bottom: 0;
    .scroll-body{
      height: ~"calc(100% - 1px)";
      display: inline-block;
      padding: 1px 4px 0;
      position: absolute;
      overflow: visible;
      white-space: nowrap;
      transition: left .3s ease;
      .ivu-tag-dot-inner{
        transition: background .2s ease;
      }
      .ivu-tag {
        border: none;
      }
    }
  }
  .contextmenu {
    position: absolute;
    margin: 0;
    padding: 5px 0;
    background: #fff;
    z-index: 100;
    list-style-type: none;
    border-radius: 4px;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);
    li {
      margin: 0;
      padding: 5px 15px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
  .nav-tag, .active-nav-color {
    margin-top: 3px;
    border-bottom: none;
    color: #636C78;

    .tool-content {
      display: flex;
      align-items: center;
      .nav-icon {
        color: #F75159;
        margin-right: 4px;
      }
      .nav-shipin{
        color: #26E0D6;
      }
    }
    .close-icon {
      color: #636C78;
      font-size: 18px;
    }
    .ivu-tag-text {
      >span {
        color: #636C78;
      }
    }
  }
  .nav-tag {
    border-radius: 0;

    &:hover {
      background-color: #F2F4F7;
    }
  }
  .active-nav-color {
    background-color: #fff;
    border-radius: 0;
    
    &:hover {
      background-color: #fff;
      opacity: 1;
    }
  }
}

.black-scroll {
  background-color: #DFE1E4;
  .nav-tag {
    background-color: #DFE1E4;
  }
}
.white-scroll {
  background-color: #EBECEF;
  .nav-tag {
    background-color: #EBECEF;
    ;
    border: none;
  }
}
