<template>
  <div class="tags-nav">
    <div class="close-con">
      <Dropdown v-show="!urgeRefund && !isVisit" transfer style="margin-top:7px;" @on-click="handleTagsOption" @on-visible-change="visibleChange">
        <a class="close-btn">
          关闭操作
          <Icon type="iconfont icon-xiala" :class="{'turn-around': ui.openDrop}" />
        </a>
       <template #list>
          <Dropdown>
            <DropdownMenu>
              <DropdownItem name="close-all">
                {{ $t('closeAll') }}
              </DropdownItem>
              <DropdownItem name="close-others">
                {{ $t('closeOthers') }}
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </template>
      </Dropdown>
    </div>
    <div class="btn-con left-btn">
      <Icon type="iconfont icon-zuoqiehuan" :class="{ 'disabled-btn': outerWidth <= bodyWidth }" @click="handleScroll(240)" />
    </div>
    <div class="btn-con right-btn">
      <Icon type="iconfont icon-youqiehuan" :class="{ 'disabled-btn': outerWidth <= bodyWidth }" @click="handleScroll(-240)" />
    </div>
    <div ref="scrollOuter" class="scroll-outer" :class="(isAgent || isVisit) ? 'black-scroll':'white-scroll'" @DOMMouseScroll="handlescroll" @mousewheel="handlescroll">
      <div ref="scrollBody" class="scroll-body" :style="{left: tagBodyLeft + 'px'}">
        <transition-group name="taglist-moving-animation">
          <Tag
            v-for="(item, index) in list"
            v-show="!item.isHide"
            ref="tagsPageOpened"
            :key="`tag-nav-${index}`"
            size="large"
            :name="item.name"
            :data-route-item="item"
            :class="getTagColor(item)"
            @click.native="handleClick(item)"
            @contextmenu.prevent.native="contextMenu(item, $event)"
          >
            <div v-if="(item.params && item.params.id == urgeRefund) || mediating">
              <Tooltip :content="`当前案件${$t('reminder')}暂未提交`" :disabled="!(item.params && item.params.id == urgeRefund)" transfer>
                <div class="tool-content">
                  <Icon v-if="item.params && item.params.id == urgeRefund" class="nav-icon" type="iconfont icon-cuiji" />
                  <Icon v-if="mediating" class="nav-icon nav-shipin" type="iconfont icon-shipin" />
                  <span>{{ showTitleInside(item) }}</span>
                  <Icon v-if="!item.meta.noCloseable && item.name !== $config.homeName" class="close-icon" type="iconfont icon-anjianguanbi" @click="handleClose(item)" />
                </div>
              </Tooltip>
            </div>
            <span v-else>
              {{ showTitleInside(item) }}
              <!-- .stop 阻止冒泡 -->
              <Icon v-if="!item.meta.noCloseable && item.name !== $config.homeName" class="close-icon" type="iconfont icon-anjianguanbi" @click.stop="handleClose(item)" />
            </span>
          </Tag>
        </transition-group>
      </div>
    </div>
  </div>
</template>

<script>
import { showTitle, routeEqual } from '@/libs/util'
import beforeClose from '@/router/before-close'
import { mapActions, mapGetters, mapMutations } from 'vuex'

export default {
  name: 'TagsNav',
  props: {
    value: Object,
    list: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      tagBodyLeft: 0,
      rightOffset: 40,
      outerPadding: 4,
      contextMenuLeft: 0,
      contextMenuTop: 0,
      visible: false,
      outerWidth: 0,
      bodyWidth: 0,
      menuList: {
        others: this.$t('closeOthers'),
        all: this.$t('closeAll')
      },
      ui: {
        openDrop: false
      }
    }
  },
  computed: {
    ...mapGetters([
      'isAgent',
      'urgeRefund',
      'duyanFifoAgent',
      'userExtend',
      'isVisit',
      'isOutsource',
      'mediating'
    ]),
    currentRouteObj() {
      const { name, params, query, meta } = this.value
      return { name, params, query, meta }
    }
  },
  methods: {
    ...mapMutations(['updateCtiCall']),
    getTagColor(item) {
      // eslint-disable-next-line
      return this.isCurrentTag(item) ? 'active-nav-color' : 'nav-tag'
    },
    handlescroll(e) {
      var type = e.type
      let delta = 0
      if (type === 'DOMMouseScroll' || type === 'mousewheel') {
        delta = (e.wheelDelta) ? e.wheelDelta : -(e.detail || 0) * 40
      }
      this.handleScroll(delta)
    },
    handleScroll(offset) {
      const outerWidth = this.outerWidth = this.$refs.scrollOuter.offsetWidth
      const bodyWidth = this.bodyWidth = this.$refs.scrollBody.offsetWidth

      if (offset > 0) {
        this.tagBodyLeft = Math.min(0, this.tagBodyLeft + offset)
      } else {
        if (outerWidth < bodyWidth) {
          if (this.tagBodyLeft < -(bodyWidth - outerWidth)) {
            this.tagBodyLeft = this.tagBodyLeft
          } else {
            this.tagBodyLeft = Math.max(this.tagBodyLeft + offset, outerWidth - bodyWidth)
          }
        } else {
          this.tagBodyLeft = 0
        }
      }
    },
    handleTagsOption(type) {
      this.tagBodyLeft = 0
      if (type.includes('all')) {
        if (this.mediating) return this.$Message.error('请先结束当前正在进行的视频调解后才能进行操作')
        // 关闭所有，除了home
        const res = this.list.filter(item => item.meta.noCloseable || item.name === this.$config.homeName)
        this.$emit('on-close', res, 'all')
      } else if (type.includes('others')) {
        // 关闭除当前页和home页的其他页
        const res = this.list.filter(item => item.meta.noCloseable || item.name === this.$config.homeName || routeEqual(this.currentRouteObj, item))
        this.$emit('on-close', res, 'others', this.currentRouteObj)
        setTimeout(() => {
          this.getTagElementByName(this.currentRouteObj.name)
        }, 100)
      }
    },
    visibleChange(visible) {
      this.ui.openDrop = visible
    },
    handleClose(current) {
      if (this.mediating) return this.$Message.error('请先结束当前正在进行的视频调解后才能操作')
      if (!this.urgeRefund) {
        this.close(current)
        return
      }
      if (current.params && Number(this.urgeRefund) === current.params.id) {
        this.$Modal.confirm({
          title: '关闭案件提示',
          content: `您未填写${this.$t('reminder')}，是否确定关闭此案件？（关闭案件默认不填写${this.$t('reminder')}）`,
          onOk: () => {
            this.updateCtiCall({ key: 'urgeRefund', data: false })
            this.close(current)
          }
        })
        return
      }
      if (current.meta && current.meta.beforeCloseName && current.meta.beforeCloseName in beforeClose) {
        new Promise(beforeClose[current.meta.beforeCloseName]).then(close => {
          if (close) {
            this.close(current)
          }
        })
      } else {
        this.close(current)
      }
    },
    close(route) {
      const res = this.list.filter(item => !routeEqual(route, item))
      this.$emit('on-close', res, undefined, route)
    },
    handleClick(item) {
      if (this.isCurrentTag(item)) return
      if (this.mediating) return this.$Message.error('请先结束当前正在进行的视频调解后才能进行操作')
      this.$emit('input', item)
    },
    showTitleInside(item) {
      return showTitle(item, this)
    },
    isCurrentTag(item) {
      return routeEqual(this.currentRouteObj, item)
    },
    moveToView(tag) {
      const outerWidth = this.outerWidth = this.$refs.scrollOuter.offsetWidth
      const bodyWidth = this.bodyWidth = this.$refs.scrollBody.offsetWidth
      if (bodyWidth < outerWidth) {
        this.tagBodyLeft = 0
      } else if (tag.offsetLeft < -this.tagBodyLeft) {
        // 标签在可视区域左侧
        this.tagBodyLeft = -tag.offsetLeft + this.outerPadding
      } else if (tag.offsetLeft > -this.tagBodyLeft && tag.offsetLeft + tag.offsetWidth < -this.tagBodyLeft + outerWidth) {
        // 标签在可视区域
        this.tagBodyLeft = Math.min(0, outerWidth - tag.offsetWidth - tag.offsetLeft - this.outerPadding)
      } else {
        // 标签在可视区域右侧
        this.tagBodyLeft = -(tag.offsetLeft - (outerWidth - this.outerPadding - tag.offsetWidth))
      }
    },
    getTagElementByName(route) {
      this.$nextTick(() => {
        this.refsTag = this.$refs.tagsPageOpened
        if (this.refsTag) {
          this.refsTag.forEach((item, index) => {
            if (routeEqual(route, item.$attrs['data-route-item'])) {
              const tag = this.refsTag[index].$el
              this.moveToView(tag)
            }
          })
        }
      })
    },
    contextMenu(item, e) {
      if (item.name === this.$config.homeName) {
        return
      }
      this.visible = true
      const offsetLeft = this.$el.getBoundingClientRect().left
      this.contextMenuLeft = e.clientX - offsetLeft + 10
      this.contextMenuTop = e.clientY - 64
    },
    closeMenu() {
      this.visible = false
    }
  },
  watch: {
    '$route'(to) {
      this.getTagElementByName(to)
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.getTagElementByName(this.$route)
    }, 200)
  }
}
</script>

<style lang="less">
@import './tags-nav.less';
</style>
