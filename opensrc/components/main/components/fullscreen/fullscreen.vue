<template>
  <div v-if="showFullScreenBtn" class="full-screen-btn-con">
    <Tooltip :content="openValue ? '退出全屏' : '全屏查看'" placement="bottom">
      <Icon class="full-icon" :class="(isAgent || isVisit) ? 'black-icon' : 'white-icon'" :type="openValue ? 'iconfont icon-tuichuquanping' : 'iconfont icon-quanping'" @click.native="handleChange" />
    </Tooltip>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Fullscreen',
  computed: {
    ...mapGetters([
      'isAgent',
      'isVisit'
    ]),
    showFullScreenBtn() {
      return window.navigator.userAgent.indexOf('MSIE') < 0
    }
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      openValue: false
    }
  },
  mounted() {
    const isFullscreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.fullScreen || document.mozFullScreen || document.webkitIsFullScreen
    document.addEventListener('fullscreenchange', () => {
      this.$emit('input', !this.value)
      this.$emit('on-change', !this.value)
      // this.value = !this.value
    })
    document.addEventListener('mozfullscreenchange', () => {
      this.$emit('input', !this.value)
      this.$emit('on-change', !this.value)
    })
    document.addEventListener('webkitfullscreenchange', () => {
      this.$emit('input', !this.value)
      this.$emit('on-change', !this.value)
    })
    document.addEventListener('msfullscreenchange', () => {
      this.$emit('input', !this.value)
      this.$emit('on-change', !this.value)
    })
    this.$emit('input', isFullscreen)
    this.openValue = this.value
  },
  methods: {
    handleFullscreen() {
      const main = document.body
      if (this.openValue) {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      } else {
        if (main.requestFullscreen) {
          main.requestFullscreen()
        } else if (main.mozRequestFullScreen) {
          main.mozRequestFullScreen()
        } else if (main.webkitRequestFullScreen) {
          main.webkitRequestFullScreen()
        } else if (main.msRequestFullscreen) {
          main.msRequestFullscreen()
        }
      }
    },
    async handleChange() {
      await this.handleFullscreen()
      this.openValue = !this.openValue
    }
  }
}
</script>

<style lang="less">
.full-screen-btn-con {
  .full-screen-btn-con .ivu-tooltip-rel{
    height: 48px;
    line-height: 48px;
    i{
      cursor: pointer;
    }
  }
  .full-icon {
    font-size: 20px;
    cursor: pointer;
    margin-left: 20px;
    &:hover {
      transform: scale(1.3);
    }
  }
  .black-icon {
    color: #999797;

    &:hover {
      color: #DADADA;
    }
    &:active {
      color: #999797;
    }
  }
  .white-icon {
    color: #9DA3AC;

    &:hover {
      color: #8C929B;
    }
    &:active {
      color: #636C78;
    }
  }
}
</style>

