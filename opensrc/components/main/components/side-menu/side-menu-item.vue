<template>
  <Submenu :name="`${parentName}`">
    <template v-slot:title>
      <!-- <div v-if="parentItem.id === 845" class="new-menu-tag">新版</div> -->
      <common-icon :type="iconItem || parentItem.icon || ''" />
      <span>{{ showTitle(parentItem) }}</span>
    </template>
    <template v-for="item in children">
      <template v-if="item.children && item.children.length === 1">
        <side-menu-item v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
        <menu-item v-else :key="`menu-${item.children[0].name}`" :name="getNameOrHref(item, true)">
          <common-icon :type="item.children[0].icon || ''" /><span>{{ showTitle(item.children[0]) }}</span>
        </menu-item>
      </template>
      <template v-else>
        <side-menu-item v-if="showChildren(item)" :key="`menu-${item.name}`" :parent-item="item"></side-menu-item>
        <menu-item v-else :key="`menu-${item.name}`" :name="getNameOrHref(item)">
          <common-icon :type="item.icon || ''" /><span>{{ showTitle(item) }}</span>
        </menu-item>
      </template>
    </template>
  </Submenu>
</template>
<script>
import mixin from './mixin'
import itemMixin from './item-mixin'
export default {
  name: 'SideMenuItem',
  mixins: [mixin, itemMixin]
}
</script>
