@import '@/view/dy-nomenu/style/casedetail.less';

@maxMenuWidth: 200px;
@minMenuWidth: 60px;

.main {
  .turn-around {
    transform: rotate(180deg);
  }
  .black-logo {
    height: 48px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    margin-left: 4px;
    img {
      height: 28px;
      display: block;
      margin: 0 auto;
    }
  }
  .white-logo {
    width: 100%;
    height: 62px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    border-bottom: 1px solid #241E1E;
    img {
      height: 28px;
      display: block;
    }
  }

  .ivu-menu {
    width: @maxMenuWidth+1 !important;
    color: #fff;
  }

  //订制颜色start
  .ivu-menu-light {
    background: #282323 !important;
    color: #fff;
  }
  .ivu-layout-sider{
    background: #282323
  }
  .ivu-menu-light.ivu-menu-vertical
    .ivu-menu-item-active:not(.ivu-menu-submenu) {
    background: #1f1919 !important;
  }
  .ivu-menu-vertical .ivu-menu-opened {
    background: #1f1919 !important;
    color: #fff;
  }
  .ivu-menu-opened .ivu-menu-submenu-title {
    background: #282323;
  }
  .ivu-menu-opened
    .ivu-menu-submenu-has-parent-submenu
    .ivu-menu-submenu-title {
    background: transparent;
  }
  .menu-collapsed .ivu-dropdown .ivu-icon {
    color: #fff !important;
  }
  .ivu-menu-light.ivu-menu-vertical
    .ivu-menu-item-active:not(.ivu-menu-submenu):after {
    width: 4px;
  }

  //tree组件节点名超出范围限制
  .ivu-tree-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 80%;
  }

  //订制颜色end
  .black {
    background: #282323;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.56);
  }
  .white {
    background: #FFF;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.24);
  }
  .header-con {
    padding: 0 16px;
    width: 100%;
    position: relative;
    z-index: 9;

    .full-check {
      color: #999797;
      font-size: 20px;
      cursor: pointer;
      margin-left: 16px;

      &:hover {
        transform: scale(1.3);
        color: #DADADA;
      }
      &:active {
        color: #999797;
      }
    }
    .divide {
      height: 24px;
      width: 1px;
      margin: 12px 20px;
    }
    .blackBg {
      background-color: rgba(255, 255, 255, 0.16);
    }
    .whiteBg {
      background-color: #DCDEE3;
    }
  }
  .main-layout-con {
    height: 100%;
    overflow: hidden;
  }
  .main-content-con {
    height: ~"calc(100% - 60px)";
    overflow: hidden;
  }
  .tag-nav-wrapper {
    padding: 0;
    height: 36px;
    background: #DFE1E4;
  }
  .content-wrapper {
    padding: 8px 16px 16px;
    height: ~"calc(100% - 80px)";
    overflow: auto;
    background-color: @body-background;
  }
  .adminSys {
    background-color: #f5f7f9;
  }
  .isPadding{
    padding: 0 !important;
  }
  .left-sider {
    .ivu-layout-sider-children {
      overflow-y: scroll;
      margin-right: -18px;
      width: 100%;
    }
  }
  .ivu-layout-header {
    height: 48px;
  }
}

.menu-collapsed {
  width: @minMenuWidth;
}

.ivu-menu-item > i {
  margin-right: 8px !important;
}
.ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i {
  margin-right: 8px !important;
}
.collased-menu-dropdown {
  width: 100%;
  margin: 0;
  line-height: normal;
  padding: 7px 0 6px 16px;
  clear: both;
  font-size: 12px !important;
  white-space: nowrap;
  list-style: none;
  cursor: pointer;
  transition: background 0.2s ease-in-out;
  &:hover {
    background: rgba(100, 100, 100, 0.1);
  }
  & * {
    color: #515a6e;
  }
  .ivu-menu-item > i {
    margin-right: 12px !important;
  }
  .ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i {
    margin-right: 8px !important;
  }
}

.ivu-select-dropdown.ivu-dropdown-transfer {
  max-height: 400px;
}
.ivu-select-dropdown.ivu-select-dropdown-transfer {
  max-height: 216px !important;
}

.cancel_tip {
  background: #fff;
  color: #222;
  line-height: 24px;
  span {
    margin-bottom: 8px;
    display: inline-block;
    margin-right: 56px;
  }
  span:last-child{
    margin-right: 0;
  }
  i {
    font-style: normal;
    color: #666;
  }
}

//modal框ui优化
.ivu-poptip-popper {
  min-width: 0 !important;
}


.ivu-auto-complete.ivu-select-dropdown {
  max-height: 280px !important;
}

