<template>
  <Layout style="height: 100%" class="main" @click.native="handleMainClick">
    <Sider v-show="!isAgent && !isVisit" v-model="collapsed" hide-trigger collapsible :width="200" :collapsed-width="60" class="left-sider" :style="{overflow: 'hidden'}" style="padding-top: 62px;">
      <div class="logo-con" :class="{collapsed: collapsed, 'white-logo': !(isAgent || isVisit), 'black-logo': isAgent || isVisit}">
        <img v-show="!collapsed" key="max-logo" src="/bpw-static/anmi/logo/logo2.png">
        <img v-show="collapsed" key="min-logo" src="/bpw-static/anmi/logo/logo-min.jpg">
      </div>
      <side-menu ref="sideMenu" accordion :active-name="$route.name" :collapsed="collapsed" :menu-list="menuList" theme="light" @on-select="turnToPage"></side-menu>
    </Sider>
    <div style="height: 100%; overflow-x: auto; overflow-y: hidden;" :style="!isAgent && !isVisit ? 'width: calc(100% - 200px);' : 'width: 100%'">
      <Layout style="height: 100%; min-width: 1220px">
        <Header class="header-con" :class="(isAgent || isVisit) ? 'black':'white'">
          <header-bar :collapsed="collapsed" @on-coll-change="handleCollapsedChange">
            <template v-if="isAgent || isVisit" #agentbtns>
              <div class="black-logo">
                <img key="max-logo" src="/bpw-static/anmi/logo/logo2.png">
              </div>
              <topMenu v-if="isAgent || isVisit" :list="tagNavLists" />
            </template>

            <user v-show="!urgeRefund" :user-avator="userAvator" />
            <template v-if="isNotification">
              <div class="divide" :class="(isAgent || isVisit) ? 'blackBg':'whiteBg'"></div>
              <fullscreen v-model="isFullscreen" class="fullscreen" />
              <notification ref="notificationRef" />
            </template>
            <Help v-if="isHelping" />
            <div v-show="isAgent" id="ctiIframe"></div>
          </header-bar>
        </Header>
        <Content class="main-content-con">
          <Layout id="main-layout-con" class="main-layout-con">
            <div v-if="showTagNav" class="tag-nav-wrapper">
              <tags-nav ref="tagsNav" :value="$route" :list="tagNavLists" @input="handleClick" @on-close="handleCloseTag" />
            </div>
            <Content id="content-wrapper" class="content-wrapper" :class="{ adminSys: isAdminSys, isPadding:isSetMessagePadding}">
              <keep-alive :include="cacheList">
                <router-view @on-close-current="handleCloseTag" />
              </keep-alive>
            </Content>
          </Layout>
        </Content>
      </Layout>
    </div>
  </Layout>
</template>
<script>
import useClipboard from 'vue-clipboard3'
import {
  getNewTagList,
  getNextRoute,
  routeEqual,
  getRouteByName,
  closeTransPoptip,
  addJs,
  formatTime
} from '@/libs/util'

import {
  getInbCase,
  caseInfo,
} from '@/api/case'

import {
  addAutomatic
} from '@/api/refund'

import SideMenu from './components/side-menu/side-menu.vue'
import WebConfig from '@/config/web'
import HeaderBar from './components/header-bar/header-bar.vue'
import TagsNav from './components/tags-nav/tags-nav.vue'
import User from './components/user/user.vue'
import Notification from './components/notification/notification.vue'
import Help from './components/help/help.vue'
import Fullscreen from '_c/main/components/fullscreen/fullscreen.vue'
import topMenu from './components/top-menu/topMenu.vue'
import { decrypt } from '@/libs/crypto.js'
import { mapMutations, mapActions, mapGetters } from 'vuex'
import * as mqtt from 'mqtt/dist/mqtt.min'
import mqttIcon from '@/assets/images/mqtt.png'
import messageNotice from './components/message/messageNotice.vue'

import './main.less'

export default {
  // name: 'Main',
  components: {
    SideMenu,
    HeaderBar,
    TagsNav,
    User,
    Notification,
    Fullscreen,
    topMenu,
    Help,
    messageNotice
  },
  data() {
    return {
      collapsed: false,
      showTagNav: true, // 是否展示tag
      isFullscreen: false,
      isAdminSys: false,
      height: 40

    }
  },
  computed: {
    ...mapGetters([
      'isAgent',
      'isVisit',
      'isInspector',
      'menuList',
      'routers',
      'urgeRefund',
      'userExtend',
      'isOutbounceLock',
      'isChangeCase',
      'isNotification',
      'tagNavList',
      'duyanFifoAgent',
      'isOutsource',
      'isHelping',
      'isSetMessagePadding',
    ]),
    tagNavLists() {
      this.showTagNav = false
      this.tagNavList.map(item => {
        if (item.name === 'home') {
          item.isHide = !!this.isVisit || !!this.isAgent
          if (this.isVisit) {
            item.meta.isTopMenu = false
          }
        } else if (item.name === 'agentFifo') {
          item.meta.isTopMenu = this.duyanFifoAgent && this.userExtend.isPersonalFifoEnabled && (this.isOutsource ? this.userExtend.fifoSwitch : true)
        } else if (item.name === 'messageEnable') {
          item.meta.isTopMenu = this.userExtend.isMessageEnabled && (this.isOutsource ? this.userExtend.smsSwitch : true)
        }

        if (!item.isHide) {
          this.showTagNav = true
        }
      })

      return this.tagNavList
    },
    userAvator() {
      return this.$store.state.user.avatorImgPath
    },
    cacheList() {
      return this.tagNavLists.length ? this.tagNavLists.filter(item => !(item.meta && item.meta.notCache)).map(item => item.name) : []
    }
  },
  watch: {
    '$route'(newRoute) {
      if (newRoute.name === 'home') {
        if (this.isVisit) {
          this.$router.push({
            name: 'inter'
          })
          return
        }
      }
      const { name, query, params, meta } = newRoute

      this.addTag({
        route: { name, query, params, meta },
        type: 'push'
      })
      this.setBreadCrumb(newRoute)

      this.setTagNavList(getNewTagList(this.tagNavLists, newRoute))
      this.$refs.sideMenu.updateOpenName(newRoute.name)
    }
  },
  mounted() {
    // 初始化设置面包屑导航和标签导航
    this.setTagNavList()
    this.isAdminSys = window.isAdminSys

    // 初始化路由
    this.setHomeRoute(this.routers)

    // 打开主页
    this.addTag({
      route: this.$store.state.app.homeRoute
    })
    this.setBreadCrumb(this.$route)
    // 设置初始语言
    this.setLocal(this.$i18n.locale)
  },
  methods: {
    ...mapMutations([
      'setBreadCrumb',
      'setTagNavList',
      'addTag',
      'setLocal',
      'setHomeRoute'
    ]),
    turnToPage(route) {
      let { name, params, query } = {}
      if (typeof route === 'string') name = route
      else {
        name = route.name
        params = route.params
        query = route.query
      }
      if (name.indexOf('isTurnByHref_') > -1) {
        window.open(name.split('_')[1])
        return
      }
      this.$router.push({
        name,
        params,
        query
      })
    },
    handleCollapsedChange(state) {
      this.collapsed = state
    },
    handleCloseTag(res, type, route) {
      if (type === 'all') {
        this.turnToPage(this.$config.homeName)
      } else if (routeEqual(this.$route, route)) {
        if (type !== 'others') {
          const nextRoute = getNextRoute(this.tagNavLists, route)
          let nexts = { ...nextRoute }

          if (['agentFifo', 'cooperation', 'messageEnable'].includes(nextRoute.name)) {
            nexts = getRouteByName(this.routers, 'mycase')
          }

          this.$router.push(nexts)
        }
      }

      this.setTagNavList(res)
    },
    handleClick(item) {
      if (this.isOutbounceLock) return
      if (this.isChangeCase) return

      this.turnToPage(item)
    },
    handleMainClick(ev) {
      closeTransPoptip(ev)
    }
  }
}
</script>
<style  lang='less'>
#ctiIframe{
  position: relative;
  top: 4px;
  z-index: 100;
  right: 20px;
}
#version{
  text-align: center;
  margin-top: 8px;
}
#sdkHJ{
  position: fixed;
  right: 30px;
  bottom: 80px;
  z-index: 99;
}
.hj{
    position: relative;
    overflow: hidden;
    background: rgb(255, 255, 255);
    border: 1px solid rgb(212, 212, 212);
    border-radius: 4px;
    // display: flex;
    // align-items: flex-start;
    color: rgb(174, 174, 174);
    width: 285px;
    .top{
      width: 100%;
      line-height: 40px;
      display: flex;
      padding: 0 10px;
      justify-content: space-between;
      .left{
        color: #515A6E;
        font-weight: 500;
        display: flex;
        align-items: center;
        .left_one{
          display: flex;
          align-items: center;
          width: 66px;
          &.left_no_login{
            width: 80px;
          }
        }
        .countdown{
          color:#8D959F;
          margin-left: 6px;
        }
        .icon-img{
          width: 24px;
          height: 24px;
          margin-right: 6px;
        }
        .icon-img1{
          width: 18px;
          height: 26px;
          position: relative;
          top:7px;
        }
        .phoneCall{
          margin-left: 22px;
          cursor: pointer;
        }
      }
      .right{
        cursor: pointer;
        color: #FF4F1F;
      }
    }
    .hj-sdk{
      margin-right: 16px;
      .ivu-form-item{
        margin-bottom: 10px!important;
      }
    }
    .login{
      text-align: center;
      .ivu-btn{
        width: 253px;
        margin-left: 16px;
        height: 27px;
        background: linear-gradient(134deg, #FF8742 0%, #FF4F1F 100%);
        box-shadow: 1px 1px 4px 0px rgba(145,45,18,0.34);
        border-radius: 14px;
      }
    }
}
</style>
