<style lang="less">
@import "./login.less";
</style>

<template>
  <div class="login">
    <div class="login-con">
      <Card title="欢迎登陆安米智能开放平台" style="text-align: center">
        <login-form :is-forget="false" @on-success-valid="handleSubmit"></login-form>
        <p class="login-tip">
          {{ version }}
        </p>
      </Card>
    </div>
  </div>
</template>

<script>
import LoginForm from '_c/login-form'
import { mapActions, mapMutations } from 'vuex'

export default {
  components: {
    LoginForm
  },
  data() {
    return {
      entering: false,
      version: anmiVersion
    }
  },
  created() {
    this.$store.commit('setIsVerifyCode', true)
  },
  methods: {
    ...mapMutations([
      'setMenuRouters'
    ]),
    ...mapActions(['handleOpen']),
    handleSubmit(loginData) {
      this.handleOpen(loginData).then(res => {
        if (res) {
          this.$router.push({
            name: this.$config.homeName
          })

          this.setMenuRouters(window.VueMainInstance.adminRoutes)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.title {
  border-bottom: 1px solid rgb(232, 234, 236);
  color: rgb(23, 35, 61);
  font-weight: bold;
  text-align: center;
  display: block;
  margin: -15px 0 15px;
  padding: 10px;
}
</style>
