<template>
  <div>
    <div class="open_title">调用说明</div>
    <p class="title_second">开放平台api为使用HTTP协议并遵从REST原则设计的纯Web接口，支持使用几乎任何客户端与编程语言对接</p>
    <p class="title_second">企业需在正常使用<strong>安米催收系统</strong>的情况下才可调用接口</p>
    <p class="title_second title_weight">URL说明</p>
    <div class="collapse-header">
      url 说明:平台api的URL一般为https://&lt;servername&gt;:&lt;port&gt;/xapi/{version}/{service}<br />
      其中{version}代表api的版本号，{service}代表具体业务类型
    </div>
    <p class="title_second title_weight">请求头信息</p>
    <div class="collapse-header">
      请求头信息:平台接口若使用POST请求方式，传参形式如下：<br/>
      Accept:application/json;charset=utf-8;Content-Type:application/json;charset=UTF-8
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">示例</p>
      <pre class="response_data">
        Query Sring Parameters
        {
          "apikey": "9bikG8I52ktBTX5gGfvUwnP0f8fFxxxx",
          "content": [
            {
                "out_serial_no": "20200202XX",
                "debt_name": "张三",
                "debt_id_card": "41072119820619XXXX",
                "repayment_amount": "100",
                "repayment_type": "银行卡",
                "repayment_card_no": "622260026000107XXXX",
                "repayment_time": "2020-02-18",
                "repayment_from": "丽丽"
            }
          ],
          "template_id": 12
        }


        Request Payload
        {
          "status": 1,
          "data": {
            "task_id": 4849,
            "total_count": 1
          }
        }
      </pre>
    </div>
  </div>
</template>

<style lang="less" scoped>
.open_title{
font-size: 18px;
font-weight: 600;
margin-bottom: 10px;
}
.title_second{
margin-bottom: 10px;
font-size: 14px;
}
.title_weight{
font-weight: 600;
}
.collapse-header{
font-size: 14px;
line-height: 20px;
background: #f8f8f9;
border: 1px solid #e8eaec;
padding: 10px 16px;
margin-bottom: 10px;
}
.iv-content{
margin-top: 16px;
}
.response_data{
background-color: rgba(32, 125, 226, .1);
border: 1px solid rgba(32, 125, 226, .1);
padding: 10px;
}
pre {
white-space: pre-wrap;
word-wrap: break-word;
}
</style>