<template>
  <div>
    <div class="list">
      <div>
        <Icon type="md-bookmarks" color="#2D8cF0" size="22" />
      </div>
      <div>公司名称： {{orgName}}</div>
    </div>
    <div class="list">
      <div>
        <Icon type="md-key" color="#2D8cF0" size="22" />
      </div>
      <div>APIKEY： {{orgApikey}}</div>
    </div>
    <!-- <div class="list">
      <div>
        <Icon type="md-alert" color="#2D8cF0" size="22" />
      </div>
      <div> 接口调用频次： 10次/s</div>
    </div> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: "home",
  computed: {
    ...mapGetters([
      'orgApikey'
    ])
  },
  data() {
    return {
      orgName: localStorage.getItem('orgName')
    }
  }
}
</script>
<style lang="less" scoped>
.list {
  display: flex;
  line-height: 88px;
  background: #fff;
  margin-bottom: 20px;
  :nth-child(1) {
    width: 68px;
    text-align: center;
    border-right: 1px solid #f1f1f1
  }
  :nth-child(2) {
    padding-left: 34px;
    font-size: 16px;
  }
}
</style>
