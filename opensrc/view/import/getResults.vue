<template>
  <div>
    <p class="open_title">获取案件导入结果</p>
    <p class="title_second">此接口用于获取案件导入结果</p>
    <p class="title_second title_weight">HTTP头信息</p>
    <div class="collapse-header">Accept: application/json, text/plain, */*</div>
    <p class="title_second title_weight">URL</p>
    <Collapse v-model="value">
      <Panel name="1">
        GET: https://&lt;servername&gt;:&lt;port&gt;/xapi/v1/openApi/caseImportResult
        <span style="color: #2D8cF0; float: right; margin-right: 15px;">{{ value.includes('1') ? '收回' : '展开' }}</span>

        <template #content>
          <p>
            <Table :columns="columns" :data="data"></Table>
            <Button type="primary" style="margin-top:20px" @click="doRequest">请求</Button>
            <pre v-if="responseData" class="response_data">{{ responseData }}</pre>
          </p>
        </template>
      </Panel>
    </Collapse>
    <div class="iv-content">
      <p class="title_second title_weight">参数表格</p>
      <Table :columns="columns1" :data="data1"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">返回结果</p>
      <Table :columns="columns2" :data="data2"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">传输数据错误</p>
      <Table :columns="columns3" :data="data3"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">注意事项</p>
      <div class="notice">
        1. 此接口仅用于获取案件导入结果，若案件部分案件导入失败，请下载错误文件进行查看具体错误，然后重新传输；<br>
        2. 任务唯一编号获取途径：案件导入接口的返回值；
      </div>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">返回示例</p>
      <pre class="response_data">
                {
                    "status": 1,
                    "data": {
                        "task_status": -1,
                        "product_name": "机器人产品",
                        "success_amt": 0,
                        "total": 1,
                        "out_batch_no": "ceshi",
                        "total_amount": 0,
                        "expire_time": *************,
                        "error_data_json": [
                            {
                                "data": {
                                    "contract_no": "1122888ax201922",
                                    "bank_code": "622202123456789220",
                                    "amount": "1500",
                                    "overdue_days": "30",
                                    "gender": "男",
                                    "out_serial_no": "***********",
                                    "id_card": "820000195008116666",
                                    "re_periods": "6",
                                    "name": "张三",
                                    "own_mobile": "***********",
                                    "overdue_date": "2019/02/01",
                                    "bank_name": "xx银行",
                                    "periods": "12"
                                },
                                "error": [
                                    {
                                        "column_key": "out_serial_no",
                                        "column_value": "***********",
                                        "error_code": "CER_101"
                                    }
                                ]
                            }
                        ]
                    }
                }
            </pre>
    </div>
  </div>
</template>

<script>
import { getCaseResult } from '@open/api/case'
import { mapGetters } from 'vuex'
import { resolveComponent } from 'vue'

export default {
  data() {
    return {
      responseData: '',
      value: '0',
      apikey: '',
      task_id: '',
      columns2: [
        {
          title: '字段名',
          key: 'parameter'
        },
        {
          title: '变量名',
          key: 'prop'
        },
        {
          title: '类型',
          key: 'type'
        },
        {
          title: '示例值',
          key: 'demonstrate'
        },
        {
          title: '描述',
          key: 'desc',
          render: (h, { row }) => {
            const desc = row.desc
            const link = h('span', [
              h('span', '详情请参照'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/error/backCode'
                  })
                }
              }, '返回值说明')
            ])

            let description = []
            if (row.href) {
              description = [desc, link]
            } else {
              description = [desc]
            }
            return h('div', description)
          }
        }
      ],
      data2: [
        {
          parameter: '返回状态码',
          prop: 'status',
          type: 'Int',
          demonstrate: '1',
          desc: '返回状态码。1: 请求成功, 0: 请求失败'
        }, {
          parameter: '错误提示',
          prop: 'message',
          type: 'String',
          demonstrate: '密码错误',
          desc: '错误提示'
        }, {
          parameter: '错误代码',
          prop: 'error_code',
          type: 'String',
          demonstrate: 'ERR_1000',
          desc: '错误代码,',
          href: true
        }, {
          parameter: '导入结果',
          prop: 'task_status',
          type: 'Int',
          demonstrate: '1',
          desc: '导入结果,分为0:导入中、1:导入完成和-1:导入失败'
        }, {
          parameter: '案件产品名称',
          prop: 'product_name',
          type: 'String',
          demonstrate: '测试产品1',
          desc: '产品名称'
        }, {
          parameter: '案件总量',
          prop: 'total',
          type: 'Int',
          demonstrate: '500',
          desc: '案件总量'
        }, {
          parameter: '成功导入量',
          prop: 'success_amt',
          type: 'Int',
          demonstrate: '300',
          desc: '成功导入量'
        }, {
          parameter: '批次号',
          prop: 'out_batch_no',
          type: 'String',
          demonstrate: 'test',
          desc: '批次号'
        }, {
          parameter: '案件总金额',
          prop: 'total_amount',
          type: 'Long',
          demonstrate: '2000',
          desc: '案件总金额'
        }, {
          parameter: '有效时间',
          prop: 'expire_time',
          type: 'Long',
          demonstrate: '1582646400000',
          desc: '导入失败的错误数据的返回有效时间，13位时间戳'
        }, {
          parameter: '错误数据',
          prop: 'error_data_json',
          type: 'JSON',
          demonstrate: '[{"data": {}, "error": []}]',
          desc: '导入失败部分的数据则显示错误数据的JSON,但当时间超过有效时间后则返回为空'
        }
      ],
      columns1: [
        {
          title: '参数名',
          key: 'parameter'
        }, {
          title: '类型',
          key: 'type'
        }, {
          title: '是否必须',
          key: 'required'
        }, {
          title: '描述',
          key: 'desc',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }, {
          title: '示例',
          key: 'demonstrate'
        }
      ],
      data1: [
        {
          parameter: 'apikey',
          type: 'String',
          required: '是',
          demonstrate: 'jpolkmthg09ikjh',
          href: true
        }, {
          parameter: 'task_id',
          type: 'Int',
          required: '是',
          desc: '任务唯一编号',
          demonstrate: '1234'
        }
      ],
      columns: [
        {
          title: '参数名',
          key: 'parameter',
          render: (h, { row }) => {
            return h('div', {
              class: 'ivu_tables'
            }, row.parameter)
          }
        }, {
          title: '值',
          key: 'value',
          render: (h, { row }) => {
            return h(resolveComponent('Input'), {
              type: row.textarea ? 'textarea' : 'text',
              modelValue: row.parameter === 'apikey' ? this.orgApikey : '',
              onOnChange: (e) => {
                this[row.parameter] = e.target.value
              }
            })
          }
        },
        {
          title: '描述',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        },
        {
          title: '类型',
          key: 'type'
        }
      ],
      data: [{
        parameter: 'apikey',
        desc: '唯一接口密钥',
        href: true,
        type: 'String'
      }, {
        parameter: 'task_id',
        desc: '任务唯一编号',
        type: 'Int'
      }],
      columns3: [{
        title: '错误代码',
        key: 'parameter'
      }, {
        title: '错误信息',
        key: 'err_info'
      }],
      data3: [{
        parameter: 'CER_100',
        err_info: '必填项不能为空'
      }, {
        parameter: 'CER_101',
        err_info: '不能重复'
      }, {
        parameter: 'CER_102',
        err_info: '该值不存在'
      }, {
        parameter: 'CER_103',
        err_info: '值类型或长度不合法'
      }]
    }
  },
  computed: {
    ...mapGetters([
      'orgApikey'
    ])
  },
  created() {
    this.apikey = this.orgApikey
  },
  methods: {
    doRequest() {
      if (!this.apikey) {
        this.$Message.error('请填写接口密钥')
        return
      }
      if (!this.task_id) {
        this.$Message.error('请填写任务唯一编号')
        return
      }
      const data = {
        apikey: this.apikey,
        task_id: parseInt(this.task_id)
      }

      getCaseResult(data).then(res => {
        this.responseData = res
      })
    }
  }
}
</script>

<style lang="less" scoped>
 :deep(.ivu-table) {
    th, td {
        height: 48px !important;
    }
}
.open_title{
font-size: 18px;
font-weight: 600;
margin-bottom: 10px;
}
.notice{
font-size: 14px;
background: #fff;
padding: 18px;
}
.response_data{
background-color: rgba(32, 125, 226, .1);
border: 1px solid rgba(32, 125, 226, .1);
padding: 10px;
}
.title_second{
margin-bottom: 10px;
font-size: 14px;
}
.title_weight{
font-weight: 600;
}
.collapse-header{
line-height: 38px;
background: #f8f8f9;
border: 1px solid #e8eaec;
padding-left: 16px;
margin-bottom: 10px;
}
.iv-content{
margin-top: 16px;
}
 :deep(.ivu_tables::after){
content: "*";
display: inline-block;
margin-right: 4px;
line-height: 1;
font-family: SimSun;
font-size: 12px;
color: #ed4014;
}
pre {
white-space: pre-wrap;
word-wrap: break-word;
}
</style>
