<template>
  <div>
    <p class="open_title">催记导出</p>
    <p class="title_second">此接口用于催记导出</p>
    <p class="title_second title_weight">HTTP头信息</p>
    <div class="collapse-header">Accept:application/json;charset=utf-8;Content-Type:application/json;charset=UTF-8</div>
    <p class="title_second title_weight">URL</p>
    <Collapse v-model="value">
      <Panel name="1">
        GET: https://&lt;servername&gt;:&lt;port&gt;/xapi/v1/openApi/operationList
        <span style="color: #2D8cF0; float: right; margin-right: 15px;">{{ value.includes('1') ? '收回' : '展开' }}</span>
        <template #content>
          <p>
            <Table :columns="columns" :data="data">
            </Table>
            <Button type="primary" style="margin-top:20px" @click="doRequest">请求</Button>
            <pre v-if="responseData" class="response_data">{{ responseData }}</pre>
          </p>
        </template>
      </Panel>
    </Collapse>
    <div class="iv-content">
      <p class="title_second title_weight">返回参数表格</p>
      <Table :columns="columns1" :data="data1"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">注意事项</p>
      <div class="notice">
        1.导出查询条件为催收时间，起始催收时间和结束催收时间为必填项，查询时间不超过一天;<br>
        2.为避免导出重复催记，需传入起始催记ID作为导出条件;首次调用可不传，默认从起始催收时间的第一条开始导出；<br>
        3.单次最多导出200条催记;<br>
        4.调用频率限制为1次/s。
      </div>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">返回示例</p>
      <pre class="response_data">
          {
              "status": 1,
              "data": {
                  "total_elements": 1,
                  "list": [
                      {
                          "id": 7229164,
                          "case_id": 7774095,
                          "action_type": 2122,
                          "action_type_name": "催收结果名称",
                          "call_type": 2676,
                          "call_type_name": "电话结果名称",
                          "reduce_amount": null,
                          "ptp_amount": null,
                          "ptp_time": null,
                          "operator_name": "操作人",
                          "case_name": "债务人姓名",
                          "id_card": "43423423423423xxxx",
                          "con_name": "债务人姓名",
                          "con_mobile": "1367365xxxx",
                          "relation_type": "本人",
                          "delt_id": 653,
                          "delt_name": "测试催收配置",
                          "product_id": 1584,
                          "product_name": "催收配置产品",
                          "next_time": null,
                          "out_serial_no": "************",
                          "out_batch_id": 2080,
                          "out_batch_no": "123",
                          "own_mobile": "1367365xxxx",
                          "case_ope_time": *************,
                          "call_uuid": "",
                          "operation_state": 2675,
                          "operation_state_name": "催收进程名称",
                          "call_style": 1,
                          "call_time": *************,
                          "call_durtion": 0,
                          "ring_durtion": 0,
                          "submit_type": 0,
                          "caller": "",
                          "tag": "",
                          "create_type": 0,
                          "desc": "结果代码测试",
                          "audio_url": "https://录音地址.wav",
                          "field_json": {
                              "bank_code": "622202123xxxxxxxx",
                              "bank_name": "xx银行",
                              "contract_no": "1122888ax201922",
                              "gender": "男",
                              "is_sync_address": "true",
                              "own_mobile": "1367365xxxx",
                              "periods": "12",
                              "re_periods": "6"
                          },
                          "action_type_code": "jieguo"
                      }
                  ]
              }
          }
      </pre>
    </div>
  </div>
</template>

<script>
import { operationList } from '@open/api/record'
import { mapGetters } from 'vuex'
import { resolveComponent } from 'vue'

export default {
  data() {
    return {
      responseData: '', // 请求返回的数据
      value: '0',
      apikey: '',
      start_id: null,
      operation_time_start: null,
      operation_time_end: null,
      out_serial_no: null,
      limit: null,
      columns1: [
        {
          title: '参数名',
          key: 'parameter'
        }, {
          title: '类型',
          key: 'type'
        }, {
          title: '描述',
          key: 'desc',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }
      ],
      data1: [
        {
          parameter: 'operator_name',
          type: 'String',
          desc: '催员'
        }, {
          parameter: 'case_ope_time',
          type: 'Int',
          desc: '催收时间'
        }, {
          parameter: 'action_type_name',
          type: 'String',
          desc: '催收结果'
        }, {
          parameter: 'call_type_name',
          type: 'String',
          desc: '电话结果'
        }, {
          parameter: 'reduce_amount',
          type: 'Int',
          desc: '减免金额'
        }, {
          parameter: 'ptp_amount',
          type: 'Int',
          desc: 'PTP金额'
        }, {
          parameter: 'ptp_time',
          type: 'Int',
          desc: 'PTP时间'
        }, {
          parameter: 'con_name',
          type: 'String',
          desc: '联系人'
        }, {
          parameter: 'con_mobile',
          type: 'String',
          desc: '联系人电话'
        }, {
          parameter: 'relation_type',
          type: 'String',
          desc: '联系人关系'
        }, {
          parameter: 'next_time',
          type: 'Int',
          desc: '下次跟进时间'
        }, {
          parameter: 'desc',
          type: 'String',
          desc: '催收备注'
        }, {
          parameter: 'call_time',
          type: 'Int',
          desc: '通话时间'
        }, {
          parameter: 'call_durtion',
          type: 'Int',
          desc: '通话时长'
        }, {
          parameter: 'ring_durtion',
          type: 'Int',
          desc: '响铃时长'
        }, {
          parameter: 'caller',
          type: 'String',
          desc: '主叫号码'
        }, {
          parameter: 'call_style',
          type: 'Int',
          desc: '呼叫类型(0：呼入 1：呼出)'
        }, {
          parameter: 'operation_state_name',
          type: 'String',
          desc: '催收进程'
        }, {
          parameter: 'submit_type',
          type: 'Int',
          desc: '催收类型(0：人为呼叫 1：机器人计划 2:预测式外呼计划)'
        }, {
          parameter: 'tag',
          type: 'String',
          desc: '客户标签'
        }, {
          parameter: 'create_type',
          type: 'Int',
          desc: '催记来源(0：催员填写 1：催记导入)'
        }, {
          parameter: 'out_serial_no',
          type: 'String',
          desc: '案件编号'
        }, {
          parameter: 'product_name',
          type: 'String',
          desc: '产品名称'
        }, {
          parameter: 'case_name',
          type: 'String',
          desc: '债务人'
        }, {
          parameter: 'own_mobile',
          type: 'String',
          desc: '债务人手机号'
        }, {
          parameter: 'id_card',
          type: 'String',
          desc: '身份证号'
        }, {
          parameter: 'out_batch_no',
          type: 'String',
          desc: '批次号'
        }, {
          parameter: 'audio_url',
          type: 'String',
          desc: '催记录音下载地址'
        }, {
          parameter: 'field_json',
          type: 'JSON',
          desc: '案件信息'
        }, {
          parameter: 'action_type_code',
          type: 'String',
          desc: '催收结果代码'
        }
      ],
      columns: [
        {
          title: '参数名',
          key: 'parameter',
          render: (h, { row }) => {
            return h('div', {
              class: row.required ? 'ivu_tables' : ''
            }, row.parameter)
          }
        }, {
          title: '值',
          key: 'value',
          render: (h, { row }) => {
            return h(resolveComponent('Input'), {
              type: row.textarea ? 'textarea' : 'text',
              modelValue: row.parameter === 'apikey' ? this.orgApikey : '',
              onOnChange: (e) => {
                this[row.parameter] = e.target.value
              }
            })
          }
        },
        {
          title: '描述',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        },
        {
          title: '类型',
          key: 'type'
        }
      ],
      data: [{
        parameter: 'apikey',
        desc: '唯一接口密钥',
        href: true,
        type: 'String',
        required: true
      }, {
        parameter: 'start_id',
        type: 'Long',
        desc: '查询起始催记id',
        required: false
      }, {
        parameter: 'operation_time_start',
        type: 'Long',
        desc: '查询起始时间（含），时间戳（13位，精确到秒），查询起、止时间不能超过一天',
        required: true
      }, {
        parameter: 'operation_time_end',
        type: 'Long',
        desc: '查询截止时间（不含），时间戳（13位，精确到秒），查询起、止时间不能超过一天',
        required: true
      }, {
        parameter: 'out_serial_no',
        type: 'String',
        desc: '案件编号',
        required: false
      }, {
        parameter: 'limit',
        type: 'Int',
        desc: '单次查询数量，最大限制200',
        required: true
      }]
    }
  },
  computed: {
    ...mapGetters([
      'orgApikey'
    ])
  },
  created() {
    this.apikey = this.orgApikey
  },
  methods: {
    doRequest() {
      if (!this.apikey) {
        this.$Message.error('请填写接口密钥')
        return
      }
      if (!this.operation_time_start) {
        this.$Message.error('起始时间')
        return
      }
      if (!this.operation_time_end) {
        this.$Message.error('截止时间')
        return
      }
      if (!this.limit) {
        this.$Message.error('单次查询数量')
        return
      }

      const data = {
        apikey: this.apikey,
        start_id: this.start_id ? parseInt(this.start_id) : null,
        operation_time_start: parseInt(this.operation_time_start),
        operation_time_end: parseInt(this.operation_time_end),
        out_serial_no: this.out_serial_no,
        limit: parseInt(this.limit)
      }

      operationList(data).then(res => {
        this.responseData = res
      })
    }
  }
}
</script>

<style lang="less" scoped>
 :deep(.ivu-table) {
    th, td {
        height: 48px !important;
    }
}
.open_title{
font-size: 18px;
font-weight: 600;
margin-bottom: 10px;
}
.notice{
font-size: 14px;
background: #fff;
padding: 18px;
}
.response_data{
background-color: rgba(32, 125, 226, .1);
border: 1px solid rgba(32, 125, 226, .1);
padding: 10px;
}
.title_second{
margin-bottom: 10px;
font-size: 14px;
}
.title_weight{
font-weight: 600;
}
.collapse-header{
line-height: 38px;
background: #f8f8f9;
border: 1px solid #e8eaec;
padding-left: 16px;
margin-bottom: 10px;
}
.iv-content{
margin-top: 16px;
}
 :deep(.ivu_tables::after){
content: "*";
display: inline-block;
margin-right: 4px;
line-height: 1;
font-family: SimSun;
font-size: 12px;
color: #ed4014;
}
pre {
white-space: pre-wrap;
word-wrap: break-word;
}
</style>
