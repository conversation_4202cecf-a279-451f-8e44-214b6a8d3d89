<template>
  <div>
    <p class="open_title">案件更新</p>
    <p class="title_second">此接口用于案件更新</p>
    <p class="title_second title_weight">HTTP头信息</p>
    <div class="collapse-header">Accept:application/json;charset=utf-8;Content-Type:application/json;charset=UTF-8</div>
    <p class="title_second title_weight">URL</p>

    <Collapse v-model="value">
      <Panel name="1">
        POST: https://&lt;servername&gt;:&lt;port&gt;/xapi/v1/openApi/caseUpdateImport
        <span style="color: #2D8cF0; float: right; margin-right: 15px;">{{ value.includes('1') ? '收回' : '展开' }}</span>
        <template #content>
          <p>
            <Table :columns="columns" :data="data">
            </Table>
            <Button type="primary" style="margin-top:20px" @click="submit">请求</Button>
            <pre v-if="responseData" class="response_data">{{ responseData }}</pre>
          </p>
        </template>
      </Panel>
    </Collapse>
    <div class="iv-content">
      <p class="title_second title_weight">参数表格</p>
      <Table :columns="columns1" :data="data1"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">注意事项</p>
      <div class="notice">
        1. content参数中的所有数据均为String类型（包括时间），格式为yyyy/MM/dd；<br>
        2. 目前最多一次性传输5000条数据，超过5000条将导入失败；<br>
        3. 案件更新为异步任务，具体导入结果需前往获取导入结果界面查询；<br>
        4. 调用频率限制为1次／s；<br>
      </div>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">返回示例</p>
      <pre class="response_data">
      {
        "status": 1,
        "data": {
          "task_id": 4849,
          "total_count": 1
        }
      }
    </pre>
    </div>
  </div>
</template>
<script>
import { caseUpdate } from '@open/api/case'
import { isJSON } from '@open/api/util'
import { mapGetters } from 'vuex'
import { resolveComponent } from 'vue'

export default {
  computed: {
    ...mapGetters([
      'orgApikey'
    ])
  },
  data() {
    return {
      responseData: '',
      value: '0',
      apikey: '',
      template_id: '', // 模版ID
      content: '',
      columns1: [
        {
          title: '参数名',
          key: 'parameter'
        }, {
          title: '类型',
          key: 'type'
        }, {
          title: '是否必须',
          key: 'required',
          render: (h, { row }) => {
            return h('span', row.required ? '是' : '否')
          }
        }, {
          title: '描述',
          key: 'desc',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }, {
          title: '示例',
          key: 'demonstrate'
        }
      ],
      data1: [
        {
          parameter: 'apikey',
          type: 'String',
          required: true,
          demonstrate: 'jpolkmthg09ikjh',
          href: true
        }, {
          parameter: 'template_id',
          type: 'Int',
          required: true,
          desc: '模版编号',
          demonstrate: '1234'
        }, {
          parameter: 'content',
          type: 'Array',
          required: true,
          desc: '案件信息JSON数组',
          demonstrate: '[{"out_serial_no": "20200202XX","debt_id_card": "41072119820619XXXX","}]'
        }
      ],
      columns: [
        {
          title: '参数名',
          key: 'parameter',
          render: (h, { row }) => {
            return h('div', {
              class: row.required ? 'ivu_tables' : ''
            }, row.parameter)
          }
        }, {
          title: '值',
          key: 'value',
          render: (h, { row }) => {
            return h(resolveComponent('Input'), {
              type: row.textarea ? 'textarea' : 'text',
              modelValue: row.parameter === 'apikey' ? this.orgApikey : '',
              onOnChange: (e) => {
                this[row.parameter] = e.target.value
              }
            })
          }
        },
        {
          title: '描述',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }, {
          title: '类型',
          key: 'type'
        }
      ],
      data: [{
        parameter: 'apikey',
        desc: '唯一接口密钥',
        href: true,
        required: true,
        type: 'string'
      }, {
        parameter: 'template_id',
        desc: '模板唯一编号',
        required: true,
        type: 'Int'
      }, {
        parameter: 'content',
        desc: '案件信息JSON数组',
        textarea: true,
        required: true,
        type: 'Array'
      }]
    }
  },
  created() {
    this.apikey = this.orgApikey
  },
  methods: {
    submit() {
      if (!this.apikey) {
        this.$Message.error('请填写接口密钥')
        return
      }
      if (!this.template_id) {
        return this.$Message.error('请填写模版编号')
      }
      if (!isJSON(this.content)) {
        this.$Message.error('content参数中请输入正确的JSON格式的值')
        return
      }

      caseUpdate({
        apikey: this.apikey,
        template_id: parseInt(this.template_id),
        content: JSON.parse(this.content)
      }).then(res => {
        this.responseData = res
      })
    }
  }
}
</script>
<style lang='less' scoped>
 :deep(.ivu-table) {
    th, td {
        height: 48px !important;
    }
}
.open_title{
font-size: 18px;
font-weight: 600;
margin-bottom: 10px;
}
.notice{
  font-size: 14px;
  background: #fff;
  padding: 18px;
}
.response_data{
  background-color: rgba(32, 125, 226, .1);
  border: 1px solid rgba(32, 125, 226, .1);
  padding: 10px;
}
.title_second{
  margin-bottom: 10px;
  font-size: 14px;
}
.title_weight{
  font-weight: 600
}
.collapse-header{
  line-height: 38px;
  background: #f8f8f9;
  border: 1px solid #e8eaec;
  padding-left: 16px;
  margin-bottom: 10px;
}
.iv-content{
  margin-top: 16px;
}
 :deep(.ivu_tables::after){
  content: "*";
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 12px;
  color: #ed4014;
}
pre {
white-space: pre-wrap;
word-wrap: break-word;
}
</style>
