<template>
  <div>
    <p class="open_title">获取更新任务结果</p>
    <p class="title_second">此接口用于获取联系人状态更新结果</p>
    <p class="title_second title_weight">HTTP头信息</p>
    <div class="collapse-header">Accept: application/json, text/plain, */*</div>
    <p class="title_second title_weight">URL</p>

    <Collapse v-model="value">
      <Panel name="1">
        GET: https://&lt;servername&gt;:&lt;port&gt;/xapi/v1/openApi/queryInvalidContactResult
        <span style="color: #2D8cF0; float: right; margin-right: 15px;">{{ value.includes('1') ? '收回' : '展开' }}</span>
        <template #content>
          <p>
            <Table :columns="columns" :data="data">
            </Table>
            <Button type="primary" style="margin-top:20px" @click="submit">请求</Button>
            <pre v-if="responseData" class="response_data">{{ responseData }}</pre>
          </p>
        </template>
      </Panel>
    </Collapse>
    <div class="iv-content">
      <p class="title_second title_weight">参数表格</p>
      <Table :columns="columns1" :data="data1"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">返回结果</p>
      <Table :columns="columns2" :data="data2"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">传输数据错误</p>
      <Table :columns="columns3" :data="data3"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">注意事项</p>
      <div class="notice">
        1.此接口仅用于获取联系人状态更新为无效的任务结果，若联系人状态更新失败，请根据错误数据调整后重新调用联系人状态更新接口；
      </div>
    </div>

    <div class="iv-content">
      <p class="title_second title_weight">返回示例</p>
      <pre class="response_data">
      {
          "status": 1,
          "data": {
              "task_status": -1,
              "success_amt": 0,
              "total": 2,
              "desc": "描述",
              "out_serial_no_error": ["案件编号1"],
              "contacts_error": [
                  {
                      "mobile":"手机号",
                      "out_serial_no":"案件编号"
                  }
              ]
          }
      }
    </pre>
    </div>
  </div>
</template>
<script>
import { getContactUpdateResult } from '@open/api/contacts'
import { mapGetters } from 'vuex'
import { resolveComponent } from 'vue'
export default {
  computed: {
    ...mapGetters(['orgApikey'])
  },
  data() {
    return {
      value: '0',
      apikey: '',
      task_id: '',
      responseData: '', // 请求返回的结果
      columns1: [
        {
          title: '参数名',
          key: 'parameter'
        }, {
          title: '类型',
          key: 'type'
        }, {
          title: '是否必须',
          key: 'required',
          render: (h, { row }) => {
            return h('span', row.required ? '是' : '否')
          }
        }, {
          title: '描述',
          key: 'desc',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }, {
          title: '示例',
          key: 'demonstrate'
        }
      ],
      data1: [
        {
          parameter: 'apikey',
          type: 'string',
          required: true,
          demonstrate: 'jpolkmthg09ikjh',
          href: true
        }, {
          parameter: 'task_id',
          type: 'Int',
          required: true,
          desc: '任务唯一编号',
          demonstrate: '123'
        }
      ],
      columns: [
        {
          title: '参数名',
          key: 'parameter',
          render: (h, { row }) => {
            return h('div', {
              class: row.required ? 'ivu_tables' : ''
            }, row.parameter)
          }
        }, {
          title: '值',
          key: 'value',
          render: (h, { row }) => {
            return h(resolveComponent('Input'), {
              type: row.textarea ? 'textarea' : 'text',
              modelValue: row.parameter === 'apikey' ? this.orgApikey : '',
              onOnChange: (e) => {
                this[row.parameter] = e.target.value
              }
            })
          }
        },
        {
          title: '类型',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }
      ],
      data: [{
        parameter: 'apikey',
        desc: '唯一接口密钥',
        href: true,
        required: true
      }, {
        parameter: 'task_id',
        desc: '任务唯一编号',
        required: true
      }],
      columns2: [{
        title: '字段名',
        key: 'parameter'
      },
      {
        title: '变量名',
        key: 'prop'
      },
      {
        title: '类型',
        key: 'type'
      },
      {
        title: '示例值',
        key: 'demonstrate'
      },
      {
        title: '描述',
        key: 'desc',
        render: (h, { row }) => {
          const desc = row.desc
          const link = h('span', [
            h('span', '详情请参照'),
            h('a', {
              href: '#',
              onClick: () => {
                this.$router.push({
                  path: '/error/backCode'
                })
              }
            }, '返回值说明')
          ])

          let description = []
          if (row.href) {
            description = [desc, link]
          } else {
            description = [desc]
          }
          return h('div', description)
        }
      }],
      data2: [{
        parameter: '返回状态码',
        prop: 'status',
        type: 'Int',
        demonstrate: '1',
        desc: '返回状态码。1: 请求成功, 0: 请求失败'
      }, {
        parameter: '错误提示',
        prop: 'message',
        type: 'String',
        demonstrate: '密码错误',
        desc: '错误提示'
      }, {
        parameter: '错误代码',
        prop: 'error_code',
        type: 'String',
        demonstrate: 'ERR_1000',
        desc: '错误代码,',
        href: true
      }, {
        parameter: '结果',
        prop: 'task_status',
        type: 'Int',
        demonstrate: 'OK',
        desc: '结果，分为 0:执行中、1:完成和-1:失败'
      }, {
        parameter: '号码总数',
        prop: 'total',
        type: 'Int',
        demonstrate: '500',
        desc: '执行号码总数（单个案件下号码有多个的，数量记为1 ）'
      }, {
        parameter: '成功更新数',
        prop: 'success_amt',
        type: 'Int',
        demonstrate: '300',
        desc: '成功更新量 （单个案件下号码有多个的，成功数量记为1 ）'
      }, {
        parameter: '失败原因',
        prop: 'desc',
        type: 'String',
        demonstrate: '失败原因',
        desc: '失败原因'
      }, {
        parameter: '案件编号错误数据',
        prop: 'out_serial_no_error',
        type: 'Long',
        demonstrate: '案件编号数组（json数组）',
        desc: '["20230327tc01","20230327tc02"]'
      }, {
        parameter: '联系人错误数据',
        prop: 'contacts_error',
        type: 'JSON',
        demonstrate: '联系人数组（json数组）;类型为指定手机号时数组必传',
        desc: '[{"mobile":"18762635463","out_serial_no":"20230327tc01"}]'
      }],
      columns3: [{
        title: '错误代码',
        key: 'parameter'
      }, {
        title: '错误信息',
        key: 'err_info'
      }],
      data3: [{
        parameter: 'CER_102',
        err_info: '该值不存在'
      }, {
        parameter: 'CER_103',
        err_info: '值类型或长度不合法'
      }]
    }
  },
  created() {
    this.apikey = this.orgApikey
  },
  methods: {
    submit() {
      if (!this.apikey) {
        this.$Message.error('请填写接口密钥')
        return
      }
      if (!this.task_id) {
        return this.$Message.error('请填写任务唯一编号')
      }
      getContactUpdateResult({
        apikey: this.apikey,
        task_id: parseInt(this.task_id)
      }).then(res => {
        this.responseData = res
      })
    }
  }
}
</script>
<style lang='less' scoped>
 :deep(.ivu-table) {
    th, td {
        height: 48px !important;
    }
}
.open_title{
font-size: 18px;
font-weight: 600;
margin-bottom: 10px;
}
.notice{
  font-size: 14px;
  background: #fff;
  padding: 18px;
}
.response_data{
  background-color: rgba(32, 125, 226, .1);
  border: 1px solid rgba(32, 125, 226, .1);
  padding: 10px;
}
.title_second{
  margin-bottom: 10px;
  font-size: 14px;
}
.title_weight{
  font-weight: 600
}
.collapse-header{
  line-height: 38px;
  background: #f8f8f9;
  border: 1px solid #e8eaec;
  padding-left: 16px;
  margin-bottom: 10px;
}
.iv-content{
  margin-top: 16px;
}
 :deep(.ivu_tables::after){
  content: "*";
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 12px;
  color: #ed4014;
}
pre {
white-space: pre-wrap;
word-wrap: break-word;
}
</style>
