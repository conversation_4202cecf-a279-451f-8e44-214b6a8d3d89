<template>
  <div>
    <p class="open_title">还款查询</p>
    <p class="title_second">此接口用于查询系统已有还款信息</p>
    <p class="title_second title_weight">HTTP头信息</p>
    <div class="collapse-header">Accept:application/json;charset=utf-8;Content-Type:application/json;charset=UTF-8</div>
    <p class="title_second title_weight">URL</p>

    <Collapse v-model="value">
      <Panel name="1">
        POST: https://&lt;servername&gt;:&lt;port&gt;/xapi/v1/openApi/repaymentList
        <span style="color: #2D8cF0; float: right; margin-right: 15px;">{{ value.includes('1') ? '收回' : '展开' }}</span>
        <template #content>
          <p>
            <Table :columns="columns" :data="data">
            </Table>
            <Button type="primary" style="margin-top:20px" @click="submit">请求</Button>
            <pre v-if="responseData" class="response_data">{{ responseData }}</pre>
          </p>
        </template>
      </Panel>
    </Collapse>
    <div class="iv-content">
      <p class="title_second title_weight">参数表格</p>
      <Table :columns="columns1" :data="data1"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">注意事项</p>
      <div class="notice">
        1.调用频率限制为1次/s。
      </div>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">返回示例</p>
      <pre class="response_data">
        {
    "data": {
        "list": [
            {
                "caseId": 0,
                "caseOperator": 0,
                "caseOperatorName": "string",
                "debtIdCard": "string",
                "debtName": "string",
                "ext11": "string",
                "ext12": "string",
                "ext13": "string",
                "ext14": "string",
                "ext15": 0,
                "ext16": 0,
                "ext17": 0,
                "ext18": 0,
                "ext19": "2019-08-24T14:15:22Z",
                "ext20": "2019-08-24T14:15:22Z",
                "ext21": "2019-08-24T14:15:22Z",
                "ext22": "2019-08-24T14:15:22Z",
                "id": 0,
                "interest": 0,
                "loanNumber": "string",
                "otherFee": 0,
                "outSerialTemp": "string",
                "penaltyInterest": 0,
                "period": 0,
                "principal": 0,
                "repaymentAmount": 0,
                "repaymentCardNo": "string",
                "repaymentFrom": "string",
                "repaymentMobile": "string",
                "repaymentStyle": "string",
                "repaymentTime": 0,
                "repaymentType": "string",
                "serviceCharge": 0
            }
        ],
        "total_elements": 50
    },
    "error_code": "string",
    "message": "success",
    "status": 1
}
    </pre>
    </div>
  </div>
</template>
<script>
import { getPaymentList } from '@open/api/org'
import { isJSON } from '@open/api/util'
import { mapGetters } from 'vuex'
import { resolveComponent } from 'vue'
export default {
  computed: {
    ...mapGetters([
      'orgApikey'
    ])
  },
  data() {
    return {
      responseData: '',
      value: '0',
      apikey: '',
      template_id: '', // 模版ID
      content: '',
      columns1: [
        {
          title: '参数名',
          key: 'parameter'
        }, {
          title: '类型',
          key: 'type'
        }, {
          title: '是否必须',
          key: 'required',
          render: (h, { row }) => {
            return h('span', row.required ? '是' : '否')
          }
        }, {
          title: '描述',
          key: 'desc',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                  href: '#',
                  onClick: () => {
                    this.$router.push({
                      path: '/'
                    })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }, {
          title: '示例',
          key: 'demonstrate'
        }
      ],
      data1: [
        {
          parameter: 'apikey',
          type: 'string',
          required: true,
          demonstrate: 'jpolkmthg09ikjh',
          href: true
        }, {
          parameter: 'repayment_time_start',
          type: 'Long',
          required: true,
          desc: '还款开始日期（含），时间戳（13位，精确到秒），还款查询起、止时间不能超过一天'
        },
        {
          parameter: 'repayment_time_end',
          type: 'Long',
          required: true,
          desc: '还款截止日期（不含），时间戳（13位，精确到秒），还款查询起、止时间不能超过一天'
        },
        {
          parameter: 'limit',
          type: 'Int',
          desc: '单次查询数量，最大限制200',
          required: true
        }, {
          parameter: 'page',
          type: 'Int',
          required: true,
          desc: '分页数'
        }
      ],
      columns: [
        {
          title: '参数名',
          key: 'parameter',
          render: (h, { row }) => {
            return h('div', {
              class: row.required ? 'ivu_tables' : ''
            }, row.parameter)
          }
        }, {
          title: '值',
          key: 'value',
          render: (h, { row }) => {
            return h(resolveComponent('Input'), {
              type: row.textarea ? 'textarea' : 'text',
              modelValue: row.parameter === 'apikey' ? this.orgApikey : '',
              onOnChange: (e) => {
                this[row.parameter] = e.target.value
              }
            })
          }
        },
        {
          title: '描述',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                  href: '#',
                  onClick: () => {
                    this.$router.push({
                      path: '/'
                    })
                  }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }, {
          title: '类型',
          key: 'type'
        }
      ],
      data: [{
        parameter: 'apikey',
        desc: '唯一接口密钥',
        href: true,
        required: true,
        type: 'string'
      }, {
        parameter: 'repayment_time_start',
        type: 'Long',
        required: true,
        desc: '还款开始日期（含），时间戳（13位，精确到秒），还款查询起、止时间不能超过一天'
      }, {
        parameter: 'repayment_time_end',
        type: 'Long',
        required: true,
        desc: '还款截止日期（不含），时间戳（13位，精确到秒），还款查询起、止时间不能超过一天'
      },
      {
        parameter: 'limit',
        type: 'Int',
        required: true,
        desc: '单次查询数量，最大限制200'
      }, {
        parameter: 'page',
        type: 'Int',
        required: true,
        desc: '分页数'
      }]
    }
  },
  created() {
    this.apikey = this.orgApikey
  },
  methods: {
    submit() {
      if (!this.apikey) {
        this.$Message.error('请填写接口密钥')
        return
      }
      if (!this.repayment_time_start) {
        this.$Message.error('开始日期')
        return
      }
      if (!this.repayment_time_end) {
        this.$Message.error('截止日期')
        return
      }
      if (!this.limit) {
        this.$Message.error('单次查询数量')
        return
      }
      if (!this.page) {
        this.$Message.error('请填写页码')
        return
      }
      // if (!this.template_id) {
      //   return this.$Message.error('请填写模版编号')
      // }
      getPaymentList({
        apikey: this.apikey,
        repayment_time_end: parseInt(this.repayment_time_end),
        repayment_time_start: parseInt(this.repayment_time_start),
        limit: parseInt(this.limit),
        page: parseInt(this.page)
      }).then(res => {
        this.responseData = res
      })
    }
  }
}
</script>
<style lang='less' scoped>
 :deep(.ivu-table) {
    th, td {
        height: 48px !important;
    }
}
.open_title{
font-size: 18px;
font-weight: 600;
margin-bottom: 10px;
}
.notice{
  font-size: 14px;
  background: #fff;
  padding: 18px;
}
.response_data{
  background-color: rgba(32, 125, 226, .1);
  border: 1px solid rgba(32, 125, 226, .1);
  padding: 10px;
}
.title_second{
  margin-bottom: 10px;
  font-size: 14px;
}
.title_weight{
  font-weight: 600
}
.collapse-header{
  line-height: 38px;
  background: #f8f8f9;
  border: 1px solid #e8eaec;
  padding-left: 16px;
  margin-bottom: 10px;
}
.iv-content{
  margin-top: 16px;
}
 :deep(.ivu_tables::after){
  content: "*";
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 12px;
  color: #ed4014;
}
pre {
white-space: pre-wrap;
word-wrap: break-word;
}
</style>
