<template>
  <div>
    <p class="open_title">获取联系人导入结果</p>
    <p class="title_second">此接口用于获取联系人导入结果</p>
    <p class="title_second title_weight">HTTP头信息</p>
    <div class="collapse-header">Accept: application/json, text/plain, */*</div>
    <p class="title_second title_weight">URL</p>

    <Collapse v-model="value">
      <Panel name="1">
        GET: https://&lt;servername&gt;:&lt;port&gt;/xapi/v1/openApi/contactImportResult

        <template #content>
          <p>
            <Table :columns="columns" :data="data">
            </Table>
            <Button type="primary" style="margin-top:20px" @click="submit">请求</Button>
            <pre v-if="responseData" class="response_data">{{ responseData }}</pre>
          </p>
        </template>
      </Panel>
    </Collapse>
    <div class="iv-content">
      <p class="title_second title_weight">参数表格</p>
      <Table :columns="columns1" :data="data1"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">返回结果</p>
      <Table :columns="columns2" :data="data2"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">传输数据错误</p>
      <Table :columns="columns3" :data="data3"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">注意事项</p>
      <div class="notice">
        1. 调用频率限制为1次／s
      </div>
    </div>

    <div class="iv-content">
      <p class="title_second title_weight">返回示例</p>
      <pre class="response_data">
                {
                    "status": 1,
                    "data": {
                        "task_status": -1,
                        "success_amt": 0,
                        "total": 1,
                        "expire_time": 1639303238000,
                        "error_data_json": [
                            {
                                "data": {
                                    "案件编号": "testbig安米1",
                                    "关系1": "朋友",
                                    "联系人1": "哈哈哈",
                                    "手机号码1": "汉字"
                                },
                                "error": [
                                    {
                                        "column_key": "手机号码1",
                                        "error_code": "CER_103",
                                        "column_value": "汉字"
                                    }
                                ]
                            }
                        ]
                    }
                }
            </pre>
    </div>
  </div>
</template>
<script>
import { contactImportantResult } from '@open/api/contacts'
import { mapGetters } from 'vuex'
import { resolveComponent } from 'vue'
export default {
  computed: {
    ...mapGetters(['orgApikey'])
  },
  data() {
    return {
      value: '0',
      apikey: '',
      task_id: '',
      responseData: '', // 请求返回的结果
      columns1: [
        {
          title: '参数名',
          key: 'parameter'
        }, {
          title: '类型',
          key: 'type'
        }, {
          title: '是否必须',
          key: 'required',
          render: (h, { row }) => {
            return h('span', row.required ? '是' : '否')
          }
        }, {
          title: '描述',
          key: 'desc',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }, {
          title: '示例',
          key: 'demonstrate'
        }
      ],
      data1: [
        {
          parameter: 'apikey',
          type: 'string',
          required: true,
          demonstrate: 'jpolkmthg09ikjh',
          href: true
        }, {
          parameter: 'task_id',
          type: 'Int',
          required: true,
          desc: '任务唯一编号',
          demonstrate: '123'
        }
      ],
      columns: [
        {
          title: '参数名',
          key: 'parameter',
          render: (h, { row }) => {
            return h('div', {
              class: row.required ? 'ivu_tables' : ''
            }, row.parameter)
          }
        }, {
          title: '值',
          key: 'value',
          render: (h, { row }) => {
            return h(resolveComponent('Input'), {
              type: row.textarea ? 'textarea' : 'text',
              modelValue: row.parameter === 'apikey' ? this.orgApikey : '',
              onOnChange: (e) => {
                this[row.parameter] = e.target.value
              }
            })
          }
        },
        {
          title: '描述',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        },
        {
          title: '类型',
          key: 'type'
        }
      ],
      data: [{
        parameter: 'apikey',
        desc: '唯一接口密钥',
        href: true,
        required: true,
        type: 'String'
      }, {
        parameter: 'task_id',
        desc: '任务唯一编号',
        required: true,
        type: 'Int'
      }],
      columns2: [{
        title: '字段名',
        key: 'parameter'
      },
      {
        title: '变量名',
        key: 'prop'
      },
      {
        title: '类型',
        key: 'type'
      },
      {
        title: '示例值',
        key: 'demonstrate'
      },
      {
        title: '描述',
        key: 'desc',
        render: (h, { row }) => {
          const desc = row.desc
          const link = h('span', [
            h('span', '详情请参照'),
            h('a', {
              href: '#',
              onClick: () => {
                this.$router.push({
                  path: '/error/backCode'
                })
              }
            }, '返回值说明')
          ])

          let description = []
          if (row.href) {
            description = [desc, link]
          } else {
            description = [desc]
          }
          return h('div', description)
        }
      }],
      data2: [{
        parameter: '返回状态码',
        prop: 'status',
        type: 'Int',
        demonstrate: '1',
        desc: '返回状态码。1: 请求成功, 0: 请求失败'
      }, {
        parameter: '错误提示',
        prop: 'message',
        type: 'String',
        demonstrate: '密码错误',
        desc: '错误提示'
      }, {
        parameter: '错误代码',
        prop: 'error_code',
        type: 'String',
        demonstrate: 'ERR_1000',
        desc: '错误代码,',
        href: true
      }, {
        parameter: '导入结果',
        prop: 'task_status',
        type: 'Int',
        demonstrate: 'OK',
        desc: '导入结果,分为0:导入中、1:导入完成和-1:导入失败'
      }, {
        parameter: '有效时间',
        prop: 'expire_time',
        type: 'Long',
        demonstrate: '1582646400000',
        desc: '导入失败的错误数据的返回有效时间，13位时间戳'
      }, {
        parameter: '错误数据',
        prop: 'error_data_json',
        type: 'JSON',
        demonstrate: '[{"data": {}, "error": []}]',
        desc: '导入失败部分的数据则显示错误数据的JSON,但当时间超过有效时间后则返回为空'
      }],
      columns3: [{
        title: '错误代码',
        key: 'parameter'
      }, {
        title: '错误信息',
        key: 'err_info'
      }],
      data3: [{
        parameter: 'CER_100',
        err_info: '必填项不能为空'
      }, {
        parameter: 'CER_101',
        err_info: '不能重复'
      }, {
        parameter: 'CER_102',
        err_info: '该值不存在'
      }, {
        parameter: 'CER_103',
        err_info: '值类型或长度不合法'
      }]
    }
  },
  created() {
    this.apikey = this.orgApikey
  },
  methods: {
    submit() {
      if (!this.apikey) {
        this.$Message.error('请填写接口密钥')
        return
      }
      if (!this.task_id) {
        return this.$Message.error('请填写任务唯一编号')
      }
      contactImportantResult({
        apikey: this.apikey,
        task_id: parseInt(this.task_id)
      }).then(res => {
        this.responseData = res
      })
    }
  }
}
</script>
<style lang='less' scoped>
 :deep(.ivu-table) {
    th, td {
        height: 48px !important;
    }
}
.open_title{
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}
.notice{
    font-size: 14px;
    background: #fff;
    padding: 18px;
}
.response_data{
    background-color: rgba(32, 125, 226, .1);
    border: 1px solid rgba(32, 125, 226, .1);
    padding: 10px;
}
.title_second{
    margin-bottom: 10px;
    font-size: 14px;
}
.title_weight{
    font-weight: 600
}
.collapse-header{
    line-height: 38px;
    background: #f8f8f9;
    border: 1px solid #e8eaec;
    padding-left: 16px;
    margin-bottom: 10px;
}
.iv-content{
    margin-top: 16px;
}
 :deep(.ivu_tables::after){
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 1;
    font-family: SimSun;
    font-size: 12px;
    color: #ed4014;
}
pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
