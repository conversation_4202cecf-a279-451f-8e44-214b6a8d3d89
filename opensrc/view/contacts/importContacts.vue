<template>
  <div>
    <p class="open_title">联系人导入</p>
    <p class="title_second">此接口用于导入联系人信息</p>
    <p class="title_second title_weight">HTTP头信息</p>
    <div class="collapse-header">Accept:application/json;charset=utf-8;Content-Type:application/json;charset=UTF-8</div>
    <p class="title_second title_weight">URL</p>
    <Collapse v-model="value">
      <Panel name="1">
        POST: https://&lt;servername&gt;:&lt;port&gt;/xapi/v1/openApi/contactImport
        <span style="color: #2D8cF0; float: right; margin-right: 15px;">{{ value.includes('1') ? '收回' : '展开' }}</span>
        <template #content>
          <p>
            <Table :columns="columns" :data="data">
            </Table>
            <Button type="primary" style="margin-top:20px" @click="doRequest">请求</Button>
            <pre v-if="responseData" class="response_data">{{ responseData }}</pre>
          </p>
        </template>
      </Panel>
    </Collapse>
    <div class="iv-content">
      <p class="title_second title_weight">参数表格</p>
      <Table :columns="columns1" :data="data1"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">注意事项</p>
      <div class="notice">
        1. content参数中的所有数据均为String类型（包括时间），格式为yyyy/MM/dd；<br>
        2. 联系人导入一共有 2 个接口，分别为联系人导入接口和获取导入结果接口，由于数据量大，需为异步操作；<br>
        3. 每次最多传输 5000 条数据，传输频次过大则任务将失败；<br>
        <!-- 4. 委案公司名称需要与业务系统一致。 -->
      </div>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">返回示例</p>
      <pre class="response_data">
                {
                    "status": 1,
                    "data": {
                        "task_id": 4849,
                        "total_count": 1
                    }
                }
            </pre>
    </div>
  </div>
</template>

<script>
import { importContacts } from '@open/api/contacts'
import { isJSON } from '@open/api/util'
import { mapGetters } from 'vuex'
import { resolveComponent } from 'vue'

export default {
  data() {
    return {
      responseData: '', // 请求返回的数据
      value: '0',
      apikey: '',
      out_batch_no: '',
      content: '',
      columns1: [
        {
          title: '参数名',
          key: 'parameter'
        }, {
          title: '类型',
          key: 'type'
        }, {
          title: '是否必须',
          key: 'required'
        }, {
          title: '描述',
          key: 'desc',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }, {
          title: '示例',
          key: 'demonstrate'
        }
      ],
      data1: [
        {
          parameter: 'apikey',
          type: 'String',
          required: '是',
          demonstrate: 'jpolkmthg09ikjh',
          href: true
        }, {
          parameter: 'content',
          type: 'Array',
          required: '是',
          desc: '联系人信息 JSON 数组',
          demonstrate: `[{"mobile":"18762635003","name":"王小五","out_serial_no":"2222","relation":"朋友","contact_type_name":"朋友"}]`
        }
      ],
      columns: [
        {
          title: '参数名',
          key: 'parameter',
          render: (h, { row }) => {
            return h('div', {
              class: row.required ? 'ivu_tables' : ''
            }, row.parameter)
          }
        }, {
          title: '值',
          key: 'value',
          render: (h, { row }) => {
            return h(resolveComponent('Input'), {
              type: row.textarea ? 'textarea' : 'text',
              modelValue: row.parameter === 'apikey' ? this.orgApikey : '',
              onOnChange: (e) => {
                this[row.parameter] = e.target.value
              }
            })
          }
        },
        {
          title: '描述',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        },
        {
          title: '类型',
          key: 'type'
        }
      ],
      data: [{
        parameter: 'apikey',
        desc: '唯一接口密钥',
        href: true,
        type: 'String',
        required: true
      }, {
        parameter: 'content',
        type: 'Array',
        desc: '联系人信息 JSON 数组',
        textarea: true,
        required: true
      }]
    }
  },
  computed: {
    ...mapGetters([
      'orgApikey'
    ])
  },
  created() {
    this.apikey = this.orgApikey
  },
  methods: {
    pageTo() {
      this.$router.push({
        path: 'getResults'
      })
    },
    doRequest() {
      if (!this.apikey) {
        this.$Message.error('请填写接口密钥')
        return
      }
      if (!isJSON(this.content)) {
        this.$Message.error('content参数中请输入正确的JSON格式的值')
        return
      }

      const data = {
        apikey: this.apikey,
        content: JSON.parse(this.content)
      }

      importContacts(data).then(res => {
        this.responseData = res
      })
    }
  }
}
</script>

<style lang="less" scoped>
 :deep(.ivu-table) {
    th, td {
        height: 48px !important;
    }
}
.open_title{
font-size: 18px;
font-weight: 600;
margin-bottom: 10px;
}
.notice{
font-size: 14px;
background: #fff;
padding: 18px;
}
.response_data{
background-color: rgba(32, 125, 226, .1);
border: 1px solid rgba(32, 125, 226, .1);
padding: 10px;
}
.title_second{
margin-bottom: 10px;
font-size: 14px;
}
.title_weight{
font-weight: 600;
}
.collapse-header{
line-height: 38px;
background: #f8f8f9;
border: 1px solid #e8eaec;
padding-left: 16px;
margin-bottom: 10px;
}
.iv-content{
margin-top: 16px;
}
 :deep(.ivu_tables::after){
content: "*";
display: inline-block;
margin-right: 4px;
line-height: 1;
font-family: SimSun;
font-size: 12px;
color: #ed4014;
}
pre {
white-space: pre-wrap;
word-wrap: break-word;
}
</style>
