<template>
  <div>
    <p class="open_title">返回值说明</p>
    <p class="title_second">状态码说明</p>
    <pre class="response_data">
      status = 1: 请求成功，并返回了对应结果
      status = 0: 请求失败，失败原因可参考错误提示与错误代码
    </pre>
<div class="iv-content">
    <p class="title_second title_weight">返回示例</p>
    <pre class="response_data">
      {
        "status": 1,
        "data": [
          {
            "id": 10000359,
            "import_time": 1575717967887,
            "total_count": 500
          }
        ]
      }
    </pre>
    </div>
    <div class="iv-content">
    <p class="title_second title_weight">错误代码</p>
    <Table :columns="columns1" :data="data1"></Table>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      columns1: [
        {
          title: '错误代码',
          key: 'parameter'
        }, {
          title: '错误信息',
          key: 'err_info'
        }, {
          title: '解决办法',
          key: 'solution'
        }
      ],
      data1: [
        {
          parameter: 'ERR_1000',
          err_info: 'apikey不能为空',
          solution: '请检查apikey是否未填写'
        }, {
          parameter: 'ERR_1001',
          err_info: 'apikey不合法',
          solution: '请检查apikey是否正确'
        }, {
          parameter: 'ERR_1002',
          err_info: '找不到该总公司',
          solution: '请检查公司id是否正确'
        }, {
          parameter: 'ERR_1003',
          err_info: '当前公司不在合作中',
          solution: '请检查公司是否在合作时间内'
        }, {
          parameter: 'ERR_1004',
          err_info: '未找到该模板',
          solution: '请检查模板id是否正确'
        }, {
          parameter: 'ERR_1005',
          err_info: '未找到该公司',
          solution: '请检查公司id是否正确'
        }, {
          parameter: 'ERR_1006',
          err_info: '传输数据不能为空',
          solution: '请检查传输数据数组是否为空'
        }, {
          parameter: 'ERR_1007',
          err_info: '超过传输数据范围',
          solution: '请检查单次调用传输数据是否超过了5000条'
        }, {
          parameter: 'ERR_1008',
          err_info: '找不到该任务',
          solution: '请检查任务id是否正确'
        }, {
          parameter: 'ERR_1009',
          err_info: '当前公司下找不到该产品',
          solution: '请检查产品id是否正确'
        }, {
          parameter: 'ERR_1010',
          err_info: '案件批次号不能为空',
          solution: '请检查案件批次号是否为空'
        }, {
          parameter: 'ERR_1014',
          err_info: '当前模板类型和功能不匹配',
          solution: '请检查该模板id是否是对应的导入功能'
        }, {
          parameter: 'ERR_1015',
          err_info: '案件变更状态不能为空',
          solution: '请检查案件变更状态是否为空'
        }, {
          parameter: 'ERR_1016',
          err_info: '案件变更状态不合法',
          solution: '请检查案件变更状态是否在正确范围内(结案:1; 停催:2; 留案:4; 停催恢复:5; 作废:-1; 退案:8;)'
        }, {
          parameter: 'ERR_1017',
          err_info: '留案时间不能为空',
          solution: '请检查案件变更状态为留案操作时，留案时间是否为空'
        }, {
          parameter: 'ERR_9999',
          err_info: '其他错误',
          solution: ''
        }
      ]
    }
  }
}
</script>
<style lang='less' scoped>
 :deep(.ivu-table) {
    th, td {
        height: 48px !important;
    }
}
.open_title{
font-size: 18px;
font-weight: 600;
margin-bottom: 10px;
}
.notice{
  font-size: 14px;
  background: #fff;
  padding: 18px 0 0 18px;
}
.response_data{
  background-color: rgba(32, 125, 226, .1);
  border: 1px solid rgba(32, 125, 226, .1);
  padding: 10px;
}
.title_second{
  margin-bottom: 10px;
  font-size: 14px;
}
.title_weight{
  font-weight: 600
}

</style>
