<template>
  <div>
    <p class="open_title">案件导出</p>
    <p class="title_second">此接口用于案件导出</p>
    <p class="title_second title_weight">HTTP头信息</p>
    <div class="collapse-header">Accept:application/json;charset=utf-8;Content-Type:application/json;charset=UTF-8</div>
    <p class="title_second title_weight">URL</p>
    <Collapse v-model="value">
      <Panel name="1">
        POST: https://&lt;servername&gt;:&lt;port&gt;/xapi/v1/openApi/caseExport
        <span style="color: #2D8cF0; float: right; margin-right: 15px;">{{ value.includes('1') ? '收回' : '展开' }}</span>

        <template #content>
          <p>
            <Table :columns="columns" :data="data"></Table>
            <Button type="primary" style="margin-top:20px" @click="doRequest">请求</Button>
            <pre v-if="responseData" class="response_data">{{ responseData }}</pre>
          </p>
        </template>
      </Panel>
    </Collapse>
    <div class="iv-content">
      <p class="title_second title_weight">返回参数表格</p>
      <Table :columns="columns1" :data="data1"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">注意事项</p>
      <div class="notice">
        1.单次最多导出200条案件数;<br>
        2.调用频率限制为1次/s。
      </div>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">返回示例</p>
      <pre class="response_data">
          {
              "create_time": 1638497685000,
              "update_time": 1649645450000,
              "create_by": "李四",
              "update_by": "李四",
              "batch_no": "3万",
              "name": "张三3215",
              "out_serial_no": "testbig安米3215",
              "id_card": "312222281838192",
              "mobile": "13082813729",
              "amount": 4214000,
              "overdue_days": 10,
              "overdue_date": 1637596800000,
              "product_name": "产品991",
              "follow_count": 0,
              "content":
              {
                  "合同编号": "199922",
                  "委外手数": "15",
                  "诉讼状态": "622202123456789220",
                  "企业社会统一信用代码": "xxddd",
                  "企业名称": "度言软件"
              },
              "contacts":
              [
                  {
                      "name": "张三",
                      "relation": "朋友",
                      "mobile": "18777777777"
                  },
                  {
                      "name": "李四",
                      "relation": "家人",
                      "mobile": "18888888888"
                  }
              ]
          }
      </pre>
    </div>
  </div>
</template>

<script>
import { caseExport } from '@open/api/case'
import { mapGetters } from 'vuex'
import { isJSON } from '@open/api/util'
import { resolveComponent } from 'vue'

export default {
  data() {
    return {
      responseData: '', // 请求返回的数据
      value: '0',
      apikey: '',
      start_id: null,
      operation_time_start: null,
      operation_time_end: null,
      limit: null,
      id_cards: null,
      out_serial_no_list: null,
      columns1: [
        {
          title: '参数名',
          key: 'parameter'
        }, {
          title: '类型',
          key: 'type'
        }, {
          title: '描述',
          key: 'desc',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }
      ],
      data1: [
        {
          parameter: 'create_time',
          type: 'Long',
          desc: '创建时间,13位时间戳'
        }, {
          parameter: 'update_time',
          type: 'Long',
          desc: '更新时间,13位时间戳'
        }, {
          parameter: 'create_by',
          type: 'String',
          desc: '创建人'
        }, {
          parameter: 'update_by',
          type: 'String',
          desc: '修改人'
        }, {
          parameter: 'batch_no',
          type: 'String',
          desc: '批次号'
        }, {
          parameter: 'name',
          type: 'String',
          desc: '姓名'
        }, {
          parameter: 'out_serial_no',
          type: 'String',
          desc: '案件编号'
        }, {
          parameter: 'id_card',
          type: 'String',
          desc: '身份证号'
        }, {
          parameter: 'mobile',
          type: 'String',
          desc: '手机号'
        }, {
          parameter: 'amount',
          type: 'Float',
          desc: '金额'
        }, {
          parameter: 'overdue_date',
          type: 'Long',
          desc: '逾期日期,13位时间戳'
        }, {
          parameter: 'product_name',
          type: 'String',
          desc: '产品名称'
        }, {
          parameter: 'out_batch_no',
          type: 'String',
          desc: '外部批次号'
        }, {
          parameter: 'follow_count',
          type: 'Integer',
          desc: '跟进次数'
        }, {
          parameter: 'content',
          type: 'Map',
          desc: '案件信息数据（json格式）自定义字段'
        }, {
          parameter: 'contacts',
          type: 'Array',
          desc: '联系人列表[{"name":"张三","relation":"朋友","mobile":"18771717171"}]'
        }
      ],
      columns: [
        {
          title: '参数名',
          key: 'parameter',
          render: (h, { row }) => {
            return h('div', {
              class: row.required ? 'ivu_tables' : ''
            }, row.parameter)
          }
        }, {
          title: '值',
          key: 'value',
          render: (h, { row }) => {
            return h(resolveComponent('Input'), {
              type: row.textarea ? 'textarea' : 'text',
              modelValue: row.parameter === 'apikey' ? this.orgApikey : '',
              onOnChange: (e) => {
                this[row.parameter] = e.target.value
              }
            })
          }
        },
        {
          title: '描述',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                href: '#',
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        },
        {
          title: '类型',
          key: 'type'
        }
      ],
      data: [{
        parameter: 'apikey',
        desc: '唯一接口密钥',
        href: true,
        type: 'String',
        required: true
      }, {
        parameter: 'name',
        type: 'String',
        desc: '债务人姓名',
        required: false
      }, {
        parameter: 'out_serial_no_list',
        type: 'Array',
        desc: '案件编号 ["20230327tc01", "20230327tc02"]',
        required: false,
        textarea: true,
        example: `["20230327tc01","20230327tc02"]`
      }, {
        parameter: 'out_batch_no',
        type: 'String',
        desc: '批次号',
        required: false
      }, {
        parameter: 'own_mobile',
        type: 'String',
        desc: '债务人手机号',
        required: false
      }, {
        parameter: 'amount_start',
        type: 'Float',
        desc: '案件金额开始',
        required: false
      }, {
        parameter: 'amount_end',
        type: 'Float',
        desc: '案件金额结束',
        required: false
      }, {
        parameter: 'create_time_start',
        type: 'Long',
        desc: '创建开始时间(13位时间戳)',
        required: false
      }, {
        parameter: 'create_time_end',
        type: 'Long',
        desc: '创建结束时间(13位时间戳)',
        required: false
      }, {
        parameter: 'id_cards',
        type: 'Array',
        desc: '身份证号',
        required: false
      }, {
        parameter: 'page',
        type: 'Int',
        desc: '页码',
        required: true
      }, {
        parameter: 'limit',
        type: 'Int',
        desc: '单页条数',
        required: true
      }]
    }
  },
  computed: {
    ...mapGetters([
      'orgApikey'
    ])
  },
  created() {
    this.apikey = this.orgApikey
  },
  methods: {
    doRequest() {
      // if (!this.apikey) {
      //   this.$Message.error('请填写接口密钥')
      //   return
      // }
      // if (!this.page) {
      //   this.$Message.error('请填写页码')
      //   return
      // }
      // if (!this.limit) {
      //   this.$Message.error('请填写单页条数')
      //   return
      // }

      // let id_cards = null
      // if (this.id_cards) {
      //   try {
      //     let str = this.id_cards.trim()
      //     if (str[0] === '[') {
      //       str = str.slice(1)
      //     }
      //     if (str[str.length - 1] === ']') {
      //       str = str.slice(0, str.length - 1)
      //     }
      //     id_cards = str.split(',').map(_c => _c.trim().replace(/(\"|')/g, '')).join(',')
      //   } catch (error) {
      //     this.$Message.error('请填写正确的身份证号')
      //     return
      //   }
      // }
      // if (!isJSON(this.out_serial_no_list)) {
      //   this.$Message.error('out_serial_no_list参数中请输入正确的JSON格式的值')
      //   return
      // }
      console.log(this.o)
      const data = {
        apikey: this.apikey,
        page: parseInt(this.page),
        limit: parseInt(this.limit),
        name: this.name || null,
        out_serial_no_list: this.out_serial_no_list ? JSON.parse(this.out_serial_no_list) : null,
        out_batch_no: this.out_batch_no || null,
        own_mobile: this.own_mobile || null,
        amount_start: this.amount_start ? parseInt(this.amount_start) : null,
        amount_end: this.amount_end ? parseInt(this.amount_end) : null,
        create_time_start: this.create_time_start ? parseInt(this.create_time_start) : null,
        create_time_end: this.create_time_end ? parseInt(this.create_time_end) : null,
        id_cards: this.id_cards ? JSON.parse(this.id_cards) : null
      }

      caseExport(data).then(res => {
        this.responseData = res
      })
    }
  }
}
</script>

<style lang="less" scoped>
 :deep(.ivu-table) {
    th, td {
        height: 48px !important;
    }
}
.open_title{
font-size: 18px;
font-weight: 600;
margin-bottom: 10px;
}
.notice{
font-size: 14px;
background: #fff;
padding: 18px;
}
.response_data{
background-color: rgba(32, 125, 226, .1);
border: 1px solid rgba(32, 125, 226, .1);
padding: 10px;
}
.title_second{
margin-bottom: 10px;
font-size: 14px;
}
.title_weight{
font-weight: 600;
}
.collapse-header{
line-height: 38px;
background: #f8f8f9;
border: 1px solid #e8eaec;
padding-left: 16px;
margin-bottom: 10px;
}
.iv-content{
margin-top: 16px;
}
 :deep(.ivu_tables::after){
content: "*";
display: inline-block;
margin-right: 4px;
line-height: 1;
font-family: SimSun;
font-size: 12px;
color: #ed4014;
}
pre {
white-space: pre-wrap;
word-wrap: break-word;
}
</style>
