<template>
  <div>
    <p class="open_title">更新案件状态</p>
    <p class="title_second">此接口用于更新案件状态</p>
    <p class="title_second title_weight">HTTP头信息</p>
    <div class="collapse-header">Accept:application/json;charset=utf-8;Content-Type:application/json;charset=UTF-8</div>
    <p class="title_second title_weight">URL</p>
    <Collapse v-model="value">
      <Panel name="1">
        POST: https://&lt;servername&gt;:&lt;port&gt;/xapi/v1/openApi/caseChangeStatus
        <span style="color: #2D8cF0; float: right; margin-right: 15px;">{{ value.includes('1') ? '收回' : '展开' }}</span>
        <template #content>
          <p>
            <Table :columns="columns" :data="data"></Table>
            <Button type="primary" style="margin-top:20px" @click="doRequest">请求</Button>
            <pre v-if="responseData" class="response_data">{{ responseData }}</pre>
          </p>
        </template>
      </Panel>
    </Collapse>
    <div class="iv-content">
      <p class="title_second title_weight">参数表格</p>
      <Table :columns="columns1" :data="data1"></Table>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">注意事项</p>
      <div class="notice">
        1. content参数中的所有数据均为String类型（包括时间），格式为yyyy/MM/dd；<br>
        2. 由于更新案件状态数据量大，需为异步操作，则分为更新案件状态和获取更新结果2个界面；<br>
        3. 根据案件编号定位案件修改案件状态，传参时案件状态名称需与本系统保持一致，如退案、留案等；<br>
        4. 目前最多一次性传输5000条数据，超过5000条将导入失败；
      </div>
    </div>
    <div class="iv-content">
      <p class="title_second title_weight">返回示例</p>
      <pre class="response_data">
                {
                    "status": 1,
                    "data": {
                        "task_id": 958,
                        "total_count": 1
                    }
                }
            </pre>
    </div>
  </div>
</template>

<script>
import { caseChangeStatus } from '@open/api/case'
import { isJSON } from '@open/api/util'
import { mapGetters } from 'vuex'
import { resolveComponent } from 'vue'

export default {
  data() {
    return {
      responseData: '',
      value: '0',
      apikey: '',
      change_status: '',
      reason: '',
      delay_time: '',
      restart_time: '',
      content: '',
      columns1: [
        {
          title: '参数名',
          key: 'parameter'
        }, {
          title: '类型',
          key: 'type'
        }, {
          title: '是否必须',
          key: 'required'
        }, {
          title: '描述',
          key: 'desc',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                props: {
                  href: '#'
                },
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        }, {
          title: '示例',
          key: 'example'
        }
      ],
      data1: [
        {
          parameter: 'apikey',
          type: 'String',
          required: '是',
          href: true,
          example: 'jpolkmthg09ikjh'
        }, {
          parameter: 'change_status',
          type: 'Int',
          required: '是',
          desc: '正常:0; 留案:1; 停催:2; 结案:3;(如停催需要恢复时，请直接传恢复后的状态)',
          example: '0'
        }, {
          parameter: 'reason',
          type: 'String',
          required: '是',
          desc: '原因',
          example: '更改状态'
        }, {
          parameter: 'delay_time',
          type: 'Long',
          required: '否(更新为留案状态时必填,13位时间戳)',
          desc: '留案时间',
          example: '1582646400000'
        }, {
          parameter: 'restart_time',
          type: 'Long',
          required: '否(更新为留案状态时选填,13位时间戳)',
          desc: '重设案件开始日期',
          example: '1582646400000'
        }, {
          parameter: 'content',
          type: 'List',
          required: '是',
          desc: '案件编号数组(json数组)',
          example: ` ["20200101xy01","20200101xy02","20200101xy03"]`
        }
      ],
      columns: [
        {
          title: '参数名',
          key: 'parameter',
          render: (h, { row }) => {
            return h('div', {
              class: row.required === true ? 'ivu_tables' : ''
            }, row.parameter)
          }
        },
        {
          title: '值',
          key: 'value',
          render: (h, { row }) => {
            return h(resolveComponent('Input'), {
              type: row.textarea ? 'textarea' : 'text',
              modelValue: row.parameter === 'apikey' ? this.orgApikey : '',
              onOnChange: (e) => {
                this[row.parameter] = e.target.value
              }
            })
          }
        },
        {
          title: '描述',
          render: (h, { row }) => {
            return row.href ? h('div', [
              h('span', '唯一接口密钥，可在'),
              h('a', {
                props: {
                  href: '#'
                },
                onClick: () => {
                  this.$router.push({
                    path: '/'
                  })
                }
              }, '管理控制台'),
              h('span', '获取')
            ]) : h('span', row.desc)
          }
        },
        {
          title: '类型',
          key: 'type'
        }
      ],
      data: [{
        parameter: 'apikey',
        desc: '唯一接口密钥',
        type: 'String',
        href: true,
        required: true
      }, {
        parameter: 'change_status',
        type: 'Int',
        required: '是',
        desc: '正常:0; 留案:1; 停催:2; 结案:3;(如停催需要恢复时，请直接传恢复后的状态)',
        required: true
      }, {
        parameter: 'reason',
        type: 'String',
        required: '是',
        desc: '原因',
        required: true
      }, {
        parameter: 'delay_time',
        type: 'Long',
        required: '否(更新为留案状态时必填,13位时间戳)',
        desc: '留案时间'
      }, {
        parameter: 'restart_time',
        type: 'Long',
        required: '否(更新为留案状态时选填,13位时间戳)',
        desc: '重设案件开始日期',
        example: '1582646400000'
      }, {
        parameter: 'content',
        type: 'List',
        required: '是',
        desc: '案件编号数组(json数组)',
        textarea: true,
        required: true
      }]
    }
  },
  computed: {
    ...mapGetters([
      'orgApikey'
    ])
  },
  created() {
    this.apikey = this.orgApikey
  },
  methods: {
    doRequest() {
      if (!this.apikey) {
        this.$Message.error('请填写接口密钥')
        return
      }
      if (!this.change_status) {
        this.$Message.error('请填写状态码')
        return
      }
      if (!this.reason) {
        this.$Message.error('请填写原因')
        return
      }
      if (!isJSON(this.content)) {
        this.$Message.error('content参数中请输入正确的JSON格式的值')
        return
      }
      const data = {
        apikey: this.apikey,
        change_status: parseInt(this.change_status),
        delay_time: parseInt(this.delay_time),
        restart_time: parseInt(this.restart_time),
        reason: this.reason,
        content: JSON.parse(this.content)
      }

      caseChangeStatus(data).then(res => {
        this.responseData = res
      })
    }
  }
}
</script>

<style lang="less" scoped>
 :deep(.ivu-table) {
    th, td {
        height: 48px !important;
    }
}
.open_title{
font-size: 18px;
font-weight: 600;
margin-bottom: 10px;
}
.notice{
font-size: 14px;
background: #fff;
padding: 18px;
}
.response_data{
background-color: rgba(32, 125, 226, .1);
border: 1px solid rgba(32, 125, 226, .1);
padding: 10px;
}
.title_second{
margin-bottom: 10px;
font-size: 14px;
}
.title_weight{
font-weight: 600;
}
.collapse-header{
line-height: 38px;
background: #f8f8f9;
border: 1px solid #e8eaec;
padding-left: 16px;
margin-bottom: 10px;
}
.iv-content{
margin-top: 16px;
}
:deep(.ivu_tables)::after {
content: "*";
display: inline-block;
margin-right: 4px;
line-height: 1;
font-family: SimSun;
font-size: 12px;
color: #ed4014;
}
pre {
white-space: pre-wrap;
word-wrap: break-word;
}
</style>
