// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import { createApp } from 'vue'
import ViewUIPlus from 'view-ui-plus'
import App from '@/App'
import router from './router'
import routes from './router/routers'
import store from '@/store'
import i18n from '@/locale'
import config from '@/config'
import 'view-ui-plus/dist/styles/viewuiplus.css'
import importDirective from '@/directive'
import '@/style/index.less'
import '@/assets/iconfont/index.css'
import '@/assets/icon/iconfont.css'

/**
 * @description 注册admin内置插件
 */
/**
 * @description 生产环境关掉提示
 */
/**
 * @description 全局注册应用配置
 */
 const app = createApp(App)
 app.config.globalProperties.$config = config
/**
 * 注册指令
 */
importDirective(app)

window.isAdminSys = true

window.VueMainInstance = app
  .use(store)
  .use(i18n)
  .use(router)
  .use(ViewUIPlus, {
    i18n
  })
  .mount('#app')

window.VueMainInstance.adminRoutes = routes
