import { get, post, put } from '@/libs/api.request'

export const importContacts = (data) => {
    return post('/v1/openApi/contactImport', data , { openApi: true })
}

export const contactImportantResult = (data) => {
    return get('/v1/openApi/contactImportResult', data, { openApi: true })
}

export const invalidContact = (data) => {
  return post('/v1/openApi/invalidContact', data, { openApi: true })
}

export const getContactUpdateResult = (data) => {
  return get('/v1/openApi/queryInvalidContactResult', data, { openApi: true })
}
