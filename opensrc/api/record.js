import { get, post, put } from '@/libs/api.request'

export const operationImport = (data) => {
    return post('/v1/openApi/operationImport', data, { openApi: true })
}

export const queryOperationImportResult = (data) => {
    return get('/v1/openApi/queryOperationImportResult', data, { openApi: true })
}

export const operationList = (data) => {
    return get('/v1/openApi/operationList', data, { openApi: true })
}
