import { get, post, put } from '@/libs/api.request'

export const getTplFields = (data) => {
  return get('v1/openApi/tplFields', data, { openApi: true })
}

export const getCaseResult = (data) => {
  return get('v1/openApi/caseImportResult', data, { openApi: true })
}

export const caseImport = (data) => {
  return post('v1/openApi/caseImport', data, { openApi: true })
}

export const caseChangeStatus = (data) => {
  return post('v1/openApi/caseChangeStatus', data, { openApi: true })
}

export const getCaseStatusResults = (data) => {
  return get('/v1/openApi/queryCaseChangeResult', data, { openApi: true })
}

export const caseUpdate = (data) => {
  return post('/v1/openApi/caseUpdateImport', data, { openApi: true })
}

export const getCaseUpdateResult = (data) => {
  return get('/v1/openApi/queryCaseUpdateImportResult', data, { openApi: true })
}

export const caseExport = (data) => {
  return post('/v1/openApi/caseExport', data, { openApi: true })
}
