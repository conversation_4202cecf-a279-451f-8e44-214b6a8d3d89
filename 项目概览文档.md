# Sing-Service 项目概览文档

## 项目基本信息

### 项目标识
- **项目名称**: sing-service
- **项目描述**: 后端服务
- **版本**: 0.0.1
- **包名**: com.post.server.sing-service
- **主类**: com.sing.service.SingServiceApplication

### 技术栈
- **Java版本**: 17
- **Spring Boot版本**: 2.7.8
- **Spring Cloud版本**: 2021.0.8
- **构建工具**: Maven
- **服务端口**: 8081

## 核心功能模块

### 1. 电子签章服务 (E-Sign)
项目的核心功能是提供电子签章服务，基于e签宝平台实现。

#### 主要功能
- **个人认证+签章**: 提供用户身份认证和电子签章功能
- **获取签章链接**: 生成签章操作的URL链接
- **文件上传与处理**: 支持PDF文件上传和模板填充
- **文件位置信息获取**: 根据关键字获取文件中的签章位置
- **OSS文件管理**: 文件上传到阿里云OSS并获取下载链接
- **合同管理**: 保存和管理合同协议信息

#### 核心接口
- `POST /esign/sign` - 个人认证+签章
- `GET /esign/getSignUrl` - 获取签章链接
- `POST /esign/uploadFileAndGetPositions` - 上传文件并获取位置信息
- `POST /esign/uploadFile` - 上传文件到OSS
- `POST /esign/createFileFromTemplate` - 填充PDF模版文件
- `GET /esign/getOssUrl` - 获取文件下载地址
- `POST /esign/saveContract` - 保存合同表内容

## 技术架构

### 数据库层
- **ORM框架**: MyBatis-Plus 3.2.0
- **数据库连接池**: Druid 1.2.16
- **支持数据库**: MySQL、SQL Server、SQLite
- **分页插件**: PageHelper 1.4.6

### 缓存层
- **Redis**: Spring Data Redis
- **本地缓存**: Caffeine 3.1.8

### 外部集成
- **配置中心**: Apollo (Ctrip)
- **服务注册**: Eureka Client
- **文件存储**: 阿里云OSS
- **电子签章**: e签宝 PaaS SDK

### 工具库
- **JSON处理**: FastJSON2 2.0.26
- **工具类**: Hutool 5.8.15
- **HTTP客户端**: Apache HttpClient
- **二维码**: ZXing
- **代码简化**: Lombok

## 项目结构

```
src/main/java/com/sing/service/
├── SingServiceApplication.java          # 主启动类
├── config/                              # 配置类
│   ├── CorsConfig.java                 # 跨域配置
│   ├── ESignConfig.java                # e签宝配置
│   ├── Knife4jConfiguration.java       # API文档配置
│   ├── MybatisPlusConfig.java          # MyBatis-Plus配置
│   └── OssConfig.java                  # OSS配置
├── controller/                          # 控制器层
│   └── ESignController.java            # 电子签章控制器
├── service/                            # 服务层
│   ├── ESignService.java               # 电子签章服务接口
│   ├── FileService.java                # 文件服务接口
│   ├── SignAccountService.java         # 签章账户服务接口
│   ├── SignContractService.java        # 合同服务接口
│   ├── SigncontractLogService.java     # 合同日志服务接口
│   └── impl/                           # 服务实现类
├── dao/                                # 数据访问层
│   ├── SignAccountMapper.java          # 签章账户Mapper
│   ├── SignContractLogMapper.java      # 合同日志Mapper
│   └── SignContractMapper.java         # 合同Mapper
├── entity/                             # 实体类
│   ├── SignAccount.java                # 签章账户实体
│   ├── SignContract.java               # 合同实体
│   ├── SignContractLog.java            # 合同日志实体
│   ├── dto/                            # 数据传输对象
│   └── vo/                             # 视图对象
├── util/                               # 工具类
│   ├── AESUtils.java                   # AES加密工具
│   ├── EsignFileUtils.java             # e签宝文件工具
│   ├── EsignHelperUtils.java           # e签宝辅助工具
│   ├── FileUtils.java                  # 文件工具
│   ├── UUIDUtils.java                  # UUID工具
│   └── enums/                          # 枚举类
├── constant/                           # 常量类
├── error/                              # 异常处理
└── response/                           # 响应封装
```

## 数据库设计

### 核心表结构
1. **sign_account** - 签章账户表
2. **sign_contract** - 合同协议表
3. **sign_contract_log** - 合同操作日志表

### 合同表字段说明
- `contract_type`: 合同类型 (1-e签宝相关协议)
- `contract_name`: 协议名称
- `traffic_code`: 流量标识
- `contract_url`: 协议地址
- `contract_system`: 系统来源 (1-资金系统, 2-营销系统)
- `contract_code`: 协议编码

## 配置说明

### Apollo配置中心
- **应用ID**: sing-service
- **环境**: UAT
- **Meta服务器**: http://119.45.118.131:8086
- **命名空间**: application

### 日志配置
- **日志文件**: /home/<USER>/logs/sing-service/sing-service.log
- **日志格式**: 包含traceId和spanId的分布式追踪格式
- **日志滚动**: 最大500MB，总容量1500MB

### API文档
- **框架**: Knife4j (Swagger增强版)
- **语言**: 中文
- **访问路径**: /doc.html

## 业务特性

### 1. 分布式追踪
- 集成Spring Cloud Sleuth
- 支持链路追踪和性能监控

### 2. 限流和队列
- Redis实现的请求队列机制
- 支持限流控制，防止系统过载

### 3. 事务管理
- 支持声明式事务
- 事务同步机制确保数据一致性

### 4. 异常处理
- 统一异常处理机制
- 自定义业务异常类型

## 部署信息

### 服务配置
- **服务名**: sing-service
- **端口**: 8081
- **Tomcat配置**: 
  - 最大线程数: 500
  - 最小空闲线程: 20
  - 最大连接数: 10000

### 外部依赖
- **e签宝服务**: 电子签章核心服务
- **阿里云OSS**: 文件存储服务
- **Apollo**: 配置管理服务
- **Eureka**: 服务注册与发现
- **Redis**: 缓存和队列服务
- **数据库**: MySQL/SQL Server

## 开发规范

### 代码规范
- 使用Lombok简化代码
- 统一的响应格式封装
- 完善的API文档注解
- 规范的包结构和命名

### 安全考虑
- AES加密工具支持
- 请求限流机制
- 文件上传安全检查

## 监控和运维

### 健康检查
- Spring Boot Actuator集成
- 服务状态监控

### 日志管理
- 结构化日志输出
- 分布式追踪支持
- 日志文件自动滚动

---

**文档生成时间**: 2025-07-03  
**项目负责人**: Mr.sandman  
**公司**: 中数金智(上海)有限公司
