# 手动触发同步指定范围内的case_log数据到es
# 脚本运行：python3 sync_case_log_touch.py start end
import json
from datasync.base_data_to_es import DataToEs
from datasync.datetime_to_json import DateEncoder
from concurrent.futures import ThreadPoolExecutor
import threading
import sys

from utils.logger_util import LOG

es_index = "bpw_case_info_query"
es_type = "_doc"

logger = LOG.get_logger("sync_bpw_case_info_touch")

sync_util = DataToEs()

# 需要转移的数据id，最大、最小值
transerDataMaxAndMinId = "SELECT max(ca.id) AS max,min(ca.id) AS min " \
                         "FROM case_info AS ca " \
                         "INNER JOIN product p ON p.id=ca.product_id " \
                         "WHERE ca.id >= %d and ca.id <= %d"

# 查询转移sql
transerDataSqlMeta = "SELECT ca.id,ca.id_card,ca.create_time,ca.update_time,ca.create_by,ca.update_by," \
                         "ca.out_serial_no,ca.serial_no,ca.NAME,ca.oper_status,ca.overdue_date,ca.overdue_days," \
                         "ca.entrust_start_time,ca.entrust_end_time,ca.amount,ca.DESC,ca.case_status,ca.field_json," \
                         "ca.call_status,ca.allot_status,ca.repair_status,ca.recovery,ca.operation_state," \
                         "ca.last_follow_time,ca.follow_count,ca.sync_status,ca.operation_next_time," \
                         "ca.out_serial_temp,ca.pay_amount,ca.return_time,ca.own_mobile,ca.division_time,ca.color," \
                         "ca.ignore_plan,ca.cooperation_status,ca.ptp_time,ca.ptp_amount,ca.delay_time,ca.product_id," \
                         "ca.org_delt_id,ca.user_id,ca.dep_id,ca.team_id,ca.store_id,ca.org_id,ca.out_batch_id," \
                         "ca.inner_batch_id,debt_id,ca.vsearch_key1,ca.vsearch_key2,ca.auto_assist_result," \
                         "ca.auto_assist_date,ca.tag,ca.auto_assist_record,ca.call_type,ca.case_tag_id," \
                         "ca.auto_recovery_date,ca.end_time,ca.ctrl_id,ca.warning,ca.end_type,ca.end_config_id," \
                         "ca.allot_agent,ca.allot_agent_state,ca.way_allot_state,ca.important," \
                         "ca.recycle_flag,ca.outsource_count,ca.way_update_date,ca.pre_dep_id,ca.stop_date,ca.recycle_date," \
                         "ca.is_visit,ca.visitor_id," \
                         "ifnull(deb.last_follow_time,ca.last_follow_time) AS debt_last_follow_time," \
                         "ifnull(deb.follow_count,ca.follow_count) AS debt_follow_count," \
                         "deb.type AS conjoint_type,p.type AS product_type,ur.NAME AS user_name," \
                         "ur.STATUS AS user_status," \
                         "ifnull(dept.is_agent,team.is_agent) AS is_agent " \
                         "FROM case_info AS ca " \
                         "INNER JOIN product p ON p.id=ca.product_id " \
                         "LEFT JOIN `user` AS ur ON ur.id=ca.user_id AND ur.STATUS=0 " \
                         "LEFT JOIN case_debtor deb ON deb.id=ca.debt_id AND deb.STATUS=0 " \
                         "LEFT JOIN org_dep_team dept ON dept.id=ca.dep_id " \
                         "LEFT JOIN org_dep_team team ON team.id=ca.team_id " \
                         "WHERE ca.id >= %d AND ca.id <= %d"

selectTagSqlMeta = "SELECT ctr.id,ctr.tag_id,ctr.case_id,ct.NAME,ct.color,ct.state " \
                   "FROM case_tag_rel ctr " \
                   "INNER JOIN case_info ca ON ctr.org_id=ca.org_id AND ctr.case_id=ca.id " \
                   "INNER JOIN case_tag ct ON ctr.tag_id=ct.id " \
                   "WHERE ca.id IN %s"

# 催收方式
operationWaySql = "SELECT case_id,operation_way FROM case_operation_way_rel WHERE case_id IN %s "

def main():
    start = int(sys.argv[1])
    end = int(sys.argv[2])
    if start is None or end is None or start > end:
        logger.info("启动参数异常")
        return

    min_max = sync_util.db.select_one(transerDataMaxAndMinId % (start, end))

    min_id = min_max["min"]
    max_id = min_max["max"]
    if min_id is None or max_id is None:
        logger.info("没有数据需要同步")
        return

    total = max_id - min_id + 1
    fetch_size = 10000
    parts = total // fetch_size
    if total % fetch_size != 0:
        parts = parts + 1

    thread_pool = ThreadPoolExecutor(max_workers=5, thread_name_prefix="sync_bpw_case_info_touch")
    futures = []
    for part in range(parts):
        part_start_id = min_id + (fetch_size * part)
        part_end_id = min_id + (fetch_size * (part + 1)) - 1
        if part_end_id > max_id:
            part_end_id = max_id

        future = thread_pool.submit(run, part_start_id, part_end_id)
        futures.append(future)
    for future in futures:
        try:
            future.result(60)
        except Exception as e:
            logger.exception(e)


def run(part_start_id, part_end_id):
    try:
        thread_name = threading.current_thread().name
        transer_datas = sync_util.db.select(transerDataSqlMeta % (part_start_id, part_end_id))

        if transer_datas:
            action_list = []
            case_info_ids = list(map(lambda x: x["id"], filter(lambda x: x["id"] is not None, transer_datas)))
            tag_rels = []
            operation_way_rels = []
            if case_info_ids:
                tag_rels = sync_util.db.select_by_param(selectTagSqlMeta, (case_info_ids,))
                operation_way_rels = sync_util.db.select_by_param(operationWaySql, (case_info_ids,))

            for case_info in transer_datas:
                logger.info(f'{thread_name}同步数据 >>> {case_info}')
                tags = list(filter(lambda x: x["case_id"] == case_info["id"], tag_rels))
                operation_ways = list(filter(lambda x: x["case_id"] == case_info["id"], operation_way_rels))
                action_list.append(build_common_source(case_info, tags, operation_ways))

            if len(action_list) > 0:
                sync_util.es_bulk(action_list)

        # log.info("<<<<<<<<<<<<<<<<<<<<" + thread_name + " == sync_case_info_touch>>>>>>>>>>>>>>>>>>>>")
    except Exception as e:
        logger.exception(e)


def build_common_source(case_info, tags, operation_ways):
    case_info_id = case_info["id"]
    source = {}

    if case_info_id is not None:
        source["id"] = case_info_id

    id_card = case_info["id_card"]
    if id_card is not None:
        source["idCard"] = id_card

    out_serial_no = case_info["out_serial_no"]
    if out_serial_no is not None:
        source["outSerialNo"] = out_serial_no
        if "#" in out_serial_no:
            index = out_serial_no.index("#")
            source["outSerialNoSearch"] = out_serial_no[0:index]
        else:
            source["outSerialNoSearch"] = out_serial_no

    create_time = case_info["create_time"]
    if create_time is not None:
        source["createTime"] = create_time

    update_time = case_info["update_time"]
    if update_time is not None:
        source["updateTime"] = update_time

    create_by = case_info["create_by"]
    if create_by is not None:
        source["createBy"] = int(create_by)

    update_by = case_info["update_by"]
    if update_by is not None:
        source["updateBy"] = int(update_by)

    serial_no = case_info["serial_no"]
    if serial_no is not None:
        source["serialNo"] = serial_no

    name = case_info["NAME"]
    if name is not None:
        source["name"] = name

    oper_status = case_info["oper_status"]
    if oper_status is not None:
        source["operStatus"] = oper_status

    overdue_date = case_info["overdue_date"]
    if overdue_date is not None:
        source["overdueDate"] = overdue_date

    overdue_days = case_info["overdue_days"]
    if overdue_days is not None:
        source["overdueDays"] = overdue_days

    entrust_start_time = case_info["entrust_start_time"]
    if entrust_start_time is not None:
        source["entrustStartTime"] = entrust_start_time

    entrust_end_time = case_info["entrust_end_time"]
    if entrust_end_time is not None:
        source["entrustEndTime"] = entrust_end_time

    amount = case_info["amount"]
    if amount is not None:
        source["amount"] = int(amount)

    desc = case_info["DESC"]
    if desc is not None:
        source["desc"] = desc

    case_status = case_info["case_status"]
    if case_status is not None:
        source["caseStatus"] = case_status

    field_json_map = {}
    field_search = []
    field_json = case_info["field_json"]
    if field_json is not None:
        field_json_tem = json.loads(field_json)
        search_key1 = field_json_tem.get("search_key1")
        if search_key1 is not None:
            field_json_map["search_key1"] = search_key1
        search_key2 = field_json_tem.get("search_key2")
        if search_key2 is not None:
            field_json_map["search_key2"] = search_key2
        for key in field_json_tem:
            if key is not None and key != "":
                value = field_json_tem[key]
                if value is not None and value != "":
                    field_search.append(key+"#"+value)

    source["fieldJson"] = field_json_map
    source["fieldSearch"] = field_search

    call_status = case_info["call_status"]
    if call_status is not None:
        source["callStatus"] = call_status

    allot_status = case_info["allot_status"]
    if allot_status is not None:
        source["allotStatus"] = allot_status

    repair_status = case_info["repair_status"]
    if repair_status is not None:
        source["repairStatus"] = repair_status

    recovery = case_info["recovery"]
    if recovery is not None:
        source["recovery"] = recovery

    operation_state = case_info["operation_state"]
    if operation_state is not None:
        source["operationState"] = operation_state

    last_follow_time = case_info["last_follow_time"]
    if last_follow_time is not None:
        source["lastFollowTime"] = last_follow_time

    follow_count = case_info["follow_count"]
    if follow_count is not None:
        source["followCount"] = follow_count

    sync_status = case_info["sync_status"]
    if sync_status is not None:
        source["syncStatus"] = sync_status

    operation_next_time = case_info["operation_next_time"]
    if operation_next_time is not None:
        source["operationNextTime"] = operation_next_time

    out_serial_temp = case_info["out_serial_temp"]
    if out_serial_temp is not None:
        source["outSerialTemp"] = out_serial_temp

    pay_amount = case_info["pay_amount"]
    if pay_amount is not None:
        source["payAmount"] = int(pay_amount)

    return_time = case_info["return_time"]
    if return_time is not None:
        source["returnTime"] = return_time

    own_mobile = case_info["own_mobile"]
    if own_mobile is not None:
        source["ownMobile"] = own_mobile

    division_time = case_info["division_time"]
    if division_time is not None:
        source["divisionTime"] = division_time

    color = case_info["color"]
    if color is not None:
        source["color"] = color

    ignore_plan = case_info["ignore_plan"]
    if ignore_plan is not None:
        source["ignorePlan"] = ignore_plan

    cooperation_status = case_info["cooperation_status"]
    if cooperation_status is not None:
        source["cooperationStatus"] = cooperation_status

    ptp_time = case_info["ptp_time"]
    if ptp_time is not None:
        source["ptpTime"] = ptp_time

    ptp_amount = case_info["ptp_amount"]
    if ptp_amount is not None:
        source["ptpAmount"] = int(ptp_amount)

    delay_time = case_info["delay_time"]
    if delay_time is not None:
        source["delayTime"] = delay_time

    product_id = case_info["product_id"]
    if product_id is not None:
        source["productId"] = product_id

    org_delt_id = case_info["org_delt_id"]
    if org_delt_id is not None:
        source["orgDeltId"] = org_delt_id

    user_id = case_info["user_id"]
    if user_id is not None:
        source["userId"] = user_id

    dep_id = case_info["dep_id"]
    if dep_id is not None:
        source["depId"] = dep_id

    team_id = case_info["team_id"]
    if team_id is not None:
        source["teamId"] = team_id

    store_id = case_info["store_id"]
    if store_id is not None:
        source["storeId"] = store_id

    org_id = case_info["org_id"]
    if org_id is not None:
        source["orgId"] = org_id

    out_batch_id = case_info["out_batch_id"]
    if out_batch_id is not None:
        source["outBatchId"] = out_batch_id

    inner_batch_id = case_info["inner_batch_id"]
    if inner_batch_id is not None:
        source["innerBatchId"] = inner_batch_id

    debt_id = case_info["debt_id"]
    if debt_id is not None:
        source["debtId"] = debt_id

    vsearch_key1 = case_info["vsearch_key1"]
    if vsearch_key1 is not None:
        source["vsearchKey1"] = vsearch_key1

    vsearch_key2 = case_info["vsearch_key2"]
    if vsearch_key2 is not None:
        source["vsearchKey2"] = vsearch_key2

    auto_assist_result = case_info["auto_assist_result"]
    if auto_assist_result is not None:
        source["autoAssistResult"] = auto_assist_result

    auto_assist_date = case_info["auto_assist_date"]
    if auto_assist_date is not None:
        source["autoAssistDate"] = auto_assist_date

    tag = case_info["tag"]
    if tag is not None:
        source["tag"] = tag

    auto_assist_record = case_info["auto_assist_record"]
    if auto_assist_record is not None:
        source["autoAssistRecord"] = auto_assist_record

    call_type = case_info["call_type"]
    if call_type is not None:
        source["callType"] = call_type

    if tags:
        tag_ids = list(map(lambda x: x["tag_id"], filter(lambda x: x["tag_id"] is not None, tags)))
        if tag_ids:
            source["caseTagId"] = tag_ids

    auto_recovery_date = case_info["auto_recovery_date"]
    if auto_recovery_date is not None:
        source["autoRecoveryDate"] = auto_recovery_date

    end_time = case_info["end_time"]
    if end_time is not None:
        source["endTime"] = end_time

    ctrl_id = case_info["ctrl_id"]
    if ctrl_id is not None:
        source["ctrlId"] = ctrl_id

    warning = case_info["warning"]
    if warning is not None:
        source["warning"] = warning

    end_type = case_info["end_type"]
    if end_type is not None:
        source["endType"] = end_type

    end_config_id = case_info["end_config_id"]
    if end_config_id is not None:
        source["endConfigId"] = end_config_id

    debt_last_follow_time = case_info["debt_last_follow_time"]
    if debt_last_follow_time is not None:
        source["debtLastFollowTime"] = debt_last_follow_time

    debt_follow_count = case_info["debt_follow_count"]
    if debt_follow_count is not None:
        source["debtFollowCount"] = debt_follow_count

    conjoint_type = case_info["conjoint_type"]
    if conjoint_type is not None:
        source["conjointType"] = conjoint_type

    product_type = case_info["product_type"]
    if product_type is not None:
        source["productType"] = product_type

    user_name = case_info["user_name"]
    if user_name is not None:
        source["userName"] = user_name

    user_status = case_info["user_status"]
    if user_status is not None:
        source["userStatus"] = user_status

    is_agent = case_info["is_agent"]
    if is_agent is not None:
        source["isAgent"] = is_agent

    allot_agent = case_info["allot_agent"]
    if allot_agent is not None:
        source["allotAgent"] = allot_agent

    allot_agent_state = case_info["allot_agent_state"]
    if allot_agent_state is not None:
        source["allotAgentState"] = allot_agent_state

    way_allot_state = case_info["way_allot_state"]
    if way_allot_state is not None:
        source["wayAllotState"] = way_allot_state

    operation_way_values = list(map(lambda x: x["operation_way"], filter(lambda x: x["operation_way"] is not None, operation_ways)))
    source["operationWay"] = operation_way_values

    important = case_info["important"]
    if important is not None:
        source["important"] = important

    recycle_flag = case_info["recycle_flag"]
    if recycle_flag is not None:
        source["recycleFlag"] = recycle_flag

    outsource_count = case_info["outsource_count"]
    if outsource_count is not None:
        source["outsourceCount"] = outsource_count

    way_update_date = case_info["way_update_date"]
    if way_update_date is not None:
        source["wayUpdateDate"] = way_update_date

    pre_dep_id = case_info["pre_dep_id"]
    if pre_dep_id is not None:
        source["preDepId"] = pre_dep_id

    stop_date = case_info["stop_date"]
    if stop_date is not None:
        source["stopDate"] = stop_date

    recycle_date = case_info["recycle_date"]
    if recycle_date is not None:
        source["recycleDate"] = recycle_date
    is_visit = case_info["is_visit"]
    if is_visit is not None:
        source["isVisit"] = is_visit
    visitor_id = case_info["visitor_id"]
    if visitor_id is not None:
        source["visitorId"] = visitor_id

    source_json = json.dumps(source, cls=DateEncoder)

    return {
        "_index": es_index,
        "_type": es_type,
        "_id": case_info_id,
        "_source": source_json
    }


if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        logger.exception(e)
