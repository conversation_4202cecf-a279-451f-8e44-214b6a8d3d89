package com.jinghang.cash.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.pojo.UserRiskRecord;
import com.jinghang.cash.service.UserRiskRecordService;
import com.jinghang.cash.mapper.UserRiskRecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【user_risk_record(风控)】的数据库操作Service实现
* @createDate 2023-11-16 20:36:47
*/
@Service
public class UserRiskRecordServiceImpl extends ServiceImpl<UserRiskRecordMapper, UserRiskRecord>
    implements UserRiskRecordService{

}




