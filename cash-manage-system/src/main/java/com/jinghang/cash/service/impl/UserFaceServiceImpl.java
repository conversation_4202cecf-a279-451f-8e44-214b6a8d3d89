package com.jinghang.cash.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.pojo.UserFace;
import com.jinghang.cash.service.UserFaceService;
import com.jinghang.cash.mapper.UserFaceMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【user_face(人脸信息)】的数据库操作Service实现
* @createDate 2023-11-20 16:54:50
*/
@Service
public class UserFaceServiceImpl extends ServiceImpl<UserFaceMapper, UserFace>
    implements UserFaceService{

}




