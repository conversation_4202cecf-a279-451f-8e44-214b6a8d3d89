package com.jinghang.cash.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.pojo.MessageTemplate;
import com.jinghang.cash.service.MessageTemplateService;
import com.jinghang.cash.mapper.MessageTemplateMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【message_template(短信模板表)】的数据库操作Service实现
* @createDate 2023-11-17 14:22:27
*/
@Service
public class MessageTemplateServiceImpl extends ServiceImpl<MessageTemplateMapper, MessageTemplate>
    implements MessageTemplateService{

}




