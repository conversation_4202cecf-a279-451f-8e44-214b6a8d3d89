package com.jinghang.cash.service;

import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.jinghang.cash.pojo.RightsOrder;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【rights_order(权益订单)】的数据库操作Service
* @createDate 2023-11-16 11:45:25
*/
public interface RightsOrderService extends IService<RightsOrder> {

    @Override
    default QueryChainWrapper<RightsOrder> query() {
        return IService.super.query();
    }

    RightsOrder getSuccessOrderById(String orderId, String state);
}
