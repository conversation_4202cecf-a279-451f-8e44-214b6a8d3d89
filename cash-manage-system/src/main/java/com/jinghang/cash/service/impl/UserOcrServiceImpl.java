package com.jinghang.cash.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.pojo.UserOcr;
import com.jinghang.cash.service.UserOcrService;
import com.jinghang.cash.mapper.UserOcrMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【user_ocr(身份证信息)】的数据库操作Service实现
* @createDate 2024-01-05 14:56:43
*/
@Service
public class UserOcrServiceImpl extends ServiceImpl<UserOcrMapper, UserOcr>
    implements UserOcrService{

}




