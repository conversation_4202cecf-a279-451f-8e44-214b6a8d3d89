package com.jinghang.cash.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jinghang.cash.pojo.MessageSendRecord;
import com.jinghang.cash.service.MessageSendRecordService;
import com.jinghang.cash.mapper.MessageSendRecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【message_send_record(短信发送记录表)】的数据库操作Service实现
* @createDate 2023-11-17 14:02:06
*/
@Service
public class MessageSendRecordServiceImpl extends ServiceImpl<MessageSendRecordMapper, MessageSendRecord>
    implements MessageSendRecordService{

}




