package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.cash.enums.FlowCapitalEnable;
import com.jinghang.ppd.api.enums.ValidStatus;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 流量路由
 * <AUTHOR>
 * @TableName flow_route_config
 */
@Data
@TableName(value ="flow_route_config")
public class FlowRouteConfig implements Serializable {
    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 流量id
     */
    private String flowId;

    /**
     * 资金ID
     */
    private String capitalId;

    /**
     * 资金优先级
     */
    private Integer priority;

    /**
     * 启用状态
     */
    @Enumerated(EnumType.STRING)
    private FlowCapitalEnable enabled;

    /**
     * 路由是否有效
     * Y - 有效
     * N - 删除
     */
    @Enumerated(EnumType.STRING)
    private ValidStatus valid;

    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private String revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}
