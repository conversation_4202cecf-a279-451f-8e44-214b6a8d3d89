package com.jinghang.cash.pojo.data;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description 拍拍贷 还款计划表
 */
@Data
@TableName(value ="s01_ppdxf_zcppd_repay_plan")
public class ZcppdRepayPlan implements Serializable {
    /**
     * 还款计划id
     */
    private String id;

    /**
     * 借据
     */
    private String loanId;

    /**
     * 期次
     */
    private Integer period;

    /**
     * 计划还款日
     */
    private Date planRepayDate;

    /**
     * 应还本金
     */
    private BigDecimal principalAmt;

    /**
     * 应还利息
     */
    private BigDecimal interestAmt;

    /**
     * 应还担保费
     */
    private BigDecimal guaranteeAmt;

    /**
     * 应还咨询费
     */
    private BigDecimal consultAmt;

    /**
     * 应还罚息
     */
    private BigDecimal penaltyAmt;

    /**
     * 应还总金额
     */
    private BigDecimal amount;

    /**
     * 还款计划状态
     */
    private String planState;

    /**
     * 实还时间
     */
    private Date actRepayTime;

    /**
     * 实还本金
     */
    private BigDecimal actPrincipalAmt;

    /**
     * 实还利息
     */
    private BigDecimal actInterestAmt;

    /**
     * 实还担保费
     */
    private BigDecimal actGuaranteeAmt;

    /**
     * 实还咨询费
     */
    private BigDecimal actConsultAmt;

    /**
     * 实还罚息
     */
    private BigDecimal actPenaltyAmt;

    /**
     * 实还总金额
     */
    private BigDecimal actAmount;

    /**
     * 减免本金
     */
    private BigDecimal reducePrincipalAmt;

    /**
     * 减免利息
     */
    private BigDecimal reduceInterestAmt;

    /**
     * 减免担保费
     */
    private BigDecimal reduceGuaranteeAmt;

    /**
     * 减免咨询费
     */
    private BigDecimal reduceConsultAmt;

    /**
     * 减免罚息
     */
    private BigDecimal reducePenaltyAmt;

    /**
     * 减免总金额
     */
    private BigDecimal reduceAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedTime;
}
