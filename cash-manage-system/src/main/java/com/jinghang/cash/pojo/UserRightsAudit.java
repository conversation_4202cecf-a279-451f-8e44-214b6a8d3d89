package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户小卡退订审核表
 */
@Data
@TableName("user_rights_audit")
public class UserRightsAudit implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = -4858084593570639487L;

    @TableId
    private String id;

    /**
     * 用户id
     */
    private String subscribeId;

    /**
     * 备注
     */
    private String reason;

    /**
     * 订阅时间
     */
    private LocalDateTime applyTime;

    private String applyBy;

    /**
     * 退订时间
     */
    private LocalDateTime auditTime;

    private String auditBy;

    /**
     * 订阅状态 待审核/审核拒绝/审核通过
     */
    private String auditState;

    /**
     * 备注
     */
    private String remark;
    /**
     * 乐观锁
     */
    private Integer revision;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

}
