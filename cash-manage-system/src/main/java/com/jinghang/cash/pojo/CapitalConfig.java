package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.cash.enums.ProtocolChannel;
import com.jinghang.cash.enums.WhetherState;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 资方配置
 *
 * <AUTHOR>
 * @TableName capital_config
 */
@TableName(value = "capital_config")
@Data
public class CapitalConfig implements Serializable {

    private static final long serialVersionUID = 5417296156525881669L;
    @TableId
    private String id;
    /**
     * 资金方
     */
    @TableField(value = "bank_channel")
    private String bankChannel;

    @TableField(value = "guarantee_company")
    private String guaranteeCompany;

    /**
     * 资方授信日限额
     */
    @TableField(value = "credit_day_limit")
    private BigDecimal creditDayLimit;

    /**
     * 资方放款日限额
     */
    @TableField(value = "loan_day_limit")
    private BigDecimal loanDayLimit;

    /**
     * 支持期数（逗号分割）
     */
    @TableField(value = "periods_range")
    private String periodsRange;

    /**
     * 年龄区间
     */
    @TableField(value = "ages_range")
    private String agesRange;

    /**
     * 单笔上下限
     */
    @TableField(value = "single_amt_range")
    private String singleAmtRange;
    /**
     * 是否开启授信时间范围
     */
    @TableField(value = "credit_time_status")
    private String creditTimeStatus;
    /**
     * 是否开启放款时间范围限制
     */
    @TableField(value = "loan_time_status")
    private String loanTimeStatus;
    /**
     * 是否开启还款时间范围限制
     */
    @TableField(value = "repay_time_status")
    private String repayTimeStatus;

    /**
     * 授信开始HH:mm:ss
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "credit_start_time")
    private String creditStartTime;

    /**
     * 授信截止HH:mm:ss
     */
    @TableField(value = "credit_end_time")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String creditEndTime;

    /**
     * 放款开始HH:mm:ss
     */
    @TableField(value = "loan_start_time")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String loanStartTime;

    /**
     * 放款截止HH:mm:ss
     */
    @TableField(value = "loan_end_time")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String loanEndTime;

    /**
     * 启用状态
     */
    @TableField(value = "enabled")
    private String enabled;

    /**
     * 资方利率
     */
    @TableField(value = "bank_rate")
    private BigDecimal bankRate;

    /**
     * 资方支持利率
     */
    @TableField(value = "support_irr_level")
    private String supportIrrLevel;

    /**
     * 还款开始时间
     */
    @TableField(value = "repay_start_time")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String repayStartTime;

    /**
     * 还款结束时间
     */
    @TableField(value = "repay_end_time")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String repayEndTime;

    /**
     * 是否可续借
     */
    @TableField(value = "renewed_flag")
    private WhetherState renewedFlag;

    /**
     * 绑卡渠道
     */
    @TableField(value = "protocol_channel")
    private String protocolChannel;

    /**
     * 备注
     */
    @TableField(value = "remark", fill = FieldFill.INSERT)
    private String remark;
    /**
     * 版本号
     */
    @TableField(value = "revision", fill = FieldFill.INSERT)
    @Version
    private String revision;
    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;


}
