package com.jinghang.cash.pojo.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.cash.enums.Relation;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;
/**
 *联系人信息
 */
@Data
@TableName(value ="s03_qh_user_contact_info")
public class QhUserContactInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 联系人
     */
    @TableField(value = "name")
    private String name;

    /**
     * 联系人手机号
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 关系
     */
    @TableField(value = "relation")
    @Enumerated(EnumType.STRING)
    private Relation relation;

}
