package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 权益还款记录
 *
 * <AUTHOR>
 * @TableName rights_repay_record
 */
@TableName(value = "rights_repay_record")
@Data
public class RightsRepayRecord {
    /**
     * 主键
     */
    private String id;

    /**
     * 外部还款流水号
     */
    @TableField(value = "outer_repay_no")
    private String outerRepayNo;

    /**
     * 借据
     */
    @TableField(value = "loan_id")
    private String loanId;

    /**
     * 权益包ID
     */
    @TableField(value = "rights_package_id")
    private String rightsPackageId;

    /**
     * 还款日期
     */
    @TableField(value = "repay_apply_date")
    private LocalDateTime repayApplyDate;

    /**
     * 还款模式,线上线下
     */
    @TableField(value = "repay_mode")
    private String repayMode;

    /**
     * 还款总额
     */
    @TableField(value = "total_amt")
    private BigDecimal totalAmt;

    /**
     * 代扣协议号
     */
    @TableField(value = "agreement_no")
    private String agreementNo;

    /**
     * 还款状态
     */
    @TableField(value = "repay_state")
    private String repayState;

    /**
     * 失败原因
     */
    @TableField(value = "fail_reason")
    private String failReason;

    /**
     * 还款成功时间
     */
    @TableField(value = "repaid_date")
    private LocalDateTime repaidDate;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 乐观锁
     */
    @TableField(value = "revision")
    private String revision;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * 权益扣费计划id
     */
    @TableField(value = "rights_repay_plan_id")
    private String rightsRepayPlanId;


}
