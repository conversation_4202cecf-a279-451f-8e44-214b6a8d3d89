package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 额度策略分箱表
 */
@Data
@TableName("revolving_strategy_relate")
public class RevolvingStrategyRelate implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 2665298682799858452L;

    @TableId
    private String id;

    /**
     * 策略id
     */
    private String strategyId;

    private String relateCode;

    /**
     * 前端可用额度
     */
    private BigDecimal frontAmount;

    /**
     * 分流比例
     */
    private BigDecimal ratio;

    /**
     * 备注
     */
    private String remark;
    /**
     * 乐观锁
     */
    private Integer revision;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
