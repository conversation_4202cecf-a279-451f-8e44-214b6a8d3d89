package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

import com.jinghang.cash.mapper.BaseEntity;
import lombok.Data;

/**
 * @TableName order_remark
 */
@TableName(value ="order_remark")
@Data
public class OrderRemark extends BaseEntity {

    private String orderNo;

    private String certNo;

    private String name;

    private String mobile;

    private String flowChannel;

    private String bankChannel;

    private BigDecimal rate;

    private String operateAccount;

    private String remarkType;

    private String remarkDefine;

    private String remark;

}
