package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.pojo.AggregatePaymentConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【aggregate_payment_config】的数据库操作Mapper
 * @createDate 2024-08-27 16:56:52
 * @Entity com.jinghang.cash.pojo.AggregatePaymentConfig
 */
@Mapper
@DS("slave")
public interface AggregatePaymentConfigMapper extends BaseMapper<AggregatePaymentConfig> {

    /**
     * 查询全部的聚合支付配置
     * @return
     */
    List<AggregatePaymentConfig> queryAllInfo();
}
