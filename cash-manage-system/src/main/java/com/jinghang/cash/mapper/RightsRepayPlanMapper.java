package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.modules.manage.vo.req.RightsRepayPlanReq;
import com.jinghang.cash.modules.manage.vo.res.RightsRepayPlanResVO;
import com.jinghang.cash.pojo.RightsRepayPlan;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【rights_repay_plan(权益扣费计划)】的数据库操作Mapper
* @createDate 2024-03-18 18:51:37
* @Entity me.zhengjie.entity.RightsRepayPlan
*/
@DS("slave")
public interface RightsRepayPlanMapper extends BaseMapper<RightsRepayPlan> {

    List<RightsRepayPlanResVO> queryRightsRepayPlan(RightsRepayPlanReq req);
}




