package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.pagehelper.Page;
import com.jinghang.cash.modules.manage.vo.OrderInfoVo;
import com.jinghang.cash.modules.manage.vo.req.OrderInfoReq;
import com.jinghang.cash.modules.manage.vo.req.OrderListReq;
import com.jinghang.cash.modules.manage.vo.rsp.OrderListRsp;
import com.jinghang.cash.pojo.Order;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order(订单)】的数据库操作Mapper
* @createDate 2023-11-15 15:48:19
* @Entity com.jinghang.cash.pojo.Order
*/
@Mapper
@DS("slave")
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 订单信息查询
     * @param orderInfoReq
     * @return
     */
    Page<OrderInfoVo> queryOrderInfo(OrderInfoReq orderInfoReq);

    /**
     * 订单列表查询
     *
     * @param orderListReq
     * @return
     */
    Page<OrderListRsp> queryOrderList(OrderListReq orderListReq);

    /**
     * 查询未订购权益订单
     *
     * @param orderListReq
     * @return
     */
    Page<OrderListRsp> queryOrderByNotStatusList(OrderListReq orderListReq);

    /**
     * 查询已订购权益订单
     *
     * @param orderListReq
     * @return
     */
    Page<OrderListRsp> queryOrderByYesStatusList(OrderListReq orderListReq);

    List<String> getIdByMobileOrCertNo(String mobile, String certNo);
}




