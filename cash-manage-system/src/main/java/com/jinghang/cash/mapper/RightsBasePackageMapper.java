package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jinghang.cash.pojo.RightsBasePackage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【rights_base_package(权益包)】的数据库操作Mapper
* @createDate 2023-11-25 09:11:07
* @Entity com.jinghang.cash.pojo.RightsBasePackage
*/
@Mapper
@DS("slave")
public interface RightsBasePackageMapper extends BaseMapper<RightsBasePackage> {

    RightsBasePackage findByOrderId(String orderId);
}




