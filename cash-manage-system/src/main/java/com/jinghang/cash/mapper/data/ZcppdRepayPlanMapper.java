package com.jinghang.cash.mapper.data;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jinghang.cash.base.BaseMapper;
import com.jinghang.cash.modules.manage.Job.dto.OverdueRepayPlanDTO;
import com.jinghang.cash.pojo.data.ZcppdRepayPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
@DS("data")
public interface ZcppdRepayPlanMapper extends BaseMapper<ZcppdRepayPlan, String> {

    List<OverdueRepayPlanDTO> statisticOverdueRepay(@Param("date") LocalDate date);

}
