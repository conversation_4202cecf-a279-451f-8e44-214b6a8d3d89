package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import com.jinghang.cash.modules.manage.vo.req.QueryAggregatePaymentReq;
import com.jinghang.cash.modules.manage.vo.rsp.AggregatePaymentRecordResp;
import com.jinghang.cash.pojo.AggregatePaymentRecord;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【aggregate_payment_record】的数据库操作Mapper
 * @createDate 2024-05-20 11:29:52
 * @Entity com.jinghang.cash.pojo.AggregatePaymentRecord
 */
@Mapper
@DS("slave")
public interface AggregatePaymentRecordMapper extends BaseMapper<AggregatePaymentRecord> {

    /**
     * 根据条件分页查询聚合支付记录
     * @param param
     * @return
     */
    Page<AggregatePaymentRecordResp> queryList(QueryAggregatePaymentReq param);

    /**
     * 根据条件分页查询聚合支付记录
     * @param param
     * @return
     */
    Page<AggregatePaymentRecordResp> queryListByPayerAcctCode(QueryAggregatePaymentReq param);
}
