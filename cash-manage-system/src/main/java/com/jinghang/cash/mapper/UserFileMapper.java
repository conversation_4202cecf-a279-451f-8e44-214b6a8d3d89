package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jinghang.cash.pojo.UserFile;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_file(影像协议文件)】的数据库操作Mapper
* @createDate 2023-11-16 10:19:53
* @Entity com.jinghang.cash.pojo.UserFile
*/
@Mapper
public interface UserFileMapper extends BaseMapper<UserFile> {

    /**
     * 查询用户附件信息
     * @return
     */
    @DS("slave")
    UserFile queryUserFileInfo(@Param("userId") String userId);

    /**
     * 查询用户签章协议
     * @return
     */
    @DS("slave")
    List<UserFile> queryUserAgreementFile(@Param("userFileIds") List<String> userFileIds);

}




