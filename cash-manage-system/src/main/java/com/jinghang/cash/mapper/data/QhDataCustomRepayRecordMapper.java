package com.jinghang.cash.mapper.data;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jinghang.cash.modules.manage.Job.bo.QhCustomRepayRecordBo;
import com.jinghang.cash.pojo.data.QhCustomRepayRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


@Mapper
@DS("data")
public interface QhDataCustomRepayRecordMapper extends BaseMapper<QhCustomRepayRecord> {

    List<QhCustomRepayRecordBo> selectByLoanIds(@Param("loanIds") Set<String> loanIds);

}




