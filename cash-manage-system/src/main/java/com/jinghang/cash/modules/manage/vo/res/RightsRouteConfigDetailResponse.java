package com.jinghang.cash.modules.manage.vo.res;

import com.jinghang.cash.enums.RightsSupplier;
import com.jinghang.cash.pojo.RightsRouteConfig;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
public class RightsRouteConfigDetailResponse {

    private RightsSupplier rightsSupplier;


    private List<RightsRouteConfigVO> paymentChannelList;

    public static class RightsRouteConfigVO extends RightsRouteConfig {

        private String paymentChannelName;

        public String getPaymentChannelName() {
            return paymentChannelName;
        }

        public void setPaymentChannelName(String paymentChannelName) {
            this.paymentChannelName = paymentChannelName;
        }
    }

}
