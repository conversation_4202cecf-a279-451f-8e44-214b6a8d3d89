package com.jinghang.cash.modules.manage.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.cash.enums.ProtocolChannel;
import com.jinghang.cash.enums.WhetherState;

import com.jinghang.ppd.api.enums.AbleStatus;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class CapitalConfigRequest implements Serializable {
    private static final long serialVersionUID = 4088875195548548847L;

    private String id;

    @NotBlank(message = "状态不能为空")
    private String enabled;

    @NotBlank(message = "资金方不能为空")
    private String bankChannel;
    /**
     * 支持期数（逗号分割）
     */
    @NotBlank(message = "支持期数不能为空")
    private String periodsRange;
    /**
     * 资方利率
     */
    @NotNull(message = "资方利率不能为空")
    private BigDecimal bankRate;

    /**
     * 资方支持利率
     */
    @NotBlank(message = "资方支持利率不能为空")
    private String supportIrrLevel;

    /**
     * 资方授信日限额
     */
    @NotNull(message = "资方授信日限额不能为空")
    private BigDecimal creditDayLimit;

    /**
     * 资方放款日限额
     */
    @NotNull(message = "资方放款日限额不能为空")
    private BigDecimal loanDayLimit;
    /**
     * 年龄区间
     */
    @NotBlank(message = "年龄区间不能为空")
    private String agesRange;

    /**
     * 单笔上下限
     */
    @NotBlank(message = "单笔限额不能为空")
    private String singleAmtRange;
    /**
     * 是否开启授信时间范围
     */
    private AbleStatus creditTimeStatus;
    /**
     * 是否开启放款时间范围限制
     */
    private AbleStatus loanTimeStatus;
    /**
     * 是否开启还款时间范围限制
     */
    private AbleStatus repayTimeStatus;

    /**
     * 授信开始时间
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String creditStartTime;

    /**
     * 授信截止时间
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String creditEndTime;

    /**
     * 放款开始时间
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String loanStartTime;

    /**
     * 放款截止时间
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String loanEndTime;

    /**
     * 还款开始时间
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String repayStartTime;

    /**
     * 还款结束时间
     */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    private String repayEndTime;

    /**
     * 融担公司
     */
    private GuaranteeCompany guaranteeCompany;

    /**
     * 是否可续借
     */
    private WhetherState renewedFlag;

    /**
     * 绑卡渠道
     */
    @NotNull(message = "绑卡渠道不能为空")
    private ProtocolChannel protocolChannel;

    /**
     * 备注
     */
    private String remark;
    /**
     * 版本号
     */
    private String revision;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;
}
