package com.jinghang.cash.modules.manage.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.enums.ResultCode;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.manage.service.OrderInfoService;
import com.jinghang.cash.modules.manage.vo.CallRecordVo;
import com.jinghang.cash.modules.manage.vo.CallUserVo;
import com.jinghang.cash.modules.manage.vo.OrderInfoVo;
import com.jinghang.cash.modules.manage.vo.req.AgreementFileReq;
import com.jinghang.cash.modules.manage.vo.req.CallRecordSaveReq;
import com.jinghang.cash.modules.manage.vo.req.FileDownloadReq;
import com.jinghang.cash.modules.manage.vo.req.OrderInfoReq;
import com.jinghang.cash.modules.manage.vo.req.OrderListReq;
import com.jinghang.cash.modules.manage.vo.req.UserOrderReq;
import com.jinghang.cash.modules.manage.vo.req.ppd.CallRecordQueryReq;
import com.jinghang.cash.modules.manage.vo.rsp.AgreementFileRsp;
import com.jinghang.cash.modules.manage.vo.rsp.OrderListRsp;
import com.jinghang.cash.modules.manage.vo.rsp.RepayPlanVo;
import com.jinghang.cash.modules.manage.vo.rsp.StageRsp;
import com.jinghang.cash.modules.manage.vo.rsp.UserFaceRsp;
import com.jinghang.cash.modules.manage.vo.rsp.UserInfoRsp;

import com.jinghang.common.util.JsonUtil;
import com.jinghang.ppd.api.dto.ResetPasswordReq;
import com.jinghang.ppd.api.dto.file.FileDownloadReqDTO;
import com.jinghang.ppd.api.dto.file.FileDownloadRespDTO;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/order")
@Api(tags = "订单接口")
public class OrderInfoController {

    private static final Logger log = LoggerFactory.getLogger(OrderInfoController.class);

    @Autowired
    private OrderInfoService orderInfoService;

    /**
     * 订单详情查询
     */
    @PostMapping(value = "/query")
    public RestResult<Object> queryOrderInfo(@RequestBody OrderInfoReq orderInfoReq) {
        if (null == orderInfoReq.getOrderId() && null == orderInfoReq.getMobile()
            && null == orderInfoReq.getCertNo() && null == orderInfoReq.getOuterOrderId()) {
            log.warn("订单详情查询查询参数为空!");
            return RestResult.fail(ResultCode.PARAM_ILLEGAL, ResultCode.PARAM_ILLEGAL.getMsg());
        }
        PageInfo<OrderInfoVo> orderInfoVoPageInfo = orderInfoService.queryOrderInfo(orderInfoReq);
        return RestResult.success(orderInfoVoPageInfo);
    }

    /**
     * 用户信息查询
     *
     * @param userOrderReq
     * @return
     */
    @PostMapping(value = "/custom")
    public RestResult<Object> queryUserInfo(@Validated @RequestBody UserOrderReq userOrderReq) {
        UserInfoRsp userInfo = orderInfoService.queryUserInfo(userOrderReq);
        return RestResult.success(userInfo);
    }

    /**
     * 查看人脸
     *
     * @param userOrderReq
     * @return
     */
    @PostMapping(value = "/face")
    public RestResult<Object> queryUserface(@Validated @RequestBody UserOrderReq userOrderReq) {
        UserFaceRsp userFaceRsp = orderInfoService.queryUserFileInfo(userOrderReq.getUserId());
        return RestResult.success(userFaceRsp);
    }

    /**
     * 还款计划
     *
     * @param userOrderReq
     * @return
     */
    @PostMapping(value = "/plan")
    public RestResult<Object> queryRepayPlan(@Validated @RequestBody UserOrderReq userOrderReq) {
        RepayPlanVo repayPlanVo = orderInfoService.queryRepayPlan(userOrderReq);
        return RestResult.success(repayPlanVo);
    }

    /**
     * 查看进度
     *
     * @param userOrderReq
     * @return
     */
    @PostMapping(value = "/stage")
    public RestResult<Object> queryStage(@Validated @RequestBody UserOrderReq userOrderReq) {
        List<StageRsp> stageRspList = orderInfoService.queryStage(userOrderReq);
        return RestResult.success(stageRspList);
    }

    /**
     * 订单列表查询
     */
    @PostMapping(value = "/list")
    public RestResult<Object> queryOrderList(@RequestBody OrderListReq orderListReq) {
        if (null == orderListReq.getOrderId() && null == orderListReq.getMobile() && null == orderListReq.getCertNo()) {
            log.warn("订单列表查询查询参数为空!");
            return RestResult.fail(ResultCode.PARAM_ILLEGAL, ResultCode.PARAM_ILLEGAL.getMsg());
        }
        PageInfo<OrderListRsp> orderListRspPageInfo = orderInfoService.queryOrderList(orderListReq);
        return RestResult.success(orderListRspPageInfo);
    }

    /**
     * 已签约协议文件
     *
     * @param agreementFileReq
     * @return
     */
    @PostMapping(value = "/agreementlist")
    public RestResult<Object> querAgreementFile(@Validated @RequestBody AgreementFileReq agreementFileReq) {
        List<AgreementFileRsp> agreementFileRsps = orderInfoService.querAgreementFile(agreementFileReq);
        return RestResult.success(agreementFileRsps);
    }

    /**
     * 已签约协议文件
     *
     * @param fileDownloadReq
     * @return
     */
    @PostMapping(value = "/downloadVoucherFile")
    public RestResult<FileDownloadRespDTO> downloadVoucherFile(@Validated @RequestBody FileDownloadReqDTO fileDownloadReq) {
        log.info("下载结清证明，request:{}", JSONObject.toJSONString(fileDownloadReq));
        FileDownloadRespDTO response = orderInfoService.downloadVoucherFile(fileDownloadReq);
        return RestResult.success(response);
    }

    /**
     * 重置密码
     *
     * @param
     * @return
     */
    @PostMapping(value = "/resetPassword")
    public RestResult<String> resetPassword(@RequestBody ResetPasswordReq resetPasswordReq) {
        RestResult<String> restResult;
        try {
            log.info("resetPassword mobile:{}", JsonUtil.toJsonString(resetPasswordReq));
            restResult = orderInfoService.resetPassword(resetPasswordReq);
        } catch (Exception e) {
            e.printStackTrace();
            return RestResult.fail(ResultCode.PARAM_ILLEGAL, "重置密码失败");
        }
        return restResult;
    }


    /**
     * 订单取消
     */
    @PostMapping(value = "/cancel")
    public RestResult<?> cancelOrder(@RequestBody OrderInfoReq orderInfoReq) {
        log.info("cancelOrder param:{}", JsonUtil.toJsonString(orderInfoReq));
        if (null == orderInfoReq.getOrderId()) {
            log.warn("订单取消参数为空!");
            return RestResult.fail(ResultCode.PARAM_ILLEGAL, ResultCode.PARAM_ILLEGAL.getMsg());
        }
        orderInfoService.cancelOrder(orderInfoReq);
        return RestResult.success();
    }

    /**
     * 外呼
     *
     * @param
     * @return
     */
    @PostMapping(value = "/handleCall")
    public RestResult<CallUserVo> handleCall() {
        CallUserVo callUserVo;
        try {
            log.info("handleCall start");
            callUserVo = orderInfoService.handleCall();
        } catch (Exception e) {
            log.error("外呼失败", e);
            return RestResult.fail(ResultCode.SYS_ERROR, "外呼失败");
        }
        return RestResult.success(callUserVo);
    }

    /**
     * 新增外呼备注
     *
     * @param
     * @return
     */
    @PostMapping(value = "/call/save")
    public RestResult<Void> callSave(@RequestBody CallRecordSaveReq callRecordSaveReq) {
        try {
            log.info("callSave callRecordSaveReq : {}", JSON.toJSONString(callRecordSaveReq));
            orderInfoService.callSave(callRecordSaveReq);
        } catch (Exception e) {
            log.error("新增外呼备注", e);
            return RestResult.fail(ResultCode.SYS_ERROR, "新增外呼备注");
        }
        return RestResult.success();
    }

    @PostMapping(value = "/queryCallRecord")
    public RestResult<PageInfo<CallRecordVo>> queryCallRecord(@RequestBody CallRecordQueryReq callRecordQueryReq) {
        PageInfo<CallRecordVo> callRecordVoPageInfo = null;
        try {
            log.info("queryCallRecord start callReq:{}", JSON.toJSONString(callRecordQueryReq));
            callRecordVoPageInfo = orderInfoService.queryCallRecord(callRecordQueryReq);
        } catch (Exception e) {
            log.error("queryCallRecord msg:{}", e.getMessage());
        }
        return RestResult.success(callRecordVoPageInfo);
    }
}
