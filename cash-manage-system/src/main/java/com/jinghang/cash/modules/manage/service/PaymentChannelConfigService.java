package com.jinghang.cash.modules.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.enums.WhetherState;
import com.jinghang.cash.modules.manage.vo.req.PaymentChannelConfigDetailRequest;
import com.jinghang.cash.modules.manage.vo.req.PaymentChannelConfigPagingRequest;
import com.jinghang.cash.modules.manage.vo.req.PaymentChannelConfigSaveRequest;
import com.jinghang.cash.modules.manage.vo.res.PaymentChannelConfigResponse;
import com.jinghang.cash.pojo.PaymentChannelConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-27
 */
public interface PaymentChannelConfigService extends IService<PaymentChannelConfig> {

    void save(PaymentChannelConfigSaveRequest request);

    PaymentChannelConfigResponse detail(PaymentChannelConfigDetailRequest request);

    PageInfo<PaymentChannelConfigResponse> paging(PaymentChannelConfigPagingRequest request);

    void changeStatus(String id, WhetherState status);

    List<PaymentChannelConfigResponse> selectList();
}
