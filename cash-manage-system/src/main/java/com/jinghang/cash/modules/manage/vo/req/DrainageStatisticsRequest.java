package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.enums.FlowChannel;
import com.jinghang.cash.modules.manage.vo.PageParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.LocalDate;

/**
 * <AUTHOR> gale
 * @Classname DrainageStatisticsRequest
 * @Description TODO
 * @Date 2023/11/20 16:59
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrainageStatisticsRequest extends PageParam {

    private LocalDate startDate;

    private LocalDate endDate;

    @Enumerated(EnumType.STRING)
    private FlowChannel applyChannel;


}
