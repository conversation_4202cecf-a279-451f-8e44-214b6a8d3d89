package com.jinghang.cash.modules.manage.Job;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jinghang.cash.enums.AuditState;
import com.jinghang.cash.mapper.DataStatisticsMapper;
import com.jinghang.cash.pojo.*;
import com.jinghang.cash.service.*;
import com.jinghang.common.util.JsonUtil;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> gale
 * @Classname DataStatisticsDataJob
 * @Description 数据统计
 * @Date 2023/11/27 16:20
 */
@Component
@JobHandler("dataStatisticsDataJob")
public class DataStatisticsDataJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(DataStatisticsDataJob.class);
    @Autowired
    private DataStatisticsService dataStatisticsService;


    @Autowired
    private UserRegisterService userRegisterService;


    @Autowired
    private UserRiskRecordService userRiskRecordService;

    @Autowired
    private LoanService loanService;

    @Autowired
    private CreditService creditService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private DataStatisticsMapper dataStatisticsMapper;


    @Override
    public void doJob(JobParam jobParam) {
        logger.info("loanAgreementJob jobParam:{}", JsonUtil.toJsonString(jobParam));
        LocalDate startDate;
        if (jobParam == null) {
            startDate = LocalDate.now();
        } else {
            startDate = jobParam.getStartDate() == null ? LocalDate.now() : jobParam.getStartDate();
        }
        this.build(startDate);
    }

    public void build(LocalDate date) {
        LocalDateTime endTime = date.plusDays(1L).atStartOfDay();


        //        LocalDate date = LocalDate.of(2023, 7, 13);
        logger.info("数据统计任务 开始执行 执行日期:{}", date);
        LocalDateTime startTime = date.atStartOfDay();
//        LocalDateTime endTime = date.plusDays(360L).atStartOfDay();
//        dataStatistics.setStatisticDate(date);
//        dataStatistics.setStatisticDate(LocalDate.now());


        DataStatistics dataStatistics = queryDataStatistics(date);

        dataStatistics.setRegisterNum(Math.toIntExact(this.countRegister(startTime, endTime)));
        dataStatistics.setApplyLimitNum(Math.toIntExact(this.countRisk(startTime, endTime)));
        dataStatistics.setApplyLimitRefuseNum(Math.toIntExact(this.countRiskFail(startTime, endTime)));
        dataStatistics.setApplyLimitPassNum(Math.toIntExact(this.countRiskSucceed(startTime, endTime)));
        dataStatistics.setApplyLimitPassAmount(this.countRiskAmtPass(startTime, endTime));
        dataStatistics.setRiskPassNum(Math.toIntExact(this.countRiskSucceed(startTime, endTime)));
        dataStatistics.setRiskPassAmount(this.countRiskAmtPass(startTime, endTime));
        dataStatistics.setRiskRefuseNum(Math.toIntExact(this.countRiskFail(startTime, endTime)));
        dataStatistics.setRiskRefuseAmount(this.countRiskAmtReject(startTime, endTime));

        List<Map<String, Object>> maps = this.applyLoan(startTime, endTime);
        Map<String, Object> countMap = maps.get(0);
        dataStatistics.setApplyNum(Integer.valueOf(countMap.get("count").toString()));
        dataStatistics.setApplyAmount(new BigDecimal(countMap.get("totalAmt").toString()));
        List<Map<String, Object>> bankCreditMaps = this.bankCredit(startTime, endTime);
        Map<String, Object> bankCreditCount = bankCreditMaps.get(0);
        dataStatistics.setCapitalCreditNum(Integer.valueOf(bankCreditCount.get("count").toString()));
        dataStatistics.setCapitalCreditAmount(new BigDecimal(bankCreditCount.get("totalAmt").toString()));

        List<Map<String, Object>> bankCreditByStateMaps = this.bankCreditByState(startTime, endTime);
        for (Map<String, Object> bankCreditByStateMap : bankCreditByStateMaps) {
            if (bankCreditByStateMap.containsKey("state") && "SUCCEED".equals(bankCreditByStateMap.get("state").toString())) {
                dataStatistics.setCapitalCreditNum(Integer.valueOf(bankCreditByStateMap.get("count").toString()));
                dataStatistics.setCapitalCreditAmount(new BigDecimal(bankCreditByStateMap.get("totalAmt").toString()));
            } else if (bankCreditByStateMap.containsKey("state") && "FAILED".equals(bankCreditByStateMap.get("state").toString())) {
                dataStatistics.setCapitalRefuseNum(Integer.valueOf(bankCreditByStateMap.get("count").toString()));
                dataStatistics.setCapitalRefuseAmount(new BigDecimal(bankCreditByStateMap.get("totalAmt").toString()));
            } else if (bankCreditByStateMap.containsKey("state") && "PROCESSING".equals(bankCreditByStateMap.get("state").toString())) {
                dataStatistics.setCapitalProcessingNum(Integer.valueOf(bankCreditByStateMap.get("count").toString()));
            }
        }

        List<Map<String, Object>> orderByStateMaps = orderByState(startTime, endTime);

        AtomicInteger orderStatusIngTotal = new AtomicInteger();
        orderByStateMaps.forEach(map -> {

            if (map.containsKey("orderState") && "INIT".equals(map.get("orderState").toString())) {
                orderStatusIngTotal.addAndGet(Integer.parseInt(map.get("count").toString()));
            } else if (map.containsKey("orderState") && "CREDITING".equals(map.get("orderState").toString())) {
                orderStatusIngTotal.addAndGet(Integer.parseInt(map.get("count").toString()));
            }
        });
        dataStatistics.setApproveNum(orderStatusIngTotal.get());
        dataStatistics.setReviewRights(Math.toIntExact(this.orderByRightsMarking(startTime, endTime)));

        List<Map<String, Object>> applyLoanMaps = applyLoanInit(startTime, endTime);
        applyLoanMaps.forEach(map -> {
            if (map.containsKey("loanState") && "INIT".equals(map.get("loanState").toString())) {
                dataStatistics.setAwaitCollectNum(Integer.valueOf(map.get("count").toString()));
                dataStatistics.setAwaitCollectAmount(new BigDecimal(map.get("totalAmt").toString()));
            } else if (map.containsKey("loanState") && "PROCESSING".equals(map.get("loanState").toString())) {
                dataStatistics.setAwaitLoanNum(Integer.valueOf(map.get("count").toString()));
                dataStatistics.setAwaitLoanAmount(new BigDecimal(map.get("totalAmt").toString()));
            } else if (map.containsKey("loanState") && "FAILED".equals(map.get("loanState").toString())) {
                dataStatistics.setCollectFailNum(Integer.valueOf(map.get("count").toString()));
                dataStatistics.setCollectFailAmount(new BigDecimal(map.get("totalAmt").toString()));
                dataStatistics.setLoanFailNum(Integer.valueOf(map.get("count").toString()));
                dataStatistics.setLoanFailAmount(new BigDecimal(map.get("totalAmt").toString()));
            } else if (map.containsKey("loanState") && "SUCCEED".equals(map.get("loanState").toString())) {
                dataStatistics.setLoanNum(Integer.valueOf(map.get("count").toString()));
                dataStatistics.setLoanSucceedAmount(new BigDecimal(map.get("totalAmt").toString()));
            }
        });
        dataStatisticsService.saveOrUpdate(dataStatistics);

        logger.info("更新完成,dataStatistics:{}", JSON.toJSONString(dataStatistics));


    }

    /**
     * 查询记录
     *
     * @param date
     * @return
     */
    public DataStatistics queryDataStatistics(LocalDate date) {
        QueryWrapper<DataStatistics> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DataStatistics::getStatisticDate, date);
        DataStatistics queryData = dataStatisticsMapper.selectOne(queryWrapper);
        if (queryData == null) {
            DataStatistics dataStatistics = new DataStatistics();
            dataStatistics.setStatisticDate(date);
            return dataStatistics;
        }
        return queryData;
    }

    /**
     * 注册人数
     *
     * @param startTime
     */
    public long countRegister(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<UserRegister> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("IFNULL(COUNT(DISTINCT mobile),0) as count").lambda()
                .ge(UserRegister::getCreatedTime, startTime)
                .lt(UserRegister::getCreatedTime, endTime);
        List<Map<String, Object>> maps = userRegisterService.listMaps(queryWrapper);
        if (CollectionUtil.isEmpty(maps)) {
            return 0;
        }
        return Long.parseLong(maps.get(0).get("count").toString());
    }


    /**
     * 申请额度人数
     *
     * @param startTime
     * @param endTime
     */
    public long countRisk(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<UserRiskRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(UserRiskRecord::getCreatedTime, startTime);
        queryWrapper.lt(UserRiskRecord::getCreatedTime, endTime);
        return userRiskRecordService.count(queryWrapper);
    }


    /**
     * 申请额度失败人数
     *
     * @param startTime
     * @param endTime
     */
    public long countRiskFail(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<UserRiskRecord> queryWrapper = Wrappers.<UserRiskRecord>lambdaQuery();
        queryWrapper.ge(UserRiskRecord::getCreatedTime, startTime);
        queryWrapper.lt(UserRiskRecord::getCreatedTime, endTime);
        queryWrapper.eq(UserRiskRecord::getApproveResult, AuditState.REJECT);
        return userRiskRecordService.count(queryWrapper);
    }

    /**
     * 申请额度成功人数
     *
     * @param startTime
     * @param endTime
     */
    public long countRiskSucceed(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<UserRiskRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(UserRiskRecord::getCreatedTime, startTime);
        queryWrapper.lt(UserRiskRecord::getCreatedTime, endTime);
        queryWrapper.eq(UserRiskRecord::getApproveResult, AuditState.PASS);
        return userRiskRecordService.count(queryWrapper);
    }


    /**
     * 申请额度成功金额
     *
     * @param startTime
     * @param endTime
     */
    public BigDecimal countRiskAmtPass(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<UserRiskRecord> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.select("IFNULL(sum(approve_amount),0) as totalAmt").lambda()
                .ge(UserRiskRecord::getCreatedTime, startTime).lt(UserRiskRecord::getCreatedTime, endTime)
                .eq(UserRiskRecord::getApproveResult, AuditState.PASS)
                .groupBy(UserRiskRecord::getApproveResultCode);
        List<Map<String, Object>> maps = userRiskRecordService.getBaseMapper().selectMaps(queryWrapper1);
        if (!maps.isEmpty()) {
            return maps.get(0).get("totalAmt") == null ? BigDecimal.ZERO : new BigDecimal(maps.get(0).get("totalAmt").toString());
        } else {
            return BigDecimal.ZERO;
        }

    }

    /**
     * 申请额度成功金额
     *
     * @param startTime
     * @param endTime
     */
    public BigDecimal countRiskAmtReject(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<UserRiskRecord> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.select("IFNULL(sum(approve_amount),0) as totalAmt").lambda()
                .ge(UserRiskRecord::getCreatedTime, startTime).lt(UserRiskRecord::getCreatedTime, endTime)
                .eq(UserRiskRecord::getApproveResult, AuditState.REJECT);
        List<Map<String, Object>> maps = userRiskRecordService.getBaseMapper().selectMaps(queryWrapper1);
        if (!maps.isEmpty()) {
            return maps.get(0).get("totalAmt") == null ? BigDecimal.ZERO : new BigDecimal(maps.get(0).get("totalAmt").toString());
        } else {
            return BigDecimal.ZERO;
        }

    }

    /**
     * 申请借款 人数 金额
     *
     * @param startTime
     * @param endTime
     */
    public List<Map<String, Object>> applyLoan(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<Loan> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.select("COUNT(0) as count,IFNULL(sum(amount),0) as totalAmt ").lambda()
                .ge(Loan::getCreatedTime, startTime).lt(Loan::getCreatedTime, endTime);
        return loanService.getBaseMapper().selectMaps(queryWrapper1);
    }

    /**
     * 资方授信人数 金额
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public List<Map<String, Object>> bankCredit(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<Credit> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("COUNT(0) as count,IFNULL(SUM(credit_amt),0) as totalAmt").lambda()
                .ge(Credit::getCreatedTime, startTime).lt(Credit::getCreatedTime, endTime);
        return creditService.getBaseMapper().selectMaps(queryWrapper);
    }


    /**
     * 资方授信人数 金额 根据状态
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public List<Map<String, Object>> bankCreditByState(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<Credit> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("COUNT(0) as count,state,IFNULL(SUM(credit_amt),0) as totalAmt").lambda()
                .ge(Credit::getCreatedTime, startTime).lt(Credit::getCreatedTime, endTime)
                .groupBy(Credit::getState);
        return creditService.getBaseMapper().selectMaps(queryWrapper);
    }

    /**
     * 订单不同状态人数
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public List<Map<String, Object>> orderByState(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("COUNT(0) as count,order_state as orderState").lambda()
                .ge(Order::getCreatedTime, startTime).lt(Order::getCreatedTime, endTime)
                .groupBy(Order::getOrderState);
        return orderService.getBaseMapper().selectMaps(queryWrapper);
    }


    /**
     * 未确认购买权益
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public long orderByRightsMarking(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<Order> queryWrapper = Wrappers.<Order>lambdaQuery();
        queryWrapper
                .ge(Order::getCreatedTime, startTime).lt(Order::getCreatedTime, endTime)
                .eq(Order::getRightsMarking, "N")
                .eq(Order::getOrderSubmitState, "Y");
        return orderService.count(queryWrapper);
    }


    /**
     * 贷款订单人数和金额
     *
     * @param startTime
     * @param endTime
     */
    public List<Map<String, Object>> applyLoanInit(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<Loan> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.select("COUNT(0) as count,IFNULL(sum(amount),0) as totalAmt ,loan_state as loanState").lambda()
                .ge(Loan::getCreatedTime, startTime).lt(Loan::getCreatedTime, endTime)
                .groupBy(Loan::getLoanState);
        return loanService.getBaseMapper().selectMaps(queryWrapper1);
    }

}
