package com.jinghang.cash.modules.manage.Job;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.enums.BankChannel;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/6/5
 */
public class JobParam {
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    private BankChannel bankChannel;

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }
}
