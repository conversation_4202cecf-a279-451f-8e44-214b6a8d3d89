package com.jinghang.cash.modules.manage.vo.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OfflineRepayApplyRequest extends OrderInfoReq implements Serializable {

    private static final long serialVersionUID = 6173601215633377193L;
    /**
     * 还款状态
     */
    private String applyState;

    private List<String> orderIds;
}
