package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.enums.WhetherState;
import com.jinghang.cash.modules.manage.vo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 * <AUTHOR>
 * @since 2024-12-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PaymentChannelConfigPagingRequest extends PageParam {

    private String paymentChannelCode;

    @Enumerated(EnumType.STRING)
    private WhetherState enabled;
}
