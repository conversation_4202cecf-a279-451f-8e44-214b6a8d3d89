package com.jinghang.cash.modules.manage.vo.rsp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class TrialRes {

    /**
     * 期数
     */
    private Integer period;

    private BigDecimal principal;

    private BigDecimal interest;
    /**
     * 罚息
     */
    private BigDecimal penalty;
    /**
     * 违约金
     */
    private BigDecimal breachFee;
    /**
     * 融担费
     */
    private BigDecimal guaranteeFee;

    /**
     * 咨询费
     */
    private BigDecimal consultFee;

    /**
     * 应还金额
     */
    private BigDecimal amount;




}
