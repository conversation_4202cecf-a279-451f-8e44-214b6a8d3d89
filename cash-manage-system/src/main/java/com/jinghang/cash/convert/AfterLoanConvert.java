package com.jinghang.cash.convert;

import com.jinghang.cash.enums.BankChannel;
import com.jinghang.cash.modules.manage.vo.req.ReduceApplyReq;
import com.jinghang.cash.modules.manage.vo.req.RepayApplyReq;
import com.jinghang.cash.modules.manage.vo.rsp.RepayPlanRsp;
import com.jinghang.cash.modules.manage.vo.rsp.TrialRes;
import com.jinghang.cash.pojo.RepayPlan;
import com.jinghang.ppd.api.dto.TrailResultDto;
import com.jinghang.ppd.api.dto.repay.ReduceApplyDto;
import com.jinghang.ppd.api.dto.repay.RepayApplyDto;
import com.jinghang.ppd.api.enums.RepayPurpose;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AfterLoanConvert {
    AfterLoanConvert INSTANCE = Mappers.getMapper(AfterLoanConvert.class);


    RepayPlanRsp toRepayPlanRsp(RepayPlan repayPlan);

    TrialRes toTrialRes(TrailResultDto trailResultDto);

    ReduceApplyDto toReduceApplyDto(ReduceApplyReq reduceApplyReq);

    @Mapping(source = "actAmount", target = "amount")
    RepayApplyDto toRepayApplyDto(RepayApplyReq req);

    RepayPurpose toRepayPurpose(com.jinghang.cash.enums.RepayPurpose repayPurpose);

    BankChannel toCore(com.jinghang.cash.enums.BankChannel bankChannel);


}
