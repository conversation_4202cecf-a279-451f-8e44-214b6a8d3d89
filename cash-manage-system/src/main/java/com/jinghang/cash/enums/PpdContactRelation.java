package com.jinghang.cash.enums;

public enum PpdContactRelation {
    OTHER("00", "未知"),
    SPOUSE("01", "配偶"),
    CHILDREN("02", "子女"),
    PARENTS("03", "父母"),
    SIBLING("04", "兄弟姐妹"),
    FRIEND("05", "朋友"),
    COLLEAGUE("06", "同事"),
    RELATIVE("07", "亲戚"),
    UNKNOWN("08", "未知");

    private final String code;

    private final String desc;

    PpdContactRelation(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (PpdContactRelation value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }
}
