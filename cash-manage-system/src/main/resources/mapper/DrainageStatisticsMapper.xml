<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.DrainageStatisticsMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.DrainageStatistics">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="statisticDate" column="statistic_date" jdbcType="TIMESTAMP"/>
        <result property="applyChannel" column="apply_channel" jdbcType="VARCHAR"/>
        <result property="registerNum" column="register_num" jdbcType="INTEGER"/>
        <result property="applyLimitNum" column="apply_limit_num" jdbcType="INTEGER"/>
        <result property="applyLimitRefuseNum" column="apply_limit_refuse_num" jdbcType="INTEGER"/>
        <result property="applyLimitPassNum" column="apply_limit_pass_num" jdbcType="INTEGER"/>
        <result property="applyLimitPassAmount" column="apply_limit_pass_amount" jdbcType="DECIMAL"/>
        <result property="riskPassNum" column="risk_pass_num" jdbcType="INTEGER"/>
        <result property="riskPassAmount" column="risk_pass_amount" jdbcType="DECIMAL"/>
        <result property="riskRefuseNum" column="risk_refuse_num" jdbcType="INTEGER"/>
        <result property="riskRefuseAmount" column="risk_refuse_amount" jdbcType="DECIMAL"/>
        <result property="applyNum" column="apply_num" jdbcType="INTEGER"/>
        <result property="applyAmount" column="apply_amount" jdbcType="DECIMAL"/>
        <result property="capitalCreditNum" column="capital_credit_num" jdbcType="INTEGER"/>
        <result property="capitalCreditAmount" column="capital_credit_amount" jdbcType="DECIMAL"/>
        <result property="capitalRefuseNum" column="capital_refuse_num" jdbcType="INTEGER"/>
        <result property="capitalRefuseAmount" column="capital_refuse_amount" jdbcType="DECIMAL"/>
        <result property="approveNum" column="approve_num" jdbcType="INTEGER"/>
        <result property="reviewRights" column="review_rights" jdbcType="INTEGER"/>
        <result property="awaitCollectNum" column="await_collect_num" jdbcType="INTEGER"/>
        <result property="awaitCollectAmount" column="await_collect_amount" jdbcType="DECIMAL"/>
        <result property="awaitLoanNum" column="await_loan_num" jdbcType="INTEGER"/>
        <result property="awaitLoanAmount" column="await_loan_amount" jdbcType="DECIMAL"/>
        <result property="collectFailNum" column="collect_fail_num" jdbcType="INTEGER"/>
        <result property="collectFailAmount" column="collect_fail_amount" jdbcType="DECIMAL"/>
        <result property="loanNum" column="loan_num" jdbcType="INTEGER"/>
        <result property="loanSucceedAmount" column="loan_succeed_amount" jdbcType="DECIMAL"/>
        <result property="loanFailAmount" column="loan_fail_amount" jdbcType="DECIMAL"/>
        <result property="loanFailNum" column="loan_fail_num" jdbcType="INTEGER"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="capitalProcessingNum" column="capital_processing_num" jdbcType="INTEGER"/>

    </resultMap>

    <sql id="Base_Column_List">
        id
        ,statistic_date,apply_channel,
        register_num,apply_limit_num,apply_limit_refuse_num,
        apply_limit_pass_num,apply_limit_pass_amount,risk_pass_num,
        risk_pass_amount,risk_refuse_num,risk_refuse_amount,
        apply_num,apply_amount,capital_credit_num,
        capital_credit_amount,capital_refuse_num,capital_refuse_amount,
        approve_num,review_rights,await_collect_num,
        await_collect_amount,await_loan_num,await_loan_amount,
        collect_fail_num,collect_fail_amount,loan_num,
        loan_succeed_amount,loan_fail_amount,revision,
        created_by,created_time,updated_by,
        updated_time,loan_fail_num.capital_processing_num
    </sql>
</mapper>
