<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.data.QhRepayPlanMapper">

    <select id="statisticOverdueRepay" resultType="com.jinghang.cash.modules.manage.Job.dto.OverdueRepayPlanDTO">
        SELECT
        t.loanId,
        sum( principal_amt + interest_amt + guarantee_amt + consult_fee ) AS loanBalance,
        sum( CASE WHEN cust_repay_state = 'NORMAL' THEN principal_amt - ifnull( act_principal_amt, 0 ) ELSE principal_amt END ) AS remainPrincipal,
        min(t.minPlanRepayDate) as minPlanRepayDate,
        min(t.minPeriod) as minPeriod,
        min(DATEDIFF( #{date}, minPlanRepayDate )) AS overdueDays,
        min(t.overdueAmt) as overdueAmt
        FROM
        (
        SELECT
        loan_id AS loanId,
        min( plan_repay_date ) AS minPlanRepayDate,
        min( period ) AS minPeriod,
        sum( CASE WHEN cust_repay_state = 'NORMAL' THEN amount - ifnull( act_amount, 0 ) ELSE amount END ) AS overdueAmt
        FROM
        s03_qh_repay_plan
        <where>
            (cust_repay_state = 'NORMAL' OR act_repay_time &gt;= #{date}) AND plan_repay_date &lt; #{date}
        </where>
        GROUP BY loan_id
        ) t,
        s03_qh_repay_plan p
        WHERE
        DATE_FORMAT( p.plan_repay_date, "%Y%m" ) >= DATE_FORMAT( t.minPlanRepayDate, "%Y%m" )
        AND p.loan_id = t.loanId
        GROUP BY
        t.loanId
    </select>
</mapper>
