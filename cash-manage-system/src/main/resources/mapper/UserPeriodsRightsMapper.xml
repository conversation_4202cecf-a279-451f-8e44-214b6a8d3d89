<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.UserPeriodsRightsMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.UserPeriodsRights">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>

        <result property="riskId" column="risk_id" jdbcType="VARCHAR"/>
        <result property="rightsAmount" column="rights_amount" jdbcType="DECIMAL"/>
        <result property="period" column="period" jdbcType="INTEGER"/>
        <result property="rightsPackageId" column="rights_package_id" jdbcType="DECIMAL"/>

    </resultMap>

    <sql id="Base_Column_List">
        id,remark,revision,
        created_by,created_time,updated_by,
        updated_time,
        risk_id,rights_amount,period,rights_package_id
    </sql>
    <select id="getByRiskId" resultType="com.jinghang.cash.pojo.UserPeriodsRights">
        select
        <include refid="Base_Column_List"/>
        from
        id,remark,revision,
        created_by,created_time,updated_by,
        updated_time,
        risk_id,rights_amount,period,rights_package_id from user_periods_rights ) u
        where u.risk_id = #{riskId} and u.rn = 1
    </select>
    <select id="getRightsAmountByOrderId" resultType="java.math.BigDecimal">
        select upr.rights_amount from `order` o left join user_periods_rights upr on o.risk_id = upr.risk_id
        and o.rights_package_id = upr.rights_package_id
        where o.id =#{orderId}
        order by upr.id desc limit 1
    </select>
</mapper>
