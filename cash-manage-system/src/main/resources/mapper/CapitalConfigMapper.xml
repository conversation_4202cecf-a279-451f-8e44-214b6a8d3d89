<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.CapitalConfigMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.CapitalConfig">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="bankChannel" column="bank_channel" jdbcType="VARCHAR"/>
        <result property="creditDayLimit" column="credit_day_limit" jdbcType="DECIMAL"/>
        <result property="loanDayLimit" column="loan_day_limit" jdbcType="DECIMAL"/>
        <result property="periodsRange" column="periods_range" jdbcType="VARCHAR"/>
        <result property="agesRange" column="ages_range" jdbcType="VARCHAR"/>
        <result property="singleAmtRange" column="single_amt_range" jdbcType="VARCHAR"/>
        <result property="creditStartTime" column="credit_start_time" jdbcType="VARCHAR"/>
        <result property="creditEndTime" column="credit_end_time" jdbcType="VARCHAR"/>
        <result property="loanStartTime" column="loan_start_time" jdbcType="VARCHAR"/>
        <result property="loanEndTime" column="loan_end_time" jdbcType="VARCHAR"/>
        <result property="enabled" column="enabled" jdbcType="VARCHAR"/>
        <result property="bankRate" column="bank_rate" jdbcType="DECIMAL"/>
        <result property="creditTimeStatus" column="credit_time_status" jdbcType="VARCHAR"/>
        <result property="loanTimeStatus" column="loan_time_status" jdbcType="VARCHAR"/>
        <result property="repayTimeStatus" column="repay_time_status" jdbcType="VARCHAR"/>
        <result property="supportIrrLevel" column="support_irr_level" jdbcType="VARCHAR"/>
        <result property="repayStartTime" column="repay_start_time" jdbcType="VARCHAR"/>
        <result property="repayEndTime" column="repay_end_time" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,bank_channel,credit_day_limit,
        loan_day_limit,periods_range,ages_range,
        single_amt_range,credit_start_time,credit_end_time,
        loan_start_time,loan_end_time,enabled,
        bank_rate,support_irr_level,repay_start_time,
        repay_end_time,remark,revision,
        created_by,created_time,updated_by,enabled,
        updated_time
    </sql>
    <sql id="queryAll">
        id
        ,bank_channel,credit_day_limit,loan_day_limit,periods_range,created_by,created_time,updated_by,enabled,
        updated_time
    </sql>

    <select id="queryAll" resultType="com.jinghang.cash.pojo.CapitalConfig">
        select
        <include refid="queryAll"/>
        from capital_config order by enabled desc , created_time desc
    </select>
    <select id="getCapitalList" resultType="com.jinghang.cash.pojo.CapitalConfig">
        select  id,  bank_channel from capital_config
        where enabled = 'ENABLE'
    </select>

</mapper>
