<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.MessageSendRecordMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.MessageSendRecord">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <id property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="sendChannel" column="send_channel" jdbcType="VARCHAR"/>
            <result property="messageType" column="message_type" jdbcType="VARCHAR"/>
            <result property="messageContent" column="message_content" jdbcType="VARCHAR"/>
            <result property="revision" column="revision" jdbcType="INTEGER"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,mobile,order_no,send_channel,
        message_type,message_content,revision,
        created_by,created_time,updated_by,
        updated_time
    </sql>
</mapper>
