# 手动触发同步指定范围内的case_operation数据到es
# 脚本运行：python3 sync_case_operation_touch.py start end
import json
from datasync.base_data_to_es import DataToEs
from datasync.datetime_to_json import DateEncoder
from concurrent.futures import ThreadPoolExecutor
import threading
import sys

from utils.logger_util import LOG

es_index = "bpw_case_operation_query"
es_type = "_doc"

logger = LOG.get_logger("sync_bpw_case_operation_touch")

sync_util = DataToEs()

# 需要转移的数据id，最大、最小值
transerDataMaxAndMinId = "SELECT max(cao.id) AS max,min(cao.id) AS min " \
                         "FROM `case_operation` cao " \
                         "LEFT JOIN `case_info` ca ON cao.case_id=ca.id " \
                         "LEFT JOIN `out_batch` batch ON ca.out_batch_id=batch.id " \
                         "WHERE cao.id >= %d and cao.id <= %d"

# 查询转移sql
transerDataSqlMeta = "SELECT cao.create_time,cao.action_type,cao.call_type,cao.reduce_amount," \
                     "cao.DESC,cao.ptp_amount,cao.ptp_time,cao.next_time,cao.id,cao.create_by," \
                     "cao.update_by,cao.call_uuid,cao.call_style,cao.call_time,cao.ring_durtion," \
                     "cao.call_durtion,cao.outcome,cao.submit_type,cao.caller,cao.create_type," \
                     "cao.con_mobile,cao.con_name,cao.relation_type,cao.operator_name,cao.STATUS," \
                     "cao.tag,cao.operation_state,cao.is_hidden,cao.case_id,cao.org_id,cao.org_delt_id," \
                     "cao.out_serial_no,cao.out_serial_temp,cao.COMMENT,cao.admin_submitter,cao.client_label_id," \
                     "cao.callback_flag,cao.site_id,cao.site_name,cao.oper_time,cao.pool_id,cao.auto_assist_record," \
                     "cao.total_oper_time,cao.is_push,cao.intention,cao.intention_name," \
                     "ca.product_id,ca.user_id,ca.team_id,ca.dep_id,ca.out_batch_id," \
                     "ca.inner_batch_id,ca.NAME,ca.id_card,ca.own_mobile," \
                     "batch.NAME AS out_batch_no " \
                     "FROM `case_operation` cao " \
                     "LEFT JOIN `case_info` ca ON cao.case_id=ca.id " \
                     "LEFT JOIN `out_batch` batch ON ca.out_batch_id=batch.id " \
                     "WHERE cao.id >= %d and cao.id <= %d"


def main():
    start = int(sys.argv[1])
    end = int(sys.argv[2])
    if start is None or end is None or start > end:
        logger.info("启动参数异常")
        return

    min_max = sync_util.db.select_one(transerDataMaxAndMinId % (start, end))

    min_id = min_max["min"]
    max_id = min_max["max"]
    if min_id is None or max_id is None:
        logger.info("没有数据需要同步")
        return

    total = max_id - min_id + 1
    fetch_size = 10000
    parts = total // fetch_size
    if total % fetch_size != 0:
        parts = parts + 1

    thread_pool = ThreadPoolExecutor(max_workers=5, thread_name_prefix="sync_bpw_case_operation_touch")
    futures = []
    for part in range(parts):
        part_start_id = min_id + (fetch_size * part)
        part_end_id = min_id + (fetch_size * (part + 1)) - 1
        if part_end_id > max_id:
            part_end_id = max_id

        future = thread_pool.submit(run, part_start_id, part_end_id)
        futures.append(future)
    for future in futures:
        try:
            future.result(60)
        except Exception as e:
            logger.exception(e)


def run(part_start_id, part_end_id):
    try:
        thread_name = threading.current_thread().name
        transer_datas = sync_util.db.select(transerDataSqlMeta % (part_start_id, part_end_id))
        if transer_datas:
            action_list = []
            for case_operation in transer_datas:
                logger.info(f'{thread_name}同步数据 >>> {case_operation}')
                action_list.append(build_common_source(case_operation))

            if len(action_list) > 0:
                sync_util.es_bulk(action_list)

        # log.info("<<<<<<<<<<<<<<<<<<<<" + thread_name + " == sync_case_operation_touch>>>>>>>>>>>>>>>>>>>>")
    except Exception as e:
        logger.exception(e)


def build_common_source(case_operation):
    case_operation_id = case_operation["id"]
    source = {}

    if case_operation_id is not None:
        source["id"] = case_operation_id

    create_time = case_operation["create_time"]
    if create_time is not None:
        source["createTime"] = create_time

    action_type = case_operation["action_type"]
    if action_type is not None:
        source["actionType"] = action_type

    call_type = case_operation["call_type"]
    if call_type is not None:
        source["callType"] = call_type

    reduce_amount = case_operation["reduce_amount"]
    if reduce_amount is not None:
        source["reduceAmount"] = reduce_amount

    desc = case_operation["DESC"]
    if desc is not None:
        source["desc"] = desc

    ptp_amount = case_operation["ptp_amount"]
    if ptp_amount is not None:
        source["ptpAmount"] = ptp_amount

    ptp_time = case_operation["ptp_time"]
    if ptp_time is not None:
        source["ptpTime"] = ptp_time

    next_time = case_operation["next_time"]
    if next_time is not None:
        source["nextTime"] = next_time

    id = case_operation["id"]
    if id is not None:
        source["id"] = id

    create_by = case_operation["create_by"]
    if create_by is not None:
        source["createBy"] = create_by

    update_by = case_operation["update_by"]
    if update_by is not None:
        source["updateBy"] = update_by

    call_uuid = case_operation["call_uuid"]
    if call_uuid is not None:
        source["callUuid"] = call_uuid

    call_style = case_operation["call_style"]
    if call_style is not None:
        source["callStyle"] = call_style

    call_time = case_operation["call_time"]
    if call_time is not None:
        source["callTime"] = call_time

    ring_durtion = case_operation["ring_durtion"]
    if ring_durtion is not None:
        source["ringDurtion"] = ring_durtion

    call_durtion = case_operation["call_durtion"]
    if call_durtion is not None:
        source["callDurtion"] = call_durtion

    outcome = case_operation["outcome"]
    if outcome is not None:
        source["outcome"] = outcome

    submit_type = case_operation["submit_type"]
    if submit_type is not None:
        source["submitType"] = submit_type

    caller = case_operation["caller"]
    if caller is not None:
        source["caller"] = caller

    create_type = case_operation["create_type"]
    if create_type is not None:
        source["createType"] = create_type

    con_mobile = case_operation["con_mobile"]
    if con_mobile is not None:
        source["conMobile"] = con_mobile

    con_name = case_operation["con_name"]
    if con_name is not None:
        source["conName"] = con_name

    relation_type = case_operation["relation_type"]
    if relation_type is not None:
        source["relationType"] = relation_type

    operator_name = case_operation["operator_name"]
    if operator_name is not None:
        source["operatorName"] = operator_name

    status = case_operation["STATUS"]
    if status is not None:
        source["status"] = status

    tag = case_operation["tag"]
    if tag is not None:
        source["tag"] = tag

    operation_state = case_operation["operation_state"]
    if operation_state is not None:
        source["operationState"] = operation_state

    is_hidden = case_operation["is_hidden"]
    if is_hidden is not None:
        source["isHidden"] = is_hidden

    case_id = case_operation["case_id"]
    if case_id is not None:
        source["caseId"] = case_id

    org_id = case_operation["org_id"]
    if org_id is not None:
        source["orgId"] = org_id

    org_delt_id = case_operation["org_delt_id"]
    if org_delt_id is not None:
        source["orgDeltId"] = org_delt_id

    out_serial_no = case_operation["out_serial_no"]
    if out_serial_no is not None:
        source["outSerialNo"] = out_serial_no

    out_serial_no_search = case_operation["out_serial_temp"]
    if out_serial_no_search is not None:
        source["outSerialNoSearch"] = out_serial_no_search

    comment = case_operation["COMMENT"]
    if comment is not None:
        source["comment"] = comment

    admin_submitter = case_operation["admin_submitter"]
    if admin_submitter is not None:
        source["adminSubmitter"] = admin_submitter

    client_label_id = case_operation["client_label_id"]
    if client_label_id is not None:
        source["clientLabelId"] = client_label_id

    product_id = case_operation["product_id"]
    if product_id is not None:
        source["productId"] = product_id

    user_id = case_operation["user_id"]
    if user_id is not None:
        source["userId"] = user_id

    team_id = case_operation["team_id"]
    if team_id is not None:
        source["teamId"] = team_id

    dep_id = case_operation["dep_id"]
    if dep_id is not None:
        source["depId"] = dep_id

    out_batch_id = case_operation["out_batch_id"]
    if out_batch_id is not None:
        source["outBatchId"] = out_batch_id

    inner_batch_id = case_operation["inner_batch_id"]
    if inner_batch_id is not None:
        source["innerBatchId"] = inner_batch_id

    name = case_operation["NAME"]
    if name is not None:
        source["name"] = name

    id_card = case_operation["id_card"]
    if id_card is not None:
        source["idCard"] = id_card

    own_mobile = case_operation["own_mobile"]
    if own_mobile is not None:
        source["ownMobile"] = own_mobile

    out_batch_no = case_operation["out_batch_no"]
    if out_batch_no is not None:
        source["outBatchNo"] = out_batch_no

    callback_flag = case_operation["callback_flag"]
    if callback_flag is not None:
        source["callbackFlag"] = callback_flag

    site_id = case_operation["site_id"]
    if site_id is not None:
        source["siteId"] = site_id

    site_name = case_operation["site_name"]
    if site_name is not None:
        source["siteName"] = site_name

    oper_time = case_operation["oper_time"]
    if oper_time is not None:
        source["operTime"] = oper_time

    is_push = case_operation["is_push"]
    if is_push is not None:
        source["isPush"] = is_push

    total_oper_time = case_operation["total_oper_time"]
    if total_oper_time is not None:
        source["totalOperTime"] = total_oper_time

    pool_id = case_operation["pool_id"]
    if pool_id is not None:
        source["poolId"] = pool_id

    auto_assist_record = case_operation["auto_assist_record"]
    if auto_assist_record is not None:
        source["autoAssistRecord"] = auto_assist_record

    intention = case_operation["intention"]
    if intention is not None:
        source["intention"] = intention

    intention_name = case_operation["intention_name"]
    if intention_name is not None:
        source["intentionName"] = intention_name

    source_json = json.dumps(source, cls=DateEncoder)

    return {
        "_index": es_index,
        "_type": es_type,
        "_id": case_operation_id,
        "_source": source_json
    }


if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        logger.exception(e)
