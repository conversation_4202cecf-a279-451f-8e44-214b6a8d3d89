<template>
  <Tabs v-model="ctrlType" :animated="false">
    <TabPane label="管控池" name="1">
      <casePool v-if="ctrlType === '1'"></casePool>
    </TabPane>
    <TabPane label="全局管控" name="2">
      <caseAll v-if="ctrlType === '2'"></caseAll>
    </TabPane>
  </Tabs>
</template>

<script setup>
import casePool from './caseCtrl/casePool.vue'
import caseAll from './caseCtrl/caseAll.vue'
import { ref } from 'vue'
const ctrlType = ref('1')
</script>

<style scoped lang='less'>
  :deep(.ivu-tabs-nav .ivu-tabs-tab) {
    padding: 0px 16px 8px;
  }
</style>
