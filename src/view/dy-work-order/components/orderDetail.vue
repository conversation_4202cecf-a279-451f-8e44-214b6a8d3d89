<template>
  <div class="detail-content">
    <div class="header-container">
      <div class="header">
        <img v-if="orderInfo.isRemind" src="@/assets/images/order-detail.png" alt="" />
        <span class="title">{{ orderInfo.title }}</span>
        <span class="status" v-if="orderInfo.status === 0" style="background-color:#F0F4F9; color: #B0B7C2;">
          <Icon class="icons" size="16" type="iconfont icon-daijiedan"></Icon>
          <span>待接单</span>
        </span>
        <span class="status" v-if="orderInfo.status === 1">
          <Icon class="icons" size="16" type="iconfont icon-waifangzhong"></Icon>
          <span>受理中</span>
        </span>
        <span class="status" v-if="orderInfo.status === 2" style="background: rgba(193,242,217,0.6); color: #17CB70;">
          <Icon class="icons" size="16" type="iconfont icon-wancheng"></Icon>
          <span>已完结</span>
        </span>
      </div>
      <div class="header-btn">
        <template v-if="fromFlag === 'my'">
          <Button v-if="isHasOrderEndBtn" type="primary" ghost :loading="ui.handleOrderEndLoading" class="h-28 w-56"
            @click='handleOrderEnd'>
            完结
          </Button>
          <Button type="primary" v-if="isHasChangeBtn" ghost :loading="ui.handlehandleChangeLoading" class="h-28 w-56"
            @click='handleChange'> 转交 </Button>
          <Button v-if="isHasOrderFlowBtn" :disabled="orderInfo.level === orderInfo.nodeCount" type="primary"
            :loading="ui.handleOrderFlowLoading" ghost class="h-28 w-104" @click='handleOrderFlow'>
            流入下一节点
          </Button>
          <Button type="primary" v-if="isHasSubmitHandleResultBtn" :loading="ui.SubmitHandleResultLoading"
            class="h-28 w-104" @click='handleSubmitHandleResult'> 提交处理结果 </Button>
          <template v-if="isHasOrderRestartBtn">
            <Button type="primary" :loading="ui.handleOrderRestartLoading" class="h-28 w-104" @click='handleOrderRestart'>
              重新开启 </Button>
          </template>
          <template v-if="isHasTakeOrderBtn">
            <Button type="primary" :loading="ui.handletakeOrderLoading" class="h-28 w-104" @click="handletakeOrder">
              接单
            </Button>
          </template>
        </template>
      </div>
    </div>
    <div class="main-content">
      <div class="left-content">
        <div class="order-desc">
          <div class="desc-header">
            <Icon class="icons" size="16" type="iconfont icon-anjianbiaoqian"></Icon>
            <span style="margin-left: 2px">工单描述</span>
          </div>
          <div v-if="orderInfo.description" class="desc-content">
            {{ orderInfo.description }}
          </div>
          <div class="desc-noContent" v-if="!orderInfo.description">
            <Icon class="icon" size="22" color="#8D959F" type="iconfont icon-kong"></Icon> <span>暂无数据</span>
          </div>
          <img v-if="orderInfo.isRestart" src="@/assets/images/restOrder.png" alt="">
        </div>
        <div class="order-info-container">
          <Tabs v-model="orderInfoTab" class="tab2" name="orderInfo">
            <TabPane label="工单信息" name="nowOrderTab" tab="orderInfo"></TabPane>
            <TabPane label="关联案件信息" name="releOrderTab" tab="orderInfo"></TabPane>
            <TabPane label="其他关联信息" name="otherReleTab" tab="orderInfo"></TabPane>
          </Tabs>
          <div class="case-item-container" v-if="orderInfoTab === 'nowOrderTab'">
            <ul class="case-item" v-if="baseOrderInfo.length">
                <li v-for="(item, index) in baseOrderInfo" :key="index" class="sign-field">
                  <div class="key">
                    {{ item.name }}
                  </div>
                  <div class="value">
                    <span v-if="item.key === 'status'" style="margin-right: 3px">
                      <Icon class="icons" size="16" :style="{ 'color': orderStatusColors[item.value].color }"
                        :type="'iconfont ' + orderStatusColors[item.value].icon"></Icon>
                    </span>
                    {{ item.key === 'status' ? (orderStatusColors[item.value].label) : (item.value || "--") }}
                    <span class="icon-text" v-if="item.key === 'orderNo' && fromFlag === 'my'" @click="addreleOrder">
                      <Icon class="icon" size="16" type="iconfont icon-guanlian"></Icon><span>关联</span>
                    </span>
                  </div>
                </li>
            </ul>
            <ul class="case-item" style="margin-top: 12px">
              <template v-for="(item, index) in fieldListInfo" :key="index">
                <li  class="sign-field"
                  :class="{ field: item.key && item.key === 'file', beforefileindex: index === beforefileindex }">
                  <div class="key">
                    {{ item.name }}
                  </div>
                  <div class="value">
                    <template v-if="orderInfo.fileList.length && item.key === 'file'">
                      <ul class="file-list">
                        <li v-for="i in orderInfo.fileList">
                          <Icon class="icons" size="16" type="iconfont icon-wenjian"></Icon>
                          <Tooltip content="点击查看/下载详情" max-width="400" placement="bottom-start">
                            <span class="file-name" @click="downloads(i.url)">{{ i.name }}</span>
                          </Tooltip>
                        </li>
                      </ul>
                    </template>
                    <template v-else>
                      <template v-if="item.key === 'urgency'">
                        <span>
                          <Icon :color="urgencyStatusList[item.value].color" class="icons" size="16" type="iconfont icon-jinjichengdu"></Icon>
                        </span>
                        {{ urgencyStatusList[item.value].text || "--" }}
                        <span class="icon-text" @click="changeUrgency" v-if="orderInfo.status !== 2 && !isInspection">
                          <Icon class="icon" size="16" type="iconfont icon-bianji3"></Icon><span>修改</span>
                        </span>
                      </template>
                      <template v-else>
                        {{ item.value || "--" }}
                      </template>

                    </template>
                  </div>
                </li>
              </template>
            </ul>
          </div>
          <div class="case-item-container rele-case" v-if="orderInfoTab === 'releOrderTab'">
            <Form v-if="fromFlag === 'my' && !orderInfo.caseInfo && store.getters.userId === orderInfo.createBy" ref="releforms"
              :label-width="74">
              <FormItem label="精准关联:">
                <Input v-model.trim="releCaseId" placeholder="请输入案件编号快速关联案件" clearable @on-clear="clearReleCaseId" class="case-input" />
                <Button type="primary" ghost :loading="ui.caseLoading" class="h-32 w-56 search-btn" @click="searchCase">查询</Button>
              </FormItem>
              <FormItem label="案件归属:" v-if="fromFlag === 'my' && releCaseId && caseInfo.length"
                style="margin-bottom: 16px;">
                <span>{{ caseBelong.join('_') || '未分配' }}</span>
              </FormItem>
            </Form>
            <template v-if="caseInfo.length">
              <ul class="case-item">
                  <li v-for="(item, index) in caseInfo" :key="index">
                    <div class="key">
                      {{ item.name }}
                    </div>
                    <div class="value">
                      {{ item.value || "--" }}
                    </div>
                  </li>
              </ul>
              <div class="rele-case-btn" v-if="caseInfo.length && fromFlag === 'my' && !orderInfo.caseInfo">
                <Button type="primary" class="h-32 w-168" @click='handleReleCase'> 确认关联 </Button>
              </div>
            </template>
            <!-- 案件未关联或者案件查询为空 -->
            <div class="rele-case-empty" v-if="!caseInfo.length">
              <img v-if="!caseError" class="img" src="@/assets/images/empty-plan.png">
              <img v-if="caseError" class="img" src="@/assets/images/no-searchContent.png">
              <p class="tips">{{ caseError ? '搜索内容为空，请重新输入正确案件编号' : '暂未关联案件信息' }}</p>
            </div>
          </div>
          <div v-if="orderInfoTab === 'otherReleTab'" class="rele-case">
            <Tables
              v-if="recordColumns.length && orderInfo.callUuid"
              ref="recordTable"
              :height="570"
              :columns="recordColumns"
              :apifun="recordApiFun"
              :inject-search-data="recordInjectSearchData"
              editable
              pageable
            >
            </Tables>
            <div class="rele-case-empty" v-else>
              <img  class="img" src="@/assets/images/empty-plan.png">
              <p class="tips">暂未关联信息</p>
            </div>
          </div>
        </div>
      </div>
      <div class="right-content">
        <div class="opera-history">
          <div class="desc-header">
            <Icon class="icons" size="16" type="iconfont icon-anjianbiaoqian"></Icon>
            <span style="margin-left: 2px">操作历史</span>
          </div>
          <div style="color: #8d959f">共 {{ orderInfo.recordList.length }} 个操作动作</div>
        </div>
        <div class="opera-history-node">
          <ul class="nodelist" v-if="orderInfo.recordList.length">
            <!-- 催单 -->
            <li v-for="(node,nodeIndex) in recordList" class="node">
              <div class="dot"></div>
              <div class="title">
                <div class="name">{{ node.userName }}</div>
                <div class="point">·</div>
                <div class="name">{{ operType[node.type] }}</div>
                <div class="time">{{ node.updateTime }}</div>
              </div>
              <!-- 2 提交处理结果 -->
              <div class="node-info" v-if="node.type === 2">
                <div v-if="node.content">
                  <span class="info">处理结果：</span><span class="text">{{
                    node.content || "--"
                  }}</span>
                </div>
                <div v-if="node.fileList && node.fileList.length">
                  <span class="info">处理附件：</span>
                  <div class="info-files">
                    <span v-for="(item, index) in node.fileList" :key="item.id">
                      <label>
                        <Icon type="iconfont icon-wenjian" :size="16" class="wenjian" color="#636C78"
                          style="margin-right: 4px" />
                        <span @click="downloads(item.url)">{{ item.name }}</span>
                      </label>
                      <Poptip v-if="!isInspection" confirm title="确认删除该附件？" @on-ok="deleteFile(index, true,nodeIndex,item)" transfer :width="241">
                        <Icon type="iconfont icon-shanchu1" :size="16" class="dele-icon"/>
                      </Poptip>
                    </span>
                  </div>
                </div>
              </div>
              <!-- 4 转交 -->
              <div class="node-info" v-if="node.type === 4">
                <div v-if="node.type === 4">
                  <span class="info">转交至：</span><span class="text">{{ node.action || "--" }}</span>
                </div>
                <div v-if="node.content">
                  <span class="info">备注：</span><span class="text">{{
                    node.content
                  }}</span>
                </div>
              </div>
              <div class="node-info" v-if='[6, 7].includes(node.type) && node.content'>
                <div>
                  <span class="info">备注：</span><span class="text">{{
                    node.content
                  }}</span>
                </div>
              </div>
              <!-- 5 修改紧急程度 -->
              <div class="node-info" v-if="node.type === 5">
                <div>
                  <span class="info">修改为：</span><span class="text">{{
                    node.action || "--"
                  }}</span>
                </div>
              </div>
            </li>
          </ul>
        </div>

        <template v-if="!orderInfo.recordList.length">
          <div class="recordList-empty">
            <img class="img" src="@/assets/images/empty-plan.png">
            <p class="tips">暂无操作历史</p>
          </div>
        </template>
      </div>
    </div>
    <Modal v-model="completedShow" :title="modalType === 'completedContent'
      ? '完结工单'
      : modalType === 'passto'
        ? '转交工单'
        : modalType === 'dealresult'
          ? '提交处理结果'
          : modalType === 'editurgency'
            ? '修改工单紧急程度'
            : modalType === 'Restart'
              ? '重新开启工单'
              : modalType === 'releOrder' ? '关联工单' : ''
      " :footer-hide="true" :closable="false" :mask-closable="false" class="openModal" ref="openModal">
      <template v-if="modalType === 'completedContent'">
        <Alert type="warning" show-icon class="warning no-border-radius no-border" style="margin: -16px -16px 16px">
          工单完结后，如有新的受理需求，工单发起人可重新开启当前工单。
        </Alert>
        <Form ref="formsRef" :label-width="84">
          <FormItem label="备注:">
            <Input v-model.trim="completedContent" type="textarea" placeholder="请输入" :maxlength="200" show-word-limit
              :rows="2" :autosize="{ minRows: 2, maxRows: 15 }" />
          </FormItem>
        </Form>
      </template>
      <!-- 转交工单 -->
      <template v-if="modalType === 'passto'">
        <Form ref="formsRef" class="passToForms" :model="passToForms" :rules="rules" :label-width="80" label-colon>
          <FormItem label="转交类型" prop="type">
            <RadioGroup v-model="passToForms.type">
              <Radio :label="1">部门</Radio>
              <Radio :label="0" style="margin-left: 30px"> 个人 </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="受理对象" prop="handler">
            <Select v-model="passToForms.handler" filterable placeholder="请选择受理对象" label-in-value>
              <OptionGroup v-for="i in  (passToForms.type ? store.getters.sortByavailableTeam :store.getters.sortByTeamUsers) "
                :label="(passToForms.type ? i.name : (i.depName ? i.depName : i.orgName) + '_' + (i.teamName ? i.teamName + '_' : '') + (i.isBankAgent ? '委外' : '内催'))">
                <Option v-for="item in i.children" :value="item.id" :label="item.name" :key="item.id">{{ item.name }}
                </Option>
              </OptionGroup>
            </Select>
          </FormItem>
          <FormItem label="备注">
            <Input v-model.trim="passToForms.remark" type="textarea" placeholder="请输入" :maxlength="200" show-word-limit
              :rows="2" :autosize="{ minRows: 2, maxRows: 15 }" />
          </FormItem>
        </Form>
      </template>
      <!-- 提交处理结果 -->
      <div class='max_height' v-if="modalType === 'dealresult'">
        <Form ref="formsRef" :label-width="84">
          <FormItem label="处理结果:">
            <Input v-model.trim="dealResult.content" type="textarea" placeholder="请输入" :maxlength="500" show-word-limit
              :rows="2" :autosize="{ minRows: 4, maxRows: 15 }" />
          </FormItem>
          <FormItem label="处理附件:">
            <Upload action="" :show-upload-list="false" :before-upload="beforeUploadAssistFile"
              accept="image/*,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/msword,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,.xls,audio/*">
              <Button icon="iconfont icon-shangchuanwenjian1" class="h-32 ivu-btn-grey" :loading="uploadLoading">
                {{ $t("uploadFiles") }}
              </Button>
            </Upload>
            <div class="tips">上传文件支持jpg、png、pdf、docx、wav等。</div>
            <div v-if="files.length" class="files">
              <span v-for="(item, index) in files" :key="item">
                <label style="display: flex;">
                  <Icon type="iconfont icon-wenjian" :size="16" color="#636C78" style="margin-right: 4px" />
                  <span>{{ item }}</span>
                  <Icon type="iconfont icon-shanchu1" :size="16" color="#636C78" style="margin-left: 4px; cursor: pointer"
                    @click="deleteFile(index)" />
                </label>
              </span>
            </div>
          </FormItem>
        </Form>
      </div>
      <!-- 修改紧急程度 -->
      <template v-if="modalType === 'editurgency'">
        <Form ref="formsRef" :label-width="66">
          <FormItem class="editurgency" label="紧急程度:">
            <div class="radio-group">
              <div v-for="item in urgencyStatusList" :key="item.value" class="radio"
                :class="{ 'active-radio': urgencyStatus === item.value }" @click="urgencyStatus = item.value">
                <Icon :style="{ color: item.color }" type="iconfont icon-jinjichengdu" />
                <span>{{ item.text }}</span>
                <img src="@/assets/images/check.png" alt="" />
              </div>
            </div>
          </FormItem>
        </Form>
      </template>
      <!-- 重新开启 -->
      <template v-if="modalType === 'Restart'">
        <Alert type="warning" show-icon class="warning no-border-radius no-border" style="margin: -16px -16px 16px">
          工单重启后，将默认保持该工单原始受理流转不变。如受理人类型/受理人/完结权限等。
        </Alert>
        <Form ref="formsRef" :model="RestartFrom" :rules="rules" :label-width="100">
          <FormItem label="处理时效:">
            <template #label>
              <span>处理时效</span>
              <Tooltip content="重启工单支持填写新的处理时效，未填写则默认原始工单处理时效不变" max-width="400" placement="bottom-start">
                <Icon type="iconfont icon-zhushi2" size="16" color="#8D959F" />
              </Tooltip>
              <span>:</span>
            </template>
            <InputNumber v-model="RestartFrom.restartHour" placeholder="请输入" :min="0" :formatter="formatValue" /><span
              style="margin: 0 12px">小时</span>
            <InputNumber v-model="RestartFrom.restartMin" placeholder="请输入" :min="0" :max="60"
              :formatter="RestartFrom.formatValue" /><span style="margin: 0 12px">分钟</span>
          </FormItem>
          <!-- 提交人自选 -->
          <template v-if="orderInfo.handlerType === 1">
            <FormItem label="受理类型" prop="type">
              <RadioGroup v-model="RestartFrom.type">
                <Radio :label="1">部门</Radio>
                <Radio :label="0" style="margin-left: 30px"> 个人 </Radio>
              </RadioGroup>
            </FormItem>
            <FormItem label="受理对象" prop="handler">
              <Select v-model="RestartFrom.handler" filterable placeholder="请选择受理对象" label-in-value>
                <OptionGroup v-for="i in  (RestartFrom.type ? store.getters.sortByavailableTeam : store.getters.sortByTeamUsers) "
                  :label="(RestartFrom.type ? i.name : (i.depName ? i.depName : i.orgName) + '_' + (i.teamName ? i.teamName + '_' : '') + (i.isBankAgent ? '委外' : '内催'))">
                  <Option v-for="item in i.children" :value="item.id" :label="item.name" :key="item.id">{{ item.name }}
                  </Option>
                </OptionGroup>
              </Select>
            </FormItem>
          </template>
          <FormItem label="备注:">
            <Input v-model.trim="RestartFrom.content" type="textarea" placeholder="请输入" :maxlength="200" show-word-limit
              :rows="2" :autosize="{ minRows: 3, maxRows: 9 }" />
          </FormItem>
        </Form>
      </template>
      <!-- 关联工单 -->
      <div v-if="modalType === 'releOrder'" class="rele-order">
        <Alert type="warning" show-icon class="warning no-border-radius no-border" style="margin: -16px -16px 16px">
          被关联工单仅支持查看工单信息，不支持对工单进行处理。
        </Alert>
        <div id="releOrderForm" class='rele-order-form'>
          <Form  :rules="rules" :label-width="80" @submit.native.prevent>
          <div v-for="(i, index) in releOrderCodeFrom" :key="index" class="div_scroll" :class="{'div_input':releOrderCodeFrom.length > 1}">
            <FormItem label="工单编号:" prop="orderCode" :rules="{
              required: true,
              type: 'number',
              trigger: 'change',
              validator: (rule, value) => {
                return validators(rule, value, i.code, index);
              },
            }">
              <Input class="input" v-model.trim="i.code" placeholder="请输入工单编号" />
            </FormItem>
            <Icon v-if="releOrderCodeFrom.length > 1" type="iconfont icon-shanchu" class="shanchu"
              @click="handleRemove(index)" />
          </div>
        </Form>
        </div>
        <span class="add-rele" @click="addRele(true)">
          <Icon type="iconfont icon-tianjia" size="16"></Icon>
          添加关联
        </span>
      </div>
      <div v-if="modalType !== ''" class="modal-btn">
        <Button style="margin-right: 8px" @click="handleCancel">取消</Button>
        <Button type="primary" :loading='completedLoading' @click="handleCompleted(modalType)">确定</Button>
      </div>
    </Modal>
    <recordDetail
      ref="recordDetailRef"
      :call-log-list="callLogList"
      :call-log-index="callLogIndex"
      :uuid="callUUid"
      :call-log-detail="callLogDetail"
      isNoShowOrderBtn
    />
  </div>
</template>

<script setup>
import Tables from '_c/tables/tables.1'
import recordDetail from '../../dy-inspect/components/recordDetail.vue'
import { moneyli2yuan } from '@/libs/util'
import { getImgInfo, dataURItoBlob, getAssetsFile } from '@/libs/util'
import {
  changeUrgencyApi, takeOrder, changeHandler, orderFlow, submitHandleResult, delOrderFile, orderEnd, orderRestart, getCasesInfoByOutSerialNo,
  releCase,
  orderLink,
  sendDataearchOrderNo
} from '@/api/workOrder'
import { getCaseAccountAge } from '@/api/case'
import { computed, nextTick, onMounted, ref,defineExpose,resolveComponent  } from "vue"
import { useStore } from 'vuex'
import { Message } from 'view-ui-plus'
import { useI18n } from 'vue-i18n'
import { getReduceDetailByUuid } from '@/api/refund'

const store = useStore()
const { t } = useI18n()
const props=defineProps({
    orderInfo: {
      type: Object,
      default: () => {
        return {}
      },
    },
    // 'list' 工单列表 'my' 我的工单进来
    fromFlag: {
      type: String,
      default: ''
    },
    type: {
      type: Number,
      default: 0
    },
    isInspection: {
    type: Boolean,
    default: false
    }
  }) 
const emit =defineEmits(['refreshOrderDetail','handleBack'])
const recordDetailRef = ref(null)
const callLogList = ref([])
const callUUid = ref(null)
const callLogIndex = ref(0)
const callLogDetail = ref({})
const recordColumns = ref([
  {
    title: t('k_createdTime'),
    key: 'createTime',
    type: 'date',
    bindmap: 'formatDate'
  },
  {
    title: '呼叫类型',
    key: 'callStyle',
    render: (h, { row }) => {
      return h('span', row.callStyle === 0 ? '呼入' : '呼出')
    }
  },
  {
    title: t('k_callResult'),
    key: 'callTypeName'
  },
  {
    title: t('k_ringDuration'),
    key: 'ringDurtion',
    bindmap: 'time'
  },
  {
    title: t('k_callDuration'),
    key: 'callDurtion',
    bindmap: 'time'
  },
  {
    title: t('k_mainPhone'),
    width: 150,
    key: 'caller',
    desenKey: 'mobile'
  },
  { title: t('k_agent'), key: 'operatorName', type: 'input' },
  { title: t('k_affiliatedGroup'), key: 'teamName' },
  { title: '提交人', key: 'adminSubmitterName' },
  { title: t('k_qcRemark'), key: 'comment', tooltip: true },
  { title: t('k_qcDetail'),
    key: 'qcDetail',
    showColumn: !props.isInspection,
    render: (h, params) => {
      const see = h(resolveComponent('Button'), {
        type: 'text',
        onClick: () => {
          callUUid.value = params.row.callUuid
          callLogIndex.value = params.index
          callLogDetail.value = params.row
          recordDetailRef.value.ui.showDrawer = true
        }
      }, {
        default: () => '查看'
      })
      return h('div', {}, [see])
    }
  },
  { title: t('k_delt'), key: 'deltName' },
  { title: t('k_deltProduct'), key: 'productName' }
])
const recordInjectSearchData = ref({})
const recordApiFun = ref({
  get: {
    func: getReduceDetailByUuid,
    callback: res => {
      if (res.data) {
        callLogList.value = [res.data]
      }
    }
  },
  filterGetList: (data)=>{
    if(data){
      data['list'] = [data]
      return {list : [data]}
     }
     else {
      return  {list : []}
    }
  }

})
const formsRef = ref(null)
const orderInfoTab=ref('nowOrderTab')
const ui=ref({
        caseLoading: false,
        searchedFlag: false,
        handleOrderEndLoading: false,
        handlehandleChangeLoading: false,
        handleOrderFlowLoading: false,
        SubmitHandleResultLoading: false,
        handleOrderRestartLoading: false,
        handletakeOrderLoading: false,
      })
    const  releCaseId=ref('')// 关联案件
    const  caseError=ref(false)
    const  caseInfo=ref([])
    const  recordList=ref([])
    const  operType=ref([
        "催单",
        "接单",
        "提交处理结果",
        "流入下个节点",
        "转交",
        "修改紧急程度",
        "完结",
        "重启工单",
      ])
      // status 0自动流转 1提交人自选
    const orderDetail=ref({ status: 1 })
    const completedContent=ref('') // 完结备注
    const completedShow=ref(false)
    const completedLoading=ref(false)
    const modalType=ref('')
      // 转交
    const passToForms=ref({
        type: 1,
        name: "",
        handler: "",
        remark: "",
      })
      // 处理结果
    const dealResult=ref({
        content: "",
        file: [],
      }) 
      // 重启工单
    const RestartFrom=ref({
        restartHour: null,
        restartMin: null,
        content: "",
        type: 1,
        handler: "",
      })
    const files=ref([])
    const  releOrderCodeFrom=ref([{ code: "", ispass: false }])
    const  uploadLoading=ref(false)
    const  rules=ref({
        name: [
          { required: true, message: "字段名称不能为空", trigger: "blur" },
        ],
        type: [
          {
            required: true,
            message: "转交类型不能为空",
            trigger: "change",
            type: "number",
          },
        ],
        handler: [
          {
            required: true,
            type: "number",
            message: "请选择受理对象",
            trigger: "change",
          },
        ],
        orderCode: [
          {
            required: true,
            message: "工单编号不能为空",
            trigger: "change",
            type: "number",
          },
        ],
      })
      // 紧急程度
    const  urgencyStatus=ref(0)
    const  timer=ref('')
    const  tabName=ref('name1')
    const  caseInfoColNum=ref([])
    const  baseOrderInfo=ref([])
    const  fieldListInfo=ref([])
    const  orderStatusColors=[
        { value: 0, label: "待接单", icon: 'icon-daijiedan', color: '#B0B7C2' },
        { value: 1, label: "受理中", icon: 'icon-shengyushixiao', color: '#FFB74D' },
        { value: 2, label: "已完结", icon: 'icon-wancheng', color: '#17CB70' },
      ]
    const  urgencyStatusList=ref([
        { value: 0, text: "低", color: "#00B6CB" },
        { value: 1, text: "一般", color: "#F9D135" },
        { value: 2, text: "紧急", color: "#FC904A" },
        { value: 3, text: "非常紧急", color: "#F71E49" },
      ])
    const  OutSerialNo=ref('')
    const  caseBelong=ref([])
    const  beforefileindex=ref(-1)

    // 完结按钮
   const isHasOrderEndBtn=computed(()=>{
      if (props.orderInfo.status === 2) {
        return false
      }
      if (props.orderInfo.status === 0) {
        if (props.orderInfo.createBy === store.getters.userId) {
          return true
        } else if (props.orderInfo.handlerType && props.orderInfo.handlerUser === store.getters.userId) {
          return true
        } else {
          return false
        }
      }
      if (props.orderInfo.status === 1) {
        if (props.orderInfo.handlerType && (props.orderInfo.handlerUser === store.getters.userId || props.orderInfo.createBy === store.getters.userId)) {
          return true
        }
        if (!props.orderInfo.endType) {
          // 0 是创建人和末级节点
          if (props.orderInfo.createBy === store.getters.userId || (props.orderInfo.level === props.orderInfo.nodeCount && props.orderInfo.handlerUser === store.getters.userId)) {
            return true
          } else {
            return false
          }
        } else {
          // 1 是各级节点
          if (props.orderInfo.createBy === store.getters.userId || props.orderInfo.handlerUser === store.getters.userId) {
            return true
          } else {
            return false
          }
        }
      } else {
        return false
      }
    })
    // 转交按钮权限
  const isHasChangeBtn=computed(()=>{
      if (props.orderInfo.status === 1 && props.orderInfo.handlerUser === store.getters.userId) {
        return true
      } else {
        return false
      }

    })
    // 流入下一节点
   const isHasOrderFlowBtn=computed(()=>{
      // if(props.type === 3 && [1,0].includes(props.orderInfo.status)){
      //   return false
      // }
      if (props.orderInfo.status === 1) {
        if (!props.orderInfo.handlerType && props.orderInfo.handlerUser === store.getters.userId) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }

    })
    // 提交处理结构
    const isHasSubmitHandleResultBtn=computed(()=>{
      if (props.orderInfo.status === 1) {
        return true
      } else if (props.type === 3 && props.orderInfo.status === 0) {
        return true
      } else {
        return false
      }
    })
    // 重新开启
    const isHasOrderRestartBtn=computed(()=>{
      if (props.orderInfo.status === 2 && props.orderInfo.createBy === store.getters.userId) {
        return true
      } else {
        return false
      }
    })
    // 接单
const isHasTakeOrderBtn=computed(()=>{
      if (props.orderInfo.status === 0 && props.type !== 0) {
        if (props.type === 4) {
          return true
        } else if (store.getters.teamId === props.orderInfo.handlerTeam) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    })
onMounted(()=>{
    store.dispatch('getRefData', {
      func: ['getAvailableAgent', 'getAvailableDepAndTeam']
    })
    baseOrderInfo.value = [{ key: 'orderNo', name: '工单编号' }, { key: 'typeName', name: '工单类型' },
    { key: 'templateName', name: '工单模版' }, { key: 'userName', name: '发起人' },
    { key: 'createTime', name: '发起时间' }, { key: 'status', name: '工单状态' },
    { key: 'handlerType', name: '受理人类型' }, { key: 'handlerDepName', name: '受理机构' },
    { key: 'handlerTeamName', name: '受理部门' }, { key: 'handlerUserName', name: '受理人' }].map((i) => {
      if (i.key === 'handlerType') {
        if (!props.orderInfo[i.key]) {
          i['value'] = '自动流转'
        } else {
          i['value'] = '提交人自选'
        }
      } else {
        i['value'] = props.orderInfo[i.key]
      }
      return i
    })
    let extList = []

    for (let i of props.orderInfo.fieldList) {
      if (!['type', 'templateId', 'title', 'description'].includes(i.field)) {
        extList.push({
          key: i.field,
          name: i.name
        })
      }
    }
    fieldListInfo.value = extList.map((item, index) => {
      if (item.key === 'handleDuration') {
        item['value'] = formatDate(props.orderInfo[item.key] * 60)
      } else {
        if (item.key === 'file') {
          beforefileindex.value = index - 1
        }
        item['value'] = props.orderInfo[item.key]
      }
      return item
    })
    recordList.value = props.orderInfo.recordList
    // 紧急·程度
    urgencyStatus.value = props.orderInfo['urgency']
    if (props.orderInfo.caseInfo) {
      caseInfo.value = [
        { key: 'outSerialTemp', name: '案件编号' },
        { key: 'name', name: '客户姓名' },
        { key: 'idCard', name: '身份证号' },
        { key: 'mobile', name: '手机号' },
        { key: 'amount', name: '贷款金额' },
        { key: 'productName', name: '产品名称' },
        { key: 'accountAge', name: '案件账龄' },
        { key: 'overdueDays', name: '逾期天数' }].map((i) => {
          if (i.key === 'accountAge') {
            i.value = !store.getters.userExtend.enableAutoUpdateOverdueDays
              ? getCaseAccountAge(props.orderInfo.caseInfo.overdueDays)
              : getCaseAccountAge(
                Math.ceil(
                  (new Date().getTime() - props.orderInfo.caseInfo.overdueDate) /
                  (24 * 3600 * 1000) -
                  1
                ) >= 0
                  ? Math.ceil(
                    (new Date().getTime() - props.orderInfo.caseInfo.overdueDate) /
                    (24 * 3600 * 1000) -
                    1
                  )
                  : 0
              )
          } else if (i.key === 'overdueDays') {
            i.value = !store.getters.userExtend.enableAutoUpdateOverdueDays ? props.orderInfo.caseInfo.overdueDays : (Math.ceil(((new Date()).getTime() - props.orderInfo.caseInfo.overdueDate) / (24 * 3600 * 1000) - 1) >= 0 ? Math.ceil(((new Date()).getTime() - props.orderInfo.caseInfo.overdueDate) / (24 * 3600 * 1000) - 1) : '')
          } else if (i.key === 'amount') {
            i.value = moneyli2yuanFn(props.orderInfo.caseInfo[i.key]) + t("yuan")
          } else if (i.key === 'mobile') {
            i.value = props.orderInfo.caseInfo.fieldJson.own_mobile
          } else {
            i.value = props.orderInfo.caseInfo[i.key]
          }
          return i
        })
    }
    if(props.orderInfo.callUuid){
      // 有uuid表示是质检
      recordInjectSearchData.value = {
        callUuid :props.orderInfo.callUuid
      }
    }
  })

 const formatDate=(second)=>{
      let leftHandleDuration = ''
      const minutes = Math.floor(((second % 86400) % 3600) / 60)
      const seconds = Math.floor(((second % 86400) % 3600) % 60)
      leftHandleDuration = ''
      if (Math.floor(second / 86400) > 0) {
        leftHandleDuration += Math.floor(second / 86400) + '天'
      }
      if (Math.floor((second % 86400) / 3600) > 0) {
        leftHandleDuration += Math.floor((second % 86400) / 3600) + '小时'
      }
      if (minutes > 0) {
        leftHandleDuration += (minutes > 9 ? minutes : ('0' + minutes)) + '分钟'
      }
      return leftHandleDuration
    }
   const handleCancel=()=>{
      completedShow.value = false
      modalType.value = ''
      completedLoading.value = false
      releOrderCodeFrom.value = [{ code: "", ispass: false }]
      // 转交
      passToForms.value = {
        type: 1,
        name: "",
        handler: "",
        remark: "",
      },
        // 处理结果
        dealResult.value = {
          content: "",
          file: [],
        },
        // 重启工单
        RestartFrom.value = {
          restartHour: null,
          restartMin: null,
          content: "",
          type: 1,
          handler: "",
        },
        // 文件
        files.value = []
        // 紧急程度
        urgencyStatus.value = props.orderInfo['urgency']
    }
    // 完结备注
   const handleCompleted=(type)=>{
      if (type === 'releOrder') {
        const flag = releOrderCodeFrom.value.every((item) => {
          return item.ispass
        })
        if (flag) {
          let orderNos = []
          for (let i of releOrderCodeFrom.value) {
            orderNos.push(i.code)
          }
          orderLink({ id: props.orderInfo.id, orderNos }).then(() => {
            Message.success("关联成功");
            emit('refreshOrderDetail')
          }).finally(() => {
            handleCancel()
          })
        } else {
          return false
        }

      } else {
        formsRef.value.validate((valid) => {
          if (valid) {
            completedLoading.value = true
            if (type === 'editurgency') {
              const sendData = {
                id: props.orderInfo.id,
                urgency: urgencyStatus.value
              }
              changeUrgencyApi(sendData).then((res) => {
                if (res.success) {
                  completedShow.value = false
                  emit('refreshOrderDetail')
                }
              }).finally(() => {
                handleCancel()
              })
            } else if (type === 'passto') {
              const sendData = {
                content: passToForms.value.remark,
                orderId: props.orderInfo.id
              }
              if (passToForms.value.type) {
                sendData.handlerTeam = passToForms.value.handler
              } else {
                sendData.handlerUser = passToForms.value.handler
              }
              changeHandler(sendData).then((res) => {
                if (res.success) {
                  Message.success("转交成功");
                  emit('handleBack')
                }
              }).finally(() => {
                handleCancel()
              })
            } else if (type === 'dealresult') {
              const sendData = {
                content: dealResult.value.content,
                orderId: props.orderInfo.id
              }
              const formData = new FormData()
              for (const key in sendData) {
                formData.append(key, sendData[key])

              }
              if (files.value.length) {
                for (let item of dealResult.value.file) {
                  formData.append('files', item)
                }
              }
              submitHandleResult(formData).then((res) => {
                if (res.success) {
                  Message.success("成功提交处理结果");
                  emit('refreshOrderDetail')
                }
              }).finally(() => {
                handleCancel()
              })
            } else if (type === 'completedContent') {
              const sendData = {
                content: completedContent.value,
                orderId: props.orderInfo.id
              }
              orderEnd(sendData).then((res) => {
                if (res.success) {
                  Message.success("已完结");
                  emit('refreshOrderDetail')
                }
              }).finally(() => {
                handleCancel()
              })
            } else if (type === 'Restart') {
              const sendData = {
                content: RestartFrom.value.content,
                orderId: props.orderInfo.id
              }
              // 如果处理时效为0 不传
              if (RestartFrom.value.restartHour > 0 || RestartFrom.value.restartMin > 0) {
                sendData.handleDuration = RestartFrom.value.restartHour * 60 + RestartFrom.value.restartMin
              }
              if (RestartFrom.value.type) {
                sendData.handlerTeam = RestartFrom.value.handler
              } else {
                sendData.handlerUser = RestartFrom.value.handler
              }
              orderRestart(sendData).then((res) => {
                if (res.success) {
                  Message.success("重新开启此工单");
                  emit('refreshOrderDetail')
                }
              }).finally(() => {
                handleCancel()
              })
            }
          }

        })
      }


    }
    const beforeUploadAssistFile=async(file, _callback)=>{
      //let size = 0;
      for (const key in files.value) {
        if (file.name === files.value[key]) {
          Message.error("文件名重复，请更改文件名后上传");
          return;
        }
        //size = size + dealResult.value.file[key].size;
      }

      // size = size + file.size;

      if (file.size > 10 * 1024 * 1024) {
        Message.error('上传文件大小不能超过10M')
        return
      }
      const fileType = file.type;

      if (fileType.indexOf("image") !== -1) {
        const img = await getImgInfo(file);
        if (img.w > 1500) {
          var image = document.createElement("img");
          image.src = img.pic;
          var canvas = document.createElement("canvas");
          var imgWidth = img.w;
          var imgHeight = img.h;
          var fullWidth = imgWidth;
          var fullHeight = imgHeight;
          if (fullWidth > 1500) {
            var rate = fullHeight / fullWidth;
            var originWidth = fullWidth;
            fullWidth = 1500;
            fullHeight = 1500 * rate;
            rate = fullWidth / originWidth;
            imgWidth = imgWidth * rate;
            imgHeight = imgHeight * rate;
          }
          canvas.setAttribute("width", fullWidth);
          canvas.setAttribute("height", fullHeight);
          var g = canvas.getContext("2d");
          g.fillStyle = "#fff";
          g.fillRect(0, 0, canvas.width, canvas.height);
          g.translate(fullWidth / 2, fullHeight / 2);
          g.drawImage(
            image,
            -imgWidth / 2,
            -imgHeight / 2,
            imgWidth,
            imgHeight
          );
          dealResult.value.file.push(
            new File(
              [dataURItoBlob(canvas.toDataURL("image/jpeg"))],
              file.name,
              { type: "image/jpeg", lastModified: Date.now() }
            )
          );
        } else {
          dealResult.value.file.push(file);
        }
      } else {
        dealResult.value.file.push(file);
      }
      files.value.push(file.name);
      await Promise.reject(new Error(false));
      _callback && _callback();
    }
   const deleteFile=(index, flag = false, nodeIndex,item)=>{
      if (flag) {
        delOrderFile(item.id).then((res) => {
          if (res.success) {
            // emit('refreshOrderDetail')
            recordList.value[nodeIndex].fileList.splice(index, 1)
          }
        })
      } else {
        dealResult.value.file.splice(index, 1);
        files.value.splice(index, 1);
      }

    }

   const downloads=(url)=>{
      const urls = url.replace('http', 'https')
      window.open(urls)
    }
   const formatValue=(value)=>{
      if (Number.isInteger(value)) {
        return value;
      } else {
        const str = String(value);
        const arr = str.split(".");
        if (arr.length > 1 && arr[1].length > 2) {
          const num = arr[1].slice(0, 2);
          return [arr[0], Number(num)].join(".");
        } else {
          return value;
        }
      }
    }
   const debounce=(fun, delay)=>{
      return (args) => {
        clearTimeout(timer.value);
        timer.value = setTimeout(function () {
          fun(args);
        }, delay);
      };
    }
   const validators=(rule, value, code, index)=>{
      return new Promise((resolve, reject) => {
        if (code) {
          const ajax = (code) => {
            const index1 = releOrderCodeFrom.value.findIndex((i) => {
              return i.code === code
            })
            if (index > -1 && index1 !== index) {
              reject("工单编号重复");
            } else if (code === props.orderInfo.orderNo) {
              reject("请勿关联当前工单");
              releOrderCodeFrom.value[index]['ispass'] = true
            } else {
              sendDataearchOrderNo(code).then((res) => {
                if (res.success) {
                  if (res.data) {
                    releOrderCodeFrom.value[index]['ispass'] = true
                    resolve();
                  } else {
                    releOrderCodeFrom.value[index]['ispass'] = false
                    reject("工单编号不存在");
                  }
                }
              })
            }
          };
          debounce(ajax, 1000)(code);
        } else {
          releOrderCodeFrom.value[index]['ispass'] = false
          reject("请输入工单编号");
        }
      })
    }
   const addRele=()=>{
      releOrderCodeFrom.value.push({ code: "", ispass: false });
      nextTick(()=>{
        let scrollElem = document.getElementById('releOrderForm')
        scrollElem.scrollTo({ top: scrollElem.scrollHeight, behavior: 'smooth' })
      })
    }
   const handleRemove=(index)=>{
      releOrderCodeFrom.value.splice(index, 1);
    }
  const handleReleCase = () => {
      releCase({
        caseId: OutSerialNo.value,
        id: props.orderInfo.id
      }).then((res) => {
        if (res.success) {
          Message.success("关联成功");
          OutSerialNo.value = ''
          emit('refreshOrderDetail', true, caseInfo.value)
          // nextTick(() => {
          //   // setTimeout(() => {
          //   orderInfoTab.value = 'releOrderTab'
          //   // },1000)
          // })
        }

      })
    }
   const clearReleCaseId = ()=>{
      caseInfo.value = []
      caseBelong.value = []
    }
   const searchCase=()=>{
      //API-12fc561c
      // 2983UR2401316002225226
      // if (OutSerialNo.value) {
      //   caseInfo.value = []
      //   OutSerialNo.value = ''
      // } else {
      ui.value.caseLoading = true
      getCasesInfoByOutSerialNo(releCaseId.value).then((res) => {
        if (res.success) {
          if (!res.data) {
            caseError.value = true
            caseInfo.value = []
            OutSerialNo.value = ''

          } else {
            caseInfo.value = [
              { key: 'outSerialTemp', name: '案件编号' },
              { key: 'name', name: '客户姓名' },
              { key: 'idCard', name: '身份证号' },
              { key: 'mobile', name: '手机号' },
              { key: 'amount', name: '贷款金额' },
              { key: 'productName', name: '产品名称' },
              { key: 'accountAge', name: '案件账龄' },
              { key: 'overdueDays', name: '逾期天数' }].map((i) => {
                if (i.key === 'accountAge') {
                  i.value = !store.getters.userExtend.enableAutoUpdateOverdueDays
                    ? getCaseAccountAge(res.data.overdueDays)
                    : getCaseAccountAge(
                      Math.ceil(
                        (new Date().getTime() - res.data.overdueDate) /
                        (24 * 3600 * 1000) -
                        1
                      ) >= 0
                        ? Math.ceil(
                          (new Date().getTime() - res.data.overdueDate) /
                          (24 * 3600 * 1000) -
                          1
                        )
                        : 0
                    )
                } else if (i.key === 'overdueDays') {
                  i.value = !store.getters.userExtend.enableAutoUpdateOverdueDays ? res.data.overdueDays : (Math.ceil(((new Date()).getTime() - res.data.overdueDate) / (24 * 3600 * 1000) - 1) >= 0 ? Math.ceil(((new Date()).getTime() - res.data.overdueDate) / (24 * 3600 * 1000) - 1) : '')
                } else if (i.key === 'amount') {
                  i.value = moneyli2yuanFn(res.data[i.key]) + t("yuan")
                } else if (i.key === 'mobile') {
                  i.value = res.data.fieldJson.own_mobile
                } else {
                  i.value = res.data[i.key]
                }
                return i
              })
            caseBelong.value = []
            if (res.data.allotAgentState) {
              for (let j of ['depName', 'teamName', 'userName']) {
                if (res.data[j]) {
                  caseBelong.value.push(res.data[j])
                }
              }
            }
            caseError.value = false
            OutSerialNo.value = res.data.id
          }
        }
      }).finally(() => {
        ui.value.caseLoading = false
      })
    }
  const  moneyli2yuanFn=(money)=>{
      return moneyli2yuan(money)
    }
    // 添加关联工单
   const addreleOrder=()=>{
      modalType.value = 'releOrder'
      completedShow.value = true
    }
    // 修改紧急程度
   const changeUrgency=()=>{
      modalType.value = 'editurgency'
      completedShow.value = true
    }
    // 接单
  const handletakeOrder=()=>{
      ui.value.handletakeOrderLoading = true
      takeOrder(props.orderInfo.id).then((res) => {
        if (res.success) {
          Message.success("接单成功");
          emit('refreshOrderDetail')
        }
      }).finally(() => {
        ui.value.handletakeOrderLoading = false
      })
    }
    // 重启
  const handleOrderRestart=()=>{
      modalType.value = 'Restart'
      completedShow.value = true
    }
    // 转交
   const handleChange=()=>{
      modalType.value = 'passto'
      completedShow.value = true
    }
    // 提交处理结果
   const handleSubmitHandleResult=()=>{
      modalType.value = 'dealresult'
      completedShow.value = true
    }
    // 流向下一个节点
   const handleOrderFlow=()=>{
      ui.value.handleOrderFlowLoading = true
      orderFlow(props.orderInfo.id).then((res) => {
        if (res.success) {
          Message.success("操作成功，工单自动流转至下一个节点");
          emit('handleBack')
        }
      }).finally(() => {
        ui.value.handleOrderFlowLoading = false
      })
    }
    // 完结
   const handleOrderEnd=()=>{
      modalType.value = 'completedContent'
      completedShow.value = true
    }
defineExpose({OutSerialNo})
</script>

<style scoped lang="less">
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  //background-color: #8c8c8c;
  background-color: rgba(0, 0, 0, 0.25);
}

::-webkit-scrollbar-track {
  background-color: #f6f6f6;
}

::-webkit-scrollbar-thumb,
::-webkit-scrollbar-track {
  border: 0;
}

.detail-content {
  height: 100%;

  .header-container {
    display: flex;
    margin-bottom: 12px;
    justify-content: space-between;

    .header {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      >img {
        width: 54px;
        height: 14px;
        margin-right: 4px;
      }

      >.title {
        margin-right: 10px;
        font-size: 16px;
        font-weight: 500;
        color: #282828;
        line-height: 22px;
      }

      >.status {
        display: inline-block;
        text-align: center;
        width: 66px;
        line-height: 22px;
        height: 22px;
        color: #ffb74d;
        line-height: 22px;
        background: rgba(255, 237, 210, 0.6);
        border-radius: 11px;

        >span {
          margin-left: 2px;
        }
      }
    }
  }

  .header-btn {
    padding-left: 10px;

    display: flex;
    justify-content: flex-end;

    >button {
      margin-left: 8px;
    }
  }

}

.openModal {
.max_height{
    max-height: 517px;
    /* overflow: auto; */
    overflow-y: auto;
    margin-right: -16px;
    padding-right: 16px;

}
  .rele-order-form{
    max-height: 480px;
    /* overflow: auto; */
    overflow-y: auto;
    margin-right: -16px;
    .div_scroll{
      margin-right: 16px;
      &.div_input{
        display: flex;
      }
      .input{
        min-width: 380px;
      }
    }
    :deep(.ivu-form-item-label){
      width: 74px !important;
    }
  }

}

.main-content {
  height: calc(~"100% - 40px");
  // background-color: aqua;
  display: flex;
  margin-right: -14px;
  overflow-y: auto;

  .left-content {
    height: 100%;
    flex: 1;
    min-width: 680px;
    // background-color: pink;
    margin-right: 12px;



    .order-desc {
      padding: 12px 16px 16px 16px;
      // // height: 197px;
      // margin-top: 16px;
      margin-bottom: 10px;
      width: 100%;
      overflow-y: auto;
      box-shadow: 0px 6px 9px 0px rgba(137, 142, 155, 0.12);
      border-radius: 4px;
      border: 1px solid #dcdee2;
      position: relative;

      .desc-header {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 12px;
        color: #636c78;

        >span {
          font-weight: 600;
        }
      }

      .desc-noContent {
        // font-size: 22px;
        display: flex;
        justify-content: center;
        align-items: center;

        span {
          margin-left: 6px;
          font-size: 12px;
          color: #8D959F;
          line-height: 22px;
        }
      }

      img {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 9;
        height: 66px;
      }
    }
  }

  .order-info-container {
    width: 100%;
    margin-top: 10px;
    padding-bottom: 16px;

    .tab2 {
      :deep(.ivu-tabs-bar) {
        margin-bottom: 12px;
        // padding: 4px 16px 12px;
      }
    }
  }

  .case-item-container {
    width: 100%;


    >.case-item {
      list-style: none;
      display: flex;
      flex-wrap: wrap;
      border-top: 1px solid #dcdee2;
      border-left: 1px solid #dcdee2;

      >li {
        width: 33.333%;
        display: flex;
        border-bottom: 1px solid #dcdee2;
        border-right: 1px solid #dcdee2;

        >.key {
          width: 98px;
          flex-shrink: 0;
          background: #f2f4f7;
          border-right: 1px solid #dcdee2;
          padding: 8px 12px;
          line-height: 17px;
          display: flex;
          align-items: center;
        }

        >.value {
          width: 100%;
          padding: 8px 12px;
          min-width: 122px;
          line-height: 17px;
          display: flex;
          align-items: center;
          word-wrap: break-word;
          word-break: break-all;

          .icon-text {
            cursor: pointer;
            margin-left: 6px;

            .icon {
              font-size: 16px;
              color: @primary-color;
              cursor: pointer;
              line-height: 17px;
              margin-right: 2px;
            }
          }


          >span {
            color: @primary-color;
          }

          &:hover {
            background-color: #fff7f5;
          }

          .file-list {
            display: flex;
            flex-wrap: wrap;
            list-style: none;
            align-items: center;

            >li {
              padding: 8px 12px;
              // height: 32px;
              background: #f2f4f7;
              border-radius: 4px;
              margin-right: 12px;
              margin-bottom: 10px;
              display: flex;

              &:hover {
                color: #ff4f1f;

                .icons {
                  color: #ff4f1f;
                }
              }

              .file-name {
                text-decoration: underline;
                cursor: pointer;
              }

              .icons {
                color: #636C78;
                margin-right: 4px;
              }
            }
          }
        }

        &.beforefileindex {
          flex: 1;
        }

        &.field {
          width: 100% !important;

          .value {
            padding: 8px 0 0 12px;

            &:hover {
              background-color: unset;
            }
          }
        }

        .values {
          // width: 100%;
          max-height: 60px;
          padding: 8px 12px;
          line-height: 17px;
          text-overflow: ellipsis;
          white-space: normal;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          word-wrap: break-word;
          word-break: break-all;

          &:hover {
            background-color: #fff7f5;
          }
        }

        &:last-child {
          flex: 1;
          border-right: 1px solid #dcdee2;

          &.field {
            flex: none;
          }
        }

        &:nth-child(3n) {
          border-right: 1px solid #dcdee2;
        }
      }
    }
  }

  .rele-case {
    .case-input {
      width: 200px;
      height: 32px;

      :deep(.ivu-input) {
        border-radius: 4px 0px 0px 4px;
        border-right: none;
      }
    }

    .search-btn {
      border-radius: 0px 4px 4px 0px;
    }

    .rele-case-btn {
      margin-top: 32px;
      display: flex;
      justify-content: center;
    }

    .rele-case-empty {
      margin-top: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      p {
        color: #8D959F;
        margin-top: 16px;
      }

      >img {
        height: 90px;
      }
    }
  }

  .right-content {
    height: 100%;
    width: 348px;
    // overflow-y: auto;

    // background-color: #F26262;
    margin-right: 16px;

    .opera-history {
      padding-left: 3px;
      // margin-top: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .desc-header {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        color: #636c78;

        >span {
          font-weight: 600;
        }
      }
    }

    .opera-history-node {
      padding-left: 10px;
      padding-bottom: 32px;

      // max-height: 725px;
      // overflow-y: auto;
    }

    .nodelist {
      list-style: none;
      padding-top: 20px;

      >li {
        padding: 0 0 32px 11px;
        position: relative;

        &:last-child {
          padding: 0 0 0 11px;

          &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 2%;
            bottom: auto;
            right: auto;
            height: 0;
            width: 0;
            background-color: #accffa;
          }
        }

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: 10px;
          bottom: auto;
          right: auto;
          height: 99%;
          width: 1px;
          background-color: #accffa;
        }

        position: relative;

        >.dot {
          position: absolute;
          top: 6px;
          left: -4px;
          width: 8px;
          height: 8px;
          background: #3488f3;
          border-radius: 50%;
        }

        >.title {
          display: flex;
          line-height: 20px;
          align-items: center;

          .point {
            margin: 0 4px;
            font-weight: 900;
          }

          .time {
            margin-left: 16px;
            font-weight: 400;
            color: #8d959f;
          }
        }

        >.node-info {
          list-style: none;
          background: #f2f4f7;
          margin-top: 8px;
          padding: 0 12px 8px 12px;
          width: 328px;

          >div {
            display: flex;
            padding-top: 8px;

            .info-files {
              flex: 4;

              >span {
                background-color: #f2f4f7;
                border-radius: 4px;
                display: inline-block;
                line-height: 17px;
                margin-right: 10px;
                margin-bottom: 10px;

                >label {
                  &:hover {
                    color: #ff4f1f;
                    cursor: pointer;

                    .wenjian {
                      color: #ff4f1f !important;
                    }
                  }

                  span {
                    text-decoration: underline;
                    word-break: break-all;

                  }
                }


                .dele-icon {
                  margin-left: 4px;
                  cursor: pointer;
                  color: #636C78;

                  &:hover {
                    color: #828EA0 !important;
                  }

                  // style="margin-left: 4px; cursor: pointer"
                }
              }
            }

            .info {
              text-align: right;
              color: #636c78;
              text-wrap: nowrap;
              line-height: 20px;
            }

            .text {
              text-align: left;
              // flex: 4;
              line-height: 20px;
              // max-width: 250px;
              // word-wrap: break-word;
              text-wrap: wrap;
              flex-grow: 1;
              letter-spacing: 0;
              // white-space: break-spaces;
              flex: 1;
              word-break: break-all;
            }
          }
        }
      }
    }

    .recordList-empty {
      margin-top: 140px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      img {
        height: 90px;
      }

      p {
        color: #8D959F;
        line-height: 22px;
      }
    }
  }
}

.tips {
  color: #636c78;
  margin-top: 4px;
}

.shanchu {
  cursor: pointer;
  color: #636c78;
  font-size: 16px;
  line-height: 32px;
  margin-left: 10px;

  &:hover {
    color: #ff4f1f;
  }
}

.files {
  margin-top: 6px;

  >span {
    background-color: #f2f4f7;
    border-radius: 4px;
    display: inline-block;
    padding: 8px 12px;
    line-height: 17px;
    margin-right: 10px;
    margin-bottom: 10px;

    >label {
      span {
        word-break: break-all;

      }
    }
  }
}

.add-rele {
  color: #ff4f1f;
  cursor: pointer;
}

.radio-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  >.radio {
    width: 100px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    margin-right: 16px;
    cursor: pointer;
    position: relative;
    margin-top: 10px;

    &:last-child {
      margin-right: 0;
    }

    &:hover {
      >span {
        color: #ff6700;
      }
    }

    >i {
      font-size: 16px;
      color: #f2cb51;
      margin-right: 6px;
    }

    >span {
      color: #636c78;
    }

    >img {
      position: absolute;
      width: 20px;
      height: 20px;
      right: -1px;
      bottom: -1px;
      display: none;
    }
  }

  >.active-radio {
    border: 1px solid #ff6700;

    >i {
      color: #ff6700;
    }

    >span {
      color: #ff6700;
    }

    >img {
      display: block;
    }
  }
}

.modal-btn {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
}

.pz {
  display: flex;
  align-items: center;
  font-size: 14px;
  position: relative;

  &.pz1 {
    border-bottom: 1px solid #dcdee3;
    padding-bottom: 12px;
    padding-top: 8px;
  }

  .tab1 {
    flex: 1;

    :deep(.ivu-tabs-tab) {
      padding: 4px 16px 12px;
    }

    :deep(.ivu-tabs-ink-bar) {
      height: 3px;
    }
  }

  .version_tips {
    position: absolute;
    right: 0;
    bottom: 24px;
    width: 222px;
    height: 28px;
    background-color: #fde5e5;
    border-radius: 16px;
    line-height: 28px;
    color: #f26262;
    text-align: center;
    font-size: 12px;

    >span {
      margin-left: 2px;
    }
  }

  .arrowback {
    flex-shrink: 0;
    display: inline-block;
    margin: 0 0 9px 0;
    padding: 0 0 12px 0;
    font-weight: 600;
    cursor: pointer;
    white-space: nowrap;
    height: 44px;
    margin-bottom: 16px;
    border-bottom: 1px solid #DCDEE3;
    display: flex;
    align-items: center;
    margin-top: -8px;
    margin-left: -16px;
    margin-right: -16px;
    padding-left: 16px;

    >span {
      display: inline-block;
      max-width: 175px;
      line-height: 16px;
      //   border-right: 1px solid #DCDEE3;
      // padding-right: 12px;
      margin-right: 12px;
    }

    &:hover {
      >span {
        color: @primary-color;

        >i {
          color: @primary-color;
        }
      }
    }
  }

  .gz-txt {
    margin-left: 42px;
    cursor: pointer;

    >span {
      margin-right: 38px;
    }
  }

  .active {
    color: #ff4f1f;
    font-weight: 600;
    padding-bottom: 12px;
    border-bottom: 2px solid #ff4f1f;
  }
}

.editurgency {
  :deep(.ivu-form-item-label) {
    line-height: 30px;
  }
}</style>
