<template>
  <div class="progress">
    <Tables
      v-if="columns.length"
      ref="tables"
      :columns="columns"
      :apifun="apifun"
      :sub-table-height="80"
      pageable
      searchable
      has-sys-btns
    >
      <template #header>
        <div class="headers">
          <Alert class="alert ivu-alert-default">
            {{ $t('resultCount') }}： 联系人 <span>{{ count }}</span> 人
          </Alert>
        </div>
      </template>
      <template #btns>
        <div class="table-btns">
          <Checkbox
            v-model="ui.handleAll"
            @on-change="handleChange"
          >
            全部
          </Checkbox>
          <div class="divide"></div>
          <Button
            style="margin-left: 10px;"
            class="p-12"
            icon="iconfont icon-daoru"
            type="primary"
            @click="contactsImport"
          >
            联系人导入
          </Button>
          <Button
            style="margin-left: 8px;"
            class="p-12"
            icon="iconfont icon-xiugai"
            type="primary"
            ghost
            @click="editStatus"
          >
            批量修改状态
          </Button>
          <Button
            style="margin-left: 8px;"
            class="p-12"
            icon="iconfont icon-shanchu"
            type="primary"
            ghost
            @click="batchDelete"
          >
            批量删除
          </Button>
        </div>
      </template>
    </Tables>

    <contactsModal v-if="ui.showContactsModal" ref="contactsModal" :case-id="caseId" :editing-forms="contactForm" role="manage" entrance="contact" @on-close="closeModals('showContactsModal')" @on-submit-done="changeContacts"></contactsModal>

    <editStatus
      v-if="ui.showEditStatus"
      ref="editStatus"
      :ids="contactsIds"
      :all-select="ui.handleAll"
      :search-info="searchInfo"
      :count="count"
      @on-close="closeEditStatus"
      @on-success="handleSuccess"
    />
    <!-- 联系人导入弹层 -->
    <Modal
      v-model="ui.showModal"
      title="联系人导入"
      :closable="false"
      :footer-hide="true"
      :mask-closable="false"
    >
      <Alert class="alert no-border-radius no-border" type="warning" show-icon style="margin: -16px -16px 16px;">
        <p> 请严格按照导入模版中的内容填写并上传。模版中标红字段为必填字段，未标红为非必填，模版中的字段不可删除。</p>
      </Alert>
      <Form
        ref="updateFrom"
        :model="updateFrom"
        :rules="updateFromRole"
        :label-width="106"
        label-colon
      >
        <FormItem label="导入模板" prop="templateId">
          <Select
            v-model="updateFrom.templateId"
            style="width:310px;margin-right:12px"
            :placeholder="$t('selectTp')"
          >
            <Option
              v-for="tpl in tplList"
              :key="tpl.id"
              :value="tpl.id"
            >
              {{ tpl.name }}
            </Option>
          </Select>
          <Button
            type="primary"
            :disabled="!updateFrom.templateId"
            class="btnSty"
            @click="download()"
          >
            {{ $t('download') }}
          </Button>
        </FormItem>
        <!-- 上传模板数据 -->
        <FormItem label="上传模板数据" prop="productId">
          <Upload
            v-if="!updateFrom.productId"
            :max-size="5120"
            :show-upload-list="true"
            action=""
            :before-upload="handleBeforeUpload"
            accept=".xls, .xlsx"
            @on-exceeded-size="fileExceededSize"
          >
            <Button
              class="ivu-btn-grey"
              icon="iconfont icon-wenjian1"
              :loading="uploadLoading"
            >
              上传模板数据文件
            </Button>
            <p class="tips">上传文件大小需在20M以内</p>
          </Upload>
          <div v-else>
            <Icon type="iconfont icon-excel" size="22" color="#306f3d" />
            <span class="spanM">{{ updateFrom.productId.name }}</span>
            <Icon type="md-close" size="16" class="delBtn" @click.stop="updateFrom.productId = 0" />
          </div>
        </FormItem>
        <FormItem class="btns">
          <Button class="btnSty h-28" style="margin-right: 12px;" @click="clearModal">
            取消
          </Button>
          <Button
            type="primary"
            :loading="uploadLoading"
            class="btnSty h-28"
            @click="submitInfo"
          >
            确定
          </Button>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script>
import Tables from '_c/tables/tables.1'
import { getContactsList, delBatch, importAmc } from '@/api/contact'
import { mapActions, mapGetters } from 'vuex'
import contactsModal from '@/view/dy-nomenu/agent/components/contactsModal'
import editStatus from './components/editStatus'
import { resolveComponent } from 'vue'
export default {
  components: {
    Tables,
    contactsModal,
    editStatus
  },
  data() {
    return {
      columns: [],
      apifun: {
        get: {
          func: getContactsList,
          callback: (res) => {
            if (res.success) {
              this.count = res.data.total
            } else {
              this.count = '--'
            }
          }
        }
      },
      count: '--',
      caseId: null,
      contactForm: null,
      contactsIds: [],
      searchInfo: null,
      ui: {
        handleAll: false,
        showContactsModal: false,
        showEditStatus: false,
        showModal: false
      },
      uploadLoading: false,
      updateFrom: {
        templateId: '',
        orgDeltId: 0,
        productId: 0
      },
      updateFromRole: {
        templateId: [{ required: true, type: 'number', message: this.$t('l_selectImportTp'), trigger: 'change' }],
        productId: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value) => {
              return new Promise((resolve, reject) => {
                if (this.updateFrom.productId) {
                  resolve()
                } else {
                  reject('上传文件不能为空')
                }
              })
            }
          }
        ]
      },
      tplList: [
        { name: '通讯模板', id: 1 }
      ]
    }
  },
  computed: {
    ...mapGetters([
      'contactStatus',
      'functions',
      'myDeltId'
    ])
  },
  created() {
    this.getRefData({
      map: ['contactStatus']
    }).then(() => {
      this.columns = [
        {
          type: 'selection',
          width: 36,
          align: 'center'
        },
        { title: '姓名', key: 'name', width: 270, search: true, submitKey: 'names', type: 'textarea', focusText: this.$t('searchsTip') },
        { title: '手机号', key: 'mobile', width: 200, search: true, submitKey: 'mobiles', type: 'textarea', focusText: this.$t('searchsTip') },
        {
          title: '关系',
          key: 'relationType',
          width: 100,
          search: true,
          submitKey: 'relationType',
          type: 'input'
        },
        {
          title: '备注',
          key: 'desc',
          width: 270,
          tooltip: true
        },
        {
          title: '状态',
          key: 'status',
          bindmap: 'contactStatus',
          minWidth: 200,
          iconRender: true,
          search: true,
          type: 'select',
          searchItems: this.contactStatus
        },
        {
          title: '操作',
          key: 'handle',
          fixed: 'right',
          width: 100,
          render: (h, { row }) => {
            const edit = h(resolveComponent('Button'), {
              type: 'text',
              class: 'ivu-btn-text-default',
              onClick: () => {
                this.contactForm = { ...row }
                this.ui.showContactsModal = true

                this.$nextTick(() => {
                  this.$refs.contactsModal.ui.contactModal = true
                })
              }
            }, {
              default: () => '修改'
            })

            const deleted = h(resolveComponent('Button'), {
              type: 'text',
              class: 'ivu-btn-text-default',
              style: {
                marginLeft: '16px'
              },
              onClick: () => {
                this.$Modal.confirm({
                  title: '删除提示',
                  content: `<p>确定删除所选联系人？删除后此债务人相关共债案件均无此联系人。</p>`,
                  onOk: () => {
                    delBatch({
                      ids: [row.id]
                    }).then(res => {
                      if (res.success) {
                        this.ui.handleAll = false
                        this.$refs.tables.selection = []
                        this.$refs.tables.handleSearch()
                      }
                    })
                  }
                })
              }
            }, {
              default: () => '删除'
            })

            const btns = [edit, deleted]

            return h('div', btns)
          }
        }
      ]
    })
  },
  methods: {
    ...mapActions([
      'getRefData'
    ]),
    handleChange(bool) {
      for (const value of this.$refs.tables.insideTableData) {
        value._disabled = bool
      }

      if (!bool) {
        this.$refs.tables.selection = []
        this.$refs.tables.clearSelection()
      }
    },
    batchDelete() {
      let count = 0
      count = this.ui.handleAll ? this.count : this.$refs.tables.selection.length

      if (count === 0) {
        return this.$Message.error('至少选择一个联系人')
      }

      this.$Modal.confirm({
        title: '删除提示',
        content: `<p>确定删除所选联系人？删除后此${this.$t('obligor')}相关共债案件均无此联系人。</p>`,
        loading: true,
        onOk: () => {
          let sendData = {}
          if (this.ui.handleAll) {
            sendData = this.$refs.tables.getSearchSumbitData() || {}
          } else {
            sendData.ids = []
            this.$refs.tables.selection.map(item => sendData.ids.push(item.id))
          }

          delBatch(sendData).then((res) => {
            if (res.success) {
              if (this.ui.handleAll) {
                this.$Modal.remove()
                setTimeout(() => {
                  this.$Modal.success({
                    title: '操作成功',
                    okText: '关闭',
                    render: (h) => {
                      const href = h('a', {
                        style: {
                          color: '#ff4f1f',
                          textDecoration: 'underline'
                        },
                        onClick: () => {
                          this.gotoList('4')
                          this.$Modal.remove()
                        }
                      }, '操作记录')
                      const str1 = h('span', {}, '请至')
                      const str2 = h('span', {}, '界面查看操作结果')
                      const content = [str1, href, str2]
                      return h('span', {
                        style: {
                          marginLeft: '6px',
                          fontSize: '12px'
                        }
                      }, content)
                    },
                    onOk: () => {
                      this.ui.handleAll = false
                      this.$refs.tables.selection = []
                      this.$refs.tables.handleSearch()
                    }
                  })
                }, 400)
              } else {
                this.$Modal.remove()
                this.ui.handleAll = false
                this.$refs.tables.selection = []
                this.$refs.tables.handleSearch()
              }
            }
          })
        }
      })
    },
    gotoList(index) {
      this.$emit('go-to-list', index)
    },
    closeModals(tag) {
      this.ui[tag] = false
    },
    changeContacts() {
      this.ui.handleAll = false
      this.$refs.tables.selection = []
      this.$refs.tables.handleSearch()
    },
    editStatus() {
      if (!this.$refs.tables.insideTableData.length) {
        this.$Message.error('列表暂无数据')
        return
      }

      const selection = this.$refs.tables.selection
      if (!selection.length && !this.ui.handleAll) {
        this.$Message.error('请至少选择一个联系人')
        return
      }

      this.contactsIds = []
      this.searchInfo = null

      if (!this.ui.handleAll) {
        for (const item of selection) {
          this.contactsIds.push(item.id)
        }
      } else {
        const search = this.$refs.tables.getSearchSumbitData()
        delete search.limit
        delete search.page
        this.searchInfo = search
      }

      this.ui.showEditStatus = true

      this.$nextTick(() => {
        this.$refs.editStatus.ui.showModal = true
      })
    },
    closeEditStatus(bool) {
      this.ui.showEditStatus = false
      if (bool) {
        this.ui.handleAll = false
        this.$refs.tables.handleSearch()
      }
    },
    handleSuccess() {
      this.closeEditStatus(true)

      setTimeout(() => {
        this.$Modal.success({
          title: '操作成功',
          okText: '关闭',
          render: (h) => {
            const href = h('a', {
              style: {
                color: '#ff4f1f',
                textDecoration: 'underline'
              },
              onClick: () => {
                this.gotoList('4')
                this.$Modal.remove()
              }
            }, '操作记录')
            const str1 = h('span', {}, '请至')
            const str2 = h('span', {}, '界面查看操作结果')
            const content = [str1, href, str2]
            return h('span', {
              style: {
                marginLeft: '6px',
                fontSize: '12px'
              }
            }, content)
          },
          onOk: () => {
            this.ui.handleAll = false
            this.$refs.tables.selection = []
            this.$refs.tables.handleSearch()
          }
        })
      }, 400)
    },
    // 联系人导入按钮
    contactsImport() {
      this.ui.showModal = true
    },
    download() {
      window.open('/static/通讯模版.xlsx')
    },
    // 取消弹层
    clearModal() {
      this.ui.showModal = false
      this.$refs.updateFrom.resetFields()
      this.updateFrom = {
        templateId: null,
        orgDeltId: 0,
        productId: 0
      }
    },
    // 确认弹层信息
    submitInfo() {
      this.$refs.updateFrom.validate((valid) => {
        if (!this.updateFrom.productId) {
          this.updateFrom.productId = false
          valid = false
        }
        if (valid) {
          this.uploadLoading = true
          const formdata = new FormData()
          formdata.append('orgDeltId', this.myDeltId)
          formdata.append('file', this.updateFrom.productId)
          importAmc(formdata).then((res) => {
            if (res.success) {
              this.clearModal()
              this.$Modal.success(({
                title: '操作成功',
                okText: '关闭',
                render: h => {
                  const href = h(
                    'a',
                    {
                      style: {
                        color: '#ff4f1f',
                        textDecoration: 'underline'
                      },
                      onClick: () => {
                        this.$emit('go-to-list', '2')
                        this.$Modal.remove()
                      }
                    },
                    '导入记录'
                  )
                  const str1 = h('span', {}, '操作成功，请至')
                  const str2 = h('span', {}, '查看导入结果。')
                  const content = [str1, href, str2]
                  return h(
                    'span',
                    {
                      style: {
                        marginLeft: '6px',
                        fontSize: '12px'
                      }
                    },
                    content
                  )
                }
              }))
            }
            this.uploadLoading = false
            this.$refs.tables.handleSearch()
          })
        }
      })
    },
    fileExceededSize(file, fileList) {
      this.$Message.error('上传文件不超过20MB')
    },
    // 文件上传之前的认证
    async handleBeforeUpload(file, _callback) {
      if (file.size > 20 * 1024 * 1024) {
        this.$Message.error('上传文件不超过20MB')
        return
      }
      this.updateFrom.productId = file
      this.$refs.updateFrom.validateField('productId')
      await Promise.reject(new Error(false))
      _callback && _callback()
    }
  }
}
</script>

<style lang="less" scoped>
.progress {
    margin-top: 8px;
}
.headers {
    border-top: 1px dotted #DCDEE3;
    padding-top: 10px;
    .alert {
        span {
            color: @primary-color;
            font-weight: 600;
        }
    }
}
.table-btns {
    display: flex;
    align-items: center;

    .divide {
        width: 1px;
        height: 20px;
        background-color: #DCDEE3;
        margin-left: 2px;
    }
}
.btnSty {
    width: 60px;
    height: 32px;
}
.delBtn {
  color: #636C78;
  &:hover{
    cursor:pointer;
    color: #828EA0;
  }
  &:active{
    color: #505862;
  }
}
.spanM {
  margin: 0 4px;
}
.tips {
  color: #8D959F;
  line-height: 17px;
  margin-top: 10px;
}
</style>
