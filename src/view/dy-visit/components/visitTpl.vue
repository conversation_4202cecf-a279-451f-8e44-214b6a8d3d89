<template>
  <div>
    <Modal
      v-model="ui.showModal"
      :footer-hide="true"
      :mask-closable="false"
      :closable="false"
      :title="props.isEdit ? '编辑模板' : '新增模板'"
      @on-visible-change="visibleChange"
    >
      <Alert type="warning" show-icon class="no-border no-border-radius alerts">
        <p>
          文件类型支持docx格式，上传文件大小不超过10M；模板中的变量配置请参考
          <a style="display: inline-block;" @click="handleIntro">模板配置说明文档</a>
          。
        </p>
      </Alert>

      <Form
        v-if="ui.showModal"
        ref="formsRef"
        :model="forms"
        :rules="rules"
        :label-width="107"
        label-colon
      >
        <FormItem label="模板名称" prop="name">
          <Input v-model="forms.name" placeholder="请输入模板名称" :maxlength="100" show-word-limit />
        </FormItem>
        <FormItem label="备注" prop="desc">
          <Input v-model="forms.desc" type="textarea" :maxlength="500" :rows="2" :autosize="{ minRows: 2, maxRows: 13 }" placeholder="请输入" show-word-limit />
        </FormItem>
        <FormItem label="上传模板文件" prop="file">
          <div class="uploads">
            <Upload
              v-if="!forms.file"
              action=""
              :show-upload-list="false"
              :before-upload="beforeUploadProofFile"
              accept=".docx"
            >
              <Button class="ivu-btn-grey" icon="iconfont icon-wenjian1">上传文件</Button>
            </Upload>
            <div v-else class="promise-file">
              <Icon type="iconfont icon-word" />
              <span>{{ forms.file.name }}</span>
              <Icon type="iconfont icon-shanchu1" @click.stop="deleteFile" />
            </div>
          </div>
        </FormItem>
        <FormItem class="btns">
          <Button style="margin-right: 12px" @click="visibleChange(false)">取消</Button>
          <Button type="primary" :loading="ui.loading" @click="handleSubmit">确定</Button>
        </FormItem>
      </Form>

      <tplIntro ref="tplIntroRef" />
    </Modal>
  </div>
</template>

<script setup>
import tplIntro from './tplIntro'
import { deepClone } from '@/libs/tools'
import { addVisitTpl, updateVisitTpl } from '@/api/visit'
import { ref, defineProps, defineEmits, defineExpose } from 'vue'
import { Message } from 'view-ui-plus'
const FORMS = {
  name: '',
  desc: '',
  file: null
}
const formsRef = ref(null)
const tplIntroRef = ref(null)
const emit = defineEmits(['on-close'])
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  },
  info: {
    type: Object,
    default() {
      return {}
    }
  }
})
const imgUrl = ref('')
const forms = ref(deepClone(FORMS))
const rules = ref({
  name: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
  desc: [{ required: false, message: '备注', trigger: 'blur' }],
  file: [{
    required: true,
    trigger: 'change',
    validator: (rule, value) => {
      return new Promise((resolve, reject) => {
        if (forms.value.file) {
          resolve()
        } else {
          reject('上传文件不能为空')
        }
      })
    }
  }]
})
const ui = ref({
  showModal: false,
  loading: false,
  upload: false
})
const visibleChange = (bool) => {
  if (bool) {
    if (props.isEdit) {
      forms.value = deepClone(props.info)
      // 编辑时回显文件
      forms.value['file'] = {
        name: forms.value.fileName,
        url: forms.value.url
      }
    } else {
      forms.value = deepClone(FORMS)
    }
  } else {
    formsRef.value.resetFields()
    ui.value.showModal = false
    ui.value.loading = false
  }
}
const handleIntro = () => {
  tplIntroRef.value.ui.showDrawer = true
}
const beforeUploadProofFile = async(file, _callback) => {
  if (file.size > 10 * 1024 * 1024) {
    Message.error('上传文件大小不超过10M')
    return
  }

  imgUrl.value = ''
  const reader = new FileReader()
  reader.readAsDataURL(file)
  forms.value.file = file
  formsRef.value.validateField('file')
  ui.value.upload = true
  await Promise.reject(new Error(false))
  _callback && _callback()
}
const deleteFile = () => {
  forms.value['file'] = null
}
const handleSubmit = () => {
  if (ui.value.loading) return

  formsRef.value.validate((valid) => {
    if (valid) {
      const sendData = {
        name: forms.value.name,
        desc: forms.value.desc
      }

      if (ui.value.upload) {
        sendData.file = forms.value.file
      }

      if (props.isEdit) {
        sendData.id = forms.value.id
      }

      const formData = new FormData()
      for (const key in sendData) {
        formData.append(key, sendData[key])
      }

      ui.value.loading = true

      const func = props.isEdit ? updateVisitTpl : addVisitTpl
      func(formData).then(res => {
        if (res.success) {
          ui.value.showModal = false
          emit('on-close', true)
        }
      }).finally(() => {
        ui.value.loading = false
      })
    }
  })
}
defineExpose({ ui })

</script>

<style lang="less" scoped>
:deep(.ivu-modal-mask), :deep(.ivu-modal-wrap) {
  z-index: 999 !important;
}
.alerts {
  margin: -16px -16px 16px;
}
.uploads {
  display: flex;
  align-items: center;
  >.promise-file {
    display: flex;
    align-items: center;
    >i {
      &:first-child {
        font-size: 22px;
        color: #3C7DC7;
      }
      &:last-child {
        font-size: 16px;
        color: #636C78;
        cursor: pointer;
      }
    }
    >span {
      margin-left: 4px;
      margin-right: 4px;
    }
  }
}
</style>
