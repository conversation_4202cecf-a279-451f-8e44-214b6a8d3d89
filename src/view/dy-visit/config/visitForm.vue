<template>
  <div>
    <Alert class="alerts">注意：外访员提交外访报告时的内容项可自定义配置，启用或关闭。内容项修改，已成功提交的外访信息将不受影响。</Alert>

    <Tables
      v-if="columns.length"
      ref="mainTableRef"
      :columns="columns"
      :apifun="apifun"
      :sub-table-height="100"
      @on-drag="dragList"
    ></Tables>

    <customFields :id="fieldId" ref="customFieldsRef" :type="fieldType" @on-close="handleClose" />
  </div>
</template>

<script setup>
import Tables from '_c/tables/tables.1'
import {
  visitFields,
  sortVisitField,
  enableVisitField
} from '@/api/visit'
import customFields from '../components/customFields'
import { resolveComponent } from 'vue'
import { ref, nextTick } from 'vue'
import { Modal } from 'view-ui-plus'
const mainTableRef = ref(null)
const customFieldsRef = ref(null)
const columns = ref([
  {
    type: 'dragged',
    width: 80,
    renderHeader: h => {
      return h('div', {}, [
        h(
          resolveComponent('Tooltip'),
          {
            placement: 'bottom-end',
            offset: 100,
            transfer: true,
            style: 'word-break: break-all;'
          }, {
            default: () => [h('span', '排序'),
              h(resolveComponent('Icon'), {
                type: 'md-help-circle',
                color: '#636C78',
                size: 16,
                style: {
                  marginLeft: '2px'
                }
              })],
            content: () => h(
              'span',
              {},
              [
                h(
                  'span',
                  {},
                  '拖动排序'
                )
              ]
            )
          }
        )
      ])
    },
    render: (h, { row }) => {
      return row.isExt ? h('span', {
        class: {
          'drag-btn': true
        }
      }, [
        h(resolveComponent('Icon'), {
          type: 'iconfont icon-tuodong',
          color: '#636C78',
          size: 16
        })
      ]) : h('span', {}, '')
    }
  },
  {
    title: '字段名称',
    key: 'name',
    width: 120,
    tooltip: true,
    desenKey: 'fieldName'
  },
  {
    title: '字段类型',
    key: 'type',
    bindmap: 'visitFieldTypes'
  },
  {
    title: '是否必填',
    type: 'required',
    render: (h, { row }) => {
      return h('span', {}, row.required ? '是' : '否')
    }
  },
  {
    title: '字段提示内容',
    key: 'hint',
    width: 260,
    tooltip: true
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    fixed: 'right',
    render: (h, { row }) => {
      const switchs = h('div', {
        style: {
          display: 'flex',
          alignItems: 'center'
        }
      }, [
        h(resolveComponent('i-switch'), {
          modelValue: row.isSelected,
          'true-value': 1,
          'false-value': 0,
          size: 'small',
          onOnChange: (bool) => {
            changeTagSwitch(bool, row)
          }
        }),
        h('span', {
          style: {
            marginLeft: '6px'
          }
        }, row.isSelected ? '启用' : '关闭')
      ])

      const btns = []
      if (row.isExt) {
        btns.push(switchs)
      }

      return h('div', {}, btns)
    }
  },
  {
    title: '操作',
    key: 'handle',
    width: 120,
    fixed: 'right',
    render: (h, { row }) => {
      const switchs = h(resolveComponent('Button'), {
        type: 'text',
        onClick: () => {
          fieldType.value = row.type
          fieldId.value = row.id

          nextTick(() => {
            customFieldsRef.value.ui.showDrawer = true
          })
        }
      }, {
        default: () => '编辑'
      })

      const btns = []
      if (row.isExt) {
        btns.push(switchs)
      }

      return h('div', {}, btns)
    }
  }
])

const apifun = ref({
  get: visitFields,
  filterGetList: (data) => {
    return {
      list: data
    }
  }
})
const fieldType = ref(null)
const fieldId = ref(null)

const dragList = (data) => {
  let canContinue = true
  for (const key in data) {
    if (key < 4 && data[key].isExt) { // 前四个为系统字段，不可调整顺序
      canContinue = false
    }
  }

  if (!canContinue) {
    mainTableRef.value.handleSearch()
    return
  }

  const json = []
  for (const [index, value] of data.entries()) {
    json.push({
      id: value.id,
      seq: index + 1
    })
  }

  sortVisitField(json).then(res => {
    if (res) {
      mainTableRef.value.handleSearch()
    }
  })
}
const changeTagSwitch = (bool, item) => {
  if (bool) {
    enableVisitField({
      isSelected: bool,
      fieldId: item.id
    }).then((res) => {
      if (!res.success) {
        mainTableRef.value.handleSearch()
      }
    })
  } else {
    Modal.confirm({
      title: '关闭表单配置提示',
      content: `是否确认关闭该表单配置？`,
      loading: true,
      onOk: () => {
        enableVisitField({
          fieldId: item.id,
          isSelected: bool
        }).then(res => {
          if (!res.success) {
            mainTableRef.value.handleSearch()
          }
        }).finally(() => {
          Modal.remove()
        })
      },
      onCancel: () => {
        mainTableRef.value.handleSearch()
      }
    })
  }
}
const handleClose = (bool) => {
  if (bool) {
    mainTableRef.value.handleSearch()
  }
}

</script>

<style lang="less" scoped>
.alerts {
  margin-bottom: 10px;
}
</style>
