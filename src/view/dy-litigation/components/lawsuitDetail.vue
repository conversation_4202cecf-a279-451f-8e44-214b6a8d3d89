<template>
  <div>
    <Modal
      v-model="ui.showModal"
      :footer-hide="true"
      :mask-closable="false"
      :fullscreen="true"
      :closable="false"
      :transfer="false"
      class-name="lawsuit-fullscreen"
    >
      <template #header>
        <div class="modal-header">
          <a @click="quit">
            <Icon custom="iconfont icon-fanhui" />返回
          </a>

          <p v-if="[0, 1].includes(caseStatus)" class="case-status">
            案件状态：
            <span class="status">
              <template v-if="caseStatus === 0">
                <img src="@/assets/images/incase.png" alt="">
                <span>办案</span>
              </template>
              <template v-else>
                <img src="@/assets/images/endcase.png" alt="">
                <span>结案</span>
              </template>
            </span>
          </p>
        </div>
      </template>

      <div class="content">
        <div class="lawsuit-process">
          <div class="header">
            <p>诉讼进度</p>
            <Button type="primary" ghost icon="iconfont icon-gengxin" @click="openProcess">更新进度</Button>
          </div>

          <div v-if="process.length" ref="processBody" class="body">
            <ul class="list">
              <li v-for="item in process" :key="item.id">
                <div class="left">
                  <div class="circle"></div>
                </div>
                <div class="right">
                  <p class="title">{{ item.processName }}</p>
                  <p class="name">{{ item.operatorName }}</p>
                  <p class="time">{{ formatTime(item.createTime) }}</p>
                </div>
              </li>
            </ul>
          </div>
          <div v-else class="empty">
            <img src="@/assets/images/empts.png" alt="">
            <p>暂无诉讼进度</p>
          </div>
        </div>

        <div class="lawsuit-info">
          <div class="header">
            <p>诉讼信息</p>
            <Button type="primary" ghost icon="iconfont icon-bianji" @click="openEdit">案件编辑</Button>
          </div>
          <div class="body">
            <div class="case-info case-man">
              <p class="title">
                <Icon type="iconfont icon-anjianbiaoqian" />
                <span>案人信息</span>
              </p>
              <div class="case-item-container">
                <ul class="case-item">
                  <template v-for="item in fields">
                    <li v-if="item.fegroup === 'baseInfo'" :key="item.key">
                      <div class="title">
                        <span>{{ item.name }}</span>
                      </div>
                      <div class="value">
                        <Tooltip
                          v-if="item.type !== 'handle'"
                          :content="item.value"
                          :max-width="410"
                          transfer
                          placement="bottom"
                          :disabled="!(item.value && item.value.length > 30)"
                        >
                          <span class="values">{{ item.value || '--' }}</span>
                        </Tooltip>
                        <Poptip
                          placement="bottom-start"
                          transfer
                          padding="0"
                          :offset="-13"
                          trigger="hover"
                          @on-popper-show="popperShow(item.value)"
                        >
                          <Icon
                            v-if="['idCard', 'secondIdCard', 'thirdIdCard'].includes(item.key) && !!item.value"
                            class="icon"
                            type="iconfont icon-chakanxiangqing"
                          />
                          <template #content>
                            <div
                              class="case-detail-search-content"
                            >
                              <p class="title">
                                身份证查询信息
                              </p>
                              <div>
                                <p>
                                  <span>发证地：</span>{{ idCardInfo.area }}
                                </p>
                                <p>
                                  <span>出生日期：</span>{{ idCardInfo.birth }}
                                </p>
                                <p><span>性别：</span>{{ idCardInfo.sex }}</p>
                                <p><span>年龄：</span>{{ idCardInfo.age }}</p>
                              </div>
                            </div>
                          </template>
                        </Poptip>
                        <Poptip
                          v-if="item.type === 'handle'"
                          v-model="ui.showRelationCase"
                          placement="bottom-start"
                          transfer
                          padding="0"
                          :offset="-13"
                          trigger="click"
                        >
                          <Button type="text" style="color: #1F7EE1">查看</Button>
                          <template #content>
                            <div class="relation-case-poptip">
                              <p class="title">查看关联案件</p>
                              <div class="container">
                                <Tables
                                  v-if="apifun && columns.length && relation.length"
                                  ref="relationTable"
                                  :columns="columns"
                                  :apifun="apifun"
                                  :max-height="347"
                                ></Tables>
                                <div v-if="!relation.length" class="empty">
                                  <img src="@/assets/images/no-content.png" alt="">
                                </div>
                              </div>
                            </div>
                          </template>
                        </Poptip>
                      </div>
                    </li>
                  </template>
                </ul>
              </div>
            </div>

            <div class="case-info case-fields">
              <p class="title">
                <Icon type="iconfont icon-anjianbiaoqian" />
                <span>案件信息</span>
              </p>
              <div class="case-item-container">
                <ul class="case-item">
                  <template v-for="item in fields">
                    <li v-if="item.fegroup === 'caseInfo'" :key="item.key">
                      <div class="title">
                        <span>{{ item.name }}</span>
                      </div>
                      <div class="value">
                        <Tooltip
                          :content="item.value"
                          :max-width="410"
                          transfer
                          placement="bottom"
                          :disabled="!(item.value && item.value.length > 30)"
                        >
                          <span class="values">{{ formatData(item.value, item) }}<span v-if="item.key === 'cost'" style="color: #FF4F1F">【已收款：<span>{{ payNum }}</span>】</span></span>
                        </Tooltip>
                      </div>
                    </li>
                  </template>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="lawsuit-table">
          <Tabs v-model="ui.isKey" :animated="false">
            <TabPane label="办理记录" name="1">
              <Tables
                v-if="handRecordColumns.length && ui.isKey === '1' && handRecordSearchData"
                ref="handleRecord"
                table-name="lawsuit-detail-handRecord"
                :columns="handRecordColumns"
                :apifun="handRecordApifun"
                :inject-search-data="handRecordSearchData"
                :height="400"
                border="header"
                pageable
                resizable
              >
                <template #header>
                  <div>
                    <div class="tables-header">
                      <Button type="primary" @click="openAddHandRecord">新增办理记录</Button>
                    </div>
                  </div>
                </template>
              </Tables>
            </TabPane>
            <TabPane label="收费记录" name="2">
              <Tables
                v-if="chargeColumns.length && ui.isKey === '2' && chargeSearchData"
                ref="chargeRecord"
                table-name="lawsuit-detail-charge"
                :columns="chargeColumns"
                :apifun="chargeApifun"
                :inject-search-data="chargeSearchData"
                :height="400"
                border="header"
                pageable
                resizable
              >
                <template #header>
                  <div>
                    <div class="tables-header">
                      <Button type="primary" @click="openAddCharge">新增收费记录</Button>
                    </div>
                  </div>
                </template>
              </Tables>
            </TabPane>
            <TabPane label="资产信息" name="3">
              <Tables
                v-if="assetsColumns.length && ui.isKey === '3' && assetsSearchData"
                ref="assetsRecord"
                table-name="lawsuit-detail-assets"
                :columns="assetsColumns"
                :apifun="assetsApifun"
                :inject-search-data="assetsSearchData"
                :height="400"
                border="header"
                pageable
                resizable
              >
                <template #header>
                  <div>
                    <div class="tables-header">
                      <Button type="primary" @click="openAddAssets">新增资产记录</Button>
                    </div>
                  </div>
                </template>
              </Tables>
            </TabPane>
            <TabPane label="材料文件" name="4">
              <Tables
                v-if="filesColumns.length && ui.isKey === '4' && filesSearchData"
                ref="filesRecord"
                table-name="lawsuit-detail-files"
                :columns="filesColumns"
                :apifun="filesApifun"
                :inject-search-data="filesSearchData"
                :height="400"
                border="header"
                pageable
                resizable
              >
                <template #header>
                  <div>
                    <div class="tables-header">
                      <Button type="primary" @click="openAddFields">上传文件</Button>
                    </div>
                  </div>
                </template>
              </Tables>
            </TabPane>
          </Tabs>
        </div>

        <div class="lawsuit-note">
          <div class="header">
            <p>案件备忘</p>

            <div class="btns">
              <Tooltip
                content="历史案件备忘"
                style="margin-right: 8px;"
                transfer
              >
                <Button
                  icon="iconfont icon-cuishoulishi"
                  class="hover-primary"
                  size="small"
                  :disabled="!caseNotes.length"
                  @click="showHistoryNotes"
                ></Button>
              </Tooltip>
              <Tooltip
                content="添加案件备忘"
                transfer
              >
                <Button
                  icon="iconfont icon-tianjialianxiren"
                  class="hover-primary"
                  size="small"
                  @click="handleAddNote"
                ></Button>
              </Tooltip>
            </div>
          </div>

          <div v-if="caseNotes.length" class="latest">
            <div class="note-content">
              <span>{{ caseNotes[0].note }}</span>
            </div>
            <div class="note-sub">
              <span class="create-by">
                {{ caseNotes[0].operatorName }}
              </span>
              <span class="create-time">
                {{ formatTime(caseNotes[0].createTime) }}
              </span>
            </div>
          </div>
          <div v-else class="emptys">
            <Icon type="iconfont icon-kongbai" />
            <span>案件备忘暂无内容</span>
          </div>
        </div>
      </div>

      <editInfo
        :id="id"
        ref="editInfoObj"
        :fields="fields"
        @on-update="updateDetail"
      />

      <addHandRecord
        :id="id"
        ref="addHandRecordObj"
        @on-close="closeAddHandRecord"
      />

      <editCharge
        :id="id"
        ref="editChargeObj"
        :data="editCharges"
        @on-close="closeEditCharge"
      />

      <editAssets
        :id="id"
        ref="editAssetsObj"
        :data="editAsset"
        @on-close="closeEditAeest"
      />

      <historyNotes
        ref="historyNotesObj"
        :data="caseNotes"
      />
    </Modal>

    <!-- 更新诉讼进度 -->
    <Modal
      v-model="ui.showProcess"
      title="更新诉讼进度"
      :footer-hide="true"
      :mask-closable="false"
      :closable="false"
    >
      <Form v-if="ui.showProcess" ref="updateProcessObj" :model="updateProcess" :rules="updateProcessRules" :label-width="76" label-colon>
        <FormItem label="进度节点" prop="processId">
          <Select v-model="updateProcess.processId" transfer filterable>
            <Option v-for="item in store.getters.availableLawsuitProcess" :key="item.id" :value="item.id">{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem class="btns">
          <Button style="margin-right: 12px;" @click="handleCancelProcess">取消</Button>
          <Button type="primary" :loading="ui.processLoading" @click="handleSubmitProcess">确定</Button>
        </FormItem>
      </Form>
    </Modal>

    <fullScreenDetail
      v-if="ui.showFullScreen"
      ref="fullScreenDetailObj"
      :case-id="caseId"
      :type="detailType"
      @on-close="closeFullScreen"
    />

    <Modal
      v-model="ui.showEditHandler"
      title="修改办理记录"
      :footer-hide="true"
      :mask-closable="false"
      :closable="false"
    >
      <Alert type="warning" class="modal-alerts no-border no-border-radius" show-icon>
        办理记录仅能修改办理日期和备注。
      </Alert>

      <Form v-if="ui.showEditHandler" ref="handlerForm" :model="editHandleForms" :rules="editHandleRules" :label-width="74" label-colon>
        <FormItem label="办理日期" prop="processTime">
          <DatePicker v-model="editHandleForms.processTime" type="date" placeholder="请选择" style="width: 220px;" transfer></DatePicker>
        </FormItem>
        <FormItem label="备注" prop="remark">
          <Input v-model.trim="editHandleForms.remark" type="textarea" placeholder="请输入" :maxlength="500" show-word-limit :rows="3" :autosize="{ minRows: 3, maxRows: 16 }"></Input>
        </FormItem>
        <FormItem class="btns">
          <Button style="margin-right: 12px;" @click="handleCancelHandler">取消</Button>
          <Button type="primary" :loading="ui.editHandlerLoading" @click="handleSubmitHandler">确定</Button>
        </FormItem>
      </Form>
    </Modal>

    <Modal
      v-model="ui.showUpload"
      title="上传文件"
      :footer-hide="true"
      :mask-closable="false"
      :closable="false"
    >
      <Form v-if="ui.showUpload" ref="fileFormsObj" :model="fileForms" :rules="fileRules" :label-width="83" label-colon>
        <FormItem label="文件类型" prop="fileType">
          <Select v-model="fileForms.fileType" transfer filterable>
            <Option v-for="item in store.getters.availableLawsuitFiles" :key="item.id" :value="item.id">{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem label="上传文件" prop="files">
          <Upload
            action=""
            :show-upload-list="false"
            :before-upload="beforeUploadFile"
          >
            <Button class="ivu-btn-grey" icon="iconfont icon-wenjian1">上传文件和图片</Button>
          </Upload>
          <ul v-if="fileForms.files.length" class="file-list">
            <li v-for="(item, index) in fileForms.files" :key="index">
              <Icon type="iconfont icon-wenjian" />
              <span>{{ item.name }}</span>
              <Icon type="iconfont icon-shanchu1" @click="deleteFile(index)" />
            </li>
          </ul>
        </FormItem>
        <FormItem class="btns">
          <Button style="margin-right: 12px;" @click="handleFileCancel">取消</Button>
          <Button type="primary" :loading="ui.uploadLoading" @click="handleFileSubmit">确定</Button>
        </FormItem>
      </Form>
    </Modal>

    <Modal
      v-model="ui.addNote"
      title="新增案件备忘"
      :footer-hide="true"
      :mask-closable="false"
      :closable="false"
    >
      <Form ref="noteForm" :model="noteForms" :rules="noteRules" :label-width="74" label-colon>
        <FormItem label="案件备忘" prop="note">
          <Input v-model.trim="noteForms.note" type="textarea" placeholder="请输入" :maxlength="500" show-word-limit :rows="3" :autosize="{ minRows: 3, maxRows: 16 }"></Input>
        </FormItem>
        <FormItem class="btns">
          <Button style="margin-right: 12px;" @click="handleCancelNote">取消</Button>
          <Button type="primary" :loading="ui.noteLoading" @click="handleSubmitNote">确定</Button>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script setup>
import {
  getLawsuitDetail,
  addLawsuitProcess,
  getLawsuitHandler,
  delLawsuitHandler,
  updateLawsuitHandler,
  getLawsuitPay,
  delLawsuitPay,
  paySum,
  getLawsuitAssets,
  delLawsuitAsset,
  getLawsuitFile,
  delLawsuitFile,
  addLawsuitFile,
  getLawsuitNote,
  addLawsuitNote
} from '@/api/lawsuit'
import { useStore } from 'vuex'
import { formatDate, formatMoney, desenRulesAll } from '@/libs/util'
import { deepClone } from '@/libs/tools'
import idCard from 'idcard'
import Tables from '_c/tables/tables.1'
import fullScreenDetail from '@/view/dy-casectrl/fullScreenDetail'
import editInfo from './editInfo'
import addHandRecord from './addHandRecord'
import editCharge from './editCharge'
import editAssets from './editAssets'
import historyNotes from './historyNotes'
import { useI18n } from 'vue-i18n'
import { Message, Modal } from 'view-ui-plus'
import { ref, nextTick, defineProps, defineEmits, defineExpose, computed, resolveComponent } from 'vue'

const store = useStore()
const { t } = useI18n()

const props = defineProps({
  id: {
    type: Number,
    default: null
  }
})

const emit = defineEmits([
  'on-close'
])

const processBody = ref(null)
const relationTable = ref(null)
const handleRecord = ref(null)
const chargeRecord = ref(null)
const assetsRecord = ref(null)
const filesRecord = ref(null)
const editInfoObj = ref(null)
const addHandRecordObj = ref(null)
const editChargeObj = ref(null)
const editAssetsObj = ref(null)
const historyNotesObj = ref(null)
const updateProcessObj = ref(null)
const fullScreenDetailObj = ref(null)
const handlerForm = ref(null)
const fileFormsObj = ref(null)
const noteForm = ref(null)

// 诉讼进度
const process = ref([])
// 诉讼信息
const fields = ref([])
// 案件状态
const caseStatus = ref(null) // 0 在案   1 结案
// 关联案件信息
const relation = ref([])
const updateProcess = ref({
  processId: null
})
const updateProcessRules = ref({
  processId: [
    { required: true, message: '进度节点不能为空', trigger: 'change', type: 'number' }
  ]
})
const idCardInfo = ref({
  area: '--',
  birth: '--',
  sex: '--',
  age: '--'
})
const columns = ref([])
const apifun = ref(null)
const caseId = ref(null)
const detailType = ref('')
// 办理记录
const handRecordColumns = ref([])
const handRecordApifun = ref({
  get: getLawsuitHandler
})
const handRecordSearchData = ref(null)
// 修改办理记录
const editHandleForms = ref({
  id: null,
  processTime: null,
  remark: null
})
const editHandleRules = ref({
  processTime: [
    { required: true, message: '办理日期不能为空', trigger: 'change', pattern: /.+/ }
  ],
  remark: [
    { required: false, message: '备注不能为空', trigger: 'blur' }
  ]
})
// 收费记录
const chargeColumns = ref([])
const chargeApifun = ref({
  get: getLawsuitPay
})
const chargeSearchData = ref(null)
// 修改收费记录
const editCharges = ref(null)
// 已收款
const payNum = ref('--')
// 资产记录
const assetsColumns = ref([])
const assetsApifun = ref({
  get: getLawsuitAssets
})
const assetsSearchData = ref(null)
// 修改资产记录
const editAsset = ref(null)
// 材料文件
const filesColumns = ref([])
const filesApifun = ref({
  get: getLawsuitFile
})
const filesSearchData = ref(null)
// 上传文件
const fileForms = ref({
  fileType: null,
  files: []
})
const fileRules = ref({
  fileType: [
    { required: true, message: '文件类型不能为空', trigger: 'change', type: 'number' }
  ],
  files: [
    {
      required: true,
      trigger: 'change',
      validator: (rule, value) => {
        return new Promise((resolve, reject) => {
          if (fileForms.value.files.length) {
            resolve()
          } else {
            reject('上传文件不能为空')
          }
        })
      }
    }
  ]
})
// 案件备忘
const caseNotes = ref([])
// 新增案件备忘
const noteForms = ref({
  note: null
})
const noteRules = ref({
  note: [
    { required: true, message: '案件备忘不能为空', trigger: 'blur' }
  ]
})
const ui = ref({
  showModal: false,
  showProcess: false,
  processLoading: false,
  showFullScreen: false,
  showRelationCase: false,
  isKey: '1',
  showEditHandler: false,
  editHandlerLoading: false,
  showUpload: false,
  uploadLoading: false,
  addNote: false,
  noteLoading: false
})

const nedSpace = computed(() => {
  return fileForms.value.files.reduce((total, item) => {
    return total + item.size
  }, 0)
})

const formatTime = (value, fmt = 'yyyy-MM-dd hh:mm') => {
  return formatDate(value, fmt)
}
const formatData = (value, item) => {
  if (value) {
    if (item.type === 'date') {
      return formatDate(value, 'yyyy-MM-dd')
    } else if (item.dataType === 'number') {
      return formatMoney(value)
    } else {
      return value
    }
  } else {
    return item.key === 'cost' ? '' : '--'
  }
}
const quit = () => {
  ui.value.showModal = false
  emit('on-close', false)
}
const initPage = () => {
  caseStatus.value = null
  fields.value = [
    { name: '被告', key: 'name', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },
    { name: '手机号', key: 'mobile', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },
    { name: '身份证号', key: 'idCard', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },
    { name: '委案公司', key: 'orgDeltName', submitKey: 'orgDeltId', value: null, submitValue: null, disabled: true, fegroup: 'baseInfo' },
    { name: '关联案件', key: 'lawsuitCase', value: null, type: 'handle', disabled: true, fegroup: 'baseInfo' },
    { name: '委托人', key: 'principal', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },
    { name: '卡号', key: 'bankNo', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },
    { name: '联系地址', key: 'address', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },
    { name: '联系电话', key: 'contactNumber', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },
    { name: '档案号', key: 'archiveNo', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },
    { name: '被告二', key: 'secondName', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },
    { name: '被告二身份证号', key: 'secondIdCard', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },
    { name: '被告三', key: 'thirdName', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },
    { name: '被告三身份证号', key: 'thirdIdCard', value: null, type: 'input', dataType: 'string', fegroup: 'baseInfo' },

    { name: '诉讼案件ID', key: 'id', value: null, disabled: true, fegroup: 'caseInfo' },
    { name: '诉讼案号', key: 'lawsuitNo', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '标的', key: 'object', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '案件类型', key: 'typeName', submitKey: 'type', value: null, submitValue: null, type: 'select', searchItems: store.getters.availableLawsuitCaseTypes, fegroup: 'caseInfo' },
    { name: '案由', key: 'summary', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '还款情况', key: 'paymentCondition', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '代理律师', key: 'lawyer', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '律师联系方式', key: 'lawyerMobile', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '立案日期', key: 'registerTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '受理法院', key: 'courtName', submitKey: 'court', value: null, submitValue: null, type: 'select', searchItems: store.getters.availableCourts, fegroup: 'caseInfo' },
    { name: '办案法官', key: 'judge', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '法官联系方式', key: 'judgeMobile', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '诉讼费缴纳日期', key: 'lawsuitPayTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '保全费缴纳日期', key: 'savePayTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '保全资产清单', key: 'saveAsset', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '首次开庭日期', key: 'firstCourtTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '执行立案日期', key: 'executeRegisterTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '判决日期', key: 'judgmentTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '送达情况', key: 'delivery', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '判决书', key: 'judgment', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '争议处理方式', key: 'dispute', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '一审判决结果', key: 'firstJudgmentResult', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '是否有二审再审', key: 'isAgainJudgmentName', submitKey: 'isAgainJudgment', value: null, submitValue: null, type: 'select', searchItems: [{ value: 1, name: '是' }, { value: 0, name: '否' }], fegroup: 'caseInfo' },
    { name: '二审立案日期', key: 'secondRegisterTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '二审开庭日期', key: 'secondCourtTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '二审判决日期', key: 'secondJudgmentTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '再审立案日期', key: 'thirdRegisterTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '再审开庭日期', key: 'thirdCourtTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '再审判决日期', key: 'thirdJudgmentTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '判决生效日期', key: 'judgmentEffectiveTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '材料提交日期', key: 'submitMaterialTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '查封日期', key: 'sealTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '续封日期', key: 'keepSealTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '查封到期日期', key: 'sealExpireTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '案件是否归档', key: 'isArchiveName', submitKey: 'isArchive', value: null, submitValue: null, type: 'select', searchItems: [{ value: 1, name: '是' }, { value: 2, name: '否' }], fegroup: 'caseInfo' },
    { name: '应收代保全费', key: 'saveAmount', value: null, type: 'input', dataType: 'number', fegroup: 'caseInfo' },
    { name: '应收保底律师费', key: 'minLawyerAmount', value: null, type: 'input', dataType: 'number', fegroup: 'caseInfo' },
    { name: '应收风险律师费', key: 'riskLawyerAmount', value: null, type: 'input', dataType: 'number', fegroup: 'caseInfo' },
    { name: '应收代垫诉讼费', key: 'courtAmount', value: null, type: 'input', dataType: 'number', fegroup: 'caseInfo' },
    { name: '合同号', key: 'contractNo', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '执行承办律师', key: 'undertakeLawyer', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '申请执行案号', key: 'applyExecuteNo', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '执行终结日期', key: 'executeFinalTime', value: null, type: 'date', fegroup: 'caseInfo' },
    { name: '费用', key: 'cost', value: null, type: 'input', dataType: 'number', fegroup: 'caseInfo' },
    { name: '执行结果', key: 'executeResult', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' },
    { name: '备注', key: 'remark', value: null, type: 'input', dataType: 'string', fegroup: 'caseInfo' }
  ]
  process.value = []
  relation.value = []
  apifun.value = null
  payNum.value = '--'
  caseNotes.value = []

  if (props.id) {
    getLawsuitDetail(props.id).then(res => {
      if (res.success) {
        const data = res.data.lawsuit
        caseStatus.value = data.status
        for (const item of fields.value) {
          if (data[item.key] || data[item.key] === 0) {
            if (item.dataType === 'date') {
              item.value = formatDate(data[item.key], 'yyyy-MM-dd')
            } else {
              item.value = data[item.key]
            }
          }

          if (data[item.submitKey] || data[item.submitKey] === 0) {
            item.submitValue = data[item.submitKey]
          }

          if (['isAgainJudgmentName', 'isArchiveName'].includes(item.key)) {
            const searchItems = item.searchItems
            for (const i of searchItems) {
              if (i.value === item.submitValue) {
                item.value = i.name
              }
            }
          }

          item.value = desenRulesAll(item.key, item.value)
        }

        process.value = [...res.data.lawsuitProcess]
        relation.value = [...res.data.lawsuitCase]
        payNum.value = res.data.repayAmount

        // 滑到最右展示内容
        nextTick(() => {
          if (processBody.value) {
            const width = processBody.value.scrollWidth
            processBody.value.scrollTo(width, 0)
          }

          apifun.value = {
            insert: relation.value
          }
        })

        handRecordSearchData.value = {
          lawsuitId: props.id
        }

        chargeSearchData.value = {
          lawsuitId: props.id
        }

        assetsSearchData.value = {
          lawsuitId: props.id
        }

        filesSearchData.value = {
          lawsuitId: props.id
        }

        getNotes()
      }
    })
  }
}
const openProcess = () => {
  ui.value.showProcess = true
}
const handleCancelProcess = () => {
  ui.value.showProcess = false
  updateProcessObj.value.resetFields()
}
const handleSubmitProcess = () => {
  if (ui.value.processLoading) return

  updateProcessObj.value.validate((valid) => {
    if (valid) {
      const sendData = {
        ...updateProcess.value,
        lawsuitId: props.id
      }

      ui.value.processLoading = true
      addLawsuitProcess(sendData).then(res => {
        if (res.success) {
          Message.success('进度节点添加成功')
          initPage()
          ui.value.showProcess = false
          updateProcessObj.value.resetFields()
        }
      }).finally(() => {
        ui.value.processLoading = false
      })
    }
  })
}
const popperShow = (idcard) => {
  const idInfo = idCard.info(idcard)
  if (idInfo.valid) {
    idCardInfo.value = {
      area: idInfo.address,
      birth: `${String(idInfo.birthday).slice(0, 4)}.${String(
        idInfo.birthday
      ).slice(4, 6)}.${String(idInfo.birthday).slice(6)}`,
      sex: `${idInfo.gender === 'M' ? '男' : '女'}`,
      age: `${idInfo.age}岁`
    }
  } else {
    idCardInfo.value = {
      area: '--',
      birth: '--',
      sex: '--',
      age: '--'
    }
  }
}
const goToCase = (row) => {
  caseId.value = row.caseId
  ui.value.showFullScreen = true

  nextTick(() => {
    fullScreenDetailObj.value.ui.shows = true
  })
}
const closeFullScreen = (bool) => {
  ui.value.showFullScreen = false
}
const openEdit = () => {
  editInfoObj.value.ui.showDrawer = true
}
const updateDetail = () => {
  initPage()
}
const openAddHandRecord = () => {
  addHandRecordObj.value.ui.showDrawer = true
}
const closeAddHandRecord = (bool) => {
  if (bool) {
    handleRecord.value.handleSearch()
    initPage()
  }
}
const handleCancelHandler = () => {
  ui.value.showEditHandler = false
}
const handleSubmitHandler = () => {
  if (ui.value.editHandlerLoading) return

  handlerForm.value.validate((valid) => {
    if (valid) {
      const sendData = { ...editHandleForms.value }
      sendData.processTime = new Date(sendData.processTime).getTime()

      ui.value.editHandlerLoading = true
      updateLawsuitHandler(sendData).then(res => {
        if (res.success) {
          ui.value.showEditHandler = false
          handleRecord.value.handleSearch()
          initPage()
        }
      }).finally(() => {
        ui.value.editHandlerLoading = false
      })
    }
  })
}
const openAddCharge = () => {
  editCharges.value = null
  editChargeObj.value.ui.showDrawer = true
}
const closeEditCharge = (bool) => {
  if (bool) {
    chargeRecord.value.handleSearch()
    getPaySum()
  }
}
const getPaySum = () => {
  payNum.value = '--'
  paySum(props.id).then((res) => {
    if (res.success) {
      payNum.value = res.data
    }
  })
}
const openAddAssets = () => {
  editAsset.value = null
  editAssetsObj.value.ui.showDrawer = true
}
const closeEditAeest = (bool) => {
  if (bool) {
    assetsRecord.value.handleSearch()
  }
}
const openAddFields = () => {
  ui.value.showUpload = true
}
const beforeUploadFile = async (file, _callback) => {
  fileForms.value.files.push(file)
  fileFormsObj.value.validateField('files')
  await Promise.reject(new Error(false))
  _callback && _callback()
}
const deleteFile = (index) => {
  fileForms.value.files.splice(index, 1)
}
const handleFileCancel = () => {
  ui.value.showUpload = false
  fileFormsObj.value.resetFields()
  fileForms.value.files = []
}
const handleFileSubmit = () => {
  if (ui.value.uploadLoading) return

  fileFormsObj.value.validate((valid) => {
    if (valid) {
      const sendData = {
        lawsuitId: props.id,
        fileType: fileForms.value.fileType
      }

      const formData = new FormData()
      for (const key in sendData) {
        formData.append(key, sendData[key])
      }

      if (fileForms.value.files.length) {
        for (const key in fileForms.value.files) {
          formData.append('files', fileForms.value.files[key])
        }
      }
      const numSpace = (nedSpace.value / 1024 / 1024 / 1024) + store.getters.fileSpace.useSpace
      if (numSpace > store.getters.fileSpace.totalSpace && !store.getters.localDeploy) {
        return Modal.confirm({
          title: '存储空间不足',
          content: '剩余可用存储空间不足，请清理存储文件或续费后再操作。',
          okText: '关闭'
        })
      }
      ui.value.uploadLoading = true
      addLawsuitFile(formData).then((res) => {
        if (res.success) {
          ui.value.showUpload = false
          fileFormsObj.value.resetFields()
          fileForms.value.files = []
          filesRecord.value.handleSearch()
        }
      }).finally(() => {
        ui.value.uploadLoading = false
      })
    }
  })
}
const getNotes = () => {
  getLawsuitNote({
    lawsuitId: props.id
  }).then(res => {
    if (res.success) {
      caseNotes.value = [...res.data]
    }
  })
}
const handleAddNote = () => {
  ui.value.addNote = true
}
const handleCancelNote = () => {
  ui.value.addNote = false
  noteForm.value.resetFields()
}
const handleSubmitNote = () => {
  if (ui.value.noteLoading) return

  noteForm.value.validate((valid) => {
    if (valid) {
      const sendData = {
        lawsuitId: props.id,
        ...noteForms.value
      }

      ui.value.noteLoading = true
      addLawsuitNote(sendData).then(res => {
        if (res.success) {
          ui.value.addNote = false
          noteForm.value.resetFields()
          getNotes()
        }
      }).finally(() => {
        ui.value.noteLoading = false
      })
    }
  })
}
const showHistoryNotes = () => {
  historyNotesObj.value.ui.showDrawer = true
}

detailType.value = store.getters.isSupervisor ? 'teamLeader' : 'manage'

store.dispatch('getRefData', {
  func: ['getAvailableLawsuitCaseTypes', 'getAvailableCourts', 'getAvailableLawsuitProcess', 'getAvailableLawsuitFiles', 'getAvailableFileSpace']
}).then(() => {
  columns.value = [
    {
      title: t('K_outSerialNo'),
      key: 'outSerialNo',
      render: (h, { row }) => {
        return h('a', {
          style: 'color: #ff4f1f;',
          onClick: () => {
            ui.value.showRelationCase = false
            goToCase(row)
          }
        }, row.outSerialNo)
      }
    },
    {
      title: t('K_outSetialMoney'),
      key: 'amount',
      align: 'right',
      render: (h, { row }) => {
        return h('span', {}, formatMoney(row.amount))
      }
    }
  ]

  handRecordColumns.value = [
    { title: '操作时间', key: 'createTime', bindmap: 'formatDate' },
    { title: '诉讼进度', key: 'processName', minWidth: 116, width: 116 },
    { title: '办理人', key: 'processorName', minWidth: 130, width: 130 },
    {
      title: '办理内容',
      key: 'content',
      minWidth: 200,
      render: (h, { row }) => {
        const common = [
          { title: '办理日期', key: 'processTime', type: 'date' }
        ]
        // 立案5
        const fillCase = [
          { title: '办理日期', key: 'processTime', type: 'date' },
          { title: '立案日期', key: 'registerTime', type: 'date' },
          { title: '受理法院', key: 'courtName' },
          { title: '法官联系方式', key: 'judgeMobile' },
          { title: '诉讼案号', key: 'lawsuitNo' },
          { title: '诉讼费缴纳日期', key: 'lawsuitPayTime', type: 'date' }
        ]
        // 保全6
        const preservation = [
          { title: '办理日期', key: 'processTime', type: 'date' },
          { title: '保全费缴纳日期', key: 'savePayTime', type: 'date' },
          { title: '保全资产清单', key: 'saveAsset' }
        ]
        // 开庭7
        const holdCourt = [
          { title: '办理日期', key: 'processTime', type: 'date' },
          { title: '首次开庭日期', key: 'firstCourtTime', type: 'date' }
        ]
        // 判决9
        const sentence = [
          { title: '办理日期', key: 'processTime', type: 'date' },
          { title: '判决日期', key: 'judgmentTime', type: 'date' },
          { title: '送达情况', key: 'delivery' },
          { title: '判决书', key: 'judgment' }
        ]
        // 执行10
        const implement = [
          { title: '办理日期', key: 'processTime', type: 'date' },
          { title: '申请执行日期', key: 'applyExecuteTime', type: 'date' },
          { title: '申请执行案号', key: 'applyExecuteNo' },
          { title: '执行终结日期', key: 'executeFinalTime', type: 'date' }
        ]

        const content = []
        let processInfo = []
        switch (row.code) {
          case 5:
            processInfo = fillCase
            break
          case 6:
            processInfo = preservation
            break
          case 7:
            processInfo = holdCourt
            break
          case 9:
            processInfo = sentence
            break
          case 10:
            processInfo = implement
            break
          default:
            processInfo = common
        }
        for (const item of processInfo) {
          if (row[item.key] || row[item.key] === 0) {
            const i = {
              ...item,
              value: row[item.key]
            }

            if (item.type === 'date') {
              i.value = formatDate(i.value, 'yyyy-MM-dd')
            }

            content.push(i)
          }
        }
        return h('div', {}, [
          content.map((item, index) => {
            return h('span', {
              style: {
                lineHeight: '18px'
              }
            }, [
              h('span', {
                style: {
                  color: '#636C78'
                }
              }, item.title + '：'),
              h('span', {}, item.value + ((index === content.length - 1) ? '' : '；'))
            ])
          })
        ])
      }
    },
    { title: '备注', key: 'remark', defaultShow: '--', tooltip: true, minWidth: 178, width: 178 },
    {
      title: '操作',
      key: 'handle',
      width: 100,
      fixed: 'right',
      render: (h, { row }) => {
        const edit = h(resolveComponent('Button'), {
          type: 'text',
          style: {
            color: '#1F7EE1'
          },
          onClick: () => {
            editHandleForms.value = {
              id: row.id,
              processTime: formatDate(row.processTime, 'yyyy-MM-dd'),
              remark: row.remark
            }
            ui.value.showEditHandler = true
          }
        }, {
          default: () => '修改'
        })

        const del = h(resolveComponent('Button'), {
          type: 'text',
          style: {
            marginLeft: '16px',
            color: '#1F7EE1'
          },
          onClick: () => {
            Modal.confirm({
              title: '办理记录删除提示',
              content: '确认删除该条办理记录？办理记录数据已更新到案件信息中的，默认保留，如需更新可通过案件编辑进行更新。',
              loading: true,
              onOk: () => {
                delLawsuitHandler(row.id).then(res => {
                  if (res.success) {
                    handleRecord.value.handleSearch()
                  }
                }).finally(() => {
                  Modal.remove()
                })
              }
            })
          }
        }, {
          default: () => '删除'
        })

        return h('div', {}, [edit, del])
      }
    }
  ]

  chargeColumns.value = [
    { title: '款项类别', key: 'categoryName' },
    {
      title: '收费金额(元)',
      key: 'amount',
      render: (h, { row }) => {
        return h('span', {}, formatMoney(row.amount))
      }
    },
    { title: '收费日期', key: 'payTime', bindmap: 'formatDate', fmt: 'yyyy-MM-dd' },
    { title: '收费对象', key: 'payer' },
    { title: '付款方式', key: 'payType', bindmap: 'payTypes' },
    { title: '收款人', key: 'payee' },
    { title: '凭证号', key: 'voucherNo' },
    { title: '备注', key: 'remark', tooltip: true },
    {
      title: '操作',
      key: 'handle',
      width: 100,
      fixed: 'right',
      render: (h, { row }) => {
        const edit = h(resolveComponent('Button'), {
          type: 'text',
          style: {
            color: '#1F7EE1'
          },
          onClick: () => {
            editCharges.value = deepClone(row)
            editCharges.value.payTime = formatDate(editCharges.value.payTime, 'yyyy-MM-dd')
            delete editCharges.value.createTime
            delete editCharges.value.updateTime

            editChargeObj.value.ui.showDrawer = true
          }
        }, {
          default: () => '修改'
        })

        const dels = h(resolveComponent('Button'), {
          type: 'text',
          style: {
            marginLeft: '16px',
            color: '#1F7EE1'
          },
          onClick: () => {
            Modal.confirm({
              title: '收费记录删除提示',
              content: '确认删除该条收费记录？删除后不可找回。',
              loading: true,
              onOk: () => {
                delLawsuitPay(row.id).then(res => {
                  if (res.success) {
                    chargeRecord.value.handleSearch()
                    getPaySum()
                  }
                }).finally(() => {
                  Modal.remove()
                })
              }
            })
          }
        }, {
          default: () => '删除'
        })

        return h('div', {}, [edit, dels])
      }
    }
  ]

  assetsColumns.value = [
    { title: '资产类型', key: 'type', bindmap: 'assetTypes', width: 116, minWidth: 116 },
    {
      title: '是否有抵押',
      key: 'isMortgage',
      minWidth: 170,
      render: (h, { row }) => {
        return h('span', {}, row.isMortgage ? '是' : '否')
      }
    },
    {
      title: '委托人是否为抵押权人',
      key: 'isSelf',
      minWidth: 190,
      render: (h, { row }) => {
        return h('span', {}, row.isSelf ? '是' : '否')
      }
    },
    { title: '财产信息', key: 'assetInformation', tooltip: true, width: 152, minWidth: 152 },
    {
      title: '面积',
      key: 'area',
      width: 100,
      minWidth: 100,
      render: (h, { row }) => {
        return h('span', {}, row.area ? (row.area + '平') : '--')
      }
    },
    { title: '权属证号', key: 'cardNo', width: 126, minWidth: 126 },
    { title: '创建人', key: 'createByName', width: 120, minWidth: 120 },
    { title: '创建时间', key: 'createTime', bindmap: 'formatDate', width: 134, minWidth: 134 },
    {
      title: '操作',
      key: 'handle',
      width: 100,
      fixed: 'right',
      render: (h, { row }) => {
        const edit = h(resolveComponent('Button'), {
          type: 'text',
          style: {
            color: '#1F7EE1'
          },
          onClick: () => {
            editAsset.value = deepClone(row)
            delete editAsset.value.createTime
            delete editAsset.value.createByName
            delete editAsset.value.createBy
            delete editAsset.value.updateBy
            delete editAsset.value.updateTime
            delete editAsset.value.orgId

            if (editAsset.value.lawsuitSeizureVOList) {
              for (const item of editAsset.value.lawsuitSeizureVOList) {
                delete item.courtName
                delete item.createTime
                delete item.orgId
                delete item.stageName
                delete item.turnName
                delete item.updateTime
                if (item.startTime && item.endTime) {
                  item.cycleTime = [formatDate(item.startTime, 'yyyy-MM-dd'), formatDate(item.endTime, 'yyyy-MM-dd')]
                }
                delete item.startTime
                delete item.endTime
              }

              editAsset.value.lawsuitSeizureParamList = deepClone(editAsset.value.lawsuitSeizureVOList)
              delete editAsset.value.lawsuitSeizureVOList
            } else {
              editAsset.value.lawsuitSeizureParamList = []
            }

            editAssetsObj.value.ui.showDrawer = true
          }
        }, {
          default: () => '修改'
        })

        const dels = h(resolveComponent('Button'), {
          type: 'text',
          style: {
            marginLeft: '16px',
            color: '#1F7EE1'
          },
          onClick: () => {
            Modal.confirm({
              title: '资产信息删除提示',
              content: '确认删除该条资产信息？删除后不可找回。',
              loading: true,
              onOk: () => {
                delLawsuitAsset(row.id).then(res => {
                  if (res.success) {
                    assetsRecord.value.handleSearch()
                  }
                }).finally(() => {
                  Modal.remove()
                })
              }
            })
          }
        }, {
          default: () => '删除'
        })

        return h('div', {}, [edit, dels])
      }
    }
  ]

  filesColumns.value = [
    { title: '附件名称', key: 'name', width: 369, minWidth: 369 },
    { title: '文件类型', key: 'fileTypeName', width: 276, minWidth: 276 },
    {
      title: '大小',
      key: 'fileSize',
      width: 217,
      minWidth: 217,
      render: (h, { row }) => {
        return h('span', {}, row.fileSize + 'KB')
      }
    },
    { title: '上传时间', key: 'createTime', bindmap: 'formatDate', width: 258, minWidth: 258 },
    { title: '操作人', key: 'operatorName', width: 120, minWidth: 120 },
    {
      title: '操作',
      key: 'handle',
      width: 100,
      minWidth: 100,
      fixed: 'right',
      render: (h, { row }) => {
        const check = h(resolveComponent('Button'), {
          type: 'text',
          style: {
            marginRight: '16px',
            color: '#1F7EE1'
          },
          onClick: () => {
            const url = row.url.replace('http', 'https')
            window.open(url)
          }
        }, {
          default: () => '查看'
        })

        const dels = h(resolveComponent('Button'), {
          type: 'text',
          style: {
            color: '#1F7EE1'
          },
          onClick: () => {
            Modal.confirm({
              title: '资产信息删除提示',
              content: '确认删除该条资产信息？删除后不可找回。',
              loading: true,
              onOk: () => {
                delLawsuitFile(row.id).then(res => {
                  if (res.success) {
                    filesRecord.value.handleSearch()
                  }
                }).finally(() => {
                  Modal.remove()
                })
              }
            })
          }
        }, {
          default: () => '删除'
        })

        const btns = []
        if (row.url) {
          btns.push(check)
        }
        btns.push(dels)

        return h('div', {}, btns)
      }
    }
  ]

  initPage()
})

defineExpose({
  ui
})
</script>

<style lang="less" scoped>
:deep(.lawsuit-fullscreen) {
  >.ivu-modal-fullscreen {
    >.ivu-modal-content {
      >.ivu-modal-body {
        top: 44px !important;
        padding: 16px 0 16px 0px !important;
      }
    }
  }
}
.modal-header {
  height: 20px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  >a {
    font-size: 14px;
    color: @title-color;
    font-weight: 600;
    display: flex;
    align-items: center;
    >i {
      font-size: 16px;
      color: #636C78;
      margin-right: 10px;
    }
  }
  >.case-status {
    font-size: 14px;
    margin-left: 24px;
    display: flex;
    align-items: center;
    >.status {
      margin-left: 4px;
      display: flex;
      align-items: center;
      >img {
        width: 16px;
        height: 16px;
        margin-right: 2px;
      }
      >span {
        font-size: 12px;
      }
    }
  }
}
.content {
  width: 100%;
  height: calc(~"100% + 32px");
  overflow: hidden;
  overflow-y: auto;
  padding: 16px 24px 0;
  margin: -16px 0;
  >.lawsuit-process, >.lawsuit-info, >.lawsuit-table, >.lawsuit-note {
    width: 100%;
    padding: 0 16px;
    box-shadow: 0px 6px 9px 0px rgba(137, 142, 155, 0.12);
    border-radius: 4px;
    border: 1px solid #DCDEE2;
    >.header {
      padding: 10px 0;
      border-bottom: 1px solid #DCDEE3;
      display: flex;
      align-items: center;
      justify-content: space-between;
      >p {
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
  >.lawsuit-process {
    >.body {
      width: calc(~"100% + 32px");
      margin: 0 -16px;
      padding: 16px 16px 20px;
      overflow: hidden;
      overflow-x: auto;
      white-space: nowrap;
      >.list {
        list-style-type: none;
        display: flex;
        >li {
          display: flex;
          margin-left: 56px;
          >.left {
            margin-right: 6px;
            padding-top: 5px;
            >.circle {
              width: 8px;
              height: 8px;
              background: @primary-color;
              border-radius: 50%;
              position: relative;
              &::before {
                width: 120px;
                height: 1px;
                content: '';
                background: url('@/assets/images/dotted.png') no-repeat;
                position: absolute;
                top: 4px;
                right: 16px;
              }
            }
          }
          >.right {
            >.title {
              position: relative;
              font-weight: 600;
              margin-bottom: 12px;
              background-color: #fff;
              z-index: 2;
              display: inline-block;
              padding-right: 16px;
            }
            >.name {
              margin-bottom: 2px;
            }
            >.name, >.time {
              color: #636C78;
            }
            &:last-child {
              margin-right: 16px;
            }
          }
          &:first-child {
            margin-left: 0;
            >.left {
              >.circle {
                &::before {
                  background: none;
                }
              }
            }
          }
        }
      }
    }
    >.empty {
      text-align: center;
      margin-bottom: 12px;
      >img {
        width: 100px;
      }
      >p {
        color: #636C78
      }
    }
  }
  >.lawsuit-info {
    margin-top: 16px;
    >.body {
      padding: 16px 0;
      >.case-info {
        >p.title {
          color: #636c78;
          display: flex;
          align-items: center;
          >i {
            font-size: 16px;
            margin-right: 4px;
          }
          >span {
            font-weight: 600;
          }
        }
        >.case-item-container {
          margin-top: 12px;
          >.case-item {
            list-style-type: none;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            border-bottom: 1px solid #DCDEE2;
            border-right: 1px solid #DCDEE2;
            align-items: stretch;
            >li {
              border-top: 1px solid #DCDEE2;
              border-left: 1px solid #DCDEE2;
              display: flex;
              >.title {
                flex-shrink: 0;
                width: 98px;
                padding: 8px 12px;
                border-right: 1px solid #DCDEE2;
                background-color: #F2F4F7;
                display: flex;
                align-items: center;
              }
              >.value {
                width: 100%;
                padding: 8px 12px 3px;
                display: flex;
                align-items: flex-start;
                &:hover {
                  background-color: #fff7f5;
                }
                .values {
                  width: 100%;
                  display: flex;
                  align-items: center;
                  max-height: 48px;
                  line-height: 17px;
                  text-overflow: ellipsis;
                  white-space: normal;
                  overflow: hidden;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 3;
                  word-wrap: break-word;
                  word-break: break-all;
                }
                .icon {
                  font-size: 16px;
                  color: @primary-color;
                  cursor: pointer;
                  margin-left: 4px;
                }
              }
              &:last-child {
                grid-column-start: 2;
                grid-column-end: 5;
              }
            }
          }
        }
      }
      >.case-fields {
        margin-top: 16px;
         >.case-item-container {
          >.case-item {
            >li {
              &:last-child {
                grid-column-start: 2;
                grid-column-end: 5;
              }
            }
          }
         }
      }
    }
  }
  >.lawsuit-table {
    margin-top: 16px;
    padding-bottom: 18px;

    .tables-header {
      margin-bottom: 12px;
      display: flex;
      justify-content: flex-end;
    }
  }
  >.lawsuit-note {
    margin-top: 16px;
    margin-bottom: 16px;
    >.latest {
      padding: 8px 0 16px;
      >.note-content {
        background-color: #F8F9FB;
        padding: 10px 12px 10px 10px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        border-radius: 4px;
        word-break: break-all;
        > span {
          white-space: pre-wrap;
          font-weight: 400;
          line-height: 19px;
        }
      }
      >.note-sub {
        color: #636c78;
        line-height: 24px;
        margin-top: 4px;
        text-align: right;
        > .create-by {
          margin-right: 16px;
        }
      }
    }
    >.emptys {
      height: 93px;
      color: #8d959f;
      font-size: 12px;
      line-height: 20px;
      text-align: center;
      padding-top: 40px;
      font-weight: 400;
      >i {
        font-size: 16px;
        margin-right: 6px;
      }
    }
  }
}
.modal-alerts {
  margin: -16px -16px 16px;
}
.file-list {
  list-style-type: none;
  display: flex;
  flex-wrap: wrap;
  margin-top: 12px;
  margin-right: -12px;
  >li {
    display: flex;
    align-items: flex-start;
    padding: 8px 12px;
    background: #F2F4F7;
    border-radius: 4px;
    margin-right: 12px;
    margin-bottom: 10px;
    >i {
      font-size: 16px;
      color: #636C78;
      &:last-child {
        cursor: pointer;
      }
    }
    >span {
      line-height: 17px;
      margin-left: 4px;
      margin-right: 6px;
    }
  }
}
</style>
