<template>
  <div class="progress">
    <Tables
      v-if="columns.length"
      ref="mainTable"
      pageable
      searchable
      :apifun="apifun"
      resizable
      border="header"
      :columns="columns"
    ></Tables>

    <fullScreenDetail
      v-if="ui.showFullScreen"
      ref="fullScreenDetailRef"
      :case-id="caseId"
      :type="detailType"
      @on-close="closeFullScreen"
    ></fullScreenDetail>

    <approvalDrawer ref="approvalDrawerRef" :info="info" type="1" :approve-status="currentStatus" />
  </div>
</template>

<script setup>
import Tables from '_c/tables/tables.1'
import { getFraudList } from '@/api/letters'
import fullScreenDetail from '@/view/dy-casectrl/fullScreenDetail'
import { getAccountList } from '@/api/account'
import approvalDrawer from '@/view/dy-flow/components/approvalDrawer'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { ref, resolveComponent, nextTick } from 'vue'

const { t } = useI18n()
const store = useStore()
const route = useRoute()
const router = useRouter()
const approvalDrawerRef = ref(null)
const fullScreenDetailRef = ref(null)
const columns = ref([])
const apifun = ref({
  get: getFraudList,
  filterGetList: (data) => {
    data.list.map(c => {
      if (c.status === 1 || c.status === -1) {
        c._disabled = true
      } else {
        c._disabled = false
      }
      return c
    })
    return data
  }
})
const caseId = ref(null)
const detailType = ref('')
const ui = ref({
  showFullScreen: false
})
const sendBy = ref([])
const info = ref(null)
const currentStatus = ref('')

detailType.value = store.getters.isSupervisor ? 'teamLeader' : 'manage'
store.dispatch('getRefData', {
  func: ['getAvailableDepOrg', 'getAvailableDepAndTeam']
}).then(() => {
  getAccountList().then(res => {
    if (res) {
      res.data.list.forEach(c => {
        sendBy.value.push({ name: c.name, value: c.id })
      })
    }
  }).finally(() => {
    columns.value = [
      {
        title: '申请时间',
        key: 'applyTime',
        bindmap: 'formatDate'
      },
      {
        title: '申请人',
        key: 'applicantName'
      },
      {
        title: '申请说明',
        key: 'applyDesc',
        tooltip: true
      },
      {
        title: '审批状态',
        key: 'status',
        bindmap: 'mediateStatus'
      },
      {
        title: '案件编号',
        key: 'outSerialNo',
        render: (h, { row }) => {
          return h('span', {
            style: {
              cursor: 'pointer',
              color: 'rgb(255, 79, 31)'
            },
            onClick: () => {
              caseId.value = row.caseId
              ui.value.showFullScreen = true

              nextTick(() => {
                fullScreenDetailRef.value.ui.shows = true
              })
            }
          }, row.outSerialNo)
        }
      },
      {
        title: '客户姓名',
        key: 'name'
      },
      {
        title: '身份证号',
        key: 'idCard'
      },
      {
        title: '手机号',
        key: 'mobile'
      },
      {
        title: '贷款金额',
        key: 'amount',
        bindmap: 'money',
        align: 'right'
      },
      {
        title: '产品名称',
        key: 'productName'
      },
      {
        title: '催收机构',
        key: 'allotAgent',
        bindmap: 'collectionAgency'
      },
      {
        title: '分配机构',
        key: 'deptName'
      },
      {
        title: '分配部门',
        key: 'teamName'
      },
      {
        title: t('operation'),
        key: 'handle',
        width: 60,
        fixed: 'right',
        render: (h, { row }) => {
          return h(resolveComponent('Button'), {
            type: 'text',
            onClick: () => {
              info.value = {
                businessType: 0,
                applyId: row.id,
                agentType: row.agentType
              }

              currentStatus.value = ''
              switch (row.status) {
                case 0:
                  currentStatus.value = '0'
                  break
                case 1:
                  currentStatus.value = '1'
                  break
                case -1:
                  currentStatus.value = '2'
                  break
                case -2:
                  currentStatus.value = '3'
                  break
              }

              nextTick(() => {
                approvalDrawerRef.value.ui.showDrawer = true
              })
            }
          }, {
            default: () => '查看'
          })
        }
      },

      // search
      {
        title: '案件编号',
        submitKey: 'outSerialNos',
        search: 'only',
        type: 'textarea',
        focusText: t('searchsTip')
      },
      {
        title: '客户姓名',
        submitKey: 'names',
        search: 'only',
        type: 'textarea',
        focusText: t('searchsTip')
      },
      {
        title: '身份证号',
        submitKey: 'idCards',
        search: 'only',
        type: 'textarea',
        focusText: t('searchsTip')
      },
      {
        title: '手机号',
        submitKey: 'mobiles',
        search: 'only',
        type: 'textarea',
        focusText: t('searchsTip')
      },
      {
        title: '审核状态',
        key: 'statusList',
        search: 'only',
        type: 'select',
        submitType: 'array',
        searchItems: store.getters.antiFraudStatus,
        multiple: true,
        defaultValue: 0
      },
      {
        title: '分配机构',
        submitKey: 'deptIds',
        search: 'only',
        type: 'select',
        multiple: true,
        submitType: 'array',
        searchItems: store.getters.availableDepOrg,
        searchTitle: '机构'
      },
      {
        title: '分配部门',
        search: 'only',
        key: 'teamIds',
        type: 'select',
        multiple: true,
        submitType: 'array',
        searchItems: store.getters.availableAllTheTeams,
        searchTitle: '部门'
      },
      {
        title: '申请时间',
        key: 'applyTimeStr',
        search: 'only',
        type: 'date',
        daterange: true
      },
      {
        title: '申请人',
        key: 'applicants',
        search: 'only',
        type: 'select',
        multiple: true,
        searchItems: sendBy.value,
        submitType: 'array'
      },
      {
        title: '机构类型',
        key: 'allotAgent',
        search: 'only',
        type: 'select',
        searchItems: store.getters.collectionAgency
      }
    ]
  })
})
const closeFullScreen = () => {
  ui.value.showFullScreen = false
}
</script>

<style lang="less" scoped>
.progress {
  margin-top: 8px;
}
</style>
