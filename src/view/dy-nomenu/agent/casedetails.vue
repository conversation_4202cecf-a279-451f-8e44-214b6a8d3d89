<template>
  <div ref="caseDetail" v-waterMark v-disCopy class="case-detail">
    <!-- 管控 -->
    <!-- 管控详情 -->
    <ctrlDetail v-if="caseId" ref="ctrlDetail" :case-id="caseId" />
    <!-- 团队预测式外呼 -->
    <div v-if="isFifo && allotAgent !== 'personalFifo'" class="team-fifo">
      <p v-if="caseInfo">
        <span class="title"> 案件负责{{ $t("agent") }}: </span>
        <span class="value">
          {{ caseInfo.userName || "-" }}
        </span>
      </p>
      <p v-if="caseInfo">
        <span class="title">
          所属小组:
        </span>
        <span class="value">
          {{ caseInfo.teamName || "-" }}
        </span>
      </p>
      <p>
        <span class="title"> 案件是否分配给接听{{ $t("agent") }}: </span>
        <span class="value">
          {{ allotAgent ? "是" : "否" }}
        </span>
      </p>
    </div>

    <template
      v-if="
          !ifCaseStatus &&
          caseIds.length &&
          ui.showChangeArrow
      "
    >
      <div
        class="next-btn left-btn"
        :class="{
          disabled:
            !caseIds.length ||
            (caseIds.length && caseIds[0].id === caseId) ||
            ui.selfUrgeRefund ||
            ui.loadingContact
        }"
        @click="changeCase(caseIndex - 1, 'pre')"
      >
        <Icon custom="iconfont icon-fanhui" />
      </div>
      <div
        class="next-btn right-btn"
        :class="{
          disabled:
            !caseIds.length ||
            (caseIds.length && caseIds[caseIds.length - 1].id === caseId) ||
            ui.selfUrgeRefund ||
            ui.loadingContact
        }"
        @click="changeCase(caseIndex + 1, 'next')"
      >
        <Icon custom="iconfont icon-fanhui" />
      </div>
    </template>
    <template v-if="caseIds.length && isModal">
      <div
        class="next-btn left-btn"
        :class="{
          disabled:
            !caseIds.length ||
            (caseIds.length && caseIds[0].id === caseId) ||
            ui.selfUrgeRefund ||
            ui.loadingContact
        }"
        @click="changeModalCase(caseIndex, 'pre')"
      >
        <Icon custom="iconfont icon-fanhui" />
      </div>
      <div
        class="next-btn right-btn"
        :class="{
          disabled:
            !caseIds.length ||
            (caseIds.length && caseIds[caseIds.length - 1].id === caseId) ||
            ui.selfUrgeRefund ||
            ui.loadingContact
        }"
        @click="changeModalCase(caseIndex, 'next')"
      >
        <Icon custom="iconfont icon-fanhui" />
      </div>
    </template>
    <div v-if="caseInfo" class="agent-info">
      <div class="top">
        <div class="left">
          <!-- 标色 -->
          <div class="case-container">
            <img v-if="caseInfo.important" style="width: 54px; margin-right: 4px;" src="@/assets/images/important.png" alt="">
            <Tooltip v-if="caseInfo.warning" content="警告" placement="bottom-start" transfer>
              <Icon class="sign-warning" type="iconfont icon-jinggao" />
            </Tooltip>
            <Tooltip v-if="curColor.value !== 0" content="案件标色" placement="bottom-start" transfer>
              <div class="sign-color" :style="'background-color: ' + curColor.color"></div>
            </Tooltip>
            <!-- 债务人名称 -->
            <Tooltip :content="caseInfo.titleName" transfer>
              <span class="title">
                {{ (caseInfo && caseInfo.name ? caseInfo.name : "--") }}
              </span>
            </Tooltip>
            <!-- 案件状态 -->
            <div v-if="casingStatus.name" :class="casingStatus.value ? 'legal-status' : 'illegal-status'">
              {{ casingStatus.name }}
            </div>
          </div>
          <div v-if="curTag.length" class="divide"></div>
          <div class="tag-container">
            <template v-if="curTag.length">
              <div
                v-for="item in curTag"
                :key="item.value"
                class="tag"
                :style="
                  `background: ${item.bgColor}; color: ${item.textColor};`
                "
              >
                <Icon type="iconfont icon-biaoqian" />
                {{ item.name }}
              </div>
            </template>
          </div>
        </div>
        <div class="right">
          <template v-if="!ifCaseStatus">
            <Button type="primary" ghost icon="iconfont icon-biaoqian" @click="editTag">
              {{ $t('handTags') }}
            </Button>
            <Button v-if="caseInfo.cooperatorState !== 0" type="primary" ghost icon="iconfont icon-xiecui" style="margin-left: 12px;" @click="applyCooperation">
              申请{{ $t("coordinate") }}
            </Button>
            <Button
              v-if="
                caseInfo.cooperatorState === 0 &&
                  (caseInfo.userId === userId || isTeamUrgeCase)
              "
              type="primary"
              icon="iconfont icon-xiecui"
              ghost
              style="margin-left: 12px;"
              @click="endCooperation"
            >
              结束{{ $t("coordinate") }}
            </Button>
            <Button
              v-if="
                caseInfo.cooperatorState === 0 &&
                  caseInfo.userId !== userId &&
                  !isTeamUrgeCase
              "
              type="primary"
              icon="iconfont icon-xiecui"
              ghost
              style="margin-left: 12px;"
              @click="endCooperation"
            >
              完成{{ $t("coordinate") }}
            </Button>
            <Button type="primary" style="margin-left: 12px;" @click="changeStatus">
              修改案件状态
            </Button>
            <!-- 案件编辑 -->
            <Button v-if="userExtend.caseInfoModifySwitch" type="primary" style="margin-left: 12px;" @click="editCase">
              案件编辑
            </Button>
            <Button type="default" style="margin-left: 12px;" class="hover-primary" icon="iconfont icon-biaose" @click="signColor"></Button>
          </template>
          <template>
            <Button v-if="!caseInfo.warning" type="primary" ghost icon="iconfont icon-jinggao" @click="signWarning(1)">
              标记警告
            </Button>
            <Button v-else type="primary" ghost icon="iconfont icon-jinggao" @click="signWarning(0)">
              取消警告
            </Button>
            <Button class="p-12" type="primary" style="margin-left: 12px;" ghost icon="iconfont icon-biaoqian" @click="editTag">
              {{ $t('handTags') }}
            </Button>
            <Button class="p-12" type="primary" style="margin-left: 12px;" ghost icon="iconfont icon-tianjiapingyu" @click="addComment">
              添加评语
            </Button>
            <Button type="primary" style="margin-left: 12px;" @click="editCase">
              案件编辑
            </Button>
          </template>
        </div>
      </div>
      <div v-if="caseInfo" class="middle">
        <p>{{ $t("K_outSerialNo") }}：{{ caseInfo.outSerialNo || "--" }}</p>
        <p class="count">
          共债案件：<Button style="margin-top: -2px;" type="text" @click="turnToDebt">
            {{ caseInfo.conjointCount || 0 }}
          </Button>
        </p>
        <p>{{ $t("agent") }}：{{ caseInfo.userName }}</p>
        <p v-if="caseInfo.cooperatorName">
          {{ $t("coordinate") }}员：{{ caseInfo.cooperatorName }}
        </p>
      </div>
      <div v-if="showComment.content" class="comment">
        <div class="comment-box">
          <p class="title">
            最新评语：
          </p>
          <div class="content">
            <Tooltip class="tooltip-comment" :content="showComment.content" :max-width="800">
              <span class="contents">
                {{ showComment.content }}
              </span>
            </Tooltip>
          </div>
        </div>
        <div class="creates">
          <span class="create-by">
            {{ showComment.createBy }}
          </span>
          <span class="create-time">
            {{ showComment.createTime }}
          </span>
          <a class="check" @click="checkAllComments">
            <i class="iconfont icon-cuishoulishi"></i>
            查看历史评语
          </a>
        </div>
      </div>
    </div>

    <div class="case-info" :style="`height: ${ui.caseInfoHeight}px;`" @click="handleClickCase">
      <!-- 联系人 -->
      <div v-if="!ifCaseStatus" class="case-contacts">
        <div v-if="ui.selfUrgeRefund && !ui.ifHideDrag" class="refund-fold" @click="showDrags">
          <div class="top">
            <Icon type="iconfont icon-cuiji" />
          </div>
          <div class="bottom">
            <Icon type="iconfont icon-cuijizhankai" />
          </div>
        </div>
        <div class="card contacts-card">
          <div class="card-title">
            <span class="title">
              联系人
            </span>
            <Button v-if="!caseInfo.cmbcCaseDetailInfo" class="add-contact" icon="iconfont icon-tianjialianxiren" @click="openNewContact"></Button>
          </div>
          <div class="card-body">
            <RadioGroup v-model="ui.contactRadio" type="button" size="small" class="contact-radio">
              <Radio :label="1">
                有效
              </Radio>
              <Radio v-if="!caseInfo.cmbcCaseDetailInfo" :label="0">
                无效
              </Radio>
            </RadioGroup>

            <ul v-if="contacts.length && ui.contactRadio === 1" class="contacts-list">
              <li v-for="(item, index) in contacts" :key="item.id" :class="{ sign: item.sign }">
                <div class="contact-info">
                  <div class="top">
                    <div class="left">
                      <Tooltip :content="item.name" transfer class="full-tooltip">
                        <span class="name">{{ item.name }}</span>
                      </Tooltip>
                      <Tooltip v-if="item.warning" content="警告" transfer>
                        <Icon class="warning" type="iconfont icon-jinggao" />
                      </Tooltip>
                    </div>
                    <div class="right">
                      <Tooltip
                        :content="
                          ui.selfUrgeRefund
                            ? item.id === refundForm.contactsId
                              ? `填写${$t('reminder')}`
                              : `请先提交未完成${$t(
                                'reminder'
                              )}或结束当前通话后，再开启${$t('reminder')}`
                            : isInCall
                              ? `请先提交未完成${$t(
                                'reminder'
                              )}或结束当前通话后，再开启${$t('reminder')}`
                              : `填写${$t('reminder')}`
                        "
                        transfer
                      >
                        <Icon
                          class="icons refund"
                          type="iconfont icon-cuiji"
                          :class="
                            ui.selfUrgeRefund
                              ? item.id === refundForm.contactsId
                                ? 'active-contact'
                                : 'passive-contact'
                              : isInCall
                                ? 'passive-contact'
                                : ''
                          "
                          :disabled="
                            ui.selfUrgeRefund
                              ? item.id === refundForm.contactsId
                                ? false
                                : true
                              : isInCall
                                ? true
                                : false
                          "
                          @click="startRefund(item)"
                        />
                      </Tooltip>

                      <Poptip v-if="(userExtend.workPhoneAgent && userExtend.enableConnectedDuyan && $store.state.user.dyAgent) || (userExtend.workPhoneAgent && wellPhoneSwitch) || (wellPhoneSwitch && userExtend.enableConnectedDuyan && $store.state.user.dyAgent )" trigger="hover" popper-class="case-detail-poptip-more" placement="bottom-start" padding="0">
                        <Icon
                          class="icons calling refund"
                          type="iconfont icon-waihu"
                          :class="
                            isInCall
                              ? item.id === refundForm.contactsId
                                ? 'active-contact-call'
                                : 'passive-contact'
                              : ui.selfUrgeRefund
                                ? 'passive-contact'
                                : ''
                          "
                          :disabled="
                            isInCall
                              ? item.id === refundForm.contactsId
                                ? false
                                : true
                              : ui.selfUrgeRefund
                                ? true
                                : false
                          "
                        />
                        <template #content>
                          <ul class="more-btns">
                            <li
                              v-if="userExtend.enableConnectedDuyan && $store.state.user.dyAgent"
                              :class="
                                isInCall
                                  ? item.id === refundForm.contactsId
                                    ? 'active-contact-call'
                                    : 'passive-contact'
                                  : ui.selfUrgeRefund
                                    ? 'passive-contact'
                                    : ''
                              "
                              @click="startCall(item)"
                            >
                              <Icon
                                type="iconfont icon-hujiaozhongxin"
                                :class="
                                  isInCall
                                    ? item.id === refundForm.contactsId
                                      ? 'active-contact-call'
                                      : 'passive-contact'
                                    : ui.selfUrgeRefund
                                      ? 'passive-contact'
                                      : ''
                                "
                              />
                              <span>呼叫中心</span>
                            </li>
                            <li
                              v-if="userExtend.workPhoneAgent&& userExtend.wpCallPhone"
                              :class="
                                isInCall
                                  ? item.id === refundForm.contactsId
                                    ? 'active-contact-call'
                                    : 'passive-contact'
                                  : ui.selfUrgeRefund
                                    ? 'passive-contact'
                                    : ''"
                              style="padding-right:10px"
                            >
                              <Poptip trigger="hover" popper-class="case-detail-poptip-more" placement="right" padding="0">
                                <Icon type="iconfont icon-shouji1" style="font-size: 14px;" />
                                <span>工作手机</span>
                                <Icon type="ios-arrow-forward" style="margin-left:12px;margin-right:0" />
                                <template #content>
                                  <ul class="more-btns">
                                    <li @click="startCallPhone(item,2)">
                                      <span>自动选择</span>
                                    </li>
                                    <li @click="startCallPhone(item,0)">
                                      <span>SMI卡1</span>
                                    </li>
                                    <li @click="startCallPhone(item,1)">
                                      <span>SMI卡2</span>
                                    </li>
                                  </ul>
                                </template>
                              </Poptip>
                            </li>
                            <li
                              v-if="wellPhoneSwitch"
                              :class="
                                isInCall
                                  ? item.id === refundForm.contactsId
                                    ? 'active-contact-call'
                                    : 'passive-contact'
                                  : ui.selfUrgeRefund
                                    ? 'passive-contact'
                                    : ''
                              "
                              @click="startCallHJ(item)"
                            >
                              <Icon
                                type="iconfont icon-waihu"
                                size="16"
                                :class="
                                  isInCall
                                    ? item.id === refundForm.contactsId
                                      ? 'active-contact-call'
                                      : 'passive-contact'
                                    : ui.selfUrgeRefund
                                      ? 'passive-contact'
                                      : ''
                                "
                              />
                              <span>慧捷</span>
                            </li>
                          </ul>
                        </template>
                      </Poptip>
                      <!-- v-if="
                          (userExtend.enableConnectedDuyan &&
                            $store.state.user.dyAgent && !userExtend.workPhoneAgent) || (userExtend.workPhoneAgent && !(userExtend.enableConnectedDuyan &&
                            $store.state.user.dyAgent))" -->
                      <Tooltip
                        v-else
                        :content="
                          isInCall
                            ? item.id === refundForm.contactsId
                              ? '拨打电话'
                              : `请先提交未完成${$t(
                                'reminder'
                              )}或结束当前通话后，再拨打电话`
                            : ui.selfUrgeRefund
                              ? `请先提交未完成${$t(
                                'reminder'
                              )}或结束当前通话后，再拨打电话`
                              : '拨打电话'
                        "
                        transfer
                      >
                        <Icon
                          class="icons calling refund"
                          type="iconfont icon-waihu"
                          :class="
                            isInCall
                              ? item.id === refundForm.contactsId
                                ? 'active-contact-call'
                                : 'passive-contact'
                              : ui.selfUrgeRefund
                                ? 'passive-contact'
                                : ''
                          "
                          :disabled="
                            isInCall
                              ? item.id === refundForm.contactsId
                                ? false
                                : true
                              : ui.selfUrgeRefund
                                ? true
                                : false
                          "
                          @click="startCall(item)"
                        />
                      </Tooltip>
                      <!-- <Tooltip
                        v-if="
                          userExtend.isMessageEnabled && !caseInfo.cmbcCaseDetailInfo &&
                            (isOutsource ? userExtend.smsSwitch : true)
                        "
                        content="发送短信"
                        transfer
                      >
                        <Icon class="icons refund msg" type="iconfont icon-fasongduanxin" @click="startMessage(item)" />
                      </Tooltip> -->
                       <!-- 发送短信 -->
                       <Poptip v-if="(userExtend.isMessageEnabled&&!caseInfo.cmbcCaseDetailInfo && (isOutsource ? userExtend.smsSwitch : true))||userExtend.wpSendSms" trigger="hover" popper-class="case-detail-poptip-more" placement="bottom-start" padding="0">
                        <Icon class="icons refund msg" type="iconfont icon-fasongduanxin" style="font-size: 12px;" />
                        <template #content>
                          <ul class="more-btns">
                            <li v-if="userExtend.isMessageEnabled" @click="startMessage(item)">
                              <Icon type="iconfont icon-fasongduanxin" style="font-size: 12px;" />
                              <span>短信平台</span>
                            </li>
                            <!-- wpSendSms工作手机是否发送短信 -->
                            <li style="padding-right:10px" v-if="userExtend.wpSendSms">
                              <Poptip trigger="hover" popper-class="case-detail-poptip-more" placement="right" padding="0">
                                <Icon type="iconfont icon-shouji1" style="font-size: 14px;" />
                                <span>工作手机</span>
                                <Icon type="ios-arrow-forward" style="margin-left:12px;margin-right:0" />
                                <template #content>
                                  <ul class="more-btns">
                                    <li @click="sendWorkSms(item,2)">
                                      <span>自动选择</span>
                                    </li>
                                    <li @click="sendWorkSms(item,0)">
                                      <span>SMI卡1</span>
                                    </li>
                                    <li @click="sendWorkSms(item,1)">
                                      <span>SMI卡2</span>
                                    </li>
                                  </ul>
                                </template>
                              </Poptip>
                            </li>
                          </ul>
                        </template>
                      </Poptip>
                      <Tooltip v-if="userExtend.wpAddWechat" content="添加微信" placement="bottom" class="refund">
                        <div @click="wxAdd(item)">
                          <Icon type="md-person-add" size="16" />
                        </div>
                      </Tooltip>
                      <Poptip v-if="!caseInfo.cmbcCaseDetailInfo" trigger="hover" popper-class="case-detail-poptip-more" placement="bottom-start" padding="0">
                        <Icon class="icons more refund" type="iconfont icon-gengduo" />
                        <template #content>
                          <ul class="more-btns">
                            <li @click="editContact(item)">
                              <Icon type="iconfont icon-bianji" />
                              <span>编辑</span>
                            </li>
                            <li :class="{ 'warning-li': !!item.sign }" @click="toggleParam(item, 'sign')">
                              <Icon type="iconfont icon-zhiding" />
                              <span>置顶</span>
                            </li>
                            <li :class="{ 'warning-li': !!item.warning }" @click="toggleParam(item, 'warning')">
                              <Icon type="iconfont icon-jinggao" />
                              <span>警告</span>
                            </li>
                            <li @click="setInvalid(item, 'invalid', index)">
                              <Icon type="iconfont icon-wuxiao" />
                              <span>无效</span>
                            </li>
                          </ul>
                        </template>
                      </Poptip>
                    </div>
                  </div>
                  <div class="middle">
                    <template v-if="item.relationType">
                      <Tooltip :disabled="item.relationType.length <= 6" :content="item.relationType" transfer>
                        <span class="relation">
                          {{ item.relationType }}
                        </span>
                      </Tooltip>
                    </template>
                    <p class="mobile">
                      {{ hidePhone(item.mobile) }}
                    </p>
                  </div>
                  <div v-if="item.desc" class="bottom">
                    <Tooltip :disabled="item.desc.length <= 10" :content="item.desc" :max-width="410" transfer>
                      备注：<span class="desc">{{ item.desc }}</span>
                    </Tooltip>
                  </div>
                </div>
              </li>
            </ul>
            <ul v-else-if="invalidContacts.length && ui.contactRadio === 0" class="contacts-list">
              <li v-for="(item, index) in invalidContacts" :key="item.id">
                <div class="contact-info">
                  <div class="top">
                    <div class="left">
                      <Tooltip :content="item.name" transfer class="full-tooltip">
                        <span class="name">{{ item.name }}</span>
                      </Tooltip>
                    </div>
                    <div class="right">
                      <Tooltip content="恢复">
                        <Icon class="icons refund" type="ios-undo" @click="setInvalid(item, 'recycle', index)" />
                      </Tooltip>
                    </div>
                  </div>
                  <div class="middle">
                    <template v-if="item.relationType">
                      <Tooltip :disabled="item.relationType.length <= 6" :content="item.relationType" transfer>
                        <span class="relation">
                          {{ item.relationType }}
                        </span>
                      </Tooltip>
                    </template>
                    <p class="mobile">
                      {{ hidePhone(item.mobile) }}
                    </p>
                  </div>
                  <div v-if="item.desc" class="bottom">
                    <Tooltip :disabled="item.desc.length <= 10" :content="item.desc" :max-width="410" transfer>
                      备注：<span class="desc">{{ item.desc }}</span>
                    </Tooltip>
                  </div>
                </div>
              </li>
            </ul>
            <div v-else class="empty-data">
              <img src="@/assets/images/error_data.png" alt="">
              <p>
                暂无联系人，你可<a @click="openNewContact">
                  添加联系人
                </a>
              </p>
            </div>

            <Spin v-if="ui.loadingContact" size="large" fix></Spin>
          </div>
        </div>
      </div>
      <!-- 填写催记 -->
      <div v-if="ui.selfUrgeRefund" v-show="ui.ifHideDrag" v-draggleVue3="draggableValue" class="refund-box">
        <div :ref="handleId" class="refund-title">
          <span class="title"> 填写{{ $t("reminderRecord") }} </span>

          <div class="refund-btns">
            <a v-if="!refundForm.contactsId" class="refresh" @click="refresh">
              <Icon type="iconfont icon-refresh" />
              <span>刷新</span>
            </a>

            <a class="close-icon" @click="hideDrags">
              <Icon type="iconfont icon-cuijishouqi" />
              <span>收起</span>
            </a>
          </div>
        </div>
        <div class="refund-content">
          <Spin v-if="ui.loadingContact || ui.loadingOperation" size="large" fix></Spin>
          <Form v-if="refundForm.contactsId || refundForm.cmbcConName" id="refundForm" ref="refundForm" :label-width="76" label-position="right" :model="refundForm" :rules="refundFormRole">
            <FormItem :label="$t('contact') + ':'">
              <div class="contact-refund-info">
                <p v-if="refundForm.contactName || refundForm.warning || refundForm.cmbcConName" class="name">
                  <span v-if="refundForm.contactName || refundForm.cmbcConName">
                    {{ refundForm.contactName || refundForm.cmbcConName }}
                  </span>
                  <Icon v-if="refundForm.warning" type="iconfont icon-jinggao" />
                </p>
                <p v-if="refundForm.relationType || refundForm.cmbcRelation" class="relation">
                  {{ refundForm.relationType || refundForm.cmbcRelation }}
                </p>
                <p class="mobile">
                  {{ hidePhone(refundForm.mobile || refundForm.cmbcConMobile) }}
                </p>
              </div>
            </FormItem>
            <FormItem :label="`${$t('collection')}进程:`" prop="operationStates">
              <Select v-model="operationStates" :disabled="!ui.selfUrgeRefund" transfer @on-change="changeProcessing">
                <Option v-for="item in operationStateList" :key="item.value" :value="item.value">
                  {{ item.rename || item.name }}
                </Option>
              </Select>
            </FormItem>
            <FormItem :label="$t('k_talkOverResult') + ':'" prop="actionType">
              <RadioGroup v-if="(userExtend.operationConfigLinkSwitch && refundResultsList.length) || !userExtend.operationConfigLinkSwitch" v-model="refundForm.actionType" class="refund-result" @on-change="changeActionType">
                <template v-for="item in refundResultsList">
                  <Radio
                    v-if="
                      item.value !== -2 &&
                        item.value !== -3 &&
                        item.value !== -4
                    "
                    :key="item.value"
                    :disabled="!ui.selfUrgeRefund"
                    class="item"
                    :class="'s' + item.value"
                    :label="item.value"
                  >
                    <span>{{ item.rename || item.name }}</span>
                  </Radio>
                </template>
              </RadioGroup>
              <p v-else style="color: #636C78; margin-top: 4px;">暂无可选项，请联系管理员配置</p>
            </FormItem>
            <FormItem v-show="refundForm.actionType === 0" label="PTP金额:" prop="ptpAmount">
              <Input v-model.trim="refundForm.ptpAmount" class="width-120" :disabled="!ui.selfUrgeRefund" :number="true" prefix="md-cash" placeholder="PTP金额" />
            </FormItem>
            <FormItem v-show="refundForm.actionType === 0" label="PTP时间:" prop="ptpTime">
              <DatePicker v-model="refundForm.ptpTime" class="width-200" type="date" :disabled="!ui.selfUrgeRefund" placeholder="PTP时间" transfer></DatePicker>
            </FormItem>
            <FormItem v-show="refundForm.actionType === 5" :label="$t('reliefMent') + ':'" prop="reduceAmount">
              <Input v-model.trim="refundForm.reduceAmount" class="width-120" :number="true" :disabled="!ui.selfUrgeRefund" prefix="md-cash" :placeholder="$t('reliefMent')" />
            </FormItem>
            <FormItem v-show="refundForm.actionType === 5" :label="$t('reliefRemark') + ':'" prop="reduceDesc">
              <Input v-model.trim="refundForm.reduceDesc" :disabled="!ui.selfUrgeRefund" type="textarea" show-word-limit :rows="2" :placeholder="$t('inputReliefRemark')" :maxlength="500" />
            </FormItem>
            <FormItem v-show="refundForm.actionType === 5" label="上传凭证:" class="upload-file">
              <Upload action="" :show-upload-list="false" :before-upload="beforeUploadNormalPicRefund">
                <Button icon="iconfont icon-shangchuantupian" class="h-32" :loading="ui.uploadLoading" :disabled="!ui.selfUrgeRefund">
                  {{ $t("uploadFiles") }}
                </Button>
              </Upload>
              <div class="tips">
                注：上传文件大小总共不能超过10M，可用扩展名jpg、png、pdf、docx、wav等
              </div>
              <div v-if="refundFile.length" class="file-list">
                <div v-for="(item, index) in refundFile" :key="item">
                  <Tooltip :content="item" max-width="400" transfer>
                    <div class="file-box">
                      <Icon class="file" type="iconfont icon-wenjian" />
                      <span class="file-name">
                        {{ item }}
                      </span>
                    </div>
                  </Tooltip>
                  <Icon class="delete" type="iconfont icon-anjianguanbi" @click="deleteFile(index, 'refundFile')" />
                </div>
              </div>
            </FormItem>
            <FormItem v-if="(userExtend.operationConfigLinkSwitch && refundForm.actionType !== null) || !userExtend.operationConfigLinkSwitch" :label="$t('k_callResult') + ':'" prop="callType">
              <RadioGroup v-if="(userExtend.operationConfigLinkSwitch && callResultList.length) || !userExtend.operationConfigLinkSwitch" v-model="refundForm.callType" class="refund-call-result" @on-change="changeCallType">
                <template v-for="item in callResultList">
                  <Radio v-if="item.value !== -3 && item.value !== -4" :key="item.value" :disabled="!ui.selfUrgeRefund" class="item" :class="'s' + item.value" :label="item.value">
                    <span>{{ item.rename || item.name }}</span>
                  </Radio>
                </template>
              </RadioGroup>
              <p v-else style="color: #636C78; margin-top: 4px;">暂无可选项，请联系管理员配置</p>
            </FormItem>
            <FormItem :label="$t('k_refundMark') + ':'" prop="desc">
              <Input v-model.trim="refundForm.desc" :disabled="!ui.selfUrgeRefund" type="textarea" :rows="2" show-word-limit :placeholder="$t('talkOverRecord')" :maxlength="500" />
            </FormItem>
            <FormItem label="客户标签：" prop="clientLabelId">
              <Select v-model="refundForm.clientLabelId" :disabled="!ui.selfUrgeRefund" transfer>
                <Option v-for="item in customLabels" :key="item.id" :value="item.id">
                  {{ item.name }}
                </Option>
              </Select>
            </FormItem>
            <FormItem label="下次跟进:">
              <DatePicker ref="datePicker" v-model="refundForm.nextTime" class="width-180" :disabled="!ui.selfUrgeRefund" type="date" transfer :placeholder="$t('nextFollowUpTime')"></DatePicker>
              <ul class="next-exprise">
                <li @click="setNextTime(1)">
                  {{ $t("today") }}
                </li>
                <li @click="setNextTime(2)">
                  {{ $t("tomorrow") }}
                </li>
                <li @click="setNextTime(3)">
                  {{ $t("dayAfterTomorrow") }}
                </li>
              </ul>
            </FormItem>
            <FormItem v-show="userExtend.operationHiddenSwitch" :label="`隐藏${$t('reminder')}：`">
              <div class="hide-refund">
                <i-switch v-model="refundForm.isHidden" :true-value="1" :false-value="0" size="small"></i-switch>
                <span class="hide-text">
                  {{ refundForm.isHidden === 1 ? "隐藏" : "不隐藏" }}
                </span>
              </div>
            </FormItem>
          </Form>
          <p v-else class="no-contact-tip">
            {{ refundForm.contactName }}
          </p>
        </div>
        <div class="subStyle">
          <Button class="confirm" :loading="ui.submitingRefund" :disabled="!ui.selfUrgeRefund" type="primary" @click="refundSubmit('submit')">
            {{ $t("elTalkoverResult") }}
          </Button>
          <Button v-if="userExtend.writeOperationSwitch" class="cancel" :disabled="!ui.selfUrgeRefund" type="default" :loading="ui.submitingRefund" @click="refundSubmit('unSubmit')">
            {{ $t("unElTalkoverResult") }}
          </Button>
          <Tooltip v-if="userExtend.writeOperationSwitch" placement="bottom" transfer>
            <Icon class="info" type="ios-help-circle-outline" />
            <template #content>
              <div style="white-space: normal;">
                <p>
                  若开了外呼功能，不填写{{
                    $t("reminder")
                  }}将生成带电话记录的{{ $t("reminder") }}，但无{{
                    $t("collection")
                  }}信息，若无外呼功能，不填写{{ $t("reminder") }}则不生成{{
                    $t("reminder")
                  }}
                </p>
              </div>
            </template>
          </Tooltip>
        </div>
      </div>
      <!-- 案件信息 -->
      <div id="detail" class="case-details">
        <!-- 案件信息 -->
        <div class="card">
          <Tabs v-model="tabName" :animated="false" class="table-tabs" @on-click="tabsChange">
            <TabPane label="案件信息" name="0">
              <div class="tabPane-content">
                <div class="caseinfo case-man-info">
                  <p class="title">
                    <Icon type="iconfont icon-anjianbiaoqian" />
                    <span>案人信息</span>
                  </p>
                  <div class="case-item-container">
                    <ul v-for="n in caseUserRows" :key="n" class="case-item">
                      <template v-for="index in ui.caseInfoColNum">
                        <li
                          v-if="getItemValue(n, index, 'caseUserInfo', 'name')"
                          :key="index"
                          :class="{
                            'sign-field': colorField.includes(
                              getItemValue(n, index, 'caseUserInfo', 'key')
                            )
                          }"
                        >
                          <div class="key">
                            {{ getItemValue(n, index, "caseUserInfo", "name") }}
                          </div>
                          <div
                            v-if="
                              getItemValue(n, index, 'caseUserInfo', 'value') &&
                                String(getItemValue(n, index, 'caseUserInfo', 'value'))
                                  .length <= 30
                            "
                            class="value"
                          >
                            <div>
                              {{ getItemValue(n, index, "caseUserInfo", "value") }}
                              <Poptip
                                placement="bottom-start"
                                transfer
                                padding="0"
                                :offset="-13"
                                trigger="hover"
                                @on-popper-show="
                                  popperShow(
                                    getItemValue(n, index, 'caseUserInfo', 'value')
                                  )
                                "
                              >
                                <Icon
                                  v-if="
                                    getItemValue(
                                      n,
                                      index,
                                      'caseUserInfo',
                                      'key'
                                    ) === 'id_card'
                                  "
                                  class="icon"
                                  type="iconfont icon-chakanxiangqing"
                                />
                                <template #content>
                                  <div class="case-detail-search-content">
                                    <p class="title">
                                      身份证查询信息
                                    </p>
                                    <div>
                                      <p>
                                        <span>发证地：</span>{{ idCardInfo.area }}
                                      </p>
                                      <p>
                                        <span>出生日期：</span>{{ idCardInfo.birth }}
                                      </p>
                                      <p><span>性别：</span>{{ idCardInfo.sex }}</p>
                                      <p><span>年龄：</span>{{ idCardInfo.age }}</p>
                                    </div>
                                  </div>
                                </template>
                              </Poptip>
                            </div>
                          </div>
                          <Tooltip
                            v-else
                            :content="
                              getItemValue(n, index, 'caseUserInfo', 'value')
                            "
                            :max-width="410"
                            transfer
                            placement="bottom"
                          >
                            <div class="values">
                              {{ getItemValue(n, index, "caseUserInfo", "value") }}
                            </div>
                          </Tooltip>
                        </li>
                      </template>
                    </ul>
                  </div>
                </div>
                <div class="caseinfo case-base-info">
                  <p class="title">
                    <Icon type="iconfont icon-anjianbiaoqian" />
                    <span>案件信息</span>
                  </p>
                  <div class="case-item-container">
                    <ul v-for="n in caseBaseRows" :key="n" class="case-item">
                      <template v-for="index in ui.caseInfoColNum">
                        <li
                          v-if="getItemValue(n, index, 'caseBaseInfo', 'name')"
                          :key="index"
                          :class="{
                            'sign-field': colorField.includes(
                              getItemValue(n, index, 'caseBaseInfo', 'key')
                            )
                          }"
                        >
                          <div class="key">
                            {{ getItemValue(n, index, "caseBaseInfo", "name") }}
                          </div>
                          <div
                            v-if="
                              getItemValue(n, index, 'caseBaseInfo', 'value') &&
                                String(getItemValue(n, index, 'caseBaseInfo', 'value'))
                                  .length <= 30
                            "
                            class="value"
                          >
                            <div>
                              {{ getItemValue(n, index, "caseBaseInfo", "value") }}
                              <Poptip
                                placement="bottom-start"
                                transfer
                                padding="0"
                                :offset="-13"
                                trigger="click"
                                @on-popper-show="popperShowMS(getItemValue(n, index, 'caseBaseInfo', 'key'), n, index)"
                              >
                                <Icon
                                  v-if="
                                    getItemValue(
                                      n,
                                      index,
                                      'caseBaseInfo',
                                      'key'
                                    ) === 'custName' || getItemValue(
                                      n,
                                      index,
                                      'caseBaseInfo',
                                      'key'
                                    ) === 'cstCrdtNo'
                                  "
                                  class="icon"
                                  type="iconfont icon-chakanxiangqing"
                                />
                                <template #content>
                                  <div class="case-detail-search-content">
                                    <p class="title">
                                      {{ getItemValue(n, index, 'caseBaseInfo', 'key') === 'custName' ? '客户姓名查询' : '身份证号查询' }}
                                    </p>
                                    <div>
                                      <p>
                                        <span>信息查询：</span>{{ partMSInfo.partCustName || partMSInfo.partCstCrdtNo }}
                                      </p>
                                    </div>
                                  </div>
                                </template>
                              </Poptip>
                            </div>
                          </div>
                          <Tooltip v-else :content="getItemValue(n, index, 'caseBaseInfo', 'value')" :max-width="410" transfer placement="bottom">
                            <div class="values">
                              {{ getItemValue(n, index, "caseBaseInfo", "value") }}
                            </div>
                          </Tooltip>
                        </li>
                      </template>
                    </ul>
                  </div>
                </div>
                <!-- 账户信息 -->
                <div v-if="accountInfo.length" class="caseinfo case-account-info">
                  <p class="title">
                    <Icon type="iconfont icon-anjianbiaoqian" />
                    <span>账户信息</span>
                  </p>
                  <Tabs class="account-tab" type="card" :animated="false" @on-click="changeAccount">
                    <TabPane v-for="(item, index) in allAccount" :key="index" :label="`账户${index + 1}`">
                      <div class="case-item-container">
                        <ul v-for="n in caseAccountRows[index]" :key="n" class="case-item">
                          <template v-for="index1 in ui.caseInfoColNum" :key="index1">
                            <li>
                              <div class="key">
                                {{ getItemValue(n, index1, "accountInfo", "name") }}
                              </div>
                              <div v-if="String(getItemValue(n, index1, 'accountInfo', 'value')).length <= 30 || getItemValue(n,index1,'accountInfo','key') === 'cardNo'" class="value">
                                <div>
                                  {{ getItemValue(n, index1, "accountInfo", "value") }}
                                  <Poptip placement="bottom-start" transfer padding="0" :offset="-13" trigger="click" @on-popper-show="popperShowMS(getItemValue(n, index1, 'accountInfo', 'key'), n, index1)">
                                    <Icon
                                      v-if="
                                        getItemValue(
                                          n,
                                          index1,
                                          'accountInfo',
                                          'key'
                                        ) === 'cardNo'
                                      "
                                      class="icon"
                                      type="iconfont icon-chakanxiangqing"
                                    />
                                    <template #content>
                                      <div class="case-detail-search-content">
                                        <p class="title">
                                          卡号查询信息
                                        </p>
                                        <div>
                                          <p>
                                            <span>卡号：</span>{{ partMSInfo.partCardNoList && partMSInfo.partCardNoList.join(',') }}
                                          </p>
                                        </div>
                                      </div>
                                    </template>
                                  </Poptip>
                                </div>
                              </div>
                              <Tooltip v-else :content="getItemValue(n, index1, 'accountInfo', 'value')" :max-width="410" transfer placement="bottom">
                                <div class="values">
                                  {{ getItemValue(n, index1, "accountInfo", "value") }}
                                </div>
                              </Tooltip>
                            </li>
                          </template>
                        </ul>
                      </div>
                    </TabPane>
                  </Tabs>
                </div>
                <!-- 还款计划 -->
                <div v-if="refundColumns.length" class="caseinfo talk-over-info">
                  <p class="title">
                    <Icon type="iconfont icon-anjianbiaoqian" />
                    <span>还款计划</span>
                  </p>
                  <div class="case-item-container">
                    <Tables v-if="refundColumns.length" ref="refundTable" :columns="refundColumns" :apifun="refundApifun" :inject-search-data="refundInjectSearchData" :height="216" style="margin: 16px 0;" pageable></Tables>
                  </div>
                </div>
              </div>
            </TabPane>
            <TabPane v-for="(item,index) in loanList" :key="index" :label="'借据'+item.label" :name="item.label">
              <div class="tabPane-content">
                <loanReceiptDetail v-if="item.label==tabName" :receipt-info="item" :case-id="caseId"></loanReceiptDetail>
              </div>
            </TabPane>
          </Tabs>
        </div>
        <!-- 催收信息 -->
        <div class="card">
          <div class="title">
            {{ $t("collection") }}信息
          </div>
          <div class="content">
            <div class="caseinfo talk-over-info">
              <div class="case-item-container">
                <ul v-for="n in caseTalkRows" :key="n" class="case-item">
                  <template v-for="index in ui.caseInfoColNum">
                    <li
                      v-if="getItemValue(n, index, 'caseTalkOver', 'name')"
                      :key="index"
                      :class="{
                        'sign-field': colorField.includes(
                          getItemValue(n, index, 'caseTalkOver', 'key')
                        )
                      }"
                    >
                      <div class="key">
                        {{ getItemValue(n, index, "caseTalkOver", "name") }}
                      </div>
                      <div
                        v-if="
                          getItemValue(n, index, 'caseTalkOver', 'value') &&
                            String(getItemValue(n, index, 'caseTalkOver', 'value'))
                              .length <= 30
                        "
                        class="value"
                      >
                        {{ getItemValue(n, index, "caseTalkOver", "value") }}
                      </div>
                      <Tooltip
                        v-else
                        :content="
                          getItemValue(n, index, 'caseTalkOver', 'value')
                        "
                        :max-width="410"
                        transfer
                        placement="bottom"
                      >
                        <div class="values">
                          {{ getItemValue(n, index, "caseTalkOver", "value") }}
                        </div>
                      </Tooltip>
                    </li>
                  </template>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- <div v-if="refundColumns.length" class="card">
          <div class="title">
            还款计划
          </div>
          <Tables v-if="refundColumns.length" ref="refundTable" :columns="refundColumns" :apifun="refundApifun" :inject-search-data="refundInjectSearchData" :height="216" style="margin: 16px 0;" pageable></Tables>
        </div> -->
        <!-- 催收小结 -->
        <div v-if="userExtend.noteDisplaySwitch" class="card">
          <div class="title">
            <span>{{ $t("collection") }}小结</span>
            <div class="btns">
              <Tooltip :content="`历史${$t('collection')}小结`" style="margin-right: 8px;" transfer>
                <Button icon="iconfont icon-cuishoulishi" class="hover-primary" size="small" :disabled="!talkContent.content" @click="showTalkNotes"></Button>
              </Tooltip>
              <Tooltip
                :content="`添加${$t('collection')}小结`"
                transfer
              >
                <Button icon="iconfont icon-tianjialianxiren" class="hover-primary" size="small" @click="addTalkNote"></Button>
              </Tooltip>
            </div>
          </div>
          <div class="content">
            <div v-if="talkContent.content" class="talkover-info">
              <template v-if="!ui.editTalkNote">
                <div class="talkover-content">
                  <span>{{ talkContent.content }}</span>
                  <Button
                    v-if="
                        talkContent.content &&
                        !ifCaseStatus
                    "
                    icon="iconfont icon-bianji"
                    type="text"
                    @click="editTalkNote"
                  >
                    编辑
                  </Button>
                </div>
                <div class="talkover-sub">
                  <span class="create-by">
                    {{ talkContent.createBy }}
                  </span>
                  <span class="create-time">
                    {{ talkContent.createTime }}
                  </span>
                </div>
              </template>
              <div v-else class="edit-talkover">
                <Input v-model="editTalkNotes" class="talkover-input" type="textarea" :placeholder="`填写${$t('collection')}小结`" :rows="2" :maxlength="600" show-word-limit autosize></Input>
                <div class="btns">
                  <Button type="primary" style="margin-right: 12px;" @click="confirmEditTalk">
                    确定
                  </Button>
                  <Button @click="cancelEditTalk">
                    取消
                  </Button>
                </div>
              </div>
            </div>
            <div v-else class="no-talkover">
              <Icon type="iconfont icon-kongbai" />
              <span>{{ $t("collection") }}小结暂无内容</span>
            </div>
          </div>
        </div>
        <!-- 列表 -->
        <div class="card tables-card">
          <Tabs v-model="curName" :animated="false" class="table-tabs" @on-click="tabsClick">
            <TabPane label="电催" name="1">
              <Tables
                v-if="
                  (([0, 2].includes(tags) &&
                    recordColumns.length &&
                    recordInjectSearchData) ||
                    ([1, 3].includes(tags) &&
                    talkCaseColumns.length &&
                    talkCaseSearchData)) &&
                    curName === '1'
                "
                :ref="[0, 2].includes(tags) ? 'recordTable' : 'talkCaseTable'"
                :height="400"
                :columns="[0, 2].includes(tags) ? recordColumns : talkCaseColumns"
                :apifun="[0, 2].includes(tags) ? recordApiFun : talkCaseApiFun"
                :inject-search-data="
                  [0, 2].includes(tags) ? recordInjectSearchData : talkCaseSearchData
                "
                editable
                pageable
              >
                <template #header>
                  <div class="tables-header">
                    <RadioGroup v-model="tags" type="button" size="small" @on-change="handleTalkChange">
                      <Radio :label="0"> 本案{{ $t("reminderRecord") }} </Radio>
                      <Radio :label="2"> 本案历史{{ $t("reminderRecord") }} </Radio>
                      <Radio :label="1"> 共债{{ $t("reminderRecord") }} </Radio>
                      <Radio :label="3"> 共债历史{{ $t("reminderRecord") }} </Radio>
                    </RadioGroup>
                    <p style="display: inline-block; margin-left: 16px; color: #636C78;">历史催记展示6个月以上的催记</p>
                  </div>
                </template>
              </Tables>
            </TabPane>

            <TabPane
              v-if="
                userExtend.mediationSwitch &&
                  (currentUserInfo.mediator &&
                  currentUserInfo.isCanMediate)
              "
              label="在线调解"
              name="10"
            >
              <Tables
                v-if="
                  curName === '10' &&
                    ((tags === 0 && mediRecord.length) ||
                    (tags === 1 && mediText.length)) ||
                    (tags === 2 && VideoMediation.length)
                "
                ref="mediation"
                :height="400"
                :columns="tags === 0 ? mediRecord : (tags === 1 ? mediText : VideoMediation)"
                :apifun="tags === 0 ? mediRecordApi : (tags === 1 ? mediTextApi : VideoMediationApi)"
                :inject-search-data="
                  tags === 0
                    ? mediRecordInjectSearchData
                    : (tags === 1 ? mediTextInjectSearchData : mediRecordInjectSearchData)
                "
                pageable
              >
                <template #header>
                  <div class="tables-header">
                    <RadioGroup v-model="tags" type="button" size="small" @on-change="handleVisitSearch">
                      <Radio :label="0">
                        文书申请记录
                      </Radio>
                      <Radio :label="1">
                        签署文书
                      </Radio>
                      <Radio :label="2">
                        视频调解记录
                      </Radio>
                    </RadioGroup>
                    <Button type="primary" style="float: right;margin-left:12px" @click="applyMediate">
                      文书申请
                    </Button>
                    <Button
                      v-if="
                          currentUserInfo.mediator &&
                          currentUserInfo.isCanVideo"
                      type="primary"
                      style="float: right"
                      :disabled="mediating"
                      @click="showMediation"
                    >
                      视频调解
                    </Button>
                  </div>
                </template>
              </Tables>
            </TabPane>

            <TabPane label="共债案件" name="2">
              <Tables v-if="debtColumns.length && curName === '2'" ref="debtTable" :height="400" :columns="debtColumns" :apifun="debtApiFun" :inject-search-data="debtSearchData" pageable>
                <template #header>
                  <div class="tables-header">
                    <RadioGroup v-model="tags" type="button" size="small" @on-change="handleDebtSearch">
                      <Radio :label="0">
                        在案
                      </Radio>
                      <Radio :label="1">
                        历史
                      </Radio>
                    </RadioGroup>
                    <div class="counting">
                      <span v-if="debtColumns.length && curName === '2'">
                        结果统计：共债案件<span> {{ conjo.total }} </span>，{{
                          $t("baile")
                        }}金额(含本案)共<span>
                          {{ formatMoney(conjo.totalConjointAmount) }}
                        </span>
                        元，其中本案{{ $t("baile") }}金额
                        <span>{{ formatMoney(conjo.currentAmount) }}</span> 元
                      </span>
                    </div>
                  </div>
                </template>
              </Tables>
            </TabPane>
            <TabPane label="地址" name="3">
              <Tables v-if="addressColumns.length && curName === '3'" ref="addressTable" :height="400" :columns="addressColumns" :apifun="addressApifun" :inject-search-data="addressInjectSearchData" pageable multselect>
                <template #header>
                  <div class="tables-header">
                    <Button
                      type="primary"
                      @click="addNewAddress"
                    >
                      新增地址
                    </Button>
                    <Button
                      v-if="
                        userExtend.visitSwitch &&
                          (isOutsource ? userExtend.depVisitSwitch : true)
                      "
                      type="primary"
                      style="margin-left: 8px;"
                      ghost
                      @click="applyOutVisit"
                    >
                      分配外访
                    </Button>
                  </div>
                </template>
              </Tables>
            </TabPane>
            <TabPane label="外访" name="4">
              <Tables
                v-if="
                  ((tags === 0 && visitRecord.length) ||
                    (tags === 1 && outvisitColumns.length)) &&
                    curName === '4'
                "
                ref="outvisitTable"
                :height="400"
                :columns="tags === 0 ? visitRecord : outvisitColumns"
                :apifun="tags === 0 ? visitRecordApifun : outvisitApifun"
                :inject-search-data="
                  tags === 0
                    ? visitRecordInjectSearchData
                    : outvisitInjectSearchData
                "
                pageable
              >
                <template #header>
                  <div class="tables-header">
                    <RadioGroup v-model="tags" type="button" size="small" @on-change="handleVisitSearch">
                      <Radio :label="0">
                        外访记录
                      </Radio>
                      <Radio :label="1">
                        外访申请
                      </Radio>
                    </RadioGroup>
                    <Button
                      v-if="
                        userExtend.visitSwitch &&
                          (isOutsource ? userExtend.depVisitSwitch : true)
                      "
                      type="primary"
                      style="float: right;"
                      @click="applyToVisit"
                    >
                      分配外访
                    </Button>
                    <div class="counting">
                      <span
                        v-if="
                          visitRecord.length && curName === '4' && tags === 0
                        "
                      >
                        结果统计：外访记录<span> {{ visitCount.total }} </span>条，待外访<span> {{ visitCount.wait }} </span>，外访中<span> {{ visitCount.ing }} </span>，已完成<span> {{ visitCount.done }} </span>，已过期<span> {{ visitCount.outDate }} </span>
                      </span>

                      <span
                        v-if="
                          outvisitColumns.length &&
                            curName === '4' &&
                            tags === 1
                        "
                      >
                        结果统计：申请记录<span> {{ visitCount.total }} </span>条，待审核<span> {{ visitCount.wait }} </span>，审核通过<span> {{ visitCount.ing }} </span>，审核拒绝<span> {{ visitCount.done }} </span>，已取消<span> {{ visitCount.outDate }} </span>
                      </span>
                    </div>
                  </div>
                </template>
              </Tables>
            </TabPane>
            <TabPane label="诉讼" name="11">
              <Tables v-if="lawsuitColumns.length && curName === '11'" ref="lawsuitTable" :height="400" :columns="lawsuitColumns" :apifun="lawsuitApifun" :inject-search-data="lawsuitInjectSearchData" pageable>
                <template #header>
                  <div class="tables-header">
                    <Button
                      type="primary"
                      @click="handleApplyLawsuit"
                    >
                      申请诉讼
                    </Button>
                  </div>
                </template>
              </Tables>
            </TabPane>
            <TabPane label="还款/划扣" name="5">
              <Tables
                v-if="billColumns.length && curName === '5'"
                ref="billTable"
                class="billTable"
                :height="400"
                :columns="tags === 0 ? billColumns : deductionColumns"
                :apifun="billApiFun"
                :inject-search-data="billInjectSearchData"
                :row-class-name="rowClassName"
                pageable
                has-sys-btns
                :custom-table="tags === 0"
                :is-add-table-header="false"
                :custom-tables="columnsList"
                :custom-field-colums="customFieldColums"
                @on-customviews-done="onCustomViewsDone"
              >
                <template #header>
                  <div class="tables-header billTable-btn">
                    <RadioGroup v-model="tags" type="button" size="small" @on-change="handleRepaySearch(1)">
                      <Radio :label="0">
                        还款信息
                      </Radio>
                      <Radio :label="1">
                        划扣信息
                      </Radio>
                    </RadioGroup>
                    <Button
                      v-if="tags === 1 "
                      type="primary"
                      style="float: right;"
                      @click="openRepay"
                    >
                      提交划扣信息
                    </Button>
                    <Button
                      v-else-if="tags === 0 "
                      type="primary"
                      style="float: right;"
                      @click="openRepay"
                    >
                      提交还款信息
                    </Button>
                  </div>
                </template>
              </Tables>
            </TabPane>
            <TabPane label="减免管理" name="6">
              <Tables v-if="reduceColumns.length && curName === '6'" ref="reduceTable" :height="400" :columns="reduceColumns" :apifun="reduceApiFun" :inject-search-data="reduceSearchData" pageable>
                <template #header>
                  <div class="tables-header">
                    <Button type="primary" @click="reliefModal(1)">
                      提交减免
                    </Button>
                  </div>
                </template>
              </Tables>
            </TabPane>
            <TabPane v-if="userExtend.spdSwitch && spdFlag" label="减免分期" name="13">
              <div class="relief-reduction">
                <RadioGroup v-model="reductionType" type="button" size="small">
                  <Radio :label="0">
                    申请记录
                  </Radio>
                  <Radio :label="1">
                    历史记录
                  </Radio>
                </RadioGroup>
                <Button type="primary" @click="reliefModal(2)">
                  分期申请
                </Button>
              </div>
              <applyRecord v-if="reductionType === 0 && curName === '13'" :case-id="caseId" />
              <historyRecord v-if="reductionType === 1 && curName === '13'" :case-id="caseId" />
            </TabPane>
            <TabPane label="协催记录" name="21">
              <Tables v-if="((urgingColumns.length && tags===0) || (letterColumns.length && tags===1)) && curName === '21'" ref="urgingTable" :height="400" :columns="tags === 0 ? urgingColumns : letterColumns" :apifun="tags === 0 ? urgingApiFun : letterApifun" :inject-search-data="urgingSearchData" :row-class-name="assistClassName" pageable>
                <template #header>
                  <div class="tables-header">
                    <RadioGroup v-model="tags" type="button" size="small" @on-change="handleRepaySearch(2)">
                      <Radio :label="0">
                        反欺诈
                      </Radio>
                      <Radio :label="1">
                        信函
                      </Radio>
                    </RadioGroup>
                    <Button
                      type="primary"
                      style="float: right;"
                      @click="openApply"
                    >
                      {{ tags === 0 ? "反欺诈申请" : "发函申请" }}
                    </Button>
                  </div>
                </template>
              </Tables>
            </TabPane>
            <TabPane label="其他申请" name="7">
              <Tables v-if="assistColumns.length && curName === '7'" ref="assistTable" :height="400" :columns="assistColumns" :apifun="assistApiFun" :inject-search-data="assistInjectSearchData" :row-class-name="assistClassName" pageable>
                <template v-if="!ifCaseStatus" #header>
                  <div class="tables-header">
                    <Button type="primary" @click="openAssist">
                      申请协办
                    </Button>
                    <Tooltip placement="bottom" style="margin-left: 5px;" transfer :max-width="410">
                      <Icon class="info" type="md-help-circle" :size="16" color="#636C78" style="margin-top: 2px;" />
                      <template #content>
                        <div style="white-space: normal;">
                          <p>
                            协办流程：{{
                              $t("agent")
                            }}填写需协办的内容并提交，由主管或管理员审核通过后，相关人员（例如客服）进行回复。
                          </p>
                        </div>
                      </template>
                    </Tooltip>
                  </div>
                </template>
              </Tables>
            </TabPane>
            <TabPane label="联系人" name="8">
              <caseConcat
                v-if="curName === '8'"
                ref="caseConcat"
                :roles="$store.getters.isAgent ? 'agent' : 'manage'"
                :case-id="caseId"
                :contacts="caseContacts"
                :if-case-status="ifCaseStatus"
                :update-contacts="updateContacts"
                :height="404"
                :edit-true="editTrue"
                :del-true="delTrue"
                @on-edit-concat="editContact"
                @on-add="openNewContact"
                @on-start-refund="caseStartRefund"
                @sync-concat="SyncConcat"
              />
            </TabPane>
            <TabPane label="补充资料" name="9">
              <Tables v-if="fileColumns.length && curName === '9'" ref="fileTables" class="tb_box" :height="400" :columns="fileColumns" :apifun="fileApiFun" :inject-search-data="injectFileSearchData" pageable>
                <template v-if="!ifCaseStatus" #header>
                  <div class="tables-header">
                    <Upload action="" :show-upload-list="false" :before-upload="beforeUploadNormal">
                      <Button icon="iconfont icon-shangchuantupian" class="h-32" :loading="ui.uploadLoading">
                        {{ $t("uploadFiles") }}
                      </Button>
                      <span style="margin-left: 20px;color: #636C78;">
                        * 上传文件大小不能超过10M
                      </span>
                    </Upload>
                  </div>
                </template>
              </Tables>
            </TabPane>

            <!-- 短信记录 -->
            <TabPane label="短信记录" name="12">
              <Tables v-if="messageColumns.length && curName === '12'" ref="messageRef" :height="400" :columns="messageColumns" :apifun="messageApifun" searchable pageable :inject-search-data="messageInjectSearchDate"></Tables>
            </TabPane>

            <TabPane label="操作记录" name="20">
              <Tables v-if="operationColumns.length && curName==='20'" :columns="operationColumns" :apifun="operationApiFun" :inject-search-data="operationInjectSearchData" :height="400" searchable pageable></Tables>
            </TabPane>
            <TabPane label="智能策略记录" name="22">
              <Tables v-if="strategyColums.length && curName==='22'" :columns="strategyColums" :apifun="strategyApiFun" :inject-search-data="lawsuitInjectSearchData" :height="400" pageable></Tables>
            </TabPane>
          </Tabs>
        </div>
      </div>
      <div v-show="ui.showDuyanAIBtn" ref="aiBtn" draggable="" class="ai-btn" v-draggleVue3="draggableAiValue">
        <img src="@/assets/images/duyanAI.png" alt="">
        <p>度言<span>AI</span></p>
        <div @click="handleClickAI">
          <Icon type="ios-arrow-down" />
        </div>
      </div>
    </div>

    <!-- 联系人 -->
    <contactsModal v-if="ui.showContactsModal" ref="contactsModal" :case-id="caseId" :editing-forms="contactForm" @on-submit-done="contactDone" @on-close="closeModals('showContactsModal')" />

    <!-- 修改案件状态 -->
    <applyCaseStatus v-if="ui.showApplyStatus" ref="applyCaseStatus" :case-id-list="[caseId]" :entrust-end-time="entrustEndTime" @on-close="closeModals('showApplyStatus')"></applyCaseStatus>

    <!-- 催收小结历史 -->
    <talkNotesHistory v-if="ui.ifShowTalknotes" ref="talkNotesHistory" :case-id="caseId" @on-save-edit="talkNoteSave" @on-close="closeModals('ifShowTalknotes')" />

    <!-- 新增催收小结 -->
    <Modal v-model="ui.addTalkNote" :footer-hide="true" :closable="false" :mask-closable="false" :title="$t('talkOverNote')">
      <Form ref="talkNoteForm" :model="talkNoteForm" :rules="talkNoteRules" :label-width="88">
        <FormItem :label="$t('talkOverNote') + '：'" prop="content">
          <Input v-model="talkNoteForm.content" :maxlength="600" type="textarea" :rows="3" :placeholder="`请填写${$t('collection')}小结`" :autosize="{ minRows: 3, maxRows: 19 }" show-word-limit></Input>
        </FormItem>
        <FormItem class="btns">
          <Button style="margin-right:8px" @click="handleReset('talkNoteForm')">
            取消
          </Button>
          <Button type="primary" :loading="ui.talkNoteLoading" @click="handleSubmit('talkNoteForm')">
            确定
          </Button>
        </FormItem>
      </Form>
    </Modal>

    <!-- 提交还款/划扣信息 -->
    <applyPayModal v-if="ui.ifApplyPay" ref="applyPayModal" :case-info="caseInfo" :tags="tags" @on-success="applyPaySuccess" @on-close="closeModals('ifApplyPay')"></applyPayModal>

    <!-- 反欺诈申请，发函申请 -->
    <letterModal
      ref="letterModal"
      :case-id="caseId"
      :tags="tags"
      @on-success="applyModalSuccess"
      @on-close="closeModals('ifApplyLetter')"
    >
    </letterModal>

    <!-- 协办 -->
    <assistDetail v-if="ui.ifAssistDetail" ref="assistDetail" :detail-form="detailForm" @on-close="closeModals('ifAssistDetail')"></assistDetail>

    <!-- 协办申请 -->
    <assistModal
      ref="assistModal"
      :case-id="caseId"
      @on-success="assistApplySuccess"
      @on-close="closeModals"
    ></assistModal>

    <!-- 案件联系人 -->
    <Modal v-model="ui.showConcat" :closable="false" :footer-hide="true" :mask-closable="false" title="同步至联系人列表">
      <Alert type="warning" show-icon class="no-border no-border-radius" style="margin: -16px -16px 16px;">
        <span>若导入的联系人号码与无效联系人的号码相同，同步后此号码仍在无效联系人列表。</span>
      </Alert>

      <Form ref="caseContactForm" class="case-contact-form" :label-width="100" label-position="left">
        <div class="title">
          <label>手机号</label>
          <label>姓名</label>
          <label>关系</label>
        </div>

        <FormItem v-for="item in concatData" :key="item.mobile" :label="item.mobile">
          <Input v-model="item.name" placeholder="请输入" style="width: 186px; margin-right: 16px;"></Input>
          <Input v-model="item.relationType" placeholder="请输入" style="width: 186px;"></Input>
        </FormItem>
        <FormItem class="btns">
          <Button style="margin-right: 12px" @click="ui.showConcat = false">
            {{ $t("cancel") }}
          </Button>
          <Button type="primary" :loading="ui.concatLoading" @click="handleConcat">
            确定
          </Button>
        </FormItem>
      </Form>
    </Modal>

    <!-- 完成协催 -->
    <Modal
      v-model="ui.showEndCooper"
      :closable="false"
      :footer-hide="true"
      :mask-closable="false"
      :title="
        finishCooper.isResponse
          ? `结束${$t('coordinate')}`
          : `完成${$t('coordinate')}`
      "
      :width="580"
      class-name="end-cooper"
    >
      <p class="notice">
        * 确认后将{{ finishCooper.isResponse ? "结束" : "完成"
        }}{{ $t("coordinate") }}
      </p>
      <Form ref="finishCooper" :model="finishCooper" label-position="left" :label-width="88">
        <FormItem label="备注" prop="applyDesc">
          <Input v-model="finishCooper.reason" type="textarea" :rows="2" :maxlength="500" placeholder="完成原因" show-word-limit></Input>
        </FormItem>
        <FormItem class="btns">
          <Button style="margin-right: 12px;" @click="cancelEndCooper">
            取消
          </Button>
          <Button type="primary" :loading="ui.cooperLoading" @click="endCooperations">
            确定
          </Button>
        </FormItem>
      </Form>
    </Modal>

    <!-- 案件编辑 -->
    <editCaseModal v-if="ui.ifEditCaseInfo" ref="editCaseModal" :case-id="caseId" :edit-user-info="editUserInfo" :edit-base-info="editBaseInfo" :case-search-info="caseSearchInfo" @on-success="editCaseSuccess" @on-close="closeModals('ifEditCaseInfo')"></editCaseModal>

    <!-- 新增/修改地址 -->
    <outvisitAddress v-if="ui.ifOutvisitAddress" ref="outvisit" :form-data="outvisit" :types="outvisit.type" :source="outvisit.source" :if-self="outvisit.ifSelf" @on-success="editAddressSuccess" @on-visible-change="addressVisibleChange"></outvisitAddress>
    <!-- 申请外访 -->
    <applyVisit v-if="ui.ifApplyVisit" ref="applyVisit" :data="applyVisit.selection" @on-visible-change="applyVisitChange"></applyVisit>
    <!-- 外访详情 -->
    <visitDetail v-if="ui.showVisitDetail" :id="visitDetail.id" ref="visitDetail" @on-visible-change="visitDetailChange"></visitDetail>
    <!-- 选择外访地址 -->
    <choseVisitAddress v-if="ui.ifChoseAddress" ref="choseVisitAddress" :inject-search-data="addressInjectSearchData" :type="choseVisit.type" :case-id="choseVisit.caseId" entrance="caseDetail" @on-visible-change="choseVisibleChange"></choseVisitAddress>
    <!-- 分配外访 -->
    <passVisitSchedule
      v-if="ui.showPassSchedule"
      ref="passVisitSchedule"
      :table-data="applyVisit.selection"
      :case-ids="[caseId]"
      entrance="caseDetail"
      @on-visible-change="scheduleChange"
    ></passVisitSchedule>
    <!-- 修改标签 -->
    <tagModal v-if="ui.tagModal" ref="tagModal" :add-or-edit="addOrEdit" :selected="tagSelect" :search-info="tagSearch" @on-close="closeModal" @on-change="changeTags"></tagModal>

    <robotDetail v-if="ui.showRight" ref="robotDetail" :call-u-uid="callUUid" @closeDra="closeModals('showRight')"></robotDetail>

    <!-- 申请协催 -->
    <applyCooperation v-if="ui.showCooperation" ref="applyCooperation" :case-info="caseInfo" @on-close="closeModals('showCooperation')"></applyCooperation>
    <!-- 添加评语 -->
    <Modal v-model="ui.addCommentModal" :closable="false" :footer-hide="true" :mask-closable="false" title="添加评语">
      <Form ref="commentForm" :model="commentForm" :rules="commentRules" :label-width="60">
        <FormItem label="评语：" prop="content">
          <Input v-model="commentForm.content" type="textarea" :maxlength="600" placeholder="填写评语" :rows="4" show-word-limit :autosize="{ minRows: 3, maxRows: 19 }"></Input>
        </FormItem>
        <FormItem class="btns">
          <Button style="margin-right: 12px;" @click="cancelComment">
            取消
          </Button>
          <Button type="primary" :loading="ui.addCommentLoading" @click="submitCommit">
            确定
          </Button>
        </FormItem>
      </Form>
    </Modal>

    <!-- 历史评语 -->
    <commentsHistory v-if="ui.showHistoryComments" ref="commentsHistory" :case-id="caseId" @on-save-edit="commentsSave" @on-close="closeModals('showHistoryComments')" />
    <!-- 案件标色 -->
    <signColor v-if="ui.showSignColor" ref="signColor" :color="curColor.value" :case-ids="[caseId]" @on-color-change="changeColor" @on-close="closeModals('showSignColor')"></signColor>

    <!-- 文书申请 -->
    <applyMediation ref="applyMediation" :case-id="caseId" @on-close="closeMediation" />
    <!-- 文书预览 -->
    <auditing v-if="ui.showAuditing" :id="auditing.id" ref="auditing" :letter-id="auditing.letterId" :info="auditing.info" :entrance="auditingEntrance" @on-close="closeAudit" />
    <!-- 发送短信 -->
    <sendMsg v-if="ui.showSendMsg" ref="sendMsg" :contact="msgContact" :msg-send-temples="msgSendTemples" :case-id="$route.params.id" @on-close="closeModals('showSendMsg')" />
    <!-- 诉讼申请 -->
    <applyLawsuit
      v-if="lawsuitApplyInfo"
      ref="applyLawsuit"
      :info="lawsuitApplyInfo"
      @on-close="closeLawsuitApply"
    />
    <!-- 减免管理 && 减免分期 -->
    <deduction v-if="ui.showDeduction" ref="deduction" :info="deductionInfo" @on-close="closeShowMediation">
    </deduction>
    <!-- 视频调解 -->
    <videoMediation v-if="ui.showVideoMediation" ref="videoMediation" :type="mediate_type" :case-id="caseId" :case-info="caseInfo" @on-close="closeModals('showVideoMediation')"></videoMediation>
    <!-- 视频调解播放 -->
    <Modal v-model="ui.showPalyVidio" title="选择视频播放" :footer-hide="true" transfer @on-cancel="modalCancel">
      <Tables v-if="palyVadioColumns.length && palyVadioApi" :columns="palyVadioColumns" :apifun="palyVadioApi" :max-height="400">
      </Tables>
    </Modal>
    <!-- // 执行路线图详情 -->
    <div v-if="ui.showRoute">
      <executionRoute ref="executionRoute" :detail="detailInfo" :is-execute="false" :transfer="false" @on-cancel="closeModals('showRoute')"></executionRoute>
    </div>
    <!-- 质检详情 -->
    <recordDetailCom
      ref="recordDetailRef"
      :call-log-list="callLogList"
      :call-log-index="callLogIndex"
      :uuid="callUUid"
      :call-log-detail="callLogDetail"
    />
    <!-- 度言AI -->
    <duyanAI ref="duyanAIRef" v-draggleVue3="dyDraggableValue" :case-id="caseId" @on-close="closeDuyanAI" />

    <!-- 添加微信 -->
    <wxAddModal v-if="userExtend.wpAddWechat" ref="wxAddModal" :custom="customType"></wxAddModal>

    <!-- 工作手机短信 -->
    <workSms v-if="ui.showWorkSms" ref="workSms" :contact="workSmsContact" :case-id="caseId" @on-close="closeModals('showSendMsg')" />
  </div>
</template>

<script>
import { mapActions, mapGetters, mapMutations } from 'vuex'
import Tables from '_c/tables/tables.1'
import outvisitAddress from './outvisitAddress'
import applyVisit from './applyVisit'
import visitDetail from '@/view/dy-visit/components/visitDetail'
import choseVisitAddress from './choseVisitAddress'
import passVisitSchedule from '@/view/dy-visit/passVisitSchedule'
import caseConcat from '@/view/dy-casectrl/case/caseConcat'
import { deepClone, linetoHump } from '@/libs/tools'
import { registerCall } from '@/api/callCenter'
import tagModal from '@/view/dy-nomenu/agent/tagModal'
import applyCaseStatus from '@/view/dy-nomenu/agent/components/applyCaseStatus'
import robotDetail from '@/view/dy-assist/robotText.vue'
import contactsModal from '@/view/dy-nomenu/agent/components/contactsModal'
import editCaseModal from '@/view/dy-nomenu/agent/components/editCaseModal'
import assistModal from '@/view/dy-nomenu/agent/components/assistModal'
import applyPayModal from '@/view/dy-nomenu/agent/components/applyPayModal'
import talkNotesHistory from '@/view/dy-nomenu/agent/components/talkNotesHistory'
import assistDetail from '@/view/dy-nomenu/agent/components/assistDetail'
import applyCooperation from '@/view/dy-nomenu/agent/components/applyCooperation'
import commentsHistory from '@/view/dy-nomenu/agent/components/commentsHistory'
import signColor from '@/view/dy-nomenu/agent/components/signColor'
import applyMediation from '@/view/dy-nomenu/agent/components/applyMediation'
import videoMediation from '@/view/dy-nomenu/agent/components/videoMediation'
import wxAddModal from '@/view/dy-nomenu/agent/components/wxAddModal'
import workSms from './components/workSms'
import idCard from 'idcard'
import audios from '@/view/dy-nomenu/agent/components/audio'
import filters from '_c/tables/filters'
import tableSelect from './components/tableSelect'
import auditing from '@/view/dy-mediate/approve/auditing'
import sendMsg from './components/sendMsg'
import ctrlDetail from './components/ctrlDetail'
import applyLawsuit from './components/applyLawsuit'
import deduction from './components/deduction'
import applyRecord from '@/view/dy-refund/reductionStag/applyRecord.vue'
import historyRecord from '@/view/dy-refund/reductionStag/historyRecord.vue'
import { pinyin } from 'pinyin-pro'
import letterModal from './components/letterModal.vue'
import executionRoute from '@/view/dy-strategy/components/executionRoute.vue'
import loanReceiptDetail from './components/loanReceiptDetail.vue'
import duyanAI from './components/duyanAI'
import { resolveComponent } from 'vue'
import {
  moneyli2yuan,
  formatDate,
  getAudioUrl,
  getImgInfo,
  dataURItoBlob,
  desenRulesAll,
  getUserInLocalstorage
} from '@/libs/util'
import {
  caseInfo,
  conjointList,
  rightsCaseInfo,
  getInbCase,
  getCaseList,
  saveCaseNote,
  getCaseFileList,
  delCaseFile,
  caseUploadFile,
  caseInfoUuid,
  finishCooperation,
  commentSave,
  commentList,
  checkStatusAndUserId,
  listUsingMysql,
  checkCaseCtrl,
  caseWarning,
  getRealName,
  fraudList,
  getCaselog, // 操作记录接口
  getMessageReturn, // 发送短信检查案件是否达成全局管控限制
  getCaseReturn, // 点呼检查案件是否达到全局管控限
  getCustomView, getAdminCustomView, setCustomView, setAdminCustomView
} from '@/api/case'
import {
  updateContact,
  getContact,
  changeStatus,
  addSyncConcat,
  getByCaseId,
  getContactId,
  getContactMobile
} from '@/api/contact'
import {
  addAutomatic,
  getRefund,
  editRefund,
  getDebtList,
  getReductions,
  getAssistApplyList,
  addRefund
} from '@/api/refund'
import {
  deleteAddress,
  getCaseAddressList,
  getVisitList,
  visitListStatistics,
  getApplyVisitList,
  applyVisitStatistics,
  cancelApplyVisit
} from '@/api/visit'
import { getLabelList, encryPhone } from '@/api/org'
import { getDeltOperation } from '@/api/delt'
import { getInfo } from '@/api/user'

import { callWorkPhone, getWorkPhoneUrl } from '@/api/workphone'
import { auditList, cancelMediation, signList, signUrge, getMessageInfo, getCaseVideo } from '@/api/mediate'
import { lawsuitList } from '@/api/lawsuit'
import { letterRecordList } from '@/api/letters'
import { histroryLog } from '@/api/strategy'
import { getCaseRepayPlan, getRepayPlanFields, getRepayFields } from '@/api/refundPlan'
import { getOperationLink } from '@/api/org'
import { getPromissoryNote } from '@/api/loanReceipt'
import recordDetailCom from '@/view/dy-inspect/components/recordDetailCom.vue'
const REFOUND_FORM = {
  contactsId: '',
  contactName: '',
  relationType: '',
  mobile: '',
  actionType: '',
  ptpAmount: '',
  ptpTime: '',
  reduceAmount: '',
  reduceDesc: '',
  callType: null,
  callStyle: 1,
  desc: '',
  nextTime: '',
  syncTypes: [],
  isHidden: 0,
  warning: 0,
  clientLabelId: '', // 标签小结ID
  cmbcConMobile: '', // 民生手机
  cmbcConName: '', // 民生联系人
  cmbcRelation: '', // 民生关系
  cmbcField: '' // 民生Field
}

export default {
  components: {
    Tables,
    outvisitAddress,
    applyVisit,
    visitDetail,
    choseVisitAddress,
    caseConcat,
    passVisitSchedule,
    tagModal,
    applyCaseStatus,
    robotDetail,
    contactsModal,
    editCaseModal,
    assistModal,
    applyPayModal,
    talkNotesHistory,
    assistDetail,
    applyCooperation,
    commentsHistory,
    signColor,
    audios,
    filters,
    tableSelect,
    applyMediation,
    auditing,
    sendMsg,
    ctrlDetail,
    applyLawsuit,
    deduction,
    applyRecord,
    historyRecord,
    letterModal,
    videoMediation,
    executionRoute,
    loanReceiptDetail,
    recordDetailCom,
    duyanAI,
    wxAddModal,
    workSms
  },
  props: {
    newCaseId: [Number, String],
    newIsFifo: {
      type: Boolean,
      default: false
    },
    newAllotAgent: {
      type: Boolean,
      default: false
    },
    newCaseIds: {
      // 当前案件所在列表中的数据
      type: Array,
      default() {
        return []
      }
    },
    newCaseIndex: [Number, String], // 当前案件在列表中的位置
    newSortBy: String,
    isModal: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateRefundUser = (rule, value, callback) => {
      if (
        !this.refundForm.contactsId ||
        !this.refundForm.relationType ||
        !this.refundForm.mobile
      ) {
        callback(new Error(this.$t('l_noCantactInfo')))
      } else {
        callback()
      }
    }
    return {
      customType: 0,
      workSmsContact: null, // 工作手机短信联系人
      mediate_type: 0,
      caseId: this.newCaseId,
      caseIds: this.newCaseIds,
      sortBy: this.newSortBy,
      caseIndex: parseInt(this.newCaseIndex),
      isFifo: this.newIsFifo,
      allotAgent: this.newAllotAgent,
      partMSInfo: {}, // 民生部分字段查看
      callUUid: '',
      caseInfo: {},
      caseBaseInfo: [], // 案件信息
      caseUserInfo: [], // 案人信息
      caseSearchInfo: [], // search_key
      caseTalkOver: [],
      editUserInfo: [],
      editBaseInfo: [],
      editForm: {},
      ifCaseStatus: true,
      editFormRule: {},
      conjo: {
        totalConjointAmount: '--',
        total: '--',
        currentAmount: '--'
      },
      smsTpl: {
        selected: null,
        refContact: null
      },
      // clientLabelId: '', // 小结标签ID
      customLabels: [], // 小结标签
      refundForm: deepClone(REFOUND_FORM),
      refundFormRole: {
        contactsId: [{ required: true, validator: validateRefundUser }],
        ptpAmount: [
          {
            required: true,
            type: 'number',
            trigger: 'change',
            validator: (rule, value) => {
              return new Promise((resolve, reject) => {
                if (this.refundForm.actionType === 0) {
                  if (!value) {
                    reject('请填写' + this.$t('promisedRepayment'))
                  } else {
                    const reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
                    if (reg.test(String(value))) {
                      resolve()
                    } else {
                      reject('最多只能输入两位小数')
                    }
                  }
                } else {
                  resolve()
                }
              })
            }
          }
        ],
        ptpTime: [
          {
            required: true,
            type: 'date',
            trigger: 'change',
            validator: (rule, value) => {
              return new Promise((resolve, reject) => {
                if (this.refundForm.actionType === 0) {
                  if (!value) {
                    reject('请选择' + this.$t('promisedRepayTime'))
                  } else {
                    resolve()
                  }
                } else {
                  resolve()
                }
              })
            }
          }
        ],
        reduceAmount: [
          {
            required: true,
            type: 'number',
            trigger: 'change',
            validator: (rule, value) => {
              return new Promise((resolve, reject) => {
                if (this.refundForm.actionType === 5) {
                  if (!value) {
                    reject('请填写' + this.$t('reliefMent'))
                  } else {
                    const reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
                    if (reg.test(String(value))) {
                      resolve()
                    } else {
                      reject('最多只能输入两位小数')
                    }
                  }
                } else {
                  resolve()
                }
              })
            }
          }
        ],
        reduceDesc: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value) => {
              return new Promise((resolve, reject) => {
                if (this.refundForm.actionType === 5) {
                  if (!value) {
                    reject('请填写' + this.$t('reliefRemark'))
                  } else {
                    resolve()
                  }
                } else {
                  resolve()
                }
              })
            }
          }
        ],
        desc: [
          {
            required: true,
            message: '请填写' + this.$t('k_refundMark'),
            trigger: 'blur'
          }
        ],
        actionType: [
          {
            required: true,
            trigger: 'change',
            message: `请选择${this.$t('collection')}结果`,
            type: 'number'
          }
        ],
        callType: [
          {
            required: true,
            trigger: 'change',
            message: '请选择电话结果',
            type: 'number'
          }
        ]
      },
      contactForm: null,
      editingContact: null,
      contacts: [],
      invalidContacts: [],
      concatData: [], // 通讯录列表
      missContactCall: null,
      operationStates: -1,
      oriOperationState: -1,
      callPhone: {
        line: 'Random',
        contact: null
      },
      curColor: {},
      curTag: [],
      entrustEndTime: null, // 案件的委案截止日期
      file: [],
      refundFile: [],
      currentImg: [],
      caseBaseRows: 0,
      caseUserRows: 0,
      caseTalkRows: 0,
      caseAccountRows: [],
      talkContent: {
        createBy: '--',
        createTime: '--',
        content: '',
        id: null,
        caseId: null
      },
      editTalkNotes: '',
      talkNoteInjectSearchData: null,
      talkNoteForm: {
        content: ''
      },
      talkNoteRules: {
        content: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('SummaryCollection')
          }
        ]
      },
      curName: '1', // 当前的tab
      tags: 0, // tabpane下按钮，0/1
      reductionType: 0, // 减免分期0默认申请记录
      // 本案催记
      recordColumns: [],
      recordApiFun: {
        get: {
          func: getRefund,
          callback: res => {
            if (res.data.list.length > 0) {
              this.callLogList = res.data.list || []
              const date = new Date()
              if (res.data.list[0].nextTime) {
                date.setTime(res.data.list[0].nextTime)
                this.refundForm.nextTime = date
              } else {
                this.refundForm.nextTime = null
              }
            }
          }
        }
      },
      recordInjectSearchData: null,
      // 共债催记
      talkCaseColumns: [],
      talkCaseApiFun: {
        get: getDebtList
      },
      talkCaseSearchData: null,
      // 共债案件
      debtColumns: [
        { title: this.$t('k_debtor'), key: 'name' },
        { title: this.$t('k_batchNum'), width: 228, key: 'batchNo' },
        { title: this.$t('K_outSerialNo'), key: 'outSerialNo' },
        {
          title: this.$t('K_outSerialStatus'),
          key: 'caseStatus',
          width: 140,
          bindmap: 'caseStates',
          showDelete: 'recovery',
          renderHeader: h => {
            return h('div', {}, [
              h('span', {}, this.$t('K_outSerialStatus')),
              h(
                filters,
                {
                  isColor:
                      this.debtSearchData.caseStatues ||
                      this.debtSearchData.caseStatues === '0'
                }, {
                  default: () => h(
                    'div',
                    {
                      style: {
                        width: '200px'
                      }
                    },
                    [
                      h(tableSelect, {
                        phtext: '案件状态',
                        searchItems: this.caseStates,
                        onOnSearch: data => {
                          document.getElementsByClassName('ivu-layout')[0].click()
                          setTimeout(() => {
                            if (data || data === 0) {
                              this.debtSearchData.caseStatues = data + ''
                            } else {
                              delete this.debtSearchData.caseStatues
                            }
                            this.$refs.debtTable.handleSearch()
                          }, 100)
                        }
                      })
                    ]
                  )
                }
              )
            ])
          }
        },
        {
          title: this.$t('k_lastFollowTime'),
          key: 'lastFollowTime',
          type: 'input',
          bindmap: 'formatDate'
        },
        { title: this.$t('k_talkOverResult'), key: 'actionTypeName' },
        { title: this.$t('k_delt'), key: 'orgDeltName' },
        {
          title: this.$t('k_deltProduct'),
          key: 'productName',
          renderHeader: h => {
            return h('div', {}, [
              h('span', {}, this.$t('k_deltProduct')),
              h(
                filters,
                {
                  isColor: this.debtSearchData.productId
                }, {
                  default: () => h(
                    'div',
                    {
                      style: {
                        width: '200px'
                      }
                    },
                    [
                      h(tableSelect, {
                        phtext: this.$t('k_deltProduct'),
                        searchItems: this.availableProduct,
                        onOnSearch: data => {
                          document.getElementsByClassName('ivu-layout')[0].click()
                          setTimeout(() => {
                            if (data || data === 0) {
                              this.debtSearchData.productId = data
                            } else {
                              delete this.debtSearchData.productId
                            }

                            this.$refs.debtTable.handleSearch()
                          }, 100)
                        }
                      })
                    ]
                  )
                }
              )
            ])
          }
        },
        {
          title: this.$t('K_outSetialMoney') + '(元)',
          key: 'amount',
          bindmap: 'money',
          align: 'right'
        },
        {
          title: this.$t('k_Amount') + '(元)',
          key: 'repTotal',
          bindmap: 'money',
          align: 'right'
        },
        {
          title: this.$t('k_hasReliefMent') + '(元)',
          key: 'reductionTotal',
          bindmap: 'money',
          align: 'right'
        },
        {
          title: this.$t('k_overdueTime'),
          key: 'overdueDate',
          bindmap: 'formatDate',
          fmt: 'yyyy-MM-dd'
        },
        {
          title: this.$t('k_overdueDays'),
          key: 'overdueDays',
          type: 'input',
          render: (h, { row }) => {
            return h(
              'span',
              {},
              !this.userExtend.enableAutoUpdateOverdueDays
                ? row.overdueDays
                : Math.ceil(
                  (new Date().getTime() - row.overdueDate) /
                  (24 * 3600 * 1000) -
                  1
                ) >= 0
                  ? Math.ceil(
                    (new Date().getTime() - row.overdueDate) /
                    (24 * 3600 * 1000) -
                    1
                  )
                  : ''
            )
          }
        },
        {
          title: this.$t('k_baileStartTime'),
          key: 'entrustStartTime',
          bindmap: 'formatDate',
          fmt: 'yyyy-MM-dd'
        },
        {
          title: this.$t('k_baileEndTime'),
          key: 'entrustEndTime',
          bindmap: 'formatDate',
          fmt: 'yyyy-MM-dd'
        },
        { title: this.$t('k_agent'), key: 'userName' },
        { title: this.$t('k_group'), key: 'teamName' },
        {
          title: this.$t('operation'),
          key: 'handle',
          width: 150,
          fixed: 'right',
          render: (h, { row, index }) => {
            return h('div', [
              h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  size: 'small',
                  style: 'color: #FC8262',
                  onClick: () => {
                    const data = {
                      caseId: row.id
                    }

                    this.$emit('open-fullScreen', data)
                  }
                },
                this.isManageRefund ? '' : '查看'
              ),
              h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  size: 'small',
                  style: 'color: #F00; margin-left: 8px;',
                  onClick: () => {
                    const route = {
                      name: 'caseDetail',
                      params: {
                        id: row.id,
                        sortBy: 'myCase',
                        name: row.name,
                        caseIds: JSON.stringify(this.caseIds),
                        index: index
                      },
                      meta: {
                        beforeCloseName: 'before_close_normal',
                        notCache: true,
                        title: '案子-' + row.name
                      }
                    }

                    this.$router.push(route)
                    const list = this.$store.state.app.tagNavList
                    for (const key in list) {
                      if (
                        list[key].params &&
                          list[key].params.id === row.id
                      ) {
                        list[key].params = route.params
                      }
                    }
                    this.setTagNavList(list)
                  }
                },
                row.userId === this.userId &&
                  (row.status === 3 || row.status === 4)
                  ? '跳转'
                  : ''
              )
            ])
          }
        }
      ],
      debtApiFun: {
        get: {
          func: conjointList,
          callback: res => {
            this.conjo = {
              totalConjointAmount: res.data.totalConjointAmount,
              total: res.data.total,
              currentAmount: res.data.currentAmount
            }
          }
        }
      },
      debtSearchData: null,
      // 地址
      addressColumns: [],
      addressApifun: {
        get: getCaseAddressList
      },
      addressInjectSearchData: null,
      outvisit: {
        type: 'add',
        id: null,
        ifSelf: false,
        source: 1
      },
      applyVisit: {
        selection: []
      },
      // 外访
      visitCount: {
        // 外访统计
        all: '--', // 总数
        wait: '--', // 待外访
        ing: '--', // 外放中
        done: '--', // 已完成
        outDate: '--' // 已失效
      },
      visitDetail: {
        id: null
      },
      passVisit: {
        address: ''
      },
      choseVisit: {
        type: '',
        caseId: null
      },
      visitRecord: [
        { title: '外访地址', key: 'address', width: 170 },
        { title: '地址类型', key: 'type', bindmap: 'addressTypes' },
        { title: '地址状态', key: 'addressState', bindmap: 'addressStatus' },
        { title: '外访状态', key: 'state', bindmap: 'outvisitStatus' },
        {
          title: '外访结果',
          key: 'result',
          tooltip: true
        },
        {
          title: '外访日期',
          key: 'visitTime',
          bindmap: 'formatDate',
          fmt: 'yyyy-MM-dd'
        },
        {
          title: '外访排期',
          key: 'visitRange',
          width: 150,
          render: (h, { row }) => {
            return h(
              'span',
              {},
              `${formatDate(row.visitStartTime, 'yyyy-MM-dd')} ~ ${formatDate(
                row.visitEndTime,
                'yyyy-MM-dd'
              )}`
            )
          }
        },
        { title: '外访员', key: 'visitName' },
        {
          title: '协访员',
          key: 'visitNames',
          render: (h, { row }) => {
            return h(
              'span',
              {},
              row.visitNames ? row.visitNames.join('、') : ''
            )
          }
        },
        {
          title: this.$t('operation'),
          key: 'handle',
          fixed: 'right',
          width: 100,
          render: (h, { row }) => {
            return h(
              resolveComponent('Button'),
              {
                type: 'text',
                style: 'color: ##1F7EE1 !important',
                onClick: () => {
                  this.visitDetail = {
                    id: row.id
                  }
                  this.ui.showVisitDetail = true

                  this.$nextTick(() => {
                    this.$refs.visitDetail.ui.showDrawer = true
                  })
                }
              },
              {
                default: () => '查看详情'
              }
            )
          }
        }
      ],
      visitRecordApifun: {
        get: {
          func: getVisitList,
          callback: res => {
            const sendData = this.visitRecordInjectSearchData
              ? this.visitRecordInjectSearchData
              : {}
            visitListStatistics(sendData).then(res => {
              if (res) {
                this.visitCount = {
                  total: res.data.total,
                  wait: res.data.waitCount,
                  ing: res.data.visitingCount,
                  done: res.data.doneCount,
                  outDate: res.data.expiredCount
                }
              } else {
                this.visitCount = {
                  all: '--',
                  wait: '--',
                  ing: '--',
                  done: '--',
                  outDate: '--'
                }
              }
            })
          }
        }
      },
      outvisitColumns: [],
      outvisitApifun: {
        get: {
          func: getApplyVisitList,
          callback: res => {
            const sendData = this.outvisitInjectSearchData
              ? this.outvisitInjectSearchData
              : {}
            applyVisitStatistics(sendData).then(res => {
              if (res) {
                this.visitCount = {
                  total: res.data.total,
                  wait: res.data.ingCount,
                  ing: res.data.passCount,
                  done: res.data.refuseCount,
                  outDate: res.data.cancelCount
                }
              } else {
                this.visitCount = {
                  all: '--',
                  wait: '--',
                  ing: '--',
                  done: '--',
                  outDate: '--'
                }
              }
            })
          }
        }
      },
      // // 诉讼
      // lawsuitColumns: [],
      // 还款、划扣
      columnsList: [],
      customFieldColums: [],
      billColumns: [],
      billApiFun: {
        get: listUsingMysql
      },
      deductionColumns: [
        {
          title: this.$t('k_applyTime'),
          key: 'createTime',
          bindmap: 'formatDate'
        },
        {
          title: this.$t('k_deductionment') + '(元)',
          key: 'repaymentAmount',
          bindmap: 'money',
          align: 'right'
        },
        {
          title: this.$t('k_status'),
          width: 100,
          key: 'applyStatus',
          bindmap: 'paymentApplyStatus'
        },
        {
          title: this.$t('k_deductionCarNo'),
          width: 190,
          key: 'repaymentCardNo',
          desenKey: 'bank_code'
        },
        {
          title: this.$t('deductionVouche'),
          key: 'voucherUrl',
          render: (h, { row }) => {
            return row.voucherUrl
              ? h(
                'div',
                {
                  style: 'color: #f00',
                  onClick: () => {
                    const url = row.voucherUrl.replace('http', 'https')
                    window.location.href = url
                  }
                },
                '查看'
              )
              : ''
          }
        },
        { title: this.$t('k_applyExplain'), key: 'desc', tooltip: true },
        { title: this.$t('k_debtor'), key: 'debtName' },
        { title: this.$t('k_delt'), key: 'deltName' },
        { title: this.$t('k_caseProduct'), key: 'productName' },
        {
          title: this.$t('K_outSetialMoney') + '(元)',
          key: 'amount',
          bindmap: 'money',
          align: 'right'
        },
        { title: this.$t('k_applicant'), key: 'createBy' }
      ],
      billInjectSearchData: null,
      // 减免
      reduceColumns: [
        {
          title: this.$t('k_applyTime'),
          key: 'createTime',
          bindmap: 'formatDate'
        },
        {
          title: this.$t('k_applyReliefMent') + '(元)',
          key: 'reduceAmount',
          bindmap: 'money',
          align: 'right'
        },
        {
          title: this.$t('k_auditStatus'),
          key: 'status',
          bindmap: 'reduceStatus'
        },
        {
          title: this.$t('k_applyExplain'),
          key: 'desc',
          width: 200,
          tooltip: true
        },
        {
          title: this.$t('k_exemptionVoucher'),
          key: 'handle',
          render: (h, { row }) => {
            return h('div', [
              h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  size: 'small',
                  onClick: () => {
                    if (!row.voucherUrl) {
                      return
                    }
                    const url = row.voucherUrl.replace('http', 'https')
                    window.open(url)
                  }
                },
                {
                  default: () => row.voucherUrl ? this.$t('check') : '-'
                }
              )
            ])
          }
        },
        { title: this.$t('k_debtor'), key: 'name' },
        { title: this.$t('k_delt'), key: 'orgDeltName' },
        { title: this.$t('k_caseProduct'), key: 'productName' },
        {
          title: this.$t('K_outSetialMoney') + '(元)',
          key: 'amount',
          bindmap: 'money',
          align: 'right'
        },
        { title: this.$t('k_applicant'), key: 'createBy' }
      ],
      reduceApiFun: {
        get: getReductions
      },
      reduceSearchData: null,
      deductionInfo: null,
      // 协催记录
      urgingColumns: [
        { title: '加入时间', key: 'applyTime', bindmap: 'formatDate' },
        { title: '申请说明', key: 'applyDesc' },
        { title: '案件备注', key: 'auditDesc' }
      ],
      letterColumns: [
        { title: '函件ID', key: 'id' },
        { title: '发函方式', key: 'sendType', bindmap: 'sendLetterType' },
        { title: '发函操作人', key: 'senderName' },
        { title: '发送时间', key: 'sendTime', bindmap: 'formatDate' },
        {
          title: '函件', key: 'url', fixed: 'right', render: (h, { row }) => {
            return row.url
              ? h(
                'a',
                {
                  style: 'color: #FF4F1F',
                  onClick: () => {
                    const url = row.url.replace('http', 'https')
                    window.open(url)
                  }
                },
                '查看'
              )
              : h('span', {}, '')
          }
        }
      ],
      letterApifun: {
        get: letterRecordList
      },
      urgingApiFun: {
        get: fraudList
      },
      // 协办
      assistColumns: [
        {
          title: this.$t('k_applyTime'),
          key: 'applyTime',
          bindmap: 'formatDate'
        },
        {
          title: this.$t('k_assistContent'),
          key: 'content',
          width: 200,
          render: (h, { row }) => {
            return h('div', {
              style: {
                display: 'flex',
                alignItems: 'center'
              }
            }, [
              h(
                'span',
                {},
                row.content && row.content.length > 10
                  ? row.content.substring(0, 10) + '...'
                  : row.content
              ),
              h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  size: 'small',
                  style: {
                    color: '#FF4F1F',
                    cursor: 'pointer',
                    textDecoration: 'underline',
                    marginLeft: '8px'
                  },
                  onClick: () => {
                    this.detailForm = {
                      type: 'content',
                      content: row.content,
                      file: row.contentFileName ? row.contentFileName : null,
                      url: row.contentUrl ? row.contentUrl : null
                    }

                    this.ui.ifAssistDetail = true

                    this.$nextTick(() => {
                      this.$refs.assistDetail.ui.detailModal = true
                    })
                  }
                },
                {
                  default: () => this.$t('detail')
                }
              )
            ])
          }
        },
        {
          title: '审核' + this.$t('k_status'),
          key: 'status',
          width: 100,
          bindmap: 'assistApplystatus'
        },
        {
          title: this.$t('k_replyContent'),
          key: 'replyDesc',
          render: (h, { row }) => {
            return row.replyDesc
              ? h('div', [
                h(
                  resolveComponent('Button'),
                  {
                    type: 'text',
                    size: 'small',
                    style: {
                      color: '#FF4F1F',
                      cursor: 'pointer',
                      textDecoration: 'underline'
                    },
                    onClick: () => {
                      this.detailForm = {
                        type: 'reply',
                        content: row.replyDesc,
                        file: row.replyFileName ? row.replyFileName : null,
                        url: row.replyUrl ? row.replyUrl : null
                      }
                      this.ui.ifAssistDetail = true

                      this.$nextTick(() => {
                        this.$refs.assistDetail.ui.detailModal = true
                      })
                    }
                  },
                  {
                    default: () => this.$t('check')
                  }
                )
              ])
              : h('span', {}, '-')
          }
        },
        { title: this.$t('k_debtor'), key: 'debtorName' },
        { title: this.$t('k_delt'), key: 'orgDeltName' },
        { title: this.$t('k_deltProduct'), key: 'productName' },
        { title: this.$t('k_applicant'), key: 'applyByName' },
        { title: this.$t('k_replier'), key: 'replyByName' }
      ],
      assistApiFun: {
        get: getAssistApplyList
      },
      assistInjectSearchData: null,
      detailForm: {
        content: null,
        file: null,
        url: null
      },
      // 联系人
      updateContacts: {
        create_time: '',
        contacts: '',
        syncStatus: 0,
        hideContacts: ''
      },
      caseContacts: {},
      editTrue: false,
      delTrue: false,
      // 通讯录列表
      concatList: [
        {
          title: this.$t('contact'),
          key: 'name',
          render: (h, { row }) => {
            return row.edit === true
              ? h(resolveComponent('Input'), {
                modelValue: row.name,
                onOnChange: function(event) {
                  row.name = event.target.value
                }
              })
              : h('span', row.name)
          }
        },
        {
          title: this.$t('k_mobile'),
          key: 'mobile'
        },
        {
          title: this.$t('k_relationship'),
          key: 'relationType',
          render: (h, { row }) => {
            return row.edit === true
              ? h(resolveComponent('Input'), {
                modelValue: row.relationType,
                onOnChange: function(event) {
                  row.relationType = event.target.value
                }
              })
              : h('span', row.relationType)
          }
        },
        {
          title: this.$t('k_operation'),
          render: (h, { row, index }) => {
            return h(
              resolveComponent('Button'),
              {
                size: 'small',
                type: 'primary',
                style: {
                  paddingLeft: '5px',
                  paddingRight: '5px'
                },
                onClick: () => {
                  if (row.edit) {
                    this.handleSave(row, index)
                  } else {
                    this.create(row)
                  }
                }
              },
              {
                default: () => row.edit ? '保存' : '修改'
              }
            )
          }
        }
      ],
      // 补充资料
      fileColumns: [],
      fileApiFun: {
        get: {
          func: getCaseFileList,
          callback: res => {
            this.uploadedFiles = res.data.list
          }
        }
      },
      injectFileSearchData: null,
      uploadedFiles: [],
      // 短信记录
      messageColumns: [],
      messageInjectSearchDate: null,
      messageApifun: {
        get: getMessageInfo
      },
      // 调解文书
      mediRecord: [],
      mediRecordApi: {
        get: auditList
      },
      mediRecordInjectSearchData: null,
      mediText: [],
      mediTextApi: {
        get: signList
      },
      mediTextInjectSearchData: null,
      VideoMediation: [],
      VideoMediationApi: {
        get: getCaseVideo
      },
      // VideoMediationSearchData: null,
      // 还款计划
      refundColumns: [],
      refundApifun: {},
      refundInjectSearchData: null,
      // 催记
      draggableValue: {
        handle: undefined,
        boundingRectMargin: {
          top: -80,
          right: 2,
          bottom: 2,
          left: 2
        },
        initialPosition: {
          top: 80
        }
      },
      handleId: 'handle-id',
      audio: '',
      // 评语
      commentForm: {
        content: ''
      },
      commentRules: {
        content: [{ required: true, message: '评语不能为空', trigger: 'blur' }]
      },
      showComment: {},
      idCardInfo: {
        area: '--',
        birth: '--',
        sex: '--',
        age: '--'
      },
      timer: null,
      // 执行id
      detailInfo: {},
      ui: {
        selfUrgeRefund: false,
        loadingContact: false,
        loadingCaseDetail: false,
        contactModal: false,
        contactLoading: false,
        uploadLoading: false,
        applyStatusLoading: false,
        caseInfoColNum: 4,
        editTalkNote: false,
        showTalkNotesHistory: false,
        addTalkNote: false,
        talkNoteLoading: false,
        ifOutvisitAddress: false,
        ifApplyVisit: false,
        showVisitDetail: false,
        ifChoseAddress: false,
        concatLoading: false,
        showConcat: false,
        ifHideDrag: true,
        submitingRefund: false,
        showPassSchedule: false,
        editSubmitLoading: false,
        tagModal: false, // 添加标签
        showApplyStatus: false,
        showRight: false,
        showContactsModal: false,
        ifEditCaseInfo: false,
        ifApplyPay: false,
        ifShowTalknotes: false,
        ifAssistDetail: false,
        showCooperation: false,
        showEndCooper: false,
        cooperLoading: false,
        addCommentModal: false,
        addCommentLoading: false,
        showHistoryComments: false,
        refreshLoading: false,
        loadingOperation: false,
        caseInfoHeight: 0,
        contactRadio: 1,
        showSignColor: false,
        isAudio: [],
        showChangeArrow: true,
        urgeLoading: false,
        showAuditing: false,
        showSendMsg: false,
        showDeduction: false,
        showVideoMediation: false,
        showPalyVidio: false,
        showRoute: false, // 是否显示执行路线图
        showDuyanAIBtn: false,
        showWorkSms: false, // 工作手机短信
      },
      selectValue: [],
      addOrEdit: false,
      tagSelect: [],
      tagSearch: {},
      finishCooper: {
        id: null,
        reason: null,
        isResponse: true
      },
      isTeamUrgeCase: false,
      // 当前电话呼出联系人
      curContacts: null,
      isCalling: false, // 填催记需不需要电话
      // 监听contatc返回
      isContact: false,
      isInvalidContact: false,
      casingStatus: {
        name: '',
        value: 0 // 0表示不可操作状态，1表示可操作状态
      },
      operationStateList: [], // 催收进程
      refundResultsList: [], // 催收结果
      allRefundResults: [], // 备份全部催收结果
      callResultList: [], // 电话结果
      allCallResults: [], // 备份全部电话结果
      // 文书预览
      auditing: {},
      auditingEntrance: '',
      colorField: [], // 需要标色的字段
      msgContact: null, // 发送短信联系人
      msgSendTemples: [], // 发送限制模板
      // 诉讼案件
      lawsuitColumns: [],
      lawsuitApifun: {
        get: lawsuitList
      },
      lawsuitInjectSearchData: null,
      lawsuitApplyInfo: null,
      // 账户信息
      accountInfo: [],
      allAccount: [],
      // 操作记录
      operationColumns: [],
      operationApiFun: {
        get: getCaselog
      },
      operationInjectSearchData: null,
      // 搜索类型
      operationStatuss: [{ name: '分配管理', value: '3,10,14' }, { name: '案件状态', value: '1,2,4,7,8,9' }, { name: '案件管理', value: '5,11,12,13,19' }],
      // 催收信息配置
      operationConfig: [],
      // 智能策略记录
      strategyColums: [],
      strategyApiFun: {
        get: histroryLog
      },
      curOperationState: null, // 当前选中催收进程配置项
      spdFlag: 1,
      // 视频播放
      palyVadioColumns: [],
      List: [],
      palyVadioApi: null,
      // 借据
      tabName: '0',
      loanList: [],
      callLogDetail: {},
      callLogIndex: 0,
      callLogList: [],
      // duyan ai
      dyDraggableValue: {
        handle: undefined,
        boundingRectMargin: {
          top: -80,
          right: 2,
          bottom: 2,
          left: 2
        },
        initialPosition: {
          top: 80
        }
      },
      draggableAiValue: {
        handle: undefined,
        boundingRectMargin: {
          top: 0,
          bottom: 120,
          right: -24
        },
        boundingDirection: 'top-bottom',
        initialPosition: {
          top: 220,
          right: 0
        }
      },
    }
  },
  computed: {
    ...mapGetters([
      'availableSmsTemplate',
      'refundResult',
      'availableRefundResult',
      'urgeRefund',
      'openedRefundForm',
      'callUuid',
      'userExtend',
      'isAgent',
      'isCtiReady',
      'caseStandardColor',
      'caseTplFields',
      'refundCallResult',
      'isRobot',
      'addressStatus',
      'addressTypes',
      'addressResource',
      'outvisitAuditStatus',
      'outvisitStatus',
      'userId',
      'qosCallUuid',
      'ctiCallbackData',
      'originCtiCall',
      'callStyle',
      'caseTagArr',
      'teamId',
      'isOutsource',
      'isInCalling',
      'isSupervisor',
      'tagNavList',
      'caseColors',
      'isInCall',
      'refundings',
      'refundedCase',
      'caseStates',
      'availableProduct',
      'currentUserInfo',
      'colorFields',
      'isManageRefund',
      'callUuidOld',
      'isNewRobot',
      'wellPhoneSwitch',
      'operationStatus',
      'userNo',
      'mediating'
    ]),
    caseColor() {
      return [
        ...this.caseColors,
        ...[{ value: 0, name: '不标色', color: null }]
      ]
    },
    contactStatus() {
      return this.isContact && this.isInvalidContact
    }
  },
  methods: {
    ...mapMutations([
      'setTagNavList',
      'setIsInCalling',
      'setIsInCall',
      'setCurOutbounce',
      'setIsOutbounceLock',
      'setIsChangeCase',
      'closeFocusPage',
      'updateCtiCall',
      'setOpenedRefundForm',
      'setRefundedCase',
      'setIsManageRefund',
      'updateCtiCall'
    ]),
    ...mapActions(['getRefData', 'getAvailableSmsTemplates', 'setCtiCall', 'postAddRefund']),
    async getFields() {
      const res = await getRepayFields({ type: 1 })
      return res.data
    },
    async onCustomViewsDone(array, type) {
      const sendData = []
      for (const item of array) {
        if (item.key) {
          sendData.push({
            key: item.key,
            checked: item.checked,
            fixed: item.fixed ? item.fixed : '',
            name: item.name,
            customType: item.customType ? item.customType : ''
          })
        } else {
          sendData.push({
            submitKey: item.submitKey,
            checked: item.checked,
            name: item.name
          })
        }
      }

      const data = {
        listing: sendData,
        userId: this.userId,
        webTag: 8,
        custom: type,
        menuType: 8
      }
      const func = !this.userExtend.userViewSwitch && type !== 0 ? setAdminCustomView : setCustomView

      func(data)
        .then(res => {
          if (res.success) {
            if (type) {
              this.$refs.billTable.ui.popShowTable = false
            } else {
              this.$refs.billTable.ui.popShowSearch = false
              setTimeout(() => {
                this.$refs.billTable.setCustomSearch()
                this.$refs.billTable.getCustomFields()
              }, 10)
            }
          }
        })
        .finally(() => {
          // 关闭按钮loading动画
          this.$refs.billTable.ui.customLoading = false
        })
    },
    handleRepayColumns() {
      const data = {
        userId: this.userId,
        webTag: 8,
        menuType: 8
      }
      getCustomView(data).then(async res => {
        if (res) {
          let { customView, customTable } = res.data
          if (!this.userExtend.userViewSwitch) {
            const adminCustable = await getAdminCustomView(data)
            customTable = adminCustable.data.fieldJson
          }
          const fields = await this.getFields()
          const field_ = []
          for (const value of fields) {
            if (value.isExt) {
              field_.push({ checked: false, name: value.name, key: value.field, fixed: '', customType: '' })
            }
          }
          this.columnsSearch = customView ? JSON.parse(customView) : []
          if (customTable) {
            const customJSON = JSON.parse(customTable)
            for (const value of field_) {
              for (const c_ of customJSON) {
                if (value.key === c_.key) {
                  c_.name = value.name
                }
              }
            }
            this.columnsList = customJSON
          } else {
          // 如果第一次是数据为空的动态注入这些还款自定义字段
            this.columnsList = []
            this.customFieldColums = field_
          }
        }
        this.billColumns = [
          {
            title: this.$t('k_repayment') + '(元)',
            key: 'repaymentAmount',
            bindmap: 'money',
            align: 'right'
          },
          {
            title: this.$t('k_status'),
            width: 100,
            key: 'applyStatus',
            bindmap: 'paymentApplyStatus'
          },
          {
            title: this.$t('k_repayBy'),
            key: 'repaymentFrom',
            render: (h, { row }) => {
              return h(
                'div',
                row.repaymentFrom === '本人' ? row.debtName : row.repaymentFrom
              )
            }
          },
          { title: '还款人手机号', key: 'repaymentMobile' },
          {
            title: this.$t('k_repayTime'),
            key: 'repaymentTime',
            bindmap: 'formatDate',
            fmt: 'yyyy-MM-dd'
          },
          { title: this.$t('k_repayWay'), key: 'repaymentStyle' },
          {
            title: this.$t('repayVouche'),
            key: 'voucherUrl',
            render: (h, { row }) => {
              return row.voucherUrl
                ? h(
                  'div',
                  {
                    style: 'color: #f00',
                    onClick: () => {
                      const url = row.voucherUrl.replace('http', 'https')
                      window.location.href = url
                    }
                  },
                  '查看'
                )
                : ''
            }
          },
          {
            title: this.$t('k_repayCarNo'),
            width: 190,
            key: 'repaymentCardNo',
            desenKey: 'bank_code'
          },
          { title: this.$t('k_repayType'), key: 'repaymentType', tooltip: true },
          {
            title: this.$t('k_applyTime'),
            key: 'createTime',
            bindmap: 'formatDate'
          },
          { title: this.$t('k_applyExplain'), key: 'desc', tooltip: true },
          { title: '客户姓名', key: 'debtName' },
          { title: this.$t('k_caseProduct'), key: 'productName' },
          {
            title: this.$t('K_outSetialMoney') + '(元)',
            key: 'amount',
            bindmap: 'money',
            align: 'right'
          },
          { title: '案件编号', key: 'outSerialNo' },
          { title: '借据号', key: 'loanNumber' },
          { title: '期数', key: 'period' },
          { title: '服务费', key: 'serviceCharge' },
          { title: '其他费用', key: 'otherFee' },
          { title: '罚息', key: 'penaltyInterest' },
          { title: '本金', key: 'principal' },
          { title: '利息', key: 'interest' },
          { title: this.$t('k_applicant'), key: 'createBy' }
        ]
      })
    },
    closeModal() {
      this.ui.tagModal = false
    },
    formatMoney(value) {
      return moneyli2yuan(value)
    },
    changeTags(data) {
      this.getTag(data)
    },
    checkInUsingStates(curVal) {
      let isUsing = false
      for (const item of this.operationStateList) {
        if (item.value === curVal) {
          isUsing = true
        }
      }

      return isUsing
    },
    initPage(bool) {
      this.setIsChangeCase(true)
      this.isContact = false
      this.isInvalidContact = false
      if (bool) {
        this.caseId = this.newCaseId
      }
      setTimeout(() => {
        this.talkCaseColumns = [
          { title: this.$t('k_contact'), key: 'conName' },
          {
            title: this.$t('k_relationship'),
            key: 'relationType',
            type: 'input'
          },
          {
            title: this.$t('k_contactTel'),
            width: 150,
            key: 'conMobile',
            desenKey: 'mobile'
          },
          {
            title: this.$t('k_talkOverType'),
            showColumn:
              (this.isRobot || this.isNewRobot) &&
              (this.isOutsource ? this.userExtend.robotSwitch : true),
            width: 150,
            key: 'submitType',
            type: 'input',
            bindmap: 'robotCallType'
          },
          {
            title: this.$t('k_callTime'),
            width: 150,
            key: 'callTime',
            bindmap: 'formatDate'
          },
          {
            title: `${this.$t('collection')}进程`,
            key: 'operationStateName',
            type: 'input'
          },
          { title: this.$t('k_talkOverResult'), key: 'actionTypeName' },
          {
            title: this.$t('k_callResult'),
            key: 'callTypeName'
          },
          {
            title: this.$t('k_refundMark'),
            key: 'desc',
            tooltip: true,
            width: 200
          },
          {
            title: '机器人协催客户标签',
            key: 'autoAssistRecord',
            tooltip: true,
            width: 200,
            renderHeader: (h, params) => {
              return h('span', {}, {
                default: () => [
                  h(resolveComponent('Icon'), {
                    custom: 'iconfont icon-robot',
                    style: 'position: relative; top:-1px; margin-right: 2px;color: #636C78;'
                  }),
                  h('span', {}, {
                    default: () => this.$t('k_collectionRecord')
                  })
                ]
              })
            }
          },
          // {
          //   title: '旧机器人客户标签',
          //   align: 'center',
          //   showColumn:
          //     (this.isRobot || this.isNewRobot) &&
          //     (this.isOutsource ? this.userExtend.robotSwitch : true),
          //   key: 'tag',
          //   bindmap: 'customLabel'
          // },
          {
            title: this.$t('telRecord'),
            width: 140,
            key: 'handle',
            render: (h, { row, index }) => {
              let isAudio = false
              if (row.callUuid) {
                if (row.outcome !== 'SUCCESS') {
                  if (row.ringDurtion > 0) {
                    if (row.submitType === 0) {
                      // 点呼
                      if (this.userExtend.isEarlyMediaRecorded) {
                        isAudio = true
                      }
                    } else {
                      // 计划
                      if (this.userExtend.isCampaignEarlyMediaRecorded) {
                        isAudio = true
                      }
                    }
                  }
                } else {
                  isAudio = true
                }
              }

              const noRightBtn = h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  ghost: true,
                  onClick: () => {
                    this.$Message.error('无操作权限')
                  }
                }, {
                  default: () => this.$t('clickPlay')
                }

              )

              const playBtn = h(
                resolveComponent('Poptip'),
                {
                  transfer: true,
                  placement: 'left',
                  'popper-class': 'audio-poptip',
                  onOnPopperShow: () => {
                    setTimeout(() => {
                      this.ui.isAudio[index] = true
                    }, 200)
                  },
                  onOnPopperHide: () => {
                    this.$nextTick(() => {
                      this.ui.isAudio[index] = false
                    })
                  }
                },
                {
                  default: () => h(resolveComponent('Button'), {
                    type: 'text',
                    ghost: true,
                    onClick: () => {
                      // 工作手机录音
                      if (row.submitType === 3) {
                        getWorkPhoneUrl({ uuid: row.callUuid }).then(res => {
                          if (res) {
                            const splitStr = '/ng'
                            if (res.data.includes(splitStr)) {
                              res.data = 'https://' + window.location.host + splitStr + res.data.split('/ng')[1]
                            }
                            this.audio = res.data
                          }
                        })
                      } else {
                        this.audio = getAudioUrl(
                          this.userExtend.duyanOrgId,
                          row.callTime,
                          row.callUuid
                        )
                      }
                    }
                  }, {
                    default: () => this.$t('clickPlay')
                  }),
                  content: () => h('div', {}, [
                    this.ui.isAudio[index] ? h(audios, {
                      src: this.audio
                    }) : ''
                  ]
                  )
                }
              )

              const btns = []
              if (isAudio) {
                if (
                  (this.isAgent && this.userExtend.operationVoiceSeatSwitch) ||
                  (this.isSupervisor &&
                    this.userExtend.operationVoiceTeamSwitch)
                ) {
                  btns.push(noRightBtn)
                } else {
                  btns.push(playBtn)
                }
              }

              return h('div', {}, btns)
            }
          },
          {
            renderHeader: h => {
              return h('span', [
                h(resolveComponent('Icon'), {
                  custom: 'iconfont icon-robot',
                  style: 'position: relative; top:-2px'
                }),
                this.$t('k_robotText')
              ])
            },
            align: 'center',
            width: 120,
            render: (h, { row }) => {
              return row.submitType === 1
                ? h(
                  'span',
                  {
                    onClick: () => {
                      this.ui.showRight = true
                      this.callUUid = row.callUuid
                      this.$nextTick(() => {
                        this.$refs.robotDetail.showRight = true
                      })
                    },
                    style: 'color: #ff724c;cursor: pointer'
                  },
                  '查看详情'
                )
                : ''
            },
            showColumn:
              (this.isRobot || this.isNewRobot) &&
              (this.isOutsource ? this.userExtend.robotSwitch : true)
          },
          {
            title: this.$t('k_nextFollowUpTime'),
            key: 'nextTime',
            type: 'date',
            bindmap: 'formatDate'
          },
          {
            title: this.$t('k_PTPment') + '(元)',
            width: 160,
            key: 'ptpAmount',
            type: 'input',
            bindmap: 'money',
            align: 'right'
          },
          {
            title: this.$t('k_PTPtime'),
            key: 'ptpTime',
            type: 'date',
            bindmap: 'formatDate'
          },
          {
            title: this.$t('k_talkOverTime'),
            key: 'createTime',
            type: 'date',
            bindmap: 'formatDate'
          },
          {
            title: this.$t('k_callingType'),
            key: 'callStyle',
            render: (h, { row }) => {
              return h(
                'span',
                {},
                row.callStyle === 0
                  ? this.$t('inBound')
                  : row.callStyle === 1
                    ? this.$t('outBound')
                    : ''
              )
            }
          },
          { title: this.$t('k_callType'), key: 'outcome', bindmap: 'outcome' },
          {
            title: this.$t('k_ringDuration'),
            key: 'ringDurtion',
            bindmap: 'time'
          },
          {
            title: this.$t('k_callDuration'),
            key: 'callDurtion',
            bindmap: 'time'
          },
          {
            title: this.$t('k_mainPhone'),
            width: 150,
            key: 'caller'
          },
          { title: this.$t('k_agent'), key: 'operatorName', type: 'input' },
          { title: this.$t('k_affiliatedGroup'), key: 'teamName' },
          { title: '提交人', key: 'adminSubmitterName' },
          { title: this.$t('k_qcRemark'), key: 'comment', tooltip: true },
          { title: this.$t('k_qcDetail'),
            key: 'qcDetail',
            render: (h, params) => {
              const see = h(resolveComponent('Button'), {
                type: 'text',
                onClick: () => {
                  this.callUUid = params.row.callUuid
                  this.callLogIndex = params.index
                  this.callLogDetail = params.row
                  this.$refs.recordDetailRef.ui.showDrawer = true
                }
              }, {
                default: () => '查看'
              })
              return h('div', {}, params.row.callUuid ? [see] : '')
            }
          },
          { title: this.$t('k_delt'), key: 'deltName' },
          { title: this.$t('k_deltProduct'), key: 'productName' },
          { title: this.$t('K_outSerialNo'), key: 'outSerialNo' }
        ]
      }, 100)
      this.ui.loadingContact = true
      this.ui.loadingCaseDetail = true

      this.editTrue = true
      this.delTrue = true

      this.isFifo = !!this.$route.params.isFifo
      this.allotAgent = this.$route.params.allotAgent
        ? this.$route.params.allotAgent === 'personalFifo'
          ? 'personalFifo'
          : true
        : false
      this.ui.showDuyanAIBtn = false

      if (this.$route.name === 'caseDetail' && !this.isModal) {
        this.ui.showDuyanAIBtn = this.userExtend.aiAnalysisSwitch
      }

      this.getRefData({
        map: [
          'availableRefundResult',
          'refundResult',
          'refundCallResult',
          'caseUpdateFields',
          'caseUserBaseFields',
          'caseBaseFields',
          'operationExportCaseFields',
          'outcome',
          'operationState'
        ],
        func: [
          'getAvailableRefundResult',
          'getAvailableOperationState',
          'getAvailableRefundCallResult',
          'getCaseTagColor',
          'getAvailableProduct'
        ]
      }).then(() => {
        this.smsTpl.selected = this.availableSmsTemplate.length
          ? this.availableSmsTemplate[0]
          : null
        if (
          (this.isFifo && this.allotAgent !== 'personalFifo') ||
          this.$route.params.isTransfer === 'true'
        ) {
          caseInfo({ caseId: this.caseId }).then(res => {
            this.getInitData(res)
          })
        }
        // 质检员接口
        // else if ('inspector') {
        //   caseInfoUuid({ callUuid: this.qosCallUuid }).then(res => {
        //     this.getInitData(res)
        //   })
        // }
        else {
          rightsCaseInfo({ caseId: this.caseId }).then(res => {
            this.getInitData(res)
          })
        }

        this.getAvailableSmsTemplates()
      })
    },
    async getInitData(res) {
      this.choseVisit = {
        type: 'distribution',
        caseId: null
      }

      this.caseBaseInfo = []
      this.caseUserInfo = []
      this.caseSearchInfo = []
      this.caseTalkOver = []
      this.editUserInfo = []
      this.editBaseInfo = []
      this.casingStatus = {
        name: '',
        value: 0
      }
      this.ui.loadingContact = true
      this.ui.loadingCaseDetail = true
      this.talkContent = {
        createBy: '--',
        createTime: '--',
        content: '',
        id: null,
        caseId: null
      }
      this.accountInfo = []
      this.allAccount = []

      if (res.data) {
        this.spdFlag = res.data.spdFlag
        this.ui.showChangeArrow = true
        this.curName = '1'
        this.tabName = '0'
        this.tags = 0
        this.caseInfo = res.data
        this.getColor(this.caseInfo.color)
        this.entrustEndTime = this.caseInfo.entrustEndTime
        this.ifCaseStatus = false

        if (this.caseInfo.name) {
          this.caseInfo.titleName = pinyin(desenRulesAll('name', this.caseInfo.name))
        }

        if (this.qosCallUuid) {
          this.caseId = this.caseInfo.id
        }
        // 判断是否为开启团队催收的小组案件
        this.isTeamUrgeCase =
          this.userExtend.teamUrgeSwitch &&
          this.caseInfo.teamId === this.teamId

        // 失效案件
        if (
          this.caseInfo.status === -1 ||
          this.caseInfo.state === 2 ||
          this.caseInfo.state === 4
        ) {
          this.ifCaseStatus = true
        }
        // 已结案案件
        if (this.caseInfo.status !== -1 && this.caseInfo.state === 3) {
          this.ifCaseStatus = true
        }
        // 未开始案件
        if (
          (this.caseInfo.status === 3 || this.caseInfo.status === 4) &&
          this.caseInfo.state === 1 &&
          new Date().getTime() < this.caseInfo.entrustStartTime
        ) {
          this.ifCaseStatus = true
        }

        if (this.caseInfo.recovery === 0) {
          switch (this.caseInfo.status) {
            case -1:
              if (this.caseInfo.state === 0) {
                this.casingStatus = {
                  name: '作废',
                  value: 0
                }
              }
              break
            case 0:
              if (this.caseInfo.state === 0) {
                this.casingStatus = {
                  name: '未分配',
                  value: 1
                }
              }
              break
            case 3:
              const item = {
                1: { name: '分案完成', value: 1 },
                2: { name: this.$t('stopTalkOver'), value: 0 },
                3: { name: '结案', value: 0 },
                4: { name: '退案', value: 0 },
                5: { name: '分配至部门', value: 1 },
                6: { name: '分配至机构', value: 1 }
              }
              this.casingStatus = item[this.caseInfo.state]
              break
            case 4:
              const item1 = {
                1: { name: '留案', value: 1 },
                2: { name: this.$t('stopTalkOver'), value: 0 },
                3: { name: '结案', value: 0 },
                4: { name: '退案', value: 0 }
              }
              this.casingStatus = item1[this.caseInfo.state]
              break
            default:
              break
          }
        } else {
          this.casingStatus = {
            name: '已删除',
            value: 0
          }
        }
        this.getTag(this.caseInfo.caseTags)

        this.recordColumns = [
          { title: this.$t('k_contact'), key: 'conName', desenKey: 'name' },
          { title: this.$t('k_relationship'), key: 'relationType' },
          {
            title: this.$t('k_contactTel'),
            width: 150,
            key: 'conMobile'
          },
          {
            title: this.$t('k_talkOverType'),
            showColumn:
              (this.isRobot || this.isNewRobot) &&
              (this.isOutsource ? this.userExtend.robotSwitch : true),
            width: 150,
            key: 'submitType',
            bindmap: 'robotCallType'
          },
          {
            title: this.$t('k_callTime'),
            width: 150,
            key: 'callTime',
            bindmap: 'formatDate'
          },
          { title: `${this.$t('collection')}进程`, key: 'operationStateName' },
          { title: this.$t('k_talkOverResult'), key: 'actionTypeName' },
          {
            title: this.$t('k_callResult'),
            key: 'callTypeName'
          },
          {
            title: '客户小结标签',
            key: 'clientLabelName'
          },
          {
            title: this.$t('k_refundMark'),
            key: 'desc',
            type: 'textarea',
            editable:
              this.userExtend.operationModifySwitch &&
              !this.ifCaseStatus,
            width: 200,
            tooltip: true,
            apifun: {
              editSingle: {
                func: editRefund,
                callback: res => {
                  setTimeout(() => {
                    if (this.$refs.recordTable) {
                      this.$refs.recordTable.handleSearch()
                    }
                    if (this.$refs.talkCaseTable) {
                      this.$refs.talkCaseTable.handleSearch()
                    }
                  }, 1000)
                }
              }
            }
          },
          {
            title: '机器人协催客户标签',
            key: 'autoAssistRecord',
            width: 200,
            renderHeader: (h, params) => {
              return h('span', {}, {
                default: () => [
                  h(resolveComponent('Icon'), {
                    custom: 'iconfont icon-robot',
                    style: 'position: relative; top:-1px; margin-right: 2px;color: #636C78;'
                  }),
                  h('span', {}, {
                    default: () => this.$t('k_collectionRecord')
                  })
                ]
              })
            }
          },
          // {
          //   title: '旧机器人客户标签',
          //   align: 'center',
          //   key: 'tag',
          //   bindmap: 'customLabel',
          //   showColumn:
          //     (this.isRobot || this.isNewRobot) &&
          //     (this.isOutsource ? this.userExtend.robotSwitch : true)
          // },
          {
            title: this.$t('telRecord'),
            width: 140,
            key: 'handle',
            render: (h, { row, index }) => {
              let isAudio = false
              if (row.callUuid) {
                if (row.outcome !== 'SUCCESS') {
                  if (row.ringDurtion > 0) {
                    if (row.submitType === 0) {
                      // 点呼
                      if (this.userExtend.isEarlyMediaRecorded) {
                        isAudio = true
                      }
                    } else {
                      // 计划
                      if (this.userExtend.isCampaignEarlyMediaRecorded) {
                        isAudio = true
                      }
                    }
                  }
                } else {
                  isAudio = true
                }
              }

              const noRightBtn = h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  ghost: true,
                  onClick: () => {
                    this.$Message.error('无操作权限')
                  }
                },
                {
                  default: () => this.$t('clickPlay')
                }
              )

              const playBtn = h(
                resolveComponent('Poptip'),
                {
                  transfer: true,
                  placement: 'left',
                  'popper-class': 'audio-poptip',
                  onOnPopperShow: () => {
                    setTimeout(() => {
                      this.ui.isAudio[index] = true
                    }, 200)
                  },
                  onOnPopperHide: () => {
                    this.$nextTick(() => {
                      this.ui.isAudio[index] = false
                    })
                  }
                },

                {
                  default: () => h(resolveComponent('Button'), {
                    type: 'text',
                    ghost: true,
                    onClick: () => {
                      // 工作手机录音
                      if (row.submitType === 3) {
                        getWorkPhoneUrl({ uuid: row.callUuid }).then(res => {
                          if (res) {
                            const splitStr = '/ng'
                            if (res.data.includes(splitStr)) {
                              res.data = 'https://' + window.location.host + splitStr + res.data.split('/ng')[1]
                            }
                            this.audio = res.data
                          }
                        })
                      } else {
                        this.audio = getAudioUrl(
                          this.userExtend.duyanOrgId,
                          row.callTime,
                          row.callUuid
                        )
                      }
                    }
                  }, {
                    default: () => this.$t('clickPlay')
                  }),
                  content: () => h('div', {}, [
                    this.ui.isAudio[index] ? h(audios, {
                      src: this.audio
                    }) : ''
                  ]
                  )
                }

                // [
                //   h(
                //     'Button',
                //     {
                //       props: {
                //         type: 'text',
                //         ghost: true
                //       },
                //       on: {
                //         click: () => {
                //           // 工作手机录音
                //           if (row.submitType === 3) {
                //             getWorkPhoneUrl({ uuid: row.callUuid }).then(res => {
                //               if (res) {
                //                 const splitStr = '/ng'
                //                 if (res.data.includes(splitStr)) {
                //                   res.data = 'https://' + window.location.host + splitStr + res.data.split('/ng')[1]
                //                 }
                //                 this.audio = res.data
                //               }
                //             })
                //           } else {
                //             this.audio = getAudioUrl(
                //               this.userExtend.duyanOrgId,
                //               row.callTime,
                //               row.callUuid
                //             )
                //           }
                //         }
                //       }
                //     },
                //     this.$t('clickPlay')
                //   ),
                //   h(
                //     'div',
                //     {
                //       slot: 'content'
                //     },
                //     [
                //       this.ui.isAudio[index]
                //         ? h(audios, {
                //           props: {
                //             src: this.audio
                //           }
                //         })
                //         : ''
                //     ]
                //   )
                // ]

              )

              const btns = []
              if (isAudio) {
                if (
                  (this.isAgent && this.userExtend.operationVoiceSeatSwitch) ||
                  (this.isSupervisor &&
                    this.userExtend.operationVoiceTeamSwitch)
                ) {
                  btns.push(noRightBtn)
                } else {
                  btns.push(playBtn)
                }
              }

              return h('div', {}, btns)
            }
          },
          {
            renderHeader: (h, params) => {
              return h('span', {}, {
                default: () => [
                  h(resolveComponent('Icon'), {
                    custom: 'iconfont icon-robot',
                    style: 'position: relative; top:-1px; margin-right: 2px;color: #636C78;'
                  }),
                  h('span', {}, {
                    default: () => this.$t('k_robotText')
                  })
                ]
              })
            },
            align: 'center',
            width: 120,
            showColumn:
              (this.isRobot || this.isNewRobot) &&
              (this.isOutsource ? this.userExtend.robotSwitch : true),
            render: (h, { row }) => {
              return row.submitType === 1 && row.callUuid
                ? h(
                  'span',
                  {
                    onClick: () => {
                      this.ui.showRight = true
                      this.callUUid = row.callUuid

                      this.$nextTick(() => {
                        this.$refs.robotDetail.showRight = true
                      })
                    },
                    style: 'color: #ff724c;cursor: pointer'
                  },
                  '查看详情'
                )
                : ''
            }
          },
          {
            title: this.$t('k_nextFollowUpTime'),
            key: 'nextTime',
            type: 'date',
            bindmap: 'formatDate'
          },
          {
            title: this.$t('k_PTPment') + '(元)',
            width: 160,
            key: 'ptpAmount',
            type: 'input',
            bindmap: 'money',
            align: 'right'
          },
          {
            title: this.$t('k_PTPtime'),
            key: 'ptpTime',
            type: 'date',
            bindmap: 'formatDate'
          },
          {
            title: this.$t('k_talkOverTime'),
            key: 'createTime',
            type: 'date',
            bindmap: 'formatDate'
          },
          {
            title: this.$t('k_callingType'),
            key: 'callStyle',
            render: (h, { row }) => {
              return h(
                'span',
                {},
                row.callStyle === 0
                  ? this.$t('inBound')
                  : row.callStyle === 1
                    ? this.$t('outBound')
                    : ''
              )
            }
          },
          { title: this.$t('k_callType'), key: 'outcome', bindmap: 'outcome' },
          {
            title: this.$t('k_ringDuration'),
            key: 'ringDurtion',
            bindmap: 'time'
          },
          {
            title: this.$t('k_callDuration'),
            key: 'callDurtion',
            bindmap: 'time'
          },
          {
            title: this.$t('k_mainPhone'),
            width: 150,
            key: 'caller',
            desenKey: 'mobile'
          },
          { title: this.$t('k_agent'), key: 'operatorName', type: 'input' },
          { title: this.$t('k_affiliatedGroup'), key: 'teamName' },
          { title: '提交人', key: 'adminSubmitterName' },
          { title: this.$t('k_qcRemark'), key: 'comment', tooltip: true },
          { title: this.$t('k_qcDetail'),
            key: 'qcDetail',
            render: (h, params) => {
              const see = h(resolveComponent('Button'), {
                type: 'text',
                onClick: () => {
                  this.callUUid = params.row.callUuid
                  this.callLogIndex = params.index
                  this.callLogDetail = params.row
                  this.$refs.recordDetailRef.ui.showDrawer = true
                }
              }, {
                default: () => '查看'
              })
              return h('div', {}, params.row.callUuid ? [see] : '')
            }
          },
          { title: this.$t('k_delt'), key: 'deltName' },
          { title: this.$t('k_deltProduct'), key: 'productName' }
        ]

        this.addressColumns = [
          {
            title: this.$t('address') + this.$t('k_type'),
            key: 'type',
            bindmap: 'addressTypes',
            width: 120
          },
          {
            title: this.$t('address'),
            key: 'address',
            width: 330,
            tooltip: true
          },
          {
            title: this.$t('k_addressStatus'),
            key: 'state',
            bindmap: 'addressStatus'
          },
          { title: this.$t('k_outVisitCount'), key: 'visitCount', width: 90 },
          {
            title: this.$t('k_remark'),
            key: 'desc',
            width: 200,
            tooltip: true
          },
          {
            title: this.$t('k_resource'),
            key: 'source',
            width: 130,
            render: (h, { row }) => {
              let source = ''
              this.addressResource.map(item => {
                if (item.value === row.source) {
                  source = item.name
                }
              })
              return h('span', {}, (row.createByName || '') + source)
            }
          },
          {
            title: this.$t('k_createdTime'),
            key: 'createTime',
            bindmap: 'formatDate',
            fmt: 'yyyy-MM-dd hh:mm'
          },
          {
            title: this.$t('operation'),
            key: 'handle',
            fixed: 'right',
            width: 90,
            render: (h, { row }) => {
              // 删除
              const del = h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  onClick: () => {
                    this.$Modal.confirm({
                      title: '删除地址提示',
                      content: '地址: ' + row.address,
                      onOk: () => {
                        deleteAddress({ addressId: row.id }).then(res => {
                          this.$refs.addressTable.handleSearch()
                        })
                      }
                    })
                  }
                },
                {
                  default: () => '删除'
                }
              )

              const edit = h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  style: {
                    marginRight: '8px'
                  },
                  onClick: () => {
                    this.outvisit = {
                      type: 'edit',
                      id: row.id,
                      addressType: row.type,
                      address: row.address,
                      addressStatus: row.state,
                      desc: row.desc,
                      ifSelf:
                          row.createBy === this.userId ||
                          (this.userExtend.teamUrgeSwitch &&
                            row.teamId === this.teamId),
                      source: row.source
                    }
                    this.ui.ifOutvisitAddress = true

                    this.$nextTick(() => {
                      this.$refs.outvisit.ui.showModal = true
                    })
                  }
                },
                {
                  default: () => '编辑'
                }
              )

              const btns = []

              if (!this.ifCaseStatus) {
                btns.push(edit)
                if (row.source === 1 && row.createBy === this.userId) {
                  btns.push(del)
                }
              }

              return h('div', {}, btns)
            }
          }
        ]

        this.outvisitColumns = [
          {
            title: this.$t('k_applyTime'),
            key: 'applyTime',
            bindmap: 'formatDate'
          },
          {
            title: this.$t('k_auditStatus'),
            key: 'state',
            bindmap: 'outvisitAuditStatus'
          },
          { title: '外访地址', key: 'address', width: 170 },
          { title: '地址类型', key: 'type', bindmap: 'addressTypes' },
          { title: this.$t('k_applicant'), key: 'applyByName' },
          {
            title: this.$t('k_applyExplain'),
            key: 'instruction',
            tooltip: true
          },
          {
            title: this.$t('k_operation'),
            key: 'handle',
            fixed: 'right',
            width: 100,
            render: (h, { row }) => {
              const cancelApply = h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  onClick: () => {
                    this.$Modal.confirm({
                      title: '取消申请提示',
                      content: `<p>外访地址: ${row.address}</p>`,
                      loading: true,
                      onOk: () => {
                        cancelApplyVisit({ applyId: row.id }).finally(() => {
                          this.$Modal.remove()
                          this.$refs.outvisitTable.handleSearch()
                        })
                      }
                    })
                  }
                },
                {
                  default: () => '取消申请'
                }
              )

              const canceled = h('span', {}, '已取消')

              const btns = []

              if (
                row.state === 0 &&
                !this.ifCaseStatus
              ) {
                btns.push(cancelApply)
              } else if (row.state === -2) {
                btns.push(canceled)
              }
              return h('div', {}, btns)
            }
          }
        ]

        this.fileColumns = [
          { title: this.$t('k_uploadTime'), key: 'uploadTime' },
          { title: this.$t('k_fileNm'), key: 'fileName' },
          { title: this.$t('k_uploadBy'), key: 'uploadBy' },
          {
            title: this.$t('operation'),
            key: 'handle',
            render: (h, { row }) => {
              const checkBtn = h(
                resolveComponent('Button'),
                {
                  style: 'margin-right: 16px;',
                  type: 'text',
                  onClick: () => {
                    const url = row.fileUrl.replace('http', 'https')
                    window.open(url)
                  }
                },
                {
                  default: () => '下载'
                }
              )

              const deleteBtn = h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  onClick: () => {
                    this.$Modal.confirm({
                      title: '删除附件提示',
                      content: '<p>确定要删除此资料？</p>',
                      loading: true,
                      onOk: () => {
                        delCaseFile(this.caseId, row.id).then(res => {
                          if (res.status === 200 && res.success) {
                            this.$refs.fileTable &&
                                this.$refs.fileTable.handleSearch()
                            this.$refs.fileTables &&
                                this.$refs.fileTables.handleSearch()
                            this.$Modal.remove()
                          }
                        })
                      }
                    })
                  }
                },
                {
                  default: () => !this.ifCaseStatus ? '删除' : ''
                }
              )

              const btns = [checkBtn, deleteBtn]

              return h('div', btns)
            }
          }
        ]

        this.mediRecord = [
          { title: '申请时间', key: 'applyTime', bindmap: 'formatDate' },
          {
            title: '文书名称',
            key: 'letterName',
            render: (h, { row }) => {
              return h(
                'span',
                {
                  style: {
                    cursor: 'pointer',
                    color: 'rgb(255, 79, 31)'
                  },
                  onClick: () => {
                    this.auditing = {
                      id: row.id,
                      letterId: row.letterId,
                      info: row
                    }
                    this.auditingEntrance = 'casedetail'
                    this.ui.showAuditing = true

                    this.$nextTick(() => {
                      this.$refs.auditing.ui.showModal = true
                    })
                  }
                },
                row.letterName
              )
            }
          },
          {
            title: '审批状态',
            key: 'state',
            bindmap: 'mediateStatus',
            iconRender: true
          },
          {
            title: '操作',
            key: 'handle',
            fixed: 'right',
            render: (h, { row }) => {
              const cancel = h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  onClick: () => {
                    this.$Modal.confirm({
                      title: '取消文书提醒',
                      content: '取消后该文书申请将停止审批，是否确认取消？',
                      loading: true,
                      onOk: () => {
                        cancelMediation(row.id).finally(() => {
                          this.$Modal.remove()
                          this.$refs.mediation.handleSearch()
                        })
                      }
                    })
                  }
                },
                {
                  default: () => '取消申请'
                }
              )

              const btns = []
              if (row.state === 0) {
                btns.push(cancel)
              }

              return h('div', {}, btns)
            }
          }
        ]

        this.mediText = [
          { title: '创建时间', key: 'applyTime', bindmap: 'formatDate' },
          { title: '文书名称', key: 'letterName' },
          { title: '签署发起人', key: 'signInitiator' },
          {
            title: '签署状态',
            key: 'signFlowStatus',
            bindmap: 'signFlowStatus',
            iconRender: true
          },
          {
            title: '通知签署人方式',
            key: 'noticeType',
            bindmap: 'noticeTypes'
          },
          { title: '短信发送时间', key: 'noticeTime', bindmap: 'formatDate' },
          {
            title: '操作',
            key: 'handle',
            width: 100,
            fixed: 'right',
            render: (h, { row }) => {
              const check = h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  onClick: () => {
                    this.auditing = {
                      id: row.id,
                      letterId: row.letterId,
                      info: row
                    }
                    this.auditingEntrance = 'text'
                    this.ui.showAuditing = true

                    this.$nextTick(() => {
                      this.$refs.auditing.ui.showModal = true
                    })
                  }
                },
                {
                  default: () => '查看'
                }
              )

              const urge = h(
                resolveComponent('Button'),
                {
                  type: 'text',
                  style: {
                    marginLeft: '16px'
                  },
                  onClick: () => {
                    if (this.ui.urgeLoading) return

                    this.ui.urgeLoading = true
                    signUrge({
                      letterId: row.letterId
                    }).finally(() => {
                      this.ui.urgeLoading = false
                      this.$refs.mediation.handleSearch()
                    })
                  }
                },
                {
                  default: () => '催签'
                }
              )

              const btns = [check]
              const noticeTime = row.noticeTime
                ? new Date(row.noticeTime).getTime()
                : null

              const now = new Date().getTime()
              let isCanNotice = false
              if (noticeTime) {
                if (now - noticeTime >= 30 * 60 * 1000) {
                  isCanNotice = true
                }
              } else {
                isCanNotice = true
              }

              if (row.signFlowStatus === 1 && isCanNotice) {
                btns.push(urge)
              }

              return h('div', {}, btns)
            }
          }
        ]

        // 视频调解记录
        this.VideoMediation = [
          { title: '视频首次发起时间', key: 'createTime', width: 160, bindmap: 'formatDate' },
          { title: '姓名', key: 'name', width: 96 },
          { title: '手机号', key: 'mobile', width: 137 },
          { title: '身份证号', key: 'idCard', width: 180 },
          {
            title: '人脸认证结果/认证次数', width: 158,
            render: (h, { row }) => {
              return h(resolveComponent('Poptip'), {
                trigger: 'hover',
                placement: 'bottom-start',
                width: 204,
                transfer: true,
                padding: '0px 0px 0px 12px',
                style: {
                  color: '#FF4F1F',
                  cursor: 'pointer'
                }
              },

              {
                default: () => h('span', {}, row.authResult === 0 ? '未认证' : row.authResult === 1 ? `认证成功/${row.authRelVOList.length ? row.authRelVOList.length : 0}` : `认证失败/${row.authRelVOList.length ? row.authRelVOList.length : 0}`),
                title: () => h('div', {
                  style: {
                    fontWeight: '600',
                    paddingTop: '6px',
                    paddingBottom: '8px'
                  }
                }, '人脸认证记录'),
                content: () => h('div', {
                  style: {
                    maxHeight: '185px',
                    margin: '0px 12px 0px 0px'
                  }
                },
                row.authRelVOList !== null ? row.authRelVOList.map(el => {
                  const time = h('div', {}, el.createTime)
                  const chose = h('div', {}, el.authResult ? '认证成功' : '认证失败')
                  return h('div', {
                    style: {
                      display: 'flex',
                      justifyContent: 'space-between',
                      margin: '12px 0'
                    }
                  }, [time, chose])
                }) : [
                  h('div', {
                    style: {
                      padding: '12px 0px',
                      color: '#8D959F',
                      textAlign: 'center'
                    }
                  }, '暂无记录')
                ]
                )

              }

              )
            }
          },
          { title: '发起人', key: 'initiator', width: 96 },
          {
            title: '视频通知方式', width: 159,
            render: (h, { row }) => {
              return h('span', {}, row.noticeType ? '短信通知' : '被邀请人自主搜索')
            }
          },
          {
            title: '操作',
            width: 148,
            fixed: 'right',
            render: (h, { row }) => {
              const goHome = h('a', {
                style: {
                  color: '#FF4F1F',
                  fontSize: '12px',
                  marginRight: '16px'
                },
                onClick: () => {
                  this.ui.showVideoMediation = true
                  this.mediate_type = 1
                  this.$nextTick(() => {
                    this.$refs.videoMediation.startVideo()
                  })
                }
              }, '进入房间')

              const playVideo = h('a', {
                style: {
                  color: '#FF4F1F',
                  fontSize: '12px'
                },
                onClick: () => {
                  this.ui.showPalyVidio = true
                  this.List = row.videoRecordList
                  this.palyVadioApi = {
                    insert: this.List
                  }
                  this.palyVadioColumns = [
                    {
                      title: '视频上传时间',
                      key: 'videoUploadTime',
                      width: 210,
                      bindmap: 'formatDate'
                    }, {
                      title: '视频时长',
                      key: 'duration',
                      width: 210,
                      bindmap: 'time'
                    }, {
                      title: '操作',
                      width: 66,
                      render: (h, { row }) => {
                        return h('a', {
                          onClick: () => {
                            window.open(row.videoUrl)
                          }
                        }, '播放')
                      }
                    }
                  ]
                }
              }, '播放视频')
              const btns = []
              if (new Date(row.expireTime).getTime() >= new Date().getTime()) {
                btns.push(goHome)
              }
              if (row.videoRecordList !== null) {
                btns.push(playVideo)
              }

              return h('div', {
                // style: {
                //   display: 'flex',
                //   justifyContent: 'space-between'
                // }
              }, btns)
            }
          }
        ]

        this.lawsuitColumns = [
          { title: '诉讼申请ID', key: 'id' },
          { title: '诉讼案号', key: 'lawsuitNo' },
          { title: '案件状态', key: 'status', bindmap: 'lawsuitCaseStatus', width: 110 },
          { title: '诉讼进度', key: 'processName', width: 110 },
          { title: '被告', key: 'name' },
          { title: '身份证号', key: 'idCard' },
          { title: '标的', key: 'object', width: 100 },
          { title: '案件类型', key: 'typeName', width: 110 },
          { title: '代理律师', key: 'lawyer' },
          { title: '受理法院', key: 'courtName' },
          { title: '委外机构', key: 'orgDeltName' }
        ]
        this.messageColumns = [
          { title: '发送时间', key: 'sendTime', bindmap: 'formatDate' },
          {
            title: '发送类型',
            render: (h, { row }) => {
              return h('span', {}, row.type === 0 ? '单人发送' : '短信计划')
            }
          },
          {
            title: '发送状态', key: 'sendStatus',
            render: (h, { row }) => {
              let result = ''; let icons
              if (row.sendStatus === 1) {
                result = '成功'
                icons = h(resolveComponent('Icon'), {
                  type: 'iconfont icon-wancheng',
                  size: 14,
                  color: '#FF4F1F'
                })
              } else {
                result = '失败'
                icons = h(resolveComponent('Icon'), {
                  type: 'iconfont icon-guanbixiankuang',
                  size: 14,
                  color: '#888F98'
                })
              }
              return h('div', {
                style: {
                  display: 'flex',
                  alignItems: 'center'
                }
              }, [
                icons,
                h('span', {
                  style: {
                    marginLeft: '4px',
                    color: row.sendStatus === 0 ? '#FF4F1F' : ''
                  }
                }, result)
              ])
            }
          },
          { title: '联系人手机号', key: 'relationMobile', search: true, type: 'input', submitKey: 'relationMobile', desenKey: 'mobile' },
          { title: '联系人', key: 'contactName' },
          { title: '关系', key: 'relation' },
          { title: '短信内容', key: 'content' },
          {
            title: '接收状态', key: 'receiveStatus',
            render: (h, { row }) => {
              let result = ''; let icons
              if (row.receiveStatus === 1) {
                result = '成功'
                icons = h(resolveComponent('Icon'), {
                  type: 'iconfont icon-wancheng',
                  size: 14,
                  color: '#FF4F1F'
                })
              } else {
                result = '失败'
                icons = h(resolveComponent('Icon'), {
                  type: 'iconfont icon-guanbixiankuang',
                  size: 14,
                  color: '#888F98'
                })
              }
              return h('div', {
                style: {
                  display: 'flex',
                  alignItems: 'center'
                }
              }, [
                icons,
                h('span', {
                  style: {
                    marginLeft: '4px',
                    color: row.receiveStatus === 0 ? '#FF4F1F' : ''
                  }
                }, result)
              ])
            }
          },
          { title: '失败原因', key: 'reason' },
          { title: '发送人', key: 'createBy' }
        ]

        // 操作记录
        this.operationColumns = [
          { title: '操作时间', key: 'createTime', bindmap: 'formatDate' },
          {
            title: '类型',
            search: true,
            type: 'select',
            submitKey: 'types',
            searchItems: this.operationStatuss,
            render: (h, { row }) => {
              if (row.type === 3 || row.type === 10 || row.type === 14) {
                return h('span', {}, '分配管理')
              }
              if (row.type === 1 || row.type === 2 || row.type === 4 || row.type === 7 || row.type === 8 || row.type === 9) {
                return h('span', {}, '案件状态')
              }
              if (row.type === 5 || row.type === 11 || row.type === 12 | row.type === 13 || row.type === 19) {
                return h('span', {}, '案件管理')
              }
            }
          },
          {
            title: '操作内容',
            render: (h, { row }) => {
              if (row.type === 3 || row.type === 10) {
                return h('span', {}, row.dunnerName ? `案件分配至【${row.dunnerName}】` : row.teamName ? `案件分配至【${row.teamName}】` : `案件分配至【${row.depName}】`)
              }
              if (row.type === 14) {
                return h('span', {}, '【重新分配】')
              }
              if (row.type === 1 || row.type === 2 || row.type === 4 || row.type === 7 || row.type === 8 || row.type === 9) {
                return h('span', {}, row.type === 1 ? `案件状态更新为【结案】` : row.type === 2 ? `案件状态更新为【停催】` : row.type === 4 ? `案件状态更新为【停催恢复】` : row.type === 7 ? `案件状态更新为【作废】` : row.type === 8 ? `案件状态更新为【留案】` : `案件状态更新为【退案】`)
              }
              if (row.type === 5 || row.type === 11 || row.type === 12 | row.type === 13 || row.type === 19) {
                return h('span', {}, row.type === 5 ? `【案件更新】` : row.type === 11 ? `【删除】` : row.type === 12 ? `【彻底删除】` : row.type === 13 ? `【删除恢复】` : `【案件自动回收】`)
              }
            }
            // bindmap: 'operationStatus'
          },
          { title: '操作人', key: 'createByName' }
        ]
        // 智能策略记录
        this.strategyColums = [
          { title: '执行策略', key: 'strategyName', width: '50%' },
          { title: '执行时间', key: 'createTime', bindmap: 'formatDate', width: '50%' },
          {
            title: '执行路线', width: 100, fixed: 'right', render: (h, { row }) => {
              return h('span', {
                style: 'color: #1F7EE1; cursor: pointer',
                onClick: () => {
                  row.result = row.matchResult
                  row.execTime = row.createTime
                  this.detailInfo = row
                  this.ui.showRoute = true
                  setTimeout(() => {
                    this.$refs.executionRoute.showDrawer = true
                  }, 100)
                }
              }, '查看')
            }
          }
        ]

        // 还款计划
        this.getInitRefund()

        // 借据信息
        this.getLoanList()

        let contacts = this.caseInfo.fields.contacts
        const hideContacts = contacts
          ? contacts
            .split(/[,，]/g)
            .map(item => this.hidePhone(item))
            .join(',')
          : null

        this.updateContacts = {
          create_time: this.caseInfo.fields.contacts_update_time
            ? formatDate(parseInt(this.caseInfo.fields.contacts_update_time))
            : formatDate(this.caseInfo.createTime),
          contacts,
          syncStatus: this.caseInfo.syncStatus,
          hideContacts
        }
        // 案件联系人
        this.caseContacts = {
          create_time: this.caseInfo.fields.contacts_update_time
            ? formatDate(parseInt(this.caseInfo.fields.contacts_update_time))
            : formatDate(this.caseInfo.createTime),
          contacts: contacts ? contacts.replace(/\s+/g, '') : '',
          syncStatus: this.caseInfo.syncStatus
        }

        if (contacts) {
          try {
            const contactFix = JSON.parse(contacts)
            if (Array.isArray(contactFix)) {
              this.concatData = []
              for (const value of contactFix) {
                this.concatData.push({
                  mobile: value
                })
              }
            } else {
              this.concatData = []
              contacts = contacts.match(
                /((((13[0-9])|(15[^4])|(18[0,1,2,3,5-9])|(17[0-8])|(16[0-8])|(19[0-8])|(147))\d{8})|((\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}))?/g
              )
              for (const value of contacts) {
                if (value.trim() && value.trim().length === 11) {
                  this.concatData.push({
                    mobile: value
                  })
                }
              }
            }
          } catch (error) {
            contacts = contacts.match(
              /((((13[0-9])|(15[^4])|(18[0,1,2,3,5-9])|(17[0-8])|(16[0-8])|(19[0-8])|(147))\d{8})|((\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}))?/g
            )
            this.concatData = []
            for (const value of contacts) {
              if (value.trim() && value.trim().length === 11) {
                this.concatData.push({
                  mobile: value
                })
              }
            }
          }
        }

        this.getContact(0, async() => {
          this.$nextTick(async() => {
            // 恢复状态
            if (
              Number(this.urgeRefund) === Number(this.caseId)
            ) {
              this.ui.selfUrgeRefund = true

              this.refundForm.desc = ''
              this.refundForm.callType = ''
              this.refundForm.actionType = ''
              if (
                this.openedRefundForm &&
                Number(this.openedRefundForm.caseId) ===
                Number(this.$route.params.id)
              ) {
                this.refundForm = this.openedRefundForm
                delete this.refundForm.caseId
              }
            } else {
              this.ui.selfUrgeRefund = false
              this.refundForm = deepClone(REFOUND_FORM)
              this.operationStates = this.caseInfo.operationState
            }
          })

          if (
            this.isInCalling.phone &&
            Number(this.isInCalling.caseId) === Number(this.caseId)
          ) {
            await this.getContactId(this.isInCalling.phone)

            if (this.refundForm.contactsId) {
              const arr = [...this.contacts, ...this.invalidContacts]

              for (const c of arr) {
                if (c.id === this.refundForm.contactsId) {
                  this.refundForm.contactName = c.name
                  this.refundForm.relationType = c.relationType
                  this.refundForm.warning = c.warning
                  this.refundForm.mobile = c.mobile
                  break
                }
              }
            } else {
              this.$Message.error({
                content: `该联系人号码被删除，无法填写${this.$t(
                  'collection'
                )}，建议重新添加此联系人或重新填写其他${this.$t(
                  'reminder'
                )}！`,
                duration: 4
              })
              this.missContactCall = this.isInCalling.phone
              this.ui.selfUrgeRefund = false
              return
            }
          }

          if (this.$route.params.phone) {
            this.ui.showChangeArrow = false

            await this.getContactId(this.$route.params.phone)
            if (this.refundForm.contactsId) {
              const arr = [...this.contacts, ...this.invalidContacts]

              for (const c of arr) {
                if (c.id === this.refundForm.contactsId) {
                  this.refundForm.contactName = c.name
                  this.refundForm.relationType = c.relationType
                  this.refundForm.warning = c.warning
                  this.refundForm.mobile = c.mobile
                  break
                }
              }

              const sendData = {
                caseId: Number(this.caseId),
                contactsId: this.refundForm.contactsId,
                callUuid: this.callUuid,
                callStyle: 1
              }

              this.ui.selfUrgeRefund = true
              addAutomatic(sendData).then(res => {
                if (res.success) {
                  const list = this.$store.state.app.tagNavList
                  for (const key in list) {
                    if (
                      list[key].params &&
                      list[key].params.id === this.caseId
                    ) {
                      delete list[key].params.phone
                    }
                  }

                  this.setTagNavList(list)
                } else {
                  this.ui.selfUrgeRefund = false
                }
              })
            } else {
              this.$Message.error({
                content: `该联系人号码被删除，无法填写${this.$t(
                  'reminder'
                )}，建议重新添加此联系人或重新填写其他${this.$t(
                  'reminder'
                )}！`,
                duration: 4
              })
              this.missContactCall = this.$route.params.phone
              this.ui.selfUrgeRefund = false
              return
            }
          }

          if (this.$route.params.isFifo) {
            this.ui.showChangeArrow = false
            this.missContactCall = this.$route.params.phone
            const list = this.$store.state.app.tagNavList
            for (const key in list) {
              if (list[key].params && list[key].params.id === this.caseId) {
                delete list[key].params.isFifo
              }
            }

            this.setTagNavList(list)
          }
        })

        // 失效联系人列表
        this.getContact(1)

        this.getLatestComment()

        // 催收小结
        this.talkNoteInjectSearchData = {
          outSerialNo: this.caseInfo.outSerialNo,
          orgDeltId: this.caseInfo.orgDeltId,
          caseId: this.caseId
        }
        this.getTalkContent(this.talkNoteInjectSearchData)

        // 催记
        this.recordInjectSearchData = {
          outSerialNo: this.caseInfo.outSerialNo,
          orgDeltId: this.caseInfo.orgDeltId,
          caseId: this.caseId
        }
        // 共债催记
        this.talkCaseSearchData = {
          caseId: this.caseId
        }

        // 共债案件
        this.debtSearchData = {
          caseId: this.caseId,
          tag: 'on'
        }

        // 地址
        this.addressInjectSearchData = {
          caseId: this.caseId
        }

        // 外访
        this.outvisitInjectSearchData = {
          caseId: this.caseId
        }
        this.visitRecordInjectSearchData = {
          caseId: this.caseId
        }

        // 还款、划扣
        this.billInjectSearchData = Object.assign(this.recordInjectSearchData, {
          repListType: 2
        })

        // 减免
        this.reduceSearchData = {
          outSerialNo: this.caseInfo.outSerialNo,
          orgDeltId: this.caseInfo.orgDeltId,
          caseId: this.caseId
        }
        // 协催反欺诈
        this.urgingSearchData = {
          caseId: this.caseId,
          status: 1
        }

        // 协办
        this.assistInjectSearchData = { action: 0, caseId: this.caseId }

        // 补充资料
        this.injectFileSearchData = {
          id: this.caseId
        }

        // 调解文书
        this.mediRecordInjectSearchData = {
          caseId: this.caseId
        }
        this.mediTextInjectSearchData = {
          caseId: this.caseId,
          startStatuses: '1'
        }

        // 诉讼案件
        this.lawsuitInjectSearchData = {
          caseId: this.caseId
        }
        // 短信记录
        this.messageInjectSearchDate = {
          caseId: this.caseId
        }
        // 操作记录
        this.operationInjectSearchData = {
          caseId: this.caseId,
          types: '1,2,3,4,5,7,8,9,10,11,12,13,14,19'
        }
        if (this.caseInfo.cmbcCaseDetailInfo) {
          this.getAccountInfo()
        } else {
          let tplFields = []
          const fields = []
          let required = null
          let unRequired = null
          for (const c in this.caseInfo.tplFields) {
            if (this.caseInfo.tplFields[c].indexOf('&') === -1) {
              tplFields.push(this.caseInfo.tplFields[c])
            } else {
              tplFields.push(this.caseInfo.tplFields[c].split('&')[0])
            }
          }

          for (const e in this.caseInfo.fields) {
            if (e.split('|')[0]) {
              fields.push(e.split('|')[0])
            } else {
              fields.push(e)
            }
          }

          const intersection = fields.filter(v => !tplFields.includes(v))
          const newTpl = [...new Set([...tplFields, ...intersection])]
          tplFields = newTpl
          this.caseInfo.tplFields = { ...newTpl }

          unRequired = tplFields.filter(c => {
            return fields.indexOf(c) !== -1
          }) // 获取非必填字段数组
          required = tplFields.filter(c => {
            return fields.indexOf(c) === -1
          }) // 获取必填字段数组

          const userBaseRequired = []
          let isOverdueDateIndex = -1
          for (const c in required) {
            if (required[c] === 'overdue_days') {
              const data = {
                feGroup: 'Base',
                name: '逾期天数',
                required: 'false',
                type: 'String',
                value: 'overdue_days'
              }
              userBaseRequired.push(data)
            }
            if (required[c] === 'overdue_date') {
              unRequired.push('overdue_date')
              this.caseInfo.fields.overdue_date = this.caseInfo.overdueDate
              isOverdueDateIndex = c
            }
            for (const e in this.caseTplFields.userBaseRequired) {
              if (required[c] === this.caseTplFields.userBaseRequired[e].value) {
                userBaseRequired.push(this.caseTplFields.userBaseRequired[e])
              }
            }
          } // 获取必填字段字典
          if (isOverdueDateIndex !== -1) {
            required.splice(isOverdueDateIndex, 1)
          }
          for (const item in this.caseInfo.tplFields) {
            if (unRequired.indexOf(tplFields[item]) === -1) {
              // 必填字段解析
              for (const c of userBaseRequired) {
                if (this.caseInfo.tplFields[item] === c.value) {
                  let value = this.caseInfo[linetoHump(c.value)]
                  if (c.value === 'own_mobile') {
                    value =
                      this.caseInfo[linetoHump(c.value)] || this.caseInfo.mobile
                  }
                  if (!value && value !== 0) {
                    continue
                  }

                  if (c.type === 'Money') {
                    value = moneyli2yuan(value)
                  }
                  if (c.type === 'Date') {
                    value = formatDate(value)
                  }
                  if (c.value === 'overdue_days') {
                    if (this.userExtend.enableAutoUpdateOverdueDays) {
                      value =
                        Math.ceil((new Date().getTime() - this.caseInfo.overdueDate) / (24 * 3600 * 1000) - 1) >= 0
                          ? Math.ceil((new Date().getTime() - this.caseInfo.overdueDate) / (24 * 3600 * 1000) - 1) : ''
                    } else {
                      value = this.caseInfo.overdueDays
                        ? this.caseInfo.overdueDays
                        : this.$t('none')
                    }
                  }

                  this[`case${c.feGroup}Info`].push({
                    name: c.name,
                    value,
                    key: c.value
                  })
                  if (
                    c.value !== 'id_card' &&
                    c.value !== 'own_mobile' &&
                    c.value !== 'out_serial_no'
                  ) {
                    if (c.value === 'overdue_days') {
                      value = this.caseInfo.overdueDays
                    }
                    this[`edit${c.feGroup}Info`].push({
                      name: c.name,
                      key: c.value,
                      value,
                      type: c.type
                    })
                  }
                }
              }
            } else {
              // 非必填字段与自定义字段解析
              let key = null
              for (const e in this.caseInfo.fields) {
                if (
                  e.indexOf('|') !== -1 &&
                  e.split('|')[0] === this.caseInfo.tplFields[item]
                ) {
                  key = e
                } else if (
                  this.caseInfo.tplFields[item].indexOf('&') !== -1 &&
                  (e === this.caseInfo.tplFields[item].split('&')[0] ||
                    e.split('|')[0] ===
                    this.caseInfo.tplFields[item].split('&')[0])
                ) {
                  key = e
                } else if (e === this.caseInfo.tplFields[item]) {
                  key = e
                }
              }

              if (key.indexOf('|') === -1) {
                let value = this.caseInfo.fields[key] || this.$t('none')
                if (this.caseTplFields.base[key]) {
                  if (this.caseTplFields.base[key].type === 'Money') {
                    value =
                      value === this.$t('none')
                        ? this.$t('none')
                        : value
                  }
                  if (this.caseTplFields.base[key].value === 'overdue_date') {
                    value = this.caseInfo.overdueDate
                      ? formatDate(this.caseInfo.overdueDate, 'yyyy-MM-dd')
                      : this.$t('none')
                  }
                  if (this.caseTplFields.base[key].value === 'overdue_days') {
                    if (this.userExtend.enableAutoUpdateOverdueDays) {
                      value = this.caseInfo.overdueDays
                        ? Math.ceil(
                          (new Date().getTime() - this.caseInfo.overdueDate) /
                          (24 * 3600 * 1000) -
                          1
                        ) >= 0
                          ? Math.ceil(
                            (new Date().getTime() - this.caseInfo.overdueDate) /
                            (24 * 3600 * 1000) -
                            1
                          )
                          : 0
                        : this.$t('none')
                    } else {
                      value = this.caseInfo.overdueDays
                        ? this.caseInfo.overdueDays
                        : this.$t('none')
                    }
                  }
                  if (key === 'contacts') {
                    this.caseBaseInfo.push({
                      value:
                        value
                          .split(/[,，]/g)
                          .map(i => this.hidePhone(i))
                          .join(',') || '',
                      key,
                      name: this.caseTplFields.base[key].name
                    })
                  } else if (key !== 'dunner_code') {
                    this.caseBaseInfo.push({
                      value,
                      key,
                      name: this.caseTplFields.base[key].name
                    })
                  }

                  if (this.caseTplFields.base[key].value === 'overdue_days') {
                    value = this.caseInfo.overdueDays
                  }

                  if (this.caseTplFields.base[key].value !== 'contacts') {
                    this.editBaseInfo.push({
                      value,
                      key: this.caseTplFields.base[key].value,
                      name: this.caseTplFields.base[key].name,
                      type: this.caseTplFields.base[key].type
                    })
                  }
                } else if (this.caseTplFields.user[key]) {
                  if (this.caseTplFields.user[key].type === 'Money') {
                    value =
                      value === this.$t('none')
                        ? this.$t('none')
                        : moneyli2yuan(value)
                  }
                  this.caseUserInfo.push({
                    value,
                    key,
                    name: this.caseTplFields.user[key].name
                  })

                  this.editUserInfo.push({
                    value,
                    key: this.caseTplFields.user[key].value,
                    name: this.caseTplFields.user[key].name,
                    type: this.caseTplFields.user[key].type
                  })
                }
              } else {
                if (key.indexOf('||') === -1) {
                  const targetKey = key.split('|')[0]
                  const value = desenRulesAll(targetKey, this.caseInfo.fields[key]) || this.$t('none')
                  this.caseBaseInfo.push({
                    value,
                    key: targetKey,
                    name: key.split('|')[1]
                  })
                  this.editBaseInfo.push({
                    value,
                    key: targetKey,
                    name: key.split('|')[1]
                  })
                } else {
                  const targetKey = key.split('||')[0]
                  const value = desenRulesAll(targetKey, this.caseInfo.fields[key]) || this.$t('none')
                  this.caseBaseInfo.push({
                    value,
                    key: targetKey,
                    name: key.split('||')[1]
                  })
                  this.caseSearchInfo.push({
                    value,
                    key: targetKey,
                    name: key.split('||')[1]
                  })
                }
              }
            }
          }

          if (this.caseInfo.delayNo) {
            this.caseBaseInfo.push({
              value: this.caseInfo.delayNo,
              key: 'delayNo',
              name: '留案号'
            })
          }
          if (this.caseInfo.endNo) {
            this.caseBaseInfo.push({
              value: this.caseInfo.endNo,
              key: 'endNo',
              name: '结案号'
            })
          }
          // console.log(this.caseInfo);
          const delt = {
            value: this.caseInfo.orgDeltName || '--',
            name: this.$t('k_delt')
          }
          const product = {
            value: this.caseInfo.productName || '--',
            name: this.$t('k_deltProduct')
          }
          const outSerialNo = {
            value: this.caseInfo.outBatchNo || '--',
            name: this.$t('k_baileBatchNo')
          }
          const outBatchNo = {
            value: this.caseInfo.batchNo || '--',
            name: this.$t('k_batchNum')
          }
          this.caseUserInfo = [
            ...this.caseUserInfo,
            // delt,
            product,
            outSerialNo,
            outBatchNo
          ]
          // console.log(this.caseUserInfo);
        }
        // 催收结果
        let actionTypeName = '--'
        this.refundResult.map(item => {
          if (item.value === this.caseInfo.actionType) {
            actionTypeName = item.name
          }
        })
        // 电话结果
        let refundCallResultName = '--'
        this.refundCallResult.map(item => {
          if (item.value === this.caseInfo.callType) {
            refundCallResultName = item.name
          }
        })
        this.caseTalkOver = [
          {
            name: '上次跟进时间',
            value: this.caseInfo.lastFollowTime
              ? formatDate(this.caseInfo.lastFollowTime)
              : '--'
          },
          { name: '跟进次数', value: this.caseInfo.followCount },
          {
            name: `${this.$t('collection')}进程`,
            value: this.caseInfo.operationStateName
          },
          { name: `最近${this.$t('collection')}结果`, value: actionTypeName },
          { name: '最近电话结果', value: refundCallResultName },
          {
            name: 'PTP金额',
            value:
              this.caseInfo.ptpAmount || this.caseInfo.ptpAmount === 0
                ? moneyli2yuan(this.caseInfo.ptpAmount)
                : '--'
          },
          {
            name: '已还款金额',
            value:
              this.caseInfo.repTotal || this.caseInfo.repTotal === 0
                ? moneyli2yuan(this.caseInfo.repTotal)
                : '--'
          },
          {
            name: '已减免金额',
            value:
              this.caseInfo.reductionTotal || this.caseInfo.reductionTotal === 0
                ? moneyli2yuan(this.caseInfo.reductionTotal)
                : '--'
          },
          {
            name: '剩余金额',
            value:
              this.caseInfo.remainAmount || this.caseInfo.remainAmount === 0
                ? moneyli2yuan(this.caseInfo.remainAmount)
                : '--'
          }
        ]

        this.caseBaseRows = Math.ceil(
          this.caseBaseInfo.length / this.ui.caseInfoColNum
        )
        this.caseUserRows = Math.ceil(
          this.caseUserInfo.length / this.ui.caseInfoColNum
        )
        this.caseTalkRows = Math.ceil(
          this.caseTalkOver.length / this.ui.caseInfoColNum
        )

        this.ui.loadingCaseDetail = false
      } else {
        this.updateCtiCall({ key: 'urgeRefund', data: false })
        this.$Modal.error({
          title: this.$t('caseInvalid'),
          content: res.message || this.$t('l_caseInvalid'),
          onOk: () => {
            this.$emit('on-close', false)
          }
        })

        this.setIsChangeCase(false)
      }
    },
    editTag() {
      this.ui.tagModal = true
      this.tagSelect = this.curTag
      this.tagSearch = {
        caseIds: [this.caseId],
        allSelect: false
      }

      this.$nextTick(() => {
        this.$refs.tagModal.ui.showModal = true
      })
    },
    // 切换案件
    changeCase(index, type) {
      if (this.mediating) return this.$Message.error('请先结束当前正在进行的视频调解后才能进行操作')
      if (this.ui.selfUrgeRefund || this.ui.loadingContact) {
        return
      }
      if (!this.caseIndex && this.caseIndex !== 0) return
      this.ui.selfUrgeRefund = false

      if (index < 0) {
        this.$Modal.error({
          title: '操作失败',
          content:
            '该案件已是本页第一个案件，如需查看其他案件请返回我的案件界面点击上一页/下一页；<br>(注：上一个，下一个按钮仅用于操作所在页数的全部案件)'
        })
        return
      }

      let caseIdsLen = (typeof this.caseIds === 'string' ? JSON.parse(this.caseIds) : this.caseIds).length
      if (index >= caseIdsLen) {
        this.$Modal.error({
          title: '操作失败',
          content:
            '该案件已是本页最后一个案件，如需查看其他案件请返回我的案件界面点击上一页/下一页；<br>(注：上一个，下一个按钮仅用于操作所在页数的全部案件)'
        })
        return
      }

      setTimeout(() => {
        if (this.ui.selfUrgeRefund || this.ui.loadingContact) {
          return
        }

        const list = this.tagNavList
        for (const key in list) {
          if (index >= 0 && index < this.caseIds.length) {
            if (
              list[key].params &&
              list[key].params.id &&
              Number(list[key].params.id) === Number(this.caseIds[index].id)
            ) {
              index = type === 'pre' ? index - 1 : index + 1
              this.changeCase(index, type)
              return
            }
          }
        }

        this.caseIndex = index

        this.setIsChangeCase(true)
        this.isContact = false
        this.isInvalidContact = false
        this.ui.loadingContact = true
        const currentRoute = this.$router.currentRoute.value
        const { params } = currentRoute

        const route = {
          name: 'caseDetail',
          params: {
            id: JSON.parse(params.caseIds)[this.caseIndex].id,
            sortBy: 'myCase',
            name: JSON.parse(params.caseIds)[this.caseIndex].name,
            caseIds: params.caseIds,
            index: this.caseIndex
          },
          meta: {
            beforeCloseName: 'before_close_normal',
            notCache: true,
            title: '案子-' + JSON.parse(params.caseIds)[this.caseIndex].name
          }
        }

        const tagsList = []
        for (const tag of this.tagNavList) {
          if (tag.name === 'caseDetail') {
            // 将要切换案件已打开
            if (Number(tag.params.id) === Number(route.params.id)) {
              tagsList.push(route)
            } else {
              // 将要切换案件未打开，则替换当前页面
              if (Number(tag.params.id) === Number(this.caseId)) {
                tagsList.push(route)
              } else {
                tagsList.push(tag)
              }
            }
          } else {
            tagsList.push(tag)
          }
        }

        this.setTagNavList(tagsList)
        this.$router.push(route)
      }, 500)
    },
    changeModalCase(index, type) {
      if (this.mediating) return this.$Message.error('请先结束当前正在进行的视频调解后才能进行操作')
      if (this.ui.selfUrgeRefund || this.ui.loadingContact) {
        return
      }
      if (!this.caseIndex && this.caseIndex !== 0) return
      this.ui.selfUrgeRefund = false

      setTimeout(() => {
        if (this.ui.selfUrgeRefund || this.ui.loadingContact) {
          return
        }

        if (index >= 0 && index < this.caseIds.length) {
          index = type === 'pre' ? index - 1 : index + 1
        }

        if (index < 0) {
          this.$Modal.error({
            title: '操作失败',
            content:
              '该案件已是本页第一个案件，如需查看其他案件请返回我的案件界面点击上一页/下一页；<br>(注：上一个，下一个按钮仅用于操作所在页数的全部案件)'
          })
          return
        }
        if (index >= this.caseIds.length) {
          this.$Modal.error({
            title: '操作失败',
            content:
              '该案件已是本页最后一个案件，如需查看其他案件请返回我的案件界面点击上一页/下一页；<br>(注：上一个，下一个按钮仅用于操作所在页数的全部案件)'
          })
          return
        }
        this.caseIndex = index
        this.caseId = this.caseIds[index]
        this.ui.loadingContact = true
        this.initPage()
      }, 500)
    },
    openNewContact() {
      this.contactForm = {
        mobile: '',
        name: '',
        relationType: '',
        sign: 0,
        warning: 0,
        status: 0,
        desc: ''
      }
      this.ui.showContactsModal = true

      this.$nextTick(() => {
        this.$refs.contactsModal.ui.contactModal = true
      })
    },
    contactDone(res) {
      if (this.editingContact) {
        this.editingContact = res.data
        this.editingContact = null
      } else {
        this.contacts.push(res.data)
      }

      this.getContact(0)
      this.getContact(1)

      if (res.success) {
        this.$refs.caseConcat.$refs.mainTable.handleSearch()
      }

      this.caseContacts = {
        create_time: this.caseInfo.fields.contacts_update_time
          ? formatDate(parseInt(this.caseInfo.fields.contacts_update_time))
          : formatDate(this.caseInfo.createTime),
        contacts: this.caseInfo.fields.contacts
          ? this.caseInfo.fields.contacts.replace(/\s+/g, '')
          : '',
        syncStatus: this.caseInfo.syncStatus
      }
    },
    getContact(status = 0, _callback) {
      if (status === 0) {
        this.isContact = false
      } else {
        this.isInvalidContact = false
      }
      // 获取民生银行通讯录
      if (this.caseInfo.cmbcCaseDetailInfo) {
        getByCaseId({ caseId: this.caseId, status }).then(res => {
          if (res) {
            if (status === 0) {
              this.isContact = true
              this.contacts = []
              this.contacts = res.data.filter((item, index) => {
                item.id = '10000' + index
                item.ownMobile = item.mobile
                return item.field != null
              })
            } else {
              this.isInvalidContact = true
              this.invalidContacts = []
            }
            _callback && _callback()
          }
        })
        return
      }
      // 获取通讯录
      getContact({ caseId: this.caseId, status }).then(res => {
        if (res) {
          if (status === 0) {
            this.isContact = true
            this.contacts = []
            this.contacts = res.data.list
            for (const key in this.contacts) {
              if (this.contacts[key] && this.contacts[key].name) {
                this.contacts[key].name = desenRulesAll('name', this.contacts[key].name)
              }

              this.contacts[key].ownMobile = this.contacts[key].mobile
              if (this.contacts[key].mobile[0] === '0') {
                this.contacts[key].ownMobile = this.contacts[key].mobile.slice(
                  1
                )
              }
            }
          } else {
            this.isInvalidContact = true
            this.invalidContacts = []
            this.invalidContacts = res.data.list
            for (const key in this.invalidContacts) {
              if (this.invalidContacts[key] && this.invalidContacts[key].name) {
                this.invalidContacts[key].name = desenRulesAll('name', this.invalidContacts[key].name)
              }
              this.invalidContacts[key].ownMobile = this.invalidContacts[
                key
              ].mobile
              if (this.invalidContacts[key].mobile[0] === '0') {
                this.invalidContacts[key].ownMobile = this.invalidContacts[
                  key
                ].mobile.slice(1)
              }
            }
          }
          _callback && _callback()
        }
      })
    },
    hidePhone(mobile) {
      return desenRulesAll('mobile', mobile)
    },
    hideName(name) {
      return desenRulesAll('name', name)
    },
    // 通过慧捷SDK打电话
    async startCallHJ(contact) {
      if (
        (this.ui.loadingContact || this.ui.selfUrgeRefund) &&
        !this.curContacts
      ) { return }
      if (this.isInCall) {
        return this.$Message.error(this.$t('l_requirePhone'))
      }
      if (this.urgeRefund) {
        if (this.refundForm.contactsId === contact.id) {
          this.ui.ifHideDrag = true
        } else {
          this.$Message.error(this.$t('l_requireCall'))
        }
        return
      }
      if (!window.wellClient || !window.wellClient.isLogin()) {
        return this.$Message.error('请先登录坐席')
      }

      this.ui.refreshLoading = true
      this.ui.loadingContact = true

      // this.setCurOutbounce({
      //   caseId: Number(this.$route.params.id),
      //   operationState: this.operationStates,
      //   contactsId: contact.id
      // })

      // this.setIsOutbounceLock(true)
      // this.curContacts = contact
      // this.isCalling = true
      // this.setIsChangeCase(true)

      // const sendData = {
      //   mobile: contact.mobile,
      //   caseId: this.caseId,
      //   contactsId: contact.id
      // }
      // const result = await callWorkPhone(sendData)

      this.updateCtiCall({ key: 'urgeRefund', data: this.caseId })

      this.refundForm.cmbcConName = contact.name
      this.refundForm.contactsId = contact.id
      this.refundForm.cmbcRelation = contact.relationType
      this.refundForm.cmbcConMobile = contact.mobile
      this.refundForm.warning = contact.warning
      this.refundForm.cmbcField = contact.field
      this.refundForm.submitType = 4 // 民生提交催记
      // 加密手机号
      //       {
      //     "caseId":"OC220511001288015",
      //     "collector":"123455",
      //     "phoneField":"nonImd2FmlTel",
      //     "serviceTel":"18612345678"
      // }
      const phone = await encryPhone({
        caseId: this.caseInfo.outSerialNo,
        collector: this.userNo,
        phoneField: contact.field || 'mblphNo',
        serviceTel: ''
      })
      // console.log(phone.data.encryptParam)
      wellClient.makeCall({
        customerPhone: phone.data,
        options: { busiType: 'MSWW' },
        prefix: '90'
      })
        .done(() => {
          this.ui.loadingContact = false
          this.ui.refreshLoading = false
          this.ui.selfUrgeRefund = true
        })
        .fail((err) => {
          if (err.errorMessage) {
            this.$Notice.error({
              duration: 0,
              title: '呼叫失败',
              desc: err.errorMessage
            })
          }
          this.ui.loadingContact = false
          this.ui.refreshLoading = false
          this.callClear()
        })
    },
    // 通过工作手机拨打电话
    async startCallPhone(contact, key) {
      if (
        (this.ui.loadingContact || this.ui.selfUrgeRefund) &&
        !this.curContacts
      ) { return }
      if (this.isInCall) {
        return this.$Message.error(this.$t('l_requirePhone'))
      }
      if (this.urgeRefund) {
        if (this.refundForm.contactsId === contact.id) {
          this.ui.ifHideDrag = true
        } else {
          this.$Message.error(this.$t('l_requireCall'))
        }
        return
      }

      this.ui.refreshLoading = true
      this.ui.loadingContact = true

      const sendData = {
        mobile: contact.mobile,
        caseId: this.caseId,
        contactsId: contact.id,
        slot: key
      }
      // const result = await callWorkPhone(sendData)

      this.updateCtiCall({ key: 'urgeRefund', data: this.caseId })

      this.refundForm.contactName = contact.name
      this.refundForm.contactsId = contact.id
      this.refundForm.relationType = contact.relationType
      this.refundForm.mobile = contact.mobile
      this.refundForm.warning = contact.warning
      if (this.userExtend.enableHiddenPhone) {
        const data = await getContactMobile(this.refundForm.contactsId)
        registerCall(data.data).then(async data => {
          const mobile = data.data.substr(-4)
          sendData.mobile = mobile
          const result = await callWorkPhone(sendData)
          if (result) {
            this.$Message.success('工作手机正在呼出，请注意查看手机')
            this.updateCtiCall({ key: 'uuid', data: result.data })
            this.ui.selfUrgeRefund = true
          } else {
            this.callClear()
          }
          this.ui.refreshLoading = false
          this.ui.loadingContact = false
        })
      } else {
        const result = await callWorkPhone(sendData)
        if (result) {
          this.$Message.success('工作手机正在呼出，请注意查看手机')
          this.updateCtiCall({ key: 'uuid', data: result.data })
          this.ui.selfUrgeRefund = true
        } else {
          this.callClear()
        }
        this.ui.refreshLoading = false
        this.ui.loadingContact = false
      }
    },
    async startCall(contact, _forceSave) {
      // 区分一下单个是工作手机还是呼叫中心
      // if (this.userExtend.workPhoneAgent && !(this.userExtend.enableConnectedDuyan && this.$store.state.user.dyAgent)) {
      //   // 工作手机呼出
      //   return this.startCallPhone(contact)
      // }
      if (this.wellPhoneSwitch && !(this.userExtend.enableConnectedDuyan && this.$store.state.user.dyAgent)) {
        // 通过慧捷SDK呼出
        return this.startCallHJ(contact)
      }
      if (
        (this.ui.loadingContact || this.ui.selfUrgeRefund) &&
        !this.curContacts
      ) { return }
      if (this.isInCall) {
        return this.$Message.error(this.$t('l_requirePhone'))
      }

      if (!this.isCtiReady && this.caseId) {
        return this.$Message.error(this.$t('l_ctiConnectFail'))
      }

      if (this.urgeRefund) {
        if (this.refundForm.contactsId === contact.id) {
          this.ui.ifHideDrag = true
        } else {
          this.$Message.error(this.$t('l_requireCall'))
        }
        return
      }
      const sendData = {
        caseId: this.caseId,
        contactId: contact.id
      }
      getCaseReturn(sendData).then(res => {
        if (res) {
          if (res.data.checkResult === 0) {
            this.sendCall(contact, _forceSave)
          }
          if (res.data.checkResult === 1) {
            this.$Modal.info({
              title: '全局管控提示',
              content: `该案件管控呼出当日上限 ${res.data.caseTimesDay} 次，已达到次数上限，当日无法呼出`,
              loading: true,
              onOk: () => {
                this.$Modal.remove()
              }
            })
          }
          if (res.data.checkResult === 2 && res.data.limitHourTime === null) {
            this.$Modal.info({
              title: '全局管控提示',
              content: `该案件单个号码拨打上限 ${res.data.limitDayTime} 次/天，当前无法呼出`,
              loading: true,
              onOk: () => {
                this.$Modal.remove()
              }
            })
          }
          if (res.data.checkResult === 2 && res.data.limitDayTime === null) {
            this.$Modal.info({
              title: '全局管控提示',
              content: `该案件单个号码拨打上限 ${res.data.limitHourTime} 次/小时，当前无法呼出`,
              loading: true,
              onOk: () => {
                this.$Modal.remove()
              }
            })
          }
        }
      })
    },
    // 接上面打电话逻辑
    async sendCall(contact, _forceSave) {
      this.ui.refreshLoading = true
      this.ui.loadingContact = true

      this.setCurOutbounce({
        caseId: Number(this.$route.params.id),
        operationState: this.operationStates,
        contactsId: contact.id
      })

      this.setIsOutbounceLock(true)
      this.curContacts = contact
      this.isCalling = true
      this.setIsChangeCase(true)

      const checkStatusAndUserIds = await checkStatusAndUserId({
        caseId: this.caseId
      })

      if (!checkStatusAndUserIds.success) {
        this.setIsChangeCase(false)
        this.ui.loadingContact = false
        this.setIsOutbounceLock(false)
        this.ui.refreshLoading = false
        this.ui.selfUrgeRefund = false

        if (checkStatusAndUserIds.status === 400) {
          this.refundForm.contactsId = null
          this.refundForm.contactName = checkStatusAndUserIds.message
        }
        return
      }

      let checkCaseCtrls
      if (this.userExtend.ctrlSwitch) {
        checkCaseCtrls = await checkCaseCtrl({
          caseId: this.caseId,
          contactId: contact.id
        })
      }

      if (
        !this.userExtend.ctrlSwitch ||
        (this.userExtend.ctrlSwitch &&
          checkCaseCtrls.success &&
          checkCaseCtrls.data.checkResult === 0)
      ) {
        // 如果是本人 或者 产品属于信用卡产品 并且开通了属地拨打功能，选择线路
        if (
          this.userExtend.enableAutoSwitchPhone &&
          !_forceSave &&
          (contact.ownSign || this.caseInfo.productType === 1)
        ) {
          this.callPhone.contact = contact
        }

        this.updateCtiCall({ key: 'urgeRefund', data: this.caseId })

        this.refundForm.contactName = contact.name
        this.refundForm.contactsId = contact.id
        this.refundForm.relationType = contact.relationType
        this.refundForm.mobile = contact.mobile
        this.refundForm.warning = contact.warning

        if (this.userExtend.enableHiddenPhone) {
          const data = await getContactMobile(this.refundForm.contactsId)
          registerCall(data.data).then(data => {
            const mobile = data.data.substr(-4)

            DYSDK.call(
              mobile,
              res => {
                this.setIsOutbounceLock(false)
                this.curContacts = null
                this.ui.loadingContact = false
                this.ui.refreshLoading = false
                if (res.errorCode) {
                  this.setIsChangeCase(false)
                  if (res.errorCode === 'PE_1003') {
                    this.$Message.error(this.$t('l_noTerLine'))
                  } else {
                    this.$Message.error(this.$t('l_changeLineError'))
                  }
                  this.callClear()
                } else {
                  this.ui.selfUrgeRefund = true
                }
              },
              null,
              null
            )
          })
        } else {
          const data = await getContactMobile(this.refundForm.contactsId)

          DYSDK.call(
            data.data,
            res => {
              this.setIsOutbounceLock(false)
              this.curContacts = null
              this.ui.loadingContact = false
              this.ui.refreshLoading = false
              if (res.errorCode) {
                this.setIsChangeCase(false)
                if (res.errorCode === 'PE_1003') {
                  this.$Message.error(this.$t('l_noTerLine'))
                } else {
                  this.$Message.error(this.$t('l_changeLineError'))
                }
                this.callClear()
              } else {
                this.ui.selfUrgeRefund = true
              }
            },
            null,
            null
          )
        }
      } else {
        this.ui.loadingContact = false
        this.ui.refreshLoading = false
        this.setIsChangeCase(false)
        this.setIsOutbounceLock(false)
        if (this.userExtend.ctrlSwitch) {
          let content
          switch (checkCaseCtrls.data.checkResult) {
            case 1:
              content = `该用户日管控呼出限制<span style="color: #FF4F1F;">${checkCaseCtrls.data.callTimesDay}</span>次，已达到次数上限，暂无法呼出。`
              break
            case 2:
              content = `<p>该号码属于管控范围内，<span style="color: #FF4F1F;">${checkCaseCtrls.data.limitDay}天${checkCaseCtrls.data.limitDayTime}次，${checkCaseCtrls.data.limitHour}小时不超过${checkCaseCtrls.data.limitHourTime}次，</span>该时段已被限制呼出。</p>`
              break
            case 3:
              content =
                '<p>该案件属于管控案件，债务人本人号码已被限制呼出。</p>'
              break
            case 4:
              content = '<p>该案件属于管控案件，联系人号码已被限制呼出。</p>'
              break
          }
          this.$Modal.warning({
            title: '管控提示',
            content
          })
        }
      }
    },
    refresh() {
      if (!this.curContacts || this.ui.refreshLoading) return
      this.getOperation()

      if (this.isCalling) {
        this.startCall(this.curContacts)
      } else {
        this.startRefund(this.curContacts)
      }
    },
    callClear() {
      this.updateCtiCall({ key: 'urgeRefund', data: false })
      this.setOpenedRefundForm({ key: 'refundForm', data: null })

      this.ui.selfUrgeRefund = false
      this.setIsManageRefund(false)
      this.refundForm = deepClone(REFOUND_FORM)
      this.$refs.refundForm && this.$refs.refundForm.resetFields()
    },
    setInvalid(item, name, index) {
      if (this.$refs.poptip) {
        this.$refs.poptip.map(item => {
          item.tIndex = 0
        })
      }

      this.$Modal.confirm({
        title: name === 'invalid' ? '设为无效联系人' + '提示' : '恢复' + '提示',
        content:
          name === 'invalid'
            ? '是否确定将此联系人设为无效联系人？'
            : '是否确定恢复此联系人至联系人列表？',
        onOk: () => {
          changeStatus({
            id: item.id,
            status: name === 'invalid' ? 1 : 0
          }).then(res => {
            if (res) {
              if (name === 'invalid') {
                this.contacts.splice(index, 1)
                this.getContact(1)
              } else {
                this.invalidContacts.splice(index, 1)
                this.getContact(0)
              }
            }
          })
        }
      })
    },
    toggleParam(contact, key) {
      const _contact = deepClone(contact)
      _contact[key] = _contact[key] ? 0 : 1
      delete _contact.idCard
      updateContact(_contact).then(res => {
        if (res) {
          contact[key] = _contact[key]
          this.getContact()
        }
      })
    },
    editContact(item) {
      this.editingContact = item
      this.contactForm = deepClone(item)
      this.ui.showContactsModal = true

      this.$nextTick(() => {
        this.$refs.contactsModal.ui.contactModal = true
      })
    },
    getColor(value) {
      const curColor = this.caseColor.filter(item => item.value === value)
      this.curColor = curColor[0]
    },
    getTag(value) {
      this.curTag = []
      const tags = [...value]

      if (tags.length === 0) return
      tags.map(item => {
        let color = ''
        let bgColor = ''
        this.caseStandardColor.map(i => {
          if (i.value === item.color) {
            color = i.textColor
            bgColor = i.bgColor
          }
        })

        this.curTag.push({
          value: item.id,
          name: item.name,
          color: item.color,
          textColor: color,
          bgColor: bgColor
        })
      })
    },
    callConfirm(data) {
      if (data.type === 0) {
        // 电话呼入
        if (!this.ui.selfUrgeRefund) {
          this.updateCtiCall({ key: 'callStyle', data: data.type })
        }
        this.getInbounCase(data)
      } else {
        // 电话呼出
        this.updateCtiCall({ key: 'callStyle', data: data.type })
      }
    },
    getInbounCase(data) {
      getInbCase(data.phone).then(async res => {
        if (res.data.caseId) {
          if (res.data.isDel) {
            this.$Modal.error({
              title: '系统提示',
              content: '打开案件失败',
              okText: '知道了'
            })
          } else {
            if (Number(res.data.caseId) === Number(this.caseId)) {
              const sendData = {
                caseId: Number(res.data.caseId),
                contactsId: res.data.contactsId,
                callUuid: this.callUuid,
                callStyle: 0
              }

              addAutomatic(sendData)
            }

            this.missContactCall = null

            const isInCalling = {
              caseId: res.data.caseId,
              uuid: data.uuid,
              contactsId: res.data.contactsId,
              contactName: res.data.contactName,
              phone: data.phone,
              relationType: res.data.relationType
            }

            this.setIsInCalling(isInCalling)

            if (res.data.caseId === Number(this.caseId)) {
              this.ui.selfUrgeRefund = true

              await this.getContactId(this.isInCalling.phone)
              if (this.refundForm.contactsId) {
                this.ui.showChangeArrow = false
                const arr = [...this.contacts, ...this.invalidContacts]

                this.refundForm = {
                  ...deepClone(REFOUND_FORM),
                  contactsId: this.refundForm.contactsId
                }

                for (const c of arr) {
                  if (c.id === this.refundForm.contactsId) {
                    this.refundForm.contactName = c.name
                    this.refundForm.relationType = c.relationType
                    this.refundForm.warning = c.warning
                    this.refundForm.mobile = c.mobile
                    break
                  }
                }

                this.updateCtiCall({ key: 'urgeRefund', data: res.data.caseId })
              } else {
                this.$Message.error({
                  content: `该联系人号码被删除，无法填写${this.$t(
                    'reminder'
                  )}，建议重新添加此联系人或重新填写其他${this.$t(
                    'reminder'
                  )}！`,
                  duration: 4
                })
                this.missContactCall = data.phone
                this.ui.selfUrgeRefund = false
                return
              }
            } else {
              const route = {
                name: 'caseDetail',
                params: {
                  id: res.data.caseId,
                  name: res.data.name,
                  caseIds: [],
                  index: 0,
                  phone: data.phone
                }
              }
              this.setCtiCall({
                key: 'callbackData',
                data: { action: 'ended', data }
              })
              this.updateCtiCall({ key: 'urgeRefund', data: res.data.caseId })
              this.$router.push(route)
              const list = this.$store.state.app.tagNavList
              for (const key in list) {
                if (
                  list[key].params &&
                  list[key].params.id === res.data.caseId
                ) {
                  list[key].params = route.params
                }
              }
              this.setTagNavList(list)
            }
          }
        } else {
          this.$Modal.error({
            title: '系统提示',
            content: `您并非本通电话对应案件的负责人，无法填写${this.$t(
              'reminder'
            )}`,
            okText: '知道了'
          })
        }
      })
    },
    changeColor(value) {
      this.caseInfo.color = value
      this.getColor(this.caseInfo.color)
    },
    changeStatus() {
      this.ui.showApplyStatus = true

      this.$nextTick(() => {
        this.$refs.applyCaseStatus.ui.applyModal = true
      })
    },
    getItemValue(group, index, dataMap, key) {
      index = (group - 1) * this.ui.caseInfoColNum + index - 1
      const item = this[dataMap][index]
      if (item) {
        if (item.key === 'load_date' && key === 'value') {
          item[key] = item[key].split(' ')[0]
        }
        if (item.key === 'own_mobile' && key === 'value') {
          return this.hidePhone(item[key])
        } else if (item.name === '身份证号' && key === 'value') {
          return desenRulesAll('idCard', item.value)
        } else if (item.key && item.key.indexOf('|') > -1) {
          const curKey = item.key.split('|')[0]
          if (key === 'value') {
            return desenRulesAll(curKey, item.value)
          } else {
            return item[key]
          }
        } else {
          if (key === 'value') {
            return desenRulesAll(item.key, item.value)
          } else {
            return item[key]
          }
        }
      }

      return ''
    },
    getTalkContent(searchData) {
      const data = { ...searchData, ...{ limit: 20, page: 1 }}
      getCaseList(data).then(res => {
        if (res.status === 200) {
          const list = res.data.list
          if (list && list.length > 0) {
            this.talkContent = {
              createBy: list[0].createBy,
              createTime: formatDate(list[0].createTime, 'MM-dd hh:mm'),
              content: list[0].content,
              id: list[0].id,
              caseId: list[0].caseId
            }
          }
        }
      })
    },
    editTalkNote() {
      this.editTalkNotes = this.talkContent.content
      this.ui.editTalkNote = true
    },
    confirmEditTalk() {
      if (this.editTalkNotes.trim() === this.talkContent.content) {
        this.cancelEditTalk()
      } else if (this.editTalkNotes.trim() === '') {
        this.cancelEditTalk()
      } else {
        const sendData = {
          id: this.talkContent.id,
          caseId: this.talkContent.caseId,
          content: this.editTalkNotes
        }
        saveCaseNote(sendData)
          .then(res => {
            this.talkContent.content = this.editTalkNotes
          })
          .finally(() => {
            this.cancelEditTalk()
          })
      }
    },
    cancelEditTalk() {
      this.ui.editTalkNote = false
      this.editTalkNotes = ''
    },
    showTalkNotes() {
      this.ui.editTalkNote = false
      this.ui.ifShowTalknotes = true

      this.$nextTick(() => {
        this.$refs.talkNotesHistory.ui.showModal = true
      })
    },
    talkNoteSave(value) {
      this.talkContent = {
        createBy: value.row.createBy || '--',
        createTime: value.row.updateTime
          ? formatDate(value.row.updateTime)
          : '--',
        content: value.value,
        id: value.row.id,
        caseId: value.row.caseId
      }
    },
    addTalkNote() {
      this.ui.editTalkNote = false
      this.ui.addTalkNote = true
    },
    handleSubmit(name) {
      this.ui.talkNoteLoading = true

      this.$refs[name].validate(valid => {
        if (valid) {
          saveCaseNote({
            content: this.talkNoteForm.content,
            caseId: this.caseId
          })
            .then(res => {
              if (res.status === 200) {
                this.ui.addTalkNote = false
                this.$refs[name].resetFields()

                this.getTalkContent(this.talkNoteInjectSearchData)
              }
            })
            .finally(() => {
              this.ui.talkNoteLoading = false
            })
        } else {
          this.ui.talkNoteLoading = false
        }
      })
    },
    handleReset(name) {
      this.$refs[name].resetFields()
      this.ui.addTalkNote = false
    },
    tabsClick(key) {
      this.tags = 0
      this.curName = key

      if (key === '1') {
        this.handleTalkChange(0)
      }
    },
    handleDebtSearch() {
      const tag = this.tags === 0 ? 'on' : 'past'
      this.debtSearchData.tag = tag
      this.$refs.debtTable && this.$refs.debtTable.pageChange(1)
    },
    editAddressSuccess() {
      this.$refs.addressTable.handleSearch()
    },
    addressVisibleChange(bool) {
      if (!bool) {
        this.ui.ifOutvisitAddress = false
      }
    },
    applyOutVisit() {
      const selection = this.$refs.addressTable.selection
      if (selection.length === 0) {
        return this.$Message.error('请至少选择一个地址')
      }

      this.applyVisit.selection = selection

      // if ('agent') {
      //   this.ui.ifApplyVisit = true

      //   this.$nextTick(() => {
      //     this.$refs.applyVisit.ui.showModal = true
      //   })
      // } else if (['manage', 'teamLeader']) {
        this.applyVisit.selection.map(item => {
          item.addressState = item.state
          item.caseId = this.caseId
          item.addressId = item.id
          item.id = null
        })

        this.ui.showPassSchedule = true

        this.$nextTick(() => {
          this.$refs.passVisitSchedule.ui.showModal = true
        })
      // }
    },
    applyVisitChange(bool) {
      if (!bool) {
        this.ui.ifApplyVisit = false
        this.$refs.addressTable.handleSearch()
      }
    },
    addNewAddress() {
      this.outvisit.type = 'add'
      this.outvisit.caseId = this.caseId
      this.ui.ifOutvisitAddress = true

      this.$nextTick(() => {
        this.$refs.outvisit.ui.showModal = true
      })
    },
    handleVisitSearch(key) {
      this.tags = key
    },
    visitDetailChange(bool) {
      this.ui.showVisitDetail = false

      if (bool) {
        this.$refs.outvisitTable.handleSearch()
      }
    },
    applyToVisit() {
      this.ui.ifChoseAddress = true
      this.choseVisit.caseId = this.caseId
      this.$nextTick(() => {
        this.$refs.choseVisitAddress.ui.showModal = true
      })
    },
    choseVisibleChange(bool) {
      if (!bool) {
        this.ui.ifChoseAddress = false
        this.$refs.outvisitTable.handleSearch()
      }
    },
    handleRepaySearch(value) {
      if (value === 1) {
        this.billInjectSearchData.repListType = this.tags === 0 ? 2 : 3
        this.$refs.billTable.pageChange(1)
      } else {
        // console.log(this.tags)
        setTimeout(() => {
          this.$refs.urgingTable.pageChange(1)
        }, 100)
      }
    },
    openRepay() {
      this.ui.ifApplyPay = true
      this.$nextTick(() => {
        this.$refs.applyPayModal.ui.showPay = true
      })
    },
    // 反欺诈申请，发函申请
    openApply() {
      this.$refs.letterModal.ui.ifShowModal = true
    },
    async beforeUploadNormalPicRefund(file, _callback) {
      let size = 0
      for (const key in this.refundFile) {
        if (file.name === this.refundFile[key]) {
          this.$Message.error('文件名重复，请更改文件名后上传')
          return
        }
        size = size + this.currentImg[key].size
      }

      size = size + file.size

      if (size > 10 * 1024 * 1024) {
        this.$Message.error('上传文件大小总共不能超过10M')
        return
      }
      const fileType = file.type
      if (fileType.indexOf('image') !== -1) {
        const img = await getImgInfo(file)
        if (img.w > 1500) {
          var image = document.createElement('img')
          image.src = img.pic
          var canvas = document.createElement('canvas')
          var imgWidth = img.w
          var imgHeight = img.h
          var fullWidth = imgWidth
          var fullHeight = imgHeight
          if (fullWidth > 1500) {
            var rate = fullHeight / fullWidth
            var originWidth = fullWidth
            fullWidth = 1500
            fullHeight = 1500 * rate
            rate = fullWidth / originWidth
            imgWidth = imgWidth * rate
            imgHeight = imgHeight * rate
          }
          canvas.setAttribute('width', fullWidth)
          canvas.setAttribute('height', fullHeight)
          var g = canvas.getContext('2d')
          g.fillStyle = '#fff'
          g.fillRect(0, 0, canvas.width, canvas.height)
          g.translate(fullWidth / 2, fullHeight / 2)
          g.drawImage(
            image,
            -imgWidth / 2,
            -imgHeight / 2,
            imgWidth,
            imgHeight
          )
          this.currentImg.push(
            new File(
              [dataURItoBlob(canvas.toDataURL('image/jpeg'))],
              file.name,
              { type: 'image/jpeg', lastModified: Date.now() }
            )
          )
        } else {
          this.currentImg.push(file)
        }
      } else {
        this.currentImg.push(file)
      }
      this.refundFile.push(file.name)
      await Promise.reject(new Error(false))
      _callback && _callback()
    },
    rowClassName({ row }) {
      return row.applyStatus === -1 ? 'finish' : null
    },
    assistClassName(row, index) {
      return row.status === 3 ? 'finish' : null
    },
    deleteFile(index, file) {
      this.currentImg.splice(index, 1)
      this[file].splice(index, 1)
    },
    openAssist() {
      this.$refs.assistModal.ui.assistModal = true
    },
    // 提交联系人
    handleConcat() {
      this.ui.concatLoading = true
      addSyncConcat({
        caseId: this.caseId,
        syncs: this.concatData
      })
        .then(res => {
          if (res.status === 200) {
            this.ui.showConcat = false
            this.updateContacts.syncStatus = 1
            this.getContact()
          }
        })
        .finally(() => {
          this.ui.concatLoading = false
        })
    },
    // 保存通讯录
    handleSave(row, index) {
      const rows = {
        name: row.name,
        mobile: row.mobile,
        relationType: row.relationType
      }
      this.concatData[index] = rows
      row.edit = false
    },
    create(row) {
      row.edit = true
    },
    SyncConcat() {
      this.ui.showConcat = true
      this.ui.concatLoading = false
    },
    async beforeUploadNormal(file, _callback) {
      if (file.size > 10 * 1024 * 1024) {
        this.$Message.warning('上传文件大小不能超过10M')
        await Promise.reject(new Error(false))
        return
      }

      for (const key in this.uploadedFiles) {
        if (file.name === this.uploadedFiles[key].fileName) {
          this.$Message.error('文件名重复，请更改文件名后上传')
          return
        }
      }
      this.ui.uploadLoading = true
      const fileType = file.type

      if (fileType.indexOf('image') !== -1) {
        const img = await getImgInfo(file)
        if (img.w > 1500) {
          var image = document.createElement('img')
          image.src = img.pic
          var canvas = document.createElement('canvas')
          var imgWidth = img.w
          var imgHeight = img.h
          var fullWidth = imgWidth
          var fullHeight = imgHeight
          if (fullWidth > 1500) {
            var rate = fullHeight / fullWidth
            var originWidth = fullWidth
            fullWidth = 1500
            fullHeight = 1500 * rate
            rate = fullWidth / originWidth
            imgWidth = imgWidth * rate
            imgHeight = imgHeight * rate
          }
          canvas.setAttribute('width', fullWidth)
          canvas.setAttribute('height', fullHeight)
          var g = canvas.getContext('2d')
          g.fillStyle = '#fff'
          g.fillRect(0, 0, canvas.width, canvas.height)
          g.translate(fullWidth / 2, fullHeight / 2)
          g.drawImage(
            image,
            -imgWidth / 2,
            -imgHeight / 2,
            imgWidth,
            imgHeight
          )
          this.file = new File(
            [dataURItoBlob(canvas.toDataURL('image/jpeg'))],
            file.name,
            { type: 'image/jpeg', lastModified: Date.now() }
          )
        } else {
          this.file = file
        }
      } else {
        this.file = file
      }

      var formData = new FormData()
      formData.append('multipartFile', this.file)
      formData.append('caseId', this.caseId)
      caseUploadFile(formData).then(res => {
        if (res.status === 200 && res.success) {
          this.$Message.success('文件上传成功！')
          this.$refs.fileTable && this.$refs.fileTable.handleSearch()
          this.$refs.fileTables && this.$refs.fileTables.handleSearch()
        } else {
          this.$Message.error(`文件上传失败`)
        }
      })
      this.ui.uploadLoading = false
      await Promise.reject(new Error(false))
      _callback && _callback()
    },
    startRefund(contact, _forceSave) {
      if (contact.id === this.refundForm.contactsId && this.ui.selfUrgeRefund) {
        this.ui.ifHideDrag = true
        return
      }
      if (
        (this.ui.loadingContact || this.ui.selfUrgeRefund) &&
        !this.curContacts
      ) { return }
      if (this.isInCall) {
        return this.$Message.error(this.$t('l_requirePhone'))
      }

      if (this.urgeRefund) {
        if (this.refundForm.contactsId === contact.id) {
          this.ui.ifHideDrag = true
        } else {
          this.$Message.error(this.$t('l_requireRefund'))
        }
        return
      }

      this.ui.refreshLoading = true
      this.ui.selfUrgeRefund = true
      this.ui.loadingContact = true

      this.curContacts = contact
      this.isCalling = false
      this.setIsChangeCase(true)

      checkStatusAndUserId({ caseId: this.caseId })
        .then(res => {
          if (res) {
            if (res.status === 200) {
              this.updateCtiCall({ key: 'urgeRefund', data: this.caseId })
              this.updateCtiCall({ key: 'uuid', data: '' })
              let list = {}
              // 如果是民生银行案件详情
              if (this.caseInfo.cmbcCaseDetailInfo) {
                list = {
                  cmbcConName: contact.name,
                  contactsId: contact.id,
                  cmbcRelation: contact.relationType,
                  cmbcConMobile: contact.mobile,
                  cmbcField: contact.field,
                  warning: contact.warning,
                  submitType: 4
                }
              } else {
                list = {
                  contactName: contact.name,
                  contactsId: contact.id,
                  relationType: contact.relationType,
                  mobile: contact.mobile,
                  warning: contact.warning
                }
              }
              this.refundForm = { ...this.refundForm, ...list }
              this.updateCtiCall({ key: 'refundForm', data: this.refundForm })

              this.curContacts = null
            } else if (res.status === 400) {
              this.refundForm.contactsId = null
              this.refundForm.contactName = res.message
            }
          }
        })
        .finally(() => {
          this.ui.loadingContact = false
          this.ui.refreshLoading = false
        })
    },
    hideDrags() {
      this.ui.ifHideDrag = false
    },
    showDrags() {
      this.ui.ifHideDrag = true
    },
    changeCallType(value) {
      let item
      let isLast = !this.refundForm.desc
      for (const c of this.refundCallResult) {
        if (c.value === value) {
          item = c
        }
        if (c.name === this.refundForm.desc) {
          isLast = true
        }
      }

      if (isLast) {
        this.refundForm.desc = item.name
      }
    },
    setNextTime(day) {
      if (!this.ui.selfUrgeRefund) {
        return
      }

      const date = new Date()
      switch (day) {
        case 1:
          this.refundForm.nextTime = date
          break
        case 2:
          date.setTime(date.getTime() + 3600 * 1000 * 24)
          this.refundForm.nextTime = date
          break
        case 3:
          date.setTime(date.getTime() + 3600 * 1000 * 24 * 2)
          this.refundForm.nextTime = date
          break
      }
    },
    // 区分填催记和不填，不填不触发必填校验
    _submit(type) {
      this.ui.submitingRefund = true
      const sendData = deepClone(this.refundForm)
      sendData.nextTime =
        type === 'submit'
          ? new Date(sendData.nextTime).getTime()
            ? new Date(sendData.nextTime).getTime()
            : null
          : null

      sendData.caseId = this.caseId

      sendData.operationState =
        type === 'submit'
          ? this.operationStates
          : this.caseInfo.operationState

      if (sendData.ptpTime) {
        sendData.ptpTime =
          type === 'submit' ? new Date(sendData.ptpTime).getTime() : null
      } else {
        delete sendData.ptpTime
      }
      if (sendData.ptpAmount) {
        sendData.ptpAmount = type === 'submit' ? sendData.ptpAmount : 0
      }
      if (sendData.reduceAmount) {
        sendData.reduceAmount =
          type === 'submit' ? sendData.reduceAmount : 0
      }

      if (this.userExtend.operationHiddenSwitch) {
        if (type === 'unSubmit') {
          sendData.isHidden = 0
        }
      } else {
        delete sendData.isHidden
      }

      if (!this.refundedCase[this.callUuid]) {
        sendData.callUuid = this.callUuid
      }

      sendData.callStyle = this.callStyle
      if (sendData.submitType === 4) {
        delete sendData.contactsId
      }
      if (type === 'unSubmit') {
        sendData.actionType = -3
        sendData.callType = -3
        sendData.desc = sendData.indesc || ''
        delete sendData.indesc
      }

      // if (this.missContactCall) {
      //   const arr = [...this.contacts, ...this.invalidContacts]
      //   if (
      //     arr.filter(c => {
      //       return c.ownMobile === this.missContactCall
      //     }).length === 0
      //   ) {
      //     this.$Message.error({
      //       content: `该联系人号码被删除，无法填写${this.$t(
      //         'reminder'
      //       )}，建议重新添加此联系人或重新填写其他${this.$t('reminder')}！`,
      //       duration: 4
      //     })
      //     this.ui.submitingRefund = false
      //     this.ui.selfUrgeRefund = false
      //     this.setIsManageRefund(false)
      //     return
      //   }
      //   sendData.contactsId = arr.filter(c => {
      //     return c.ownMobile === this.missContactCall
      //   })[0].id
      //   sendData.contactName = arr.filter(c => {
      //     return c.ownMobile === this.missContactCall
      //   })[0].name
      //   sendData.relationType = arr.filter(c => {
      //     return c.ownMobile === this.missContactCall
      //   })[0].relationType
      // }

      const formData = new FormData()
      for (const key in sendData) {
        if (sendData[key] || sendData[key] === 0) {
          formData.append(key, sendData[key])
        }
      }

      if (this.refundForm.actionType === 5 && type === 'submit') {
        for (const key in this.currentImg) {
          formData.append('files', this.currentImg[key])
        }
      }

      if (
        type === 'unSubmit' &&
        (!this.userExtend.enableConnectedDuyan ||
          !this.$store.state.user.dyAgent)
      ) {
        this.updateCtiCall({ key: 'urgeRefund', data: false })
        this.ui.selfUrgeRefund = false
        this.setIsManageRefund(false)
        this.ui.submitingRefund = false
        return
      }

      if (type === 'unSubmit') {
        formData.set('syncTypes', '')
      }

      addRefund(formData)
        .then(res => {
          if (res.success) {
            if (this.$refs.ctrlDetail) {
              this.$refs.ctrlDetail.getCaseCtrl()
            }
            if (sendData.callUuid) {
              this.setRefundedCase({
                uuid: sendData.callUuid,
                value: sendData.caseId
              })
            }
            this.operationStates = Number(formData.get('operationState'))
            this.caseInfo.operationState = this.operationStates
            this.oriOperationState = this.operationStates
            this.callClear()
            this.currentImg = []
            this.refundFile = []

            if (sendData.reduceAmount && this.$refs.reduceTable) {
              this.$refs.reduceTable.handleSearch()
            }
            if (this.$refs.recordTable) {
              this.$refs.recordTable.handleSearch()
            }
            if (this.$refs.talkCaseTable) {
              this.$refs.talkCaseTable.handleSearch()
            }

            this.updateCtiCall({ key: 'uuid', data: '' })
            this.ui.submitingRefund = false
            this.missContactCall = null

            const list = this.$store.state.app.tagNavList
            for (const key in list) {
              if (list[key].params && list[key].params.id === this.caseId) {
                delete list[key].params.phone
                delete list[key].params.allotAgent
                delete list[key].params.isFifo
                delete list[key].params.uuid
              }
            }

            this.setTagNavList(list)
          } else if (res.status === 9001) {
            this.$Modal.confirm({
              title: '提示',
              content: res.message,
              onOk: () => {
                this.ui.selfUrgeRefund = false
                this.setIsManageRefund(false)
                this.ui.submitingRefund = false
                this.caseInfo.operationState = this.operationStates
                this.operationStates = Number(
                  formData.get('operationState')
                )
                this.oriOperationState = this.operationStates
                this.callClear()
                this.currentImg = []
                this.refundFile = []
                this.missContactCall = null

                for (const key in list) {
                  if (
                    list[key].params &&
                    list[key].params.id === this.caseId
                  ) {
                    delete list[key].params.phone
                    delete list[key].params.allotAgent
                    delete list[key].params.isFifo
                    delete list[key].params.uuid
                  }
                }

                this.setTagNavList(list)
              },
              onCancel: () => {
                this.ui.selfUrgeRefund = false
                this.setIsManageRefund(false)
                this.ui.submitingRefund = false
                this.caseInfo.operationState = this.operationStates
                this.operationStates = Number(
                  formData.get('operationState')
                )
                this.oriOperationState = this.operationStates
                this.callClear()
                this.currentImg = []
                this.refundFile = []
                this.missContactCall = null

                for (const key in list) {
                  if (
                    list[key].params &&
                    list[key].params.id === this.caseId
                  ) {
                    delete list[key].params.phone
                    delete list[key].params.allotAgent
                    delete list[key].params.isFifo
                    delete list[key].params.uuid
                  }
                }

                this.setTagNavList(list)
              }
            })
          }
        })
        .finally(() => {
          this.ui.submitingRefund = false
          this.setIsInCalling({})
        })
    },
    refundSubmit(type) {
      // 管理员不填写催记不生成催记 或者没有callUUid且提交类型为民生惠捷发起的案件不生成催记
      if (type === 'unSubmit' && !this.callUuid) {
        return this.callClear()
      }
      // if (
      //   type === 'unSubmit'
      // ) {
      //   this.ui.selfUrgeRefund = false
      //   this.setIsManageRefund(false)
      //   return
      // }
      // _callBack is auto save
      if (type === 'unSubmit') {
        this._submit(type)
      } else {
        this.$refs.refundForm.validate(valid => {
          if (valid) {
            this._submit(type)
          }
        })
      }
    },
    turnToDebt() {
      this.curName = '2'
      this.$nextTick(() => {
        const details = this.$el.querySelector('#detail')
        details.scrollTop = details.scrollHeight
      })
    },
    scheduleChange(bool) {
      if (!bool) {
        this.ui.showPassSchedule = false
        this.$refs.addressTable && this.$refs.addressTable.handleSearch()
        this.$refs.outvisitTable && this.$refs.outvisitTable.handleSearch()
      }
    },
    closeModals(modal) {
      this.ui[modal] = false
    },
    editCase() {
      this.ui.ifEditCaseInfo = true

      this.$nextTick(() => {
        this.$refs.editCaseModal.ui.showEditCase = true
      })
    },
    editCaseSuccess() {
      this.ui.ifEditCaseInfo = false
      this.initPage()
    },
    // 反欺诈，信函添加成功
    applyModalSuccess() {
      this.$refs.urgingTable.handleSearch()
    },
    assistApplySuccess() {
      this.$refs.assistTable.handleSearch()
    },
    applyPaySuccess() {
      this.ui.ifApplyPay = false
      this.$refs.billTable && this.$refs.billTable.handleSearch()
    },
    applyCooperation() {
      this.ui.showCooperation = true

      this.$nextTick(() => {
        this.$refs.applyCooperation.ui.shows = true
      })
    },
    endCooperation(type) {
      this.finishCooper.isResponse =
        this.caseInfo.userId === this.userId || this.isTeamUrgeCase
      this.ui.showEndCooper = true
    },
    cancelEndCooper() {
      this.ui.showEndCooper = false
      this.finishCooper.reason = null
    },
    endCooperations() {
      let sendData = {}
      sendData = {
        caseId: this.caseId,
        reason: this.finishCooper.reason
      }
      this.ui.cooperLoading = true

      finishCooperation(sendData)
        .then(res => {
          if (res) {
            this.caseInfo.cooperatorState = 1

            if (this.caseInfo.userId !== this.userId && !this.isTeamUrgeCase) {
              this.initPage()
            }
          }
        })
        .finally(() => {
          this.ui.cooperLoading = false
          this.ui.showEndCooper = false

          this.finishCooper = {
            id: null,
            reason: null,
            isResponse: true
          }
        })
    },
    addComment() {
      this.ui.addCommentModal = true
    },
    cancelComment() {
      this.ui.addCommentModal = false
      this.$refs.commentForm.resetFields()
    },
    submitCommit() {
      this.$refs.commentForm.validate(valid => {
        if (valid) {
          const sendData = {
            caseId: this.caseId,
            content: this.commentForm.content
          }

          this.ui.addCommentLoading = true
          commentSave(sendData)
            .then(res => {
              if (res) {
                this.ui.addCommentModal = false
                this.$refs.commentForm.resetFields()

                this.getLatestComment()
              }
            })
            .finally(() => {
              this.ui.addCommentLoading = false
            })
        }
      })
    },
    getLatestComment() {
      const sendData = {
        caseId: this.caseId,
        limit: 20,
        page: 1
      }
      commentList(sendData).then(res => {
        if (res) {
          if (res.data.list.length > 0) {
            const data = res.data.list[0]
            this.showComment = {
              content: data.content,
              createBy: data.createBy,
              createTime: formatDate(data.updateTime)
            }
          } else {
            this.showComment = {}
          }
        }
      })
    },
    checkAllComments() {
      this.ui.showHistoryComments = true

      this.$nextTick(() => {
        this.$refs.commentsHistory.ui.showModal = true
      })
    },
    commentsSave(value) {
      this.showComment = {
        content: value.row.content,
        createBy: value.row.createBy,
        createTime: formatDate(value.row.createTime)
      }
    },
    getOperation() {
      if (!this.caseInfo.orgDeltId) return
      if (this.ui.loadingOperation) return

      this.setIsChangeCase(true)
      this.ui.loadingOperation = true
      const sendData = {
        isAll: 0,
        orgDeltId: this.caseInfo.orgDeltId
      }

      this.operationStateList = []
      this.refundResultsList = []
      this.callResultList = []
      this.allCallResults = []
      this.allRefundResults = []

      const func = [
        getDeltOperation({ ...sendData, type: 1 }),
        getDeltOperation({ ...sendData, type: 2 }),
        getDeltOperation({ ...sendData, type: 3 }),
        getLabelList()
      ]
      Promise.all(func)
        .then(res => {
          if (res) {
            // 催收进程
            if (res[0].success) {
              const operationStateList = res[0].data
              const ids = []
              for (const item of operationStateList) {
                item.value = item.code
                this.operationStateList.push(item)
                ids.push(item.id)
              }

              const operationState = this.operationStateList.length
                ? this.operationStateList[0].value
                : null

              this.operationStates = this.checkInUsingStates(
                this.caseInfo.operationState
              )
                ? this.caseInfo.operationState
                : operationState

              if (this.userExtend.operationConfigLinkSwitch) {
                getOperationLink(ids).then(res => {
                  if (res.success) {
                    this.operationConfig = deepClone(res.data)
                    this.changeProcessing(this.operationStates)
                  }
                })
              }
            }
            // 电话结果
            if (res[1].success) {
              const callResultList = res[1].data
              for (const item of callResultList) {
                item.value = item.code
                this.allCallResults.push(item)
              }
            }

            // 催收结果
            if (res[2].success) {
              const refundResultsList = res[2].data
              for (const item of refundResultsList) {
                item.value = item.code
                this.allRefundResults.push(item)
              }
            }
            // 客户标签
            if (res[3].success) {
              const customLabels = []
              const customLabel = res[3].data
              for (const value of customLabel) {
                if ((!value.orgDeltIds || value.orgDeltIds.includes(this.caseInfo.orgDeltId)) && value.enable === 1) {
                  customLabels.push(value)
                }
              }
              this.customLabels = customLabels
            }

            if (!this.userExtend.operationConfigLinkSwitch) {
              this.callResultList = deepClone(this.allCallResults)
              this.refundResultsList = deepClone(this.allRefundResults)
            }
          }
        })
        .finally(() => {
          this.ui.loadingOperation = false
          // 1s是为了等待setCtiCall那边因为切换案件导致自动提交催记
          setTimeout(() => {
            this.setIsChangeCase(false)
          }, 1000)
        })
    },
    popperShow() {
      const idInfo = idCard.info(this.caseInfo.idCard)
      if (idInfo.valid) {
        this.idCardInfo = {
          area: idInfo.address,
          birth: `${String(idInfo.birthday).slice(0, 4)}.${String(
            idInfo.birthday
          ).slice(4, 6)}.${String(idInfo.birthday).slice(6)}`,
          sex: `${idInfo.gender === 'M' ? '男' : '女'}`,
          age: `${idInfo.age}岁`
        }
      } else {
        this.idCardInfo = {
          area: '--',
          birth: '--',
          sex: '--',
          age: '--'
        }
      }
    },
    // 获取民生相关信息
    popperShowMS(type, n, index) {
      const bizType = type === 'custName' ? '00' : (type === 'cardNo' ? '01' : '02')
      const sendData = { ...this.caseInfo.cmbcCaseDetailInfo.caseAmount, ...{ caseId: this.caseInfo.outSerialNo, callId: this.callUuid, bizType }}
      this.partMSInfo = {}
      getRealName(sendData).then(res => {
        if (type === 'cardNo') {
          const cardValue = this.getItemValue(n, index, 'accountInfo', 'value').split(',')
          for (const key in cardValue) {
            cardValue[key] = cardValue[key].slice(0, 4) + res.data.partCardNoList[key] + cardValue[key].slice(-4)
          }
          this.partMSInfo = {
            partCardNoList: cardValue
          }
        } else if (type === 'custName') {
          const custName = this.getItemValue(n, index, 'caseBaseInfo', 'value')
          this.partMSInfo = {
            partCustName: custName.slice(0, 1) + res.data.partCustName
          }
        } else {
          const partCstCrdtNo = this.getItemValue(n, index, 'caseBaseInfo', 'value')
          this.partMSInfo = {
            partCstCrdtNo: partCstCrdtNo.slice(0, 1) + res.data.partCstCrdtNo + partCstCrdtNo.slice(-2)
          }
        }
      })
    },
    resizeWin() {
      if (this.timer) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        if (this.$refs.caseDetail) {
          const height =
            document.documentElement.clientHeight || document.body.clientHeight
          this.ui.caseInfoHeight = height - 120
        }
      }, 300)
    },
    signColor() {
      this.ui.showSignColor = true

      this.$nextTick(() => {
        this.$refs.signColor.ui.ifCaseColor = true
      })
    },
    handleTalkChange(val) {
      this.ui.isAudio = []

      switch (val) {
        case 0:
          delete this.recordInjectSearchData.dataType
          break
        case 1:
          delete this.talkCaseSearchData.dataType
          break
        case 2:
          this.recordInjectSearchData.dataType = 0
          break
        case 3:
          this.talkCaseSearchData.dataType = 0
          break
      }
      setTimeout(() => {
        if ([0, 2].includes(val)) {
          this.$refs.recordTable.pageChange(1)
        } else {
          this.$refs.talkCaseTable.pageChange(1)
        }
      }, 10)
    },
    applyMediate() {
      this.ui.showApplyMediation = true

      this.$nextTick(() => {
        this.$refs.applyMediation.ui.showDrawer = true
      })
    },
    // 视频调解
    showMediation() {
      this.ui.showVideoMediation = true
      this.$nextTick(() => {
        this.$refs.videoMediation.ui.showModal = true
      })
    },
    closeMediation(bool) {
      if (bool) {
        this.$refs.mediation.handleSearch()
      }
    },
    closeAudit() {
      this.ui.showAuditing = false
    },
    caseStartRefund(contact) {
      this.refundForm = deepClone(REFOUND_FORM)

      const contacts = {
        contactName: contact.name,
        contactsId: contact.id,
        relationType: contact.relationType,
        mobile: contact.mobile,
        warning: contact.warning
      }

      this.refundForm = { ...this.refundForm, ...contacts }

      this.ui.selfUrgeRefund = true
      this.setIsManageRefund(true)
    },
    startMessage(contact) {
      const sendData = {
        caseId: this.caseId,
        contactId: contact.id
      }
      getMessageReturn(sendData).then(res => {
        if (res) {
          if (res.data.checkResult === 0) {
            this.msgContact = contact
            if (res.data.smsTemplateId !== null) {
              this.msgSendTemples = res.data.smsTemplateId
            }

            this.ui.showSendMsg = true

            this.$nextTick(() => {
              this.$refs.sendMsg.ui.showModal = true
            })
          }
          if (res.data.checkResult === 1) {
            this.$Modal.info({
              title: '全局管控提示',
              content: `该案件管控短信发送当日上限 ${res.data.caseTimesLimit} 条，已达到短信条数上限，当日无法发送`,
              loading: true,
              onOk: () => {
                this.$Modal.remove()
              }
            })
          }
          if (res.data.checkResult === 2) {
            this.$Modal.info({
              title: '全局管控提示',
              content: `该案件单个号码短信发送上限 ${res.data.mobileTimesLimit} 条/天，当日无法发送`,
              loading: true,
              onOk: () => {
                this.$Modal.remove()
              }
            })
          }
        }
      })
    },
    // 工作手机
    // 工作手机短信发送
    sendWorkSms(contact, key) {
      this.workSmsContact = { ...contact, solt: key }
      this.workSmsContact.solt = key
      this.ui.showWorkSms = true
      this.$nextTick(() => {
        this.$refs.workSms.ui.showModal = true
      })
    },
    handleClickCase() {
      this.$refs.ctrlDetail.ui.showDrawer = false
    },
    signWarning(bool) {
      caseWarning({
        caseId: this.caseId,
        warning: bool
      }).then(res => {
        if (res.success) {
          this.caseInfo.warning = bool
        }
      })
    },
    handleApplyLawsuit() {
      this.lawsuitApplyInfo = {
        name: this.caseInfo.name,
        caseId: this.caseId,
        amount: this.caseInfo.amount
      }

      this.$nextTick(() => {
        this.$refs.applyLawsuit.ui.showModal = true
      })
    },
    closeLawsuitApply(bool) {
      this.lawsuitApplyInfo = null
      if (bool) {
        this.$refs.lawsuitTable.handleSearch()
      }
    },
    getAccountInfo() {
      // this.allAccount = [
      //   [
      //     {
      //       key,
      //       title,
      //       value
      //     }
      //   ],
      //   []
      // ]

      // 账户信息
      const infoMap = {
        accTpVal: '账户类型',
        bal: '账户余额',
        odue2: '最新逾期期数',
        wwWtsdStatusVal: '委外账户状态',
        changingHandsVal: '手别',
        yrFee: '年费',
        down0Amt: '降至0期金额本币',
        down1Amt: '降至1期金额本币',
        down2Amt: '降至2期金额本币',
        down3Amt: '降至3期金额本币',
        cycleDay: '账单日',
        odue1: '发卡逾期期数',
        curDelayTimes: '委托时点逾期期数',
        mpRemainAmt: '普通分期未摊金额',
        mpRemPpl: '大额分期未摊金额',
        accountNo: '账号',
        baseSt: '本金',
        intSt: '利息',
        feeSt: '费用',
        baseintfee: '本息费合计',
        baseintfeeshadow: '本息费合计加停计息费',
        wtsdBase: '委托时点本金',
        wtsdInt: '委托时点利息',
        wtsdFee: '委托时点费用',
        wtsdBwbBal: '委托时点本息费合计',
        wtsdHtjxfBal: '委托时点本息费合计加停计息费',
        fjPayNet: '委托期净回款',
        byPayNet: '本月净回款',
        cardNo: '卡号',
        creditlimit: '账户信用额度',
        fcOdFlag: '是否有外币欠款',
        odueLme: '期初逾期期数',
        baseLme: '期初本金',
        baseintfeeLme: '期初本息费',
        principalBaseintfeeshadow: '期初本息费加停计息费',
        Down2Base: '本月本金压降额_降至2',
        Down0Base: '本月本金压降额_降至0',
        prCode: 'PR值',
        openDt: '开户日期',
        wtsdStatus: '账户状态代码',
        acctProduct: '账户类型描述',
        minRpyAmt: '最低还款额剩余未还金额',
        // mpRemainAmt: '普通分期未分摊本币',
        // mpRemPpl: '大额分期未分摊本币',
        accGrd: '账户分包',
        outDebt: '表外欠款',
        wtsdStatusVal: '委托时点账户状态',
        accCcycd: '币种',
        billMoney: '账单金额',
        penchgAccAmt: '应收滞纳金',
        wroffDt: '封包日期',
        intChgdAmt: '应收透支利息',
        totalAmountOwed: '总欠款金额',
        prddayMoney: '升期日金额'
      }

      if (this.caseInfo.cmbcCaseDetailInfo && this.caseInfo.cmbcCaseDetailInfo.accountInfoList && this.caseInfo.cmbcCaseDetailInfo.accountInfoList.length) {
        for (const item of this.caseInfo.cmbcCaseDetailInfo.accountInfoList) {
          const keysArr = Object.keys(item)
          const keysMap = []
          for (const i of keysArr) {
            keysMap.push({
              key: i,
              name: infoMap[i],
              value: item[i]
            })
          }

          this.caseAccountRows.push(
            Math.ceil(keysMap.length / this.ui.caseInfoColNum)
          )
          this.allAccount.push(keysMap)
        }

        this.changeAccount('0')
      }
      // 案人信息
      const caseCust = {
        gndVal: '性别',
        coNm: '单位名称',
        coTelno: '单位电话号码',
        unitTelExn: '单位电话分机',
        famTelDstcno: '住宅电话区号',
        rsdncTel: '住宅电话',
        mblphNo: '手机号码',
        copAdr: '单位地址',
        homeAdr: '家庭地址',
        brAdr: '户籍地址',
        theAgeRange: '年龄区间',
        emailAdr: '电邮地址',
        ctcInf: '本人修复手机号'
      }

      const caseAmount = {
        baseSt: '本金',
        intSt: '利息',
        feeSt: '费用',
        baseintfee: '本息费合计',
        baseintfeeshadow: '本息费合计加停计',
        wtsdBase: '委托时点本金',
        wtsdInt: '委托时点利息',
        wtsdFee: '委托时点费用',
        wtsdBwbBal: '委托时点本息费合计',
        wtsdHtjxfBal: '委托时点本息费合计加停计息费',
        fjPayNet: '委托期净回款',
        byPayNet: '本月净回款',
        wroffDt: '封包日期',
        allDebt: '总欠款金额'
      }

      const userMap = { ...caseCust, ...caseAmount }

      const caseUser = { ...(this.caseInfo.cmbcCaseDetailInfo.caseCust || {}), ...(this.caseInfo.cmbcCaseDetailInfo.caseAmount || {}) }

      if (caseUser) {
        for (const key in caseUser) {
          this.caseUserInfo.push({
            value: caseUser[key],
            key,
            name: userMap[key]
          })
        }
      }
      // 案件信息
      const caseBaseMap = {
        releaseStartDate: '跑批开始日期',
        releaseEndDate: '跑批停止日期',
        cstCrdtNo: '证件号',
        custName: '客户姓名',
        curEntrustBatch: '委托批次',
        bankAddressVal: '城市',
        curEntrustAgencyVal: '委外机构',
        thmonTskTp: '本月任务类型',
        txnDt: '末次回款日期',
        txnamt: '末次回款金额',
        txnamtNow: '当日本外币回款额',
        custInd: '客户标识',
        accGrd: '账户分包',
        odue1: '委托时点逾期期数'
      }

      const caseBase = { ...(this.caseInfo.cmbcCaseDetailInfo.caseBase || {}) }
      if (caseBase) {
        for (const key in caseBase) {
          this.caseBaseInfo.push({
            value: caseBase[key],
            key,
            name: caseBaseMap[key]
          })
        }
      }
    },
    changeAccount(tab) {
      this.accountInfo = deepClone(this.allAccount[tab])
    },
    getInitRefund() {
      const sendData = {
        caseId: this.caseId,
        promissoryNoteId: 0,
        limit: 20,
        page: 1
      }

      this.refundColumns = []
      this.refundApifun = null
      this.refundInjectSearchData = {
        caseId: this.caseId,
        promissoryNoteId: 0
      }
      const columns = []
      getCaseRepayPlan(sendData).then(res => {
        if (res.success) {
          const data = res.data
          if (data.list.length) {
            this.refundApifun = {
              get: getCaseRepayPlan
            }
            getRepayPlanFields().then(response => {
              if (response.success) {
                const list = response.data
                for (const item of list) {
                  columns.push({
                    title: item.name,
                    key: item.camelField,
                    defaultShow: '--',
                    width: ['repayTime', 'closeTime', 'ext7', 'ext8'].includes(item.camelField) ? 180 : null,
                    align: !['period', 'repayTime', 'closeTime', 'ext7', 'ext8', 'repayStatus', 'ext1', 'ext4', 'ext5', 'ext6'].includes(item.camelField) ? 'right' : null,
                    bindmap: ['closeTime', 'repayTime'].includes(item.camelField) ? 'formatDate' : (
                      item.type === '金额-小数' ? 'formatMoney' : null
                    ),
                    fmt: ['closeTime', 'repayTime'].includes(item.camelField) ? 'yyyy-MM-dd' : null
                  })
                }

                this.refundColumns = [...columns]
              }
            })
          }
        }
      })
    },
    reliefModal(type) {
      // type=1 减免管理，type=2 减免分期
      this.deductionInfo = {
        caseId: this.caseId,
        type
      }
      this.ui.showDeduction = true

      this.$nextTick(() => {
        this.$refs.deduction.ui.showModal = true
      })
    },
    closeShowMediation(bool) {
      this.ui.showDeduction = false

      if (bool) {
        this.$refs.reduceTable.handleSearch()
      }
    },
    changeProcessing(value) {
      if (this.userExtend.operationConfigLinkSwitch) {
        let selectId
        this.curOperationState = null
        for (const item of this.operationStateList) {
          if (item.code === value) {
            selectId = item.id
          }
        }

        this.callResultList = []
        const resultItem = []
        for (const item of this.operationConfig) {
          if (item.processId === selectId) {
            this.curOperationState = deepClone(item.subList)
            for (const i of item.subList) {
              resultItem.push(i.resultId)
            }
          }
        }

        this.refundResultsList = this.allRefundResults.filter(_c => resultItem.includes(_c.id))
      }
      this.refundForm.actionType = null
      this.refundForm.callType = null
    },
    changeActionType(value) {
      if (this.userExtend.operationConfigLinkSwitch) {
        let selectId
        for (const item of this.allRefundResults) {
          if (item.value === value) {
            selectId = item.id
          }
        }

        const callResultItems = []
        for (const item of this.curOperationState) {
          if (item.resultId === selectId) {
            for (const i of item.subList) {
              callResultItems.push(i.callResultId)
            }
          }
        }

        this.callResultList = this.allCallResults.filter(_c => callResultItems.includes(_c.id))
      }
      this.refundForm.callType = null
    },
    modalCancel() {
      this.List = []
      this.palyVadioApi = null
    },
    async getContactId(mobile, caseId = this.caseId) {
      this.refundForm.contactsId = null

      await getContactId({
        caseId,
        mobile
      }).then(res => {
        if (res.success) {
          this.refundForm.contactsId = res.data
        }
      })
    },
    // 借据
    tabsChange(key) {
      this.tabName = key
    },
    getLoanList() {
      getPromissoryNote({ caseId: this.caseId }).then((res) => {
        if (res.success) {
          this.loanList = res.data.reverse()
          this.loanList.map((item, i) => {
            item.label = String(i + 1)
          })
        }
      })
    },
    handleClickAI() {
      this.$refs.duyanAIRef.ui.showModal = true
      this.ui.showDuyanAIBtn = false
    },
    closeDuyanAI() {
      this.ui.showDuyanAIBtn = true
    },
    // 工作手机
    async wxAdd(item) {
      const role = getUserInLocalstorage()
      const res = await getInfo(role)
      this.customType = res.data.user.wpCustomWechatApply
      this.$refs.wxAddModal.ui.showWxAdd = true 
      // this.customType = 1
      const hidePhoneNumber = this.hidePhone(item.mobile)
      Object.assign(this.$refs.wxAddModal.userInfo, { ...item, caseId: this.caseId, hideNumber: hidePhoneNumber })
    },
  },
  watch: {
    $route(newRoute, oldRoute) {
      if (
        newRoute.params &&
        oldRoute.params &&
        newRoute.params.id === oldRoute.params.id
      ) {
        return
      }

      if (this.ui.selfUrgeRefund) {
        const refunds = {
          ...this.refundForm,
          operationState: this.caseInfo.operationState,
          caseId: Number(this.caseId)
        }

        this.setOpenedRefundForm({ key: 'refundForm', data: refunds })
        this.ui.selfUrgeRefund = false
      }

      this.caseIds = typeof newRoute.params.caseIds === 'string' ? newRoute.params.caseIds : JSON.stringify(newRoute.params.caseIds)
      this.caseIndex = parseInt(newRoute.params.index)
      this.caseId = newRoute.params.id
      this.sortBy = newRoute.params.sortBy

      this.setIsChangeCase(true)
      this.isContact = false
      this.isInvalidContact = false
      this.ui.loadingContact = true

      if (newRoute.params.phone) {
        this.refundForm.mobile = newRoute.params.phone
      }

      this.initPage()
    },
    originCtiCall(data) {
      if (this.ui.selfUrgeRefund) {
        const refunds = {
          ...this.refundForm,
          operationState: this.caseInfo.operationState,
          caseId: Number(this.caseId)
        }
        this.setOpenedRefundForm({ key: 'refundForm', data: refunds })
        this.ui.selfUrgeRefund = false
      } else {
        this.updateCtiCall({ key: data.key, data: data.data })
        this.setIsOutbounceLock(false)
      }
    },
    async ctiCallbackData(data) {
      if (!data) {
        return
      }
      if (data.data && data.data.phone) {
        data.data.phone = data.data.phone.replace(/-/g, '')
      }
      // 0呼入1呼出
      // ob 在电话connecting的时候创建call_uuid 和 caseId的关联关系；  call_uuid 为null 不走 addAutomatic
      // if (
      //   data.action === "callConnecting" &&
      //   data.data &&
      //   data.data.type === 0
      // ) {
      //   if (this.ui.selfUrgeRefund) {
      //     this.setIsRefunding(true);
      //   }
      // }

      if (data.action === 'ended') {
        this.ui.selfUrgeRefund = true

        // await this.getContactId(this.isInCalling.phone)
        await this.getContactId(this.isInCalling.phone, this.isInCalling.caseId)

        if (this.refundForm.contactsId) {
          const arr = [...this.contacts, ...this.invalidContacts]

          for (const c of arr) {
            if (c.id === this.refundForm.contactsId) {
              this.refundForm.contactName = c.name
              this.refundForm.relationType = c.relationType
              this.refundForm.warning = c.warning
              this.refundForm.mobile = c.mobile
              break
            }
          }

          this.updateCtiCall({ key: 'urgeRefund', data: this.caseId })
        }
      } else if (data.action === 'callConfirm') {
        // if (this.ui.selfUrgeRefund) {
        //   const refunds = {
        //     ...this.refundForm,
        //     operationState: this.caseInfo.operationState,
        //     caseId: Number(this.caseId)
        //   };
        //   this.setOpenedRefundForm({ key: "refundForm", data: refunds });
        // }
        this.callConfirm(data.data)
      }

      if (['callBridge_start', 'getRobotInfo'].includes(data.action)) {
        // if (this.ui.selfUrgeRefund) {
        //   const refunds = {
        //     ...this.refundForm,
        //     operationState: this.caseInfo.operationState,
        //     caseId: Number(this.caseId)
        //   };

        //   this.setOpenedRefundForm({ key: "refundForm", data: refunds });

        // }
        // this.isInCall = true
        this.setIsInCall(true)
        let caseId = ''
        if (data.action === 'callBridge_start') {
          caseId = Number(data.data.userTag.split('，')[0])
        } else {
          caseId = Number(data.data.U_caseId)
        }
        const allotAgent =
          data.data.userTag?.split('，')[1] == '1'
            ? true
            : data.data.userTag?.split('，')[1] == '-1'
              ? 'personalFifo'
              : false
        if (this.$route.name === 'caseDetail' && caseId !== this.caseId) {
          // 解决预测式外呼电话进来时正好处于详情页面
          caseInfo({ caseId }).then(res => {
            if (res.success) {
              const route = {
                name: 'caseDetail',
                params: {
                  id: res.data.id,
                  name: res.data.name,
                  allotAgent: allotAgent,
                  uuid: data.data.uuid,
                  isFifo: true,
                  isTransfer: data.action === 'getRobotInfo',
                  phone: data.data.phone,
                  caseIds: [],
                  index: 0
                }
              }
              this.refundForm = deepClone(REFOUND_FORM)
              this.updateCtiCall({ key: 'urgeRefund', data: res.data.id })
              this.$router.push(route)
              const list = this.$store.state.app.tagNavList
              for (const key in list) {
                if (list[key].params && list[key].params.id === res.data.id) {
                  list[key].params = route.params
                }
              }
              this.setTagNavList(list)
            }
          })
        } else {
          await this.getContactId(this.isInCalling.phone)

          if (this.refundForm.contactsId) {
            const arr = [...this.contacts, ...this.invalidContacts]

            for (const c of arr) {
              if (c.id === this.refundForm.contactsId) {
                this.refundForm.contactName = c.name
                this.refundForm.relationType = c.relationType
                this.refundForm.warning = c.warning
                this.refundForm.mobile = c.mobile
                break
              }
            }
          } else {
            this.$Message.error({
              content: `该联系人号码被删除，无法填写${this.$t(
                'reminder'
              )}，建议重新添加此联系人或重新填写其他${this.$t('reminder')}！`,
              duration: 4
            })
            this.missContactCall = data.data.phone
            return
          }
        }

        if (Number(caseId) === Number(this.caseId)) {
          this.isFifo = true
          this.allotAgent = allotAgent
          this.ui.selfUrgeRefund = true
          this.missContactCall = data.data.phone

          // 自动新建催记
          let sendData = {}
          sendData = {
            caseId: caseId,
            contactsId:
              this.contacts.filter(c => {
                return c.ownMobile === data.data.phone
              })[0] &&
              this.contacts.filter(c => {
                return c.ownMobile === data.data.phone
              })[0].id,
            callUuid: this.callUuid,
            callStyle: 1
          }

          addAutomatic(sendData)
        }
        const isInit = this.caseId === caseId
        this.updateCtiCall({ key: 'urgeRefund', data: caseId })

        if (isInit) {
          const allotAgent =
            data.data.userTag?.split('，')[1] == '1'
              ? true
              : data.data.userTag?.split('，')[1] == '-1'
                ? 'personalFifo'
                : false

          this.$route.params.id = this.caseId
          this.$route.params.allotAgent = allotAgent
          this.$route.params.uuid = data.data.uuid
          this.$route.params.isFifo = true
          this.$route.params.phone = data.data.phone
          this.$route.params.caseIds = []
          this.$route.params.index = 0

          this.initPage()
        }
      }
    },
    contactStatus(val) {
      if (val) {
        setTimeout(() => {
          this.setIsChangeCase(false)
          this.ui.loadingContact = false
        }, 1000)
      }
    },
    'ui.selfUrgeRefund'(val) {
      if (!val) return

      this.getOperation()
      this.$nextTick(() => {
        this.draggableValue.handle = this.$refs[this.handleId]
      })
    }
  },
  beforeUnmount() {
    // 案件详情页到非案件详情页
    if (this.ui.selfUrgeRefund) {
      const refunds = {
        ...this.refundForm,
        operationState: this.caseInfo.operationState,
        caseId: Number(this.caseId)
      }

      this.setOpenedRefundForm({ key: 'refundForm', data: refunds })
      this.ui.selfUrgeRefund = false
    }

    window.removeEventListener('resize', this.resizeWin)
  },
  created() {
    this.getRefData({
      map: ['operationStatus']
    })
    this.handleRepayColumns()
    this.initPage()

    if (this.userExtend.fieldColorSwitch) {
      for (const item of this.colorFields) {
        this.colorField.push(item.value)
      }
    }
  },
  mounted() {
    if (this.$refs.caseDetail) {
      this.draggableValue.boundingElement = this.$refs.caseDetail
      const height =
        document.documentElement.clientHeight || document.body.clientHeight
      this.ui.caseInfoHeight = height - 120
    }

    if (this.$refs.aiBtn) {
      this.draggableAiValue.boundingElement = this.$refs.caseDetail
      this.$nextTick(() => {
        this.draggableAiValue.handle = this.$refs.aiBtn
      })
    }

    window.addEventListener('resize', this.resizeWin)
  }
}
</script>

<style lang="less" scoped>
.case-detail {
  padding: 12px 0 0;
  overflow: hidden;
  overflow-y: auto;
  .case-alert {
    margin-bottom: 20px;
  }
  .team-fifo,
  .qos-detail {
    display: flex;
    > p {
      color: #282828;
      margin-right: 32px;
      > .value {
        font-weight: 600;
        margin-left: 8px;
      }
    }
  }
  .team-fifo {
    margin-left: 16px;
    margin-bottom: 20px;
    line-height: 32px;
  }
  .agent-info {
    border-bottom: 1px solid #dcdee3;
    padding: 0 16px 4px;
    > .top {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      > .left {
        display: flex;
        align-items: flex-start;
        white-space: nowrap;
        justify-content: flex-start;
        > .case-container {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .sign-color {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
          }
          .sign-warning {
            font-size: 16px;
            color: @primary-color;
            margin-right: 10px;
          }
          .title {
            font-size: 16px;
            font-weight: 600;
            margin-right: 8px;
          }
          > .legal-status {
            color: #25b360;
            background-color: rgba(37, 179, 96, 0.16);
            border-radius: 10px;
            padding: 2px 12px;
          }
          > .illegal-status {
            color: #ff1a2e;
            background-color: rgba(255, 26, 46, 0.16);
            border-radius: 10px;
            padding: 2px 12px;
          }
        }
        > .divide {
          width: 1px;
          height: 12px;
          background-color: #dcdee3;
          margin-right: 16px;
          margin-left: 16px;
          margin-top: 4px;
        }
        > .tag-container {
          display: inline-flex;
          align-items: flex-start;
          flex-wrap: wrap;
          padding-right: 80px;
          > .tag {
            height: 26px;
            padding: 4px 6px;
            border-radius: 4px;
            margin-right: 6px;
            white-space: nowrap;
            display: inline-flex;
            align-items: center;
            margin-bottom: 6px;
            > i {
              margin-right: 4px;
            }
          }
        }
      }
      > .right {
        display: flex;
        flex-wrap: nowrap;
      }
    }
    > .middle {
      margin-top: 10px;
      margin-bottom: 8px;
      display: flex;
      > p {
        padding-right: @padding-md;
        border-right: 1px solid #dcdee3;
        margin-left: 16px;
        line-height: 12px;
        &:first-child {
          margin-left: 0;
        }
        &:last-child {
          border-right: none;
        }
      }
    }
    > .comment {
      background-color: rgba(231, 115, 27, 0.1);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 16px;
      border-radius: 4px;
      margin-bottom: 12px;
      > .comment-box {
        color: #e7731b;
        display: flex;
        margin-right: 20px;
        height: 16px;
        overflow: hidden;
        > p.title {
          white-space: nowrap;
        }
        > .content {
          .contents {
            display: inline-block;
            height: 16px;
            overflow: hidden;
          }
        }
      }
      > .creates {
        white-space: nowrap;
        display: flex;
        align-items: center;
        > .create-by {
          color: #636c78;
          margin-right: 12px;
        }
        > .create-time {
          color: #636c78;
          margin-right: 12px;
        }
        > .check {
          font-size: 12px;
          color: #282828;
          display: inline-flex;
          align-items: center;
          > i {
            color: #636c78;
            margin-right: 2px;
          }
        }
      }
    }
  }
  .finish td {
    background: #f2f2f2;
  }
  .case-info {
    width: 100%;
    display: flex;
    position: relative;

    > .case-contacts {
      position: relative;
      width: 20%;
      min-width: 196px;
      height: 100%;
      margin-right: 12px;
      padding-top: 12px;
      box-shadow: 0px 6px 9px 0px rgba(137, 142, 155, 0.12);
      > .card {
        width: 100%;
        height: 100%;
        border-radius: 3px 3px 0px 0px;
        > .card-title {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 6px 10px 6px 16px;
          background: linear-gradient(
            135deg,
            #eee3ec 0%,
            #e9f3f9 14%,
            #e9eefb 39%,
            #eef1f6 49%,
            #ebf3f8 78%,
            #eff2f7 100%
          );
          border-radius: 3px 3px 0px 0px;
          > .title {
            font-size: 14px;
            font-weight: 600;
          }
        }
        > .card-body {
          width: 100%;
          height: calc(~"100% - 40px");
          position: relative;
          padding: 8px 0 8px 0;
          > .contact-radio {
            margin-bottom: 8px;
            margin-left: 16px;
          }
          > .contacts-list {
            list-style: none;
            width: 100%;
            height: calc(~"100% - 24px");
            overflow-y: auto;
            > li {
              width: 100%;
              padding: 0 16px;
              > .contact-info {
                border-bottom: 1px solid #dcdee3;
                padding: 12px 0;
                width: 100%;
                > .top {
                  width: 100%;
                  display: flex;
                  justify-content: space-between;
                  > .left {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    overflow: hidden;
                    white-space: nowrap;
                    :deep(.ivu-tooltip.full-tooltip) {
                      width: 100%;
                      overflow: hidden;
                    }
                    :deep(.ivu-tooltip) {
                      line-height: 1;
                      display: flex;
                      align-items: center;
                    }
                    :deep(.ivu-tooltip-rel) {
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      line-height: 1;
                    }
                    .name {
                      width: 100%;
                      font-size: 13px;
                      font-weight: 600;
                    }
                    .warning {
                      font-size: 16px;
                      color: #f26262;
                      margin: 0 10px;
                    }
                  }
                  > .right {
                    width: 113px;
                    color: #636c78;
                    display: inline-flex;
                    align-items: center;
                    justify-content: flex-end;
                    .active-contact {
                      color: @primary-color !important;
                    }
                    .active-contact-call {
                      color: #19be6b;
                    }
                    .passive-contact {
                      color: #cccccc;
                    }
                    .refund {
                      color: #636c78;
                      font-size: 15px;
                      margin-right: 10px;
                      cursor: pointer;
                      &:hover {
                        color: @primary-color;
                      }
                      &[disabled="disabled"] {
                        cursor: not-allowed;
                        color: #cccccc;
                      }
                    }
                    .msg {
                      font-size: 13px;
                    }
                    .more {
                      margin-right: 0;
                    }
                  }
                }
                > .middle {
                  width: 100%;
                  height: 18px;
                  display: flex;
                  align-items: center;
                  margin-top: 6px;
                  :deep(.ivu-tooltip) {
                    width: 100%;
                    overflow: hidden;
                    line-height: 1;
                    display: flex;
                    align-items: center;
                    flex-basis: content;
                  }
                  :deep(.ivu-tooltip-rel) {
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    line-height: 1;
                  }
                  .relation {
                    color: #636c78;
                  }
                  .mobile {
                    width: 86px;
                    color: #636c78;
                    border-left: 1px solid #dcdee3;
                    padding-left: 10px;
                    margin-left: 10px;
                    line-height: 12px;
                  }
                }
                > .bottom {
                  width: 100%;
                  margin-top: 4px;
                  color: #636c78;
                  white-space: nowrap;
                  .desc {
                    display: inline-block;
                    width: 100%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                  :deep(.ivu-tooltip) {
                    width: 100%;
                  }
                  :deep(.ivu-tooltip-rel) {
                    display: inline-flex;
                    align-items: center;
                  }
                }
              }
              &:hover {
                background-color: #f3f4f8;
              }
            }
            .sign {
              background-color: #f3f4f8;
            }
          }
          > .empty-data {
            margin-top: 50px;
            > img {
              display: block;
              width: 129px;
              margin: 0 auto;
            }
            > p {
              margin-top: 16px;
              color: #8d959f;
              text-align: center;
              > a {
                color: #ff4f1f;
              }
            }
          }
        }
      }
      > .contacts-card {
        border: 1px solid #dcdee2;
        border-radius: 3px 3px 0px 0px;
        > .card-title {
          > .add-contact {
            &:hover {
              color: @primary-color;
              border-color: @primary-color;
            }
          }
        }
      }
      > .refund-fold {
        position: absolute;
        right: -32px;
        cursor: pointer;
        > .top {
          width: 32px;
          height: 44px;
          background-color: rgba(255, 79, 31, 0.22);
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0px 4px 0px 0px;
          > i {
            color: #f75159;
            font-size: 20px;
          }
        }
        > .bottom {
          width: 32px;
          height: 20px;
          background: rgba(255, 79, 31, 0.11);
          border-radius: 0px 0px 4px 0px;
          display: flex;
          align-items: center;
          justify-content: center;
          > i {
            color: @primary-color;
            font-size: 16px;
          }
        }
      }
    }
    > .refund-box {
      position: absolute;
      width: 440px;
      background: #ffffff;
      border-radius: 4px;
      z-index: 1001;
      box-shadow: 0px 2px 8px 2px rgba(0, 0, 0, 0.15);
      > .refund-title {
        width: 100%;
        height: 40px;
        background: #4f515e;
        border-radius: 4px 4px 0px 0px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 16px;

        > .title {
          font-size: 14px;
          color: #fff;
          font-weight: 600;
        }
        > .refund-btns {
          > a {
            color: #fff;
            display: inline-flex;
            align-items: center;
            margin-right: 8px;
            > i {
              font-size: 14px;
              margin-right: 4px;
            }
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
      > .refund-content {
        position: relative;
        padding: 16px 16px 0;
        overflow: hidden;
        overflow-y: auto;
        background: #ffffff;
        border-radius: 0px 0px 4px 4px;
        // box-shadow: 1px -1px 0px 2px rgba(0, 0, 0, 0.15);

        .contact-refund-info {
          line-height: 16px;
          margin-top: 8px;
          > .name {
            font-weight: 600;
            padding-right: 10px;
            border-right: 1px solid #dcdee3;
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 4px;
            > i {
              color: #f26262;
              font-size: 16px;
              margin-left: 6px;
            }
          }
          > .relation {
            display: inline-block;
            font-weight: 600;
            padding-right: 10px;
            border-right: 1px solid #dcdee3;
            margin-right: 10px;
            margin-bottom: 4px;
          }
          > .mobile {
            display: inline-block;
            font-weight: 600;
          }
        }
        .refund-result,
        .refund-call-result {
          > .item {
            font-weight: 600;
            line-height: 26px;
            margin-right: 16px;
          }
        }
        .refund-result {
          .s0 {
            color: rgb(255, 91, 83);
          }
          .s1 {
            color: rgb(0, 172, 173);
          }
          .s2,
          .s3 {
            color: rgb(247, 157, 0);
          }
          .s4 {
            color: rgb(0, 154, 214);
          }
          .s5 {
            color: rgb(255, 0, 0);
          }
          .s6 {
            color: rgb(0, 96, 255);
          }
          .s7,
          .s8 {
            color: rgb(102, 102, 102);
          }
        }
        .refund-call-result {
          .s5,
          .s6 {
            color: rgb(124, 134, 202);
          }
        }
        .width-120 {
          width: 120px;
        }
        .width-180 {
          width: 180px;
        }
        .width-200 {
          width: 200px;
        }
        .upload-file {
          .tips {
            font-weight: 400;
            color: #636c78;
            line-height: 17px;
            margin-top: 10px;
          }
          .file-list {
            margin-top: 8px;
            max-width: 100%;
            display: flex;
            flex-wrap: wrap;
            > div {
              background-color: #f3f4f7;
              border-radius: 3px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0 12px;
              white-space: nowrap;
              margin-right: 6px;
              margin-bottom: 4px;
              &:nth-child(2n) {
                margin-right: 0;
              }

              i {
                color: #636c78;
              }
              .file-box {
                width: 90px;
              }
              .file {
                font-size: 14px;
                margin-right: 6px;
              }
              .file-name {
                display: inline-block;
                width: 80px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                position: absolute;
              }
              .delete {
                font-size: 18px;
                margin-left: 12px;
                cursor: pointer;
              }
            }
          }
        }
        .next-exprise {
          list-style: none;
          display: inline-block;
          > li {
            display: inline-block;
            color: @primary-color;
            margin-left: 10px;
            cursor: default;
            &:first-child {
              margin-left: 12px;
            }
          }
        }
        .hide-refund {
          margin-top: 2px;
          .hide-text {
            margin-left: 6px;
          }
        }
        .no-contact-tip {
          color: #888;
          text-align: center;
          margin-top: 45%;
        }
      }
      > .subStyle {
        width: 100%;
        height: 56px;
        background: #fff;
        border-radius: 0 0 4px 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 16px;
        border-top: 1px solid #dcdee3;
        .confirm {
          margin-right: 12px;
        }
        .info {
          margin-left: 8px;
          color: #636c78;
          font-size: 14px;
        }
      }
    }
    @media screen and (max-height: 700px) {
      .refund-box {
        .refund-content {
          max-height: 70vh;
        }
      }
    }
    @media screen and (min-height: 700px) {
      .refund-box {
        .refund-content {
          max-height: 529px;
          min-height: 494px;
        }
      }
    }
    > .case-details {
      width: 100%;
      height: 100%;
      overflow: hidden;
      overflow-y: auto;
      padding-right: 2px;
      padding-top: 12px;
      > .card {
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 6px 9px 0px rgba(137, 142, 155, 0.12);
        border-radius: 4px;
        border: 1px solid #dcdee2;
        padding: 0 16px;
        margin-bottom: 12px;
        &:last-child {
          margin-bottom: 0;
        }
        > .title {
          border-bottom: 1px solid #dcdee3;
          font-weight: 600;
          font-size: 14px;
          line-height: 20px;
          padding: 9px 0 11px;
          display: flex;
          justify-content: space-between;
        }
        .tabPane-content {
          > .caseinfo {
            margin-top: 16px;
            margin-bottom: 12px;
            > p.title {
              color: #636c78;
              display: flex;
              align-items: center;
              > i {
                font-size: 16px;
                margin-right: 4px;
              }
              > span {
                font-weight: 600;
              }
            }
            > .account-tab {
              margin-top: 10px;
              :deep(.ivu-tabs-bar) {
                margin-bottom: 16px;
              }
            }
            .case-item-container {
              margin-top: 12px;
              border-bottom: 1px solid #dcdee2;
              > .case-item {
                list-style: none;
                display: flex;
                > li {
                  width: 25%;
                  display: flex;
                  border-top: 1px solid #dcdee2;
                  border-left: 1px solid #dcdee2;
                  > .key {
                    width: 109px;
                    flex-shrink: 0;
                    background: #f2f4f7;
                    border-right: 1px solid #dcdee2;
                    padding: 8px 12px;
                    line-height: 17px;
                    display: flex;
                    align-items: center;
                  }
                  > .value {
                    width: 100%;
                    padding: 8px 12px;
                    line-height: 17px;
                    display: flex;
                    align-items: center;
                    word-wrap: break-word;
                    word-break: break-all;

                    .icon {
                      font-size: 16px;
                      color: @primary-color;
                      cursor: pointer;
                      line-height: 17px;
                    }
                    &:hover {
                      background-color: #fff7f5;
                    }
                  }
                  .values {
                    width: 100%;
                    height: 60px;
                    padding: 8px 12px;
                    line-height: 17px;
                    text-overflow: ellipsis;
                    white-space: normal;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    word-wrap: break-word;
                    word-break: break-all;
                    &:hover {
                      background-color: #fff7f5;
                    }
                  }
                  &:last-child {
                    flex: 1;
                    border-right: 1px solid #dcdee2;
                  }
                }
              }
            }
          }
          > .talkover-info {
            padding: 9px 0 16px;
            > .talkover-content {
              background-color: #f8f9fb;
              padding: 10px 12px 10px 10px;
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              border-radius: 4px;
              word-break: break-all;
              > span {
                white-space: pre-wrap;
                font-weight: 400;
                line-height: 19px;
              }
            }
            > .talkover-sub {
              color: #636c78;
              line-height: 24px;
              margin-top: 4px;
              text-align: right;
              > .create-by {
                margin-right: 16px;
              }
            }
            > .edit-talkover {
              width: 100%;
              display: flex;
              > .talkover-input {
                width: 100%;
                margin-right: 20px;
              }
              > .btns {
                display: flex;
                align-items: center;
              }
            }
          }
          > .no-talkover {
            height: 93px;
            color: #8d959f;
            font-size: 12px;
            line-height: 20px;
            text-align: center;
            padding-top: 38px;
            font-weight: 400;
            > i {
              font-size: 16px;
              margin-right: 6px;
            }
          }
        }
        > .content {
          > .caseinfo {
            margin-top: 16px;
            margin-bottom: 12px;
            > p.title {
              color: #636c78;
              display: flex;
              align-items: center;
              > i {
                font-size: 16px;
                margin-right: 4px;
              }
              > span {
                font-weight: 600;
              }
            }
            > .account-tab {
              margin-top: 10px;
              :deep(.ivu-tabs-bar) {
                margin-bottom: 16px;
              }
            }
            .case-item-container {
              margin-top: 12px;
              border-bottom: 1px solid #dcdee2;
              > .case-item {
                list-style: none;
                display: flex;
                > li {
                  width: 25%;
                  display: flex;
                  border-top: 1px solid #dcdee2;
                  border-left: 1px solid #dcdee2;
                  > .key {
                    width: 109px;
                    flex-shrink: 0;
                    background: #f2f4f7;
                    border-right: 1px solid #dcdee2;
                    padding: 8px 12px;
                    line-height: 17px;
                    display: flex;
                    align-items: center;
                  }
                  > .value {
                    width: 100%;
                    padding: 8px 12px;
                    line-height: 17px;
                    display: flex;
                    align-items: center;
                    word-wrap: break-word;
                    word-break: break-all;

                    .icon {
                      font-size: 16px;
                      color: @primary-color;
                      cursor: pointer;
                      line-height: 17px;
                    }
                    &:hover {
                      background-color: #fff7f5;
                    }
                  }
                  :deep(.ivu-tooltip) {
                    height: fit-content;
                  }
                  .values {
                    width: 100%;
                    // height: 60px;
                    padding: 8px 12px;
                    line-height: 17px;
                    text-overflow: ellipsis;
                    white-space: normal;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    word-wrap: break-word;
                    word-break: break-all;
                    &:hover {
                      background-color: #fff7f5;
                    }
                  }
                  &:last-child {
                    flex: 1;
                    border-right: 1px solid #dcdee2;
                  }
                }
              }
            }
          }
          > .talkover-info {
            padding: 9px 0 16px;
            > .talkover-content {
              background-color: #f8f9fb;
              padding: 10px 12px 10px 10px;
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              border-radius: 4px;
              word-break: break-all;
              > span {
                white-space: pre-wrap;
                font-weight: 400;
                line-height: 19px;
              }
            }
            > .talkover-sub {
              color: #636c78;
              line-height: 24px;
              margin-top: 4px;
              text-align: right;
              > .create-by {
                margin-right: 16px;
              }
            }
            > .edit-talkover {
              width: 100%;
              display: flex;
              > .talkover-input {
                width: 100%;
                margin-right: 20px;
              }
              > .btns {
                display: flex;
                align-items: center;
              }
            }
          }
          > .no-talkover {
            height: 93px;
            color: #8d959f;
            font-size: 12px;
            line-height: 20px;
            text-align: center;
            padding-top: 38px;
            font-weight: 400;
            > i {
              font-size: 16px;
              margin-right: 6px;
            }
          }
        }
        > .table-tabs {
          :deep(.ivu-tabs-content){
            .billTable{
            .sys-btns{
              margin-bottom: 2px
            }
          }
          }
          .tables-header {
            margin-bottom: 12px;
            position: relative;
            &.billTable-btn{
              margin-bottom: 10px;
            }
            > .counting {
              margin-top: 12px;
              background: #ebeef3;
              border-radius: 4px;
              border: 1px solid #dcdee3;
              padding: 6px 16px;
              > span {
                > span {
                  font-weight: 600;
                  color: @primary-color;
                }
              }
            }
            .button-repay{
              position: absolute;
              left: 0;
              top: 43px;
            }
          }
          .case-form {
            .scroll {
              height: 326px;
              border: 1px solid #dcdee2;
              border-radius: 4px;
              overflow: hidden;
              overflow-y: auto;
              > div {
                padding: 0 6px;
              }
            }
          }
        }
      }
      > .tables-card {
        min-height: 543px;
        padding-bottom: 18px;
      }
    }
    .ai-btn {
      width: 116px;
      height: 40px;
      display: flex;
      align-items: center;
      padding: 10px 16px;
      background: linear-gradient( 187deg, #FF6700 0%, #FFB860 100%);
      box-shadow: -1px 2px 5px 0px rgba(156,77,28,0.38);
      border-radius: 100px 0px 0px 100px;
      position: absolute;
      right: 0;
      top: 12px;
      cursor: move;
      z-index: 1000;
      >img {
        width: 20px;
        height: 20px;
      }
      >p {
        font-size: 13px;
        color: #FFF;
        white-space: nowrap;
        margin-left: 2px;
        font-weight: 600;
        display: flex;
        align-items: center;
        >span {
          font-size: 15px;
          font-weight: 500;
          margin: 0 2px 2px;
        }
      }
      >div {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 16px;
        width: 16px;
        border-radius: 50%;
        background-color: #FFAC6B;
        margin-left: 10px;
        cursor: pointer;
        >i {
          color: #FFF;
          font-size: 14px;
        }
      }
    }
  }
  .next-btn {
    width: 32px;
    height: 70px;
    background: rgba(0, 0, 0, 0.42);
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    z-index: 9;
    top: 50%;
    cursor: pointer;
    > i {
      font-size: 22px;
      color: #fff;
    }
    &:active {
      background: rgba(0, 0, 0, 0.52);
    }
  }
  .disabled {
    background: rgba(0, 0, 0, 0.32);
    cursor: not-allowed;
    > i {
      color: #ccc;
    }
  }
  .left-btn {
    left: 0;
  }
  .right-btn {
    right: 0;
    transform: rotate(180deg);
  }
}
.end-cooper {
  .notice {
    font-weight: 400;
    color: #636c78;
    line-height: 17px;
    margin-bottom: 20px;
  }
}
.case-form {
  .case-scroll {
    height: 80px;
    border: 1px solid #dcdee2;
    border-radius: 4px;
    overflow: hidden;
    overflow-y: auto;
    > div {
      height: 80px;
      word-wrap: break-word;
    }
  }
}
.sign-field {
  > .value {
    color: @primary-color;
    > div {
      color: @primary-color;
    }
  }
}
.case-contact-form {
  .title {
    margin-bottom: 8px;
    > label {
      font-size: 13px;
      font-weight: 600;
      display: inline-block;
      width: 202px;
      padding-left: 2px;
      &:first-child {
        width: 100px;
      }
      &:last-child {
        width: 186px;
      }
    }
  }
}
.icon-hujiaozhongxin:before {
  font-size: 16px !important;
}
.relief-reduction {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
</style>
