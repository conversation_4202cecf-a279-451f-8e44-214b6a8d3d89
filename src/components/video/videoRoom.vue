<template>
  <div v-if="showModal">
    <Modal
      v-model="showModal"
      :width="isCollage ? '280': '910'"
      footer-hide
      class="rtc-modal"
      :closable="false"
      :class="{collage: isCollage}"
      :mask-closable="false"
      :draggable="!isCollage"
      reset-drag-position
      sticky
      :mask="false"
      :fullscreen="openValue"
    >
      <template v-if="!isCollage" #header>
        <div class="local-info">
          <div class="info-left">
            <span>{{ caseInfo.userName }}发起的视频调解</span>
            <span></span>
            <span>{{ duration }}</span>
          </div>
          <div class="info-right">
            <span @click="handleFullscreen">
              <Icon class="iconfont icon-quanping1" size="16"></Icon>
              {{ openValue ? '退出全屏' : '全屏' }}
            </span>
            <span v-if="!openValue" @click="collapse">
              <Icon class="iconfont icon-cuijishouqi" size="16"></Icon>
              收起
            </span>
          </div>
        </div>
      </template>
      <div class="rtc-info-contain">
        <div class="info-container" :class="{rtc_collage:isCollage, full_screen:openValue }">
          <!-- 本地流区域 -->
          <!-- v-if="localStream" -->
          <div :class="{remote_container: enableSmall, local_stream_container: !enableSmall}" @click.stop="switchStream">
            <span v-if="debotor" class="joinName">等待{{ caseInfo.name }}进入…</span>
            <div v-if="enableSmall && speaking && remoteStreamList.length" class="speaking">
              <span>正在讲话:</span>
              <span>{{ speaking }}</span>
            </div>
            <!-- 本地流播放区域 -->
            <div id="localStream" :class="{ local_stream_content:!enableSmall, remote_stream_container: enableSmall}"></div>
          </div>
          <!-- 远端流区域 -->
          <div :class="{remote_container: !enableSmall, local_stream_container: enableSmall}" @click.stop="switchStream">
            <div
              :class="{local_stream_content:enableSmall, remote_stream_container: !enableSmall}"
            >
              <div v-if="!enableSmall && speaking && remoteStreamList.length" class="speaking">
                <span>正在讲话:</span>
                <span>{{ speaking }}</span>
              </div>
              <div
                v-for="(item) in remoteStreamList"
                :id="item.getUserId()"
                :key="item.getUserId()"
              >
              </div>
            </div>
          </div>
          <!-- 底部操作栏 -->
          <div class="info-bottom">
            <div v-if="!isCollage" class="invite-link" :class="{disable_link: remoteStreamList.length, showDisable_link: invite}" @click="invite=true">
              <Icon class="iconfont icon-yaoqingjiaru "></Icon>
              <p>邀请加入</p>
            </div>
            <span v-else class="recycle" @click="isCollage=!isCollage">
              <Icon class="iconfont icon-huifu1" size="16"></Icon>
              恢复窗口
            </span>
            <Button type="primary" class="closeVideo" @click="closeVideo">结束视频</Button>
          </div>

          <div v-if="invite && !isCollage" class="invite-info">
            <div class="invite-top">
              <span class="invite-title">被邀请人加入视频</span>
              <Icon class="iconfont icon-anjianguanbi invite-right" size="26" color="#636C78" @click="invite=false"></Icon>
            </div>
            <div class="invite-container">
              <div class="invalid">
                <span>本次视频房间失效时间为：</span>
                <span>{{ roomInfo.expireTime }}</span>
              </div>
              <div class="tips">
                建议与被邀请人约定视频时间，在约定时间内提前进入房间，需调解员进入房间后，被邀请人方可进入房间。
              </div>

              <div class="copyInfo">
                调解员{{ caseInfo.userName }}邀请您加入视频调解<br>
                点击链接直接进行身份认证进入视频房间：<br>
                {{ inviteLink }}
              </div>
              <div>
                复制该信息，手机或电脑浏览器搜索该链接即可参与
              </div>
            </div>
            <img class="invite_bg" src="../../assets/images/invite-bg.png">
            <Button type="primary" class="copyLink" @click="handleCopyInviteLink">复制房间号和链接</Button>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import rtc from '../mixins/rtc.js'
import useClipboard from 'vue-clipboard3'
// import shareRtc from '../mixins/share-rtc.js'
// import LibGenerateTestUserSig from '@/libs/lib-generate-test-usersig.min.js'
import { formatTime } from '@/libs/util'
import { getRoomInfo, updateRoomStatus } from '@/api/mediate'
import { mapMutations } from 'vuex'
export default {
  name: 'compRoom',
  mixins: [rtc],
  props: {
    type: String,
    sdkAppId: Number,
    secretKey: String,
    userId: String,
    roomId: String,
    cameraId: String,
    microphoneId: String,
    inviteUserSig: String,
    userSig: String,
    caseInfo: Object
  },
  data() {
    return {
      inviteLink: 'https://h5.anmiai.com/invite?uuid=' + this.roomId,
      showModal: false,
      invite: false,
      duration: '00:00:00',
      timeRecord: Date.now(),
      time: null,
      debotor: true,
      enableSmall: false, // 这里默认远端流在右上角本地流占大头
      openValue: false, // 开启全屏
      isCollage: false, // 是否收起
      speakingInfo: {},
      roomInfo: {} // 房间信息
    }
  },
  watch: {
    cameraId(val) {
      this.switchDevice('video', val)
    },
    microphoneId(val) {
      this.switchDevice('audio', val)
    },
    isCollage(val) {
      if (this.remoteStreamList.length) {
        this.enableSmall = !val
      } else {
        this.enableSmall = false
      }
    }
  },
  mounted() {
    // const isFullscreen = document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.fullScreen || document.mozFullScreen || document.webkitIsFullScreen
    document.addEventListener('fullscreenchange', () => {
      this.openValue = !this.openValue
    })
    document.addEventListener('mozfullscreenchange', () => {
      this.openValue = !this.openValue
    })
    document.addEventListener('webkitfullscreenchange', () => {
      this.openValue = !this.openValue
    })
    document.addEventListener('msfullscreenchange', () => {
      this.openValue = !this.openValue
    })
    this.speakingInfo = {
      [this.caseInfo.userId]: this.caseInfo.userName + '(我)',
      [this.caseInfo.id]: this.caseInfo.name
    }
    getRoomInfo({ uuid: this.roomId }).then(res => {
      if (res.data) {
        this.roomInfo = res.data
      }
    })
    this.time = setInterval(() => {
      this.initStateTime()
    }, 1000)
  },
  methods: {
    ...mapMutations(['setMediate']),
    collapse() {
      this.isCollage = !this.isCollage
    },
    initStateTime() {
      const date = new Date().getTime() - this.timeRecord
      const length = Math.floor(date / 1000)
      this.duration = formatTime(length)
    },
    switchStream(event) {
      if (event.target.className === 'remote_stream_container' || event.target.localName === 'video') {
        this.enableSmall = !this.enableSmall
      }
    },
    closeVideo() {
      // this.handleLeave()
      // this.updateStatus()
      // this.showModal = false
      // this.$emit('on-close', false)
      // return
      this.$Modal.confirm({
        title: '结束视频提示',
        content: '房间在创建成功后24小时内有效，请注意沟通时长，是否确认结束本次沟通？',
        loading: true,
        onOk: () => {
          clearInterval(this.time)
          this.setMediate(false)
          this.handleLeave()
          this.updateStatus()
          this.showModal = false
          this.$Modal.remove()
          this.$emit('on-close', false)
          // this.$Modal.remove()
        },
        onCancel: () => {
          // console.log(222)
        }
      })
    },
    async updateStatus() {
      await updateRoomStatus({ uuid: this.roomInfo.uuid, status: 0 })
    },
    handleCopyInviteLink() {
      const { toClipboard } = useClipboard()
      toClipboard(this.inviteLink).then(() => {
        this.$Message.success('复制成功')
      })
      // navigator.clipboard.writeText(this.inviteLink)
    },
    // 点击【Join Room】按钮
    async handleJoinRoom() {
      if (!this.sdkAppId) {
        this.$Message.error('appid缺失')
        return
      }
      if (!this.userId || !this.roomId) {
        this.$Message.error('用户ID缺失')
        return
      }
      if (!this.userSig) {
        return this.$Message.error('签名缺失')
      }
      // const userSigGenerator = new LibGenerateTestUserSig(this.sdkAppId, this.secretKey, 604800)
      // this.userSig = userSigGenerator.genTestUserSig(this.userId)
      await this.initClient()
      await this.join()
      await this.initLocalStream()
      await this.playLocalStream()
      await this.publish()
    },

    // 点击【Publish】按钮
    async handlePublish() {
      await this.publish()
    },

    // 点击【Unpublish】按钮
    async handleUnpublish() {
      await this.unPublish()
    },

    // 点击【Leave Room】按钮
    async handleLeave() {
      await this.leave()
    },

    // 点击【开始屏幕分享】按钮
    async handleStartScreenShare() {
      if (!this.sdkAppId || !this.secretKey) {
        // alert(this.$t('Please enter sdkAppId and secretKey'));
        return
      }
      await this.initShareClient()
      await this.initShareLocalStream()
      await this.handleShareJoin()
      await this.handleSharePublish()
    },

    // 点击【停止屏幕分享按钮】
    async handleStopScreenShare() {
      await this.handleShareUnpublish()
      await this.handleShareLeave()
    },
    handleFullscreen() {
      const main = document.body
      if (this.openValue) {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      } else {
        if (main.requestFullscreen) {
          main.requestFullscreen()
        } else if (main.mozRequestFullScreen) {
          main.mozRequestFullScreen()
        } else if (main.webkitRequestFullScreen) {
          main.webkitRequestFullScreen()
        } else if (main.msRequestFullscreen) {
          main.msRequestFullscreen()
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.rtc-modal {
  :deep(.ivu-modal-body){
    padding:0
  }
  :deep(.ivu-modal-header){
    padding: 0;
  }
}
.collage{
  :deep(.ivu-modal-wrap){
    left: auto;
    right: 16px;
    top: 84px;
  }
  :deep(.ivu-modal-header){
    display: none;
  }
  :deep(.ivu-modal-content){
    width: 280px;
  }

}
 .local-info{
      height: 40px;
      background: #4F515E;
      border-radius: 4px 4px 0px 0px;
      display: flex;
      align-items: center;
      padding: 0 16px;
      color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .info-left{
        display: flex;
        align-items: center;
        >span:nth-child(1){
          font-size: 14px;
          display: inline-block;
          // padding-right: 10px;
          // border-right: 1px solid rgba(255,255,255,0.36);
        }
        >span:nth-child(2){
          display: inline-block;
          width: 1px;
          height: 16px;
          background: rgba(255,255,255,0.36);
          margin: 0 10px;
          // padding-left: 10px;
        }
      }
      .info-right{
        cursor: pointer;
        >span:nth-child(1){
          display: inline-block;
          margin-right: 12px;
          &:hover{
            color: #FF4F1F;
          }
        }
        >span:nth-child(2):hover {
          color: #FF4F1F;
        }
      }
  }
.rtc-info-contain{
  // position: fixed;
  // overflow: auto;
  // top: 0;
  // right: 0;
  // bottom: 0;
  // left: 0;
  // z-index: 1000;
  // -webkit-overflow-scrolling: touch;
  // outline: 0;
}
.info-container {
    // width: 910px;
    margin: 0 auto;
    position: relative;
    outline: none;
    background: #DCDEE2;
    // top: 100px;
    // background: #CFD2D7;
    box-shadow: 0px 4px 12px 0px rgba(0,0,0,0.15);
    // height: 496px;
    // display: flex;
    // background: #000;
    justify-content: space-between;
    .joinName{
      position: absolute;
      left: 0;
      top: 242px;
      left: 43%;
      color: #fff;
      font-size: 18px;
      z-index: 99;
    }
    .local_stream_container {
      width: 100%;
      // height: 496px;
      padding: 0 0 16px;
      position: relative;
      padding-bottom: 52px;

    }
    .local_stream_content{
      height: 496px;
      overflow: hidden;
      // background: #DBE9FA;
      background: #4F515E;
      // &.rtc_local{
      //   height: 100vh;
      // }
    }
    // 收起小窗css
    &.rtc_collage{
      .local_stream_container {
        padding: 0;
      }
      .local_stream_content{
        height: 154px;
        border-radius: 4px;
      }
      .remote_container{
        width: 100px;
        height: 100px;
        top:10px;
        display: none;
      }
      .joinName{
        top:50px;
        font-size: 14px;
        left: 30%;
      }
      .speaking{
        min-width: 96px;
        padding: 0 6px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        background: rgba(255,255,255,0.72);
        border-radius: 4px;
        position: absolute;
        left:4px;
        bottom: 4px;
        color: #636C78;
        z-index: 99;
        >span:nth-child(2){
          color: #282828;
          display: inline-block;
          margin-left: 4px;
       }
     }
      .info-bottom{
        justify-content: space-between;
        padding-left: 16px;
        cursor: pointer;
        position: relative;
        .recycle {
          color: #282828
        };
        .recycle:hover{
          color: #FF4F1F;
        }
        // align-items: center;
      }
    }
    // 全屏样式
    &.full_screen{
      .remote_container{
        width: 400px;
        height: 200px;
        .remote_stream_container{
          width: 100%;
          height: 100%
        }
        .joinName{
          left: -30%;
          top: 250px
        }
      }
      .local_stream_content{
        height: 86vh;
      }
    }
  }

  .remote_container {
    position: absolute;
    width: 200px;
    height: 110px;
    display: block;
    // background: #4F515E;
    z-index: 5;
    right:0;
    top:56px;
    .joinName{
      left: -150%;
      top: 200px;
    }
    .remote_stream_container {
      // background: #000;
      width: 200px;
      height: 110px;
      padding-right: 16px;
      margin: 0 10px 10px 0;
      border-radius: 4px;
      cursor: pointer;
    }
    // &.rtc_remote{
    //   width: 400px;
    //   height: 200px;
    //   .remote_stream_container {
    //   background: #000;
    //   width: 400px;
    //   height: 200px;
    //   margin: 0 10px 10px 0;
    //   cursor: pointer;
    //   }
    //   .local_stream_content{
    //     height: 100vh;
    //   }
    // }
    .speaking{
      min-width: 96px;
      padding: 0 6px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      background: rgba(255,255,255,0.72);
      border-radius: 4px;
      position: absolute;
      left:4px;
      bottom: 4px;
      color: #636C78;
      z-index: 99;
      >span:nth-child(2){
        color: #282828;
        display: inline-block;
        margin-left: 4px;
      }
    }
  }
  .info-bottom{
    // width: 910px;
    height: 52px;
    background: #FFFFFF;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0px 0px 6px 6px;
    position: absolute;
    bottom: 0;
    width: 100%;
    color: #636C78;
    .invite-link{
      width: 92px;
      height: 42px;
      padding-top: 2px;
      text-align: center;
      // background: #F3F5F8;
      border-radius: 4px;
      cursor: pointer;
      &.disable_link{
        color: #CCCCCC;
        cursor: not-allowed;
      }
    }
    .invite-link:hover {
      background: #F3F5F8;
    }
    .showDisable_link {
      background: #F3F5F8;
    }
    .closeVideo{
      position: absolute;
      right: 15px;
      top: 10px;
      height: 32px;
      line-height: 32px;
    }
  }
  .invite-info{
    width: 520px;
    height: 360px;
    background: linear-gradient(144deg, #FDFEFF 0%, #DBE9FA 200%);
    // background-image: linear-gradient(to right,#FDFEFF,#DBE9FA);
    box-shadow: 0px 4px 12px 0px rgba(0,0,0,0.15);
    border-radius: 4px;
    position: absolute;
    left:190px;
    bottom: 130px;
    z-index: 99;
    .invite-top{
      height: 44px;
      border-bottom: 1px solid #DCDEE3;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding:0 16px;
      .invite-title{
        font-weight: 600;
        font-size: 14px;
      }
      .invite-right{
        cursor: pointer;
      }
    }
    .invite-container{
      padding:  15px 16px 10px 16px;
      .invalid{
        >span:nth-child(2){
          color: #FF4F1F;
        }
      }
      .tips{
        margin-top: 4px;
        color: #636C78;
        border-bottom: 1px dashed #DCDEE3;
        padding-bottom: 16px;
      }
      .copyInfo{
        margin: 15px 0;
      }
    }
    .invite_bg{
      width: 216px;
      height: 216px;
      position: absolute;
      right:0;
      bottom: 0;
      z-index: -1000;
    }
    .copyLink{
      position: absolute;
      left: 196px;
      bottom: 10px;
      height: 32px;
    }
  }
</style>
