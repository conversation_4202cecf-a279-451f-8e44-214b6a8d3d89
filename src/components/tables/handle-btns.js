import { deepClone } from '@/libs/tools'
import {
  getTableTooltipConfirmBtn
} from '@/libs/uiUtil'
import { resolveComponent } from 'vue'

const checkBtnCanShow = (vm, params, btnName) => {
  return (vm.apifun && (!vm.apifun.checkBtnCanShow || (typeof vm.apifun.checkBtnCanShow === 'function' && vm.apifun.checkBtnCanShow(vm, params, btnName))))
}

const btns = {
  delete: (h, params, vm) => {
    if (!checkBtnCanShow(vm, params, 'delete')) { return null }

    return getTableTooltipConfirmBtn(h, {
      iconColor: '#B82920',
      size: 17,
      isDel: true,
      callBackFunc: () => {
        vm.deleteItem(params.row)
      }
    })
  },
  download: (h, params, vm) => {
    if (!checkBtnCanShow(vm, params, 'download')) { return null }

    return getTableTooltipConfirmBtn(h, {
      tipStr: '下载',
      confirmStr: '你确定要下载吗?',
      icon: 'ios-cloud-download',
      callBackFunc: () => {

      }
    })
  },
  edit: (h, params, vm) => {
    if (!checkBtnCanShow(vm, params, 'edit')) { return null }

    return h(resolveComponent('Button'), {
      type: 'text',
      onClick: () => {
        vm.showEditer.value = true
        vm.edittingRow.value = deepClone(params.row)

        for (const item of vm.updateFormModel.value.columnsCp) {
          if (item.update) {
            item.updateVal = vm.edittingRow.value[ vm.getSearchOrUpdateKey(item) ]
            // rowModifyKey 为真正需要修改的值
          }
        }
      }
    }, {
      default: () => '编辑'
    })
  },
  audioPlayer: (h, params, vm) => {
    if (!checkBtnCanShow(vm, params, 'audioPlayer')) { return null }

    return h(resolveComponent('Button'), {
      type: 'primary',
      shape: 'circle',
      size: 'small',
      icon: 'md-play',
      onClick: ($event) => {
        const startClass = 'ivu-icon-md-play'
        const pauseClass = 'ivu-icon-md-pause'
        const loadingClass = 'ivu-icon-ios-loading'
        const classList = $event.target.classList
        const isLoading = classList.contains(loadingClass)
        if (isLoading) { return }

        const isStart = classList.contains(pauseClass)
        const playerDom = vm.$el.querySelector('audio')
        const audioUrl = playerDom.getAttribute('src')

        if (isStart) {
          classList.remove(pauseClass)
          classList.add(startClass)
          playerDom.pause()
        } else {
          classList.remove(startClass)
          classList.add(pauseClass)

          if (!audioUrl && params.column.apifun && params.column.apifun.getAudioUrl) {
            classList.add('ivu-load-loop')
            classList.add(loadingClass)

            params.column.apifun.getAudioUrl(params).then((url) => {
              if (url) {
                playerDom.src = url
                playerDom.play()
              }
              classList.remove('ivu-load-loop')
              classList.remove(loadingClass)
            })
          } else if (audioUrl) {
            playerDom.play()
          } else {
            console.log('no auido src, please add column.apifun.getAudioUrl')
          }
        }
      }
    })
  }// audioPlayer
}

export default btns
