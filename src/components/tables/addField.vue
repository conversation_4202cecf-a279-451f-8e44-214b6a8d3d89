<template>
  <Drawer
    v-model="ui.showDrawer"
    :closable="false"
    :width="600"
    :mask-closable="false"
    @on-close="hclose"
  >
    <template #header>
      <div class="header">
        <div class="quit" @click="hclose">
          <Icon type="iconfont icon-fanhui" :size="16" />
          <span>添加字段</span>
        </div>
      </div>
    </template>

    <div class="content">
      <div class="left">
        <Tabs
          v-model="ui.isTab"
          class="tabs"
          :animated="false"
          @on-click="tabsClick"
        >
          <TabPane
            :label="$t('baileInfo')"
            name="baileInfo"
          >
            <div class="tab-content">
              <Spin v-if="ui.spinShow" size="large" fix></Spin>
              <div class="btns">
                <div class="search">
                  <Input
                    v-model.trim="ui.searchKey"
                    class="input"
                    placeholder="请输入关键字搜索"
                    clearable
                    suffix="ios-search"
                    @on-enter="handleSearch"
                    @on-change="handleSearch"
                  >
                  </Input>
                  <!-- <Button
                    class="h-32 search-btn"
                    type="primary"
                    icon="iconfont icon-chakanxiangqing"
                    @click="handleSearch"
                  >
                    搜索
                  </Button> -->
                </div>
              </div>
              <CheckboxGroup
                v-if="fixedField.length"
                v-model="ui.checkFields"
                class="checkgroup"
              >
                <Tooltip
                  v-for="(item, index) in fixedField"
                  :key="item.value"
                  :disabled="item.name.length <= 10"
                  :content="item.name"
                  :max-width="160"
                  theme="light"
                  transfer
                  placement="bottom-start"
                >
                  <Checkbox
                    class="checkbox"
                    :class="{ 'top-check': index <= 2 }"
                    border
                    :label="item.value"
                    :disabled="checkDisable.includes(item.value)"
                  >
                    {{ item.name }}
                  </Checkbox>
                </Tooltip>
              </CheckboxGroup>
              <div v-else class="empty">
                <!-- <img src="@/assets/images/empts.png" alt="" /> -->
                <img src="@/assets/images/search.png" alt="">
                <!-- <p>暂无可用字段</p> -->
              </div>
            </div>
          </TabPane>
          <TabPane
            :label="$t('addCustomTp')"
            name="customFields"
          >
            <div class="tab-content">
              <Spin v-if="ui.spinShow" size="large" fix></Spin>
              <div class="btns">
                <div class="search">
                  <Input
                    v-model.trim="ui.searchKey"
                    class="input"
                    placeholder="请输入关键字搜索"
                    clearable
                    suffix="ios-search"
                    style="width: 260px"
                    @on-enter="customHandleSearch"
                    @on-change="customHandleSearch"
                  ></Input>
                  <!-- <Button
                    class="h-32 search-btn"
                    type="primary"
                    icon="iconfont icon-chakanxiangqing"
                    @click="customHandleSearch"
                  >
                    搜索
                  </Button> -->
                </div>
                <div>
                </div>
              </div>
              <CheckboxGroup
                v-if="customField.length"
                v-model="ui.checkFields"
                class="checkgroup"
              >
                <Tooltip
                  v-for="(item, index) in customField"
                  :key="item.value"
                  :disabled="item.name.length <= 10"
                  :content="item.name"
                  :max-width="160"
                  theme="light"
                  transfer
                  placement="bottom-start"
                >
                  <Checkbox
                    class="checkbox"
                    :class="{ 'top-check': index <= 2 }"
                    border
                    :label="item.value"
                    :disabled="checkDisable.includes(item.value)"
                  >
                    {{ item.name }}
                  </Checkbox>
                </Tooltip>
              </CheckboxGroup>
              <div v-else class="empty">
                <!-- <img src="@/assets/images/empts.png" alt="" /> -->
                <img src="@/assets/images/search.png" alt="">
                <!-- <p>暂无可用字段</p> -->
              </div>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>

    <div class="footer">
      <Button type="primary" @click="handleSubmit">确定</Button>
      <!-- <div class="pop-content" slot="content">
          <p>
            <Icon type="ios-help-circle" />
            <span>勾选字段还未添加，如放弃本次添加请再次点击确定。</span>
          </p>
        </div> -->
      <Button
        style="margin-left: 12px"
        @click="hclose"
      >
        取消
      </Button>
    </div>
  </Drawer>
</template>

<script>
import draggable from 'vuedraggable'
import { deepClone } from '@/libs/tools'
import { getCustomTp } from '@/api/template'
import { getDictionary } from '@/api/data.js'

export default {
  components: {
    draggable
  },
  props: {
    customViewsTables: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      fixedField: [], // 固定字段
      customField: [], // 自定义字段
      checkDisabled: [],
      ui: {
        cloneFixedField: [], // 固定字段备份
        cloneCustomField: [], // 自定义字段备份
        showDrawer: true,
        checkFields: [], // checkbox勾选
        cloneCheckFields: [],
        cloneCheckCustomFields: [], // 原先以选自定义字段加上案件基本字段
        isTab: 'baileInfo',
        searchKey: null,
        showAdd: false,
        spinShow: false
      }
    }
  },
  created() {
    this.getDictionary()
  },
  methods: {
    // 固定字段
    async getDictionary() {
      // this.ui.spinShow = true
      this.ui.checkFields = []
      this.checkDisable = this.customViewsTables.map(item => item.key)
      const res = await getDictionary(['caseUserBaseFields', 'caseBaseFields'])
      const arr = [...JSON.parse(res.data[0].myvalue), ...JSON.parse(res.data[1].myvalue)].filter(c => c.name !== '员工编号')
      arr.forEach(item => item.value = this.hUpperCase(item.value))
      this.fixedField = arr
      this.ui.cloneFixedField = deepClone(this.fixedField)
      this.fixedField.forEach(e => {
        this.checkDisable.forEach(key => {
          if (e.value == key) {
            this.ui.checkFields.push(key)
          }
        })
      })

      this.ui.cloneCheckFields = deepClone(this.ui.checkFields)
      this.ui.spinShow = false
    },
    // 自定义字段
    async getCustomTp() {
      this.ui.spinShow = true
      // this.ui.checkFields = deepClone(this.ui.cloneCheckFields)
      const res = await getCustomTp()
      this.customField = res.data
      this.ui.cloneCustomField = deepClone(this.customField)
      const fileds = []
      this.customField.forEach(e => {
        this.checkDisable.forEach(key => {
          if (e.value === key) {
            fileds.push(key)
            this.ui.checkFields.push(key)
          }
        })
      })
      this.ui.cloneCheckCustomFields = [...this.ui.cloneCheckFields, ...fileds]
      this.ui.spinShow = false
    },
    // tabs切换
    tabsClick(name) {
      this.ui.searchKey = ''
      this.ui.isTab = name
      if (name === 'baileInfo') {
        this.ui.checkFields = deepClone(this.ui.cloneCheckFields)
        this.fixedField = deepClone(this.ui.cloneFixedField)
      } else {
        this.ui.checkFields = deepClone(this.ui.cloneCheckFields)
        this.getCustomTp()
      }
    },
    handleSearch() {
      if (!this.ui.searchKey) {
        this.fixedField = deepClone(this.ui.cloneFixedField)
        return
      }
      this.fixedField = this.ui.cloneFixedField.filter(_c => _c.name.indexOf(this.ui.searchKey) > -1)
    },
    customHandleSearch() {
      if (!this.ui.searchKey) {
        this.customField = deepClone(this.ui.cloneCustomField)
        return
      }
      this.customField = this.ui.cloneCustomField.filter(_c => _c.name.indexOf(this.ui.searchKey) > -1)
    },
    handleSubmit() {
      // const arr = deepClone(this.ui.checkFields)
      // console.log(this.ui.checkFields)
      // console.log( this.ui.cloneCheckFields, 'clone')
      // return
      const submitArr = []
      if (this.ui.isTab === 'baileInfo') {
        const arr = this.ui.checkFields.filter(x => this.ui.cloneCheckFields.indexOf(x) < 0)
        for (const value of arr) {
          for (const value1 of this.ui.cloneFixedField) {
            if (value === value1.value) {
              submitArr.push({
                name: value1.name, key: value1.value, checked: true, customType: value1.type
              })
            }
          }
        }
      } else {
        const arr = this.ui.checkFields.filter(x => this.ui.cloneCheckCustomFields.indexOf(x) < 0)
        for (const value of arr) {
          for (const value1 of this.ui.cloneCustomField) {
            if (value === value1.value) {
              submitArr.push({
                name: value1.name, key: value1.value, checked: true, customType: value1.type
              })
            }
          }
        }
      }
      this.$emit('add-fields', submitArr)
      this.hclose()
    },
    hclose() {
      // this.ui.showDrawer = false
      setTimeout(() => {
        this.$emit('on-close')
      }, 10)
    },
    hUpperCase(str) {
      const convertedStr = str.replace(/_(.)/g, function(match, p1) {
        return p1.toUpperCase()
      }).replace(/_/g, '')
      return convertedStr
    }
  }
}
</script>

<style lang="less" scoped>
:deep(.ivu-tabs-tab) {
  padding: 11px 16px !important;
  font-size: 12px !important;
}
:deep(.ivu-drawer-wrap) {
  z-index: 10000;
}
.quit {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  > i {
    margin-right: 10px;
    color: #636c78;
    &:hover {
      color: #505862;
    }
  }
}
.footer {
  width: 100%;
  position: absolute;
  border-top: 1px solid #dcdee3;
  padding: 10px 24px 0px 24px;
  bottom: 0;
  margin: 0 -16px;

  .pop-content {
    width: 240px;
    padding: 16px 16px 24px;
    white-space: normal;
    > p {
      width: 100%;
      display: flex;
      align-items: flex-start;
      line-height: 18px;
      > i {
        font-size: 16px;
        color: #faad14;
        margin-right: 8px;
      }
      > span {
        font-size: 13px;
      }
    }
  }
}
.content {
  height: calc(~"100% - 6px");
  display: flex;
  margin: -16px -16px 0;
  > .left {
    width: 100%;
    height: 100%;
    box-shadow: 4px 0 6px 0 rgba(0, 0, 0, 0.05);
    > .tabs {
      height: 100%;
      :deep(.ivu-tabs-bar) {
        margin-left: 24px;
        margin-right: 16px;
      }
      :deep(.ivu-tabs-content) {
        height: calc(~"100% - 60px");
        > .ivu-tabs-tabpane {
          height: 100%;
        }
      }
      .tab-content {
        height: 100%;
        padding-left: 24px;
        padding-right: 16px;
        position: relative;
        .btns {
          margin-top: 16px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-bottom: 12px;
          margin-right: 8px;
          > .search {
            > .input {
              width: 270px;
            }
            > .search-btn {
              border-radius: 0 4px 4px 0;
            }
          }
        }
        .checkgroup {
          height: calc(~"100% - 38px");
          overflow: hidden;
          overflow-y: auto;
          margin-right: -16px;
          padding-bottom: 12px;
          .checkbox {
            width: 164px;
            height: 32px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            margin-right: 10px;
            margin-top: 10px;
            margin-bottom: -8px;
            line-height: 32px;
          }
          .top-check {
            margin-top: 0;
          }
          :deep(.ivu-checkbox-border) {
            padding: 0 8px !important;
          }
          :deep(.ivu-checkbox-wrapper-checked.ivu-checkbox-border) {
            border-color: #dcdee2 !important;
          }
        }
        .empty {
          text-align: center;
          margin-top: 80px;
          > img {
            width: 211px;
            height: auto;
          }
          > p {
            color: #8d959f;
            > a {
              color: @primary-color;
              text-decoration: underline;
            }
          }
        }
      }
      .case-tag {
        padding-right: 16px;
        > .btns {
          display: flex;
          justify-content: flex-end;
          margin-bottom: 16px;
        }
        > .checkgroup {
          margin-left: 24px;
          .check {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 24px 16px 16px;
            &:nth-child(odd) {
              background-color: #f3f5f8;
            }
          }
          .edit-check {
            padding: 8px 16px;
            > .btn {
            }
          }
        }
      }
    }
  }
  > .right {
    width: 440px;
    flex-shrink: 0;
    height: 100%;
    padding: 16px 0 0 16px;
    > .header {
      display: flex;
      justify-content: space-between;
      padding-right: 16px;
      margin-bottom: 24px;
      > .title {
        > span {
          &:first-child {
            font-size: 13px;
            font-weight: 600;
            line-height: 18px;
          }
          &.sub {
            color: #8d959f;
          }
        }
      }
      > .count {
        color: #8d959f;
        > span {
          color: @primary-color;
        }
      }
    }
    > .content {
      height: calc(~"100% - 42px");
      overflow: hidden;
      overflow-y: auto;
      padding-bottom: 16px;
      .fields {
        display: inline-flex;
        align-items: center;
        background-color: #f2f4f7;
        border-radius: 4px;
        padding: 8px 12px;
        cursor: move;
        margin-right: 6px;
        margin-bottom: 12px;
        > i {
          font-size: 16px;
          color: #636c78;
          cursor: pointer;
          margin-left: 12px;
          &:hover {
            color: #828ea0;
          }
          &:active {
            color: #505862;
          }
        }
      }
    }
  }
}
</style>

