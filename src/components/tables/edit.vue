<template>
  <div class="tables-edit-outer">
    <div v-if="params.column.editable && params.column.type == 'select'">
      <Select v-model="selectVal" :disabled="isSelectDisabled" size="small" @on-change="changeSelectValue">
        <Option v-for="item in params.column.bindmap" :key="item.value" :value="item.value">{{ item.name }}</Option>
      </Select>
    </div>
    <div v-else class="tables-edit-con">
      <p class="value-con" :class="{ 'tables-edit-outer-ellipsis': ellipsis }">
        <span>{{ (showValue || showValue === 0) ? showValue : '无' }}</span>
        <Icon v-if="editable" type="iconfont icon-bianji" class="tables-edit-btn" @click.native="startEdit" />
      </p>
    </div>

    <Modal
      v-model="ui.reasonModal"
      :footer-hide="true"
      :closable="false"
      :mask-closable="false"
      :title="ui.reasonModalName"
    >
      <Form
        ref="changeForm"
        :model="form.change"
        :rules="form.changeRole"
        label-position="left"
        :label-width="110"
      >
        <FormItem label="回馈原因" prop="reason">
          <Input v-model.trim="form.change.reason" type="textarea" :autosize="{minRows: 2,maxRows: 5}"></Input>
        </FormItem>
        <FormItem>
          <Button type="primary" :loading="ui.updatingCell" @click="doChangeWithReason('changeForm')">
            提交
          </Button>
          <Button style="margin-left: 8px" @click="cancelChange('changeForm')">
            取消
          </Button>
        </FormItem>
      </Form>
    </Modal>

    <Modal
      v-model="isEditting"
      :title="`编辑${params.column.title}`"
      :footer-hide="true"
      :closable="false"
      :mask-closable="false"
    >
      <Form ref="forms" label-position="top" label-colon>
        <FormItem :label="params.column.title">
          <Input v-if="params.column.type !== 'textarea'" :disabled="ui.updatingCell" :model-value="value" @input="handleInput" />
          <Input v-if="params.column.type === 'textarea'" type="textarea" :rows="3" :disabled="ui.updatingCell" :model-value="value" @input="handleInput" />
        </FormItem>
        <FormItem class="btns">
          <Button style="margin-right: 12px;" :disabled="ui.updatingCell" @click="canceltEdit">
            取消
          </Button>
          <Button type="primary" :disabled="ui.updatingCell" @click="saveEdit">
            确定
          </Button>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script>

const doChange = (vw, val, _reason) => {
  vw.ui.updatingCell = true
  vw.params.column.apifun.editSingle(vw.params.row, val, _reason).then((res) => {
    if (res) {
      vw.selectVal = val
      vw.params.row[vw.params.column.key] = val
      vw.ui.reasonModal = false
    } else {
      vw.selectVal = vw.params.row[vw.params.column.key]
    }
    vw.ui.updatingCell = false
  })
}

const rollback = (vw) => {
  vw.selectVal = vw.params.row[vw.params.column.key]
}

export default {
  name: 'TablesEdit',
  props: {
    value: [String, Number],
    showValue: [String, Number],
    edittingCellId: String,
    updatingCell: Boolean,
    params: Object,
    editable: Boolean,
    ellipsis: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      ui: {
        reasonModalName: '原因',
        reasonModal: false,
        updatingCell: this.updatingCell,
        showEdit: false
      },
      selectVal: this.params.row[this.params.column.key],
      form: {
        change: {
          reason: null
        },
        changeRole: {
          reason: [{ required: true, message: '原因不能为空', trigger: 'blur' }]
        }
      }
    }
  },
  computed: {
    isSelectDisabled() {
      if (this.ui.updatingCell) { return true }

      if (this.params.column.apifun && this.params.column.apifun.disableFun) {
        return this.params.column.apifun.disableFun(this.params.row)
      }

      return false
    },
    isEditting() {
      return this.edittingCellId === `editting-${this.params.index}-${this.params.column.key}`
    }
  },
  methods: {
    doChangeWithReason() {
      doChange(this, this.selectVal, this.form.change.reason)
    },
    cancelChange(form) {
      this.formCancel(form)
      rollback(this)
    },
    formCancel(form) {
      this.$refs[form].resetFields()
      this.ui.reasonModal = false
    },
    changeSelectValue(val) {
      for (const cr of this.params.column.changeReasons) {
        if (cr.val === val) {
          this.formCancel('changeForm')
          this.ui.reasonModal = true
          if (cr.name) {
            this.ui.reasonModalName = cr.name
          }
          return
        }
      }

      this.$Modal.confirm({
        title: '修改确认提示',
        content: '是否要修改？',
        onOk: () => {
          doChange(this, val)
        },
        onCancel: () => {
          rollback(this)
        }
      })
    },
    handleInput(val) {
      this.$emit('input', val)
    },
    startEdit() {
      this.$emit('on-start-edit', this.params)
    },
    saveEdit() {
      this.$emit('on-save-edit', this.params)
    },
    canceltEdit() {
      this.$emit('on-cancel-edit', this.params)
    }
  }
}
</script>

<style lang="less">
.tables-edit-outer-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
}
.tables-edit-outer{
  width: 100% !important;
  height: 100%;
  .tables-edit-con{
    position: relative;
    width: 100%;
    height: 100%;
    cursor: default;
    >.value-con {
      position: relative;
      display: block;
      line-height: 22px;
      &:hover {
        >.tables-edit-btn {
          display: block;
        }
      }
      >.tables-edit-btn {
        cursor: pointer;
        font-size: 14px;
        position: absolute;
        right: 0px;
        bottom: 4px;
        width: 14px;
        height: 14px;
        background-color: #FEF6F5;
        display: none;
        &:hover {
          display: block;
        }
      }
    }

    &:hover {
      color: @primary-color;
    }
  }
}
</style>
