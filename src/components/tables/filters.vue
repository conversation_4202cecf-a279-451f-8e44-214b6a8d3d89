<template>
  <Poptip v-model="ui.onPoptip" popper-class="filter-popper" placement="bottom" :offset="3" :padding="padding" :width="width" transfer>
    <Icon :type="icon" :size="16" style="cursor: pointer; margin-left: 2px;" :color="isColor ? '#FF4F1F' : '#636C78'" />
    <template #content>
      <div style="overflow: hidden;">
        <slot></slot>
      </div>
    </template>
  </Poptip>
</template>

<script>
export default {
  props: {
    icon: {
      type: String,
      default: 'iconfont icon-shaixuan'
    },
    isColor: {
      type: Boolean,
      default: false
    },
    width: {
      type: Number,
      default: 300
    },
    padding: {
      type: [Number, String],
      default: '8px 16px'
    }
  },
  data() {
    return {
      ui: {
        onPoptip: false
      }
    }
  }
}
</script>

<style lang="less">
.filter-popper {
  width: 100%;
  .ivu-poptip-body-content {
    height: auto;
  }
}
</style>
