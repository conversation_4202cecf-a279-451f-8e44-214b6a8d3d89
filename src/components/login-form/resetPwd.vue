<template>
  <div class="resets">
    <div class="header">
      <img src="/bpw-static/anmi/logo/logo.png" alt="">
      <!-- <img src="../../assets/images/logo.png" alt=""> -->
    </div>

    <div v-if="!isSuccess" class="reset-con">
      <div class="return" @click="quit">
        <Icon type="iconfont icon-fanhui" />
        <span>返回登录</span>
      </div>

      <div class="cons">
        <p class="title">
          <span>重置密码</span>
        </p>
        <Form
          ref="pwdForm"
          class="forms"
          :model="pwdForm"
          :label-width="108"
          :rules="validatorPwd"
        >
          <FormItem :label="$t('k_mobile')+'：'" prop="mobile">
            <Input v-model.trim="pwdForm.mobile" class="inputs" type="text" placeholder="请输入你需要找回登录密码的手机号码" />
          </FormItem>
          <FormItem label="新密码：" prop="newPwd">
            <Input v-model="pwdForm.newPwd" class="inputs" type="password" :placeholder="$t('k_newPassword')" password />
          </FormItem>
          <FormItem label="确认新密码：" prop="confirmPwd">
            <Input v-model="pwdForm.confirmPwd" class="inputs" type="password" placeholder="请再输入一次新密码" password />
          </FormItem>
          <FormItem label="图形验证码：" prop="validateCode" class="validate-item">
            <Input
              v-model.trim="pwdForm.validateCode"
              placeholder="请输入图形验证码"
            ></Input>
            <!-- 此处不能使用插槽形式放在input标签内，短信倒计时会影响输入框中文输入，出现闪退情况-->
            <span class="imgcode-box">
              <img
                :src="vcodeImg"
                @click="refreshVcodeImg"
              >
            </span>
          </FormItem>
          <FormItem label="获取验证码：" class="vertify-item" prop="vertifyCode">
            <Input v-model.trim="pwdForm.vertifyCode" class="vertify-input" type="text" placeholder="请输入收到的验证码"></Input>
            <!-- 此处不能使用插槽形式放在input标签内，短信倒计时会影响输入框中文输入，出现闪退情况-->
            <Button class="get-code" :disabled="timeCountDown" @click="vertify(0)">
              {{ timeCountDown ? (vcDownCount + 's后重新获取') : '获取验证码' }}
            </Button>
          </FormItem>
          <p class="voice">
            收不到短信？试试<a :disabled="timeCountDown" @click="vertify(1)">
              语音验证码
            </a>
          </p>
          <p v-if="msg" class="msg">
            {{ msg }}
          </p>
          <FormItem class="btn">
            <Button type="primary" class="h-42" long @click="handleSubmit">
              确认
            </Button>
          </FormItem>
        </Form>
      </div>
    </div>
    <div v-else class="success-con">
      <img src="@/assets/images/pass.png" alt="">
      <p class="title">
        密码重置成功
      </p>
      <p class="tip">
        <span>{{ count }}s</span>后自动跳转至登录页
      </p>
      <Button type="primary" class="login-btn" @click="handleLogin">立即登录</Button>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapMutations } from 'vuex'
import { getImageUuid } from '@/libs/util'
import { forgetPwd } from '@/api/user'

export default {
  data() {
    return {
      pwdForm: {
        mobile: '',
        newPwd: '',
        confirmPwd: '',
        vertifyCode: '',
        validateCode: null
      },
      validatorPwd: {
        mobile: [{
          required: true,
          trigger: 'blur',
          validator: (rule, value) => {
            return new Promise((resolve, reject) => {
              if (!value) {
                reject('手机号码不能为空')
              } else if (value.length !== 11) {
                reject('请输入正确的手机号码')
              } else {
                resolve()
              }
            })
          }
        }],
        newPwd: [{
          required: true,
          trigger: 'blur',
          validator: (rule, value) => {
            return new Promise((resolve, reject) => {
              if (!value) {
                reject('请输入6-16位新密码')
              } else if (value.length < 6 || value.length > 16) {
                reject('请输入6-16位新密码')
              } else {
                resolve()
              }
            })
          }
        }],
        confirmPwd: [{
          required: true,
          trigger: 'blur',
          validator: (rule, value) => {
            return new Promise((resolve, reject) => {
              if (!value) {
                reject('密码不能为空')
              } else if (value !== this.pwdForm.newPwd) {
                reject('两次密码输入不一致')
              } else {
                resolve()
              }
            })
          }
        }],
        validateCode: [{
          required: true,
          trigger: 'blur',
          message: '图形验证码不能为空'
        }],
        vertifyCode: [{
          required: true,
          trigger: 'blur',
          message: '验证码不能为空'
        }]
      },
      msg: '',
      ifSendCode: false,
      isSuccess: false,
      count: 3
    }
  },
  computed: {
    ...mapGetters([
      'vcodeImg',
      'timeCountDown',
      'voTimeCountDown',
      'vcDownCount',
      'voDownCount'
    ])
  },
  methods: {
    ...mapMutations([
      'refreshVcodeImg'
    ]),
    ...mapActions([
      'getVerify'
    ]),
    quit() {
      this.$emit('on-close')
    },
    vertify(isVoice) {
      if (this.ifSendCode) return
      if (!this.pwdForm.mobile) {
        return this.$Message.error('手机号码不能为空')
      }

      if (this.pwdForm.mobile && this.pwdForm.mobile.length !== 11) {
        return this.$Message.error('请输入正确的手机号码')
      }

      if (!this.pwdForm.validateCode) {
        return this.$Message.error('图形验证码不能为空')
      }

      const sendData = {
        mobile: this.pwdForm.mobile,
        type: 0,
        isVoice,
        validateCode: this.pwdForm.validateCode,
        imageUuid: getImageUuid()
      }

      this.ifSendCode = true
      this.getVerify(sendData).then(res => {
        if (res.success) {
          if (isVoice) {
            this.msg = '语音验证码正在发送，请注意接听'
          } else {
            this.msg = '短信验证码已发送至********' + this.pwdForm.mobile.slice(-4)
          }
        } else {
          this.refreshVcodeImg()
          this.msg = ''
        }
      }).finally(() => {
        this.ifSendCode = false
      })
    },
    handleSubmit() {
      this.$refs.pwdForm.validate((valid) => {
        if (valid) {
          const sendData = {
            mobile: this.pwdForm.mobile,
            newPassword: this.pwdForm.newPwd,
            smsCode: this.pwdForm.vertifyCode
          }

          forgetPwd(sendData).then(res => {
            if (res.success) {
              this.isSuccess = true

              let timer = setInterval(() => {
                this.count = this.count > -1 ? this.count - 1 : -1

                if (this.count === -1) {
                  clearInterval(timer)
                  this.$emit('on-close')
                }
              }, 1000)
            }
          })
        }
      })
    },
    handleLogin() {
      this.$emit('on-close')
    }
  }
}
</script>

<style lang="less" scoped>
.resets {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;

  >.header {
    width: 100%;
    height: 90px;
    background-color: #282323;
    padding: 0 116px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #DCDEE3;
    margin-bottom: 32px;
    >img {
      height: 40px;
    }
  }
  >.reset-con {
    >.return {
      padding: 0 116px;
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-bottom: 16px;
      >i {
        font-size: 16px;
      }
      >span {
        color: #636C78;
        font-size: 14px;
        margin-left: 2px;
      }
      &:hover {
        >i {
          color: @primary-color;
        }
        >span {
          color: @primary-color;
        }
      }
    }
    >.cons {
      text-align: center;
      >.title {
        color: #000;
        font-size: 24px;
        margin-bottom: 24px;

        >span {
          margin-left: -226px;
        }
      }
      >.forms {
        width: 536px;
        margin: 0 auto;

         :deep(.ivu-form-item-label) {
          font-size: 14px;
        }
         :deep(.ivu-form-item) {
          margin-bottom: 20px;
        }
         :deep(.ivu-input) {
          padding: 6px 12px 4px;
        }

        .validate-item {
          position: relative;
          .imgcode-box {
            position: absolute;
            right: 2px;
            top: 2px;
            >img {
              height: 30px;
            }
          }
        }
        .vertify-item {
          .vertify-input {
            width: calc(~"100% - 126px");
             :deep(.ivu-input) {
              border-radius: 4px 0 0 4px;
              border-right: none;
            }
          }
          .get-code {
            width: 126px;
            height: 32px;
            font-size: 14px;
            border-radius: 0 4px 4px 0;
            background-color: #EBEEF3;
            padding: 2px 16px 0;

            &:hover {
              background-color: #EBEEF3;
              color: #282828;
              border-color: #DCDEE2;
            }
            &:disabled {
              color: #282828;
            }
          }
        }
        .voice {
          text-align: right;
          line-height: 22px;
          margin-top: -4px;
          color: #636C78;
          font-size: 14px;
          >a {
            text-decoration: underline;
            color: #FF6000;

            &[disabled] {
              color: #F1D8C7;
              cursor: not-allowed;
            }
          }
        }
        .msg {
          text-align: left;
          padding-left: 108px;
          margin-bottom: -16px;
          margin-top: 20px;
          font-size: 14px;
        }
        .btn {
          margin-top: 32px;
           :deep(.ivu-btn) {
            font-size: 14px;
          }
        }
      }
    }
  }
  >.success-con {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-top: 64px;
    >img {
      width: 48px;
      margin-bottom: 24px;
    }
    >.title {
      font-size: 24px;
      color: #333;
      line-height: 33px;
      margin-bottom: 8px;
    }
    >.tip {
      font-size: 18px;
      color: #666;
      line-height: 25px;
      >span {
        color: #FF6700;
      }
    }
    >.login-btn {
      margin-top: 32px;
      width: 160px;
      height: 42px;
      font-size: 14px;
    }
  }
}
</style>
