<template>
  <Drawer
    v-model="ui.showDrawer"
    custom-class="drawer"
    :mask-closable="true"
    :closable="false"
    :transfer="false"
    :width="1000"
    :with-header="false"
    @on-close="handleClose"
  >
    <div class="drawer-content">
      <div class="header" :loading="ui.loading">
        <div class="header-l">
          <div class="quit" @click="handleClose">
            <Icon type="iconfont icon-fanhui" :size="15" />
          </div>
          <div class="infos">
            <Tooltip
              :content="hideName(debtInfo.name)"
              :disabled="debtInfo.name && debtInfo.name.length < 8"
            >
              <span class="wx-name">联系人：{{ hideName(debtInfo.name) }}</span>
            </Tooltip>
            <p class="info">
              <span
                >关系：<b>{{ debtInfo.relationType }}</b>
              </span>
              <span style="padding-right: 0"> 微信： </span>
              <span
                class="wxPhone"
              >
                <span style="border-left: 0; padding-left: 0">
                  <Avatar
                    :src="
                      debtInfo.wechatInfoList
                        ? userInfo.img
                        : artiImg
                    "
                    icon="ios-person"
                    size="26"
                    style="margin-bottom: 1px"
                  ></Avatar>
                  <b style="margin-left: 6px; margin-right: 10px">{{
                    debtInfo.wechatInfoList ? userInfo.nickname : "--"
                  }}</b>
                </span>
                <Poptip padding="0" v-model="showList">
                  <span
                    class="toggle"
                    v-if="
                      debtInfo.wechatInfoList
                        ? debtInfo.wechatInfoList.length > 1
                        : false
                    "
                  >
                    <Icon type="iconfont icon-qiehuan1" size="16" ></Icon>
                    切换
                  </span>
                  <template #content class="tipContent" >
                    <ul id="wxInfo">
                      <li
                        v-for="(item, index) in this.debtInfo.wechatInfoList"
                        :key="index"
                        @click="hwechat(item)"
                      >
                        <img
                          :src="item.img ? item.img : artiImg"
                          alt=""
                        />
                        <div class="wxName">
                          <p :class="{ hColor: item.originWechatNo == userInfo.originWechatNo }">
                            {{ item.nickname }}
                          </p>
                        </div>
                      </li>
                    </ul>
                  </template>
                </Poptip>
              </span>
            </p>
          </div>
        </div>
      </div>
      <div class="content" :loading="ui.loading">
        <div class="agents">
          <div style="margin-left: 16px; padding-right: 16px">
            <Select
              v-model="currentAgent"
              style="width: 332px; height: 32px"
              @on-change="hChange(currentAgent)"
            >
              <Option
                v-for="item in agentList"
                :key="item.id"
                :label="'跟进催员：' + item.name"
                :value="item.id"
                >{{ item.name }}</Option
              >
            </Select>
          </div>
          <div class="correlation">
            <div style="margin-right: 4px; margin-top: -1px;">
              <!-- <img src="@/assets/images/wx.png" alt="" /> -->
              <Icon
                type="iconfont icon-weixin1"
                size="16"
                style="color: #5ac981"
              ></Icon>
            </div>
            <span
              >关联微信号
              <b style="color: #ff6000">{{ userList.length }}</b></span
            >
          </div>
          <ul class="list" v-if="userList.length">
            <li
              v-for="item in userList"
              :key="item.wechatNo"
              :class="{ 'active-agent': conversationId === item.wechatNo }"
              @click="changeConversation(item)"
            >
              <div style="margin-left: 10px; margin-right: 10px">
                <Avatar :src="item.img ? item.img : '@/assets/images/data_image.png'" size="40"></Avatar>
              </div>
              <div>
                <p
                  style="
                    font-size: 13px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #282828;
                    margin-bottom: 3px;
                  "
                >
                  {{ item.nickName }}
                </p>
              </div>
            </li>
          </ul>
          <div v-else class="empty">
            <img src="@/assets/images/empts.png" alt="" />
            <p>暂无数据</p>
          </div>
        </div>
        <div class="chat-container">
          <div class="ctent-header">
            <span>{{ expeditorName }}</span>
            <DatePicker
              v-model="timerange"
              @on-change="changeDate"
              format="yyyy-MM-dd"
              type="daterange"
              placement="bottom-end"
              placeholder="请选择"
              :clearable="false"
              :options="options"
              style="width: 232px; height: 32px"
            />
          </div>
          <div
            class="cons"
            :loading="ui.loadingMsg"
            ref="chartContentSrollWrapper"
          >
            <p
              class="load"
              v-if="ui.loadBefore"
              @click="loadBefore"
              style="margin-bottom: 12px"
            >
              <a
                :loading="ui.loadMoreLoading"
                element-loading-text="加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(255, 255, 255, 0.8)"
                >加载更多
              </a>
            </p>
            <p class="no-more" v-else style="margin-bottom: 12px">
              <a :loading="ui.loadMoreLoading">已无更多数据</a>
            </p>
            <ul class="msg-list">
              <template v-for="(item, index) in msgList">
                <li
                  v-if="item.type !== 10000"
                  :key="item.wechatId"
                  :id="`txt-${item.wechatId}`"
                  :class="
                    userInfo.originWechatNo === item.sender ? 'left' : 'right'
                  "
                >
                  <img
                    v-if="
                      userInfo.originWechatNo === item.sender
                        ? !!userInfo.img
                        : !!conversationAgent.img
                    "
                    class="img"
                    :src="
                      userInfo.originWechatNo === item.sender
                        ? userInfo.img
                        : conversationAgent.img
                    "
                    alt=""
                  />
                  <div v-else class="avator">
                    <img src="@/assets/images/avator.png" alt="" />
                  </div>
                  <div class="info">
                    <p class="time">
                      {{ formatTime(item.sendTime) }}
                    </p>
                    <!-- type微信消息类型：1.文本，3.图片，34.语音，*********.红包，*********.转账，10000.系统消息 -->
                    <p class="txt" v-if="item.type === 1">
                      {{ item.content }}
                    </p>

                    <div v-if="item.type === 3" class="img" >
                      <img 
                        style="width: 150px; height: auto"
                        :src="item.resourceUrl"
                        @click="showPreview(item)"
                      />
                      <div id="previewContainer" @click="hidePreview" v-if="preview">
                          <img id="largePreview" :src="preview">
                      </div>
                    </div>

                    <div
                      v-if="item.type === 34"
                      class="audios"
                      @click="playVideo(item, index)"
                    >
                      <div class="plays">
                        <img
                          :src="
                            requireImg(
                              `${
                                userInfo.originWechatNo === item.sender
                                  ? 'tiktok'
                                  : 'wechat'
                              }_${item.time % 3}.png`
                            )
                          "
                          alt=""
                        />

                        <div class="trans-content" v-if="item.isCheckSensitive">
                          <div
                            v-if="!!item.content && item.content.trim() !== ''"
                          >
                            {{ item.content }}
                          </div>
                          <div v-else class="empty">
                            <i class="el-icon-warning-outline"></i>
                            <span>语音内容为空</span>
                          </div>
                        </div>
                      </div>
                      <p class="duration">
                        {{ parseInt(item.resourceLength / 1000) }}'
                      </p>
                    </div>

                    <div
                      v-if="item.type === 50"
                      class="hAudio"
                      style="backgroundColor:#fff"
                    >
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          padding: 5px;
                          border-bottom: 1px solid #e2e2e2;
                        "
                      >
                        <img
                          v-if="userInfo.originWechatNo === item.sender"
                          src="@/assets/images/call.png"
                          alt=""
                        />
                        <span style="margin: 0 8px">语音通话</span>
                        <img
                          v-if="userInfo.originWechatNo !== item.sender"
                          src="@/assets/images/video.png"
                          alt=""
                        />
                      </div>
                      <audio
                        :src="item.resourceUrl"
                        controls
                        style="margin-top: 5px"
                      ></audio>
                    </div>

                    <div v-if="item.type === *********" class="envelopes">
                      <div class="top">
                        <img src="@/assets/images/envelops.png" alt="" />
                        <p>{{ item.content }}</p>
                      </div>
                      <div class="bottom">微信红包</div>
                    </div>

                    <div
                      v-if="item.type === *********"
                      class="accounts"
                      :class="{
                        'accounts-in':
                          item.wechatTransfer &&
                          (!!item.wechatTransfer).transferStatusDesc,
                      }"
                    >
                      <div class="top">
                        <img
                          src="@/assets/images/pay_send.png"
                          alt=""
                          v-if="
                            item.wechatTransfer &&
                            (!item.wechatTransfer.transferStatusDesc ||
                              item.wechatTransfer.transferStatusDesc ===
                                '已过期')
                          "
                        />
                        <img
                          src="@/assets/images/pay_check.png"
                          alt=""
                          v-else
                        />
                        <div class="info">
                          <p>￥{{ getCount(item.content) }}</p>
                        </div>
                      </div>
                      <div class="bottom">
                        微信转账
                        <span
                          v-if="
                            item.wechatTransfer &&
                            item.wechatTransfer.description
                          "
                        >
                          ({{ item.wechatTransfer.description }})
                        </span>
                      </div>
                    </div>
                  </div>
                </li>
                <div v-else :key="index" class="system">
                  <img src="@/assets/images/sys_envelop.png" alt="" />
                  <span>{{ item.content }}</span>
                </div>
              </template>
              <audio
                id="audio_msg"
                class="audio"
                :src="resourceUrl.url"
                reload="none"
                @ended="endPlay"
              ></audio>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </Drawer>
</template>

<script>
import { mapGetters } from "vuex";
import { formatDate, timeDifference,desenRulesAll } from "@/libs/util";
import { bindWx, getHistoryUsers, wechatListByPage } from "@/api/workphone";
import artiImg from '@/assets/images/arti.png';

export default {
  props: {
    debtInfo: {
      type: Object,
      default: {},
    },
  },
  created() {
    this.expeditorList();
  },
  data() {
    return {
      originWechatNo: "", // 当前抽屉内容的originWechatNo
      userInfo: {}, // 联系人微信信息
      userList: [], // 联系人的多个微信号列表该
      agentList: [],
      currentAgent: null,
      expeditorName: "",
      conversationId: null,
      conversationAgent: {}, //催员微信信息
      msgList: [], // 会话列表
      wechatPage: 1,
      timer: null,
      resourceUrl: {
        index: null,
        url: "",
      },
      ui: {
        showDrawer: false,
        loadBefore: false,
        loadMoreLoading: false,
        loading: false,
        loadingMsg: false,
      },
      showList: false,
      preview:'',
      options: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      timerange: [
        formatDate(new Date() - 29 * 24 * 3600 * 1000, "yyyy-MM-dd"),
        formatDate(new Date(), "yyyy-MM-dd"),
      ],
    };
  },
  computed: {
    ...mapGetters(["orgId"]),
    requireImg() {
      return function (url) {
        // return new URL(`../../assets/images/` + url, import.meta.url).href
        const path = `../../assets/images/${url}`;
        const modules = import.meta.globEager("../../assets/images/*");
        return modules[path].default;
      };
    },
  },
  methods: {
    // 获取催员列表
    async expeditorList() {
      this.$nextTick(() => {
        this.userInfo = this.debtInfo.wechatInfoList? this.debtInfo.wechatInfoList[0] : {};
      });
      let result = await getHistoryUsers(this.debtInfo.caseId);
      if (result.success) {
        const data = result.data;
        for (const item of data) {
          this.agentList.push(item);
        }
        if (this.agentList.length) {
          this.currentAgent = this.agentList[0].id;
          this.getWxList();
        }
      }
    },
    // 获取催员对应的微信列表
    async getWxList() {
      const res = await bindWx(this.currentAgent);
      this.userList = res.data;
      if (res.data.length > 0) {
        this.conversationId = res.data[0].wechatNo;
        this.conversationAgent.img = res.data[0].img;
        this.expeditorName = res.data[0].nickName
        this.getWechatListByPage();
      } else {
        this.expeditorName = ''
        this.ui.loadBefore = false
      }
    },
    // 切换微信
    hwechat(item) {
      this.userInfo = { ...item };
      this.wechatPage = 1
      if (this.userList.length > 0) {
        this.getWechatListByPage();
      }
      this.showList = false;
    },
    // 催员变化请求微信列表
    hChange(id) {
      this.msgList = [];
      // const ele = this.agentList.find((ele) => ele.id);
      // this.expeditorName = ele.name;
      this.wechatPage = 1
      this.getWxList();
    },
    // 获取对话内容
    async getWechatListByPage() {
      this.ui.loadBefore = false;
      const setDate = {
        limit: 20,
        page: this.wechatPage,
        talker: this.userInfo.originWechatNo || null,
        userId: this.currentAgent,
        wechatNo: this.conversationId,
        startDate: this.timerange[0] + " 00:00:00",
        endDate: this.timerange[1] + " 23:59:59",
      };
      const res = await wechatListByPage(setDate);

      if (!res || !res.data) {
        this.msgList = [];
        this.ui.loadBefore = false;
        return;
      }

      if (res.data.pages > this.wechatPage) {
        this.ui.loadBefore = true;
      }
      res.data.list.forEach((item) => {
        item.time = 2;
      });
      this.msgList = res.data.list || [];
      setTimeout(() => {
        const ele = this.$refs.chartContentSrollWrapper;
        ele.scrollTop = ele.scrollHeight;
      }, 200);
    },
    // 关闭弹窗
    handleClose() {
      this.ui.showDrawer = false;
      setTimeout(() => {
        this.$emit("on-hclose");
      },500)
    },
    hideName(name) {
      return desenRulesAll('name', name)
    },
    formatTime(value) {
      return formatDate(value, "yyyy-MM-dd hh:mm:ss");
    },
    // 点击微信列表
    changeConversation(item) {
      this.conversationId = item.wechatNo;
      this.conversationAgent.originWechatNo = item.wechatNo;
      this.conversationAgent.img = item.img;
      this.wechatPage = 1;
      this.expeditorName = item.nickName
      this.getWechatListByPage();
    },
    // 通过日期获取对话
    changeDate(value) {
      // 加载聊天内容
      if (timeDifference(value[0], value[1])) {
        return this.$Message.error("查询跨度不能超过一个月");
      } else {
        this.timerange = [value[0], value[1]];
        this.getWechatListByPage();
      }
    },
    getCount(str) {
      let regex = /转账(\d+(\.\d+)?)元/;
      let match = str.match(regex);
      let amount;
      if (match) {
        amount = match[1];
      }
      return amount;
    },
    playVideo(item, index) {
      clearInterval(this.timer);
      const audio = document.getElementById("audio_msg");

      for (const key in this.msgList) {
        if (this.msgList[key].resourceLength) {
          this.msgList[key].time = 2;
        }
      }

      if (audio.paused) {
        if (this.resourceUrl.index === index) {
          audio.play();
          this.timer = setInterval(() => {
            if (this.msgList[index].time < 2) {
              this.msgList[index].time += 1;
            } else {
              this.msgList[index].time = 0;
            }
          }, 400);
        }
      } else {
        audio.pause();
      }

      if (this.resourceUrl.index !== index) {
        this.resourceUrl.index = index;
        this.resourceUrl.url = item.resourceUrl;
        audio.load();

        this.$nextTick(() => {
          audio.play();
          this.timer = setInterval(() => {
            if (this.msgList[index].time < 2) {
              this.msgList[index].time += 1;
            } else {
              this.msgList[index].time = 0;
            }
          }, 400);
        });
      }
    },
    endPlay() {
      clearInterval(this.timer);

      for (const key in this.msgList) {
        if (this.msgList[key].resourceLength) {
          this.msgList[key].time = 2;
        }
      }
      this.resourceUrl.url = "";
      this.resourceUrl.index = null;
    },
    async loadBefore() {
      this.wechatPage += 1;
      this.ui.loadBefore = false;
      this.ui.loadingMsg = true;
      const setDate = {
        limit: 20,
        page: this.wechatPage,
        talker: this.userInfo.originWechatNo || null,
        userId: this.currentAgent,
        wechatNo: this.conversationId,
        startDate: this.timerange[0] + " 00:00:00",
        endDate: this.timerange[1] + " 23:59:59",
      };
      const res = await wechatListByPage(setDate);
      const message = this.msgList[0];
      let arr = res.data.list.reverse();
      arr.forEach((item) => {
        item.time = 2
        this.msgList.unshift(item);
      });
      if (res.data.pages > this.wechatPage) {
        this.ui.loadBefore = true;
      }
      setTimeout(() => {
        const dialog = document.getElementsByClassName("cons")[0];
        const target = document.getElementById(`txt-${message.wechatId}`);
        if (target) {
          dialog.scrollTo(null, target.offsetTop - 130);
        }
      }, 0);
    },
    showPreview(item) {
      this.preview = item.resourceUrl
    },
    hidePreview() {
      this.preview = ''
    }
  },
};
</script>

<style lang="scss">
.user-drop {
  width: 210px;
  padding: 0;
  .el-dropdown__list {
    padding: 0;
    margin: 0;
    > .el-dropdown-menu {
      padding: 4px 0;
      > .el-dropdown-menu__item {
        padding: 0;
        > .user-list {
          height: 38px;
          display: flex;
          align-items: center;
          padding: 6px 24px 6px 12px;
          > .left {
            margin-right: 8px;
            > .img {
              width: 26px;
              height: 26px;
              border-radius: 4px;
              display: inline-block;
              margin-top: 12px;
            }
          }
          > .right {
            > .name {
              font-size: 12px;
              color: #282828;
              line-height: 17px;
              margin: 0;
              margin-bottom: 3px;
            }
            > .wx {
              font-size: 12px;
              color: #636c78;
              line-height: 17px;
              margin: 0;
            }
            > .no-wx {
              font-size: 12px;
              color: #636c78;
              line-height: 17px;
              margin: 0;
              margin-bottom: 4px;
            }
          }
          &:hover {
            background-color: #f3f4f8;
          }
        }
        > .active-user {
          background-color: #f3f4f8;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
::v-deep .ivu-drawer-wrap {
  z-index: 10000;
}
::v-deep .ivu-drawer-mask {
  z-index: 10000;
}
::v-deep .ivu-drawer-body {
  padding: 0;
}
.drawer-content {
  width: 100%;
  height: 100%;
  background-color: #f2f4f7;
}
.header {
  height: 45px;
  background-color: #fff;
  box-shadow: 0px 6px 6px 0px rgba(0, 0, 0, 0);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #dcdee3;
  margin-left: 0;
  margin-bottom: 0;
  .header-l {
    display: flex;
    align-items: center;
    > .quit {
      margin-left: 24px;
      margin-right: 10px;
      font-size: 14px;
      font-weight: 500;
      color: #282828;
      line-height: 21px;
      cursor: pointer;
    }
    > .infos {
      display: flex;
      align-items: center;
      position: relative;
      > .el-icon-arrow-left {
        font-size: 16px;
        color: #282828;
        font-weight: 600;
        cursor: pointer;
        margin-right: 12px;
      }
      .wx-name {
        height: 48px;
        padding-top: 16.5px;
        flex-shrink: 0;
        display: inline-block;
        max-width: 133px;
        font-size: 14px;
        color: #282828;
        font-weight: 600;
        margin-right: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        word-wrap: break-word;
        white-space: nowrap;
        word-break: break-all;
      }
      > .info {
        line-height: 17px;
        margin-right: 12px;
        white-space: nowrap;
        > span {
          display: inline-block;
          border-left: 1px solid #dcdee3;
          padding: 0 12px;
          font-size: 12px;
          color: #282828;
          line-height: 12px;
          > span {
            color: #282828;
          }
        }
        > .wxPhone {
          background-color: #f2f4f7;
          border-radius: 16px;
          height: 32px;
          line-height: 32px;
          padding-left: 3px;
          > .wechatSignal {
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #636c78;
            line-height: 12px;
            border-left: 1px solid #dcdee3;
            padding-left: 10px;
          }
          .toggle {
            height: 32px;
            // margin-left: 13px;
            color: #282828;
            &:hover {
              cursor: pointer;
              color: #ff4f1f;
            }
          }
           ul {
            z-index: 999;
            box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            padding: 4px 0;
            background-color: #fff;
            > li {
              display: flex;
              align-items: center;
              // padding-top: 10px;
              padding-left: 12px;
              width: 166px;
              height: 52px;
              font-size: 12px;
              background-color: #fff;
              cursor: pointer;
              > img {
                width: 26px;
                height: 26px;
                border-radius: 50%;
              }
              > .wxName {
                margin-left: 6px;
                > p {
                  width: 100px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
                > .hColor {
                  color: #ff4f1f;
                }
              }
              &:hover {
                background-color: #f3f4f8;
              }
            }
          }
        }
      }
    }
    > .con-list {
      width: 536px;
      height: 42px;
      border-radius: 21px;
      background-color: #f2f4f7;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px 0 24px;
      > .left {
        display: flex;
        align-items: center;
        > .img {
          width: 26px;
          height: 26px;
          border-radius: 4px;
          background-color: #000;
          margin-right: 10px;
        }
        .name {
          font-size: 14px;
          color: #282828;
          line-height: 21px;
          font-weight: 600;
          white-space: nowrap;
          margin-right: 12px;
          display: inline-block;
          max-width: 61px;
          overflow: hidden;
          text-overflow: ellipsis;
          word-wrap: break-word;
          white-space: nowrap;
          word-break: break-all;
        }
        > .info {
          font-size: 12px;
          color: #636c78;
          line-height: 17px;
          white-space: nowrap;
          display: flex;
          align-items: center;
          span {
            display: inline-block;
            border-left: 1px solid #dcdee3;
            padding: 0 12px;
            line-height: 12px;
          }
          .wx {
            display: inline-block;
            max-width: 119px;
            overflow: hidden;
            text-overflow: ellipsis;
            word-wrap: break-word;
            white-space: nowrap;
            word-break: break-all;
            line-height: 12px;
          }
        }
      }
      > .right {
        .switch {
          white-space: nowrap;
          cursor: pointer;
          > i {
            font-size: 12px;
            color: #444;
            margin-right: 4px;
          }
          > span {
            font-size: 12px;
            color: #282828;
          }
          &:hover {
            > i {
              color: #ff4f1f;
            }
            > span {
              color: #ff4f1f;
            }
          }
        }
      }
    }
  }
}
.content {
  height: calc(100% - 48px);
  background-color: #f2f4f7;
  display: flex;
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.06);
  > .agents {
    flex-shrink: 0;
    width: 364px;
    height: 100%;
    padding-top: 16px;
    background-color: #fff;
    box-shadow: 1px 6px 6px 0px rgba(0, 0, 0, 0.02);
    // border-radius: 0px 0px 6px 6px;
    > .correlation {
      height: 45px;
      padding-left: 255px;
      padding-top: 16px;
      display: flex;
      > img {
        vertical-align: baseline;
      }
      > span {
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #636c78;
        line-height: 17px;
        vertical-align: baseline;
      }
    }
    > .headers {
      height: 40px;
      font-size: 13px;
      // color: #282828;
      font-weight: 600;
      line-height: 18px;
      border-radius: 3px 3px 0px 0px;
      background: linear-gradient(
        135deg,
        #eee3ec 0%,
        #e9f3f9 14%,
        #e9eefb 39%,
        #eef1f6 49%,
        #ebf3f8 78%,
        #eff2f7 100%
      );
      display: flex;
      align-items: center;
      padding: 0 20px;
    }
    > .list {
      height: calc(100% - 77px);
      list-style-type: none;
      margin: 0;
      overflow: auto;
      overflow-y: auto;
      > li {
        display: flex;
        align-items: center;
        position: relative;
        width: 100%;
        height: 72px;
        padding: 16px 12px;
        cursor: pointer;
        > .daybox {
          position: absolute;
          right: 16px;
          font-size: 12px;
          color: #8d959f;
        }
        > .top {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          > img {
            width: 16px;
            height: 16px;
            margin-right: 12px;
          }
          > span {
            font-size: 14px;
            color: #282828;
            font-weight: 600;
            line-height: 20px;
          }
        }
        > .middle {
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          > img {
            width: 16px;
            height: 16px;
            margin-right: 12px;
          }
          > span {
            font-size: 12px;
            color: #636c78;
            line-height: 17px;
            margin-right: 6px;
            display: inline-block;
            width: 102px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            &:last-child {
              margin-right: 0;
            }
          }
          > .wx {
            width: 140px;
          }
        }
        > .bottom {
          padding-left: 26px;
          > span {
            font-size: 12px;
            color: #636c78;
            line-height: 17px;
          }
        }
        &:hover {
          background-color: #f3f4f8;
        }
      }
      > .active-agent {
        background-color: #EBEEF3;
      }
    }
    > .empty {
      margin: 64px 0 16px;
      text-align: center;
      > img {
        width: 129px;
      }
      > p {
        margin: 0;
        font-size: 12px;
        color: #8d959f;
      }
    }
  }
  > .chat-container {
    width: calc(100% - 70px);
    background-color: #f7f7f7;
    border-left: 1px solid #dcdee2;
    border-radius: 0 0 0 6px;
    > .ctent-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 48px;
      border-bottom: 1px solid #dcdee3;
      padding-right: 16px;
      background-color: #f2f4f7;
      > span {
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #282828;
        margin-left: 16px;
      }
    }
    > .top {
      border-bottom: 1px solid #dcdee2;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;
      > .left {
        display: flex;
        align-items: center;
        > .img {
          width: 40px;
          height: 40px;
          background-color: #000;
          border-radius: 4px;
          margin-right: 12px;
        }
        > .name {
          font-size: 13px;
          color: #282828;
          line-height: 18px;
          font-weight: 600;
        }
      }
    }
    > .cons {
      height: calc(100% - 45px);
      overflow: hidden;
      overflow-y: auto;
      padding: 12px 24px 0 16px;
      background-color: #f2f4f7;
      > .no-more {
        text-align: center;
        > a {
          color: #8d959f;
        }
      }
      > .msg-list {
        list-style-type: none;
        padding-left: 0;
        > li {
          display: flex;
          align-items: flex-start;
          margin-bottom: 28px;
          &:last-child {
            margin-bottom: 0;
          }
          > .img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
          }
          > .avator {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            // background-color: #c0c4cc;
            display: flex;
            align-items: center;
            justify-content: center;
            > img {
              width: 40px;
              height: 40px;
              border-radius: 50%;
            }
          }
          > .info {
            position: relative;
            p {
              margin: 0;
            }
            > .time {
              font-size: 12px;
              color: #8d959f;
              line-height: 17px;
              margin-bottom: 4px;
            }
            > .txt {
              display: inline-block;
              max-width: 400px;
              padding: 8px 10px;
              font-size: 12px;
              color: #282828;
              border-radius: 4px;
            }
            > .img {
              > img {
                width:150px
              }
              > #previewContainer {
                // display: none;
                position: fixed;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                background-color: rgba(0, 0, 0, 0.2);
                z-index: 7000;
                text-align: center; 
                > #largePreview {
                  position: relative;
                  z-index: 9999;
                  width: 300px;
                  height: auto;
                  margin-top: 5%;
                  border-radius: 5px;
                }
              }
            }
            > .audios {
              display: flex;
              align-items: flex-start;
              > .plays {
                max-width: 400px;
                padding: 8px 10px;
                border-radius: 4px;
                font-size: 12px;
                color: #282828;
                line-height: 1;
                > img {
                  width: 13px;
                  height: 16px;
                }
                > .trans-content {
                  margin-top: 2px;
                  padding-top: 4px;
                  > div {
                    color: #0f170a;
                    text-align: left;
                    line-height: 17px;
                  }
                  > .empty {
                    display: flex;
                    align-items: center;
                    > i {
                      font-size: 16px;
                      margin-right: 8px;
                    }
                    > span {
                      font-size: 12px;
                    }
                  }
                }
              }
              > .duration {
                font-size: 12px;
                color: #a9a5a5;
                padding: 8px 0;
              }
            }
            > .hAudio {
              padding: 0 5px;
              border-radius: 4px;
            }
            > .envelopes {
              background-color: #ff901b;
              width: 223px;
              padding: 8px;
              border-radius: 4px;
              > .top {
                width: 100%;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                border-bottom: 1px solid rgba(255, 255, 255, 0.59);
                padding-bottom: 6px;
                margin-bottom: 6px;
                > img {
                  width: 31px;
                  height: 36px;
                  margin-right: 8px;
                  margin-left: 7px;
                }
                > p {
                  margin: 0;
                  font-size: 14px;
                  color: #fff;
                  line-height: 20px;
                }
              }
              > .bottom {
                font-size: 12px;
                color: #fff;
                line-height: 17px;
                display: flex;
                justify-content: flex-start;
                padding: 0 7px;
              }
            }
            > .accounts {
              width: 223px;
              padding: 8px;
              background-color: #ff901b;
              &::before {
                position: absolute;
                width: 10px;
                height: 10px;
                content: " ";
                transform: rotate(45deg);
                background: #ff901b;
                box-sizing: border-box;
              }
              > .top {
                width: 100%;
                display: flex;
                justify-content: flex-start;
                border-bottom: 1px solid rgba(255, 255, 255, 0.59);
                > img {
                  width: 32px;
                  height: 32px;
                  transform: rotate(360deg);
                  margin-right: 8px;
                  margin-left: 8px;
                  margin-bottom: 8px;
                }
                > .info {
                  font-size: 12px;
                  color: #fff;
                  line-height: 17px;
                  margin-bottom: 4px;
                  > p {
                    margin: 0;
                    text-align: left;
                    &:first-child {
                      font-size: 16px;
                    }
                    &:last-child {
                      margin-left: 2px;
                    }
                  }
                }
              }
              > .bottom {
                width: 100%;
                display: flex;
                justify-content: flex-start;
                font-size: 12px;
                color: #fff;
                line-height: 17px;
                padding: 8px 8px 0;
              }
            }
            > .accounts-in {
              background-color: #ffd4b5;
              &::before {
                background-color: #ffd4b5;
              }
            }
          }
        }
        > .left {
          > .img {
            margin-right: 8px;
            border-radius: 50%;
          }
          > .avator {
            margin-right: 12px;
          }
          > .info {
            > .txt {
              background-color: #fff;
              &::before {
                position: absolute;
                top: 30px;
                left: -4px;
                width: 10px;
                height: 10px;
                content: " ";
                transform: rotate(45deg);
                background: #fff;
                box-sizing: border-box;
              }
            }
            > .audios {
              flex-direction: row;
              &::before {
                position: absolute;
                top: 30px;
                left: -4px;
                width: 10px;
                height: 10px;
                content: " ";
                transform: rotate(45deg);
                background: #fff;
                box-sizing: border-box;
              }
              > .plays {
                background-color: #fff;
                > img {
                  margin-right: 122px;
                }
                > .trans-content {
                  border-top: 1px solid #dedede;
                  > .empty {
                    color: #a9a5a5;
                  }
                }
              }
              > .duration {
                margin-left: 4px;
              }
            }
            > .envelopes {
              &::before {
                position: absolute;
                left: -2px;
                width: 10px;
                height: 10px;
                content: " ";
                transform: rotate(45deg);
                background: #ff901b;
                box-sizing: border-box;
              }
            }
            > .accounts {
              &::before {
                left: -2px;
              }
            }
            > .accounts-in {
              &::before {
                background-color: #ffd4b5;
              }
            }
          }
        }
        > .right {
          flex-direction: row-reverse;
          > .img {
            margin-left: 8px;
            border-radius: 50%;
          }
          > .avator {
            margin-left: 12px;
          }
          > .info {
            text-align: right;
            > .time {
              text-align: right;
            }
            > .txt {
              text-align: left;
              background-color: #95ec69;
              &::after {
                position: absolute;
                top: 30px;
                right: -4px;
                width: 10px;
                height: 10px;
                content: " ";
                transform: rotate(45deg);
                background: #95ec69;
                box-sizing: border-box;
              }
            }
            > .audios {
              flex-direction: row-reverse;
              &::after {
                position: absolute;
                top: 30px;
                right: -4px;
                width: 10px;
                height: 10px;
                content: " ";
                transform: rotate(45deg);
                background: #95ec69;
                box-sizing: border-box;
              }
              > .plays {
                text-align: right;
                background-color: #95ec69;
                > img {
                  transform: rotate(180deg);
                  margin-left: 122px;
                }
                > .trans-content {
                  border-top: 1px solid #78c74f;
                  > .empty {
                    color: #5a9f36;
                  }
                }
              }
              > .duration {
                margin-right: 4px;
              }
            }
            > .envelopes {
              &::after {
                position: absolute;
                top: 30px;
                right: -2px;
                width: 10px;
                height: 10px;
                content: " ";
                transform: rotate(45deg);
                background: #ff901b;
                box-sizing: border-box;
              }
            }
            > .accounts {
              &::after {
                top: 30px;
                right: -2px;
              }
            }
            > .accounts-in {
              &::after {
                background-color: #ffd4b5;
              }
            }
          }
        }
        > .system {
          text-align: center;
          margin-top: 8px;
          margin-bottom: 8px;
          > img {
            width: 13px;
            height: 15px;
            margin-right: 4px;
            margin-bottom: -2px;
          }
          > span {
            font-size: 12px;
            color: #b7b4b4;
            line-height: 17px;
          }
        }
        > .audio {
          display: none;
        }
      }
      > .load {
        text-align: center;
        color: #61affe;
        font-size: 12px;
        > a {
          cursor: pointer;
        }
      }
    }
  }
}
</style>