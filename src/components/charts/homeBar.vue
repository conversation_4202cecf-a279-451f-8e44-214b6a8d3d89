<template>
  <div class="bar">
    <div ref="dom" class="charts chart-bar"></div>
    <ul class="yXis">
      <li v-for="(item, index) in seriesData" :key="index">
        {{ item }}
      </li>
    </ul>
  </div>
</template>

<script>
import { markRaw } from 'vue'
import echarts from 'echarts'
import tdTheme from './theme.json'
import { on, off } from '@/libs/tools'
echarts.registerTheme('tdTheme', tdTheme)
import { formatNum } from '@/libs/util'

export default {
  props: {
    value: {
      type: Object,
      default() {
        return {}
      }
    },
    seriesData: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      dom: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.inits()
    })
  },
  beforeUnmount() {
    off(window, 'resize', this.resize)
    this.dom.clear()
  },
  methods: {
    resize() {
      this.dom.resize()
    },
    inits() {
      const option = {
        grid: {
          top: 0,
          left: 0,
          right: 55,
          bottom: 25,
          containLabel: true
        },
        xAxis: {
          show: false,
          type: 'value'
        },
        yAxis: {
          show: false,
          type: 'category'
        },
        series: [
          {
            name: '案件状态',
            type: 'bar',
            silent: true,
            data: Object.values(this.value).reverse(),
            barCategoryGap: '70%',
            label: {
              normal: {
                show: true,
                position: 'right',
                color: '#515A6E',
                formatter(params) {
                  return formatNum(params.value)
                }
              }
            },
            itemStyle: {
              normal: {
                color: (params) => {
                  const colorList = ['#55CAF4', '#55CAF4', '#F9C62A', '#F39D47']
                  return colorList[params.dataIndex]
                },
                borderWidth: 10
              }
            }
          }
        ]
      }
      this.dom = markRaw(echarts.init(this.$refs.dom, 'tdTheme'))
      this.dom.setOption(option)
      on(window, 'resize', this.resize)
    }
  }
}
</script>

<style lang="less" scoped>
.bar {
    position: relative;
}
.charts.chart-bar {
    height: 290px;
    margin-right: 5%;
}
.yXis {
    list-style-type: none;
    position: absolute;
    top: 0;
    li {
        height: 60px;
        color: #929AAB;
        font-size: 12px;
        line-height: 10px;
    }
}
</style>
