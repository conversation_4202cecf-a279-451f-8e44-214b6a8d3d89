<template>
  <div class="header-bar">
    <div class="agentBtns">
      <slot name="agentbtns"></slot>
    </div>
    <sider-trigger v-show="false" :collapsed="collapsed" icon="md-menu" @on-change="handleCollpasedChange"></sider-trigger>
    <custom-bread-crumb style="margin-left: 0px;" :list="breadCrumbList"></custom-bread-crumb>

    <div class="custom-content-con">
      <slot></slot>
    </div>
  </div>
</template>
<script>
import siderTrigger from './sider-trigger'
import customBreadCrumb from './custom-bread-crumb'
import Fullscreen from '../fullscreen'
import { mapGetters } from 'vuex'

export default {
  name: 'HeaderBar',
  components: {
    siderTrigger,
    customBreadCrumb,
    Fullscreen
  },
  props: {
    collapsed: Boolean
  },
  data() {
    return {
      version: anmiVersion
    }
  },
  computed: {
    ...mapGetters([
      'urgeRefund'
    ]),
    breadCrumbList() {
      return this.$store.state.app.breadCrumbList.slice(this.$store.state.app.breadCrumbList.length - 1)
    }
  },
  methods: {
    handleCollpasedChange(state) {
      this.$emit('on-coll-change', state)
    }
  }
}
</script>

<style lang="less">
@import './header-bar.less';

.agentBtns{
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
}
</style>
