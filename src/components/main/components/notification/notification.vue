<template>
  <div class="container">
    <Poptip v-model="ui.showPoptip" popper-class="notice-poptip" placement="bottom-end" :offset="33" transfer padding="0" @on-popper-show="getMsg">
      <Icon type="ios-notifications" class="icon white-icon" />
      <div v-if="hasUnreadNotice || isHasNotice" class="unread"></div>
      <template #content>
        <div class="contains">
          <ul class="title">
            <li :class="activeIndex===1?'active':''" @click="getMessage(1)">系统消息</li>
            <li :class="activeIndex===2?'active':''" @click="getMessage(2)">
              <span v-if="hasUnreadreceive" class="unread"></span>
              即时消息
            </li>
          </ul>

          <template v-if="activeIndex===1 && list.length">
            <template v-if="list.length">
              <ul class="contents">
                <li v-for="item in list" :key="item.id" @click="checkAll">
                  <Tooltip content="查看详细信息" transfer :delay="2000">
                    <div class="item">
                      <p class="item-title">
                        {{ item.title }}
                      </p>
                      <p class="item-con">
                        {{ item.summary }}
                      </p>
                    </div>
                    <p class="time">
                      {{ formatDate(item.releaseTime) }}
                    </p>
                  </Tooltip>
                </li>
              </ul>
              <div class="check-all">
                <a @click="checkAll">
                  查看所有消息 >>
                </a>
              </div>
            </template>
            <div v-else class="empty-container">
              <img class="empty" src="@/assets/images/empty.png" alt="">
            </div>
          </template>
          <template v-else-if="activeIndex===2&&list.length">
            <ul class="immediateContents">
              <li v-for="(item,index) in list" :key="item.senId" @click="checkAll(item,index)">
                <Tooltip content="查看详细信息" transfer style="width:100%">
                  <div class="listStyle">
                    <div class="img-bor" :style="randomRgb(index%5,0)"><div class="img" :style="randomRgb(index%5,1)">{{ item.fromUserName.substr(0,1)||'无' }}</div></div>
                    <div class="content">
                      <div class="active"><span v-if="!(item.readed==1)" class="unread"></span><span class="name">{{ item.fromUserName }}</span><span class="theme">{{ item.subject }}</span></div>
                      <div>{{ formatDate(item.createTime) }}</div>
                    </div>
                  </div>
                </Tooltip>
              </li>
            </ul>
            <div class="check-all">
              <a @click="checkAll">
                查看所有消息 >>
              </a>
            </div>
          </template>
          <div v-else class="empty-container">
            <img class="empty" src="@/assets/images/empty.png" alt="">
          </div>
        </div>
      </template>
    </Poptip>
    <Modal
      v-model="ui.showMessageModal"
      :footer-hide="true"
      mask-closable
      :closable="false"
      :width="856"
      :padding="0"
    >
      <template #header>
        <div class="modals-message-title">
          <span class="title">
            即时消息
          </span>
          <div>
            <span class="all-read" @click="readAllFn">全部已读</span>
            <Icon type="md-close" class="close" @click="closeModal" />
          </div>
        </div>
      </template>
      <div v-if="ui.showMessageModal" ref="message_collapse_container" class="modal-message-container">
        <Collapse v-model="key" simple accordion @on-change="messageChangeCollapse">
          <Panel v-for="(item, index) in list" :key="index" class="panel" hide-arrow :class="key.includes(String(index)) ? 'active-class' : ''">
            <div class="message-title title-info">
              <div class="message-info">
                <div class="listStyle active">
                  <div class="img-bor" :style="randomRgb(index%5,0)"><div class="img" :style="randomRgb(index%5,1)">{{ item.fromUserName.substr(0,1)||'无' }}</div></div>
                  <div class="content">
                    <div :class="item.readed?'':'active'"><span v-if="!(item.readed==1)" class="unread"></span><span class="name">{{ item.fromUserName }}</span><span class="theme">{{ item.subject }}</span></div>
                    <div>{{ formatDate(item.createTime) }}</div>
                  </div>
                </div>
                <Icon :type="key.includes(String(index))?'ios-arrow-up':'ios-arrow-down'" class="arrow" @click="seeDetailFn(item)" />
              </div>
            </div>
            <template #content>
              <div class="content">
                <div class="con" v-html="item.content"></div>
              </div>
            </template>
          </Panel>
        </Collapse>
        <Spin v-if="ui.spinShow" fix></Spin>
      </div>

      <div class="page-content">
        <Page class="pages table-page" :total="total" :current="page" size="small" show-total @on-change="getMsgList" />
      </div>
    </Modal>

    <Modal
      v-model="ui.showModal"
      :footer-hide="true"
      :mask-closable="false"
      :closable="false"
      :width="856"
      :padding="0"
    >
      <template v-if="activeIndex===1" #header>
        <div class="modals-title">
          <span class="title">
            系统消息
          </span>
          <Icon type="md-close" class="close" @click="closeModal" />
        </div>
      </template>
      <div v-if="activeIndex===1">
        <div class="modal-container">
          <Collapse v-model="key" simple accordion @on-change="changeCollapse">
            <Panel v-for="(item, index) in list" :key="index" class="panel" hide-arrow :class="key.includes(String(index)) ? 'active-class' : ''">
              <div class="title-info">
                <div class="img">
                  <img :src="item.image || baseUrl" alt="">
                </div>
                <div class="info">
                  <div class="top">
                    <p class="info-title">
                      {{ item.title }}
                    </p>
                    <p class="info-summary" :class="(key.includes(String(index))) ? 'show-more' : 'show-lines'">
                      {{ item.summary }}
                    </p>
                    <p class="info-time">
                      {{ formatDate(item.releaseTime) }}
                    </p>
                  </div>
                  <Icon v-if="!item.url" :type="key.includes(String(index))?'ios-arrow-up':'ios-arrow-down'" class="arrow" />
                  <Icon v-else type="ios-link" class="arrow" />
                </div>
              </div>
              <template #content>
                <div v-if="item.content" class="content">
                  <div class="con" v-html="item.content"></div>
                </div>
              </template>
            </Panel>
          </Collapse>
          <Spin v-if="ui.spinShow" fix></Spin>
        </div>

        <div class="page-content">
          <Page class="pages table-page" :total="total" :current="page" size="small" show-total @on-change="getMsgList" />
        </div>
      </div>
      <div>
      </div>
    </Modal>
  </div>
</template>

<script>
import { getNoticeMsg } from '@/api/org'
import { formatDate } from '@/libs/util'
import { mapGetters, mapMutations } from 'vuex'
import { messageReadAll, receiveList, messageDetail } from '@/api/innerMessage'
import baseUrl from '@/assets/images/info.png'

export default {
  data() {
    return {
      key: [],
      hasUnread: false,
      list: [],
      baseUrl: baseUrl,
      page: 1,
      total: 0,
      ui: {
        showPoptip: false,
        showModal: false,
        spinShow: false,
        showMessageModal: false
      },
      activeIndex: 1,
      hasUnreadreceive: false,
      hasUnreadNotice: false
    }
  },
  computed: {
    ...mapGetters([
      'currentRole',
      'isHasNotice',
      'isHasNoticeList'
    ])
  },
  created() {
    this.getMsg()
  },
  methods: {
    ...mapMutations([
      'setHasNotice'
    ]),
    formatDate(val) {
      return formatDate(val, 'yyyy年MM月dd日 hh:mm:ss')
    },
    async getMsg() {
      const hasUnreadNotice = false
      let list1 = []
      let list2 = []
      await getNoticeMsg({
        page: 1,
        limit: 10
      }).then(res => {
        if (res.success) {
          list1 = res.data.list || []
          if (list1 && list1.length > 0) {
            const now = new Date(new Date().toLocaleDateString()).getTime()
            const data = new Date(this.list[0]?.releaseTime).getTime()
            this.hasUnreadNotice = data >= now
          } else {
            this.hasUnreadNotice = false
          }
        }
      })
      await receiveList({
        page: 1,
        limit: 10
      }).then(res => {
        if (res.success) {
          list2 = res.data.list
          const index = list2.findIndex((item) => {
            return item.readed === 0
          })
          if (index > -1) {
            this.hasUnreadreceive = true
          } else {
            this.hasUnreadreceive = false
          }
        }
      })
      if (this.activeIndex === 1) {
        this.list = list1
      } else {
        this.list = list2
      }
      this.setHasNotice(this.hasUnreadreceive)
    },
    checkAll(message, index) {
      this.total = 0
      this.page = 1
      this.ui.showPoptip = false
      this.key = []

      this.getMsgList(1)
      if (this.activeIndex === 1) {
        this.ui.showModal = true
      } else if (this.activeIndex === 2) {
        let route
        if (this.isHasNoticeList) {
          if (message.hasOwnProperty('sendId')) {
            route = {
              name: 'message_inbox',
              params: {
                sendId: message.sendId,
                sendDetailId: message.sendDetailId
              }
            }
          } else {
            route = {
              name: 'message_inbox'
            }
          }
          this.$router.push(route)
        } else {
          if (message.hasOwnProperty('sendId')) {
            setTimeout(() => {
              const scrollBox = this.$refs['message_collapse_container']
              const top = (index - 1) * 78
              this.$nextTick(_ => {
                scrollBox.scrollTo({
                  top: top,
                  behavior: 'smooth'
                })
              })
            }, 10)
            this.key.push(index)
            this.seeDetailFn(message)
          }
          this.ui.showMessageModal = true
        }
      }
    },
    checkAllFn(message) {
      this.activeIndex = 2
      this.getMsgList(1)
      if (message.hasOwnProperty('sendId')) {
        setTimeout(() => {
          const index = this.list.findIndex((item) => {
            return item.sendId === message.sendId
          })
          this.key.push(index)
          this.seeDetailFn(message)
        }, 500)
      }

      this.ui.showMessageModal = true
    },
    getMsgList(num) {
      this.page = num

      this.ui.spinShow = true
      const fun = this.activeIndex === 1 ? getNoticeMsg : receiveList
      fun({
        page: this.page,
        limit: 10
      }).then(res => {
        if (res.success) {
          this.list = res.data.list || []
          this.total = res.data.total
        }
      }).finally(() => {
        this.ui.spinShow = false
      })
    },
    closeModal() {
      if (this.activeIndex === 1) {
        this.ui.showModal = false
      } else if (this.activeIndex === 2) {
        this.ui.showMessageModal = false
      }
      this.total = 0
      this.page = 1
      this.key = []
    },
    changeCollapse(value) {
      if (!value.length) return
      const index = Number(value[0])
      const item = this.list[index]
      if (item.url) {
        window.open(item.url, '_blank')
      }
    },
    messageChangeCollapse(value) {
      if (!value.length) return
      const index = Number(value[0])
      const item = this.list[index]
      this.seeDetailFn(item)
    },
    getMessage(index) {
      this.activeIndex = index
      this.getMsgList(1)
    },
    randomRgb(randomColor, flag) {
      const colors = [['#6616D4', '#ECE7F9'], ['#E74A0E', '#FDE7D9'], ['#D79512', '#F1E1B3'], ['#0FC5DF', '#BDF4FF'], ['#19C92C', '#E7F9E9']]
      if (!flag) {
        return { border: `1px solid ${colors[randomColor][1]}` }
      } else {
        return {
          backgroundColor: colors[randomColor][1],
          color: colors[randomColor][0]
        }
      }
    },
    readAllFn() {
      // 全部已读
      messageReadAll().then((res) => {
        if (res.status === 200) {
          this.setHasNotice(false)
          this.list = this.list.map((item) => {
            return { ...item, readed: 1 }
          })
        }
      })
    },
    seeDetailFn(row) {
      const data = {
        sendId: row.sendId,
        sendDetailId: row.sendDetailId
      }
      messageDetail(data).then((res) => {
        if (res.status === 200) {
          this.list = this.list.map((item) => {
            if (item.sendId === row.sendId) {
              return { ...item, readed: 1 }
            } else {
              return item
            }
          })
          const index = this.list.findIndex((item) => {
            return item.readed === 0
          })
          if (index > -1) {
            this.setHasNotice(true)
            this.hasUnreadreceive = true
          } else {
            this.hasUnreadreceive = false
            this.setHasNotice(false)
          }
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.container {
    width: 20px;
    height: 100%;
    position: relative;

    .icon {
        position: absolute;
        top: -14px;
        font-size: 20px;
        cursor: pointer;
    }
    .unread {
        width: 5px;
        height: 5px;
        background-color: #FF1A2E;
        border-radius: 50%;
        position: absolute;
        right: -18px;
        top: -12px;
    }
    .black-icon {
        color: #999797;
    }
    .white-icon {
      color: rgba(255, 255, 255, 0.65);
    }

    &:hover {
        .icon {
            transform: scale(1.3);
        }
        .unread {
            transform: scale(1.3);
            top: -14px;
            right: -20px;
        }
        .black-icon {
            color: #DADADA;
        }
        .white-icon {
            color: #fff;
        }
    }
    &:active {
        .black-icon {
            color: #999797;
        }
        .white-icon {
            color: #636C78;
        }
    }
}
.contains {
    .title {
      display:flex;
        background-color: #F1F1F1;
        width: 320px;
        align-items:center;
        border-radius: 4px 4px 0 0;
        padding: 15px 16px;
        li {
          list-style:none;
          font-size: 12px;
          font-weight: 500;
          color: #8D959F;
          cursor: pointer;
          &.active{
            font-size: 14px;
            color: #282828;
            font-weight: 600;
          }

        }
        >:nth-child(2) {
          margin-left: 6px;
          position: relative;
          .unread{
            position: absolute;
            top: 0;
            right: -9px;
            display:inline-block;
            width: 5px;
            height: 5px;
            background-color: #FF1A2E;
            border-radius: 50%;
            margin-right: 4px;
          }
        }
    }
    .contents {
        height: 290px;
        overflow: hidden;
        overflow-y: auto;

        li {
            width: 298px;
            margin-left: 6px;
            margin-right: 6px;
            overflow: hidden;
            padding: 10px 7px;
            cursor: pointer;
            list-style:none;

            .item {
                width: 280px;
                overflow: hidden;
                padding-left: 4px;
                .item-title {
                    font-size: 12px;
                    color: #A97E53;
                    font-weight: 600;
                    margin-right: 4px;
                }
                .item-con {
                    display: inline-block;
                    font-size: 12px;
                    color: #282828;
                    font-weight: 400;
                }
            }
            .time {
                font-size: 12px;
                color: #8D959F;
            }

            &:hover {
                background-color: #F3F4F8;
            }
        }
    }
}
.immediateContents{
      list-style-type: none;
      height:290px;
      overflow: hidden;
      overflow-y: auto;
      .listStyle {
        &:hover{
          cursor: pointer;
        background-color: #F3F4F8;
      }
        margin-left:10px;
        width: ~"calc(100% - 10px)";
        height: 58px;
        display: flex;
        justify-content:center;
        align-items:center;
        padding-left:16px;
        .img-bor{
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-right: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          .img{
            width: 27px;
            height: 27px;
            border-radius: 50%;
            color: red;
            font-weight: 600;
            font-size: 12px;
            text-align: center;
            line-height: 27px;
         }
        }

        >.content {
          flex:1;
          padding:10px 10px 10px 0;
          border-top:unset !important;
          position: relative;
          .unread{
            display:inline-block;
            width: 5px;
            height: 5px;
            background-color: #FF1A2E;
            border-radius: 50%;
            margin-right: 4px;
          }
          .theme{
            display: inline-block;
              font-size: 12px;
              font-weight: 400;
              color: #282828;
              line-height: 20px;
              margin-left:10px;
              max-width: 145px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
          }
          >:nth-child(1) {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
            display: flex;
            align-items: center;
            .name{
              color: #282828;
              display: inline-block;
              max-width: 58px;
              font-size: 12px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          >:nth-child(2) {
            font-size: 12px;
            margin-bottom: 4px;
          }
          >:last-child {
            font-size: 12px;
            color: #8D959F;
             white-space: nowrap;
             overflow: hidden;
             text-overflow: ellipsis;
          }
        }
      }
}

.check-all {
    text-align: center;
    padding-top: 9px;
    padding-bottom: 20px;
    a {
        font-size: 12px;
        color: #636C78;
        font-weight: 400;

        &:hover {
            color: #828EA0;
        }
        &:active {
            color: #505862;
        }
    }
}
.empty-container {
    height: 336px;
    padding-top: 40px;
    text-align: center;
    .empty {
        width: 129px;
        height: 121px;
    }
}
.modal-container {
    height: 430px;
    overflow: hidden;
    overflow-y: auto;
    margin: 3px 16px;
    position: relative;
}
.modals-message-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    >.title {
      font-size: 14px;
      font-weight: 600;
      color: #282828;
      line-height: 21px;
    }
    .all-read{
      font-weight: 500;
      color: #FE4F1F;
      line-height: 17px;
      margin-right: 16px;
      cursor: pointer;
      &:hover{
        color: #FF714C;
      }
    }
    .close {
        font-size: 20px;
        color: #636C78;
        cursor: pointer;

        &:hover {
            color: #828EA0;
        }
        &:active {
            color: #505862;
        }
    }
}
.modal-message-container {
    height: 430px;
    overflow: hidden;
    overflow-y: auto;
    padding: 3px 16px;
    position: relative;
    .message-title{
      height: auto;
      display: flex;
      align-items: flex-start;
      height: 78px;
      &:hover{
        background-color: #F3F4F8;
      }
      .active-key{
        background-color: #F3F4F8;
      }
      &.active-title{
        border-bottom: 1px dashed #DCDEE3;
      }
      &.title-info{
        padding: 0 16px 0 0  !important;
      }
    .message-info{
        width: 100%;
        margin-left: 16px;
        display: flex;
        justify-content: space-between;
        >.arrow {
            font-size: 20px;
            color: #8D959F;
            margin-top: 22px;
            margin-left: 16px;
        }
      }

    }
    .listStyle {
        &:hover{
          cursor: pointer;
        // background-color: #F3F4F8;
      }
        width: ~"calc(100% - 10px)";
        display: flex;
        justify-content:center;
        align-items:center;
        .img-bor{
          width:42px;
          height:42px;
          border-radius:50%;
          padding:2px;
          .img{
          width: 36px;
          height: 36px;
          border-radius: 50%;
          color: red;
          font-weight: 600;
          font-size: 14px;
          text-align: center;
          line-height: 38px;
         }
        }

        >.content {
          flex:1;
          padding:14px 10px 15px 16px;
          border-top:unset !important;
          .unread{
            display:inline-block;
              width: 5px;
              height: 5px;
              border-radius:50%;
              background: #FF1A2E;
              margin-bottom: 2px;
              margin-right: 4px;
          }
          .name{
            color: #282828;
            font-size: 12px;
            max-width: 63px;
              display: inline-block;
              white-space: nowrap;
             overflow: hidden;
             text-overflow: ellipsis;
          }
          .theme{
              font-size: 12px;
              font-weight: 400;
              color: #282828;
              line-height: 17px;
              margin-left:10px;
              max-width: 600px;
              display: inline-block;
              white-space: nowrap;
             overflow: hidden;
             text-overflow: ellipsis;
          }
          >:nth-child(1) {
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            margin-bottom: 6px;
          }
          >:nth-child(2) {
            font-size: 12px;
          }
          >:last-child {
            font-size: 12px;
            color: #8D959F;
             white-space: nowrap;
             overflow: hidden;
             text-overflow: ellipsis;
          }
        }
      }
}
.title-info {
    height: auto;
    display: flex;
    align-items: flex-start;
    padding: 15px 16px;
    >.img {
        width: 100px;
        height: 70px;
        > img {
            width: 100px;
            height: 70px;
        }
    }
    >.info {
        width: 100%;
        margin-left: 16px;
        display: flex;
        justify-content: space-between;
        >.top {
            >.info-title {
                font-size: 14px;
                color: #A97E53;
                font-weight: 600;
                line-height: 17px;
            }
            >.info-summary {
                font-size: 12px;
                font-weight: 400;
                color: #282828;
                line-height: 17px;
                margin-top: 4px;
            }
            >.info-time {
                font-size: 12px;
                color: #8D959F;
                line-height: 12px;
                margin-top: 4px;
            }
        }
        >.arrow {
            font-size: 20px;
            color: #8D959F;
            margin-top: 22px;
            margin-left: 16px;
        }
    }
}
.content {
    border-top: 1px dotted #DCDEE3 !important;
    padding: 16px;
    color: #636C78;
}
.panel {
    &:hover {
        .title-info {
            background-color: #F3F4F8;
            .arrow {
                color: #FF4F1F;
            }
        }
        .content {
            background-color: #F3F4F8;
        }
    }
}
.active-class {
    .title-info {
        background-color: #F3F4F8;
    }
    .content {
        background-color: #F3F4F8;
    }
}
.page-content {
    padding-bottom: 18px;
    text-align: right;
    >.pages {
        margin: 18px 22px 0;
    }
}
.show-lines {
    height: 34px;
    overflow: hidden;
}
.show-more {
    min-height: 34px;
}
.modals-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    >.title {
        font-size: 14px;
        font-weight: 600;
        color: #282828;
        line-height: 21px;
    }
    >.close {
        font-size: 20px;
        color: #636C78;
        cursor: pointer;

        &:hover {
            color: #828EA0;
        }
        &:active {
            color: #505862;
        }
    }
}
 :deep(.ivu-collapse-header) {
    height: auto !important;
    padding-left: 0 !important;
}
 :deep(.ivu-modal-body) {
    padding: 0 !important;
}
 :deep(.ivu-collapse) {
    border: none !important;
}
 :deep(.ivu-collapse > .ivu-collapse-item) {
    border-top: none !important;
}
 :deep(.ivu-collapse-content) {
    padding: 0 !important;
}
 :deep(.ivu-collapse-content > .ivu-collapse-content-box) {
    padding-bottom: 0 !important;
}
 :deep(.ivu-collapse-simple > .ivu-collapse-item.ivu-collapse-item-active > .ivu-collapse-header) {
    border-bottom: 0 solid transparent !important;
}
 :deep(.ivu-collapse > .ivu-collapse-item > .ivu-collapse-header) {
    border-bottom: 0 solid transparent !important;
}
.modal-message-container{
  :deep(.ivu-collapse > .ivu-collapse-item > .ivu-collapse-header) {
    line-height:unset;
    border-bottom: 1 solid transparent !important;
}
}
</style>
