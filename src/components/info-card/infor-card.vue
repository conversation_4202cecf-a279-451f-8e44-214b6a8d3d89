<template>
  <Card :shadow="shadow" class="info-card-wrapper" :padding="0">
    <div ref="contentCon" class="content-con">
      <div class="left-area" :style="{background: color, width: leftWidth}">
        <common-icon class="icon" :type="icon" :size="iconSize" color="#fff" />
      </div>
      <div class="right-area" :style="{width: rightWidth}">
        <div>
          <slot></slot>
        </div>
      </div>
    </div>
  </Card>
</template>

<script>
import CommonIcon from '_c/common-icon'
export default {
  name: 'InforCard',
  components: {
    CommonIcon
  },
  props: {
    left: {
      type: [Number, String],
      default: 36
    },
    color: {
      type: String,
      default: '#2d8cf0'
    },
    icon: {
      type: String,
      default: ''
    },
    iconSize: {
      type: Number,
      default: 20
    },
    shadow: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    leftWidth() {
      return Number(this.left) ? `${this.left}%` : this.left
    },
    rightWidth() {
      return Number(this.left) ? `${100 - this.left}%` : `calc(100% - ${this.left})`
    }
  }
}
</script>

<style lang="less">
.common{
  float: left;
  height: 100%;
  display: table;
  text-align: center;
}
.size{
  width: 100%;
  height: 108px;
}
.color {
  background-color: #FAFBFD;
  border: 1px #eee;
}
.middle-center{
  display: table-cell;
  vertical-align: middle;
}
.info-card-wrapper{
  .size;
  .color;
  position: relative;
  border-radius: 4px;
  .ivu-card-body{
    .size;
  }
  .content-con{
    .size;
    position: relative;
    border: 1px solid #dcdee3;
    border-radius: 4px;
    .left-area{
      .common;
      border-radius: 4px 0 0 4px;

      & > .icon{
        .middle-center;
      }
    }
    .right-area{
      .common;
      background-color: #fff;
      border-radius: 0 4px 4px 0;
      & > div{
        .middle-center;
      }
    }
  }
}
</style>
