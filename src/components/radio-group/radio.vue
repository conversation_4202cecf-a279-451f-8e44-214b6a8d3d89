<template>
  <div class="radios-group">
    <div
      v-for="item in props.value"
      class="radio"
      :index="item.value"
      :class="{ 'active-radio': checked === item.value }"
      :style="`width: ${ width }`"
      @click="handleChange(item)"
    >
      <span>{{ item.name }}</span>
      <img src="@/assets/images/check.png" alt="">
    </div>
  </div>
</template>

<script setup>
import { ref, defineEmits, defineProps, onMounted } from 'vue'
const emit = defineEmits(['on-change'])
const props = defineProps({
  value: {
    type: Array,
    default() {
      return []
    }
  },
  active: {
    type: [String, Number],
    default: null
  },
  width: {
    type: String,
    default: 'auto'
  }
})

const checked = ref(null)
onMounted(() => {
  if (props.value.length) {
    if (props.active || props.active === 0) {
      checked.value = props.active
    } else {
      getDefaultCheck()
    }
  }
})
const getDefaultCheck = () => {
  checked.value = null

  let isChecked = false
  for (const item of props.value) {
    if (item.default) {
      checked.value = item.value
      isChecked = true
    }
  }

  if (!isChecked) {
    const firstItem = props.value[0]
    checked.value = firstItem.value
  }
}
const handleChange = (item) => {
  checked.value = item.value
  emit('on-change', checked.value)
}
</script>

<style lang="less" scoped>
.radios-group {
  display: flex;
  align-items: center;
  >.radio {
    // width: 82px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #DCDEE2;
    border-radius: 4px;
    margin-right: 12px;
    cursor: pointer;
    position: relative;
    padding: 0 16px;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      >span {
        color: #FF4F1F;
      }
    }
    >i {
      font-size: 16px;
      color: #F2CB51;
      margin-right: 6px;
    }
    >span {
      line-height: 30px;
      color: #636C78;
    }
    >img {
      position: absolute;
      width: 20px;
      height: 20px;
      right: -1px;
      bottom: -1px;
      display: none;
    }
  }
  >.active-radio {
    border: 1px solid #FF4F1F;
    >i {
      color: #FF4F1F;
    }
    >span {
      color: #FF4F1F;
    }
    >img {
      display: block;
    }
  }
}
</style>
