knife4j.enable=true
knife4j.setting.language=zh-CN
knife4j.basic.enable=false
knife4j.basic.username=Mr.sandman
knife4j.basic.password=27coosVMgM9KbK93LZaNyXEV
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.call-setters-on-nulls=true
mybatis-plus.type-aliases-package=com.sing.service.entity
mybatis-plus.global-config.db-config.id-type=auto
server.port=8081
spring.application.name=sing-service
spring.config.import=apollo://application
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
logging.file.name=/home/<USER>/logs/sing-service/sing-service.log
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level][%X{traceId:-},%X{spanId:-}][%thread] %logger{50}.%method:%line - %msg%n
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%-5level][%X{traceId:-},%X{spanId:-}][%thread] %logger{50}.%method:%line - %msg%n
logging.level.com.netflix.discovery=warn
logging.logback.rollingpolicy.max-file-size=500MB
logging.logback.rollingpolicy.total-size-cap=1500MB
