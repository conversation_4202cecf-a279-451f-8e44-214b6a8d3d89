package com.sing.service.entity.dto;


import com.sing.service.entity.vo.AuthVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.entity.dto.EsignDTO
 * @作者 Mr.sandman
 * @时间 2025/05/21 19:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("e签宝参数")
public class ESignDTO {

    @ApiModelProperty(value = "协议编码",  required = true)
    private String contractCode;

    @ApiModelProperty(value = "流量标识 ",  required = true)
    private String trafficCode;

    @ApiModelProperty(value = "授信人id", required = true)
    private String creditId;

    @ApiModelProperty(value = "联系地址",  required = true)
    private String address;

    @ApiModelProperty(value = "手机号", required = true)
    private String phone;

    @ApiModelProperty(value = "分期次数",  required = true)
    private String stagesNumber;

    @ApiModelProperty(value = "借款金额",  required = true)
    private String amount;

    @ApiModelProperty(value = "借款合同编号",  required = true)
    private String amountNo;

    @ApiModelProperty(value = "个人扣款授权编号",  required = true)
    private String authNo;

    @ApiModelProperty(value = "借记卡账户名称",  required = true)
    private String acctName;

    @ApiModelProperty(value = "借记卡银行卡号",  required = true)
    private String bankCardNo;

    @ApiModelProperty(value = "借记卡开户行",  required = true)
    private String bankName;

    @ApiModelProperty(value = "借款用途",  required = true)
    private String purpose;

    @ApiModelProperty(value = "大写人民币",  required = true)
    private String capitalRmb;

    @ApiModelProperty(value = "小写人民币",  required = true)
    private String lowercaseRmb;

    @ApiModelProperty(value = "合同起始年份",  required = true)
    private String loanActvYear;

    @ApiModelProperty(value = "合同起始月份",  required = true)
    private String loanActvMonth;

    @ApiModelProperty(value = "合同起始日",  required = true)
    private String loanActvDay;

    @ApiModelProperty(value = "合同结束年份",  required = true)
    private String dueYear;

    @ApiModelProperty(value = "合同结束月份",  required = true)
    private String dueMonth;

    @ApiModelProperty(value = "合同结束日",  required = true)
    private String dueDay;

    @ApiModelProperty(value = "年化利率",  required = true)
    private String intRate;

    @ApiModelProperty(value = "市场定价率(LPR)",  required = true)
    private String priceRate;

    @ApiModelProperty(value = "还款方式",  required = true)
    private String mtdCde;

    @ApiModelProperty(value = "还款日",  required = true)
    private String repayDate;

    @ApiModelProperty(value = "每期还款金额",  required = true)
    private String repayAmount;

    @ApiModelProperty(value = "罚息日利率",  required = true)
    private String guarOdIntRate;

    @ApiModelProperty(value = "咨询服务费",  required = true)
    private String serviceFee;

    // 认证信息
    @ApiModelProperty("认证信息")
    private AuthVo authDto;

}
