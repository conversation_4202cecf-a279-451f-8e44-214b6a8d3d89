package com.sing.service.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.entity.vo.uploadDataVo
 * @作者 Mr.sandman
 * @时间 2025/05/21 14:37
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("上传数据信息")
public class UploadDataVo {

  @ApiModelProperty("文件id")
  private String fileId;
  @ApiModelProperty("上传文件地址")
  private String uploadUrl;
}
