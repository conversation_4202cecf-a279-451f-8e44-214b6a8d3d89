package com.sing.service.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.entity.vo.FaceVo
 * @作者 Mr.sandman
 * @时间 2025/05/21 15:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("人脸认证信息")
public class FaceVo {

  @ApiModelProperty(value = "信息核验供应商", required = true)
  private String infoVerifySupplier;

  @ApiModelProperty(value = "核验时间",  required = true)
  private String verifyTime;

  @ApiModelProperty(value = "核验流水号",  required = true)
  private String verifySerialNumber;

  @ApiModelProperty(value = "人脸照片文件ossKey",  required = true)
  private String faceImage;

  @ApiModelProperty(value = "人脸照片相似度得分",  required = true)
  private String faceImageSimilarScore;

  @ApiModelProperty(value = "人脸活体检测得分",  required = true)
  private String faceLiveDetectScore;

}
