package com.sing.service.controller;

import com.sing.service.entity.SignContract;
import com.sing.service.entity.dto.ESignDTO;
import com.sing.service.error.EsignDemoException;
import com.sing.service.response.ResultMsg;
import com.sing.service.service.ESignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.singservice.sing_service.controller.ESignController
 * @作者 Mr.sandman
 * @时间 2025/05/20 14:31
 */
@RestController
@RequestMapping("/esign")
@Api(tags = "e签宝")
public class ESignController {

  @Autowired
  private ESignService eSignService;

  @PostMapping("/sign")
  @ApiOperation(value = "个人认证+签章", notes = "个人认证+签章")
  public ResultMsg sign( @RequestBody ESignDTO eSignDTO ) throws EsignDemoException {
    return eSignService.sign(eSignDTO);
  }

  @GetMapping("/getSignUrl")
  @ApiOperation(value = "获取签章链接", notes = "获取签章链接")
  public ResultMsg getSignUrl(@RequestParam(value = "contractNo") String contractNo) {
    return eSignService.getSignUrl(contractNo);
  }

  @PostMapping("/uploadFileAndGetPositions")
  @ApiOperation(value = "上传文件并获取文件位置信息", notes = "上传文件并获取文件位置信息")
  public ResultMsg uploadFileAndGetPositions(@RequestParam("file") MultipartFile file,
                                             @RequestParam("keyword") String keyword) throws EsignDemoException {
    return eSignService.getDocKeyPosition(file, keyword);
  }

  @PostMapping("/uploadFile")
  @ApiOperation(value = "上传文件到oss", notes = "上传文件到oss")
  public ResultMsg uploadFile(@RequestParam("file") MultipartFile file) throws EsignDemoException {
    return eSignService.uploadFileToOss(file);
  }

  @PostMapping("/createFileFromTemplate")
  @ApiOperation(value = "填充pdf模版文件", notes = "填充pdf模版文件")
  public ResultMsg createFileFromTemplate(@RequestParam("file") MultipartFile file) throws EsignDemoException {
    return eSignService.createFileFromTemplate(file);
  }

  /**
   * 获取文件下载地址
   */
  @GetMapping("/getOssUrl")
  public ResultMsg getOssUrl(@RequestParam("ossKey") String ossKey) throws EsignDemoException {
    return eSignService.getOssUrl(ossKey);
  }

  /**
   * 保存合同表内容
   */
  @PostMapping("/saveContract")
  public ResultMsg saveContract(@RequestBody SignContract contractDTO ) {
    return eSignService.saveContract(contractDTO);
  }

}
