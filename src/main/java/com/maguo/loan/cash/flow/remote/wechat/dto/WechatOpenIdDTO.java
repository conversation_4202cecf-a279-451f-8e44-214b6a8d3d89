package com.maguo.loan.cash.flow.remote.wechat.dto;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @since 2024-12-09
 */
public class WechatOpenIdDTO {

    @JSO<PERSON>ield(name = "session_key")
    private String accessKey;

    @JSO<PERSON>ield(name = "openid")
    private String openId;

    private String unionId;

    private String errmsg;

    private Integer errcode;

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }

    public Integer getErrcode() {
        return errcode;
    }

    public void setErrcode(Integer errcode) {
        this.errcode = errcode;
    }
}
