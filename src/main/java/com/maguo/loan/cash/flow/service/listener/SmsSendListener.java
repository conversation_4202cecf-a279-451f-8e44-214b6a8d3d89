package com.maguo.loan.cash.flow.service.listener;

import com.alibaba.fastjson2.JSONObject;
import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.enums.SmsTemplate;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.remote.nfsp.req.SmsSendReq;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.SmsService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/10/29
 */
@Component
public class SmsSendListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(SmsSendListener.class);
    @Autowired
    private SmsService smsService;

    @Autowired
    private OrderRepository orderRepository;

    public SmsSendListener(MqService mqService, WarningService mqWarningService, SmsService smsService) {
        super(mqService, mqWarningService);
        this.smsService = smsService;
    }

    @RabbitListener(queues = RabbitConfig.Queues.SMS_SEND)
    public void listenSmsSend(Message message, Channel channel) {
        String jsonStr = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听短信消息:{}", jsonStr);
            SmsSendReq smsSendReq = JSONObject.parseObject(jsonStr, SmsSendReq.class);
            if (smsSendReq.getTemplateId().equals(SmsTemplate.AMOUNT_NOT_APPLIED.getTemplateNo())) {
                Order order = orderRepository.findOrderById(smsSendReq.getContext());
                if (order == null || order.getOrderSubmitState() == WhetherState.Y) {
                    return;
                }
            }
            //smsService.send2Common(smsSendReq);
        } catch (Exception e) {
            processException(jsonStr, message, e, "跑批短信发送异常", getMqService()::submitSmsSendDelay);
        } finally {
            ackMsg(jsonStr, message, channel);
        }
    }
}
