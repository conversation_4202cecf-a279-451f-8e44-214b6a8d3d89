package com.maguo.loan.cash.flow.entity;

import com.jinghang.common.util.StringUtil;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;


import java.math.BigDecimal;
import java.time.LocalDateTime;


@Entity
@Table(name = "bf_transfer_record")
public class BfTransferRecord extends BaseEntity {
    private String payerNo; // 付款方(二级子商户号)
    private String payeeNo; // 收款方(二级子商户号)
    private String transSerialNo; // 商户流水号
    private String serverTransId; // 渠道核心流水号唯一（宝付结算款来帐通知）
    private String businessNo; // 接口返回的业务流水号
    private String accountType; // BALANCE-余额户 TRANSIT-在途户
    private BigDecimal dealAmount; // 转账金额,单位：元
    private BigDecimal feeAmount; // 手续费金额,单位：元
    private Integer retCode; // 返回码 1 成功 0 失败
    private Integer state; // 订单状态 0失败 1成功 2处理中
    private String errorCode; // 错误码
    private String errorMsg; // 错误原因

    public String getPayerNo() {
        return payerNo;
    }

    public void setPayerNo(String payerNo) {
        this.payerNo = payerNo;
    }

    public String getPayeeNo() {
        return payeeNo;
    }

    public void setPayeeNo(String payeeNo) {
        this.payeeNo = payeeNo;
    }

    public String getTransSerialNo() {
        return transSerialNo;
    }

    public void setTransSerialNo(String transSerialNo) {
        this.transSerialNo = transSerialNo;
    }

    public String getServerTransId() {
        return serverTransId;
    }

    public void setServerTransId(String serverTransId) {
        this.serverTransId = serverTransId;
    }

    public String getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public BigDecimal getDealAmount() {
        return dealAmount;
    }

    public void setDealAmount(BigDecimal dealAmount) {
        this.dealAmount = dealAmount;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public Integer getRetCode() {
        return retCode;
    }

    public void setRetCode(Integer retCode) {
        this.retCode = retCode;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
    @Override
    protected String prefix() {
        return "BF_TR";
    }

    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        if (StringUtil.isBlank(super.getCreatedBy())) {
            super.setCreatedBy("sys");
        }
        if (super.getCreatedTime() == null) {
            super.setCreatedTime(LocalDateTime.now());
        }
        if (StringUtil.isBlank(super.getUpdatedBy())) {
            super.setUpdatedBy("sys");
        }
        super.setUpdatedTime(LocalDateTime.now());
    }
}
