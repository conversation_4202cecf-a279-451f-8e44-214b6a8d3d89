package com.maguo.loan.cash.flow.dto.repush;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.maguo.loan.cash.flow.enums.ChargeBizType;
import com.maguo.loan.cash.flow.enums.Payee;
import com.maguo.loan.cash.flow.enums.ProcessState;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-01-01
 */
public class RepushRepayNotifyDto {

    /**
     * 公共账户类型
     */
    private String commonWithholdType;

    @Enumerated(EnumType.STRING)
    private ChargeBizType bizType = ChargeBizType.FINANCE;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Enumerated(EnumType.STRING)
    private ProcessState payState = ProcessState.SUCCEED;

    /**
     * 收款方
     */
    @Enumerated(EnumType.STRING)
    private Payee payee = Payee.CAPITAL;

    public String getCommonWithholdType() {
        return commonWithholdType;
    }

    public void setCommonWithholdType(String commonWithholdType) {
        this.commonWithholdType = commonWithholdType;
    }

    public ChargeBizType getBizType() {
        return bizType;
    }

    public void setBizType(ChargeBizType bizType) {
        this.bizType = bizType;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public ProcessState getPayState() {
        return payState;
    }

    public void setPayState(ProcessState payState) {
        this.payState = payState;
    }

    public Payee getPayee() {
        return payee;
    }

    public void setPayee(Payee payee) {
        this.payee = payee;
    }
}
