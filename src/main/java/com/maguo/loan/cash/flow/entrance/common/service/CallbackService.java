package com.maguo.loan.cash.flow.entrance.common.service;


import com.jinghang.common.util.CollectionUtil;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PartnerConfig;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteePlan;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteeRecord;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiBaseConstant;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiCovert;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiEnumCovert;
import com.maguo.loan.cash.flow.entrance.common.dto.request.callback.ApprovalPushReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.request.callback.OrderStatusPushReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.request.callback.RepaidPushReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.request.callback.RepayPlanPushReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.request.callback.RepayPushReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.response.QueryRepayResDTO;
import com.maguo.loan.cash.flow.entrance.common.enums.ApprovalStatus;
import com.maguo.loan.cash.flow.entrance.common.enums.RepayResult;
import com.maguo.loan.cash.flow.entrance.common.enums.RepayStatus;
import com.maguo.loan.cash.flow.entrance.common.enums.SubmitLoan;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiBizException;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiResultCode;
import com.maguo.loan.cash.flow.enums.CallbackState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OperationSource;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.QhBank;
import com.maguo.loan.cash.flow.enums.RepayMode;
import com.maguo.loan.cash.flow.enums.RepayRecordType;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.FlowRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.PartnerConfigRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteePlanRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteeRecordRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.LoanService;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.RepayPlanService;
import com.maguo.loan.cash.flow.service.callback.AbstractCallbackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.maguo.loan.cash.flow.entrance.common.covert.CommonApiBaseConstant.DAYS_INTERVAL;


/**
 * 公共流程回调推送
 *
 * <AUTHOR>
 * @date 2024/8/27
 */
@Service
public class CallbackService extends AbstractCallbackService {

    private static final Logger logger = LoggerFactory.getLogger(CallbackService.class);

    @Autowired
    private PreOrderRepository preOrderRepository;

    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Autowired
    private OrderService orderService;

    @Autowired
    private CommonApiService commonApiService;

    @Autowired
    private LoanService loanService;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;

    @Autowired
    private PartnerConfigRepository partnerConfigRepository;

    @Autowired
    private RepayExtraGuaranteePlanRepository repayExtraGuaranteePlanRepository;



    @Autowired
    private RepayPlanService repayPlanService;

    @Autowired
    private RepayOperateService repayOperateService;

    @Autowired
    private RepayExtraGuaranteeRecordRepository feeRecordRepository;

    @Autowired
    private FlowRepayRecordRepository flowRepayRecordRepository;

    @Override
    public FlowChannel getFlowChannel() {
        return null;
    }


    @Override
    public void processCallback(CallBackDTO callBackDTO) {
        FlowChannel flowChannel = callBackDTO.getFlowChannel();

        PartnerConfig partnerConfig = partnerConfigRepository.findByFlowChannel(flowChannel);

        WhetherState approvalPush = partnerConfig.getApprovalPush();
        WhetherState orderStatusPush = partnerConfig.getOrderStatusPush();
        WhetherState repayPlanPush = partnerConfig.getRepayPlanPush();
        WhetherState repaidPush = partnerConfig.getRepaidPush();
        WhetherState repayPush = partnerConfig.getRepayPush();
        WhetherState rightsStatusPush = partnerConfig.getRightsStatusPush();
        switch (callBackDTO.getCallbackState()) {
            case RISK_REJECT, RISK_PASS -> {
                // 进件(风控)审批回调 businessId = riskId
                if (approvalPush == WhetherState.N) {
                    logger.info("优品标准api,{},进件审批结果不需要回调", flowChannel.getDesc());
                    return;
                }
                approvalPush(callBackDTO);
            }
            case ORDER_APPLY, SUBMIT_WITHOUT_RIGHTS, CLEAR, CREDIT_FAIL, LOAN_FAIL -> {
                // 订单状态回调 businessId = orderId
                if (orderStatusPush == WhetherState.N) {
                    logger.info("优品标准api,{},订单状态推送不需要推送,callbackState:{}", flowChannel.getDesc(), callBackDTO.getCallbackState());
                    return;
                }
                orderStatusPush(callBackDTO);
            }
            case LOAN_CANCEL -> {
                Order order = orderService.findById(callBackDTO.getBusinessId());
                if (order.getOrderSubmitState() == WhetherState.N) {
                    if (approvalPush == WhetherState.N) {
                        logger.info("优品标准api,{},进件审批结果不需要回调", flowChannel.getDesc());
                        return;
                    }
                    approvalInvalidPush(callBackDTO, order);
                } else {
                    if (orderStatusPush == WhetherState.N) {
                        logger.info("优品标准api,{},订单状态推送不需要推送,callbackState:{}", flowChannel.getDesc(), callBackDTO.getCallbackState());
                        return;
                    }
                    orderStatusPush(callBackDTO);
                }
            }
            case LOAN_PASS -> {
                logger.info("优品标准api,放款通过回调过滤:FlowChannel{}, 订单状态是否推送:{}, 还款计划是否推送:{},权益扣费计划是否变更:{}",
                    callBackDTO.getFlowChannel(), orderStatusPush, repayPlanPush, rightsStatusPush);
                if (orderStatusPush == WhetherState.Y) {
                    // businessId = orderId
                    // 订单状态回调
                    orderStatusPush(callBackDTO);
                }
                if (repayPlanPush == WhetherState.Y) {
                    // 还款计划推送
                    repayPlanPush(callBackDTO);
                }
                // 权益扣费计划变更
                Loan loan = loanService.findByOrderId(callBackDTO.getBusinessId());
                callBackDTO.setBusinessId(loan.getId());

            }
            case OVERDUE -> {
                logger.info("优品标准api,逾期回调过滤:FlowChannel{}, 还款计划是否推送:{}", flowChannel.getDesc(), repayPlanPush);
                // businessId = repayPlanId
                // 还款计划推送
                RepayPlan repayPlan = repayPlanService.findById(callBackDTO.getBusinessId());
                String loanId = repayPlan.getLoanId();
                Loan loan = loanService.findById(loanId);
                callBackDTO.setBusinessId(loan.getOrderId());
                if (repayPlanPush == WhetherState.Y) {
                    repayPlanPush(callBackDTO);
                }
            }
            case REPAID, REPAY_FAIL -> {
                logger.info("标准api,FlowChannel{},还款结果是否推送:{}, 还款计划是否推送:{}", flowChannel.getDesc(), repaidPush, repayPlanPush);

                // businessId = customRepayRecordId
                String crrId = callBackDTO.getBusinessId();
                CustomRepayRecord customRepayRecord = customRepayRecordRepository.findById(crrId).orElseThrow();
                Loan loan = loanService.findById(customRepayRecord.getLoanId());
                if (repaidPush == WhetherState.Y && callBackDTO.getCallbackState() == CallbackState.REPAID) {
                    // 还款结果推送
                    repaidPush(customRepayRecord, loan, loan.getFlowChannel());
                }
                if (repayPush == WhetherState.Y) {
                    //还款推送v2
                    repayPush(customRepayRecord, loan);
                }

                // 还款计划推送
                callBackDTO.setBusinessId(loan.getOrderId());
                if (repayPlanPush == WhetherState.Y) {
                    repayPlanPush(callBackDTO);
                }
            }
            case RIGHT_ORDER_SUCCESS, RIGHT_ORDER_REFUND, RIGHT_ORDER_CANCELED -> {
                logger.info("优品标准api,权益扣费计划变更推送过滤:FlowChannel{},权益扣费计划变更是否推送:{}", flowChannel.getDesc(), rightsStatusPush);

            }
            default -> {
            }
        }
    }


    /**
     * 进件(风控)审批回调
     */
    private void approvalPush(CallBackDTO callBackDTO) {
        ApprovalPushReqDTO pushReqDTO = new ApprovalPushReqDTO();

        String riskId = callBackDTO.getBusinessId();

        UserRiskRecord userRiskRecord =
            userRiskRecordRepository.findById(riskId).orElseThrow(() -> new CommonApiBizException(CommonApiResultCode.USER_RISK_RECORD));
        PreOrder preOrder = preOrderRepository.findByRiskId(riskId).orElseThrow(() -> new CommonApiBizException(CommonApiResultCode.PRE_ORDER_NOT_EXIST));

        if (callBackDTO.getCallbackState() == CallbackState.RISK_PASS) {
            pushReqDTO.setPartnerOrderNo(preOrder.getOrderNo());
            pushReqDTO.setApprovalStatus(ApprovalStatus.AUDIT_PASS.name());
            pushReqDTO.setAmount(userRiskRecord.getApproveAmount());
            pushReqDTO.setApprovalTime(userRiskRecord.getPassTime());
            Order order = orderService.query(preOrder.getOrderNo(), null, callBackDTO.getFlowChannel());
            if (Objects.isNull(order)) {
                throw new CommonApiBizException(CommonApiResultCode.ORDER_NOT_EXIST);
            }
            pushReqDTO.setExpireTime(order.getApplyTime().plusDays(CommonApiBaseConstant.DATE_SUBTRACT_THREE));
            pushReqDTO.setOrderId(order.getId());
            pushReqDTO.setApprovalRate(userRiskRecord.getApproveRate().getRate());
        } else {
            pushReqDTO.setApprovalStatus(ApprovalStatus.AUDIT_REJECT.name());
            pushReqDTO.setAmount(BigDecimal.ZERO);
            pushReqDTO.setPartnerOrderNo(preOrder.getOrderNo());
            pushReqDTO.setFreeTime(userRiskRecord.getCreatedTime().plusDays(DAYS_INTERVAL));
        }
        // 获取请求域名
        FlowChannel flowChannel = callBackDTO.getFlowChannel();
        PartnerConfig partnerConfig = partnerConfigRepository.findByFlowChannel(flowChannel);
        //回调流量
        commonApiService.request(pushReqDTO, partnerConfig);
    }

    private void approvalInvalidPush(CallBackDTO callBackDTO, Order order) {
        try {
            ApprovalPushReqDTO pushReqDTO = new ApprovalPushReqDTO();
            pushReqDTO.setPartnerOrderNo(order.getOuterOrderId());
            pushReqDTO.setApprovalStatus(ApprovalStatus.QUOTA_INVALID.name());
            pushReqDTO.setAmount(order.getApproveAmount());
            pushReqDTO.setApprovalRate(order.getApproveRate().getRate());
            pushReqDTO.setApprovalTime(order.getApplyTime());
            pushReqDTO.setOrderId(order.getId());
            // 获取请求域名
            FlowChannel flowChannel = callBackDTO.getFlowChannel();
            PartnerConfig partnerConfig = partnerConfigRepository.findByFlowChannel(flowChannel);
            //回调流量
            commonApiService.request(pushReqDTO, partnerConfig);
        } catch (Exception e) {
            logger.error("标准API失效异常:{},flowChannel:{}", order.getId(), order.getFlowChannel());
        }
    }

    /**
     * 订单状态推送
     */
    private void orderStatusPush(CallBackDTO callBackDTO) {
        // 声明参数DTO
        OrderStatusPushReqDTO pushReqDTO = new OrderStatusPushReqDTO();

        // 查询订单
        String orderId = callBackDTO.getBusinessId();
        Order order = orderService.findById(orderId);
        pushReqDTO.setOrderId(order.getId());
        pushReqDTO.setPartnerOrderNo(order.getOuterOrderId());

        // 订单状态转换
        //需要订单状态推送的状态: ORDER_APPLY, CLEAR, CREDIT_FAIL, LOAN_FAIL, LOAN_CANCEL,CLEAR
        OrderState orderState = CommonApiEnumCovert.INSTANCE.toOrderState(callBackDTO.getCallbackState());
        pushReqDTO.setOrderState(orderState.name());
        if (callBackDTO.getCallbackState() == CallbackState.SUBMIT_WITHOUT_RIGHTS) {
            pushReqDTO.setOrderState(SubmitLoan.SUBMIT_LOAN.name());
        }

        // 时间字段处理
        Loan loan = loanService.findByOrderId(orderId);
        pushReqDTO.setReLoan(Objects.nonNull(loan) && Objects.nonNull(loan.getReloan()) ? loan.getReloan().name() : "");
        if (orderState == OrderState.LOAN_PASS) {
            pushReqDTO.setBankChannelName(
                Objects.isNull(order.getBankChannel()) ? "" : Objects.isNull(QhBank.getQhBankBy(order.getBankChannel()))
                    ? "" : QhBank.getQhBankBy(order.getBankChannel()).getBankName());
            pushReqDTO.setLoanSuccessTime(loan.getLoanTime());
            pushReqDTO.setLoanAmount(loan.getAmount());
        } else if (orderState == OrderState.LOAN_FAIL) {
            // 放款取消和放款失败都是放款失败
            if (callBackDTO.getCallbackState() == CallbackState.LOAN_CANCEL) {
                pushReqDTO.setRejectReason("放款取消");
            } else {
                pushReqDTO.setRejectReason("放款失败");
                if (callBackDTO.getCallbackState() == CallbackState.CREDIT_FAIL) {
                    pushReqDTO.setFreezeDay(order.getUpdatedTime().plusDays(DAYS_INTERVAL));
                }
            }
        }
        // 获取请求域名
        FlowChannel flowChannel = callBackDTO.getFlowChannel();
        PartnerConfig partnerConfig = partnerConfigRepository.findByFlowChannel(flowChannel);
        //回调流量
        commonApiService.request(pushReqDTO, partnerConfig);
    }

    /**
     * 还款计划推送
     */
    private void repayPlanPush(CallBackDTO callBackDTO) {
        RepayPlanPushReqDTO pushReqDTO = new RepayPlanPushReqDTO();
        String orderId = callBackDTO.getBusinessId();
        Order order = orderService.findById(orderId);
        if (Objects.isNull(order)) {
            throw new CommonApiBizException(CommonApiResultCode.ORDER_NOT_EXIST);
        }
        Loan loan = loanService.findByOrderId(orderId);
        if (Objects.isNull(loan)) {
            throw new CommonApiBizException(CommonApiResultCode.LOAN_NOT_EXIST);
        }

        pushReqDTO.setOrderId(order.getId());
        pushReqDTO.setPartnerOrderNo(order.getOuterOrderId());

        // 查询还款计划
        List<RepayPlan> list = repayPlanRepository.findByLoanId(loan.getId());
        if (CollectionUtil.isEmpty(list)) {
            throw new CommonApiBizException(CommonApiResultCode.REPAY_PLAN_NOT_EXIST);
        }

        // 处理线下还款,实还与应还不一致问题
        list.forEach(element -> {
            if (element.getCustRepayState() == RepayState.REPAID) {
                element.setAmount(element.getActAmount());
                element.setGuaranteeAmt(element.getActGuaranteeAmt());
                element.setConsultFee(element.getActConsultFee());
                element.setPenaltyAmt(element.getActPenaltyAmt());
                element.setInterestAmt(element.getActInterestAmt());
                element.setPrincipalAmt(element.getActPrincipalAmt());
            }
        });
        List<RepayPlanPushReqDTO.RepayPlanDTO> planDTOList = CommonApiCovert.INSTANCE.toRepayPlanBack(list);

        // 费用计划
        List<RepayExtraGuaranteePlan> feePlanList = repayExtraGuaranteePlanRepository.findByLoanId(loan.getId());

        for (RepayPlanPushReqDTO.RepayPlanDTO plan : planDTOList) {
            // 减免金额
            plan.setReduceAmount(BigDecimal.ZERO);
            plan.setCustRepayState(RepayStatus.REPAID.name().equals(plan.getCustRepayState()) ? RepayStatus.REPAID.name() : RepayStatus.NORMAL.name());

            // 还款计划状态:已还
            if (RepayStatus.NORMAL.name().equals(plan.getCustRepayState())) {
                // 不存在二次扣款 不需要判断是否部分还款
                if (CollectionUtil.isEmpty(feePlanList)) {
                    continue;
                }
                // 判断是否为二次扣款
                Map<Integer, RepayState> map =
                    feePlanList.stream().collect(Collectors.toMap(RepayExtraGuaranteePlan::getPeriod, RepayExtraGuaranteePlan::getPlanState));
                if (map.containsKey(plan.getPeriod())) {
                    // 1、存在部分扣款，状态为部分还款
                    plan.setCustRepayState(RepayStatus.PARTIAL_REPAYMENT.name());
                }
            }
        }
        pushReqDTO.setRepayPlanList(planDTOList);

        // 获取请求域名
        FlowChannel flowChannel = callBackDTO.getFlowChannel();
        PartnerConfig partnerConfig = partnerConfigRepository.findByFlowChannel(flowChannel);
        //回调流量
        commonApiService.request(pushReqDTO, partnerConfig);
    }

    /**
     * 还款结果推送
     */
    @Deprecated
    private void repaidPush(CustomRepayRecord customRepayRecord, Loan loan, FlowChannel flowChannel) {
        RepaidPushReqDTO pushReqDTO;

        Order order = orderService.findById(loan.getOrderId());

        // 查询还款计划
        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(loan.getId(), customRepayRecord.getPeriod());
        if (Objects.isNull(repayPlan)) {
            throw new CommonApiBizException(CommonApiResultCode.REPAY_PLAN_NOT_EXIST);
        }

        if (repayPlan.getCustRepayState() == RepayState.REPAID) {
            repayPlan.setAmount(repayPlan.getActAmount());
            repayPlan.setGuaranteeAmt(repayPlan.getActGuaranteeAmt());
            repayPlan.setConsultFee(repayPlan.getActConsultFee());
            repayPlan.setPrincipalAmt(repayPlan.getActPrincipalAmt());
            repayPlan.setInterestAmt(repayPlan.getActInterestAmt());
            repayPlan.setPenaltyAmt(repayPlan.getActPenaltyAmt());
        }
        pushReqDTO = CommonApiCovert.INSTANCE.toRepaidPushReqDTO(repayPlan);
        pushReqDTO.setReduceAmount(BigDecimal.ZERO);
        pushReqDTO.setPeriod(customRepayRecord.getPeriod());
        pushReqDTO.setRepaidDate(customRepayRecord.getRepaidDate());
        pushReqDTO.setRepayMode(customRepayRecord.getRepayMode().name());
        pushReqDTO.setRepayPurpose(customRepayRecord.getRepayPurpose().name());
        pushReqDTO.setOrderId(loan.getOrderId());
        if (customRepayRecord.getRepayMode() == RepayMode.ONLINE && customRepayRecord.getOperationSource() == OperationSource.USER) {
            // 用户还款时才会有合作机构还款流水
            pushReqDTO.setPartnerRepayNo(customRepayRecord.getOuterRepayNo());
        }
        pushReqDTO.setRepayNo(customRepayRecord.getId());
        // 合作机构单号
        pushReqDTO.setPartnerOrderNo(order.getOuterOrderId());

        // 还款结果,只有还款成功才会推送
        pushReqDTO.setRepayResult(RepayResult.REPAY_SUCCESS.name());

        // 获取请求域名
        PartnerConfig partnerConfig = partnerConfigRepository.findByFlowChannel(flowChannel);

        //回调流量
        commonApiService.request(pushReqDTO, partnerConfig);
    }


    /**
     * 还款结果推送v2
     */
    private void repayPush(CustomRepayRecord customRepayRecord, Loan loan) {
        Order order = orderService.findById(loan.getOrderId());

        RepayRecordType repayRecordType;
        RepayExtraGuaranteeRecord feeRecord;
        String recordId;
        RepayMode repayMode;

        List<RepayExtraGuaranteeRecord> feeRecords = feeRecordRepository.findBySourceRepayIdOrderByCreatedTimeAsc(customRepayRecord.getId());
        if (CollectionUtil.isEmpty(feeRecords) || feeRecords.size() == 1) {
            //首次还款
            repayRecordType = RepayRecordType.CUSTOMER_REPAY_RECORD;
            feeRecord = CollectionUtil.isEmpty(feeRecords) ? null : feeRecords.get(0);
            recordId = customRepayRecord.getId();
            repayMode = customRepayRecord.getRepayMode();
        } else {
            repayRecordType = RepayRecordType.FEE_REPAY_RECORD;
            feeRecord = feeRecords.get(feeRecords.size() - 1);
            recordId = feeRecord.getId();
            repayMode = feeRecord.getRepayMode();
        }
        QueryRepayResDTO queryRepayResDTO = repayOperateService.genRepayResult(repayRecordType, customRepayRecord, feeRecord, null);
        RepayPushReqDTO pushReqDTO = CommonApiCovert.INSTANCE.toRepayPushReqDTO(queryRepayResDTO);
        pushReqDTO.setRepayMode(repayMode);
        pushReqDTO.setRepayPurpose(customRepayRecord.getRepayPurpose());

        pushReqDTO.setPeriod(customRepayRecord.getPeriod());
        pushReqDTO.setOrderId(order.getId());
        pushReqDTO.setPartnerOrderNo(order.getOuterOrderId());

        flowRepayRecordRepository.findByRecordIdAndRepayRecordType(recordId, repayRecordType).ifPresent(flowRepayRecord -> {
            pushReqDTO.setRepayId(flowRepayRecord.getId());
            pushReqDTO.setPartnerRepayNo(flowRepayRecord.getPartnerRepayNo());
        });

        // 获取请求域名
        PartnerConfig partnerConfig = partnerConfigRepository.findByFlowChannel(order.getFlowChannel());

        //回调流量
        commonApiService.request(pushReqDTO, partnerConfig);
    }


}
