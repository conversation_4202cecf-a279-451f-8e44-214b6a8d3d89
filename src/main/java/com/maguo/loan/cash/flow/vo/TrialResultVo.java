package com.maguo.loan.cash.flow.vo;



import com.maguo.loan.cash.flow.enums.Payee;
import com.maguo.loan.cash.flow.enums.RepayPurpose;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/10/16
 */
public class TrialResultVo {
    /**
     * 本金
     */
    private BigDecimal principal;
    /**
     * 利息
     */
    private BigDecimal interest;
    /**
     * 罚息
     */
    private BigDecimal penalty;
    /**
     * 资方罚息
     */
    private BigDecimal capitalPenalty;
    /**
     * 违约金
     */
    private BigDecimal breachFee;
    /**
     * 总融担费
     */
    private BigDecimal guaranteeFee;

    /**
     * 资方代收的融担费
     */
    private BigDecimal capitalGuaranteeFee;

    /**
     * 平台收取的融担费
     */
    private BigDecimal extraGuaranteeFee;

    /**
     * 咨询费
     */
    private BigDecimal consultFee;

    /**
     * 应还金额
     */
    private BigDecimal amount;


    private String bankName;

    private String bankAccountCode;

    /**
     * 收款方
     */
    private Payee payee;

    private RepayPurpose repayPurpose;

    public BigDecimal getCapitalGuaranteeFee() {
        return capitalGuaranteeFee;
    }

    public void setCapitalGuaranteeFee(BigDecimal capitalGuaranteeFee) {
        this.capitalGuaranteeFee = capitalGuaranteeFee;
    }

    public BigDecimal getExtraGuaranteeFee() {
        return extraGuaranteeFee;
    }

    public void setExtraGuaranteeFee(BigDecimal extraGuaranteeFee) {
        this.extraGuaranteeFee = extraGuaranteeFee;
    }

    private String remark;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccountCode() {
        return bankAccountCode;
    }

    public void setBankAccountCode(String bankAccountCode) {
        this.bankAccountCode = bankAccountCode;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public BigDecimal getBreachFee() {
        return breachFee;
    }

    public void setBreachFee(BigDecimal breachFee) {
        this.breachFee = breachFee;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Payee getPayee() {
        return payee;
    }

    public void setPayee(Payee payee) {
        this.payee = payee;
    }

    public BigDecimal getCapitalPenalty() {

        return capitalPenalty;
    }

    public void setCapitalPenalty(BigDecimal capitalPenalty) {
        this.capitalPenalty = capitalPenalty;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }
}
