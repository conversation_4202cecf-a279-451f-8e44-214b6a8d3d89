package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.FeeType;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@Entity
@Table(name = "repay_extra_guarantee_plan")
public class RepayExtraGuaranteePlan extends BaseEntity {
    /**
     * 对客还款记录
     */
    private String repayRecordId;
    /**
     * 用户
     */
    private String userId;
    /**
     * 借据id
     */
    private String loanId;
    /**
     * 期次
     */
    private Integer period;
    /**
     * 计划还款日
     */
    private LocalDate planRepayDate;
    /**
     * 还款目的
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose repayPurpose;
    /**
     * 应还金额
     */
    private BigDecimal planAmount;

    /**
     * 应还融担费
     */
    private BigDecimal planGuaranteeAmt;

    /**
     * 应还咨询费
     */
    private BigDecimal planConsultAmt;

    /**
     * 应还平台罚息（对客罚息 - 对资罚息）
     */
    private BigDecimal planPlatformPenaltyAmt;

    /**
     * 已还金额
     */
    private BigDecimal paidAmount;
    /**
     * 剩余未还金额
     */
    private BigDecimal leftAmount;
    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    private RepayState planState;
    /**
     * 实还时间
     */
    private LocalDateTime actRepayTime;


    /**
     * 费用类型
     */
    @Enumerated(EnumType.STRING)
    private FeeType feeType;

    public FeeType getFeeType() {
        return feeType;
    }

    public void setFeeType(FeeType feeType) {
        this.feeType = feeType;
    }

    public String getRepayRecordId() {
        return repayRecordId;
    }

    public void setRepayRecordId(String repayRecordId) {
        this.repayRecordId = repayRecordId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public LocalDate getPlanRepayDate() {
        return planRepayDate;
    }

    public void setPlanRepayDate(LocalDate planRepayDate) {
        this.planRepayDate = planRepayDate;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public BigDecimal getPlanAmount() {
        return planAmount;
    }

    public void setPlanAmount(BigDecimal planAmount) {
        this.planAmount = planAmount;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public BigDecimal getLeftAmount() {
        return leftAmount;
    }

    public void setLeftAmount(BigDecimal leftAmount) {
        this.leftAmount = leftAmount;
    }

    public RepayState getPlanState() {
        return planState;
    }

    public void setPlanState(RepayState planState) {
        this.planState = planState;
    }

    public LocalDateTime getActRepayTime() {
        return actRepayTime;
    }

    public void setActRepayTime(LocalDateTime actRepayTime) {
        this.actRepayTime = actRepayTime;
    }

    public BigDecimal getPlanGuaranteeAmt() {
        return planGuaranteeAmt;
    }

    public void setPlanGuaranteeAmt(BigDecimal planGuaranteeAmt) {
        this.planGuaranteeAmt = planGuaranteeAmt;
    }

    public BigDecimal getPlanConsultAmt() {
        return planConsultAmt;
    }

    public void setPlanConsultAmt(BigDecimal planConsultAmt) {
        this.planConsultAmt = planConsultAmt;
    }

    public BigDecimal getPlanPlatformPenaltyAmt() {
        return planPlatformPenaltyAmt;
    }

    public void setPlanPlatformPenaltyAmt(BigDecimal planPlatformPenaltyAmt) {
        this.planPlatformPenaltyAmt = planPlatformPenaltyAmt;
    }

    @Override
    protected String prefix() {
        return "EG";
    }
}
