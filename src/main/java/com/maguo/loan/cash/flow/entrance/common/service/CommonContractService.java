package com.maguo.loan.cash.flow.entrance.common.service;


import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.entity.AgreementShow;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiCovert;
import com.maguo.loan.cash.flow.entrance.common.dto.request.ContractReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.response.ContractResDTO;
import com.maguo.loan.cash.flow.entrance.common.enums.AgreementType;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiBizException;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiResultCode;
import com.maguo.loan.cash.flow.enums.ContractParamEnum;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.LoanService;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.agreement.AgreementShowService;
import com.maguo.loan.cash.flow.util.ThreadLocalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.maguo.loan.cash.flow.entrance.common.constant.CommonBaseConstant.REPAY_URL_DAY;


/**
 * <AUTHOR>
 * @date 2024/8/20
 */
@Service
public class CommonContractService {

    @Autowired
    private AgreementShowService agreementShowService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private LoanService loanService;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Autowired
    private FileService fileService;


    /**
     * 获取协议
     */
    public ContractResDTO getContract(ContractReqDTO reqDTO) {
        ContractResDTO resDTO = new ContractResDTO();
        List<AgreementShow> agreementList = new ArrayList<>();
        Order order = null;

        // 校验参数
        if (Objects.equals(reqDTO.getScene(), AgreementType.COMMON_LOAN_SCENE.getCode())
            || Objects.equals(reqDTO.getScene(), AgreementType.COMMON_REPAY_SCENE.getCode())
            || Objects.equals(reqDTO.getScene(), AgreementType.COMMON_RIGHTS_SCENE.getCode())) {
            if (StringUtil.isBlank(reqDTO.getPartnerOrderNo())) {
                throw new CommonApiBizException(CommonApiResultCode.PARAM_PARTNER_ORDER_NOT_EXIST);
            }
            order = orderService.query(reqDTO.getPartnerOrderNo(), null, ThreadLocalUtil.getFlowChannel());
            if (Objects.isNull(order)) {
                throw new CommonApiBizException(CommonApiResultCode.ORDER_NOT_EXIST);
            }
        }

        // 风控和绑卡阶段协议
        if (reqDTO.getScene().equals(AgreementType.COMMON_CREDIT_SCENE.getCode()) || reqDTO.getScene().equals(AgreementType.COMMON_BIND_CARD_SCENE.getCode())) {
            String param =
                reqDTO.getScene().equals(AgreementType.COMMON_CREDIT_SCENE.getCode()) ? ContractParamEnum.RISK.name() : ContractParamEnum.BIND_CARD.name();
            agreementList = agreementShowService.getNewAgreements(order, param);
        } else if (reqDTO.getScene().equals(AgreementType.COMMON_LOAN_SCENE.getCode())) {
            // 要款场景协议
            List<AgreementShow> rightsAgreementShow = agreementShowService.getNewAgreements(order, ContractParamEnum.RIGHTS.name());
            if (CollectionUtil.isNotEmpty(rightsAgreementShow)) {
                agreementList.addAll(rightsAgreementShow);
            }
            List<AgreementShow> loanAgreementShow = agreementShowService.getNewAgreements(order, ContractParamEnum.LOAN.name());
            if (CollectionUtil.isNotEmpty(loanAgreementShow)) {
                agreementList.addAll(loanAgreementShow);
            }
        } else if (reqDTO.getScene().equals(AgreementType.COMMON_RIGHTS_SCENE.getCode())) {
            // 会员场景协议
            List<AgreementShow> rightsAgreementShow = agreementShowService.getNewAgreements(order, ContractParamEnum.RIGHTS.name());
            agreementList.addAll(rightsAgreementShow);
        } else if (reqDTO.getScene().equals(AgreementType.COMMON_REPAY_SCENE.getCode())) {
            //贷后协议
            Loan loan = loanService.findByOrderId(order.getId());
            if (Objects.isNull(loan)) {
                throw new CommonApiBizException(CommonApiResultCode.LOAN_NOT_EXIST);
            }
            String relatedId = loan.getLoanRecordId();
            List<UserFile> creditFiles = agreementSignRelationRepository.queryUserFiles(relatedId);
            List<ContractResDTO.ContractInfo> list = mergeStream(creditFiles);
            resDTO.setContractList(list);
            return resDTO;
        }
        List<ContractResDTO.ContractInfo> list = CommonApiCovert.INSTANCE.toContractInfo(agreementList);
        resDTO.setContractList(list);
        return resDTO;
    }

    private List<ContractResDTO.ContractInfo> mergeStream(List<UserFile> userFiles) {
        List<ContractResDTO.ContractInfo> list = new ArrayList<>();
        userFiles.stream().filter(item -> WhetherState.Y == item.getSignFinal())
            //去重复
            .collect(Collectors.toMap(UserFile::getFileType, value -> value, (v1, v2) -> {
                if (v1.getCreatedTime().isAfter(v2.getCreatedTime())) {
                    return v1;
                }
                return v2;
            })).values().forEach(userFile -> {
                ContractResDTO.ContractInfo contractResponse = new ContractResDTO.ContractInfo();
                contractResponse.setContractName(userFile.getFileName());
                String ossUrl = fileService.getOssUrl(userFile.getOssBucket(), userFile.getOssKey(), REPAY_URL_DAY);
                contractResponse.setContractUrl(ossUrl);
                list.add(contractResponse);
            });
        return list;
    }
}
