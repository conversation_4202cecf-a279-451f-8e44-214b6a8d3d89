package com.maguo.loan.cash.flow.entrance.lvxin.dto.loan;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 权益还款计划(试算)
 * @Date 2024/5/21 11:15
 * @Version v1.0
 **/
public class LvxinProfitPlan {
    /**
     * 期数
     */
    private Integer period;
    /**
     * 权益金额
     */
    private BigDecimal amount;
    /**
     * 还款日期，10位时间戳：1685351549
     */
    private Long repayTime;

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getRepayTime() {
        return repayTime;
    }

    public void setRepayTime(Long repayTime) {
        this.repayTime = repayTime;
    }
}
