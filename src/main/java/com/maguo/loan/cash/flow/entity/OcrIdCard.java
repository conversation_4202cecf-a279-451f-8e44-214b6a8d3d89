package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.ApplicationSource;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IdCardSide;
import com.maguo.loan.cash.flow.enums.OcrClientType;
import com.maguo.loan.cash.flow.enums.ProcessState;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDate;

@Entity
@Table(name = "ocr_id_card")
public class OcrIdCard extends BaseEntity {


    private String mobile;

    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    private String ossBucket;

    private String dataUrl;


    @Enumerated(EnumType.STRING)
    private ProcessState status;

    @Enumerated(EnumType.STRING)
    private IdCardSide side;

    @Column
    private int code;

    private String name;


    private String sex;


    private String nationality;


    private String number;


    private String birth;


    private String address;


    private String authority;


    private LocalDate startDate;


    private LocalDate endDate;
    @Enumerated(EnumType.STRING)
    private OcrClientType clientId;

    private String clientNo;


    private String charge;


    private String remark;

    @Enumerated(EnumType.STRING)
    private ApplicationSource applicationSource;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getOssBucket() {
        return ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    public String getDataUrl() {
        return dataUrl;
    }

    public void setDataUrl(String dataUrl) {
        this.dataUrl = dataUrl;
    }

    public ProcessState getStatus() {
        return status;
    }

    public void setStatus(ProcessState status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getBirth() {
        return birth;
    }

    public void setBirth(String birth) {
        this.birth = birth;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAuthority() {
        return authority;
    }

    public void setAuthority(String authority) {
        this.authority = authority;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getCharge() {
        return charge;
    }

    public void setCharge(String charge) {
        this.charge = charge;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public OcrClientType getClientId() {
        return clientId;
    }

    public void setClientId(OcrClientType clientId) {
        this.clientId = clientId;
    }

    public String getClientNo() {
        return clientNo;
    }

    public void setClientNo(String clientNo) {
        this.clientNo = clientNo;
    }

    @Override
    protected String prefix() {
        return "OIC";
    }

    public IdCardSide getSide() {
        return side;
    }

    public void setSide(IdCardSide side) {
        this.side = side;
    }

    public ApplicationSource getApplicationSource() {
        return applicationSource;
    }

    public void setApplicationSource(ApplicationSource applicationSource) {
        this.applicationSource = applicationSource;
    }
}
