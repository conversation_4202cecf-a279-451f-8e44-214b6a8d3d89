package com.maguo.loan.cash.flow.dto.user;


import com.maguo.loan.cash.flow.enums.OcrClientType;

/**
 * <AUTHOR>
 * @since 2024-11-20
 */
public class FaceInfoSaveRequest {

    /**
     * 人脸活体照片路径
     */
    private String livingPath;

    /**
     * 人脸识别分数
     */
    private String score;

    private OcrClientType ocrClientType = OcrClientType.XINNUO;

    public String getLivingPath() {
        return livingPath;
    }

    public void setLivingPath(String livingPath) {
        this.livingPath = livingPath;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public OcrClientType getOcrClientType() {
        return ocrClientType;
    }

    public void setOcrClientType(OcrClientType ocrClientType) {
        this.ocrClientType = ocrClientType;
    }
}
