package com.maguo.loan.cash.flow.entrance.common.dto.request;

import jakarta.validation.constraints.NotBlank;

import java.io.Serial;
import java.io.Serializable;

/**
 * 还款链接
 * <AUTHOR>
 * @date 2024/7/11
 */
public class GenerationRepayUrlReqDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -3393099560191542920L;
    /**
     * 外部放款订单流水号
     */
    @NotBlank
    private String partnerOrderNo;

    /**
     * 返回跳转链接
     */
    private String redirectUrl;

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }
}
