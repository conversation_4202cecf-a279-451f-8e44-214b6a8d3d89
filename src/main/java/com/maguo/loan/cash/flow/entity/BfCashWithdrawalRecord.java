package com.maguo.loan.cash.flow.entity;

import com.jinghang.common.util.StringUtil;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "bf_cash_withdrawal_record")
public class BfCashWithdrawalRecord extends BaseEntity {

    @Column(name = "server_trans_id", length = 128)
    private String serverTransId;

    @Column(name = "contract_no", length = 32)
    private String contractNo;

    @Column(name = "trans_serial_no", length = 50)
    private String transSerialNo;

    @Column(name = "deal_amount", precision = 10, scale = 2)
    private BigDecimal dealAmount;

    @Column(name = "ret_code")
    private Integer retCode;

    @Column(name = "error_code", length = 20)
    private String errorCode;

    @Column(name = "error_msg", length = 40)
    private String errorMsg;

    @Column(name = "state")
    private Integer state;

    public String getServerTransId() {
        return serverTransId;
    }

    public void setServerTransId(String serverTransId) {
        this.serverTransId = serverTransId;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getTransSerialNo() {
        return transSerialNo;
    }

    public void setTransSerialNo(String transSerialNo) {
        this.transSerialNo = transSerialNo;
    }

    public BigDecimal getDealAmount() {
        return dealAmount;
    }

    public void setDealAmount(BigDecimal dealAmount) {
        this.dealAmount = dealAmount;
    }

    public Integer getRetCode() {
        return retCode;
    }

    public void setRetCode(Integer retCode) {
        this.retCode = retCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
    @Override
    protected String prefix() {
        return "BF_CW";
    }

    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        if (StringUtil.isBlank(super.getCreatedBy())) {
            super.setCreatedBy("sys");
        }
        if (super.getCreatedTime() == null) {
            super.setCreatedTime(LocalDateTime.now());
        }
        if (StringUtil.isBlank(super.getUpdatedBy())) {
            super.setUpdatedBy("sys");
        }
        super.setUpdatedTime(LocalDateTime.now());
    }
}
