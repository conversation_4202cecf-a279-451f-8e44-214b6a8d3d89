package com.maguo.loan.cash.flow.vo;

import com.maguo.loan.cash.flow.enums.RepayPurpose;
import jakarta.validation.constraints.NotNull;

/**
 * @Classname RepayConfirmRequest
 * @Description 还款确认请求
 * @Date 2023/9/22 17:18
 * @Created by gale
 */
public class RepayTrialRequest extends BaseRequest {


    /**
     * 还款方式 结清 或者当期
     */
    @NotNull(message = "还款方式不能为空")
    private RepayPurpose repayPurpose;

    /**
     * 期数
     */
    @NotNull(message = "期数不能为空")
    private Integer periods;

    @NotNull(message = "借据单号不能为空")
    private String loanId;


    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }
}
