package com.maguo.loan.cash.flow.entity;

import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.common.BaoFuSettlementStateCons;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 宝付结算通知记录实体类
 */
@Entity
@Table(name = "bf_settlement_notify")
public class BfSettlementNotify extends BaseEntity {

    /**
     * 渠道核心流水号
     */
    private String serverTransId;

    /**
     * 通知类型 (03-记账簿入金通知)
     */
    private Integer type;

    /**
     * 转账金额(单位：元)
     */
    private BigDecimal amount;

    /**
     * 记录时间 (格式：yyyyMMddHHmmss)
     */
    private LocalDateTime recordTime;

    /**
     * 明细序号
     */
    private String seqNo;

    /**
     * 银行类型 (HT：华通银行；SSQY：苏商银行)
     */
    private String bankType;

    /**
     * 外部订单号 (企业网银转账的银行流水号或网联流水号)
     */
    private String orderId;

    /**
     * 处理状态
     * (0-接收通知, 1-蚂蚁账户加值完成,
     *  2-蚂蚁给湖消转账完成, 3-湖消转账/取现完成)
     */
    private Integer step;

    /**
     * 是否完成 0未完成 1完成
     */
    private int finishStatus;

    public String getServerTransId() {
        return serverTransId;
    }

    public void setServerTransId(String serverTransId) {
        this.serverTransId = serverTransId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
    }

    public String getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(String seqNo) {
        this.seqNo = seqNo;
    }

    public String getBankType() {
        return bankType;
    }

    public void setBankType(String bankType) {
        this.bankType = bankType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getStep() {
        return step;
    }

    public void setStep(Integer step) {
        this.step = step;
    }

    public int getFinishStatus() {
        return finishStatus;
    }

    public void setFinishStatus(int finishStatus) {
        this.finishStatus = finishStatus;
    }


    public boolean isFinished() {
       return this.finishStatus == BaoFuSettlementStateCons.NOTIFY_FINISHED;
    }

    @Override
    protected String prefix() {
        return "BF_SN";
    }


    @Override
    public void prePersist() {
        if (StringUtil.isBlank(super.getId())) {
            setId(genId());
        }
        if (StringUtil.isBlank(super.getCreatedBy())) {
            super.setCreatedBy("sys");
        }
        if (super.getCreatedTime() == null) {
            super.setCreatedTime(LocalDateTime.now());
        }
        if (StringUtil.isBlank(super.getUpdatedBy())) {
            super.setUpdatedBy("sys");
        }
        super.setUpdatedTime(LocalDateTime.now());
    }
}
