package com.maguo.loan.cash.flow.dto.user;

import com.maguo.loan.cash.flow.enums.ApplicationSource;
import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024-11-19
 */
public class IdCardInfoSaveRequest {

    private ApplicationSource applicationSource;

    @NotBlank(message = "姓名不能为空")
    private String name;
    @NotBlank(message = "身份证号不能为空")
    private String certNo;

    /**
     * 身份证有效期开始
     */
    private String certStartDay;

    /**
     * 身份证有效期结束
     */
    private String certEndDay;

    /**
     * 是否长期的
     */
    private boolean permanent;

    /**
     * 性别
     */
    private String gender;

    /**
     * 民族
     */
    private String nation;

    /**
     * 签发机构
     */
    private String certOrg;

    private String certAddress;
    /**
     * 身份证人像图片地址
     */
    private String facePicPath;

    /**
     * 身份证国徽图片地址
     */
    private String nationPicPath;

    public ApplicationSource getApplicationSource() {
        return applicationSource;
    }

    public void setApplicationSource(ApplicationSource applicationSource) {
        this.applicationSource = applicationSource;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getCertStartDay() {
        return certStartDay;
    }

    public void setCertStartDay(String certStartDay) {
        this.certStartDay = certStartDay;
    }

    public String getCertEndDay() {
        return certEndDay;
    }

    public void setCertEndDay(String certEndDay) {
        this.certEndDay = certEndDay;
    }

    public boolean isPermanent() {
        return permanent;
    }

    public void setPermanent(boolean permanent) {
        this.permanent = permanent;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getCertOrg() {
        return certOrg;
    }

    public void setCertOrg(String certOrg) {
        this.certOrg = certOrg;
    }

    public String getCertAddress() {
        return certAddress;
    }

    public void setCertAddress(String certAddress) {
        this.certAddress = certAddress;
    }

    public String getFacePicPath() {
        return facePicPath;
    }

    public void setFacePicPath(String facePicPath) {
        this.facePicPath = facePicPath;
    }

    public String getNationPicPath() {
        return nationPicPath;
    }

    public void setNationPicPath(String nationPicPath) {
        this.nationPicPath = nationPicPath;
    }
}
