package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.ShmPushFailFlow;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ShmPushFailFlowRepository extends JpaRepository<ShmPushFailFlow, String> {

    @Query(value = """
    SELECT *  FROM shm_push_fail_flow
    WHERE  create_time < CURRENT_DATE
    AND (is_repush IS NULL OR is_repush = '')
    AND (id > :id OR :id IS NULL)
    ORDER BY id
    LIMIT :batchSize
    """, nativeQuery = true)
    List<ShmPushFailFlow> findLoanId(Long id, int batchSize);


    @Modifying
    @Transactional
    @Query(value = "UPDATE shm_push_fail_flow SET is_repush = 'Y' WHERE id IN (:ids)", nativeQuery = true)
    int updateIsRepushByIds(List<Long> ids);
}
