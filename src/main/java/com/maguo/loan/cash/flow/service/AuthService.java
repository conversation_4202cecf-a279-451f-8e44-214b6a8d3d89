package com.maguo.loan.cash.flow.service;


import com.maguo.loan.cash.flow.entity.UserAccount;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.repository.UserAccountRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

/**
 * 鉴权服务
 */
@Service
public class AuthService {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private UserAccountRepository userAccountRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;


    @Nullable
    public UserAccount getUserAccount(String userAccountToken) {
        if (userAccountToken == null || userAccountToken.isEmpty()) {
            return null;
        }
        Object accountIdObj = cacheService.get(userAccountToken);
        if (accountIdObj == null) {
            return null;
        }
        String accountId = accountIdObj.toString();
        return userAccountRepository.findById(accountId).orElse(null);
    }


    @Nullable
    public UserInfo getUserInfo(String userAccountToken) {
        UserAccount userAccount = getUserAccount(userAccountToken);
        if (userAccount == null) {
            return null;
        }
        String userId = userAccount.getUserId();
        if (userId == null) {
            return null;
        }
        return userInfoRepository.findById(userId).orElse(null);
    }

    @Nullable
    public UserInfo getUserInfoByAccount(UserAccount account) {
        String userId = account.getUserId();
        if (userId == null) {
            return null;
        }
        return userInfoRepository.findById(userId).orElse(null);
    }

}
