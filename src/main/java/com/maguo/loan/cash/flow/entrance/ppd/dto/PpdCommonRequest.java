package com.maguo.loan.cash.flow.entrance.ppd.dto;

import jakarta.validation.constraints.NotBlank;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/27 16:37
 **/
public class PpdCommonRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = -2189608329935642925L;

    /**
     * 在信也的渠道号
     */
    @NotBlank(message = "channelA 不能为空")
    private String channelA;
    /**
     * 信也在的渠道号
     */
    @NotBlank(message = "channelB 不能为空")
    private String channelB;
    /**
     * 接口ID
     */
    @NotBlank(message = "serviceId 不能为空")
    private String serviceId;
    /**
     * 流水号
     */
    @NotBlank(message = "seqNo 不能为空")
    private String seqNo;
    /**
     * 请求日期
     */
    @NotBlank(message = "transDate 不能为空")
    private String transDate;
    /**
     * 请求时间
     */
    @NotBlank(message = "transTime 不能为空")
    private String transTime;
    /**
     * 业务数据
     */
    @NotBlank(message = "data 不能为空")
    private String data;
    /**
     * 业务数据签名
     */
    @NotBlank(message = "sign 不能为空")
    private String sign;

    public String getChannelA() {
        return channelA;
    }

    public void setChannelA(String channelA) {
        this.channelA = channelA;
    }

    public String getChannelB() {
        return channelB;
    }

    public void setChannelB(String channelB) {
        this.channelB = channelB;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(String seqNo) {
        this.seqNo = seqNo;
    }

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

    public String getTransTime() {
        return transTime;
    }

    public void setTransTime(String transTime) {
        this.transTime = transTime;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
