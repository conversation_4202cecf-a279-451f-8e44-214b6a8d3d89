package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 * @since 2025-02-18
 */
public enum AppRevolvingHomeCardRouter {
    /**
     * AUDITING - 审核中
     * RISK_REJECT - 审核失败
     * LOAN_APPLY - 立即提现
     * NOT_BUY_RIGHTS - 审核中 未购买权益
     * LOANING - 放款中
     * CREDIT_FAIL - 审核失败
     * LOAN_FAIL - 审核失败
     * TO_REPAY - 立即还款
     * OVERDUE_TO_REPAY - 还款
     */
    LOAN_APPLY("可用额度(元)", null, "立即提现", null, "总额度 %s"),
    NOT_BUY_RIGHTS("可用额度(元)", null, "审核中", null, "总额度 %s"),
    LOANING("可用额度(元)", null, "放款中", null, "总额度 %s"),
    TO_REPAY("已用额度(元)", null, "还款", null, null),
    OVERDUE_TO_REPAY("今日应还(元)", "您已逾期%s天", "立即还款", null, "已产生罚息，未结清将影响信用"),
    NONE("可用额度(元)", null, null, null, null);

    private final String title;
    private final String subTitle;
    private final String buttonText;
    private final String buttonFloatText;
    private final String bottomText;

    AppRevolvingHomeCardRouter(String title, String subTitle, String buttonText, String buttonFloatText, String bottomText) {
        this.title = title;
        this.subTitle = subTitle;
        this.buttonText = buttonText;
        this.buttonFloatText = buttonFloatText;
        this.bottomText = bottomText;
    }

    public String getTitle() {
        return title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public String getButtonText() {
        return buttonText;
    }

    public String getButtonFloatText() {
        return buttonFloatText;
    }

    public String getBottomText() {
        return bottomText;
    }
}
