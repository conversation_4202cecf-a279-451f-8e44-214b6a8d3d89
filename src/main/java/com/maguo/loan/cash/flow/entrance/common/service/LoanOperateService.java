package com.maguo.loan.cash.flow.entrance.common.service;


import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiBaseConstant;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiCovert;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiEnumCovert;
import com.maguo.loan.cash.flow.entrance.common.dto.request.GenerationLoanUrlReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.request.QueryLoanReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.request.SubmitLoanReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.request.TrialReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.response.GenerationLoanUrlResDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.response.QueryLoanResDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.response.SubmitLoanResDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.response.TrialResDTO;
import com.maguo.loan.cash.flow.entrance.common.enums.SubmitLoan;
import com.maguo.loan.cash.flow.entrance.common.enums.cache.CacheKey;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiBizException;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiResultCode;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.QhBank;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.util.CommonRepayPlanCalc;
import com.maguo.loan.cash.flow.util.RepayPlanItem;
import com.maguo.loan.cash.flow.util.ThreadLocalUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

import static com.maguo.loan.cash.flow.job.susupend.SuspendActivationJob.LOCK_RELEASE_SECOND;


/**
 * 借款
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
@Service
public class LoanOperateService {

    private static final Logger logger = LoggerFactory.getLogger(LoanOperateService.class);

    @Autowired
    private OrderService orderService;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private UserBankCardRepository userBankCardRepository;


    @Autowired
    private LockService lockService;

    @Autowired
    private OrderRepository orderRepository;

    /**
     * 借款试算
     */
    public TrialResDTO trial(TrialReqDTO reqDTO) {
        TrialResDTO result = new TrialResDTO();
        FlowChannel flowChannelEnum = ThreadLocalUtil.getFlowChannel();

        // 查询订单
        Order order = orderService.query(reqDTO.getPartnerOrderNo(), null, flowChannelEnum);
        if (Objects.isNull(order)) {
            throw new CommonApiBizException(CommonApiResultCode.ORDER_NOT_EXIST);
        }
        result.setApplyPeriods(order.getApplyPeriods());
        result.setRate(order.getIrrRate());
        result.setApproveAmount(order.getApproveAmount());

        List<RepayPlanItem> repayPlanItems = CommonRepayPlanCalc.calcRepayPlan(order);
        //计划还款日
        LocalDate now = LocalDate.now();
        for (RepayPlanItem item : repayPlanItems) {
            item.setCustomRepayDate(now.plusMonths(item.getPeriod()));
        }
        // 转换为人品还款计划，并格式化金额、日期
        List<TrialResDTO.Plan> repayPlans = CommonApiCovert.INSTANCE.toPlans(repayPlanItems);
        result.setPlans(repayPlans);
        return result;
    }

    /**
     * 提交要款前置加锁
     */
    public SubmitLoanResDTO submitLoanPre(SubmitLoanReqDTO reqDTO) {
        // 加锁
        String template = CacheKey.SUBMIT_LOAN.getTemplate();
        String lockKey = MessageFormat.format(template, ThreadLocalUtil.getFlowChannel().name(), reqDTO.getPartnerOrderNo());
        Locker lock = lockService.getLock(lockKey);
        try {
            if (!lock.tryLock(Duration.ZERO, Duration.ofSeconds(LOCK_RELEASE_SECOND))) {
                throw new CommonApiBizException(CommonApiResultCode.REPEAT_SUBMIT);
            }
            return submitLoan(reqDTO);
        } catch (InterruptedException e) {
            throw new CommonApiBizException(CommonApiResultCode.SYSTEM_ERROR);
        } finally {
            lock.unlock();
        }
    }


    /**
     * 提交要款
     */
    private SubmitLoanResDTO submitLoan(SubmitLoanReqDTO reqDTO) {
        SubmitLoanResDTO result = new SubmitLoanResDTO();
        FlowChannel flowChannelEnum = ThreadLocalUtil.getFlowChannel();

        // 查询订单
        Order order = orderService.query(reqDTO.getPartnerOrderNo(), null, flowChannelEnum);
        if (Objects.isNull(order)) {
            throw new CommonApiBizException(CommonApiResultCode.ORDER_NOT_EXIST);
        }
        // 提交要款
        WhetherState rightsMarking = CommonApiEnumCovert.INSTANCE.toWhetherState(reqDTO.getRightsMarking());
        if (Objects.isNull(rightsMarking)) {
            throw new CommonApiBizException(CommonApiResultCode.PARAM_RIGHTSMARKING_FAIL);
        }
        orderService.apply(order.getId(), rightsMarking);
        result.setOrderId(order.getId());
        result.setPartnerOrderNo(reqDTO.getPartnerOrderNo());
        result.setAmount(order.getApproveAmount());
        result.setPeriod(order.getApplyPeriods());
        // 订单状态有变化，需要重新获取订单状态
        Order newOrder = orderRepository.findById(order.getId()).orElseThrow();
        if (newOrder.getOrderSubmitState() == WhetherState.Y && newOrder.getApproveRightsForce() != newOrder.getRightsMarking()) {
            result.setOrderState(SubmitLoan.SUBMIT_LOAN.name());
        } else {
            result.setOrderState(CommonApiEnumCovert.INSTANCE.toOrderStatus(newOrder.getOrderState()));
        }
        return result;
    }

    /**
     * 借款查询
     */
    public QueryLoanResDTO queryLoan(QueryLoanReqDTO reqDTO) {
        QueryLoanResDTO result = new QueryLoanResDTO();
        FlowChannel flowChannelEnum = ThreadLocalUtil.getFlowChannel();

        // 查询订单
        Order order = orderService.query(reqDTO.getPartnerOrderNo(), null, flowChannelEnum);
        if (Objects.isNull(order)) {
            throw new CommonApiBizException(CommonApiResultCode.ORDER_NOT_EXIST);
        }
        result.setRightsMarking(order.getRightsMarking().name());
        result.setRate(order.getIrrRate());
        result.setPeriod(order.getApplyPeriods());
        result.setOrderId(order.getId());
        result.setPartnerOrderNo(reqDTO.getPartnerOrderNo());
        result.setModifyTime(order.getUpdatedTime());
        if (order.getOrderState() == OrderState.AUDIT_PASS) {
            //未真正提交要款
            result.setOrderState(order.getOrderSubmitState() == WhetherState.N ? SubmitLoan.NO_SUBMIT_LOAN.name() : SubmitLoan.SUBMIT_LOAN.name());
            return result;
        } else if (order.getOrderState() == OrderState.LOAN_CANCEL) {
            result.setOrderState(order.getOrderSubmitState() == WhetherState.N ? SubmitLoan.NO_SUBMIT_LOAN.name() : OrderState.LOAN_FAIL.name());
            return result;
        }
        result.setOrderState(CommonApiEnumCovert.INSTANCE.toOrderStatus(order.getOrderState()));

        // 放款通过、结清时
        Loan loan = loanRepository.findByOrderId(order.getId());
        if (OrderState.LOAN_PASS.name().equals(result.getOrderState()) || OrderState.CLEAR.name().equals(result.getOrderState())) {
            if (Objects.isNull(loan)) {
                throw new CommonApiBizException(CommonApiResultCode.LOAN_NOT_EXIST);
            }
            result.setBankChannelName(Objects.isNull(order.getBankChannel()) ? "" : Objects.isNull(QhBank.getQhBankBy(order.getBankChannel()))
                    ? "" : QhBank.getQhBankBy(order.getBankChannel()).getBankName());
            result.setLoanAmount(loan.getAmount());
            UserBankCard userBankCard = userBankCardRepository.findUserBankCardById(loan.getRepayCardId());
            result.setBankCardId(loan.getRepayCardId());
            result.setBankCardNo(userBankCard.getCardNo());
            result.setBankName(userBankCard.getBankName());


        } else if (OrderState.LOAN_FAIL.name().equals(result.getOrderState())) {
            // 失败时
            result.setFreezeTime(order.getUpdatedTime().plusDays(CommonApiBaseConstant.DAYS_INTERVAL));
            result.setRejectReason("放款失败");
        }
        // 复借标签
        result.setReLoan(Objects.nonNull(loan) && Objects.nonNull(loan.getReloan()) ? loan.getReloan().name() : "");
        // 处理中
        return result;
    }

    /**
     * 借款链接生成
     */
    public GenerationLoanUrlResDTO getLoanUrl(GenerationLoanUrlReqDTO reqDTO) {
        return null;
    }
}
