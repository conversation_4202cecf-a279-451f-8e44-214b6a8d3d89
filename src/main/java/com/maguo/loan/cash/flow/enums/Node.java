package com.maguo.loan.cash.flow.enums;

/**
 * 渠道操作节点
 *
 * <AUTHOR>
 * @date 2024/7/2
 */
public enum Node {
    LOGIN(1, "登陆节点"),
    ENTER_ORDER(2, "app进件节点"),
    CREDIT_SUCCESS(3, "app授信成功节点"),
    LOAN_SUCCESS(4, "app要款申请节点");


    private Integer sort;

    private String description;

    Node(Integer sort, String description) {
        this.sort = sort;
        this.description = description;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
