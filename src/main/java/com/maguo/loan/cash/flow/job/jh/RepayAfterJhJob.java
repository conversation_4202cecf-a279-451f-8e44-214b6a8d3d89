package com.maguo.loan.cash.flow.job.jh;

import com.maguo.loan.cash.flow.entity.vo.RepayAfterVo;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.service.JHReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.job.jh.RepayAfterJhJob
 * @作者 Mr.sandman
 * @时间 2025/05/29 17:46
 */
@Component
@JobHandler("repayAfterJhJob")
public class RepayAfterJhJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(RepayAfterJhJob.class);

    @Value("${lvxin.loan.sftpPath}")
    private String sftpPath;

    @Autowired
    private JHReconService jhReconService;
    @Autowired
    private SftpUtils sftpUtils;

    @Override
    public void doJob( JobParam jobParam ) {
        logger.info("生成长银还款后还款计划csv开始");
        try {
            // 优先从 JobParam 获取 startDate，如果没有则使用当前日期减一天
            LocalDate localDate;
            if ( Objects.nonNull(jobParam) && jobParam.getStartDate() != null) {
                localDate = jobParam.getStartDate();
            } else {
                localDate = LocalDate.now().minusDays(1);
            }
            String  yesterday = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = "repayment_plan_" + yesterday + ".csv";
            String okFileName = "repayment_plan_" + yesterday + ".ok";
            String remoteDir = sftpPath + yesterday + "/";
            FlowChannel flowChannel = FlowChannel.getFlowChannel(jobParam.getChannel());
            logger.info("获取拍拍放款文件数据 时间: {}, 渠道: {}", localDate, flowChannel);
            // 获取数据
            List<RepayAfterVo> repayAfterVos = jhReconService.getRepayAfterReconFile(localDate,flowChannel);
            // 生成csv文件流
            ByteArrayOutputStream stream = generateCsvToStream(repayAfterVos);
            // 上传到 SFTP
            sftpUtils.uploadStreamToSftp(stream, fileName, remoteDir);
            // 上传.ok文件
            sftpUtils.uploadStreamToSftp(new ByteArrayOutputStream(), okFileName, remoteDir);
            logger.info("生成长银还款后还款计划csv上传成功");
        } catch ( Exception e ) {
            logger.error("长银还款后还款计划csv上传失败", e);
            e.printStackTrace();
        }
    }


    private static ByteArrayOutputStream generateCsvToStream( List<RepayAfterVo> data ) throws IOException {
        String[] headers = {
            "对方业务号", "借据号", "期次号", "到期日", "应还总金额", "应还本金", "应还利息", "应还罚息", "应还复利金额",
            "应还费用金额", "应还担保费金额", "应还担保费逾期费用", "实还总金额", "实还本金", "实还利息",
            "实还罚息", "实还复利金额", "实还费用金额", "实还担保费金额", "实还担保费罚息金额", "是否结清", "结清日期"
        };

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
        BufferedWriter bw = new BufferedWriter(writer);

        // 写表头
        bw.write(String.join(",", headers));
        bw.newLine();

        // 写数据
        for ( RepayAfterVo loan : data) {
            bw.write(String.join(",", Arrays.asList(
                safe(loan.getOutApplSeq()), safe(loan.getLoanNo()), safe(loan.getTnr()),
                safe(loan.getDueDt()), safe(loan.getShTotalAmt()), safe(loan.getShPrcpAmt()),
                safe(loan.getShIntAmt()), safe(loan.getShOdIntAmt()), safe(loan.getShCommIntAmt()),
                safe(loan.getShFeeAmt()), safe(loan.getShGuarAmt()), safe(loan.getShGuarOdAmt()),
                safe(loan.getAcTotalAmt()), safe(loan.getAcPrcpAmt()), safe(loan.getAcIntAmt()),
                safe(loan.getAcOdIntAmt()), safe(loan.getAcCommIntAmt()), safe(loan.getAcFeeAmt()),
                safe(loan.getAcGuarAmt()), safe(loan.getAcGuarOdAmt()), safe(loan.getIsSetl()),
                safe(loan.getSetlDt()))));
            bw.newLine();
        }

        bw.flush();
        return outputStream;
    }


    private static String safe(String val) {
        return val == null ? "" : val;
    }

    private static String safe( BigDecimal val ) {
        return val == null ? "" : val.toPlainString();
    }


}
