package com.maguo.loan.cash.flow.entrance.ppd.dto.res;

import com.maguo.loan.cash.flow.entrance.ppd.dto.CommonResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RepayQueryResponse extends CommonResult {

    /**
     * 渠道返回结果时间
     * 格式yyyyMMddHHmmss
     */
    private String resultTime;

    /**
     * 渠道扣款流水号
     */

    private String channelOrderNo;

    /**
     * 渠道失败返回码
     * 渠道方真实失败状态码
     */
    private String channelCode;

    /**
     * 渠道失败原因
     * 渠道方真实失败原因
     */
    private String channelMsg;

    /**
     * 入账信息
     */
    private List<AccountingInfo> accountingInfos;

    // getter 和 setter 方法

    public String getResultTime() {
        return resultTime;
    }

    public void setResultTime(String resultTime) {
        this.resultTime = resultTime;
    }

    public String getChannelOrderNo() {
        return channelOrderNo;
    }

    public void setChannelOrderNo(String channelOrderNo) {
        this.channelOrderNo = channelOrderNo;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getChannelMsg() {
        return channelMsg;
    }

    public void setChannelMsg(String channelMsg) {
        this.channelMsg = channelMsg;
    }

    public List<AccountingInfo> getAccountingInfos() {
        return accountingInfos;
    }

    public void setAccountingInfos(List<AccountingInfo> accountingInfos) {
        this.accountingInfos = accountingInfos;
    }

    /**
     * 入账信息类
     */
    public static class AccountingInfo {
        /**
         * 期次
         */
        private Integer term;

        /**
         * 实际入账罚息
         */
        private BigDecimal accountingOverdue = BigDecimal.ZERO;

        /**
         * 实际入账利息
         */
        private BigDecimal accountingInterest = BigDecimal.ZERO;

        /**
         * 实际入账融担费
         */
        private BigDecimal accountingZhibaoFee = BigDecimal.ZERO;

        /**
         * 实际入账咨询费
         */
        private BigDecimal accountingPoundageFee = BigDecimal.ZERO;

        /**
         * 实际入账本金
         */
        private BigDecimal accountingPrincipal = BigDecimal.ZERO;

        /**
         * 应还本金缩减金额
         */
        private BigDecimal reductPrincipal = BigDecimal.ZERO;

        /**
         * 应还利息缩减金额
         */
        private BigDecimal reductInterest = BigDecimal.ZERO;

        /**
         * 应还融担费缩减金额
         */
        private BigDecimal reductZhibaoFee = BigDecimal.ZERO;

        /**
         * 应还咨询费缩减金额
         */
        private BigDecimal reductPoundageFee = BigDecimal.ZERO;

        /**
         * 应还罚息缩减金额
         */
        private BigDecimal reductOverdue = BigDecimal.ZERO;

        // getter 和 setter 方法

        public Integer getTerm() {
            return term;
        }

        public void setTerm(Integer term) {
            this.term = term;
        }

        public BigDecimal getAccountingOverdue() {
            return accountingOverdue;
        }

        public void setAccountingOverdue(BigDecimal accountingOverdue) {
            this.accountingOverdue = accountingOverdue;
        }

        public BigDecimal getAccountingInterest() {
            return accountingInterest;
        }

        public void setAccountingInterest(BigDecimal accountingInterest) {
            this.accountingInterest = accountingInterest;
        }

        public BigDecimal getAccountingZhibaoFee() {
            return accountingZhibaoFee;
        }

        public void setAccountingZhibaoFee(BigDecimal accountingZhibaoFee) {
            this.accountingZhibaoFee = accountingZhibaoFee;
        }

        public BigDecimal getAccountingPoundageFee() {
            return accountingPoundageFee;
        }

        public void setAccountingPoundageFee(BigDecimal accountingPoundageFee) {
            this.accountingPoundageFee = accountingPoundageFee;
        }

        public BigDecimal getAccountingPrincipal() {
            return accountingPrincipal;
        }

        public void setAccountingPrincipal(BigDecimal accountingPrincipal) {
            this.accountingPrincipal = accountingPrincipal;
        }

        public BigDecimal getReductPrincipal() {
            return reductPrincipal;
        }

        public void setReductPrincipal(BigDecimal reductPrincipal) {
            this.reductPrincipal = reductPrincipal;
        }

        public BigDecimal getReductInterest() {
            return reductInterest;
        }

        public void setReductInterest(BigDecimal reductInterest) {
            this.reductInterest = reductInterest;
        }

        public BigDecimal getReductZhibaoFee() {
            return reductZhibaoFee;
        }

        public void setReductZhibaoFee(BigDecimal reductZhibaoFee) {
            this.reductZhibaoFee = reductZhibaoFee;
        }

        public BigDecimal getReductPoundageFee() {
            return reductPoundageFee;
        }

        public void setReductPoundageFee(BigDecimal reductPoundageFee) {
            this.reductPoundageFee = reductPoundageFee;
        }

        public BigDecimal getReductOverdue() {
            return reductOverdue;
        }

        public void setReductOverdue(BigDecimal reductOverdue) {
            this.reductOverdue = reductOverdue;
        }
    }
}

