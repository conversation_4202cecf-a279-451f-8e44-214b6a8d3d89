package com.maguo.loan.cash.flow.convert;


import com.maguo.loan.cash.flow.util.BaseConstants;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OcrCovert {

    int CERT_NO_CITY_LENGTH = 4;
    int CERT_NO_DISTRICT_LENGTH = 6;

    OcrCovert INSTANCE = Mappers.getMapper(OcrCovert.class);



    @Named("certEndStrCvt")
    default String certEndStrCvt(LocalDate validEndDate) {
        if (validEndDate == null) {
            //返回空
            return null;
        }
        if (validEndDate.isEqual(BaseConstants.DEFAULT_LONG_CERT_END)) {
            return "长期";
        }
        return validEndDate.format(DateTimeFormatter.ofPattern("yyyy.MM.dd"));
    }

    @Named("permanent")
    default boolean permanent(LocalDate validEndDate) {
        if (validEndDate == null) {
            //返回空
            return false;
        }
        if (validEndDate.isEqual(BaseConstants.DEFAULT_LONG_CERT_END)) {
            return true;
        }

        return false;
    }
}
