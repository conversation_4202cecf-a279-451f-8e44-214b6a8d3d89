package com.maguo.loan.cash.flow.job.inner.service;

import com.alibaba.fastjson2.JSON;

import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.maguo.loan.cash.flow.entity.ReconciliationFile;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.RecOrigin;
import com.maguo.loan.cash.flow.enums.ReccState;
import com.maguo.loan.cash.flow.remote.core.FinLoanFileService;
import com.maguo.loan.cash.flow.repository.ReconciliationFileRepository;
import com.maguo.loan.cash.flow.service.FileService;

import com.maguo.loan.cash.flow.service.WarningService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public abstract class AbstractRecDownloadService {
    private static final Logger logger = LoggerFactory.getLogger(AbstractRecDownloadService.class);

    @Autowired
    private FinLoanFileService finLoanFileService;

    @Autowired
    private WarningService warningService;

    @Autowired
    private FileService fileService;

    @Autowired
    private ReconciliationFileRepository recFileRepository;


    protected static final String SEPARATOR = "\\|";

    public void downLoad(LocalDate fileDate) {
        FileDownloadDto fileDownloadDto = new FileDownloadDto();
        fileDownloadDto.setType(com.jinghang.capital.api.dto.FileType.valueOf(this.reccType().name()));
        fileDownloadDto.setProduct(Product.ZC_CASH);
        fileDownloadDto.setFileDate(fileDate);

        // 下载内部对账文件
        RestResult<FileDownloadResultDto> restResult = finLoanFileService.download(fileDownloadDto);
        logger.info("下载内部对账文件,{},{}", JSON.toJSONString(fileDownloadDto), JSON.toJSONString(restResult));
        if (!restResult.isSuccess()) {
            warningService.warn("下载内部对账文件异常:" + JSON.toJSONString(fileDownloadDto), msg -> logger.error("下载内部对账文件异常:{}", JSON.toJSONString(restResult)));
            return;
        }
        if (Objects.isNull(restResult.getData()) || StringUtils.isAnyBlank(restResult.getData().getOssBucket(), restResult.getData().getOssPath())) {
            warningService.warn("内部对账文件不存在:" + JSON.toJSONString(fileDownloadDto), msg -> logger.error("内部对账文件不存在:{}", JSON.toJSONString(restResult)));
            return;
        }

        // 对账文件记录主表
        ReconciliationFile recFile = saveRec(fileDate, restResult.getData());

        // 对账
        if (!reccDetail(recFile)) {
            recFile.setRecState(ReccState.F);
            warningService.warn("内部对账文件未对齐:" + recFile.getId(), msg -> logger.error("内部对账文件未对齐:{}", JSON.toJSONString(restResult)));
        } else {
            // 明细全部对齐
            recFile.setRecState(ReccState.S);
        }
        recFileRepository.save(recFile);

    }

    private ReconciliationFile saveRec(LocalDate fileDate, FileDownloadResultDto resultDto) {
        ReconciliationFile recFile = new ReconciliationFile();
        recFile.setFileOrigin(RecOrigin.FIN_CORE.name());
        recFile.setFileType(this.reccType());
        recFile.setFileDate(fileDate);
        recFile.setFileName(resultDto.getFileName());
        recFile.setOssBucket(resultDto.getOssBucket());
        recFile.setOssPath(resultDto.getOssPath());
        recFile.setRecState(ReccState.P);

        return recFileRepository.save(recFile);
    }

    /**
     * 对明细
     */
    public abstract boolean reccDetail(ReconciliationFile recFile);


    public abstract FileType reccType();


    public WarningService getWarningService() {
        return warningService;
    }

    public FileService getFileService() {
        return fileService;
    }


}
