package com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay;

import com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay.common.BaseRepayRequest;

import java.math.BigDecimal;

/**
 * 还款通知请求数据传输对象
 */
public class RepayNoticeReqDTO implements BaseRepayRequest {
    /**
     * 唯一放款请求流水号
     */
    private String loanReqNo;
    /**
     * 请求方代码：CJCYDL_PPD2/CJCYDL_PPD3
     */
    private String sourceCode;
    /**
     * 唯一还款请求流水号
     */
    private String repayNo;
    /**
     * 还款模式：02-线下还款
     */
    private String repayMode;
    /**
     * 还款类型：01-按期正常还款/03-一次性提前结清/04-逾期整期还款
     */
    private String repayType;
    /**
     * 用户真实还款时间(yyyyMMddHHmmss)
     */
    private String repayTime;
    /**
     * 还款期次(结清还款传起始期次)
     */
    private Integer repayTerm;
    /**
     * 还款总金额(元)
     */
    private BigDecimal repayAmount;
    /**
     * 还款本金(元)
     */
    private BigDecimal repayPrincipal;
    /**
     * 还款利息(元)
     */
    private BigDecimal repayInterest;
    /**
     * 还款罚息(元)
     */
    private BigDecimal repayOverdue;
    /**
     * 还款手续费(元)
     */
    private BigDecimal repayPoundage;
    /**
     * 还款提结违约金(元)
     */
    private BigDecimal repayLateFee;
    /**
     * 减免申请单号
     */
    private String applyReductNo;
    /**
     * 减免总金额(元)
     */
    private BigDecimal reductAmount;
    /**
     * 减免本金(元)
     */
    private BigDecimal reductPrincipal;
    /**
     * 减免利息(元)
     */
    private BigDecimal reductInterest;
    /**
     * 减免罚息(元)
     */
    private BigDecimal reductOverdue;
    /**
     * 减免手续费(元)
     */
    private BigDecimal reductPoundage;

    @Override
    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    @Override
    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    @Override
    public String getRepayNo() {
        return repayNo;
    }

    public void setRepayNo(String repayNo) {
        this.repayNo = repayNo;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    @Override
    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    @Override
    public String getRepayTime() {
        return repayTime;
    }

    public void setRepayTime(String repayTime) {
        this.repayTime = repayTime;
    }

    @Override
    public Integer getRepayTerm() {
        return repayTerm;
    }

    public void setRepayTerm(Integer repayTerm) {
        this.repayTerm = repayTerm;
    }

    @Override
    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount(BigDecimal repayAmount) {
        this.repayAmount = repayAmount;
    }

    @Override
    public BigDecimal getRepayPrincipal() {
        return repayPrincipal;
    }

    public void setRepayPrincipal(BigDecimal repayPrincipal) {
        this.repayPrincipal = repayPrincipal;
    }

    @Override
    public BigDecimal getRepayInterest() {
        return repayInterest;
    }

    public void setRepayInterest(BigDecimal repayInterest) {
        this.repayInterest = repayInterest;
    }

    @Override
    public BigDecimal getRepayOverdue() {
        return repayOverdue;
    }

    public void setRepayOverdue(BigDecimal repayOverdue) {
        this.repayOverdue = repayOverdue;
    }

    @Override
    public BigDecimal getRepayPoundage() {
        return repayPoundage;
    }

    public void setRepayPoundage(BigDecimal repayPoundage) {
        this.repayPoundage = repayPoundage;
    }

    @Override
    public BigDecimal getRepayLateFee() {
        return repayLateFee;
    }

    public void setRepayLateFee(BigDecimal repayLateFee) {
        this.repayLateFee = repayLateFee;
    }

    public String getApplyReductNo() {
        return applyReductNo;
    }

    public void setApplyReductNo(String applyReductNo) {
        this.applyReductNo = applyReductNo;
    }

    public BigDecimal getReductAmount() {
        return reductAmount;
    }

    public void setReductAmount(BigDecimal reductAmount) {
        this.reductAmount = reductAmount;
    }

    public BigDecimal getReductPrincipal() {
        return reductPrincipal;
    }

    public void setReductPrincipal(BigDecimal reductPrincipal) {
        this.reductPrincipal = reductPrincipal;
    }

    public BigDecimal getReductInterest() {
        return reductInterest;
    }

    public void setReductInterest(BigDecimal reductInterest) {
        this.reductInterest = reductInterest;
    }

    public BigDecimal getReductOverdue() {
        return reductOverdue;
    }

    public void setReductOverdue(BigDecimal reductOverdue) {
        this.reductOverdue = reductOverdue;
    }

    public BigDecimal getReductPoundage() {
        return reductPoundage;
    }

    public void setReductPoundage(BigDecimal reductPoundage) {
        this.reductPoundage = reductPoundage;
    }
}
