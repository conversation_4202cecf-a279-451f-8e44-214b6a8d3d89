package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.enums.ApplicationSource;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface PreOrderRepository extends JpaRepository<PreOrder, String> {

    Optional<PreOrder> findByOrderNoAndFlowChannel(String orderNo, FlowChannel flowChannel);

    Optional<PreOrder> findByOrderNoAndFlowChannelAndApplicationSource(String orderNo, FlowChannel flowChannel, ApplicationSource applicationSource);

    Boolean existsByCertNoAndFlowChannel(String certNo, FlowChannel flowChannel);

    Optional<PreOrder> findByRiskId(String riskId);

    List<PreOrder> findByCertNoAndFlowChannelOrderByCreatedTime(String certNo, FlowChannel flowChannel);

    PreOrder findTopByOpenIdOrderByCreatedTimeDesc(String userId);

    List<PreOrder> findByOpenIdAndPreOrderStateNotOrderByApplyTimeDesc(String userId, PreOrderState preOrderState);

    List<PreOrder> findByApplyTimeBetweenAndPreOrderStateIn(LocalDateTime start, LocalDateTime end, List<ProcessState> init);

    Integer countByApplyTimeBetweenAndPreOrderStateIn(LocalDateTime start, LocalDateTime end, List<ProcessState> init);

    Optional<PreOrder> findByOpenIdAndFlowChannel(String userId, FlowChannel flowChannel);
    PreOrder findTopByOpenIdAndFlowChannelOrderByCreatedTimeDesc(String userId,FlowChannel flowChannel);
}
