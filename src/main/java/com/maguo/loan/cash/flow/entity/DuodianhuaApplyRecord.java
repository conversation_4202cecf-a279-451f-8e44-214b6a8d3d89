package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

@Entity
@Table(name = "duodianhua_apply_record")
public class DuodianhuaApplyRecord extends BaseEntity {
    /**
     * 主键
     */
    private String id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 基础姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号（注意X大写）
     */
    private String idCard;

    /**
     * 家庭住址，没有就默认身份证户籍地址信息
     */
    private String houseAddress;

    /**
     * 居住地省
     */
    private String liveProvince;

    /**
     * 居住地市
     */
    private String liveCity;

    /**
     * 居住地区,县，区
     */
    private String liveDistrict;

    /**
     * 现居住地址
     */
    private String liveAddress;

    /**
     * 婚配状态, 10 未婚,20 已婚,30 丧偶,40 离婚, 99未知
     */
    private String marriage;

    /**
     * 第一联系人
     */
    private String firstName;

    /**
     * 第一联系人手机号
     */
    private String firstPhone;

    /**
     * 第一联系人关系
     */
    private String firstRelation;

    /**
     * 第二联系人
     */
    private String secondName;

    /**
     * 第二联系人手机号
     */
    private String secondPhone;

    /**
     * 第二联系人关系
     */
    private String secondRelation;

    /**
     * 用户注册时间 （yyyy-MM-dd HH:mm:ss）
     */
    private String registerDateTime;

    /**
     * 申请时间 （yyyy-MM-dd HH:mm:ss）
     */
    private String applyDateTime;

    /**
     * 教育程度code
     */
    private String educationCode;

    /**
     * 家庭月收入
     */
    private String familyMonthlyIncome;

    /**
     * 个人月收入
     */
    private String selfMonthIncome;

    /**
     * 居住状态
     */
    private String livest;

    /**
     * 职业
     */
    private String job;

    /**
     * 职务
     */
    private String duty;

    /**
     * 工作性质
     */
    private String jobNature;

    /**
     * 性别
     */
    private String sex;

    /**
     * 民族
     */
    private String nation;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 身份证地址
     */
    private String address;

    /**
     * 身份证反面图片（国徽面）url
     */
    private String backFile;

    /**
     * 身份证正面图片 （人像面）url
     */
    private String frontFile;

    /**
     * 发证机关
     */
    private String issuedBy;

    /**
     * 最优活体照 url
     */
    private String natureFile;

    /**
     * 有效期
     */
    private String validDate;

    /**
     * 过期时间
     */
    private String overdueDate;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 公司省
     */
    private String companyProvince;

    /**
     * 公司市
     */
    private String companyCity;

    /**
     * 公司区
     */
    private String companyDistrict;

    /**
     * '纬度'
     */
    private String latitude;


    /**
     * '经度'
     */
    private String longitude;



    /**
     * 联系人信息json
     */
    private String contacts;


    /**
     * 设备信息json
     */
    private String deviceInfo;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getHouseAddress() {
        return houseAddress;
    }

    public void setHouseAddress(String houseAddress) {
        this.houseAddress = houseAddress;
    }

    public String getLiveProvince() {
        return liveProvince;
    }

    public void setLiveProvince(String liveProvince) {
        this.liveProvince = liveProvince;
    }

    public String getLiveCity() {
        return liveCity;
    }

    public void setLiveCity(String liveCity) {
        this.liveCity = liveCity;
    }

    public String getLiveDistrict() {
        return liveDistrict;
    }

    public void setLiveDistrict(String liveDistrict) {
        this.liveDistrict = liveDistrict;
    }

    public String getLiveAddress() {
        return liveAddress;
    }

    public void setLiveAddress(String liveAddress) {
        this.liveAddress = liveAddress;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getFirstPhone() {
        return firstPhone;
    }

    public void setFirstPhone(String firstPhone) {
        this.firstPhone = firstPhone;
    }

    public String getFirstRelation() {
        return firstRelation;
    }

    public void setFirstRelation(String firstRelation) {
        this.firstRelation = firstRelation;
    }

    public String getSecondName() {
        return secondName;
    }

    public void setSecondName(String secondName) {
        this.secondName = secondName;
    }

    public String getSecondPhone() {
        return secondPhone;
    }

    public void setSecondPhone(String secondPhone) {
        this.secondPhone = secondPhone;
    }

    public String getSecondRelation() {
        return secondRelation;
    }

    public void setSecondRelation(String secondRelation) {
        this.secondRelation = secondRelation;
    }

    public String getRegisterDateTime() {
        return registerDateTime;
    }

    public void setRegisterDateTime(String registerDateTime) {
        this.registerDateTime = registerDateTime;
    }

    public String getApplyDateTime() {
        return applyDateTime;
    }

    public void setApplyDateTime(String applyDateTime) {
        this.applyDateTime = applyDateTime;
    }

    public String getEducationCode() {
        return educationCode;
    }

    public void setEducationCode(String educationCode) {
        this.educationCode = educationCode;
    }

    public String getFamilyMonthlyIncome() {
        return familyMonthlyIncome;
    }

    public void setFamilyMonthlyIncome(String familyMonthlyIncome) {
        this.familyMonthlyIncome = familyMonthlyIncome;
    }

    public String getSelfMonthIncome() {
        return selfMonthIncome;
    }

    public void setSelfMonthIncome(String selfMonthIncome) {
        this.selfMonthIncome = selfMonthIncome;
    }

    public String getLivest() {
        return livest;
    }

    public void setLivest(String livest) {
        this.livest = livest;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getDuty() {
        return duty;
    }

    public void setDuty(String duty) {
        this.duty = duty;
    }

    public String getJobNature() {
        return jobNature;
    }

    public void setJobNature(String jobNature) {
        this.jobNature = jobNature;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBackFile() {
        return backFile;
    }

    public void setBackFile(String backFile) {
        this.backFile = backFile;
    }

    public String getFrontFile() {
        return frontFile;
    }

    public void setFrontFile(String frontFile) {
        this.frontFile = frontFile;
    }

    public String getIssuedBy() {
        return issuedBy;
    }

    public void setIssuedBy(String issuedBy) {
        this.issuedBy = issuedBy;
    }

    public String getNatureFile() {
        return natureFile;
    }

    public void setNatureFile(String natureFile) {
        this.natureFile = natureFile;
    }

    public String getValidDate() {
        return validDate;
    }

    public void setValidDate(String validDate) {
        this.validDate = validDate;
    }

    public String getOverdueDate() {
        return overdueDate;
    }

    public void setOverdueDate(String overdueDate) {
        this.overdueDate = overdueDate;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyProvince() {
        return companyProvince;
    }

    public void setCompanyProvince(String companyProvince) {
        this.companyProvince = companyProvince;
    }

    public String getCompanyCity() {
        return companyCity;
    }

    public void setCompanyCity(String companyCity) {
        this.companyCity = companyCity;
    }

    public String getCompanyDistrict() {
        return companyDistrict;
    }

    public void setCompanyDistrict(String companyDistrict) {
        this.companyDistrict = companyDistrict;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
}
