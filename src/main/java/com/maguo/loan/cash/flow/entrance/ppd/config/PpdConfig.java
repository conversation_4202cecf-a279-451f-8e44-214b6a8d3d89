package com.maguo.loan.cash.flow.entrance.ppd.config;

import com.jinghang.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 拍拍贷加解密配置
 */
@Configuration
public class PpdConfig {

    /**
     * 客户端公钥
     */
    @Value("${ppd.client.publicKey}")
    private String clientPublicKey;

    /**
     * 服务端私钥
     */
    @Value("${ppd.server.privateKey}")
    private String serverPrivateKey;

    /**
     * 拍拍请求地址
     */
    @Value("${ppd.request.url}")
    private String ppdRequestUrl;


    @Value("${ppd.sftp.zzln.user}")
    private String sftpUser;
    @Value("${ppd.sftp.zzln.password}")
    private String sftpPassword;
    @Value("${ppd.sftp.zzln.host}")
    private String sftpHost;
    @Value("${ppd.sftp.zzln.port}")
    private Integer sftpPort;
    @Value("${ppd.sftp.dir.down}")
    private String sftpDownloadPath;
    @Value("${ppd.sftp.credit.apply.down}")
    private String sftpCreditApplyDownloadPath;

    @Value("${ppd.sftp.dir.upload}")
    private String sftpUploadPath;

    /**
     * 是否跳过验签
     */
    @Value("${ppd.skipSignVerify:false}")
    private Boolean skipSignVerify;

    @Value("${ppd.sys.time}")
    private String rlSysTime;

    @Value("${ppd.sysTime.mock}")
    private String rlSysTimeMock;
    public LocalDateTime isMockTime(LocalDateTime time) {
        return "true".equals(rlSysTimeMock) ? LocalDateTime.parse(rlSysTime, DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) : time;
    }

    public String getRlSysTime() {
        return rlSysTime;
    }

    public String getRlSysTimeMock() {
        return rlSysTimeMock;
    }

    public String getSftpUser() {
        return sftpUser;
    }

    public String getSftpPassword() {
        return sftpPassword;
    }

    public String getSftpHost() {
        return sftpHost;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public String getSftpDownloadPath() {
        return sftpDownloadPath;
    }

    public String getSftpUploadPath() {
        return sftpUploadPath;
    }

    public Boolean getSkipSignVerify() {
        return skipSignVerify;
    }

    public String getClientPublicKey() {
        return clientPublicKey;
    }

    public String getServerPrivateKey() {
        return serverPrivateKey;
    }


    public String getPpdRequestUrl() {
        return ppdRequestUrl;
    }

    public String getSftpCreditApplyDownloadPath() {
        return sftpCreditApplyDownloadPath;
    }

}
