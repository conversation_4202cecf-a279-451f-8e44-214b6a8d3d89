package com.maguo.loan.cash.flow.controller;

import com.maguo.loan.cash.flow.remote.core.FinHXBKCallbackApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * HXBK回调统一入口Controller
 * 接收外部HXBK的回调请求，透传到Capital模块处理
 *
 * @Author: Lior
 * @CreateTime: 2025/7/10 22:30
 */
@RestController
@RequestMapping("/hxbk/callback")
public class HXBKCallbackController {

    private static final Logger logger = LoggerFactory.getLogger(HXBKCallbackController.class);

    @Autowired
    private FinHXBKCallbackApiService finHXBKCallbackService;

    /**
     * HXBK统一回调入口
     * 接收外部HXBK的所有类型回调请求，透传到Capital模块处理
     *
     * @param requestBody 回调请求体
     * @return 回调响应
     */
    @PostMapping("/handle")
    public String handle(@RequestBody String requestBody) {
        logger.info("收到HXBK回调请求，请求长度: {}", requestBody.length());

        try {
            String response = finHXBKCallbackService.handle(requestBody);
            logger.info("HXBK回调处理完成");
            return response;
        } catch (Exception e) {
            logger.error("HXBK回调处理异常", e);
            throw e;
        }
    }
}
