package com.maguo.loan.cash.flow.entrance.lvxin.controller;


import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.contract.ContractRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.contract.ContractResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.service.LvxinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("lvxin")
public class LvxinContractController {

    @Autowired
    private LvxinService lvxinService;

    /**
     * 获取协议
     */
    @PostMapping("/api/partner/v1/queryContracts")
    public LvxinResponse getContract(@RequestBody ContractRequest contractRequest) {
        ContractResponse contractResponses = lvxinService.getContract(contractRequest);
        return LvxinResponse.success(contractResponses);
    }
}
