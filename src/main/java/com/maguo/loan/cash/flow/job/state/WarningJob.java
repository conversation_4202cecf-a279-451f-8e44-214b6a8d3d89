package com.maguo.loan.cash.flow.job.state;


import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.BankRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRecordRepository;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.util.DateUtil;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
@JobHandler("warningJob")
public class WarningJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(WarningJob.class);

    @Resource(name = "warningStateService")
    private WarningService warningService;

    @Autowired
    private LoanRecordRepository loanRecordRepository;

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;

    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;

    /**
     * 达到阈值时@指定人
     */
    @Value("${warning.serviceData.at:}")
    private String warningAt;


    @Value("${warningJob.minusHours}")
    private Long subtractionHours;

    @Value("${warningJob.size}")
    private Integer size;

    private static final int ONE_HUNDRED = 100;

    private static final int THREE_HUNDRED = 300;


    @Override
    public void doJob(JobParam jobParam) {
        logger.info("warningJob start");

        boolean isAt = false;

        LocalDateTime end = LocalDateTime.now().minusHours(1L);
        LocalDateTime start = end.minusHours(subtractionHours);
        logger.info(" 查询开始时间为:{},结束时间为:{}", start, end);
        StringBuilder sb = new StringBuilder();
        /*
        对资还款记录
         */
        Integer bankRepayRecord = bankRepayRecordRepository
            .countByCreatedTimeBetweenAndStateIn(start, end, List.of(ProcessState.INIT, ProcessState.PROCESSING));

        if (bankRepayRecord >= size) {
            sb.append(message(start, end, "bank_repay_record", bankRepayRecord)).append("\n").append("\n");

            if (bankRepayRecord >= ONE_HUNDRED) {
                isAt = true;
            }
        }
        /*
        授信记录
         */
        Integer credit = creditRepository
            .countByApplyTimeBetweenAndStateIn(start, end, List.of(ProcessState.INIT, ProcessState.PROCESSING));

        if (credit >= size) {
            sb.append(message(start, end, "credit", credit)).append("\n").append("\n");

            if (credit >= ONE_HUNDRED) {
                isAt = true;
            }
        }
        /*
        对客还款记录
         */
        Integer customRepayRecord = customRepayRecordRepository
            .countByRepayApplyDateBetweenAndRepayStateIn(start, end, List.of(ProcessState.INIT, ProcessState.PROCESSING));

        if (customRepayRecord >= size) {
            sb.append(message(start, end, "custom_repay_record", customRepayRecord)).append("\n").append("\n");

            if (customRepayRecord >= THREE_HUNDRED) {
                isAt = true;
            }
        }
        /*
        放款记录
         */

        Integer loan = loanRecordRepository
            .countByApplyTimeBetweenAndLoanStateIn(start, end, List.of(ProcessState.INIT, ProcessState.PROCESSING));

        if (loan >= size) {
            sb.append(message(start, end, "loan_record", loan));

            if (loan >= ONE_HUNDRED) {
                isAt = true;
            }
        }

        if (!sb.isEmpty()) {
            if (isAt) {
                warningService.warn(sb.toString(), warningAt.split(","));
            } else {
                warningService.warn(sb.toString());
            }
        }

        logger.info("warningJob end");
    }

    public String message(LocalDateTime start, LocalDateTime end, String table, Integer number) {
        StringBuilder string = new StringBuilder();
        string.append(DateUtil.formatLocalDateTime(start)).append(" ~ ").append(DateUtil.formatLocalDateTime(end))
            .append("\n")
            .append("cash-business.").append(table).append("不是最终态条数为").append(number);
        return string.toString();
    }
}
