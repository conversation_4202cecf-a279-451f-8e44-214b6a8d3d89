package com.maguo.loan.cash.flow.repository;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.maguo.loan.cash.flow.entity.WithholdAccountConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 */
public interface WithholdAccountConfigRepository extends JpaRepository<WithholdAccountConfig, String> {

    WithholdAccountConfig findByBankChannelAndFlowChannelAndGuaranteeCompany(BankChannel bankChannel, FlowChannel flowChannel, GuaranteeCompany guaranteeCompany);

}
