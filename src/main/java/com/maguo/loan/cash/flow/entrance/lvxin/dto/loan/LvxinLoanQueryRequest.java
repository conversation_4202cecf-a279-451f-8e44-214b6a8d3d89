package com.maguo.loan.cash.flow.entrance.lvxin.dto.loan;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 查询放款结果
 * @Date 2024/5/22 14:35
 * @Version v1.0
 **/
public class LvxinLoanQueryRequest {

    private String userId;
    /**
     * 绿信放款订单号
     */
    @NotBlank(message = "放款订单号不能为空")
    private String loanGid;
    /**
     * loanId
     */
    @NotBlank(message = "请求流水号不能为空")
    private String partnerOrderNo;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public @NotBlank(message = "放款订单号不能为空")String getLoanGid() {
        return loanGid;
    }

    public void setLoanGid(@NotBlank(message = "放款订单号不能为空")String loanGid) {
        this.loanGid = loanGid;
    }

    public @NotBlank(message = "请求流水号不能为空")String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(@NotBlank(message = "请求流水号不能为空")String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }
}
