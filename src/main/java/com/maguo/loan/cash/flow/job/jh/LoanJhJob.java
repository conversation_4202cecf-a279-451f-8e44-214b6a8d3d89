package com.maguo.loan.cash.flow.job.jh;

import com.maguo.loan.cash.flow.entity.vo.LoanVo;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.service.JHReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.job.jh.LoanJob
 * @作者 Mr.sandman
 * @时间 2025/05/29 15:48
 */
@Component
@JobHandler("loanJhJob")
public class LoanJhJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(LoanJhJob.class);

    @Value("${lvxin.loan.sftpPath}")
    private String sftpPath;

    @Autowired
    private JHReconService jhReconService;
    @Autowired
    private SftpUtils sftpUtils;

    @Override
    public void doJob( JobParam jobParam ) {
        logger.info("生成长银借款明细csv开始");
        try {
            // 优先从 JobParam 获取 startDate，如果没有则使用当前日期减一天
            LocalDate localDate;
            if ( Objects.nonNull(jobParam) && jobParam.getStartDate() != null) {
                localDate = jobParam.getStartDate();
            } else {
                localDate = LocalDate.now().minusDays(1);
            }
            String  yesterday = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = "loan_" + yesterday + ".csv";
            String okFileName = "loan_" + yesterday + ".ok";
            String remoteDir = sftpPath + yesterday + "/";
            FlowChannel flowChannel = FlowChannel.getFlowChannel(jobParam.getChannel());
            logger.info("获取拍拍放款文件数据 时间: {}, 渠道: {}", localDate, flowChannel);
            // 获取数据
            List<LoanVo> loanVos = jhReconService.getLoanDetailReconFile(localDate,flowChannel);
            // 生成内存中的csv文件 字节流
            ByteArrayOutputStream stream = generateCsvToStream(loanVos);
            // 上传到 SFTP
            sftpUtils.uploadStreamToSftp(stream, fileName, remoteDir);
            // 上传.ok文件
            sftpUtils.uploadStreamToSftp(new ByteArrayOutputStream(), okFileName, remoteDir);
            logger.info("长银借款明细csv上传成功");
        } catch (Exception e) {
            logger.error("长银借款明细csv上传失败:", e);
            e.printStackTrace();
        }


    }


    private static ByteArrayOutputStream generateCsvToStream(List<LoanVo> data) throws IOException {
        String[] headers = {
            "对方业务号", "客户编号", "用款申请日期", "用款申请流水号", "合同号", "借据号",
            "放款金额", "申请期限", "年化利率", "放款时间", "合同到期日", "放款账户姓名",
            "放款账户开户行", "放款账号"
        };

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
        BufferedWriter bw = new BufferedWriter(writer);

        // 写表头
        bw.write(String.join(",", headers));
        bw.newLine();

        // 写数据（你替换的逻辑）
        for (LoanVo loan : data) {
            bw.write(String.join(",", Arrays.asList(
                safe(loan.getOutAppSeq()), safe(loan.getCustId()), safe(loan.getApplyDt()),
                safe(loan.getApplSeq()), safe(loan.getContNo()), safe(loan.getLoanNo()),
                safe(loan.getDnAmt()), safe(loan.getApplyTnr().toString()), safe(loan.getBasicIntRat()),
                safe(loan.getLoanActvDt()), safe(loan.getContEndDt()), safe(loan.getAcctName()),
                safe(loan.getAcctBank()), safe(loan.getAcctNo()))));
            bw.newLine();
        }

        bw.flush();
        return outputStream;
    }

    // 处理 null 的字段
    private static String safe(String val) {
        return val == null ? "" : val;
    }

    private static String safe( BigDecimal val ) {
        return val == null ? "" : val.toPlainString();
    }

}
