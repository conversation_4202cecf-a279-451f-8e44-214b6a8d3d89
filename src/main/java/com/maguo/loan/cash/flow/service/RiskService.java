package com.maguo.loan.cash.flow.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.credit.PreCreditApplyDto;
import com.jinghang.capital.api.dto.credit.PreCreditApplyResultDto;
import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.io.IoUtil;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.config.RiskConfig;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.CollisionMarketingRecord;
import com.maguo.loan.cash.flow.entity.CollisionRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.LvxinApplyRecord;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.UserContactInfo;
import com.maguo.loan.cash.flow.entity.UserDevice;
import com.maguo.loan.cash.flow.entity.UserFace;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.UserRenewedRecord;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entity.ppd.PpdCreditApplyRecord;
import com.maguo.loan.cash.flow.entity.ppd.PpdLoanApplyRecord;
import com.maguo.loan.cash.flow.entrance.common.constant.CommonBaseConstant;
import com.maguo.loan.cash.flow.enums.AmountType;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.ApplyType;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.CYBKResultType;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.FlowType;
import com.maguo.loan.cash.flow.enums.Industry;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.MarkStatusFlow;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.remote.core.FinCreditService;
import com.maguo.loan.cash.flow.remote.riskdata.req.RiskCallbackRequest;
import com.maguo.loan.cash.flow.remote.riskdata.req.RiskDataRequest;
import com.maguo.loan.cash.flow.remote.riskdata.resp.RiskDataResponse;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.AreaCodeRepository;
import com.maguo.loan.cash.flow.repository.CollisionMarketingRecordRepository;
import com.maguo.loan.cash.flow.repository.CollisionMarketingSuccessRecordRepository;
import com.maguo.loan.cash.flow.repository.CollisionRecordRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.LvxinApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.repository.RiskRevolvingRecordRepository;
import com.maguo.loan.cash.flow.repository.UserAccountRepository;
import com.maguo.loan.cash.flow.repository.UserBankCardRepository;
import com.maguo.loan.cash.flow.repository.UserContactInfoRepository;
import com.maguo.loan.cash.flow.repository.UserDeviceRepository;
import com.maguo.loan.cash.flow.repository.UserFaceRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.repository.UserInfoExpandRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.repository.UserOcrRepository;
import com.maguo.loan.cash.flow.repository.UserRenewedRecordRepository;
import com.maguo.loan.cash.flow.repository.UserRenewedTagRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.repository.ppd.PpdCreditApplyRecordRepository;
import com.maguo.loan.cash.flow.repository.ppd.PpdLoanApplyRecordRepository;
import com.maguo.loan.cash.flow.service.client.RiskControlClient;
import com.maguo.loan.cash.flow.service.event.RiskResultEvent;
import com.maguo.loan.cash.flow.util.ChineseAddressParser;
import com.maguo.loan.cash.flow.util.ImageUtil;
import jakarta.annotation.PostConstruct;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 风控服务
 */
@Service
public class RiskService {

    private static final Logger logger = LoggerFactory.getLogger(RiskService.class);


    private static final int IMG_MAX_SIZE = 500 * 1024;

    /**
     * 价格风控规则名
     */
    private static final String AMOUNT_RULE_NAME = "计算单元0";

    /**
     * 权益风控规则
     * 1. 低  2. 高
     */
    private static final String RIGHT_RULE_NAME = "计算单元1";

    /**
     * 利率风控规则
     * 1. 低  2. 高
     */
    private static final String RATE_RULE_NAME = "计算单元2";
    /**
     * 是否强制购买权益
     * 0.不强制购买权益
     * 1.强制购买权益
     */
    private static final String APPROVE_RIGHTS_FORCE = "计算单元3";

    /**
     * 风控审批期数,可以为多个,逗号分割
     */
    private static final String RISK_PERIOD = "计算单元4";

    /**
     * 权益包
     */
    private static final String RIGHTS_PACKAGE = "计算单元5";

    /**
     * 权益金额,可以为多个,逗号分割
     */
    private static final String RIGHTS_AMOUNTS = "计算单元6";

    /**
     * 客户风险评分
     * 风控输出的客户风险评分，评分区间[0,600]，以应对资方要求必传客户评分和等级的要求
     */
    private static final String APPROVE_SCORE = "计算单元7";
    /**
     * 用户风险等级
     */
    private static final String RISK_LEVEL = "计算单元8";

    /**
     * A卡评分（欺诈风险）
     */
    private static final String A_CARD_FRACTION = "计算单元12";
    /**
     * 用信意愿度（分）
     */
    private static final String WILLINGNESS = "计算单元13";
    /**
     * 客户分层标签
     */
    private static final String CUSTOMER_TIERED_LABELS = "计算单元14";

    /**
     * 额度类型
     */
    private static final String AMOUNT_TYPE = "计算单元15";

    private static final String RIGHTS_PAY_TYPE = "计算单元16";

    private static final Integer ONE = 1;
    private static final Integer TWO = 2;
    private static final Integer THREE = 3;
    private static final Integer FOUR = 4;


    private static final int MIN_CREDIT_AMT = 0;
    private static final int MAX_CREDIT_AMT = 50000;
    private static final int DAYS_INTERVAL = 30; // 时间间隔常量

    private static final String DEFAULT_RENEWED_TYPE = "-999";


    private static List<FileType> fileTypes = List.of(FileType.ID_FACE, FileType.ID_NATION, FileType.ID_HEAD);

    @Autowired
    private RiskConfig riskConfig;

    @Autowired
    private MqService mqService;

    @Autowired
    private RiskControlClient riskControlClient;


    @Autowired
    private CollisionRecordRepository collisionRecordRepository;

    @Autowired
    private UserRiskRecordRepository riskRecordRepository;

    @Autowired
    private AgreementSignatureRecordRepository signatureRecordRepository;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private UserOcrRepository userOcrRepository;

    @Autowired
    private UserFaceRepository userFaceRepository;

    @Autowired
    private UserDeviceRepository userDeviceRepository;

    @Autowired
    private UserContactInfoRepository userContactInfoRepository;

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private FileService ossFileService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private WarningService warningService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;


    @Autowired
    private UserAccountRepository userAccountRepository;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Autowired
    private AreaCodeRepository areaCodeRepository;
    @Autowired
    private CollisionMarketingRecordRepository collisionApplyRecordRepository;
    @Autowired
    private CollisionMarketingSuccessRecordRepository collisionMarketingSuccessRecordRepository;
    @Autowired
    private UserInfoExpandRepository userInfoExpandRepository;

    @Autowired
    private CheckService checkService;


    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;
    @Autowired
    private CreditRepository creditRepository;
    @Autowired
    private OrderRepository orderRepository;


    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private UserRenewedRecordRepository userRenewedRecordRepository;

    @Autowired
    private UserRenewedTagRepository userRenewedTagRepository;

    @Autowired
    private RiskRevolvingRecordRepository riskRevolvingRecordRepository;

    @Autowired
    private LvxinApplyRecordRepository lvxinApplyRecordRepository;

    @Autowired
    private PpdCreditApplyRecordRepository ppdCreditApplyRecordRepository;


    @Autowired
    private UserBankCardRepository userBankCardRepository;

    @Autowired
    private OrderService orderService;


    /**
     * 续借标识查询开关
     */
    @Value("${qh.renewed.query.open:false}")
    private boolean renewedOpen;

    @Value("${qh.renewed.query.rate.limit:1}")
    private int renewedRateLimit;

    @Value("${qh.renewed.query.rate.period:3}")
    private int renewedRatePeriod;

    /**
     * 发起风控前部分参数特殊字符转换规则
     * 正则表达式
     */
    private static final String SYMBOLS = "[~!！@$%^&*\\[\\]{}<>？?=+￥【】……、#/\\\\.,。*_《》\\s]";
    private static final Pattern SYMBOL_PATTERN = Pattern.compile("^" + SYMBOLS + "+$");

    /**
     * 是否是优质客户
     */
    public static final String QUALITY_FIELD_NAME = "计算单元0";

    @Autowired
    private RedissonClient redissonClient;

    private RRateLimiter rateLimiter;
    @Autowired
    private PreOrderRepository preOrderRepository;
    @Autowired
    private FinCreditService finCreditService;

    private static final int RENEWED_EXPIRE_DAYS = 30;

    private static final BigDecimal MAX_SUBSCRIBE_AMOUNT = new BigDecimal(100);
    @Autowired
    private PpdLoanApplyRecordRepository ppdLoanApplyRecordRepository;


    @PostConstruct
    public void init() {
        rateLimiter = redissonClient.getRateLimiter("qh_renewed_query_rate_limiter");
        rateLimiter.setRate(RateType.OVERALL, renewedRateLimit, renewedRatePeriod, RateIntervalUnit.SECONDS);
    }

    /**
     * 用户装库
     *
     * @param phoneMd5  手机md5
     * @param certNoMd5 身份证md5
     * @param nameMd5   姓名md5
     * @return 装库状态
     */
    public CollisionRecord userCheck(String phoneMd5, String certNoMd5, String nameMd5, FlowChannel channel) {
        CollisionRecord newRecord = new CollisionRecord();
        newRecord.setPhone(phoneMd5);
        newRecord.setCertNo(certNoMd5);
        newRecord.setName(nameMd5);
        newRecord.setState(AuditState.INIT);
        newRecord.setFlowChannel(channel);
        //调用资方撞库，获取撞库返回信息
        RestResult<PreCreditApplyResultDto> restResult = new RestResult<>();
        try {
            // 调用fin-core试算
            PreCreditApplyDto preCreditApply = new PreCreditApplyDto();
            preCreditApply.setMobile(phoneMd5);
            preCreditApply.setCardNo(certNoMd5);
            preCreditApply.setBankChannel(BankChannel.CYBK);
            restResult = finCreditService.preCredit(preCreditApply);
            logger.info("请求fin-core撞库结果 : {}", JsonUtil.toJsonString(restResult));
        } catch (Exception e) {
            logger.error("请求fin-core撞库异常", e);
            throw new BizException(ResultCode.BIZ_ERROR);
        }
        if (restResult.isSuccess()) {
            // 更新原因
            PreCreditApplyResultDto resultDto = restResult.getData();
            if (CYBKResultType.REJECTED.getCode().equals(resultDto.getRuleCode())) {
                newRecord.setState(AuditState.REJECT);
                newRecord.setFailReason(resultDto.getRuleDesc());
            } else {
                newRecord.setState(AuditState.PASS);
                newRecord.setFailReason(resultDto.getRuleDesc());
            }
        }
        CollisionRecord saved = collisionRecordRepository.save(newRecord);
        return saved;
    }

    private boolean hasFailedRecordsInLast30Days(String userId, CollisionRecord collisionRecord) {
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(DAYS_INTERVAL);
        List<UserRiskRecord> userRiskRecords = userRiskRecordRepository.findAllByUserIdAndAmountTypeNot(userId, AmountType.REVOLVING);
        List<Order> orders = orderRepository.findAllByUserId(userId);


        //当前流量为irr36时，校验30天是否内风控拒过
        //当前流量为irr36+权益时，校验30天是否被irr36+权益拒过
        if (FlowType.IRR36 == collisionRecord.getFlowChannel().getFlowType()) {
            boolean isRejected =
                userRiskRecords.stream().anyMatch(record -> record.getApproveResult() == AuditState.REJECT && record.getCreatedTime().isAfter(thirtyDaysAgo));
            if (isRejected) {
                collisionRecord.setFailReason("存在30天内风控拒绝记录");
                return true;
            }
        } else if (FlowType.IRR36_RIGHTS == collisionRecord.getFlowChannel().getFlowType()) {
            boolean isRejected =
                userRiskRecords.stream().anyMatch(record -> record.getApproveResult() == AuditState.REJECT
                    && record.getFlowChannel().getFlowType() == FlowType.IRR36_RIGHTS
                    && record.getCreatedTime().isAfter(thirtyDaysAgo));
            if (isRejected) {
                collisionRecord.setFailReason("存在30天内风控拒绝记录");
                return true;
            }
        }

        for (Order order : orders) {
            if (order.getOrderState() == OrderState.CREDIT_FAIL && order.getUpdatedTime().isAfter(thirtyDaysAgo)) {
                collisionRecord.setFailReason("存在30天内授信失败记录");
                return true;
            }
        }

        return false;
    }


    private static final String QH_PROD_CODE = "15";


    /**
     * 用户装库(对接广告流量的撞库)
     *
     * @param phoneMd5  手机md5
     * @param certNoMd5 身份证md5
     * @return 装库状态
     */
    public boolean userCollisionMarketing(String phoneMd5, String certNoMd5, ApplyChannel applyChannel, FlowChannel channel) {
        CollisionMarketingRecord marketingRecord = new CollisionMarketingRecord();
        marketingRecord.setPhone(phoneMd5);
        marketingRecord.setCertNo(certNoMd5);
        marketingRecord.setState(AuditState.INIT);
        marketingRecord.setApplyChannel(applyChannel);
        marketingRecord.setFlowChannel(channel);
        CollisionMarketingRecord saved = collisionApplyRecordRepository.save(marketingRecord);

        return remoteCollisionCheck(saved);
    }


    private boolean remoteCollisionCheck(CollisionMarketingRecord record) {


        return record.getState() == AuditState.PASS;
    }

    public void platformRisk(final UserRiskRecord record) throws HttpException, JsonProcessingException {
        boolean allSuccess = isRiskAgreementAllSuccess(record);
        logger.info("用户风控表 id: [{}],  所有签章结果: [{}]", record.getId(), allSuccess);
        if (allSuccess) {
            // 开始走风控系统
            platformRiskInterAsync(record);
        } else {
            mqService.submitRiskApplyDelay(record.getId());
        }
    }

    /**
     * 放款风控处理
     *
     * @param record
     */
    public void platformLoanRisk(final UserRiskRecord record) {
        // 开始走放款风控
        try {
            platformRiskLoanInterAsync(record);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 内部风控请求（异步）
     *
     * @param record 风控记录
     */
    private void platformRiskInterAsync(final UserRiskRecord record) throws HttpException, JsonProcessingException {
        logger.info("平台风控开始, 启用标识: [{}] 用户风控记录id: [{}]", riskConfig.getRiskEnable(), record.getId());

        AuditState riskState = AuditState.AUDITING;
        if (riskConfig.getRiskEnable()) {
            final String userId = record.getUserId();
            UserInfo userInfo = userInfoRepository.findById(userId).orElseThrow(() -> new BizException(ResultCode.USER_NOT_EXIST));
            RiskDataRequest request = buildRiskCreditApplyRequest(record, userInfo);
            logger.info("平台风控开始, 请求参数: [{}]", JSON.toJSONString(request));
            String res = HttpUtil.post(riskConfig.getRiskUrl(), JSON.toJSONString(request));
            RiskDataResponse riskResult = JsonUtil.convertToObject(res, RiskDataResponse.class);
            logger.info("平台风控返回： [{}]", JSON.toJSONString(riskResult));

            if (CommonBaseConstant.RISK_CODE_200.equals(riskResult.getCode())) {
                String result = riskResult.getResult().getAsyn_commit_status();
                record.setPipelineId(riskResult.getResult().getPipeline_id());
                if (!"SUCCESS".equals(result)) {
                    riskState = AuditState.REJECT;
                    record.setRiskFinalResult(riskResult.getMessage());
                }
            } else {
                logger.info("风控审核接口请求异常 code: [{}] userRecordId: [{}]", riskResult.getCode(), userId);
                riskState = AuditState.REJECT;
                record.setRiskFinalResult(riskResult.getMessage());
            }
        } else {
            riskState = AuditState.PASS;
        }
        record.setApproveResult(riskState);
        riskRecordRepository.save(record);
        if (record.getApproveResult() == AuditState.PASS) {
            //推送风控通过事件
            eventPublisher.publishEvent(new RiskResultEvent(record.getId()));
        }
        if (record.getApproveResult() == AuditState.REJECT) {
            //推送风控拒绝事件
            eventPublisher.publishEvent(new RiskResultEvent(record.getId()));
        }
    }


    /**
     * 内部放款风控请求（异步）
     *
     * @param record 风控记录
     */
    private void platformRiskLoanInterAsync(final UserRiskRecord record) throws HttpException, JsonProcessingException {
        logger.info("平台风控开始, 启用标识: [{}] 用户风控记录id: [{}]", riskConfig.getRiskEnable(), record.getId());

        AuditState riskState = AuditState.AUDITING;
        final String userId = record.getUserId();
        UserInfo userInfo = userInfoRepository.findById(userId).orElseThrow(() -> new BizException(ResultCode.USER_NOT_EXIST));
        RiskDataRequest request = buildRiskLoanApplyRequest(record, userInfo);
        String loanReqNo = String.valueOf(request.getInputParamMap().get("loanReqNo"));
        if (riskConfig.getRiskEnable()) {
            logger.info("平台风控开始, 请求参数: [{}]", JSON.toJSONString(request));
            String res = HttpUtil.post(riskConfig.getRiskUrl(), JSON.toJSONString(request));
            RiskDataResponse riskResult = JsonUtil.convertToObject(res, RiskDataResponse.class);
            logger.info("平台风控返回： [{}]", JSON.toJSONString(riskResult));

            if (CommonBaseConstant.RISK_CODE_200.equals(riskResult.getCode())) {
                String result = riskResult.getResult().getAsyn_commit_status();
                record.setPipelineId(riskResult.getResult().getPipeline_id());
                if (!"SUCCESS".equals(result)) {
                    riskState = AuditState.REJECT;
                    record.setRiskFinalResult(riskResult.getMessage());
                }
            } else {
                logger.info("风控审核接口请求异常 code: [{}] userRecordId: [{}]", riskResult.getCode(), userId);
                riskState = AuditState.REJECT;
                record.setRiskFinalResult(riskResult.getMessage());
            }
        } else {
            riskState = AuditState.PASS;
        }

        record.setApproveResult(riskState);
        riskRecordRepository.save(record);
        Order order = orderRepository.findTopByOuterOrderIdAndFlowChannelAndApplyChannel(loanReqNo, FlowChannel.PPCJDL, record.getApplyChannel());
        if (record.getApproveResult() == AuditState.PASS) {
            //推送风控通过事件 - 路由资金方推送授信放款
            orderService.orderRoute(order);
        }
    }


    private RiskDataRequest buildRiskCreditApplyRequest(UserRiskRecord record, UserInfo userInfo) {

        if (null == record.getFlowChannel() || null == record.getApplyChannel()) {
            logger.error("风控入参, 缺少必要入参：产品号及渠道号,对应风控标识流水号：{}", record.getId());
            throw new RuntimeException("风控入参, 缺少必要入参：产品号及渠道号,对应风控标识流水号");
        }
        List<UserContactInfo> contacts = userContactInfoRepository.findByUserId(userInfo.getId());

        PreOrder preOrder = preOrderRepository.findByRiskId(record.getId()).orElseThrow();


        //开始请求风控系统
        RiskDataRequest request = new RiskDataRequest();
        chooseRiskSerialNumber(request, record, ApplyType.RISK);

        Map<String, Object> map = new HashMap<>();
        map.put("personId", userInfo.getCertNo());
        map.put("person_id", userInfo.getCertNo());
        map.put("person_name", preOrder.getName());
        map.put("person_mobile", preOrder.getMobile());
        map.put("business_number", preOrder.getOrderNo());
        map.put("idcard", userInfo.getCertNo());
        map.put("mobile", userInfo.getMobile());
        // 顶级字段,mobile
        map.put("userId", userInfo.getId());
        map.put("cdtId", preOrder.getOrderNo());
        map.put("cusPhone", preOrder.getMobile());
        map.put("cusName", preOrder.getName());
        map.put("cusIdno", preOrder.getCertNo());
        map.put("cdtAplAmt", preOrder.getApplyAmount().toString());
        map.put("cdtAplTerm", preOrder.getApplyPeriods());
        map.put("cdtDttm", preOrder.getApplyTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        map.put("flowChannel", record.getFlowChannel());
        map.put("applyChannel", record.getApplyChannel());
        if (FlowChannel.PPCJDL == record.getFlowChannel()) {
            convertPpdApplyChannel(record, preOrder, map);
        }


        // baseInfo 下的字段
        map.put("baseInfoMaritalStatus", userInfo.getMarriage());
        map.put("baseInfoCompanyPhone", userInfo.getUnitPhone());


        map.put("baseInfoLiveAddress", userInfo.getLivingAddress());

        String[] provinceCityDistrict = {"", "", ""};
        try {
            provinceCityDistrict = ChineseAddressParser.getProvinceCityDistrict(userInfo.getLivingAddress());
        } catch (Exception e) {
            logger.info("风控入参, 住宿地址: [{}] 解析异常, 设置未知", userInfo.getLivingAddress());
            provinceCityDistrict[0] = "未知";
            provinceCityDistrict[1] = "未知";
            provinceCityDistrict[2] = "未知";
        }

        map.put("baseInfoLiveProvince", provinceCityDistrict[0]);
        map.put("baseInfoLiveCity", provinceCityDistrict[1]);
        map.put("baseInfoLiveArea", provinceCityDistrict[2]);
        map.put("baseInfoProvinceCode", userInfo.getLivingProvinceCode());
        map.put("baseInfoCityCode", userInfo.getLivingCityCode());
        map.put("baseInfoAreaCode", userInfo.getLivingDistrictCode());


        map.put("baseInfoIndustry", null == userInfo.getIndustry() ? Industry.TWENTY.getCode() : userInfo.getIndustry().getCode());

        // contactInfos 下的字段  暂时先用一个
        UserContactInfo userContactInfo = contacts.get(0);
        map.put("contactInfosName", userContactInfo.getName());
        map.put("contactInfosPhone", userContactInfo.getPhone());
        map.put("contactInfosRelation", userContactInfo.getRelation().name());


        if (record.getFlowChannel().equals(FlowChannel.LVXIN)) {
            LvxinApplyRecord lvxinApplyRecord = lvxinApplyRecordRepository.findByOrderNo(preOrder.getOrderNo()).orElseThrow();

            if ("长期".equals(lvxinApplyRecord.getIdEndTime())) {
                map.put("idnoVal", lvxinApplyRecord.getIdEndTime());
            } else {
                long daysDiff = ChronoUnit.DAYS.between(LocalDate.now(),
                    LocalDate.parse(lvxinApplyRecord.getIdEndTime(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                );
                map.put("idnoVal", daysDiff);
            }


            // authInfo 下的字段
            map.put("authInfoFrontUrl", lvxinApplyRecord.getIdPositive());
            map.put("authInfoBackUrl", lvxinApplyRecord.getIdNegative());
            map.put("authInfoBorrower", lvxinApplyRecord.getLivePhoto());
            map.put("authInfoAddress", lvxinApplyRecord.getIdAddress());
            map.put("authInfoStartDueTimeOcr", lvxinApplyRecord.getIdStartTime());
            map.put("authInfoEndDueTimeOcr", lvxinApplyRecord.getIdEndTime());
            map.put("authInfoSex", lvxinApplyRecord.getIdSex());
            map.put("authInfoBirthday", extractBirthDate(lvxinApplyRecord.getIdCardNo()));
            map.put("authInfoNation", lvxinApplyRecord.getIdEthnic());
            map.put("authInfoAuthority", lvxinApplyRecord.getIdIssueOrg());
            map.put("authInfoLiveRate", lvxinApplyRecord.getFaceScore());


            String[] cityAndDistrict = {"", ""};
            try {
                cityAndDistrict = ChineseAddressParser.getCityAndDistrict(lvxinApplyRecord.getWorkUnitAddress());
            } catch (Exception e) {
                logger.info("风控入参, 工作地址: [{}] 解析异常, 设置未知", lvxinApplyRecord.getWorkUnitAddress());
                cityAndDistrict[0] = "未知";
                cityAndDistrict[1] = "未知";
            }

            map.put("baseInfoCompanyCity", cityAndDistrict[0]);
            map.put("baseInfoCompanyArea", cityAndDistrict[1]);
            map.put("baseInfoCompanyProvinceCode", lvxinApplyRecord.getWorkUnitProvinceCode());
            map.put("baseInfoCompanyCityCode", lvxinApplyRecord.getWorkUnitCityCode());
            map.put("baseInfoCompanyAreaCode", lvxinApplyRecord.getWorkUnitAreaCode());
            map.put("baseInfoInCome", lvxinApplyRecord.getMonthlyIncome());

            map.put("baseInfoEducational", lvxinApplyRecord.getEducation());
            map.put("baseInfoCompanyAddress", lvxinApplyRecord.getWorkUnitAddress());
            map.put("baseInfoCompanyName", lvxinApplyRecord.getWorkUnitName());
            map.put("baseInfoWorkType", lvxinApplyRecord.getJob());
        }

        if (record.getFlowChannel().equals(FlowChannel.PPCJDL)) {
            PpdCreditApplyRecord ppdCreditApplyRecord = ppdCreditApplyRecordRepository.findByLoanReqNo(preOrder.getOrderNo());
            if ("9999-12-31".equals(ppdCreditApplyRecord.getIdExpiryDate())) {
                map.put("idnoVal", "长期");
            } else {
                long daysDiff = ChronoUnit.DAYS.between(
                    LocalDate.now(),
                    LocalDate.parse(ppdCreditApplyRecord.getIdExpiryDate())
                );
                map.put("idnoVal", daysDiff);
            }
            map.put("authInfoEndDueTimeOcr", ppdCreditApplyRecord.getIdExpiryDate());

            UserOcr userOcr = userOcrRepository.findByUserId(record.getUserId());
            UserFace userFace = userFaceRepository.findByUserId(record.getUserId());

            // authInfo 下的字段
            map.put("authInfoFrontUrl", userOcr.getHeadOssKey());
            map.put("authInfoBackUrl", userOcr.getNationOssKey());
            map.put("authInfoBorrower", userFace.getOssKey());
            map.put("authInfoAddress", ppdCreditApplyRecord.getAddressOcr());
            map.put("authInfoStartDueTimeOcr", ppdCreditApplyRecord.getIdBeginDate());

            map.put("authInfoSex", ppdCreditApplyRecord.getSex());
            map.put("authInfoBirthday", extractBirthDate(ppdCreditApplyRecord.getIdNo()));
            map.put("authInfoNation", ppdCreditApplyRecord.getNation());
            map.put("authInfoAuthority", ppdCreditApplyRecord.getCertificationUnit());
            map.put("authInfoLiveRate", ppdCreditApplyRecord.getFaceRecoScore());


            String[] cityAndDistrict = {"", ""};
            try {
                cityAndDistrict = ChineseAddressParser.getCityAndDistrict(ppdCreditApplyRecord.getAddressOcr());
            } catch (Exception e) {
                logger.info("风控入参, 工作地址: [{}] 解析异常, 设置未知", ppdCreditApplyRecord.getAddressOcr());
                cityAndDistrict[0] = "未知";
                cityAndDistrict[1] = "未知";
            }

            map.put("baseInfoCompanyCity", cityAndDistrict[0]);
            map.put("baseInfoCompanyArea", cityAndDistrict[1]);

            map.put("baseInfoEducational", ppdCreditApplyRecord.getHighestDegree());
            map.put("baseInfoWorkType", ppdCreditApplyRecord.getOccupation());
        }

        //applyTime倒序查获userId向下所有授信订单
        List<PreOrder> preOrders = preOrderRepository.findByOpenIdAndPreOrderStateNotOrderByApplyTimeDesc(userInfo.getId(), PreOrderState.AUDITING);
        //最近一次授信申请成功距今天数
        map.put("creditLstAplSucDays", 0);
        //最近一次授信申请拒绝距今天数
        map.put("creditLstAplFailDays", 0);
        //最近一次授信结果
        map.put("creditLstAplRes", "");
        if (!preOrders.isEmpty()) {
            map.put("creditLstAplRes", preOrders.get(0).getPreOrderState() == PreOrderState.AUDIT_PASS ? "通过" : "拒绝");
            for (PreOrder order : preOrders) {
                //最近一次授信申请成功距今天数
                if (order.getPreOrderState().equals(PreOrderState.AUDIT_PASS)) {
                    if (map.get("creditLstAplSucDays") == null || "".equals(map.get("creditLstAplSucDays"))) {
                        //若首次赋值则认定为最近一次授信记录
                        if (null != order.getApplyTime()) {
                            map.put("creditLstAplSucDays", Duration.between(order.getApplyTime(), LocalDateTime.now()).toDays());
                        }
                    }
                }
                //最近一次授信申请拒绝距今天数
                if (order.getPreOrderState().equals(PreOrderState.AUDIT_REJECT)) {
                    if (map.get("creditLstAplFailDays") == null || "".equals(map.get("creditLstAplFailDays"))) {
                        //若首次赋值则认定为最近一次授信记录
                        if (null != order.getApplyTime()) {
                            map.put("creditLstAplFailDays", Duration.between(order.getApplyTime(), LocalDateTime.now()).toDays());
                        }
                    }
                }
            }
        }

        List<Loan> loanLists = loanRepository.findByUserIdOrderByApplyTimeDesc(userInfo.getId());
        //最近一次支用申请成功距今天数
        map.put("loanLstAplSucDays", 0);
        //最近一次支用申请拒绝距今天数
        map.put("loanLstAplFailDays", 0);
        //最近一次支用结果
        map.put("loanLstAplRes", "");

        if (!loanLists.isEmpty()) {
            //最近一次支用结果
            map.put("loanLstAplRes", loanLists.get(0).getLoanState() == ProcessState.SUCCEED ? "通过" : "拒绝");
            for (Loan l : loanLists) {
                //最近一次支用申请成功距今天数
                if (l.getLoanState().equals(ProcessState.SUCCEED)) {
                    if (map.get("loanLstAplSucDays") == null || "".equals(map.get("loanLstAplSucDays"))) {
                        map.put("loanLstAplSucDays", Duration.between(l.getApplyTime(), LocalDateTime.now()).toDays());
                    }
                }
                //最近一次支用申请拒绝距今天数
                if (l.getLoanState().equals(ProcessState.FAILED)) {
                    if (map.get("loanLstAplFailDays") == null || "".equals(map.get("loanLstAplFailDays"))) {
                        map.put("loanLstAplFailDays", Duration.between(l.getApplyTime(), LocalDateTime.now()).toDays());
                    }
                }
            }
        }
        Map<String, Object> userOverdueDays = repayPlanRepository.findUserOverdueDays(userInfo.getId());
        //当前逾期天数
        map.put("reapyCurOvdNumMax", userOverdueDays.getOrDefault("currentOverdueDays", 0));
        map.put("reapyHisOvdNumMax", userOverdueDays.getOrDefault("historicalMaxOverdueDays", 0));

        //风控订单信息获取配置表方法（风控喜好：渠道维度支用次数）
        Map<String, Object> loanCounts = loanRepository.countUserIdLoansByChannel(userInfo.getId());
        map.put("lxTheLoanNum", loanCounts.getOrDefault(ApplyChannel.LVXIN.name(), 0));
        map.put("ppP002TheLoanNum", loanCounts.getOrDefault(ApplyChannel.CJCYDL_PPD2.name(), 0));
        map.put("ppP001TheLoanNum", loanCounts.getOrDefault(ApplyChannel.CJCYDL_PPD3.name(), 0));

        request.setInputParamMap(map);
        return request;
    }

    private RiskDataRequest buildRiskLoanApplyRequest(UserRiskRecord record, UserInfo userInfo) {

        if (null == record.getFlowChannel() || null == record.getApplyChannel()) {
            logger.error("风控入参, 缺少必要入参：产品号及渠道号,对应风控标识流水号：{}", record.getId());
            throw new RuntimeException("风控入参, 缺少必要入参：产品号及渠道号,对应风控标识流水号");
        }


        //开始请求风控系统
        RiskDataRequest request = new RiskDataRequest();
        chooseRiskSerialNumber(request, record, ApplyType.RISK_LOAN);
        Map<String, Object> map = new HashMap<>();

        PreOrder preOrder = preOrderRepository.findTopByOpenIdAndFlowChannelOrderByCreatedTimeDesc(record.getUserId(), record.getFlowChannel());

        PpdLoanApplyRecord loanApplyRecord = ppdLoanApplyRecordRepository.findByLoanReqNo(preOrder.getOrderNo());
        map.put("loanDttm", loanApplyRecord.getCreatedTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("loanId", loanApplyRecord.getLoanReqNo());
        map.put("term", loanApplyRecord.getLoanTerm());
        map.put("loanAmt", loanApplyRecord.getLoanAmt());
        map.put("loanReqNo", preOrder.getOrderNo());

        map.put("business_number", preOrder.getOrderNo());
        map.put("personId", userInfo.getCertNo());
        map.put("person_id", userInfo.getCertNo());
        map.put("person_name", userInfo.getName());
        map.put("person_mobile", userInfo.getMobile());
        map.put("bank_card_number", loanApplyRecord.getBankAcct());
        map.put("person_email", userInfo.getEmail());

        map.put("idcard", userInfo.getCertNo());
        map.put("mobile", userInfo.getMobile());
        // 顶级字段,mobile
        map.put("userId", userInfo.getId());

        map.put("cdtId", preOrder.getOrderNo());


        map.put("flowChannel", record.getFlowChannel());
        map.put("applyChannel", record.getApplyChannel());
        if (FlowChannel.PPCJDL == record.getFlowChannel()) {
            convertPpdApplyChannel(record, preOrder, map);
        }


        //applyTime倒序查获userId向下所有授信订单
        List<PreOrder> preOrders = preOrderRepository.findByOpenIdAndPreOrderStateNotOrderByApplyTimeDesc(userInfo.getId(), PreOrderState.AUDITING);
        //最近一次授信申请成功距今天数
        map.put("creditLstAplSucDays", 0);
        //最近一次授信申请拒绝距今天数
        map.put("creditLstAplFailDays", 0);
        //最近一次授信结果
        map.put("creditLstAplRes", "");
        if (!preOrders.isEmpty()) {
            map.put("creditLstAplRes", preOrders.get(0).getPreOrderState() == PreOrderState.AUDIT_PASS ? "通过" : "拒绝");
            for (PreOrder order : preOrders) {
                //最近一次授信申请成功距今天数
                if (order.getPreOrderState().equals(PreOrderState.AUDIT_PASS)) {
                    if (map.get("creditLstAplSucDays") == null || "".equals(map.get("creditLstAplSucDays"))) {
                        //若首次赋值则认定为最近一次授信记录
                        if (null != order.getApplyTime()) {
                            map.put("creditLstAplSucDays", Duration.between(order.getApplyTime(), LocalDateTime.now()).toDays());
                        }
                    }
                }
                //最近一次授信申请拒绝距今天数
                if (order.getPreOrderState().equals(PreOrderState.AUDIT_REJECT)) {
                    if (map.get("creditLstAplFailDays") == null || "".equals(map.get("creditLstAplFailDays"))) {
                        //若首次赋值则认定为最近一次授信记录
                        if (null != order.getApplyTime()) {
                            map.put("creditLstAplFailDays", Duration.between(order.getApplyTime(), LocalDateTime.now()).toDays());
                        }
                    }
                }
            }
        }

        List<Loan> loanLists = loanRepository.findByUserIdOrderByApplyTimeDesc(userInfo.getId());
        //最近一次支用申请成功距今天数
        map.put("loanLstAplSucDays", 0);
        //最近一次支用申请拒绝距今天数
        map.put("loanLstAplFailDays", 0);
        //最近一次支用结果
        map.put("loanLstAplRes", "");

        if (!loanLists.isEmpty()) {
            //最近一次支用结果
            map.put("loanLstAplRes", loanLists.get(0).getLoanState() == ProcessState.SUCCEED ? "通过" : "拒绝");
            for (Loan l : loanLists) {
                //最近一次支用申请成功距今天数
                if (l.getLoanState().equals(ProcessState.SUCCEED)) {
                    if (map.get("loanLstAplSucDays") == null || "".equals(map.get("loanLstAplSucDays"))) {
                        map.put("loanLstAplSucDays", Duration.between(l.getApplyTime(), LocalDateTime.now()).toDays());
                    }
                }
                //最近一次支用申请拒绝距今天数
                if (l.getLoanState().equals(ProcessState.FAILED)) {
                    if (map.get("loanLstAplFailDays") == null || "".equals(map.get("loanLstAplFailDays"))) {
                        map.put("loanLstAplFailDays", Duration.between(l.getApplyTime(), LocalDateTime.now()).toDays());
                    }
                }
            }
        }
        Map<String, Object> userOverdueDays = repayPlanRepository.findUserOverdueDays(userInfo.getId());
        //当前逾期天数
        map.put("reapyCurOvdNumMax", userOverdueDays.getOrDefault("currentOverdueDays", 0));
        map.put("reapyHisOvdNumMax", userOverdueDays.getOrDefault("historicalMaxOverdueDays", 0));

        //风控订单信息获取配置表方法（风控喜好：渠道维度支用次数）
        Map<String, Object> loanCounts = loanRepository.countUserIdLoansByChannel(userInfo.getId());
        map.put("lxTheLoanNum", loanCounts.getOrDefault(ApplyChannel.LVXIN.name(), 0));
        map.put("ppP002TheLoanNum", loanCounts.getOrDefault(ApplyChannel.CJCYDL_PPD2.name(), 0));
        map.put("ppP001TheLoanNum", loanCounts.getOrDefault(ApplyChannel.CJCYDL_PPD3.name(), 0));

        request.setInputParamMap(map);
        return request;
    }

    private void convertPpdApplyChannel(UserRiskRecord record, PreOrder preOrder, Map<String, Object> map) {
        PpdCreditApplyRecord ppdCreditApplyRecord = ppdCreditApplyRecordRepository.findByLoanReqNo(preOrder.getOrderNo());
        String applyChannel = "";
        if (ppdCreditApplyRecord.getSourceCode().equals(ApplyChannel.CJCYDL_PPD2.name())) {
            switch (ppdCreditApplyRecord.getAccessType()) {
                case "1" -> applyChannel = "p004";
                case "2" -> applyChannel = "p006";
                default -> applyChannel = record.getApplyChannel();
            }
        } else if (ppdCreditApplyRecord.getSourceCode().equals(ApplyChannel.CJCYDL_PPD3.name())) {
            switch (ppdCreditApplyRecord.getAccessType()) {
                case "1" -> applyChannel = "p003";
                case "2" -> applyChannel = "p005";
                default -> applyChannel = record.getApplyChannel();
            }
        }
        map.put("applyChannel", applyChannel);
    }

    public static String extractBirthDate(String idCard) {
        if (idCard == null || idCard.length() < 15) {
            return ""; // 非法身份证号
        }

        if (idCard.length() == 18) {
            // 18位：第7-14位是出生日期（YYYYMMDD）
            return idCard.substring(6, 14);
        } else {
            // 15位：第7-12位是出生日期（YYMMDD），补全19前缀
            return "19" + idCard.substring(6, 12);
        }
    }


    public void platformRiskQuery(final UserRiskRecord record) {
        AuditState approveResult = record.getApproveResult();
        if (approveResult != AuditState.AUDITING) {
            return;
        }
        String riskId = record.getId();
        logger.info("user risk, credit query, risk id: {}", riskId);
        platformRiskQueryInter(record);

    }

    /**
     * 调用风控查询接口
     *
     * @param record 授信记录
     */
    public void platformRiskQueryInter(final UserRiskRecord record) {


    }


//    private boolean checkRiskResult(String riskId, WeiyanUserCreditQueryResponseDTO responseDTO) {

//    }


    @Nullable
    public UserRiskRecord findPlatformRiskRecord(String riskId) {
        return riskRecordRepository.findById(riskId).orElse(null);
    }

    @Nullable
    public UserRiskRecord findRiskRecordByUserId(String userId, FlowChannel flowChannel) {
        return riskRecordRepository.findTopByUserIdAndFlowChannelOrderByCreatedTimeDesc(userId, flowChannel);
    }


    public boolean isRiskAgreementAllSuccess(UserRiskRecord userRiskRecord) {
        String userRiskRecordId = userRiskRecord.getId();
        List<AgreementSignRelation> agreementSignRelations = agreementSignRelationRepository.findByRelatedId(userRiskRecordId);
        List<String> signApplyIdList = agreementSignRelations.stream().map(AgreementSignRelation::getSignApplyId)
            .collect(Collectors.toList());

        //签章成功后会复制到user_file表
        List<UserFile> userFiles = userFileRepository.findAllById(signApplyIdList);
        return true;
    }

    public String getImageBase64(String userId, FileType fileType) throws IOException {
        UserFile uf = userFileRepository.findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(userId, LoanStage.RISK, fileType);
        if (uf == null) {
            // throw new BizException(ResultCode.RISK_IMG_NOT_EXIST);
            return null;
        }
        InputStream is = ossFileService.getOssFile(uf.getOssBucket(), uf.getOssKey());
        String s = Base64.getEncoder().encodeToString(is.readAllBytes());
        IOUtils.closeQuietly(is);
        return s;
    }


    /**
     * 身份证图片大小压缩到500k以下，2k以上
     *
     * @param userId   用户id
     * @param fileType 文件类型
     * @return
     * @throws IOException
     */
    public String getCompressImageBase64(String userId, FileType fileType) throws IOException {
        UserFile uf = userFileRepository.findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(userId, LoanStage.RISK, fileType);
        if (uf == null) {
            // throw new BizException(ResultCode.RISK_IMG_NOT_EXIST);
            return null;
        }
        Path tempLocalFile = Files.createTempFile("img", ".jpg");
        try (var os = Files.newOutputStream(tempLocalFile)) {
            InputStream is = ossFileService.getOssFile(uf.getOssBucket(), uf.getOssKey());
            IOUtils.copy(is, os);
            IOUtils.closeQuietly(is);
        }

        File scaledImg = ImageUtil.scaleImages(tempLocalFile.toFile(), IMG_MAX_SIZE);

        String s = Base64.getEncoder().encodeToString(Files.readAllBytes(scaledImg.toPath()));

        deleteTempFile(scaledImg);
        deleteTempFile(tempLocalFile);

        return s;
    }


    /**
     * 将文件压缩成zip然后再传
     *
     * @param userId
     * @param fileType
     * @return
     * @throws IOException
     */
    public String getZipBase64(String userId, FileType fileType, String fileName) throws IOException {
        UserFile uf = userFileRepository.findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(userId, LoanStage.RISK, fileType);
        if (uf == null) {
            // throw new BizException(ResultCode.RISK_IMG_NOT_EXIST);
            return null;
        }
        InputStream is = ossFileService.getOssFile(uf.getOssBucket(), uf.getOssKey());
        Path tempFile = Files.createTempFile("tmp", ".zip");

        try (ZipOutputStream zipOut = new ZipOutputStream(Files.newOutputStream(tempFile))) {

            ZipEntry entry = new ZipEntry(fileName);
            try {
                zipOut.putNextEntry(entry);
                IoUtil.copy(is, zipOut);
                zipOut.closeEntry();
            } catch (IOException e) {
                logger.error("create file error, target: {}", fileName, e);
                // throw new RuntimeException(e);
            }

        } catch (IOException e) {
            logger.error("create file error, target: {}", fileName, e);
            // throw new RuntimeException(e);
        }
        InputStream zipFileOS = Files.newInputStream(tempFile);

        String s = Base64.getEncoder().encodeToString(zipFileOS.readAllBytes());
        IOUtils.closeQuietly(is);
        Files.deleteIfExists(tempFile);
        return s;
    }

    public String getImageIdCardMergeBase64(String userId, FileType idFaceType, FileType idNationType) throws Exception {
        UserFile faceFile = userFileRepository.findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(userId, LoanStage.RISK, idFaceType);
        if (faceFile == null) {
            return null;
        }
        InputStream faceOssIs = ossFileService.getOssFile(faceFile.getOssBucket(), faceFile.getOssKey());
        Path faceLocalImg = Files.createTempFile(userId, ".jpg");
        OutputStream faceLocalOs = Files.newOutputStream(faceLocalImg);
        IOUtils.copy(faceOssIs, faceLocalOs);
        IOUtils.closeQuietly(faceOssIs);
        IOUtils.closeQuietly(faceLocalOs);

        UserFile nationFile = userFileRepository.findTopByUserIdAndLoanStageAndFileTypeOrderByUpdatedTimeDesc(userId, LoanStage.RISK, idNationType);
        if (nationFile == null) {
            return null;
        }
        InputStream nationOssIs = ossFileService.getOssFile(nationFile.getOssBucket(), nationFile.getOssKey());
        Path nationLocalImg = Files.createTempFile(userId, ".jpg");
        OutputStream nationLocalOs = Files.newOutputStream(nationLocalImg);
        IOUtils.copy(nationOssIs, nationLocalOs);
        IOUtils.closeQuietly(nationOssIs);
        IOUtils.closeQuietly(nationLocalOs);

        File targetFile = ImageUtil.mergeTwoFromFile(faceLocalImg.toFile(), nationLocalImg.toFile(), userId);

        Files.deleteIfExists(faceLocalImg);
        Files.deleteIfExists(nationLocalImg);

        if (targetFile == null) {
            return null;
        }
        File finalTarget = ImageUtil.scaleImages(targetFile, userId);

        String mergeResult = Base64.getEncoder().encodeToString(FileUtils.readFileToByteArray(finalTarget));

        Files.deleteIfExists(targetFile.toPath());
        Files.deleteIfExists(finalTarget.toPath());

        return mergeResult;
    }

    private void deleteTempFile(File tmpFile) {
        if (tmpFile == null) {
            return;
        }
        tmpFile.deleteOnExit();
    }

    private void deleteTempFile(Path tmpFile) {
        if (tmpFile == null) {
            return;
        }
        try {
            Files.delete(tmpFile);
        } catch (IOException e) {
            // no throw
        }
    }

    private String buildHuaRongAuthIdx(String id) {
        return id + "AND111100000000";
    }

    /**
     * 续借查询
     *
     * @param renewedRecordId 续借标识记录id
     */
    public void renewedQuery(String renewedRecordId) {
        UserRenewedRecord renewedRecord = userRenewedRecordRepository.findById(renewedRecordId).orElseThrow();
        // 查询借款信息
        Loan loan = loanRepository.findByOrderId(renewedRecord.getOrderId());

        boolean renewedFlag = false;

        if (renewedOpen) {
            // 查询用户信息
            UserInfo userInfo = userInfoRepository.findById(renewedRecord.getUserId()).orElseThrow();
            // 查询设备信息
            UserDevice userDevice = userDeviceRepository.findByUserId(renewedRecord.getUserId());
            // 查询orc信息
            UserOcr ocr = userOcrRepository.findByUserId(renewedRecord.getUserId());
            // 查询联系人信息
            List<UserContactInfo> userContacts = userContactInfoRepository.findByUserId(userInfo.getId());
            if (CollectionUtils.isEmpty(userContacts)) {
                // 联系人为空时直接返回不再查询风控
                logger.info("续借标识查询联系人为空, creditId: {}", renewedRecord.getId());
                renewedRecord.setFailReason("联系人为空不查询");
                renewedRecord.setStatus(MarkStatusFlow.EXCEPTION);
                userRenewedRecordRepository.save(renewedRecord);
                return;
            }

        }


    }

    public void chooseRiskSerialNumber(RiskDataRequest request, UserRiskRecord record, ApplyType applyType) {
        FlowChannel flowChannel = record.getFlowChannel();
        if (flowChannel == FlowChannel.LVXIN) {
            request.setCallSerialNumber(riskConfig.getCreditApplyLvxinSerialNumber());
            request.setSystemKey(riskConfig.getCreditApplyLvxinSystemKey());
        } else if (flowChannel == FlowChannel.PPCJDL) {
            if (applyType == ApplyType.RISK) {
                request.setCallSerialNumber(riskConfig.getCreditApplyPpdSerialNumber());
                request.setSystemKey(riskConfig.getCreditApplyPpdSystemKey());
            } else if (applyType == ApplyType.RISK_LOAN) {
                request.setCallSerialNumber(riskConfig.getLoanApplyPpdSerialNumber());
                request.setSystemKey(riskConfig.getLoanApplyPpdSystemKey());
            }

        }
    }

    public String riskCallback(RiskCallbackRequest request) {
        logger.info("风控回调接口请求参数：{}", JsonUtil.toJsonString(request));
        try {
            AuditState riskState = AuditState.PASS;
            String pipelineId = request.getResult().getPipeline_id();
            UserRiskRecord record = userRiskRecordRepository.findByPipelineId(pipelineId).orElseThrow();
            if (CommonBaseConstant.RISK_CODE_200.equals(request.getCode())) {
                RiskCallbackRequest.InnerResult result = request.getResult().getResult();
                if (!"pass".equals(result.getDecision())) {
                    riskState = AuditState.REJECT;
                    if (result.getCustom_str() != null) {
                        record.setRiskFinalResult(result.getCustom_str().containsKey("risk_reason") ? result.getCustom_str().getString("risk_reason") : "");
                    } else {
                        record.setRiskFinalResult("风控拒绝，未知原因");
                    }
                }
            } else {
                logger.info("风控审核接口回调异常 code: [{}] userRecordId: [{}]", request.getCode(), record.getUserId());
                riskState = AuditState.REJECT;
                record.setRiskFinalResult(request.getMessage());
            }
            record.setApproveResult(riskState);
            riskRecordRepository.save(record);
            if (record.getApplyType() == ApplyType.RISK) {
                if (record.getApproveResult() == AuditState.PASS) {
                    //推送风控通过事件
                    eventPublisher.publishEvent(new RiskResultEvent(record.getId()));
                }
                if (record.getApproveResult() == AuditState.REJECT) {
                    //推送风控拒绝事件
                    eventPublisher.publishEvent(new RiskResultEvent(record.getId()));
                }
                // 处理中延迟查询
                if (record.getApproveResult() == AuditState.AUDITING) {
                    mqService.submitRiskQueryDelay(record.getId());
                }
            } else if (record.getApplyType() == ApplyType.RISK_LOAN) {
                PreOrder preOrder = preOrderRepository.findTopByOpenIdAndFlowChannelOrderByCreatedTimeDesc(record.getUserId(), record.getFlowChannel());
                Order order = orderRepository.findTopByOuterOrderIdAndFlowChannelAndApplyChannel(preOrder.getOrderNo(), FlowChannel.PPCJDL, record.getApplyChannel());
                if (record.getApproveResult() == AuditState.PASS) {
                    //推送风控通过事件 - 路由资金方推送授信放款
                    orderService.orderRoute(order);
                } else if (record.getApproveResult() == AuditState.REJECT) {
                    order.setOrderState(OrderState.LOAN_FAIL);
                    order.setRemark("支用风控拒绝");
                    orderRepository.save(order);
                }
            }
        } catch (Exception e) {
            logger.error("风控回调处理异常:{},{}", JsonUtil.toJsonString(request), e.getMessage());
            return "fail";
        }
        return "success";
    }
}
