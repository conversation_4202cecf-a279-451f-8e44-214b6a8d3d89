package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.FlowChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * 流量协议关联表
 */
@Entity
@Table(name = "flow_agreement_relation")
public class FlowAgreementRelation extends BaseEntity {
    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    /**
     * 入参
     */
    private String param;

    /**
     * 协议编号数组
     */
    private String agreementNos;

    @Override
    protected String prefix() {
        return "FAR";
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getAgreementNos() {
        return agreementNos;
    }

    public void setAgreementNos(String agreementNos) {
        this.agreementNos = agreementNos;
    }
}
