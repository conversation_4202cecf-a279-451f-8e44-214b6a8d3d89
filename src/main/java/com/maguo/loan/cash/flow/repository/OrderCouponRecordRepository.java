package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.OrderCouponRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface OrderCouponRecordRepository extends JpaRepository<OrderCouponRecord, String> {
    @Query("select ccf from Order c join OrderCouponRecord ccf on c.id = ccf.orderId "
        + "where c.userId = ?1 and ccf.useState = 'SUCCEED' order by c.applyTime DESC limit 1")
    OrderCouponRecord findByUserId(String userId);



    List<OrderCouponRecord> findByOrderId(String id);

}
