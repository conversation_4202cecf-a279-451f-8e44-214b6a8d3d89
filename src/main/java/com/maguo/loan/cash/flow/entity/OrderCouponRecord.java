package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
@Entity
@Table(name = "order_coupon_record")
public class OrderCouponRecord extends BaseEntity {

    /**
     *授信id
     */
    private String orderId;


    /**
     *优惠券id
     */
    private String couponId;
    /**
     * 使用状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState useState;
    /**
     * 风控出额
     */
    private BigDecimal riskAmount;
    /**
     * 授信额度
     */
    private BigDecimal creditAmount;

    /**
     * 优惠券类型
     */
    private String couponType;

    /**
     * 优惠券值
     */
    private BigDecimal couponValue;


    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;


    /**
     * 原始金额
     */
    private BigDecimal originAmount;


    /**
     * 实际金额
     */
    private BigDecimal actAmount;


    /**
     * userId
     */
    private String userId;
    /**
     * 减免期数
     */
    private Integer freePeriod;


    public BigDecimal getCouponValue() {
        return couponValue;
    }

    public void setCouponValue(BigDecimal couponValue) {
        this.couponValue = couponValue;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public BigDecimal getOriginAmount() {
        return originAmount;
    }

    public void setOriginAmount(BigDecimal originAmount) {
        this.originAmount = originAmount;
    }

    public BigDecimal getActAmount() {
        return actAmount;
    }

    public void setActAmount(BigDecimal actAmount) {
        this.actAmount = actAmount;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public String getCouponId() {
        return couponId;
    }

    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    public ProcessState getUseState() {
        return useState;
    }

    public void setUseState(ProcessState useState) {
        this.useState = useState;
    }

    public BigDecimal getRiskAmount() {
        return riskAmount;
    }

    public void setRiskAmount(BigDecimal riskAmount) {
        this.riskAmount = riskAmount;
    }

    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getFreePeriod() {
        return freePeriod;
    }

    public void setFreePeriod(Integer freePeriod) {
        this.freePeriod = freePeriod;
    }

    @Override
    protected String prefix() {
        return "CP";
    }
}
