package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.AuditState;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 * 撞库申请记录
 */
@Entity
@Table(name = "collision_marketing_record")
public class CollisionMarketingRecord extends BaseEntity {

    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    /**
     *渠道标签
     */
    @Enumerated(EnumType.STRING)
    private ApplyChannel applyChannel;

    /**
     * MD5手机号
     */
    private String phone;

    /**
     * MD5哈希身份证
     */
    private String certNo;

    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    private AuditState state;

    /**
     * 拒绝原因
     */
    private String failReason;

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public ApplyChannel getApplyChannel() {
        return applyChannel;
    }

    public void setApplyChannel(ApplyChannel applyChannel) {
        this.applyChannel = applyChannel;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public AuditState getState() {
        return state;
    }

    public void setState(AuditState state) {
        this.state = state;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

}
