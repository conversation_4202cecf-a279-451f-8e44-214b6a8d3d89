package com.maguo.loan.cash.flow.service.banks.cybk;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.repay.PlanItemDto;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.enums.RateLevel;
import com.maguo.loan.cash.flow.service.banks.AbstractBankLoanService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2023/12/30
 */
@Service
public class CYBKLoanService extends AbstractBankLoanService {
    @Override
    public BankChannel bankChannel() {
        return BankChannel.CYBK;
    }

//    @Override
//    public BigDecimal planConsultFee(Loan loan, PlanItemDto planItemDto, BigDecimal remainingPrincipalAmt) {
//         //IRR36等额本息
//        List<RepayPlan> consultPlans = PlanGenerator.genPlan(InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST,
//            loan.getLoanTime().toLocalDate(), loan.getAmount(), RateLevel.RATE_36.getRate(), loan.getPeriods());
//        // 计算咨询费
//        RepayPlan consultPlan = consultPlans.stream().filter(c -> c.getCurrentPeriod() == planItemDto.getPeriod()).findFirst().orElseThrow();
//        return consultPlan.getPrincipal().add(consultPlan.getInterest())
//            .subtract(planItemDto.getPrincipalAmt().add(planItemDto.getInterestAmt()).add(planItemDto.getGuaranteeAmt()));
//    }

    @Override
    public BigDecimal planConsultFee(Loan loan, PlanItemDto planItemDto, BigDecimal remainingPrincipalAmt, BigDecimal SAN_LIU_LING,long num) {
        return remainingPrincipalAmt.multiply(RateLevel.CONSULT_FEE_MONTH_RATE_36.getRate()).setScale(2,RoundingMode.HALF_DOWN);
    }
}
