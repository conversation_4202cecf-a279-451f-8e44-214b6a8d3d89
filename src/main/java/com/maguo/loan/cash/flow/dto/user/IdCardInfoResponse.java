package com.maguo.loan.cash.flow.dto.user;


public class IdCardInfoResponse {
    private String name;

    /**
     * 身份证有效期开始
     */
    private String certStartDay;

    /**
     * 身份证有效期结束
     */
    private String certEndDay;

    /**
     * 性别
     */
    private String gender;

    /**
     * 民族
     */
    private String nation;

    /**
     * 签发机构
     */
    private String certOrg;
    /**
     * 身份证人像图片地址
     */
    private String facePicUrl;

    /**
     * 人像图片过期时间
     */
    private String facePicExpire;

    /**
     * 身份证国徽图片地址
     */
    private String nationPicUrl;

    /**
     * 国徽图片过期时间
     */
    private String nationPicExpire;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCertStartDay() {
        return certStartDay;
    }

    public void setCertStartDay(String certStartDay) {
        this.certStartDay = certStartDay;
    }

    public String getCertEndDay() {
        return certEndDay;
    }

    public void setCertEndDay(String certEndDay) {
        this.certEndDay = certEndDay;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getCertOrg() {
        return certOrg;
    }

    public void setCertOrg(String certOrg) {
        this.certOrg = certOrg;
    }

    public String getFacePicUrl() {
        return facePicUrl;
    }

    public void setFacePicUrl(String facePicUrl) {
        this.facePicUrl = facePicUrl;
    }

    public String getFacePicExpire() {
        return facePicExpire;
    }

    public void setFacePicExpire(String facePicExpire) {
        this.facePicExpire = facePicExpire;
    }

    public String getNationPicUrl() {
        return nationPicUrl;
    }

    public void setNationPicUrl(String nationPicUrl) {
        this.nationPicUrl = nationPicUrl;
    }

    public String getNationPicExpire() {
        return nationPicExpire;
    }

    public void setNationPicExpire(String nationPicExpire) {
        this.nationPicExpire = nationPicExpire;
    }
}
