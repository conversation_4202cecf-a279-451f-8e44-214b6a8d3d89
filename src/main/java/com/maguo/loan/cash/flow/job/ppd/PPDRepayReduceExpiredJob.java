package com.maguo.loan.cash.flow.job.ppd;

import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.OfflineRepayReduce;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.UseState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.OfflineRepayReduceRepository;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * @作者 lys
 * @时间 2025/07/07 16:24
 */
@Component
@JobHandler("ppdRepayReduceExpiredJob")
public class PPDRepayReduceExpiredJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PPDRepayReduceExpiredJob.class);

    @Autowired
    private OfflineRepayReduceRepository offlineRepayReduceRepository;

    @Override
    public void doJob(JobParam jobParam) {
        logger.info("ppdRepayReduceExpiredJob jobParam:{}", JsonUtil.toJsonString(jobParam));
        if (Objects.isNull(jobParam)) {
            jobParam = new JobParam();
        }
        LocalDate localDate = jobParam.getStartDate() != null
            ? jobParam.getStartDate()
            : LocalDate.now().minusDays(1);
        List<OfflineRepayReduce> repayReduceList = offlineRepayReduceRepository.findByFlowChannelWithDate(FlowChannel.PPCJDL.name(), localDate);
        repayReduceList.forEach(r -> {
            r.setUseState(UseState.EXPIRED);
        });
        offlineRepayReduceRepository.saveAll(repayReduceList);
        logger.info("ppdRepayReduceExpiredJob done. count:{}", repayReduceList.size());
    }

}
