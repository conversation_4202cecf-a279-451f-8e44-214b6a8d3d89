package com.maguo.loan.cash.flow.remote.nfsp;


public enum CommonRequestType {
    /**
     * 协议绑卡申请
     */
    AGREEMENT_BIND_CARD_APPLY("协议绑卡申请", "pay/agreement/applyBindCard"),
    /**
     * 协议绑卡确认
     */
    AGREEMENT_BIND_CARD_CONFIRM("协议绑卡确认", "pay/agreement/bindCardConfirm"),
    /**
     * 协议签章
     */
    AGREEMENT_SIGN("协议签章", "trustSign/allSignContract"),
    /**
     * 签章结果查询
     */
    AGREEMENT_SIGN_RESULT("签章结果查询", "trustSign/getContractResult"),
    /**
     * 协议扣款
     */
    AGREEMENT_WITHHOLD("协议扣款", "pay/agreement/withholdUseThirdPartyAgrNo"),
    AGREEMENT_WITHHOLD_RESULT("扣款结果查询", "pay/queryOrder"),
    SMS_SEND("发送短信", "message/sms"),

    /**
     * 转账支付申请
     */
    TRANS_PAY_APPLY("转账支付申请", "pay/agreement/transPay"),

    /**
     * 转账支付查询
     */
    TRANS_PAY_QUERY("转账支付查询", "pay/agreement/transPayQuery"),
    QUERY_BINDING("协议支付查询绑卡", "pay/agreement/checkAgreementNo");

    private final String desc;

    private final String path;

    CommonRequestType(String desc, String path) {
        this.desc = desc;
        this.path = path;
    }

    public String getDesc() {
        return desc;
    }

    public String getPath() {
        return path;
    }
}
