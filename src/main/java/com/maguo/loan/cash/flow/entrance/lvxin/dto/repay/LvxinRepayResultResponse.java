package com.maguo.loan.cash.flow.entrance.lvxin.dto.repay;

import com.maguo.loan.cash.flow.entrance.common.enums.RepayResult;

import java.math.BigDecimal;

/**
 * @Description 还款结果查询 响应参数
 * @Date 2024/5/27 16:51
 * @Version v1.0
 **/
public class LvxinRepayResultResponse {
    //还款流水标识
    private String repayId;
    //期次period
    private Integer period;
    //请求流水号
    private String partnerOrderNo;
    //还款状态
    private RepayResult repayStatus;
    //实还款时间
    private String actRepayTime;

    //实还款本金
    private BigDecimal actPrincipalAmt;
    //实还利息
    private BigDecimal actInterestAmt;
    //实还罚息
    private BigDecimal actPenaltyAmt;
    //实还咨询费
    private BigDecimal actConsultFee;
    //实还其他费用
    private BigDecimal actBreachAmt;

    //实还总额
    private BigDecimal actAmount;
    //实还总额
    private BigDecimal remainPrincipalAmt;
    //剩余利息
    private BigDecimal remainInterestAmt;
    //剩余罚息
    private BigDecimal remainPenaltyAmt;
    //剩余咨询费
    private BigDecimal remainConsultFee;
    //剩余其他费用
    private BigDecimal remainBreachAmt;
    //剩余待还总额
    private BigDecimal remainAmount;

    private String repayReason;

    public String getRepayId() {
        return repayId;
    }

    public void setRepayId(String repayId) {
        this.repayId = repayId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public RepayResult getRepayStatus() {
        return repayStatus;
    }

    public void setRepayStatus(RepayResult repayStatus) {
        this.repayStatus = repayStatus;
    }

    public String getActRepayTime() {
        return actRepayTime;
    }

    public void setActRepayTime(String actRepayTime) {
        this.actRepayTime = actRepayTime;
    }

    public BigDecimal getActPrincipalAmt() {
        return actPrincipalAmt;
    }

    public void setActPrincipalAmt(BigDecimal actPrincipalAmt) {
        this.actPrincipalAmt = actPrincipalAmt;
    }

    public BigDecimal getActInterestAmt() {
        return actInterestAmt;
    }

    public void setActInterestAmt(BigDecimal actInterestAmt) {
        this.actInterestAmt = actInterestAmt;
    }

    public BigDecimal getActPenaltyAmt() {
        return actPenaltyAmt;
    }

    public void setActPenaltyAmt(BigDecimal actPenaltyAmt) {
        this.actPenaltyAmt = actPenaltyAmt;
    }

    public BigDecimal getActConsultFee() {
        return actConsultFee;
    }

    public void setActConsultFee(BigDecimal actConsultFee) {
        this.actConsultFee = actConsultFee;
    }

    public BigDecimal getActBreachAmt() {
        return actBreachAmt;
    }

    public void setActBreachAmt(BigDecimal actBreachAmt) {
        this.actBreachAmt = actBreachAmt;
    }

    public BigDecimal getActAmount() {
        return actAmount;
    }

    public void setActAmount(BigDecimal actAmount) {
        this.actAmount = actAmount;
    }

    public BigDecimal getRemainPrincipalAmt() {
        return remainPrincipalAmt;
    }

    public void setRemainPrincipalAmt(BigDecimal remainPrincipalAmt) {
        this.remainPrincipalAmt = remainPrincipalAmt;
    }

    public BigDecimal getRemainInterestAmt() {
        return remainInterestAmt;
    }

    public void setRemainInterestAmt(BigDecimal remainInterestAmt) {
        this.remainInterestAmt = remainInterestAmt;
    }

    public BigDecimal getRemainPenaltyAmt() {
        return remainPenaltyAmt;
    }

    public void setRemainPenaltyAmt(BigDecimal remainPenaltyAmt) {
        this.remainPenaltyAmt = remainPenaltyAmt;
    }

    public BigDecimal getRemainConsultFee() {
        return remainConsultFee;
    }

    public void setRemainConsultFee(BigDecimal remainConsultFee) {
        this.remainConsultFee = remainConsultFee;
    }

    public BigDecimal getRemainBreachAmt() {
        return remainBreachAmt;
    }

    public void setRemainBreachAmt(BigDecimal remainBreachAmt) {
        this.remainBreachAmt = remainBreachAmt;
    }

    public BigDecimal getRemainAmount() {
        return remainAmount;
    }

    public void setRemainAmount(BigDecimal remainAmount) {
        this.remainAmount = remainAmount;
    }

    public String getRepayReason() {
        return repayReason;
    }

    public void setRepayReason(String repayReason) {
        this.repayReason = repayReason;
    }
}
