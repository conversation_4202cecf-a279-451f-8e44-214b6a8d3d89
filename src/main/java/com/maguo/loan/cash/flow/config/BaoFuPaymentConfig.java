package com.maguo.loan.cash.flow.config;

import com.zsjz.third.part.LocalCachedPayKeyFetcher;
import com.zsjz.third.part.PayConfig;
import com.zsjz.third.part.PayConfigException;
import com.zsjz.third.part.PayMerchantConfig;
import com.zsjz.third.part.baofoo.BaoFuBindCardService;
import com.zsjz.third.part.baofoo.BaoFuBindConfirmService;
import com.zsjz.third.part.baofoo.BaoFuChargeQueryService;
import com.zsjz.third.part.baofoo.BaoFuChargeService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

@Configuration
@ConfigurationProperties(prefix = "pay.baofu")
public class BaoFuPaymentConfig {

    @Value("${pay.baofu.paymentUrl}")
    private String paymentUrl;
    @Value("${pay.baofu.unionGatewayUrl}")
    private String unionGatewayUrl;
    @Value("${pay.baofu.terminalId}")
    private String terminalId;
    @Value("${pay.baofu.merchantId}")
    private String merchantId;
    @Value("${pay.baofu.defrayUrl}")
    private String defrayUrl;
    @Value("${pay.baofu.privateKey}")
    private String privateKey;
    @Value("${pay.baofu.publicKey}")
    private String publicKey;
    @Value("${pay.baofu.password}")
    private String password;

    public String getPaymentUrl() {
        return paymentUrl;
    }

    public void setPaymentUrl(String paymentUrl) {
        this.paymentUrl = paymentUrl;
    }

    public String getUnionGatewayUrl() {
        return unionGatewayUrl;
    }

    public void setUnionGatewayUrl(String unionGatewayUrl) {
        this.unionGatewayUrl = unionGatewayUrl;
    }

    public String getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getDefrayUrl() {
        return defrayUrl;
    }

    public void setDefrayUrl(String defrayUrl) {
        this.defrayUrl = defrayUrl;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Bean
    public PayConfig payConfig() {
        PayConfig config = new PayConfig();
        config.getBaoFu().setPaymentUrl(paymentUrl);
        config.getBaoFu().setUnionGatewayUrl(unionGatewayUrl);
        config.getBaoFu().setDefrayUrl(defrayUrl);
        PayMerchantConfig merchantConfig = new PayMerchantConfig();
        merchantConfig.setTerminalId(terminalId);
        merchantConfig.setMerchantId(merchantId);
        merchantConfig.setPrivateKeyPassword(password);
        config.addMerchantConfig(merchantConfig);
        return config;
    }

    @Bean
    @Primary
    public LocalCachedPayKeyFetcher keyFetcher(PayConfig payConfig) throws IOException {
        return new LocalCachedPayKeyFetcher() {
            private PublicKey publicKey;
            private PrivateKey privateKey;

            @Override
            public PublicKey loadPublicKey(PayMerchantConfig merchantConfig) throws IOException {
                if (this.publicKey == null) {
                    synchronized (this) {
                        if (publicKey == null) {
                            this.publicKey = getPublicKey();
                        }
                    }
                }
                return publicKey;
            }

            @Override
            public PrivateKey loadPrivateKey(PayMerchantConfig merchantConfig) throws IOException {
                if (this.privateKey == null) {
                    synchronized (this) {
                        if (privateKey == null) {
                            this.privateKey = getPrivateKey();
                        }
                    }
                }
                return privateKey;
            }
        };
    }

    // 初始化各个服务 Bean
    @Bean
    public BaoFuBindCardService baofuBindCardService(
            PayConfig payConfig,
            LocalCachedPayKeyFetcher keyFetcher) {
        return new BaoFuBindCardService(payConfig, keyFetcher);
    }

    @Bean
    public BaoFuBindConfirmService baofuBindConfirmService(
            PayConfig payConfig,
            LocalCachedPayKeyFetcher keyFetcher) {
        return new BaoFuBindConfirmService(payConfig, keyFetcher);
    }

    @Bean
    public BaoFuChargeService baofuChargeService(
            PayConfig payConfig,
            LocalCachedPayKeyFetcher keyFetcher) {
        return new BaoFuChargeService(payConfig, keyFetcher);
    }

    @Bean
    public BaoFuChargeQueryService baofuChargeQueryService(
            PayConfig payConfig,
            LocalCachedPayKeyFetcher keyFetcher) {
        return new BaoFuChargeQueryService(payConfig, keyFetcher);
    }

    // Base64 长度补全工具方法
    private static String padBase64(String base64) {
        int padding = 4 - (base64.length() % 4);
        if (padding != 4) {
            return base64 + "====".substring(0, padding);
        }
        return base64;
    }

    private PublicKey getPublicKey() {
        X509Certificate x509cert = null;
        try {
            // 空内容检查
            if (publicKey == null || publicKey.trim().isEmpty()) {
                throw new IllegalArgumentException("公钥内容为空");
            }

            // 清洗 Base64
            String cleanBase64 = publicKey
                    .replaceAll("-----BEGIN CERTIFICATE-----", "")
                    .replaceAll("-----END CERTIFICATE-----", "")
                    .replaceAll("\\s", "");

            // 长度校验
            if (cleanBase64.length() % 4 != 0) {
                throw new IllegalArgumentException("Base64 长度非法，需补齐 = 号");
            }

            // 自动补全 Base64 长度（添加等号）
            cleanBase64 = padBase64(cleanBase64);

            // 解码
            byte[] certBytes = Base64.getDecoder().decode(cleanBase64);
            if (certBytes.length == 0) {
                throw new IllegalArgumentException("Base64 解码后为空");
            }
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            x509cert = (X509Certificate) cf.generateCertificate(new ByteArrayInputStream(certBytes));
        } catch (GeneralSecurityException e) {
            throw new PayConfigException("加载公钥异常", e);
        }
        return x509cert.getPublicKey();
    }

    private PrivateKey getPrivateKey() {
        PrivateKey prikey = null;
        try {
            privateKey = padBase64(privateKey);
            byte[] pkcs8Bytes = Base64.getDecoder().decode(privateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(pkcs8Bytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA"); // 根据密钥类型调整
            prikey = keyFactory.generatePrivate(keySpec);
            if (prikey == null) {
                throw new PayConfigException("没有找到匹配私钥");
            }
            return prikey;
        } catch (GeneralSecurityException e) {
            throw new PayConfigException("加载私钥异常", e);
        }
    }


}
