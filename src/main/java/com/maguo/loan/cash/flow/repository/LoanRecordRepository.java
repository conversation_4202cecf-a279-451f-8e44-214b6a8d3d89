package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.LoanRecord;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;


/**
 *
 */
public interface LoanRecordRepository extends JpaRepository<LoanRecord, String> {

    Boolean existsByOrderIdAndLoanStateNotIn(String orderId, ProcessState... state);

    Boolean existsByLoanIdAndLoanStateNotIn(String loanId, ProcessState... state);

    List<LoanRecord> findByApplyTimeBetweenAndLoanStateIn(LocalDateTime applyTimeStart, LocalDateTime applyTimeEnd, Collection<ProcessState> loanStates);
}

