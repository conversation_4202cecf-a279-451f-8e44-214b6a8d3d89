package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.CreditService;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/6/1 11:07
 */
@Component
public class RecreditApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(RecreditApplyListener.class);

    @Autowired
    private CreditService creditService;

    public RecreditApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.RECREDIT_APPLY)
    public void listenRecreditApply(Message message, Channel channel) {
        String creditId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听重新授信申请:{}", creditId);
            creditService.bankRecredit(creditId);
        } catch (Exception e) {
            processException(creditId, message, e, "重新授信申请异常", getMqService()::submitRecreditResultQueryDelay);
        } finally {
            ackMsg(creditId, message, channel);
        }
    }
}
