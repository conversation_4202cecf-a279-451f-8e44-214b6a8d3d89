package com.maguo.loan.cash.flow.entrance.lvxin.dto.repay;

import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.SyncBindCard;
import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;

/**
 * @ClassName RepayRequest
 * <AUTHOR>
 * @Description 还款请求
 * @Date 2024/5/27 16:50
 * @Version v1.0
 **/
public class LvxinRepayRequest {
    /**
     * 绿信放款id
     */
    @NotBlank(message = "绿信放款id不能为空")
    private String loanGid;
    /**
     * 绿信授信流水号
     */
    @NotBlank(message = "授信流水号不能为空")
    private String partnerUserId;
    /**
     * 请求流水号
     */
    private String partnerOrderNo;
    /**
     * 还款期数
     */
    private Integer period;
    /**
     * 绿信 还款订单号
     */
    private String repaymentGid;
    //客户扣款总金额
    @NotBlank(message = "客户扣款总金额不能为空")
    private BigDecimal repayTotalAmount;
    //应还本金
    @NotBlank(message = "应还本金不能为空")
    private BigDecimal repayPrincipal;
    //应还咨询费
    @NotBlank(message = "应还咨询费不能为空")
    private BigDecimal repayConsultingFee;
    //应还利息
    @NotBlank(message = "应还利息不能为空")
    private BigDecimal repayInterest;
    //应还罚息
    private BigDecimal repayPenaltyAmt;
    //应还罚息

    private BigDecimal repayBreachAmt;
    //	还款类型	1：当期 2：结清
    @NotBlank(message = "还款类型不能为空")
    private String repayType;
    // 还款目的	REPAY:偿还
    private String repayPurpose;
    //还款渠道	ONLINE线上 OFFLINE线下
    private String repayMode;

    //咨询费减免
    private BigDecimal consultationFeeWaiver;

    //罚息减免
    private BigDecimal penaltyInterestWaiver;


    //客户实际还款日期
    private String repayDate;

    /**
     * 银行卡签约信息
     */
    private SyncBindCard syncBindCard;

    public SyncBindCard getSyncBindCard() {
        return syncBindCard;
    }

    public void setSyncBindCard(SyncBindCard syncBindCard) {
        this.syncBindCard = syncBindCard;
    }

    public String getLoanGid() {
        return loanGid;
    }

    public void setLoanGid(String loanGid) {
        this.loanGid = loanGid;
    }

    public String getPartnerUserId() {
        return partnerUserId;
    }

    public void setPartnerUserId(String partnerUserId) {
        this.partnerUserId = partnerUserId;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepaymentGid() {
        return repaymentGid;
    }

    public void setRepaymentGid(String repaymentGid) {
        this.repaymentGid = repaymentGid;
    }

    public BigDecimal getRepayTotalAmount() {
        return repayTotalAmount;
    }

    public void setRepayTotalAmount(BigDecimal repayTotalAmount) {
        this.repayTotalAmount = repayTotalAmount;
    }

    public BigDecimal getRepayPrincipal() {
        return repayPrincipal;
    }

    public void setRepayPrincipal(BigDecimal repayPrincipal) {
        this.repayPrincipal = repayPrincipal;
    }

    public BigDecimal getRepayConsultingFee() {
        return repayConsultingFee;
    }

    public void setRepayConsultingFee(BigDecimal repayConsultingFee) {
        this.repayConsultingFee = repayConsultingFee;
    }

    public BigDecimal getRepayInterest() {
        return repayInterest;
    }

    public void setRepayInterest(BigDecimal repayInterest) {
        this.repayInterest = repayInterest;
    }

    public BigDecimal getRepayPenaltyAmt() {
        return repayPenaltyAmt;
    }

    public void setRepayPenaltyAmt(BigDecimal repayPenaltyAmt) {
        this.repayPenaltyAmt = repayPenaltyAmt;
    }

    public BigDecimal getRepayBreachAmt() {
        return repayBreachAmt;
    }

    public void setRepayBreachAmt(BigDecimal repayBreachAmt) {
        this.repayBreachAmt = repayBreachAmt;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(String repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public BigDecimal getConsultationFeeWaiver() {
        return consultationFeeWaiver;
    }

    public void setConsultationFeeWaiver(BigDecimal consultationFeeWaiver) {
        this.consultationFeeWaiver = consultationFeeWaiver;
    }

    public BigDecimal getPenaltyInterestWaiver() {
        return penaltyInterestWaiver;
    }

    public void setPenaltyInterestWaiver(BigDecimal penaltyInterestWaiver) {
        this.penaltyInterestWaiver = penaltyInterestWaiver;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }
}
