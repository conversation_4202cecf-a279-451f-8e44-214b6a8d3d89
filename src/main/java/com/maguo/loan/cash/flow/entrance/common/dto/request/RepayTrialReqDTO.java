package com.maguo.loan.cash.flow.entrance.common.dto.request;

import jakarta.validation.constraints.NotBlank;

/**
 * 还款试算入参
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
public class RepayTrialReqDTO {
    /**
     * 合作机构单号
     */
    @NotBlank
    private String partnerOrderNo;
    /**
     * 还款方式
     */
    @NotBlank
    private String repayPurpose;
    /**
     * 期次
     */
    @NotBlank
    private Integer period;

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(String repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }
}
