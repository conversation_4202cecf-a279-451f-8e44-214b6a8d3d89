package com.maguo.loan.cash.flow.vo.bound;

import com.maguo.loan.cash.flow.enums.ProcessState;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;

/**
 * <AUTHOR>
 * @date 2023/10/14
 */
public class BindResultVo {
    public BindResultVo() {

    }


    public BindResultVo(ProcessState state, String failReason) {
        this.state = state;
        this.failReason = failReason;
    }

    /**
     * 绑卡状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState state;

    /**
     * 失败原因
     */
    private String failReason;

    private String creditId;

    private String orderId;

    public ProcessState getState() {
        return state;
    }

    public void setState(ProcessState state) {
        this.state = state;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
