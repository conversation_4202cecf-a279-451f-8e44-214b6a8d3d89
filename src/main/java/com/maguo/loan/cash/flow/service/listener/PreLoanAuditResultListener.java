package com.maguo.loan.cash.flow.service.listener;



import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.PreLoanService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Component
public class PreLoanAuditResultListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(PreLoanAuditResultListener.class);
    public PreLoanAuditResultListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private PreLoanService preLoanService;

    @RabbitListener(queues = RabbitConfig.Queues.PRE_LOAN_AUDIT_RESULT)
    public void preLoanAuditResult(Message message, Channel channel) {
        String id = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("收到贷前审批查询消息:{}", id);
            preLoanService.preLoanAuditResult(id);
        } catch (Exception e) {
            processException(id, message, e, "贷前审批消息处理异常", getMqService()::submitPreLoanAuditResultDelay);
        } finally {
            ackMsg(id, message, channel);
        }
    }
}
