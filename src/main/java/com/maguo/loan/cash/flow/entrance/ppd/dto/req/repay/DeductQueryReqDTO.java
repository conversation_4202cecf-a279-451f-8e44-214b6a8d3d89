package com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay;

/**
 * 扣款结果查询请求数据传输对象
 */
public class DeductQueryReqDTO {

    /**
     * 放款请求流水号
     * <p>唯一标识原始放款交易，长度限制64字符</p>
     */
    private String loanReqNo;

    /**
     * 请求方系统代码
     * <p>固定值：CJCYDL_PPD2 或 CJCYDL_PPD3</p>
     */
    private String sourceCode;

    /**
     * 还款请求流水号
     * <p>唯一标识当前扣款交易，长度限制64字符</p>
     */
    private String repayNo;

    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getRepayNo() {
        return repayNo;
    }

    public void setRepayNo(String repayNo) {
        this.repayNo = repayNo;
    }
}
