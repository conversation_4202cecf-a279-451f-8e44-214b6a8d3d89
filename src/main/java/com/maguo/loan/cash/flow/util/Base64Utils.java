package com.maguo.loan.cash.flow.util;




import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2023/6/4 15:49
 */
public class Base64Utils {


    /**
     * base64转化成 inputStream
     *
     * @param base64
     * @return
     */
    public static InputStream base64ToInputStream(String base64) throws BizException {
        ByteArrayInputStream stream = null;
        try {
            byte[] bytes = decodeBase64(base64);
            stream = new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            throw new BizException(ResultCode.PARAM_ILLEGAL);
        }
        return stream;
    }


    /**
     * base64转化成 inputStream
     *
     * @param base64
     * @return
     */
    public static InputStream base64ToInputStream(byte[] base64) {
        return new ByteArrayInputStream(base64);
    }


    public static byte[] decodeBase64(String base64) {
        try {
            return Base64.getDecoder().decode(base64);
        } catch (Exception e) {
            throw new BizException(ResultCode.PARAM_ILLEGAL);
        }

    }

    /**
     * 将inputstream转为Base64
     *
     * @param is
     * @return
     * @throws Exception
     */
    public static String inputStream2Base64(InputStream is) throws BizException {
        byte[] data = null;
        final int oneThousand = 1000;
        try {
            ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
            byte[] buff = new byte[oneThousand];
            int rc = 0;
            while ((rc = is.read(buff, 0, oneThousand)) > 0) {
                swapStream.write(buff, 0, rc);
            }
            data = swapStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    // ignore
                }
            }
        }

        return Base64.getEncoder().encodeToString(data);
    }
}
