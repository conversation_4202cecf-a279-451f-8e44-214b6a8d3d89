package com.maguo.loan.cash.flow.enums;

public enum RemoteCallStatus {
    SUCCESS("0000", "调用成功"),
    SIGNATURE_FAILURE("1000", "验签失败"),
    PARAMETER_ENCODING_ERROR("1001", "参数非UTF-8编码"),
    INVALID_REQUEST_PARAMETERS("1002", "请求参数错误"),
    MERCHANT_FREEZE("1003", "商户冻结"),
    MERCHANT_DISABLED("1004", "商户停用"),
    MERCHANT_ACCOUNT_EXPIRED("1005", "商户账号过期"),
    EXCEEDED_CALL_FREQUENCY_LIMIT("1006", "调用频率过高"),
    FREE_USAGE_LIMIT_EXHAUSTED("1007", "免费次数用尽"),
    UNAUTHORIZED_SERVICE("1008", "服务无权限"),
    APP_KEY_VALIDATION_FAILURE("1009", "App_key验证失败"),
    REQUEST_EXPIRED("1010", "请求过期"),
    INVALID_IMAGE_FILE("1011", "文件不是图片文件或已经损坏"),
    INVALID_IMAGE_SIZE_OR_FORMAT("1012", "图片大小或格式不符合要求"),
    UPSTREAM_SERVICE_EXCEPTION("2000", "上游服务异常"),
    OTHER_ERROR("9999", "其他错误");

    private final String code;
    private final String message;

    RemoteCallStatus(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
