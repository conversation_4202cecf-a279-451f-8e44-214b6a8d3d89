package com.maguo.loan.cash.flow.dto;




import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.WriteOffTypeEnum;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class OfflineRepayApplyRequest {

    /**
     * 还款方式
     */
    private RepayPurpose repayPurpose;

    /**
     * 期数
     */
    private Integer period;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 实还金额
     */
    private BigDecimal actAmount;

    /**
     * 溢出款金额
     */
    private BigDecimal overflowAmount;

    /**
     * 操作人员
     */
    private String operator;


    /**
     * 销账类型
     */
    private WriteOffTypeEnum writeOffType;

    private String outerRepayNo;

    //咨询费减免
    private BigDecimal consultationFeeWaiver;

    //罚息减免
    private BigDecimal penaltyInterestWaiver;


    private String repayDate;

    private String reductNo;

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public String getOuterRepayNo() {
        return outerRepayNo;
    }

    public void setOuterRepayNo(String outerRepayNo) {
        this.outerRepayNo = outerRepayNo;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getActAmount() {
        return actAmount;
    }

    public void setActAmount(BigDecimal actAmount) {
        this.actAmount = actAmount;
    }

    public BigDecimal getOverflowAmount() {
        return overflowAmount;
    }

    public void setOverflowAmount(BigDecimal overflowAmount) {
        this.overflowAmount = overflowAmount;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public WriteOffTypeEnum getWriteOffType() {
        return writeOffType;
    }

    public void setWriteOffType(WriteOffTypeEnum writeOffType) {
        this.writeOffType = writeOffType;
    }

    public BigDecimal getConsultationFeeWaiver() {
        return consultationFeeWaiver;
    }

    public void setConsultationFeeWaiver(BigDecimal consultationFeeWaiver) {
        this.consultationFeeWaiver = consultationFeeWaiver;
    }

    public BigDecimal getPenaltyInterestWaiver() {
        return penaltyInterestWaiver;
    }

    public void setPenaltyInterestWaiver(BigDecimal penaltyInterestWaiver) {
        this.penaltyInterestWaiver = penaltyInterestWaiver;
    }

    public String getReductNo() {
        return reductNo;
    }

    public void setReductNo(String reductNo) {
        this.reductNo = reductNo;
    }
}
