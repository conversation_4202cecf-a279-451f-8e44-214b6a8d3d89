package com.maguo.loan.cash.flow.entrance.lvxin.controller;


import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.ApprovalRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.ApprovalResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.CreditQueryRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.CreditQueryResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.service.LvxinService;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.util.BaseConstants;
import com.maguo.loan.cash.flow.util.RedisKeyConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;

@RestController
@RequestMapping("lvxin")
public class LvxinCreditController {

    @Autowired
    private LvxinService lvxinService;

    @Autowired
    private LockService lockService;

    /**
     * 授信申请
     */
    @PostMapping("/api/partner/v1/apply")
    public LvxinResponse creditApply(@RequestBody ApprovalRequest approvalRequest) {
        Locker lock = lockService.getLock(RedisKeyConstants.APPROVAL_APPLY + approvalRequest.getIdCard());
        ApprovalResponse approval=new ApprovalResponse();
        try {
            boolean locked = lock.tryLock(Duration.ZERO, BaseConstants.DEFAULT_LOCK_RELEASE_TIME);
            if (locked) {
                approval=lvxinService.approval(approvalRequest);
            } else {
                throw new BizException(ResultCode.APPROVAL_APPLY_REPEAT);
            }
        } catch (InterruptedException e) {
            //ignore
        } finally {
            lock.unlock();
        }
        return LvxinResponse.success(approval);
    }

    /**
     * 查询授信结果
     */
    @PostMapping("/api/partner/v1/queryApplyResult")
    public LvxinResponse creditResultQuery(@RequestBody CreditQueryRequest creditQueryRequest) {
        CreditQueryResponse response = lvxinService.creditResultQuery(creditQueryRequest);
        return LvxinResponse.success(response);
    }


}
