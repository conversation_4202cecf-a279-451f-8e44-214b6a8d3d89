package com.maguo.loan.cash.flow.util;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;
import java.util.Date;
import java.util.TimeZone;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 时间日期转换工具类（线程安全）
 *
 * 功能：
 * 1. Date/LocalDateTime/Instant/LocalDate/LocalTime 之间的互转
 * 2. 日期时间字符串与对象之间的转换
 * 3. 时区转换
 * 4. 时间加减计算
 * 5. 时间差计算
 * 6. 时间戳转换
 */
public class DateTimeUtils {

    // 常用模式缓存（提高性能）
    private static final ConcurrentMap<String, DateTimeFormatter> FORMATTER_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentMap<String, ThreadLocal<SimpleDateFormat>> SIMPLE_DATE_FORMAT_CACHE = new ConcurrentHashMap<>();

    // 常用格式常量
    public static final String PATTERN_DATETIME = "yyyy-MM-dd HH:mm:ss";
    public static final String PATTERN_DATE = "yyyy-MM-dd";
    public static final String PATTERN_TIME = "HH:mm:ss";
    public static final String PATTERN_DATETIME_COMPACT = "yyyyMMddHHmmss";
    public static final String PATTERN_DATE_COMPACT = "yyyyMMdd";
    public static final String PATTERN_TIME_COMPACT = "HHmmss";
    public static final String PATTERN_ISO_DATETIME = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String PATTERN_ISO_DATE = "yyyy-MM-dd";
    public static final String PATTERN_ISO_TIME = "HH:mm:ss";

    // 默认时区（系统时区）
    public static final ZoneId DEFAULT_ZONE_ID = ZoneId.systemDefault();
    public static final TimeZone DEFAULT_TIME_ZONE = TimeZone.getDefault();

    /**
     * 获取 DateTimeFormatter（线程安全）
     */
    private static DateTimeFormatter getFormatter(String pattern) {
        return FORMATTER_CACHE.computeIfAbsent(pattern, DateTimeFormatter::ofPattern);
    }

    /**
     * 获取 SimpleDateFormat（线程安全）
     */
    private static SimpleDateFormat getSimpleDateFormat(String pattern) {
        ThreadLocal<SimpleDateFormat> threadLocal = SIMPLE_DATE_FORMAT_CACHE.computeIfAbsent(
            pattern,
            p -> ThreadLocal.withInitial(() -> new SimpleDateFormat(p))
        );
        return threadLocal.get();
    }

    // ================== Date 与其他类型互转 ==================

    /**
     * Date 转 LocalDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        return date.toInstant().atZone(DEFAULT_ZONE_ID).toLocalDateTime();
    }

    /**
     * LocalDateTime 转 Date
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(DEFAULT_ZONE_ID).toInstant());
    }

    /**
     * Date 转 LocalDate
     */
    public static LocalDate dateToLocalDate(Date date) {
        return date.toInstant().atZone(DEFAULT_ZONE_ID).toLocalDate();
    }

    /**
     * LocalDate 转 Date
     */
    public static Date localDateToDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(DEFAULT_ZONE_ID).toInstant());
    }

    /**
     * Date 转 Instant
     */
    public static Instant dateToInstant(Date date) {
        return date.toInstant();
    }

    /**
     * Instant 转 Date
     */
    public static Date instantToDate(Instant instant) {
        return Date.from(instant);
    }

    // ================== 字符串与时间对象互转 ==================

    /**
     * 字符串转 LocalDateTime
     */
    public static LocalDateTime stringToLocalDateTime(String dateTimeStr, String pattern) {
        return LocalDateTime.parse(dateTimeStr, getFormatter(pattern));
    }

    /**
     * LocalDateTime 转字符串
     */
    public static String localDateTimeToString(LocalDateTime dateTime, String pattern) {
        return dateTime.format(getFormatter(pattern));
    }

    /**
     * 字符串转 LocalDate
     */
    public static LocalDate stringToLocalDate(String dateStr, String pattern) {
        return LocalDate.parse(dateStr, getFormatter(pattern));
    }

    /**
     * LocalDate 转字符串
     */
    public static String localDateToString(LocalDate date, String pattern) {
        return date.format(getFormatter(pattern));
    }

    /**
     * 字符串转 Date
     */
    public static Date stringToDate(String dateStr, String pattern) {
        try {
            return getSimpleDateFormat(pattern).parse(dateStr);
        } catch (ParseException e) {
            throw new DateTimeParseException("日期解析失败: " + dateStr, dateStr, 0, e);
        }
    }

    /**
     * Date 转字符串
     */
    public static String dateToString(Date date, String pattern) {
        return getSimpleDateFormat(pattern).format(date);
    }

    // ================== 时间戳操作 ==================

    /**
     * 时间戳转 LocalDateTime
     */
    public static LocalDateTime timestampToLocalDateTime(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), DEFAULT_ZONE_ID);
    }

    /**
     * LocalDateTime 转时间戳
     */
    public static long localDateTimeToTimestamp(LocalDateTime dateTime) {
        return dateTime.atZone(DEFAULT_ZONE_ID).toInstant().toEpochMilli();
    }

    /**
     * 时间戳转 Date
     */
    public static Date timestampToDate(long timestamp) {
        return new Date(timestamp);
    }

    /**
     * Date 转时间戳
     */
    public static long dateToTimestamp(Date date) {
        return date.getTime();
    }

    // ================== 时区转换 ==================

    /**
     * 转换时区 (LocalDateTime)
     */
    public static LocalDateTime convertZone(LocalDateTime dateTime, ZoneId fromZone, ZoneId toZone) {
        return dateTime.atZone(fromZone).withZoneSameInstant(toZone).toLocalDateTime();
    }

    /**
     * 转换时区 (Date)
     */
    public static Date convertZone(Date date, TimeZone fromZone, TimeZone toZone) {
        long time = date.getTime();
        return new Date(time - fromZone.getOffset(time) + toZone.getOffset(time));
    }

    /**
     * 将UTC时间转为本地时间 (LocalDateTime)
     */
    public static LocalDateTime utcToLocal(LocalDateTime utcTime) {
        return convertZone(utcTime, ZoneId.of("UTC"), DEFAULT_ZONE_ID);
    }

    /**
     * 将本地时间转为UTC时间 (LocalDateTime)
     */
    public static LocalDateTime localToUtc(LocalDateTime localTime) {
        return convertZone(localTime, DEFAULT_ZONE_ID, ZoneId.of("UTC"));
    }

    // ================== 时间计算 ==================

    /**
     * 增加时间 (LocalDateTime)
     */
    public static LocalDateTime plus(LocalDateTime dateTime, long amount, ChronoUnit unit) {
        return dateTime.plus(amount, unit);
    }

    /**
     * 减少时间 (LocalDateTime)
     */
    public static LocalDateTime minus(LocalDateTime dateTime, long amount, ChronoUnit unit) {
        return dateTime.minus(amount, unit);
    }

    /**
     * 增加天数 (LocalDate)
     */
    public static LocalDate plusDays(LocalDate date, int days) {
        return date.plusDays(days);
    }

    /**
     * 减少天数 (LocalDate)
     */
    public static LocalDate minusDays(LocalDate date, int days) {
        return date.minusDays(days);
    }

    // ================== 时间差计算 ==================

    /**
     * 计算两个时间之间的差值
     */
    public static long between(LocalDateTime start, LocalDateTime end, ChronoUnit unit) {
        return unit.between(start, end);
    }

    /**
     * 计算两个日期之间的天数差
     */
    public static long daysBetween(LocalDate start, LocalDate end) {
        return ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 计算两个时间之间的小时差
     */
    public static long hoursBetween(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.HOURS.between(start, end);
    }

    /**
     * 计算两个时间之间的分钟差
     */
    public static long minutesBetween(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.MINUTES.between(start, end);
    }

    // ================== 日期比较 ==================

    /**
     * 判断日期是否在今天之前
     */
    public static boolean isBeforeToday(LocalDate date) {
        return date.isBefore(LocalDate.now());
    }

    /**
     * 判断日期是否在今天之后
     */
    public static boolean isAfterToday(LocalDate date) {
        return date.isAfter(LocalDate.now());
    }

    /**
     * 判断两个日期是否在同一天
     */
    public static boolean isSameDay(LocalDate date1, LocalDate date2) {
        return date1.equals(date2);
    }

    /**
     * 判断时间是否在指定范围内
     */
    public static boolean isBetween(LocalDateTime time, LocalDateTime start, LocalDateTime end) {
        return !time.isBefore(start) && !time.isAfter(end);
    }

    // ================== 获取常用时间 ==================

    /**
     * 获取当前时间字符串
     */
    public static String nowString(String pattern) {
        return LocalDateTime.now().format(getFormatter(pattern));
    }

    /**
     * 获取当天的开始时间 (00:00:00)
     */
    public static LocalDateTime startOfDay(LocalDate date) {
        return date.atStartOfDay();
    }

    /**
     * 获取当天的结束时间 (23:59:59.999)
     */
    public static LocalDateTime endOfDay(LocalDate date) {
        return date.atTime(LocalTime.MAX);
    }

    /**
     * 获取本周的第一天 (周一)
     */
    public static LocalDate firstDayOfWeek() {
        return LocalDate.now().with(DayOfWeek.MONDAY);
    }

    /**
     * 获取本月的第一天
     */
    public static LocalDate firstDayOfMonth() {
        return LocalDate.now().withDayOfMonth(1);
    }

    // ================== 特殊格式处理 ==================

    /**
     *  (yyyyMMddHHmmss)
     */
    public static LocalDateTime parse_yyyyMMddHHmmss(String dateTimeStr) {
        return stringToLocalDateTime(dateTimeStr, PATTERN_DATETIME_COMPACT);
    }

    /**
     *  (yyyyMMddHHmmss)
     */
    public static String format_yyyyMMddHHmmss(LocalDateTime dateTime) {
        return localDateTimeToString(dateTime, PATTERN_DATETIME_COMPACT);
    }
    public static String format_yyyy_MM_dd(LocalDateTime dateTime) {
        return localDateTimeToString(dateTime, PATTERN_DATE);
    }

    /**
     * 解析ISO格式时间 (yyyy-MM-dd'T'HH:mm:ss)
     */
    public static LocalDateTime parseIsoDateTime(String dateTimeStr) {
        return stringToLocalDateTime(dateTimeStr, PATTERN_ISO_DATETIME);
    }

    /**
     * 格式化为ISO格式时间 (yyyy-MM-dd'T'HH:mm:ss)
     */
    public static String formatIsoDateTime(LocalDateTime dateTime) {
        return localDateTimeToString(dateTime, PATTERN_ISO_DATETIME);
    }

    // ================== 异常处理 ==================

    /**
     * 安全解析日期时间
     * @return 解析成功返回 LocalDateTime，失败返回 null
     */
    public static LocalDateTime safeParseLocalDateTime(String dateTimeStr, String pattern) {
        try {
            return stringToLocalDateTime(dateTimeStr, pattern);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    /**
     * 安全解析日期
     * @return 解析成功返回 LocalDate，失败返回 null
     */
    public static LocalDate safeParseLocalDate(String dateStr, String pattern) {
        try {
            return stringToLocalDate(dateStr, pattern);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    // ================== 中文日期时间格式化 ==================

    // 中文日期时间格式器（线程安全）
    private static final DateTimeFormatter CHINESE_DATETIME_FORMATTER =
        DateTimeFormatter.ofPattern("yyyy年MM月dd日HH时mm分");

    /**
     * 将时间对象格式化为中文日期时间字符串 (xxxx年xx月xx日xx时xx分)
     */
    public static String toChineseDateTime(TemporalAccessor temporal) {
        if (temporal == null) return null;
        return CHINESE_DATETIME_FORMATTER.format(temporal);
    }

    /**
     * LocalDateTime 转中文日期时间
     */
    public static String toChineseDateTime(LocalDateTime dateTime) {
        return toChineseDateTime((TemporalAccessor) dateTime);
    }

    /**
     * LocalDate 转中文日期时间 (时间部分补00时00分)
     */
    public static String toChineseDateTime(LocalDate date) {
        if (date == null) return null;
        return date.atStartOfDay().format(CHINESE_DATETIME_FORMATTER);
    }

    /**
     * Date 转中文日期时间
     */
    public static String toChineseDateTime(Date date) {
        if (date == null) return null;
        return toChineseDateTime(date.toInstant().atZone(DEFAULT_ZONE_ID).toLocalDateTime());
    }

    /**
     * 时间戳转中文日期时间
     */
    public static String toChineseDateTime(long timestamp) {
        return toChineseDateTime(Instant.ofEpochMilli(timestamp).atZone(DEFAULT_ZONE_ID).toLocalDateTime());
    }

    /**
     * 字符串时间转中文日期时间
     * @param dateTimeStr 原始时间字符串
     * @param pattern 原始时间格式
     */
    public static String toChineseDateTime(String dateTimeStr, String pattern) {
        if (dateTimeStr == null || pattern == null) return null;

        try {
            // 尝试解析为LocalDateTime
            return toChineseDateTime(LocalDateTime.parse(dateTimeStr, getFormatter(pattern)));
        } catch (DateTimeException e) {
            // 如果失败，尝试解析为LocalDate
            try {
                return toChineseDateTime(LocalDate.parse(dateTimeStr, getFormatter(pattern)));
            } catch (DateTimeException ex) {
                throw new DateTimeParseException("无法解析时间字符串: " + dateTimeStr, dateTimeStr, 0, ex);
            }
        }
    }

    /**
     * 安全转换为中文日期时间（解析失败返回null）
     */
    public static String safeToChineseDateTime(String dateTimeStr, String pattern) {
        try {
            return toChineseDateTime(dateTimeStr, pattern);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    // ================== 其他中文格式 ==================

    /**
     * 格式化为中文日期 (xxxx年xx月xx日)
     */
    public static String toChineseDate(LocalDate date) {
        if (date == null) return null;
        return date.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
    }

    /**
     * 格式化为中文时间 (xx时xx分)
     */
    public static String toChineseTime(LocalTime time) {
        if (time == null) return null;
        return time.format(DateTimeFormatter.ofPattern("HH时mm分"));
    }

    /**
     * 获取当前时间的中文表示
     */
    public static String currentChineseDateTime() {
        return toChineseDateTime(LocalDateTime.now());
    }


}
