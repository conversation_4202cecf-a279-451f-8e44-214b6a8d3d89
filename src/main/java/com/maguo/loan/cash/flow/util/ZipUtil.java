package com.maguo.loan.cash.flow.util;

import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

public class ZipUtil implements AutoCloseable {
    private ByteArrayOutputStream baos;
    private ZipArchiveOutputStream zaos;

    private static final int BUFFER_READ_SIZE = 1024;

    public ZipUtil() throws IOException {
        baos = new ByteArrayOutputStream();
        zaos = new ZipArchiveOutputStream(baos);
    }

    public void addEntry(InputStream inputStream, String fileName) throws IOException {
        ZipArchiveEntry zipEntry = new ZipArchiveEntry(fileName);
        zaos.putArchiveEntry(zipEntry);
        byte[] bytes = new byte[BUFFER_READ_SIZE];
        int length;
        while ((length = inputStream.read(bytes)) >= 0) {
            zaos.write(bytes, 0, length);
        }
        zaos.closeArchiveEntry();
        inputStream.close(); // 确保每个传入的InputStream在使用后都被关闭
    }

    public InputStream getZipInputStream() throws IOException {
        zaos.finish(); // 完成zip文件的构建
        return new ByteArrayInputStream(baos.toByteArray()); // 直接返回ByteArrayOutputStream的InputStream
    }

    @Override
    public void close() throws IOException {
        if (zaos != null) {
            zaos.close();
        }
        if (baos != null) {
            baos.close();
        }
    }
}
