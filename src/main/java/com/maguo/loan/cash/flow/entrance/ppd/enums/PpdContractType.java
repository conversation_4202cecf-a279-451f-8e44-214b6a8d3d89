package com.maguo.loan.cash.flow.entrance.ppd.enums;


/**
 * <AUTHOR>
 * @Description 拍拍贷协议、影像文件枚举
 * @Date 2025/1/1 22:36
 **/
public enum PpdContractType {
//
//    ID_HEAD("01", "身份证人头面", PhotoType.ID_CARD_FRONT, FileType.ID_HEAD, "jpg"),
//    ID_NATION("02", "身份证国徽面", PhotoType.ID_CARD_BACK, FileType.ID_NATION, "jpg"),
//    ID_FACE("03", "活体人脸", PhotoType.LIVE_PHOTO, FileType.ID_FACE, "jpg"),
//
//    SUMMARY_SYNTHESIS_AUTHORIZATION("zhsqs", "平台综合授权书(合并版)", null, FileType.SUMMARY_SYNTHESIS_AUTHORIZATION, "pdf"),
//    PLATFORM_USER_AGREEMENT_AND_PRIVACY_POLICY("mgxxsqs", "平台用户协议及隐私政策", null, FileType.PLATFORM_USER_AGREEMENT_AND_PRIVACY_POLICY, "pdf"),
//    ZZ_LN_PERSONAL_INFORMATION_QUERY_APPLY_LETTER("xxcxsqs", "中置辽农个人信息查询及使用授权书", null, FileType.ZZ_LN_PERSONAL_INFORMATION_QUERY_APPLY_LETTER, "pdf"),
//    ZZ_LN_SYNTHESIS_AUTHORIZATION("xxcxsqs_2", "综合授权书-资方", null, FileType.ZZ_LN_SYNTHESIS_AUTHORIZATION, "pdf"),
//    ZZ_LN_PROMISE_NOT_STUDENT("fxscnh", "中置辽宁非学生承诺函", null, FileType.ZZ_LN_PROMISE_NOT_STUDENT, "pdf"),
//    LOAN_CONTRACT("dkht", "中置辽农借款合同", null, FileType.LOAN_CONTRACT, "pdf"),
//    ENTRUSTED_GUARANTEE_CONTRACT("dbh", "中置辽农委托担保合同", null, FileType.ENTRUSTED_GUARANTEE_CONTRACT, "pdf"),
//    ENTRUSTED_DEDUCTION_LETTER("kksqs_zf", "中置辽农委托扣款授权书", null, FileType.ENTRUSTED_DEDUCTION_LETTER, "pdf"),
//    YTX_GUARANTEE_CONSULT_CONTRACT("dbzxfxy", "益通详-担保咨询服务合同", null, FileType.YTX_GUARANTEE_CONSULT_CONTRACT, "pdf");
//
//    private String ppdFileType;
//    private String fileName;
//
//    private PhotoType photoType;
//    private FileType fileType;
//    private String suffixName;
//
//    PpdContractType(String ppdFileType, String fileName, PhotoType photoType, FileType fileType, String suffixName) {
//        this.ppdFileType = ppdFileType;
//        this.fileName = fileName;
//        this.photoType = photoType;
//        this.fileType = fileType;
//        this.suffixName = suffixName;
//    }
//
//    public static PpdContractType getByFileType(FileType fileType) {
//        if (fileType == null) {
//            return null;
//        }
//        for (PpdContractType value : values()) {
//            if (value.fileType == fileType) {
//                return value;
//            }
//        }
//        return null;
//    }
//
//    public String getPpdFileType() {
//        return ppdFileType;
//    }
//
//    public String getFileName() {
//        return fileName;
//    }
//
//    public PhotoType getPhotoType() {
//        return photoType;
//    }
//
//    public FileType getFileType() {
//        return fileType;
//    }
//
//    public String getSuffixName() {
//        return suffixName;
//    }
}
