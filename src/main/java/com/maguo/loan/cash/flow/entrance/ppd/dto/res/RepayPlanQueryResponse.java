package com.maguo.loan.cash.flow.entrance.ppd.dto.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.maguo.loan.cash.flow.entrance.ppd.dto.CommonResult;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

public class RepayPlanQueryResponse extends CommonResult {
    /**
     * 实际生成还款计划的利率
     */
    private BigDecimal rate;

    /**
     * 客户还款计划列表
     */
    private List<RepayPlan> planList;

    // 还款计划实体类
    public static class RepayPlan {

        /**
         * 总期数
         */
        private Integer totalTerms;
        /**
         * 当前期数
         */
        private Integer term;
        /**
         * 应还款日期
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate dateDue;
        /**
         * 应还款总金额
         */
        private BigDecimal totalAmt = BigDecimal.ZERO;
        /**
         * 应还本金
         */
        private BigDecimal prinAmt = BigDecimal.ZERO;
        /**
         * 应还利息
         */
        private BigDecimal intAmt = BigDecimal.ZERO;
        /**
         * 应还罚息
         */
        private BigDecimal ointAmt = BigDecimal.ZERO;
        /**
         * 应还担保费
         */
        private BigDecimal zhibaoFee = BigDecimal.ZERO;
        /**
         * 应还咨询费
         */
        private BigDecimal poundageAmt = BigDecimal.ZERO;
        /**
         * 实还总金额
         */
        private BigDecimal actualTotalAmt = BigDecimal.ZERO;
        /**
         * 实还本金
         */
        private BigDecimal actualPrinAmt = BigDecimal.ZERO;
        /**
         * 实还利息
         */
        private BigDecimal actualIntAmt = BigDecimal.ZERO;
        /**
         * 实还罚息
         */
        private BigDecimal actualOintAmt = BigDecimal.ZERO;
        /**
         * 实还担保费
         */
        private BigDecimal actualZhibaoFee = BigDecimal.ZERO;
        /**
         * 实还咨询费
         */
        private BigDecimal actualPoundageAmt = BigDecimal.ZERO;

        private BigDecimal reductTotalAmt = BigDecimal.ZERO;
        private BigDecimal reductPrinAmt = BigDecimal.ZERO;
        private BigDecimal reductIntAmt = BigDecimal.ZERO;
        private BigDecimal reductOintAmt = BigDecimal.ZERO;
        private BigDecimal reductZhibaoFee = BigDecimal.ZERO;
        private BigDecimal reductPoundage = BigDecimal.ZERO;

        private String ointUpdatetime;

        public BigDecimal getReductPrinAmt() {
            return reductPrinAmt;
        }

        public void setReductPrinAmt(BigDecimal reductPrinAmt) {
            this.reductPrinAmt = reductPrinAmt;
        }

        public BigDecimal getReductTotalAmt() {
            return reductTotalAmt;
        }

        public void setReductTotalAmt(BigDecimal reductTotalAmt) {
            this.reductTotalAmt = reductTotalAmt;
        }

        public BigDecimal getReductIntAmt() {
            return reductIntAmt;
        }

        public void setReductIntAmt(BigDecimal reductIntAmt) {
            this.reductIntAmt = reductIntAmt;
        }

        public BigDecimal getReductOintAmt() {
            return reductOintAmt;
        }

        public void setReductOintAmt(BigDecimal reductOintAmt) {
            this.reductOintAmt = reductOintAmt;
        }

        public BigDecimal getReductZhibaoFee() {
            return reductZhibaoFee;
        }

        public void setReductZhibaoFee(BigDecimal reductZhibaoFee) {
            this.reductZhibaoFee = reductZhibaoFee;
        }

        public BigDecimal getReductPoundage() {
            return reductPoundage;
        }

        public void setReductPoundage(BigDecimal reductPoundage) {
            this.reductPoundage = reductPoundage;
        }

        public Integer getTotalTerms() {
            return totalTerms;
        }

        public void setTotalTerms(Integer totalTerms) {
            this.totalTerms = totalTerms;
        }

        public Integer getTerm() {
            return term;
        }

        public void setTerm(Integer term) {
            this.term = term;
        }

        public LocalDate getDateDue() {
            return dateDue;
        }

        public void setDateDue(LocalDate dateDue) {
            this.dateDue = dateDue;
        }

        public BigDecimal getTotalAmt() {
            return totalAmt;
        }

        public void setTotalAmt(BigDecimal totalAmt) {
            this.totalAmt = totalAmt;
        }

        public BigDecimal getPrinAmt() {
            return prinAmt;
        }

        public void setPrinAmt(BigDecimal prinAmt) {
            this.prinAmt = prinAmt;
        }

        public BigDecimal getIntAmt() {
            return intAmt;
        }

        public void setIntAmt(BigDecimal intAmt) {
            this.intAmt = intAmt;
        }

        public BigDecimal getOintAmt() {
            return ointAmt;
        }

        public void setOintAmt(BigDecimal ointAmt) {
            this.ointAmt = ointAmt;
        }

        public BigDecimal getZhibaoFee() {
            return zhibaoFee;
        }

        public void setZhibaoFee(BigDecimal zhibaoFee) {
            this.zhibaoFee = zhibaoFee;
        }

        public BigDecimal getPoundageAmt() {
            return poundageAmt;
        }

        public void setPoundageAmt(BigDecimal poundageAmt) {
            this.poundageAmt = poundageAmt;
        }

        public BigDecimal getActualTotalAmt() {
            return actualTotalAmt;
        }

        public void setActualTotalAmt(BigDecimal actualTotalAmt) {
            this.actualTotalAmt = actualTotalAmt;
        }

        public BigDecimal getActualPrinAmt() {
            return actualPrinAmt;
        }

        public void setActualPrinAmt(BigDecimal actualPrinAmt) {
            this.actualPrinAmt = actualPrinAmt;
        }

        public BigDecimal getActualIntAmt() {
            return actualIntAmt;
        }

        public void setActualIntAmt(BigDecimal actualIntAmt) {
            this.actualIntAmt = actualIntAmt;
        }

        public BigDecimal getActualOintAmt() {
            return actualOintAmt;
        }

        public void setActualOintAmt(BigDecimal actualOintAmt) {
            this.actualOintAmt = actualOintAmt;
        }

        public BigDecimal getActualZhibaoFee() {
            return actualZhibaoFee;
        }

        public void setActualZhibaoFee(BigDecimal actualZhibaoFee) {
            this.actualZhibaoFee = actualZhibaoFee;
        }

        public BigDecimal getActualPoundageAmt() {
            return actualPoundageAmt;
        }

        public void setActualPoundageAmt(BigDecimal actualPoundageAmt) {
            this.actualPoundageAmt = actualPoundageAmt;
        }

        public String getOintUpdatetime() {
            return ointUpdatetime;
        }

        public void setOintUpdatetime(String ointUpdatetime) {
            this.ointUpdatetime = ointUpdatetime;
        }
    }

    // Getter and Setter

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public List<RepayPlan> getPlanList() {
        return planList;
    }

    public void setPlanList(List<RepayPlan> planList) {
        this.planList = planList;
    }
}
