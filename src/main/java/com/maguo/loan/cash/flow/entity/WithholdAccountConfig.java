package com.maguo.loan.cash.flow.entity;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.WithholdMode;

import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * 扣款商户配置表
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "withhold_account_config")
public class WithholdAccountConfig extends BaseEntity {

    /**
     * 资方
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;
    /**
     * 流量方
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;
    /**
     * 融担公司
     */
    @Enumerated(EnumType.STRING)
    private GuaranteeCompany guaranteeCompany;

    /**
     * 扣款模式
     */
    @Enumerated(EnumType.STRING)
    private WithholdMode withholdMode;

    /**
     * 扣款主商户类型
     */
    private String mainMemberType;

    /**
     * 扣款主商户号
     */
    private String mainMemberId;

    /**
     * 本息分账商户号
     */
    private String principalShareMemberId;

    /**
     * 融担分账商户号
     */
    private String guaranteeShareMemberId;

    /**
     * 承担手续费商户号
     */
    private String feeMemberId;

    /**
     * 计费商户号
     */
    private String callFeeMemberId;

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public WithholdMode getWithholdMode() {
        return withholdMode;
    }

    public void setWithholdMode(WithholdMode withholdMode) {
        this.withholdMode = withholdMode;
    }

    public String getMainMemberType() {
        return mainMemberType;
    }

    public void setMainMemberType(String mainMemberType) {
        this.mainMemberType = mainMemberType;
    }

    public String getMainMemberId() {
        return mainMemberId;
    }

    public void setMainMemberId(String mainMemberId) {
        this.mainMemberId = mainMemberId;
    }

    public String getPrincipalShareMemberId() {
        return principalShareMemberId;
    }

    public void setPrincipalShareMemberId(String principalShareMemberId) {
        this.principalShareMemberId = principalShareMemberId;
    }

    public String getGuaranteeShareMemberId() {
        return guaranteeShareMemberId;
    }

    public void setGuaranteeShareMemberId(String guaranteeShareMemberId) {
        this.guaranteeShareMemberId = guaranteeShareMemberId;
    }

    public String getFeeMemberId() {
        return feeMemberId;
    }

    public void setFeeMemberId(String feeMemberId) {
        this.feeMemberId = feeMemberId;
    }

    public String getCallFeeMemberId() {
        return callFeeMemberId;
    }

    public void setCallFeeMemberId(String callFeeMemberId) {
        this.callFeeMemberId = callFeeMemberId;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }
}
