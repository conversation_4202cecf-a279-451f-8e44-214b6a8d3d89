package com.maguo.loan.cash.flow.entrance.ppd.controller;

import com.maguo.loan.cash.flow.entrance.ppd.common.ResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.common.ValidationException;
import com.maguo.loan.cash.flow.entrance.ppd.dto.ApiResult;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.CreditApplyRequest;
import com.maguo.loan.cash.flow.entrance.ppd.dto.req.CreditQueryRequest;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdBizException;
import com.maguo.loan.cash.flow.entrance.ppd.exception.PpdResultCode;
import com.maguo.loan.cash.flow.entrance.ppd.service.PpdService;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;


@RestController
@RequestMapping("/ppd/api")
public class PpdCreditController extends PpdApiValidator {

    @Autowired
    private PpdService ppdService;

    @PostMapping("/creditApply")
    public ApiResult creditApply(@RequestBody @Valid CreditApplyRequest request, BindingResult bindingResult) throws PpdBizException {

        // 必填参数校验
        validate(bindingResult);

        BigDecimal loanAmt = request.getLoanAmt();
        if(loanAmt == null){
            throw new ValidationException("放款金额不能为空。", ResultCode.PARAM_ILLEGAL);
        }

        boolean isInteger = loanAmt.scale() <= 0 || loanAmt.stripTrailingZeros().scale() <= 0;
        if(!isInteger){
            throw new ValidationException("放款金额必须是整数。", ResultCode.PARAM_ILLEGAL);
        }

        // 验证范围 [1000, 50000]
        boolean inRange = loanAmt.compareTo(BigDecimal.valueOf(1000)) >= 0 && loanAmt.compareTo(BigDecimal.valueOf(50000)) <= 0;
        if(!inRange){
            throw new ValidationException("放款金额必须大于等于1000且小于等于50000。", ResultCode.PARAM_ILLEGAL);
        }

        String idExpiryDate = request.getIdExpiryDate();
        LocalDate expiryDate = LocalDate.parse(idExpiryDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        if(LocalDate.now().isAfter(expiryDate)) {
            throw new ValidationException("身份证已过期。", ResultCode.PARAM_ILLEGAL);
        }
        // 验证范围 [1000, 50000]

        boolean isStandardAge =  request.getAge()>= 22 && request.getAge()<= 55;
        if(!isStandardAge){
            throw new ValidationException("申请年龄验证不通过,需要在22到55之间。", ResultCode.PARAM_ILLEGAL);
        }
        ApplyChannel applyChannel = ApplyChannel.getApplyChannel(request.getSourceCode());
        if (applyChannel == null) {
            throw new ValidationException("未知渠道号。", ResultCode.UNKNOWN_CHANNEL);
        }
        // 业务逻辑
        return ppdService.approval(request);
    }

    @PostMapping("/creditQuery")
    public ApiResult creditQuery(@RequestBody @Valid CreditQueryRequest request, BindingResult bindingResult) throws PpdBizException {

        // 必填参数校验
        validate(bindingResult);
        //验证
        ApplyChannel applyChannel = ApplyChannel.getApplyChannel(request.getSourceCode());
        if (applyChannel == null) {
            throw new ValidationException("未知渠道号。", ResultCode.UNKNOWN_CHANNEL);
        }
        // 业务逻辑
        return ppdService.creditResultQuery(request);
    }

}
