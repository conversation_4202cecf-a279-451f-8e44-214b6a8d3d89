package com.maguo.loan.cash.flow.service.listener;

import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entrance.lvxin.service.LvxinService;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;


@Component
public class LvxinImgsDownloadListner  extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(LvxinImgsDownloadListner.class);

    @Autowired
    LvxinService lvxinService;
    public LvxinImgsDownloadListner(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }
    @RabbitListener(queues =  RabbitConfig.Queues.CREDIT_IMGS_FILE_DOWNLOAD)
    public void LvxinImgsDownloadListner(Message message, Channel channel) {
        String applyRecordId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听流量影像件异步下载:{}", applyRecordId);
            lvxinService.imgsDownloadListner(applyRecordId);
        } catch (Exception e) {
            processException(applyRecordId, message, e, "流量影像件异步下载异常", getMqService()::submitImgFileDownloadDelay);
        } finally {
            ackMsg(applyRecordId, message, channel);
        }
    }
}
