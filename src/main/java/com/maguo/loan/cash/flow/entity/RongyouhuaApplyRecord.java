package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "rongyouhua_apply_record")
public class RongyouhuaApplyRecord extends BaseEntity {

    /**
     * 订单编号
     */
    private String orderNo;

    private String mobile;

    /**
     * 姓名
     */
    private String name;
    private String currentAddress;
    private Integer marriageType;
    private Integer educationType;
    private Integer houseType;
    private String inCome;
    private Integer loanUsageType;
    private Integer professionType;
    private Integer industryType;
    private String score;
    private String idNum;
    private String sex;
    private String nation;
    private String identityAddress;
    private String issueAgency;
    private String periodValidityDate;
    private Integer contactOneType;
    private String contactOneName;
    private String contactOneMobile;
    private Integer contactTwoType;
    private String contactTwoName;
    private String contactTwoMobile;
    private String companyName;
    private String companyTel;
    private String companyAddress;
    private String companyCity;
    private String osType;
    private String deviceBrand;
    private String deviceModel;
    private String ip;
    private String applyY;
    private String applyX;
    private String applypos;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCurrentAddress() {
        return currentAddress;
    }

    public void setCurrentAddress(String currentAddress) {
        this.currentAddress = currentAddress;
    }

    public Integer getMarriageType() {
        return marriageType;
    }

    public void setMarriageType(Integer marriageType) {
        this.marriageType = marriageType;
    }

    public Integer getEducationType() {
        return educationType;
    }

    public void setEducationType(Integer educationType) {
        this.educationType = educationType;
    }

    public Integer getHouseType() {
        return houseType;
    }

    public void setHouseType(Integer houseType) {
        this.houseType = houseType;
    }

    public String getInCome() {
        return inCome;
    }

    public void setInCome(String inCome) {
        this.inCome = inCome;
    }

    public Integer getLoanUsageType() {
        return loanUsageType;
    }

    public void setLoanUsageType(Integer loanUsageType) {
        this.loanUsageType = loanUsageType;
    }

    public Integer getProfessionType() {
        return professionType;
    }

    public void setProfessionType(Integer professionType) {
        this.professionType = professionType;
    }

    public Integer getIndustryType() {
        return industryType;
    }

    public void setIndustryType(Integer industryType) {
        this.industryType = industryType;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getIdNum() {
        return idNum;
    }

    public void setIdNum(String idNum) {
        this.idNum = idNum;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getIdentityAddress() {
        return identityAddress;
    }

    public void setIdentityAddress(String identityAddress) {
        this.identityAddress = identityAddress;
    }

    public String getIssueAgency() {
        return issueAgency;
    }

    public void setIssueAgency(String issueAgency) {
        this.issueAgency = issueAgency;
    }

    public String getPeriodValidityDate() {
        return periodValidityDate;
    }

    public void setPeriodValidityDate(String periodValidityDate) {
        this.periodValidityDate = periodValidityDate;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyTel() {
        return companyTel;
    }

    public void setCompanyTel(String companyTel) {
        this.companyTel = companyTel;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyCity() {
        return companyCity;
    }

    public void setCompanyCity(String companyCity) {
        this.companyCity = companyCity;
    }

    public String getOsType() {
        return osType;
    }

    public void setOsType(String osType) {
        this.osType = osType;
    }

    public String getDeviceBrand() {
        return deviceBrand;
    }

    public void setDeviceBrand(String deviceBrand) {
        this.deviceBrand = deviceBrand;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getApplyY() {
        return applyY;
    }

    public void setApplyY(String applyY) {
        this.applyY = applyY;
    }

    public String getApplyX() {
        return applyX;
    }

    public void setApplyX(String applyX) {
        this.applyX = applyX;
    }

    public String getApplypos() {
        return applypos;
    }

    public void setApplypos(String applypos) {
        this.applypos = applypos;
    }

    public Integer getContactOneType() {
        return contactOneType;
    }

    public void setContactOneType(Integer contactOneType) {
        this.contactOneType = contactOneType;
    }

    public String getContactOneName() {
        return contactOneName;
    }

    public void setContactOneName(String contactOneName) {
        this.contactOneName = contactOneName;
    }

    public String getContactOneMobile() {
        return contactOneMobile;
    }

    public void setContactOneMobile(String contactOneMobile) {
        this.contactOneMobile = contactOneMobile;
    }

    public Integer getContactTwoType() {
        return contactTwoType;
    }

    public void setContactTwoType(Integer contactTwoType) {
        this.contactTwoType = contactTwoType;
    }

    public String getContactTwoName() {
        return contactTwoName;
    }

    public void setContactTwoName(String contactTwoName) {
        this.contactTwoName = contactTwoName;
    }

    public String getContactTwoMobile() {
        return contactTwoMobile;
    }

    public void setContactTwoMobile(String contactTwoMobile) {
        this.contactTwoMobile = contactTwoMobile;
    }
}
