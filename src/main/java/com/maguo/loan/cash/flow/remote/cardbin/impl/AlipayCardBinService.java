package com.maguo.loan.cash.flow.remote.cardbin.impl;


import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.BankList;
import com.maguo.loan.cash.flow.remote.cardbin.CardBin;
import com.maguo.loan.cash.flow.remote.cardbin.CardBinService;
import com.maguo.loan.cash.flow.repository.BankListRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class AlipayCardBinService implements CardBinService {
    private static final Logger log = LoggerFactory.getLogger(AlipayCardBinService.class);

    @Autowired
    private BankListRepository bankListRepository;

    @Value("${alipay.cardbin.service.url}")
    private String url;

    @Override
    public CardBin query(String cardNo) {
        String requestUrl = url + cardNo;
        try {
            String resp = HttpUtil.get(requestUrl);
            log.info("alipay cardBin 接口,url:{}, response:{}", requestUrl, resp);

            var result = JsonUtil.convertToObject(resp, AlipayCardBinResult.class);

            return getCardBinResult(result);
        } catch (Exception e) {
            log.error("alipay cardBin接口接口异常, url: {}", requestUrl, e);
        }
        return null;
    }


    private CardBin getCardBinResult(AlipayCardBinResult result) {
        if (result == null || !result.isValidated()) {
            return null;
        }
        CardBin cardBin = new CardBin();
        cardBin.setBankAbbr(result.getBank());
        cardBin.setCredit(result.getCardType().equals("CC"));
        BankList bank = findBank(cardBin.getBankAbbr());
        if (bank != null) {
            cardBin.setName(bank.getName());
            cardBin.setShortName(bank.getShortName());
            cardBin.setBankAbbr(bank.getAbbr());
            cardBin.setIconUrl(bank.getIconUrl());
        }
        return cardBin;
    }

    private BankList findBank(String code) {
        return bankListRepository.findByAbbrOrOldAbbr(code).orElse(null);
    }


    static class AlipayCardBinResult {
        private boolean validated;
        private String cardType;
        private String bank;
        private String key;
        private String stat;

        public boolean isValidated() {
            return validated;
        }

        public void setValidated(boolean validated) {
            this.validated = validated;
        }

        public String getCardType() {
            return cardType;
        }

        public void setCardType(String cardType) {
            this.cardType = cardType;
        }

        public String getBank() {
            return bank;
        }

        public void setBank(String bank) {
            this.bank = bank;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getStat() {
            return stat;
        }

        public void setStat(String stat) {
            this.stat = stat;
        }
    }

}
