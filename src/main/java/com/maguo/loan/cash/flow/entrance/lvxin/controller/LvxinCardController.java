package com.maguo.loan.cash.flow.entrance.lvxin.controller;


import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.BindCardApplyRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.BindCardApplyResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.BindCardConfirmRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.BindCardConfirmResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.QueryBankListRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.card.QueryBankListResponse;
import com.maguo.loan.cash.flow.entrance.lvxin.service.LvxinService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("lvxin")
public class LvxinCardController {

    @Autowired
    private LvxinService lvxinService;

    /**
     * 获取协议
     */
    @PostMapping("/api/partner/v1/queryBankList")
    public LvxinResponse bankList(@RequestBody QueryBankListRequest request) {
        QueryBankListResponse response = lvxinService.supportedBankList(request);
        return LvxinResponse.success(response);
    }

    /**
     * 绑卡申请
     */
    @PostMapping("/api/partner/v1/bindCard")
    public LvxinResponse bindApply(@RequestBody BindCardApplyRequest request) {
        BindCardApplyResponse response = lvxinService.bindCardApply(request);
        return LvxinResponse.success(response);
    }

    /**
     * 绑卡确认
     */
    @PostMapping("/api/partner/v1/bindCardVerifyCode")
    public LvxinResponse bindConfirm(@RequestBody BindCardConfirmRequest request) {
        BindCardConfirmResponse response = lvxinService.bindConfirm(request);
        return LvxinResponse.success(response);
    }
}
