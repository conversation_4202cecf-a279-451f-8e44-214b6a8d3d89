package com.maguo.loan.cash.flow.entrance.common.enums.cache;

/**
 * commonApi redis key模版
 *
 * <AUTHOR>
 * @date 2024/8/23
 */
public enum CacheKey {
    BIND_CARD_APPLY("commonApi:bindCardApply:{0}_{1}"),
    BIND_CARD_CONFIRM("commonApi:bindCardConfirm:{0}_{1}"),
    APPROVAL("commonApi:approval:{0}"),
    SUBMIT_LOAN("commonApi:submitLoan:{0}"),
    REPAY("commonApi:repay:{0}");


    private String template;

    CacheKey(String template) {
        this.template = template;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }
}
