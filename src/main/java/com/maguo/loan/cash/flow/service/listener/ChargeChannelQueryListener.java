package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.pay.ThirdPayService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @since 2025-01-06
 */
@Component
public class ChargeChannelQueryListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(ChargeChannelQueryListener.class);

    public ChargeChannelQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @Autowired
    private ThirdPayService thirdPayService;

    @RabbitListener(queues = RabbitConfig.Queues.CHARGE_CHANNEL_QUERY)
    public void listenChargeResult(Message message, Channel channel) {
        String chargeId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听扣款结果:{}", chargeId);
            // service
            thirdPayService.query(chargeId);
        } catch (Exception e) {
            processException(chargeId, message, e, "查询扣款结果异常", getMqService()::submitChargeQueryDelay);
        } finally {
            ackMsg(chargeId, message, channel);
        }
    }
}
