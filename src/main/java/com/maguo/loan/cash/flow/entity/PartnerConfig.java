package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * 公共对接流量配置
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "partner_config")
public class PartnerConfig extends BaseEntity {


    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    /**
     * 域名
     */
    private String partnerDomainName;

    /**
     * 公司代码
     */
    private String partnerOrgCode;

    /**
     * 产品代码
     */
    private String partnerProductCode;

    /**
     * 合作机构名称
     */
    private String partnerName;

    /**
     * 合作机构公钥
     */
    private String partnerPublicKey;

    /**
     * 合作机构私钥
     */
    private String partnerPrivateKey;

    /**
     * 我方公钥
     */
    private String serverPublicKey;

    /**
     * 我方私钥
     */
    private String serverPrivateKey;
    /**
     * 进件(风控审批结果推送)
     */
    @Enumerated(EnumType.STRING)
    private WhetherState approvalPush;

    /**
     * 订单状态推送
     */
    @Enumerated(EnumType.STRING)
    private WhetherState orderStatusPush;

    /**
     * 还款结果推送
     */
    @Enumerated(EnumType.STRING)
    private WhetherState repaidPush;

    /**
     * 还款结果推送
     */
    @Enumerated(EnumType.STRING)
    private WhetherState repayPush;

    /**
     * 还款计划推送
     */
    @Enumerated(EnumType.STRING)
    private WhetherState repayPlanPush;

    /**
     * 权益状态推送
     */
    @Enumerated(EnumType.STRING)
    private WhetherState rightsStatusPush;

    @Enumerated(EnumType.STRING)
    private AbleStatus enable;

    public AbleStatus getEnable() {
        return enable;
    }

    public void setEnable(AbleStatus enable) {
        this.enable = enable;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getPartnerDomainName() {
        return partnerDomainName;
    }

    public void setPartnerDomainName(String partnerDomainName) {
        this.partnerDomainName = partnerDomainName;
    }

    public String getPartnerOrgCode() {
        return partnerOrgCode;
    }

    public void setPartnerOrgCode(String partnerOrgCode) {
        this.partnerOrgCode = partnerOrgCode;
    }

    public String getPartnerProductCode() {
        return partnerProductCode;
    }

    public void setPartnerProductCode(String partnerProductCode) {
        this.partnerProductCode = partnerProductCode;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getPartnerPublicKey() {
        return partnerPublicKey;
    }

    public void setPartnerPublicKey(String partnerPublicKey) {
        this.partnerPublicKey = partnerPublicKey;
    }

    public String getPartnerPrivateKey() {
        return partnerPrivateKey;
    }

    public void setPartnerPrivateKey(String partnerPrivateKey) {
        this.partnerPrivateKey = partnerPrivateKey;
    }

    public String getServerPublicKey() {
        return serverPublicKey;
    }

    public void setServerPublicKey(String serverPublicKey) {
        this.serverPublicKey = serverPublicKey;
    }

    public String getServerPrivateKey() {
        return serverPrivateKey;
    }

    public void setServerPrivateKey(String serverPrivateKey) {
        this.serverPrivateKey = serverPrivateKey;
    }

    public WhetherState getApprovalPush() {
        return approvalPush;
    }

    public void setApprovalPush(WhetherState approvalPush) {
        this.approvalPush = approvalPush;
    }

    public WhetherState getOrderStatusPush() {
        return orderStatusPush;
    }

    public void setOrderStatusPush(WhetherState orderStatusPush) {
        this.orderStatusPush = orderStatusPush;
    }

    public WhetherState getRepaidPush() {
        return repaidPush;
    }

    public void setRepaidPush(WhetherState repaidPush) {
        this.repaidPush = repaidPush;
    }

    public WhetherState getRepayPlanPush() {
        return repayPlanPush;
    }

    public void setRepayPlanPush(WhetherState repayPlanPush) {
        this.repayPlanPush = repayPlanPush;
    }

    public WhetherState getRightsStatusPush() {
        return rightsStatusPush;
    }

    public void setRightsStatusPush(WhetherState rightsStatusPush) {
        this.rightsStatusPush = rightsStatusPush;
    }

    public WhetherState getRepayPush() {
        return repayPush;
    }

    public void setRepayPush(WhetherState repayPush) {
        this.repayPush = repayPush;
    }

    @Override
    protected String prefix() {
        return "PC";
    }
}
