package com.maguo.loan.cash.flow.vo;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
public abstract class BaseRequest {

    @NotBlank(message = "订单编号不能为空")
    private String orderId;


    /**
     * redis中对应userId,
     * 失效，有效
     * 越权
     */
    private String token;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

}
