package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.ThirdUserInfo;
import com.maguo.loan.cash.flow.enums.ThirdChannel;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface ThirdUserInfoRepository extends JpaRepository<ThirdUserInfo, String> {

    ThirdUserInfo findByThirdUserIdAndMobileAndThirdChannel(String thirdUserId, String mobile, ThirdChannel thirdChannel);

    Optional<ThirdUserInfo> findByMobileAndThirdChannel(String mobile, ThirdChannel thirdChannel);

    Optional<ThirdUserInfo> findByUserIdAndMobileAndThirdChannel(String userId, String mobile, ThirdChannel thirdChannel);
}
