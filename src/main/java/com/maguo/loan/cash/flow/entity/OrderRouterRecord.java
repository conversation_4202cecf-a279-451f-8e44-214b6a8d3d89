package com.maguo.loan.cash.flow.entity;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RouteState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/12/20
 */
@Entity
@Table(name = "order_router_record")
public class OrderRouterRecord extends BaseEntity {
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 用户
     */
    private String userId;
    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;
    /**
     * 路由配置
     */
    private String routeConfigId;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 路由资方
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;
    /**
     * 路由状态
     */
    @Enumerated(EnumType.STRING)
    private RouteState routeState;
    /**
     * 路由失败原因
     */
    private String routeFailReason;
    /**
     * 授信状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState creditState;
    /**
     * 授信失败原因
     */
    private String creditFailReason;

    private String projectCode;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getRouteConfigId() {
        return routeConfigId;
    }

    public void setRouteConfigId(String routeConfigId) {
        this.routeConfigId = routeConfigId;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public RouteState getRouteState() {
        return routeState;
    }

    public void setRouteState(RouteState routeState) {
        this.routeState = routeState;
    }

    public String getRouteFailReason() {
        return routeFailReason;
    }

    public void setRouteFailReason(String routeFailReason) {
        this.routeFailReason = routeFailReason;
    }

    public ProcessState getCreditState() {
        return creditState;
    }

    public void setCreditState(ProcessState creditState) {
        this.creditState = creditState;
    }

    public String getCreditFailReason() {
        return creditFailReason;
    }

    public void setCreditFailReason(String creditFailReason) {
        this.creditFailReason = creditFailReason;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    @Override
    protected String prefix() {
        return "OR";
    }
}
