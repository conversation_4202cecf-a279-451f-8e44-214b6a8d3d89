package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.BankRepayRecord;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface BankRepayRecordRepository extends JpaRepository<BankRepayRecord, String> {
    boolean existsByLoanIdAndPeriodAndStateIn(String loanId, Integer period, Collection<ProcessState> states);

    List<BankRepayRecord> findAllByLoanIdAndPeriod(String loanId, Integer period);

    int countByStateAndRepayTimeBetween(ProcessState repayState, LocalDateTime beginTime, LocalDateTime endTime);

    List<BankRepayRecord> findByLoanId(String loanId);

    List<BankRepayRecord> findByCreatedTimeBetweenAndStateIn(LocalDateTime createdTimeStart, LocalDateTime createdTimeEnd, Collection<ProcessState> states);

    BankRepayRecord findBySourceRecordId(String sourceRecordId);

    BankRepayRecord findTopBySourceRecordIdOrderByCreatedTimeDesc(String sourceRecordId);

    BankRepayRecord findByLoanIdAndPeriodAndStateIn(String loanId, Integer period, ProcessState... state);


    @Query(value = "select "
        + "o.id as loanNo,"
        + "zcar.platform_apply_code as cooppfApplCde,"
        + "'' as setlSeq,"
        + "if(brr.created_time is null,'',date_format(brr.created_time,'%Y-%m-%d %H:%i:%s')) as setlCreateDt,"
        + "brr.amount as setlRecvAmt,"
        + "brr.amount as setlAmt,"
        + "ubc.card_no paymAcctNo,"
        + "'02' as hkTyp,"
        + "'01' as hkChannel,"
        + "if(brr.state='SUCCEED','01','02') as hkSts,"
        + "brr.principal as setlPrcp,"
        + "brr.interest as setlNormInt,"
        + "'0' as setlPenalFeeAmt,"
        + "brr.amount as setlOdIntAmt,"
        + "'0' as setlCommOdInt,"
        + "'0' as setlBrokerageAmt,"
        + "if(o.order_state='CLEAR','SETL','NORM')loanSts,"
        + "'' as tradeCode,"
        + "brr.amount as setlInstmAmt,"
        + "brr.principal as setlPrcpAmt,"
        + "brr.interest as setlNormIntAmt,"
        + "'0' as setlOdAmt,"
        + "brr.amount as setlFeeAmt,"
        + "brr.bank_repay_no as accountSeq,"
        + "if(brr.created_time is null,'',date_format(brr.created_time,'%Y-%m-%d %H:%i:%s')) as setlCreateTime,"
        + "brr.period as psPerdNo,"
        + "if(brr.repay_purpose ='CURRENT','07','02')settlTyp,"
        + "if(rp.plan_repay_date  is null,'',date_format(rp.plan_repay_date ,'%Y-%m-%d')) as psDueDt,"
        + "if(brr.repay_purpose = 'CLEAR', 'Y', 'N') as settleEarlyFlag,"
        + "brr.bank_repay_no as payNo,"
        + "zcar.apply_seq as ysxApplSeq,"
        + "brr.principal as cpSetlPrcp,"
        + "brr.interest as cpSetlNormInt,"
        + "'0' as cpSetlPenalFeeAmt "
        + "from loan l join `order` o on o.id=l.order_id "
        + "join bank_repay_record brr on l.id = brr.loan_id "
        + "join user_bank_card ubc on l.loan_card_id  = ubc.id "
        + "join repay_plan rp on rp.loan_id =brr.loan_id and rp.period =brr.period "
        + "join zycfc_credit_apply_record zcar on o.risk_id = zcar.risk_id "
        + "where o.call_zy_risk=?1 and l.flow_channel ='ZYCFC'"
        + "and brr.created_time >=?2 and brr.created_time <?3 and brr.state = 'SUCCEED'", nativeQuery = true)
    List<Map<String, String>> findUploadRepayFile(WhetherState whetherState, LocalDateTime localDateTime, LocalDateTime localDateTime1);
}

