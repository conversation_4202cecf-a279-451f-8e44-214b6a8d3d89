package com.maguo.loan.cash.flow.entrance.common.enums.api;

/**
 * 公共流程回调地址
 *
 * <AUTHOR>
 * @date 2024/8/29
 */
public enum CallBackApi {

    APPROVAL_PUSH("/notice/approvalPush", "进件(风控审批结果推送)"),
    ORDER_STATUS_PUSH("/notice/orderStatusPush", "订单状态推送"),
    REPAID_PUSH("/notice/repaidPush", "还款结果推送"),
    REPAY_PUSH("/notice/repayPush", "还款结果推送v2"),
    REPAY_PLAN_PUSH("/notice/repayPlanPush", "还款计划推送"),
    RIGHTS_STATUS_PUSH("/notice/rightsStatusPush", "权益推送");
    /**
     * 访问路径
     */
    private final String uri;

    /**
     * 接口名称
     */
    private final String description;

    CallBackApi(String uri, String description) {
        this.uri = uri;
        this.description = description;
    }

    public String getUri() {
        return uri;
    }

    public String getDescription() {
        return description;
    }
}
