package com.maguo.loan.cash.flow.entrance.common.dto.request;

import jakarta.validation.constraints.NotBlank;

import java.io.Serial;
import java.io.Serializable;

/**
 * 获取h5要款链接
 *
 * <AUTHOR>
 * @date 2024/7/11
 */
public class GenerationLoanUrlReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8685742004855311367L;
    /**
     * 返回跳转链接
     */
    private String redirectUrl;
    /**
     * 合作机构单号
     */
    @NotBlank(message = "合作机构单号不能为空")
    private String partnerOrderNo;

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }
}
