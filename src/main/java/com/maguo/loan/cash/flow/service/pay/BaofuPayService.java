package com.maguo.loan.cash.flow.service.pay;

import com.alibaba.fastjson2.JSON;
import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.config.BaoFuPaymentConfig;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.WithholdAccountConfig;
import com.maguo.loan.cash.flow.entity.WithholdFlow;
import com.maguo.loan.cash.flow.entity.WithholdShareInfo;
import com.maguo.loan.cash.flow.enums.ChargeBizType;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.zsjz.third.part.PayType;
import com.zsjz.third.part.Status;
import com.zsjz.third.part.baofoo.BaoFuChargeQueryService;
import com.zsjz.third.part.baofoo.BaoFuChargeService;
import com.zsjz.third.part.baofoo.vo.BaoFuChargeQueryRequest;
import com.zsjz.third.part.baofoo.vo.BaoFuChargeQueryResponse;
import com.zsjz.third.part.baofoo.vo.BaoFuChargeRequest;
import com.zsjz.third.part.baofoo.vo.BaoFuChargeResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2023/8/19
 */
@Service
public class BaofuPayService extends AbstractPayService {
    private static final Logger logger = LoggerFactory.getLogger(BaofuPayService.class);

    @Autowired
    private BaoFuChargeService chargeService;
    @Autowired
    private BaoFuChargeQueryService chargeQueryService;

    @Autowired
    BaoFuPaymentConfig config;
    @Override
    public WithholdFlow payApply(WithholdFlow withholdFlow) {
        String chargeId = withholdFlow.getId();
        logger.info("发起扣款请求chargeId={}", chargeId);

        if (withholdFlow.getPayState().isFinal()) {
            warningService.warn("扣款结果已是终态:" + chargeId, msg -> logger.error("扣款结果已是终态:{}", chargeId));
            return withholdFlow;
        }

        try {
            Loan loan = loanRepository.findById(withholdFlow.getLoanId()).orElseThrow(() -> new BizException(ResultCode.LOAN_NOT_EXIST));
            //扣款商户号配置
            WithholdAccountConfig accountConfig = withholdAccountConfigRepository.findByBankChannelAndFlowChannelAndGuaranteeCompany(loan.getBankChannel(),loan.getFlowChannel(),
                    loan.getGuaranteeCompany());
            ChargeBizType bizType = withholdFlow.getBizType();
            String orderPreNo = generateOrderPreNo(bizType, loan);
            BaoFuChargeRequest request = new BaoFuChargeRequest();
            if (WhetherState.Y.equals(withholdFlow.getShareInfo())) {
                request.setPayType(PayType.SPLIT_PARENT);
            }
            request.setOrderId(chargeId);
            request.setAmount(withholdFlow.getPayAmount());
            //request.setUserId(loan.getUserId());
            request.setProtocolNo(withholdFlow.getAgreementNo());
            request.setFeeMerchantId(accountConfig.getFeeMemberId());
            request.setTerminalId(withholdFlow.getChannelMchId());
            request.setMerchantId(accountConfig.getMainMemberId());
            request.setCallFeeMemberId(accountConfig.getCallFeeMemberId());
            List<WithholdShareInfo> childOrderMerchant = withholdShareInfoRepository.findAllByWithholdIdAndMerchantConfirm(chargeId, WhetherState.Y);
            childOrderMerchant.forEach(item -> {
                request.addChildOrderMerchant(item.getMerchantNo());
            });
            List<WithholdShareInfo> splitInfo = withholdShareInfoRepository.findAllByWithholdId(chargeId);
            splitInfo.forEach(item -> {
                request.addSplitInfo(item.getMerchantNo(), item.getAmount());
            });
            logger.info("请求宝付扣款={}", JSON.toJSONString(request));
            BaoFuChargeResponse call = chargeService.call(request);
            // 支付订单号
            if (Objects.nonNull(call)) {
                withholdFlow.setPayOrderNo(call.getPayOrderId());
                withholdFlow.setMotherDeductionNumber(orderPreNo+chargeId);
            }
        } catch (Exception e) {
            logger.error("请求公共扣款异常:", e);
        } finally {
            // 请求公共后, withholdFlow置为处理中
            withholdFlow.setPayState(ProcessState.PROCESSING);
            withholdFlow = withholdFlowRepository.save(withholdFlow);
        }

        // 查询扣款队列
        mqService.submitChargeQueryDelay(chargeId);

        return withholdFlow;
    }


    @Override
    public void payResult(WithholdFlow withholdFlow) {
        String chargeId = withholdFlow.getId();
        logger.info("扣款结果查询,chargeId:{}", chargeId);

        if (withholdFlow.getPayState().isFinal()) {
            warningService.warn("扣款结果已是终态:" + chargeId, msg -> logger.error("扣款结果已是终态:{}", chargeId));
            return;
        }
        BaoFuChargeQueryRequest request = new BaoFuChargeQueryRequest();
        request.setOrderId(chargeId);
        request.setOrderTime(LocalDateTime.now());
        request.setTerminalId(withholdFlow.getChannelMchId());
        request.setMerchantId(config.getMerchantId());
        try {
            BaoFuChargeQueryResponse call = chargeQueryService.call(request);

            // 支付结果非终态
            if (call.getOrderStatus().equals(Status.PROCESSING)) {
                mqService.submitChargeQueryDelay(chargeId);
                return;
            }

            // 处理支付结果
            handlePayResult(withholdFlow, call);

        } catch (Exception e) {
            logger.error("查询公共系统扣款结果异常", e);

            mqService.submitChargeQueryDelay(chargeId);
        }
    }

}
