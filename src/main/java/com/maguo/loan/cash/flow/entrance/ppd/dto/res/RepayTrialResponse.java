package com.maguo.loan.cash.flow.entrance.ppd.dto.res;

import com.maguo.loan.cash.flow.entrance.ppd.dto.CommonResult;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class RepayTrialResponse extends CommonResult {
    /**
     * 还款总金额
     */
    private BigDecimal repayAmount = BigDecimal.ZERO;
    /**
     * 还款本金
     */
    private BigDecimal repayPrincipal = BigDecimal.ZERO;
    /**
     * 还款利息
     */
    private BigDecimal repayInterest = BigDecimal.ZERO;
    /**
     * 还款罚息
     */
    private BigDecimal repayOverdue = BigDecimal.ZERO;
    /**
     * 还款担保费
     */
    private BigDecimal repayZhibaoFee = BigDecimal.ZERO;
    /**
     * 还款咨询费
     */
    private BigDecimal repayPoundage = BigDecimal.ZERO;

    public BigDecimal getRepayAmount() {
        return repayAmount;
    }

    public void setRepayAmount(BigDecimal repayAmount) {
        this.repayAmount = repayAmount;
    }

    public BigDecimal getRepayPrincipal() {
        return repayPrincipal;
    }

    public void setRepayPrincipal(BigDecimal repayPrincipal) {
        this.repayPrincipal = repayPrincipal;
    }

    public BigDecimal getRepayInterest() {
        return repayInterest;
    }

    public void setRepayInterest(BigDecimal repayInterest) {
        this.repayInterest = repayInterest;
    }

    public BigDecimal getRepayOverdue() {
        return repayOverdue;
    }

    public void setRepayOverdue(BigDecimal repayOverdue) {
        this.repayOverdue = repayOverdue;
    }

    public BigDecimal getRepayZhibaoFee() {
        return repayZhibaoFee;
    }

    public void setRepayZhibaoFee(BigDecimal repayZhibaoFee) {
        this.repayZhibaoFee = repayZhibaoFee;
    }

    public BigDecimal getRepayPoundage() {
        return repayPoundage;
    }

    public void setRepayPoundage(BigDecimal repayPoundage) {
        this.repayPoundage = repayPoundage;
    }
}
