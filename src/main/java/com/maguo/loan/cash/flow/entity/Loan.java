package com.maguo.loan.cash.flow.entity;

import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.maguo.loan.cash.flow.enums.ApplyChannel;
import com.maguo.loan.cash.flow.enums.BusinessType;
import com.maguo.loan.cash.flow.enums.EquityRecipient;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.LoanPurpose;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RightsLevel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "loan")
public class Loan extends BaseEntity {

    /**
     * 渠道标签
     */
    private String applyChannel;

    private String userId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 授信id
     */
    private String creditId;

    /**
     * 放款记录id
     */
    private String loanRecordId;

    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;
    /**
     * 外部放款单号
     */
    private String outerLoanId;
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 期数
     */
    private Integer periods;
    /**
     * 用途
     */
    @Enumerated(EnumType.STRING)
    private LoanPurpose loanPurpose;
    /**
     * 放款卡id
     */
    private String loanCardId;
    /**
     * 还款卡id
     */
    private String repayCardId;
    /**
     * 权益包id
     */
    private String packageId;
    /**
     * 放款状态
     */
    @Enumerated(EnumType.STRING)
    private ProcessState loanState;
    /**
     * 资金渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;
    /**
     * 融担公司
     */
    @Enumerated(EnumType.STRING)
    private GuaranteeCompany guaranteeCompany;
    /**
     * 资金编号
     */
    private String loanNo;

    /**
     * 对客利率（金融）
     */
    private BigDecimal irrRate;
    /**
     * 资方利率
     */
    private BigDecimal bankRate;
    /**
     * 放款成功时间
     */
    private LocalDateTime loanTime;
    /**
     * 放款后首次同步core还款计划
     */
    @Enumerated(EnumType.STRING)
    private WhetherState planSyncCore;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 借款合同编号
     */
    private String loanContractNo;

    /**
     * 审批权益等级
     */
    @Enumerated(EnumType.STRING)
    private RightsLevel approveRights;

    @Enumerated(EnumType.STRING)
    private WhetherState rightsDeductState;

    /**
     * 权益金额
     */
    private BigDecimal rightsAmount;

    /**
     * 是否复贷
     */
    @Enumerated(EnumType.STRING)
    private WhetherState reloan;

    /**
     * 业务类型
     */
    @Enumerated(EnumType.STRING)
    private BusinessType businessType;

    /**
     * 是否含权益
     * Y:含权益：N:不含权益
     */
    @Enumerated(EnumType.STRING)
    private IsIncludingEquity isIncludingEquity;

    /**
     * 权益收取方
     * O：外部,I：内部（默认O：外部）
     */
    @Enumerated(EnumType.STRING)
    private EquityRecipient equityRecipient;
    public String getApplyChannel() {
        return applyChannel;
    }

    public void setApplyChannel(String applyChannel) {
        this.applyChannel = applyChannel;
    }

    public BigDecimal getRightsAmount() {
        return rightsAmount;
    }

    public void setRightsAmount(BigDecimal rightsAmount) {
        this.rightsAmount = rightsAmount;
    }

    public WhetherState getRightsDeductState() {
        return rightsDeductState;
    }

    public void setRightsDeductState(WhetherState rightsDeductState) {
        this.rightsDeductState = rightsDeductState;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }


    public String getOuterLoanId() {
        return outerLoanId;
    }

    public void setOuterLoanId(String outerLoanId) {
        this.outerLoanId = outerLoanId;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }


    public String getLoanCardId() {
        return loanCardId;
    }

    public void setLoanCardId(String loanCardId) {
        this.loanCardId = loanCardId;
    }

    public String getRepayCardId() {
        return repayCardId;
    }

    public void setRepayCardId(String repayCardId) {
        this.repayCardId = repayCardId;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }


    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public LocalDateTime getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(LocalDateTime loanTime) {
        this.loanTime = loanTime;
    }


    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getLoanContractNo() {
        return loanContractNo;
    }

    public void setLoanContractNo(String loanContractNo) {
        this.loanContractNo = loanContractNo;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(LoanPurpose loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public ProcessState getLoanState() {
        return loanState;
    }

    public void setLoanState(ProcessState loanState) {
        this.loanState = loanState;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }

    public WhetherState getPlanSyncCore() {
        return planSyncCore;
    }

    public void setPlanSyncCore(WhetherState planSyncCore) {
        this.planSyncCore = planSyncCore;
    }

    public BigDecimal getIrrRate() {
        return irrRate;
    }

    public void setIrrRate(BigDecimal irrRate) {
        this.irrRate = irrRate;
    }

    public BigDecimal getBankRate() {
        return bankRate;
    }

    public void setBankRate(BigDecimal bankRate) {
        this.bankRate = bankRate;
    }

    @Override
    protected String prefix() {
        return "LO";
    }

    public RightsLevel getApproveRights() {
        return approveRights;
    }

    public void setApproveRights(RightsLevel approveRights) {
        this.approveRights = approveRights;
    }

    public WhetherState getReloan() {
        return reloan;
    }

    public void setReloan(WhetherState reloan) {
        this.reloan = reloan;
    }

    public String getLoanRecordId() {
        return loanRecordId;
    }

    public void setLoanRecordId(String loanRecordId) {
        this.loanRecordId = loanRecordId;
    }

    public BusinessType getBusinessType() {
        return businessType;
    }

    public void setBusinessType(BusinessType businessType) {
        this.businessType = businessType;
    }

    public IsIncludingEquity getIsIncludingEquity() {
        return isIncludingEquity;
    }

    public void setIsIncludingEquity(IsIncludingEquity isIncludingEquity) {
        this.isIncludingEquity = isIncludingEquity;
    }

    public EquityRecipient getEquityRecipient() {
        return equityRecipient;
    }

    public void setEquityRecipient(EquityRecipient equityRecipient) {
        this.equityRecipient = equityRecipient;
    }
}
