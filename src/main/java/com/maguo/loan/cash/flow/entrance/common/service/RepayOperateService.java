package com.maguo.loan.cash.flow.entrance.common.service;


import com.jinghang.common.util.CollectionUtil;
import com.maguo.loan.cash.flow.dto.repay.OnlineRepayRequestDto;
import com.maguo.loan.cash.flow.dto.repay.OnlineRepayResponseDto;
import com.maguo.loan.cash.flow.entity.BankRepayRecord;
import com.maguo.loan.cash.flow.entity.CustomRepayRecord;
import com.maguo.loan.cash.flow.entity.FlowRepayRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteePlan;
import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteeRecord;
import com.maguo.loan.cash.flow.entity.RepayPlan;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiCovert;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiEnumCovert;
import com.maguo.loan.cash.flow.entrance.common.dto.request.QueryRepayPlanReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.request.QueryRepayReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.request.RepayReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.request.RepayTrialReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.response.QueryRepayPlanResDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.response.QueryRepayResDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.response.RepayResDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.response.RepayTrialResDTO;
import com.maguo.loan.cash.flow.entrance.common.enums.RepayResult;
import com.maguo.loan.cash.flow.entrance.common.enums.RepayStatus;
import com.maguo.loan.cash.flow.entrance.common.enums.cache.CacheKey;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiBizException;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiResultCode;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OperationSource;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayRecordType;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.repository.BankRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.CustomRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.FlowRepayRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteePlanRepository;
import com.maguo.loan.cash.flow.repository.RepayExtraGuaranteeRecordRepository;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.RepayService;
import com.maguo.loan.cash.flow.service.TrialService;
import com.maguo.loan.cash.flow.util.AmountUtil;
import com.maguo.loan.cash.flow.util.ThreadLocalUtil;
import com.maguo.loan.cash.flow.vo.TrialResultVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.maguo.loan.cash.flow.job.susupend.SuspendActivationJob.LOCK_RELEASE_SECOND;


/**
 * 还款操作
 *
 * <AUTHOR>
 * @date 2024/9/3
 */
@Service
public class RepayOperateService {

    public static final Logger logger = LoggerFactory.getLogger(RepayOperateService.class);

    @Autowired
    private OrderService orderService;

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private RepayExtraGuaranteePlanRepository repayExtraGuaranteePlanRepository;

    @Autowired
    private TrialService trialService;

    @Autowired
    private FlowRepayRecordRepository flowRepayRecordRepository;

    @Autowired
    private CustomRepayRecordRepository customRepayRecordRepository;

    @Autowired
    private RepayExtraGuaranteeRecordRepository feeRecordRepository;

    @Autowired
    private RepayService repayService;

    @Autowired
    private BankRepayRecordRepository bankRepayRecordRepository;



    @Autowired
    private LockService lockService;

    public static final int ALL_SUCCEED = 0;

    public static final int FIRST_PART_SUCCEED = 1;

    public static final int SECOND_PART_SUCCEED = 2;


    /**
     * 查询还款计划
     */
    public QueryRepayPlanResDTO queryRepayPlan(QueryRepayPlanReqDTO reqDTO) {
        QueryRepayPlanResDTO result = new QueryRepayPlanResDTO();
        result.setRepayPlanList(new ArrayList<>());
        FlowChannel flowChannelEnum = ThreadLocalUtil.getFlowChannel();
        // 查询订单
        Order order = orderService.query(reqDTO.getPartnerOrderNo(), null, flowChannelEnum);
        if (Objects.isNull(order)) {
            throw new CommonApiBizException(CommonApiResultCode.ORDER_NOT_EXIST);
        }

        // 查询借据
        Loan loan = loanRepository.findByOrderId(order.getId());
        if (Objects.isNull(loan)) {
            return result;
        }

        List<RepayPlan> list = repayPlanRepository.findByLoanId(loan.getId());
        if (CollectionUtil.isEmpty(list)) {
            return result;
        }
        // 处理线下还款,实还与应还不一致问题
        list.forEach(element -> {
            if (element.getCustRepayState() == RepayState.REPAID) {
                element.setAmount(element.getActAmount());
                element.setGuaranteeAmt(element.getActGuaranteeAmt());
                element.setConsultFee(element.getActConsultFee());
                element.setPenaltyAmt(element.getActPenaltyAmt());
                element.setInterestAmt(element.getActInterestAmt());
                element.setPrincipalAmt(element.getActPrincipalAmt());
            }
        });

        List<QueryRepayPlanResDTO.RepayPlan> repayPlans = CommonApiCovert.INSTANCE.toRepayPlanDTOList(list);
        List<RepayExtraGuaranteePlan> feePlanList = repayExtraGuaranteePlanRepository.findByLoanId(loan.getId());

        // 还款计划数据拼装
        for (QueryRepayPlanResDTO.RepayPlan plan : repayPlans) {
            plan.setReduceAmount(BigDecimal.ZERO);
            plan.setCustRepayState(RepayState.REPAID.name().equals(plan.getCustRepayState()) ? RepayStatus.REPAID.name() : RepayStatus.NORMAL.name());
            // 已还
            if (RepayStatus.REPAID.name().equals(plan.getCustRepayState())) {
                continue;
            }
            // 还款计划状态:未还
            if (CollectionUtil.isEmpty(feePlanList)) {
                continue;
            }
            // 存在二次扣费计划
            Map<Integer, RepayState> map =
                feePlanList.stream().collect(Collectors.toMap(RepayExtraGuaranteePlan::getPeriod, RepayExtraGuaranteePlan::getPlanState));
            // 部分还款
            if (map.containsKey(plan.getPeriod())) {
                plan.setCustRepayState(RepayStatus.PARTIAL_REPAYMENT.name());
            }
        }
        result.setPartnerOrderNo(reqDTO.getPartnerOrderNo());
        result.setOrderId(order.getId());
        result.setRepayPlanList(repayPlans);
        return result;
    }

    /**
     * 还款试算
     */
    public RepayTrialResDTO repayTrial(RepayTrialReqDTO reqDTO) {

        // 流量渠道校验
        FlowChannel flowChannelEnum = ThreadLocalUtil.getFlowChannel();

        // 还款目的校验
        RepayPurpose repayPurpose = CommonApiEnumCovert.INSTANCE.toRepayPurpose(reqDTO.getRepayPurpose());
        if (Objects.isNull(repayPurpose)) {
            throw new CommonApiBizException(CommonApiResultCode.PARAM_REPAY_PURPOSE_FAIL);
        }

        // 查询订单
        Order order = orderService.query(reqDTO.getPartnerOrderNo(), null, flowChannelEnum);
        if (Objects.isNull(order)) {
            throw new CommonApiBizException(CommonApiResultCode.ORDER_NOT_EXIST);
        }

        // 查询借据
        Loan loan = loanRepository.findByOrderId(order.getId());
        if (Objects.isNull(loan)) {
            throw new CommonApiBizException(CommonApiResultCode.LOAN_NOT_EXIST);
        }
        // 试算
        TrialResultVo repayTrial = trialService.repayTrial(loan.getId(), repayPurpose, reqDTO.getPeriod(),null);

        RepayTrialResDTO result = CommonApiCovert.INSTANCE.toRepayTrialResDTO(repayTrial);
        result.setPeriod(reqDTO.getPeriod());
        return result;

    }

    /**
     * 还款
     */
    public RepayResDTO repay(RepayReqDTO reqDTO) {
        // 加锁
        String template = CacheKey.REPAY.getTemplate();
        String lockKey = MessageFormat.format(template, ThreadLocalUtil.getFlowChannel().name(), reqDTO.getPartnerOrderNo());
        Locker lock = lockService.getLock(lockKey);
        try {
            if (!lock.tryLock(Duration.ZERO, Duration.ofSeconds(LOCK_RELEASE_SECOND))) {
                throw new CommonApiBizException(CommonApiResultCode.REPEAT_SUBMIT);
            }
            return doRepay(reqDTO);
        } catch (InterruptedException e) {
            throw new CommonApiBizException(CommonApiResultCode.SYSTEM_ERROR);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 还款
     *
     * @param reqDTO reqDTO
     * @return RepayResDTO
     */
    private RepayResDTO doRepay(RepayReqDTO reqDTO) {
        // 流量渠道校验
        FlowChannel flowChannelEnum = ThreadLocalUtil.getFlowChannel();
        // 还款目的校验
        RepayPurpose repayPurpose = CommonApiEnumCovert.INSTANCE.toRepayPurpose(reqDTO.getRepayPurpose());
        if (Objects.isNull(repayPurpose)) {
            throw new CommonApiBizException(CommonApiResultCode.PARAM_REPAY_PURPOSE_FAIL);
        }

        // 查询订单
        Order order = orderService.query(reqDTO.getPartnerOrderNo(), null, flowChannelEnum);
        if (Objects.isNull(order)) {
            throw new CommonApiBizException(CommonApiResultCode.ORDER_NOT_EXIST);
        }

        // 查询借据
        Loan loan = loanRepository.findByOrderId(order.getId());
        if (Objects.isNull(loan)) {
            throw new CommonApiBizException(CommonApiResultCode.LOAN_NOT_EXIST);
        }

        // 重复单号校验
        FlowRepayRecord dbFlowRepayRecord = flowRepayRecordRepository.findByPartnerRepayNoAndFlowChannel(reqDTO.getPartnerRepayNo(), flowChannelEnum);
        if (dbFlowRepayRecord != null) {
            throw new CommonApiBizException(CommonApiResultCode.REPEAT_SUBMIT);
        }

        // 保存还款申请记录
        FlowRepayRecord flowRepayRecord = new FlowRepayRecord();
        flowRepayRecord.setRepayPurpose(repayPurpose);
        flowRepayRecord.setFlowChannel(flowChannelEnum);
        flowRepayRecord.setPartnerRepayNo(reqDTO.getPartnerRepayNo());
        flowRepayRecord.setApplyTime(LocalDateTime.now());
        flowRepayRecord.setLoanId(loan.getId());
        flowRepayRecord.setOrderId(order.getId());
        flowRepayRecord.setPartnerOrderNo(reqDTO.getPartnerOrderNo());
        flowRepayRecord.setPeriod(reqDTO.getPeriod());

        // 线上还款
        OnlineRepayRequestDto requestDto = new OnlineRepayRequestDto();
        requestDto.setRepayPurpose(repayPurpose);
        requestDto.setLoanId(loan.getId());
        requestDto.setPeriod(reqDTO.getPeriod());
        requestDto.setOperationSource(OperationSource.USER);
        OnlineRepayResponseDto responseDto = repayService.online(requestDto);

        flowRepayRecord.setRecordId(responseDto.getRecordId());
        flowRepayRecord.setRepayRecordType(responseDto.getRepayRecordType());
        flowRepayRecord = flowRepayRecordRepository.save(flowRepayRecord);

        RepayResDTO result = new RepayResDTO();
        result.setRepayId(flowRepayRecord.getId());
        return result;
    }

    /**
     * 查询线上还款结果
     */
    public QueryRepayResDTO queryRepayResult(QueryRepayReqDTO reqDTO) {
        // 流量渠道校验
        FlowChannel flowChannelEnum = ThreadLocalUtil.getFlowChannel();

        // 还款记录
        FlowRepayRecord flowRepayRecord =
            flowRepayRecordRepository.findByPartnerRepayNoAndFlowChannel(reqDTO.getPartnerRepayNo(), flowChannelEnum);

        if (Objects.isNull(flowRepayRecord)) {
            throw new CommonApiBizException(CommonApiResultCode.FLOW_REPAY_RECORD);
        }

        CustomRepayRecord customRepayRecord;
        RepayExtraGuaranteeRecord feeRecord;

        RepayRecordType repayRecordType = flowRepayRecord.getRepayRecordType();

        if (RepayRecordType.CUSTOMER_REPAY_RECORD == repayRecordType) {
            customRepayRecord = customRepayRecordRepository.findById(flowRepayRecord.getRecordId()).orElseThrow();
            feeRecord = feeRecordRepository.findTopBySourceRepayIdOrderByCreatedTimeAsc(customRepayRecord.getId());
        } else {
            feeRecord = feeRecordRepository.findById(flowRepayRecord.getRecordId()).orElseThrow();
            customRepayRecord = customRepayRecordRepository.findById(feeRecord.getSourceRepayId()).orElseThrow();
        }

        QueryRepayResDTO repayResDTO = genRepayResult(repayRecordType, customRepayRecord, feeRecord, null);
        repayResDTO.setPartnerOrderNo(flowRepayRecord.getPartnerOrderNo());
        repayResDTO.setPartnerRepayNo(reqDTO.getPartnerRepayNo());
        repayResDTO.setRepayId(flowRepayRecord.getId());

        return repayResDTO;

        //        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(flowRepayRecord.getLoanId(), flowRepayRecord.getPeriod());
        //        if (repayPlan.getCustRepayState() == RepayState.REPAID) {
        //            // 处理减免情况
        //            repayPlan.setGuaranteeAmt(repayPlan.getActGuaranteeAmt());
        //            repayPlan.setConsultFee(repayPlan.getActConsultFee());
        //            repayPlan.setPenaltyAmt(repayPlan.getActPenaltyAmt());
        //            repayPlan.setInterestAmt(repayPlan.getActInterestAmt());
        //            repayPlan.setPrincipalAmt(repayPlan.getActPrincipalAmt());
        //            repayPlan.setAmount(repayPlan.getActAmount());
        //        }
        //        QueryRepayResDTO result = CommonApiCovert.INSTANCE.toQueryRepayResDTO(flowRepayRecord, repayPlan);
        //        result.setReduceAmount(BigDecimal.ZERO);
        //        // 判断还款状态
        //        result.setRepayStatus(repayPlan.getCustRepayState() == RepayState.REPAID ? RepayStatus.REPAID.name() : RepayStatus.NORMAL.name());
        //
        //        if (repayPlan.getCustRepayState() == RepayState.NORMAL) {
        //            // 存在二次扣款会有部分还款状态
        //            Boolean flag = repayExtraGuaranteePlanRepository.existsByLoanIdAndPeriod(flowRepayRecord.getLoanId(), flowRepayRecord.getPeriod());
        //            if (flag) {
        //                // 部分还款
        //                result.setRepayStatus(RepayStatus.PARTIAL_REPAYMENT.name());
        //            }
        //        }
        //        return result;
    }


    public QueryRepayResDTO genRepayResult(RepayRecordType repayRecordType,
                                           CustomRepayRecord customRepayRecord,
                                           RepayExtraGuaranteeRecord feeRecord,
                                           QueryRepayResDTO result) {
        if (result == null) {
            result = new QueryRepayResDTO();
        }

        result.setPeriod(customRepayRecord.getPeriod());


        int succeedType = -1;

        //成功类型
        if (RepayRecordType.CUSTOMER_REPAY_RECORD == repayRecordType) {
            //一次还款
            if (ProcessState.FAILED == customRepayRecord.getRepayState()) {
                result.setRepayState(RepayResult.REPAY_FAIL);
            } else if (ProcessState.INIT == customRepayRecord.getRepayState()) {
                result.setRepayState(RepayResult.REPAY_PROCESSING);
            } else if (ProcessState.SUCCEED == customRepayRecord.getRepayState()) {
                if (Objects.isNull(feeRecord) || ProcessState.SUCCEED == feeRecord.getRepayState()) {
                    succeedType = ALL_SUCCEED;
                    result.setRepayState(RepayResult.REPAY_SUCCESS);
                } else {
                    succeedType = FIRST_PART_SUCCEED;
                    result.setRepayState(RepayResult.REPAY_PART_SUCCESS);
                }
            } else {
                //可能第二笔未扣到
                if (Objects.isNull(feeRecord)) {
                    result.setRepayState(RepayResult.REPAY_PROCESSING);
                } else if (ProcessState.FAILED == feeRecord.getRepayState()) {
                    succeedType = FIRST_PART_SUCCEED;

                    result.setRepayState(RepayResult.REPAY_PART_SUCCESS);
                } else {
                    result.setRepayState(RepayResult.REPAY_PROCESSING);
                }
            }
        } else {
            if (ProcessState.FAILED == feeRecord.getRepayState()) {
                result.setRepayState(RepayResult.REPAY_FAIL);
            } else if (ProcessState.SUCCEED == feeRecord.getRepayState()) {
                succeedType = SECOND_PART_SUCCEED;

                result.setRepayState(RepayResult.REPAY_SUCCESS);
            } else {
                result.setRepayState(RepayResult.REPAY_PROCESSING);
            }
        }

        if (result.getRepayState() != RepayResult.REPAY_SUCCESS && result.getRepayState() != RepayResult.REPAY_PART_SUCCESS) {
            return result;
        }

        BankRepayRecord bankRepayRecord = bankRepayRecordRepository
            .findByLoanIdAndPeriodAndStateIn(customRepayRecord.getLoanId(), customRepayRecord.getPeriod(), ProcessState.SUCCEED, ProcessState.PROCESSING);

        RepayPlan repayPlan = repayPlanRepository.findByLoanIdAndPeriod(customRepayRecord.getLoanId(), customRepayRecord.getPeriod());

        switch (succeedType) {
            case ALL_SUCCEED -> {
                result.setActPrincipalAmt(repayPlan.getActPrincipalAmt());
                result.setActInterestAmt(repayPlan.getActInterestAmt());
                result.setActGuaranteeAmt(repayPlan.getActGuaranteeAmt());
                result.setActConsultFee(repayPlan.getActConsultFee());
                result.setActPenaltyAmt(repayPlan.getActPenaltyAmt());
                result.setActRepayTime(customRepayRecord.getRepaidDate());
            }

            case FIRST_PART_SUCCEED -> {
                result.setActPrincipalAmt(bankRepayRecord.getPrincipal());
                result.setActInterestAmt(bankRepayRecord.getInterest());
                result.setActGuaranteeAmt(bankRepayRecord.getGuarantee());
                result.setActConsultFee(bankRepayRecord.getConsultFee());
                result.setActPenaltyAmt(bankRepayRecord.getPenalty());
                result.setActRepayTime(bankRepayRecord.getRepayTime());
            }

            case SECOND_PART_SUCCEED -> {
                result.setActGuaranteeAmt(feeRecord.getActGuaranteeAmt());
                result.setActConsultFee(feeRecord.getActConsultAmt());
                result.setActPenaltyAmt(feeRecord.getActPlatformPenaltyAmt());
                result.setActRepayTime(customRepayRecord.getRepaidDate());
            }
            default -> {
            }
        }
        result.setActAmount(AmountUtil.sum(result.getActPrincipalAmt(), result.getActInterestAmt(), result.getActGuaranteeAmt(),
            result.getActConsultFee(), result.getActPenaltyAmt()));

        return result;

    }
}
