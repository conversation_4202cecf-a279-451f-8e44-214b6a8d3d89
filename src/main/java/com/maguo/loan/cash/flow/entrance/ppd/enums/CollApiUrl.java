package com.maguo.loan.cash.flow.entrance.ppd.enums;

import com.maguo.loan.cash.flow.entrance.ppd.common.ResultCode;

/**
 * <AUTHOR> gale
 * @Classname CollApiUrl
 * @Description 催收接口api
 * @Date 2024/4/2 10:13
 */
public enum CollApiUrl {

    NEW_CASE("/api/new/case", "推送新案", ResultCode.PUSH_NEW_CASE_ERROR),
    UPDATE_CASE("/api/update/case", "更新案件", ResultCode.PUSH_UPDATE_CASE_ERROR),
    ADD_CASE("/api/repay/add", "新增还款", ResultCode.PUSH_ADD_REPAY_ERROR),
    PAYMENT_RECEIPT("/api/paymentReceipt", "回执", ResultCode.PUSH_PAYMENT_RECEIPT_ERROR),
    COMPLAINT("/api/complaint", "投诉", ResultCode.PUSH_COMPLAINT_ERROR),
    ADD_REMARK("/api/remark/add", "备注", ResultCode.PUSH_REMARK_ERROR),
    REPAY_RECORD_INFO("/api/repay/record", "还款记录", ResultCode.PUSH_REPAY_RECORD_INFO_ERROR);

    private final String url;
    private final String desc;
    private final ResultCode resultCode;

    CollApiUrl(String url, String desc, ResultCode resultCode) {
        this.url = url;
        this.desc = desc;
        this.resultCode = resultCode;
    }

    public String getUrl() {
        return url;
    }

    public String getDesc() {
        return desc;
    }

    public ResultCode getResultCode() {
        return resultCode;
    }
}
