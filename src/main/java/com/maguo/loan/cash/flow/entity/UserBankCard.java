package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.ProtocolChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "user_bank_card")
public class UserBankCard extends BaseEntity {
    private String id;

    private String userId;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 户名
     */
    private String cardName;
    /**
     * 预留手机号
     */
    private String phone;
    /**
     * 身份证
     */
    private String certNo;
    /**
     * 银行编码
     */
    private String bankCode;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 绑卡渠道
     */
    @Enumerated(EnumType.STRING)
    private ProtocolChannel channel;
    /**
     * 绑卡主体
     */
    private String merchantNo;
    /**
     * 绑卡协议号
     */
    private String agreeNo;

    @Enumerated(EnumType.STRING)
    private BoundSide boundSide;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public ProtocolChannel getChannel() {
        return channel;
    }

    public void setChannel(ProtocolChannel channel) {
        this.channel = channel;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getAgreeNo() {
        return agreeNo;
    }

    public void setAgreeNo(String agreeNo) {
        this.agreeNo = agreeNo;
    }

    public BoundSide getBoundSide() {
        return boundSide;
    }

    public void setBoundSide(BoundSide boundSide) {
        this.boundSide = boundSide;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }
}
