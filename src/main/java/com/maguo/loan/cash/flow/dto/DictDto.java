package com.maguo.loan.cash.flow.dto;

import java.util.Map;

public class DictDto {

    private String name;

    private Map<String, String> dictionary;


    public DictDto() {
    }

    public DictDto(String name, Map<String, String> dictionary) {
        this.name = name;
        this.dictionary = dictionary;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, String> getDictionary() {
        return dictionary;
    }

    public void setDictionary(Map<String, String> dictionary) {
        this.dictionary = dictionary;
    }
}
