package com.maguo.loan.cash.flow.entity;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/20
 */
@Entity
@Table(name = "router_check_record")
public class RouterCheckRecord extends BaseEntity {
    /**
     * 路由记录ID
     */
    private String routerRecordId;
    /**
     * 路由配置ID
     */
    private String routerConfigId;
    /**
     * 路由优先级
     */
    private Integer bankPriority;
    /**
     * 资金渠道
     */
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;
    /**
     * 资方支持利率
     */
    private String supportIrrLevel;
    /**
     * 资方授信日限额
     */
    private BigDecimal bankCreditDayLimit;
    /**
     * 资方放款日限额
     */
    private BigDecimal bankLoanDayLimit;
    /**
     * 支持的期数
     */
    private String bankPeriodsRange;
    /**
     * 支持年龄区间
     */
    private String bankAgesRange;
    /**
     * 单笔限额区间
     */
    private String bankSingleAmtRange;
    /**
     * 资方启用状态
     */
    @Enumerated(EnumType.STRING)
    private AbleStatus bankEnabled;
    /**
     * 资方授信已使用
     */
    private BigDecimal bankCreditDayUsed;
    /**
     * 资方放款已使用
     */
    private BigDecimal bankLoanDayUsed;
    /**
     * 授信开始
     */
    private String bankCreditStartTime;
    /**
     * 授信截止
     */
    private String bankCreditEndTime;
    /**
     * 放款开始
     */
    private String bankLoanStartTime;
    /**
     * 放款截止
     */
    private String bankLoanEndTime;

    /**
     * 是否可续借
     */
    @Enumerated(EnumType.STRING)
    private WhetherState renewedFlag;

    /**
     * 流量规则ID
     */
    private String flowRuleId;
    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;
    /**
     * 分给流量的授信日限额
     */
    private BigDecimal flowCreditDayAmt;
    /**
     * 分给流量的放款日限额
     */
    private BigDecimal flowLoanDayAmt;
    /**
     * 流量授信已使用
     */
    private BigDecimal flowCreditDayUsed;
    /**
     * 流量放款已使用
     */
    private BigDecimal flowLoanDayUsed;

    @Enumerated(EnumType.STRING)
    private AbleStatus flowEnabled;

    public String getSupportIrrLevel() {
        return supportIrrLevel;
    }

    public void setSupportIrrLevel(String supportIrrLevel) {
        this.supportIrrLevel = supportIrrLevel;
    }

    public String getRouterRecordId() {
        return routerRecordId;
    }

    public void setRouterRecordId(String routerRecordId) {
        this.routerRecordId = routerRecordId;
    }

    public String getRouterConfigId() {
        return routerConfigId;
    }

    public void setRouterConfigId(String routerConfigId) {
        this.routerConfigId = routerConfigId;
    }

    public Integer getBankPriority() {
        return bankPriority;
    }

    public void setBankPriority(Integer bankPriority) {
        this.bankPriority = bankPriority;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public BigDecimal getBankCreditDayLimit() {
        return bankCreditDayLimit;
    }

    public void setBankCreditDayLimit(BigDecimal bankCreditDayLimit) {
        this.bankCreditDayLimit = bankCreditDayLimit;
    }

    public BigDecimal getBankLoanDayLimit() {
        return bankLoanDayLimit;
    }

    public void setBankLoanDayLimit(BigDecimal bankLoanDayLimit) {
        this.bankLoanDayLimit = bankLoanDayLimit;
    }

    public String getBankPeriodsRange() {
        return bankPeriodsRange;
    }

    public void setBankPeriodsRange(String bankPeriodsRange) {
        this.bankPeriodsRange = bankPeriodsRange;
    }

    public String getBankAgesRange() {
        return bankAgesRange;
    }

    public void setBankAgesRange(String bankAgesRange) {
        this.bankAgesRange = bankAgesRange;
    }

    public String getBankSingleAmtRange() {
        return bankSingleAmtRange;
    }

    public void setBankSingleAmtRange(String bankSingleAmtRange) {
        this.bankSingleAmtRange = bankSingleAmtRange;
    }

    public AbleStatus getBankEnabled() {
        return bankEnabled;
    }

    public void setBankEnabled(AbleStatus bankEnabled) {
        this.bankEnabled = bankEnabled;
    }

    public BigDecimal getBankCreditDayUsed() {
        return bankCreditDayUsed;
    }

    public void setBankCreditDayUsed(BigDecimal bankCreditDayUsed) {
        this.bankCreditDayUsed = bankCreditDayUsed;
    }

    public BigDecimal getBankLoanDayUsed() {
        return bankLoanDayUsed;
    }

    public void setBankLoanDayUsed(BigDecimal bankLoanDayUsed) {
        this.bankLoanDayUsed = bankLoanDayUsed;
    }

    public String getBankCreditStartTime() {
        return bankCreditStartTime;
    }

    public void setBankCreditStartTime(String bankCreditStartTime) {
        this.bankCreditStartTime = bankCreditStartTime;
    }

    public String getBankCreditEndTime() {
        return bankCreditEndTime;
    }

    public void setBankCreditEndTime(String bankCreditEndTime) {
        this.bankCreditEndTime = bankCreditEndTime;
    }

    public String getBankLoanStartTime() {
        return bankLoanStartTime;
    }

    public void setBankLoanStartTime(String bankLoanStartTime) {
        this.bankLoanStartTime = bankLoanStartTime;
    }

    public String getBankLoanEndTime() {
        return bankLoanEndTime;
    }

    public void setBankLoanEndTime(String bankLoanEndTime) {
        this.bankLoanEndTime = bankLoanEndTime;
    }

    public String getFlowRuleId() {
        return flowRuleId;
    }

    public void setFlowRuleId(String flowRuleId) {
        this.flowRuleId = flowRuleId;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public BigDecimal getFlowCreditDayAmt() {
        return flowCreditDayAmt;
    }

    public void setFlowCreditDayAmt(BigDecimal flowCreditDayAmt) {
        this.flowCreditDayAmt = flowCreditDayAmt;
    }

    public BigDecimal getFlowLoanDayAmt() {
        return flowLoanDayAmt;
    }

    public void setFlowLoanDayAmt(BigDecimal flowLoanDayAmt) {
        this.flowLoanDayAmt = flowLoanDayAmt;
    }

    public BigDecimal getFlowCreditDayUsed() {
        return flowCreditDayUsed;
    }

    public void setFlowCreditDayUsed(BigDecimal flowCreditDayUsed) {
        this.flowCreditDayUsed = flowCreditDayUsed;
    }

    public BigDecimal getFlowLoanDayUsed() {
        return flowLoanDayUsed;
    }

    public void setFlowLoanDayUsed(BigDecimal flowLoanDayUsed) {
        this.flowLoanDayUsed = flowLoanDayUsed;
    }

    public AbleStatus getFlowEnabled() {
        return flowEnabled;
    }

    public void setFlowEnabled(AbleStatus flowEnabled) {
        this.flowEnabled = flowEnabled;
    }

    public WhetherState getRenewedFlag() {
        return renewedFlag;
    }

    public void setRenewedFlag(WhetherState renewedFlag) {
        this.renewedFlag = renewedFlag;
    }
}
