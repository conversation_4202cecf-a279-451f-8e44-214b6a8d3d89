package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.FlowRouteConfig;
import com.maguo.loan.cash.flow.enums.FlowCapitalEnable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <AUTHOR>
 * 资金流量映射关系
 */
public interface FlowRouteConfigRepository extends JpaRepository<FlowRouteConfig, String> {

    List<FlowRouteConfig> findByFlowIdAndEnabled(String id, FlowCapitalEnable flowCapitalEnable);

}
