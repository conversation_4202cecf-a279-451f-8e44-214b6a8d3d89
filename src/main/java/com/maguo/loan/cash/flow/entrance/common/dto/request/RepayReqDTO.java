package com.maguo.loan.cash.flow.entrance.common.dto.request;

import jakarta.validation.constraints.NotBlank;

/**
 * 还款入参
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
public class RepayReqDTO {
    /**
     * 合作机构单号
     */
    @NotBlank
    private String partnerOrderNo;

    /**
     * 合作机构还款流水号
     */
    @NotBlank
    private String partnerRepayNo;
    /**
     * 还款目的
     */
    @NotBlank
    private String repayPurpose;
    /**
     * 期次
     */
    @NotBlank
    private Integer period;

    public String getPartnerRepayNo() {
        return partnerRepayNo;
    }

    public void setPartnerRepayNo(String partnerRepayNo) {
        this.partnerRepayNo = partnerRepayNo;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(String repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }
}
