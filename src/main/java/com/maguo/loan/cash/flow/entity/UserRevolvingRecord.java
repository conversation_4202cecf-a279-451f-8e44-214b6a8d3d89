package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.RevolvingAdjustType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户循环额度调整记录表
 */
@Entity
@Table(name = "user_revolving_record")
public class UserRevolvingRecord extends BaseEntity {

    /**
     * 用户
     */
    private String userId;

    /**
     * 调整额度
     */
    private BigDecimal adjustAmount;

    /**
     * 调整类型
     */
    @Enumerated(EnumType.STRING)
    private RevolvingAdjustType adjustType;

    /**
     * 调整时间
     */
    private LocalDateTime adjustTime;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public BigDecimal getAdjustAmount() {
        return adjustAmount;
    }

    public void setAdjustAmount(BigDecimal adjustAmount) {
        this.adjustAmount = adjustAmount;
    }

    public RevolvingAdjustType getAdjustType() {
        return adjustType;
    }

    public void setAdjustType(RevolvingAdjustType adjustType) {
        this.adjustType = adjustType;
    }

    public LocalDateTime getAdjustTime() {
        return adjustTime;
    }

    public void setAdjustTime(LocalDateTime adjustTime) {
        this.adjustTime = adjustTime;
    }

    @Override
    protected String prefix() {
        return "URR";
    }
}
