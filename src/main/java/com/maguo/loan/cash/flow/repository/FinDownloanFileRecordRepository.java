package com.maguo.loan.cash.flow.repository;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.entity.FinDownloadFileRecord;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * 流量配置
 */
public interface FinDownloanFileRecordRepository extends JpaRepository<FinDownloadFileRecord, String> {

    @Query("select l from FinDownloadFileRecord l where l.bankChannel = ?1 and l.status = ?2 and l.createdTime >= ?3")
    List<FinDownloadFileRecord> findByBankChannelAndStatusAndCreatedTime(BankChannel bankChannel, ProcessState status, LocalDateTime startDate);

    @Query("select l from FinDownloadFileRecord l where l.bankChannel = ?1 and l.fileType = ?2 and l.createdTime between ?3 and ?4")
    List<FinDownloadFileRecord> findByFileType(BankChannel bankChannel, FileType fileType, LocalDateTime startDate, LocalDateTime endDate);

    FinDownloadFileRecord findTopByBizIdAndFileTypeOrderByCreatedTimeDesc(String bizId, FileType fileType);
}
