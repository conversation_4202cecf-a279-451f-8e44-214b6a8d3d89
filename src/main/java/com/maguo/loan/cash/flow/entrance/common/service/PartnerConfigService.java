package com.maguo.loan.cash.flow.entrance.common.service;


import com.jinghang.common.util.CollectionUtil;
import com.maguo.loan.cash.flow.entity.PartnerConfig;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiCovert;
import com.maguo.loan.cash.flow.entrance.common.dto.response.PartnerConfigResDTO;
import com.maguo.loan.cash.flow.repository.PartnerConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 获取相关配置
 *
 * <AUTHOR>
 * @date 2024/9/2
 */
@Service
public class PartnerConfigService {

    @Autowired
    private PartnerConfigRepository partnerConfigRepository;

    public List<PartnerConfigResDTO> getAllPartnerConfig() {
        List<PartnerConfig> list = partnerConfigRepository.findAll();
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return CommonApiCovert.INSTANCE.toPartnerConfigResDTOS(list);
    }
}
