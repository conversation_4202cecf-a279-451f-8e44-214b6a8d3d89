package com.jinghang.ppd.api;


import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.collection.PpdReduceApplyRequest;
import com.jinghang.ppd.api.dto.collection.PpdReduceApplyResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 催收api
 */
public interface CollectionApi {

    @PostMapping("collect/api/reduction")
    RestResult<PpdReduceApplyResponse> reduction(@RequestBody PpdReduceApplyRequest trailDto);


}
