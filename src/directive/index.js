import directive from './directives'

const importDirective = app => {
  /**
   * 拖拽指令 v-draggable="options"
   * options = {
   *  trigger: /这里传入作为拖拽触发器的CSS选择器/,
   *  body:    /这里传入需要移动容器的CSS选择器/,
   *  recover: /拖动结束之后是否恢复到原来的位置/
   * }
   */
  app.directive('draggable', directive.draggable)
  app.directive('waterMark', directive.waterMark)
  app.directive('disCopy', directive.disCopy)
  app.directive('draggleVue3', directive.draggleVue3)
}

export default importDirective
