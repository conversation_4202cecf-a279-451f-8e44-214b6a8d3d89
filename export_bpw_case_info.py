# !/usr/bin/python3
import json
import multiprocessing

import requests
import os
import datetime as datetime
import openpyxl
from apscheduler.executors.pool import ProcessPoolExecutor
from apscheduler.schedulers import blocking as bl
from apscheduler.schedulers import background as bg
from elasticsearch import Elasticsearch
from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE
from openpyxl.utils import get_column_letter
from NacosHelper import NacosHelper
from es_query_builder import *
import redis

from utils import base_utils
from utils.base_utils import init_base_conf, init_heartbeat
from utils import encrypt_utils

config = NacosHelper().init_config()
logger, fileUtil, ossSwitch, esSwitch, encryptSwitch, encryptMode, bucket, mysql_pool, taskUtil, redis_pool = init_base_conf("export_bpw_case_info", config)

task_queue_key = "export_case_from_es_task_queue"

# 减免审核结果字典
applyStatusDict = {'0': '申请', '1': '审核通过', '2': '审核失败'}

# 联系人状态字典
contact_status_dict = {0: '正常', 1: '无效'}

# 附加文件模板
caseFileEntityDict = {
    '0': {'value': 'out_serial_temp', 'name': '委案编号'},
    '1': {'value': 'case_name', 'name': '姓名'},
    '2': {'value': 'new_file_name', 'name': '文件名'}
}

caseTagDict = {'name': '案件标签', 'value': 'case_tag'}

operationWayDict = {'name': '催收方式', 'value': 'operation_way_name'}

# 案件信息基本字段
caseBaseSql = "ca.id,ca.out_serial_no,ca.own_mobile,ca.org_delt_id,delt.name as org_delt_name,p.name as product_name,ca.name as " \
              "case_name,ca.id_card, ca.entrust_start_time,ca.entrust_end_time,ca.amount,ca.pay_amount,inbat.name as batch_no," \
              "outbat.name as out_batch_no, u.name as agent,u.user_no,ca.case_status,ca.allot_status,ca.operation_state," \
              "ca.overdue_date,ca.overdue_days, dep_team1.name as dep_name,dep_team2.name as team_name," \
              "ca.call_status,ca.field_json,ca.oper_status,ca.last_follow_time,ifnull(cador.last_follow_time,ca.last_follow_time) as " \
              "debtor_last_follow_time,ifnull(cador.follow_count,ca.follow_count) as debtor_follow_count,ca.division_time,ca.end_config_id,ca.end_type,cec.attached_res, " \
              "ca.delay_no, ca.end_no,ca.ptp_time,ca.ptp_amount"

# 业务基本字段id
caseBaseBusinessIdSql = "ca.id case_id,delt.id as org_delt_id,p.id as product_id,inbat.id as inner_batch_id,outbat.id" \
                        " as out_batch_id,u.id as user_id,dep_team1.id as dep_id, dep_team2.id as team_id,cador.id as" \
                        " debt_id "

# 还款信息基本字段
repaymentBaseSql = ",rep.repayment_from,rep.repayment_time,rep.repayment_amount,rep.repayment_type," \
                   "rep.repayment_card_no, rep.repayment_style,rep.case_operator_name repayment_operator_name," \
                   "rep.create_time repayment_create_time, rep.desc repayment_desc "
# 减免信息基本字段
reductionBaseSql = ",re.reduce_amount reduction_amount,re.desc apply_desc,re.create_time apply_time," \
                   "apply_ur.name apply_name,approval_ur.name approval_name,re.status apply_status"
# 基本关联
leftJoinBaseSql = " inner join `org_delt` delt on delt.id = ca.org_delt_id\
        inner join `product` p on p.id = ca.product_id\
        left join `inner_batch` inbat on ca.inner_batch_id = inbat.id\
        inner join `out_batch` outbat on ca.out_batch_id = outbat.id\
        left join `org_dep_team` dep_team1 on ca.dep_id = dep_team1.id\
        left join `org_dep_team` dep_team2 on ca.team_id = dep_team2.id\
        left join user u on ca.user_id = u.id\
        left join case_debtor cador on cador.id=ca.debt_id"

# 查询催收小结的sql
# caseNoteSql = "select t.case_id,GROUP_CONCAT(t.content ORDER BY t.id DESC SEPARATOR '│') as case_note from case_note t where t.type=0 and t.case_id in %s GROUP BY t.case_id "
caseNoteSql = "select t.case_id,t.content as case_note from case_note t where t.type=0 and t.case_id in %s "

# 减免总和
reductionSumSql = "select case_id, sum(reduce_amount) as reduction_total from apply_reduction where status = 1 and case_id in %s group by case_id"

# 查询联系人
contactSql = "SELECT ca.id as case_id, ca.name as case_name, ca.out_serial_no as out_serial_no, ca.org_delt_id, delt.name as delt_name,IF(deb.id IS NOT NULL, deb.id ,ca.id ) AS rel_id," \
             " IF(deb.id IS NOT NULL, 1, 0 ) as is_conjoint,IF(con.id IS NOT null, con.name,ca.name) as contact_name," \
             " IF(con.id IS NOT null, con.status, 0) as contact_status," \
             " IF(con.id IS NOT null, con.relation_type,'本人') as relation_type," \
             " IF(con.id IS NOT null,con.mobile,ca.own_mobile) as contact_mobile," \
             " IF(con.desc IS NOT null,con.desc,'') as remark," \
             " IF(ctc.name is NOT null, ctc.name, '') as contact_type_name" \
             " FROM	case_info ca" \
             "	INNER JOIN org_delt delt ON ca.org_delt_id = delt.id" \
             "	LEFT JOIN case_debtor deb ON ca.debt_id = deb.id " \
             "	INNER JOIN contacts con on IF( deb.id IS NULL, 0, 1 )=con.is_conjoint and ifnull( deb.id, ca.id )=con.rel_id" \
             "  LEFT JOIN  contact_type_config ctc on ctc.id = con.contact_type_id" \
             " WHERE con.status<>-1 and ca.id IN %s "
# 基本关联
leftJoinOtherBaseSql = " left join `org_delt` delt on delt.id = ca.org_delt_id" \
                       " left join `product` p on p.id = ca.product_id" \
                       " left join `inner_batch` inbat on ca.inner_batch_id = inbat.id" \
                       " left join `out_batch` outbat on ca.out_batch_id = outbat.id" \
                       " left join `org_dep_team` dep_team1 on ca.dep_id = dep_team1.id" \
                       " left join `org_dep_team` dep_team2 on ca.team_id = dep_team2.id" \
                       " left join user u on ca.user_id = u.id" \
                       " left join case_debtor cador on cador.id=ca.debt_id" \
                       " left join case_end_config cec on ca.end_config_id = cec.id"

# 案件关联
caseFromSql = " from `case_info` as ca "
# 还款关联
repaymentFromSql = " from `case_repayment` rep left join `case_info` ca on rep.case_id=ca.id "
# 减免关联
reductionFromSql = " from `apply_reduction` re left join `case_info` ca on re.case_id=ca.id " \
                   " left join `user` apply_ur on apply_ur.id=re.create_by " \
                   " left join `user` approval_ur on approval_ur.id=re.update_by "
# 案件标签
tagSql = "select ctr.case_id as case_id, ct.name as case_tag from case_tag ct left join case_tag_rel ctr ON ct.id = ctr.tag_id where ctr.case_id in %s "

# 催收方式
operationWaySql = "SELECT case_id,case when operation_way=1 then '电催' when operation_way=2 then '外访' when operation_way=3 then '调解' when operation_way=4 then '诉讼' end as operation_way_name FROM case_operation_way_rel WHERE case_id IN %s "

caseFileSql = "select cf.id file_id, ca.id as case_id, ca.name  as case_name, ca.org_delt_id, ca.out_serial_temp, cf.file_name, cf.file_url, " \
              " concat(SUBSTRING_INDEX(file_name,'.',1), '_', cf.id, '.' ,SUBSTRING_INDEX(file_name,'.',-1)) as new_file_name" \
              " from case_info ca" \
              " inner join case_file cf on cf.case_id = ca.id" \
              " where ca.id IN %s"

# 催收备注
operationDescSql = " select cao.id, cao.case_id, cao.desc from case_operation cao " \
                   " inner join (select max(id) as id from case_operation where case_id in %s group by case_id) temp on cao.id = temp.id"

# 默认分页大小
pageSize = 500


def push_task():
    rs = redis.Redis(connection_pool=redis_pool)
    is_connected = rs.ping
    if is_connected is ConnectionError:
        return
    taskList = taskUtil.getUndoTasks(2)
    if len(taskList) <= 0:
        return
    task_queue = rs.smembers(task_queue_key)
    num = 0
    for task in taskList:
        if str(task["id"]) not in task_queue:
            rs.sadd(task_queue_key, task["id"])
            num += 1


def consumer_task():
    rs = redis.Redis(connection_pool=redis_pool)
    is_connected = rs.ping
    if is_connected is ConnectionError:
        return
    task_id = rs.spop(task_queue_key)
    # task_id = 19434
    if task_id is None:
        return
    try:

        task = mysql_pool.select_one(f"select org_id,data,id,dep_id,team_id from download_task where status = 0 and  id = {task_id}")
        # task = mysql_pool.select_one(f"select org_id,data,id,dep_id,team_id from download_task where  id = 19434")
        if task is None:
            return
        orgId, data, taskId = task["org_id"], task["data"], task["id"]
        language = taskUtil.getOrgLanguage(orgId)
        all_fields = taskUtil.getLanguageFieldDict("caseExportFields", language)
        start = datetime.datetime.now()
        logger.info("执行案件导出任务taskId:" + str(taskId) + ",data:" + repr(data))
        taskUtil.updateTaskIng(taskId)
        export_case_info(orgId, data, taskId, all_fields, language)
        end = datetime.datetime.now()
        logger.info("任务id:" + str(taskId) + "开始时间:" + str(start) + ",结束时间:" + str(end))
    except Exception as ex:
        logger.exception(ex)
        taskUtil.updateFail(task_id, str(ex))


def export_case_info(orgId, data, task_id, allfields, language):
    if data is None:
        raise RuntimeError("任务序列化数据为空")
    if allfields is None:
        raise RuntimeError("全部案件导出字段为空")
    if orgId is None:
        raise RuntimeError("公司id为空")
    dataJson = json.loads(data)

    # 获取模板字段
    templateId = dataJson['templateId']
    caseEntity, containsCaseNote = getCaseEntity(templateId, allfields, orgId)
    logger.info("模板字段:" + repr(caseEntity))
    operStateDict = taskUtil.getOperaStateDict(orgId)
    operStatusDict = taskUtil.getOperStatusDict(orgId)

    order = {"value": ""}
    total = {"value": 1}
    if esSwitch:
        caseIdsList = get_case_ids_from_es(orgId, dataJson, order, total)
    else:
        caseIdsList = get_case_ids_from_mysql(orgId, dataJson, order, total)
    export_excel(task_id, caseEntity, dataJson, order, language, caseIdsList, total, operStatusDict, operStateDict, containsCaseNote)


def get_case_ids_from_es(orgId, dataJson, order, total):
    """ 注意此处的用法，该函数返回一个yield迭代器，在调用的时候会立即返回，然后在需要遍历结果的时候才会执行，
    同时会修改order和total的值，mysql同理
    :param orgId: 公司id
    :param dataJson:
    :param order: 排序字段（为了保证Elasticsearch导出查询和mysql导出查询的顺序一致）遍历使用时此值会发生变化
    :param total: 总数，同order，遍历结果时会发生变化
    :return: 返回可遍历的迭代器
    """
    _must = []
    _must_not = []
    _should = []
    _sort_list = []
    _must.append(TermQuery(key="recovery", value=0))
    _must.append(TermQuery(key="orgId", value=str(orgId)))
    # 委案公司多选
    if 'deltIds' in dataJson.keys():
        _must.append(TermsQuery(key="orgDeltId", values=dataJson['deltIds'].split(",")))
    # 案件颜色
    if 'color' in dataJson.keys():
        _must.append(TermQuery(key="color", value=str(dataJson['color'])))
    # 债务人姓名
    if 'names' in dataJson.keys():
        _must.append(TermsQuery(key="name", values=dataJson['names']))
    # 债务人身份证
    if 'idCards' in dataJson.keys():
        _must.append(TermsQuery(key="idCard", values=dataJson['idCards']))
    # 债务人手机号
    if 'mobiles' in dataJson.keys():
        _must.append(TermsQuery(key="ownMobile", values=dataJson['mobiles']))
    # 债务人委案编号
    if 'outSerialNos' in dataJson.keys():
        length = len(dataJson['outSerialNos'])
        if length > 1:
            _must.append(TermsQuery(key="outSerialTemp", values=dataJson['outSerialNos']))
        elif length == 1:
            _must.append(WildcardQuery(key="outSerialTemp", value=dataJson['outSerialNos'][0] + "*"))
    # 自定义标签 searchKeyParam是一个字典
    if 'searchKeyParam' in dataJson.keys() and len(dataJson["searchKeyParam"]) > 0:
        for key in dataJson["searchKeyParam"]:
            _must.append(TermsQuery(key="fieldJson." + key + ".keyword", values=dataJson["searchKeyParam"][key]))
    # 委案时间查询
    if 'deltStart' in dataJson.keys() and 'deltEnd' in dataJson.keys():
        _range = RangeQuery(key="entrustStartTime",
                            _from=str(dataJson['deltStart']),
                            to=str(dataJson['deltEnd']),
                            otherMap={"format": "yyyy-MM-dd HH:mm:ss"})
        _must.append(_range)
    # 产品编号查询，支持多选
    if 'products' in dataJson.keys():
        _must.append(TermsQuery(key="productId", values=dataJson['products'].split(",")))
    # 催员ID查询，支持多选
    if 'userIds' in dataJson.keys():
        _must.append(TermsQuery(key="userId", values=dataJson['userIds'].split(",")))
    # 团队查询
    if 'teamIds' in dataJson.keys():
        _must.append(TermsQuery(key="teamId", values=dataJson['teamIds'].split(",")))
    # 分公司(委外机构)查询
    if 'depIds' in dataJson.keys():
        _must.append(TermsQuery(key="depId", values=dataJson['depIds'].split(",")))
    # 团队查询
    if 'teamId' in dataJson.keys():
        _must.append(TermQuery(key="teamId", value=dataJson['teamId']))
    # 分公司(委外机构)查询
    if 'depId' in dataJson.keys():
        _must.append(TermQuery(key="depId", value=dataJson['depId']))
    # 催收状态查询，支持多选
    if 'actionTypes' in dataJson.keys():
        _must.append(TermsQuery(key="operStatus", values=dataJson['actionTypes'].split(",")))
    # 协催结果查询，支持多选
    if 'callStatuses' in dataJson.keys():
        _must.append(TermsQuery(key="callStatus", values=dataJson['callStatuses'].split(",")))
    # action判断
    if 'action' in dataJson.keys():
        if dataJson['action'] == 1 or dataJson['action'] == 6 or dataJson['action'] == 9:
            _must.append(TermsQuery(key="caseStatus", values=[0, 1]))
            _must.append(TermQuery(key="allotStatus", value=3))
        if dataJson['action'] == 2:
            _must.append(TermQuery(key="allotStatus", value=0))
        if dataJson['action'] == 3:
            _must_not.append(TermQuery(key="allotStatus", value=3))
        if dataJson['action'] == 4:
            _must.append(TermQuery(key="caseStatus", value=4))
        if dataJson['action'] == 5:
            _must.append(TermQuery(key="caseStatus", value=3))
        if dataJson['action'] == 7:
            _must.append(TermQuery(key="allotStatus", value=2))
        if dataJson['action'] == 8:
            _must.append(TermQuery(key="allotStatus", value=1))
    # status查询
    if 'caseStatues' in dataJson.keys():
        _must.append(TermsQuery(key="caseStatus", values=dataJson['caseStatues'].split(",")))
    if 'notInCaseStatuses' in dataJson.keys():
        _must_not.append(TermsQuery(key="caseStatus", values=dataJson['notInCaseStatuses'].split(",")))
    if 'allotStatues' in dataJson.keys():
        _must.append(TermsQuery(key="allotStatus", values=dataJson['allotStatues'].split(",")))
    if 'notInAllotStatues' in dataJson.keys():
        _must_not.append(TermsQuery(key="allotStatus", values=dataJson['notInAllotStatues'].split(",")))
    # 逾期日期搜索
    if 'overdueDateStart' in dataJson.keys():
        _must.append(RangeQuery(key="overdueDate",
                                _from=str(dataJson['overdueDateStart']),
                                to=None,
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"}))
    if 'overdueDateEnd' in dataJson.keys():
        _must.append(RangeQuery(key="overdueDate",
                                _from=None,
                                to=str(dataJson['overdueDateEnd']),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_upper=False))
    # 上次跟进时间搜索
    if 'lastFollowTimeStart' in dataJson.keys():
        _must.append(RangeQuery(key="lastFollowTime",
                                _from=str(dataJson['lastFollowTimeStart']),
                                to=None,
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=True,
                                include_upper=False))
    # 上次跟进时间搜索
    if 'lastFollowTimeEnd' in dataJson.keys():
        _must.append(RangeQuery(key="lastFollowTime",
                                _from=None,
                                to=str(dataJson['lastFollowTimeEnd']),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=False,
                                include_upper=True))
    # 逾期天数
    if 'overdueDays' in dataJson.keys():
        _must.append(TermQuery(key="overdueDays", value=str(dataJson['overdueDays'])))
    # 逾期天数范围查询
    if 'overdueDaysStart' in dataJson.keys():
        _must.append(RangeQuery(key="overdueDays",
                                _from=dataJson['overdueDaysStart']))
    if 'overdueDaysEnd' in dataJson.keys():
        _must.append(RangeQuery(key="overdueDays",
                                to=dataJson['overdueDaysEnd']))
    # 下次跟进时间搜索
    if 'operationNextTimeStart' in dataJson.keys():
        _must.append(RangeQuery(key="operationNextTime",
                                _from=str(dataJson['operationNextTimeStart']),
                                to=None,
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=True,
                                include_upper=False))
    if 'operationNextTimeEnd' in dataJson.keys():
        _must.append(RangeQuery(key="operationNextTime",
                                _from=None,
                                to=str(dataJson['operationNextTimeEnd']),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=False,
                                include_upper=False))
    # 分案日期搜索
    if 'divisionTimeStart' in dataJson.keys():
        _must.append(RangeQuery(key="divisionTime",
                                _from=str(dataJson['divisionTimeStart']),
                                to=None,
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=True,
                                include_upper=False))
    if 'divisionTimeEnd' in dataJson.keys():
        _must.append(RangeQuery(key="divisionTime",
                                _from=None,
                                to=str(dataJson['divisionTimeEnd']),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=False,
                                include_upper=True))
    # 结案时间搜索
    if 'endTimeStart' in dataJson.keys():
        _must.append(RangeQuery(key="endTime",
                                _from=str(dataJson['endTimeStart']),
                                to=None,
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=True,
                                include_upper=False))
    if 'endTimeEnd' in dataJson.keys():
        _must.append(RangeQuery(key="endTime",
                                _from=None,
                                to=str(dataJson['endTimeEnd']),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=False,
                                include_upper=True))
    # 委案时间查询
    if 'deltStartStrOn' in dataJson.keys() and 'deltStartStrOff' in dataJson.keys():
        dateFormat = 'yyyy-MM-dd HH:mm:ss'
        if len(str(dataJson['deltStartStrOn'])) == 10:
            dateFormat = 'yyyy-MM-dd'
        _must.append(RangeQuery(key="entrustStartTime",
                                _from=str(dataJson['deltStartStrOn']),
                                to=str(dataJson['deltStartStrOff']),
                                otherMap={"format": dateFormat}))
    if 'deltEndStrOn' in dataJson.keys() and 'deltEndStrOff' in dataJson.keys():
        _must.append(RangeQuery(key="entrustEndTime",
                                _from=str(dataJson['deltEndStrOn']),
                                to=str(dataJson['deltEndStrOff']),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"}))
    # 催收进程查询，支持多选
    if 'operationStates' in dataJson.keys():
        _must.append(TermsQuery(key="operationState", values=dataJson['operationStates'].split(",")))
    # 催收结果状态查询，支持多选
    if 'operStatus' in dataJson.keys():
        _must.append(TermsQuery(key="operStatus", values=dataJson['operStatus'].split(",")))
    # 案件id查询
    if 'caseIds' in dataJson.keys() and len(dataJson['caseIds']) > 0:
        _must.append(TermsQuery(key="id", values=dataJson['caseIds']))
    # 委案批次号查询
    if 'outBatchIds' in dataJson.keys():
        _must.append(TermsQuery(key="outBatchId", values=dataJson['outBatchIds'].split(",")))
    # 内部批次号查询
    if 'innerBatchIds' in dataJson.keys():
        _must.append(TermsQuery(key="innerBatchId", values=dataJson['innerBatchIds'].split(",")))
    # 智能协催电话结果
    if 'autoAssistResult' in dataJson.keys():
        _must.append(TermQuery(key="autoAssistResult", value=dataJson['autoAssistResult']))
    # autoAssistDateStart
    if 'autoAssistDateStart' in dataJson.keys() and 'autoAssistDateEnd' in dataJson.keys():
        _must.append(RangeQuery(key="autoAssistDate",
                                _from=str(dataJson['autoAssistDateStart']),
                                to=str(dataJson['autoAssistDateEnd']),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"}))
    # tag
    if 'tag' in dataJson.keys():
        _must.append(TermQuery(key="tag", value=dataJson['tag']))
    # ignorePlan
    if 'ignorePlan' in dataJson.keys():
        _must.append(TermQuery(key="ignorePlan", value=dataJson['ignorePlan']))
    if 'minAmount' in dataJson.keys():
        _must.append(RangeQuery(key="amount",
                                _from=dataJson['minAmount']))
    if 'maxAmount' in dataJson.keys():
        _must.append(RangeQuery(key="amount",
                                to=dataJson['maxAmount']))
    if 'callTypes' in dataJson.keys():
        _must.append(TermsQuery(key="callType", values=dataJson['callTypes'].split(",")))
    if 'caseTagIds' in dataJson.keys():
        _must.append(TermsQuery(key="caseTagId", values=dataJson['caseTagIds'].split(",")))
    if 'noTag' in dataJson.keys() and dataJson.get("noTag"):
        _must_not.append(ExistsQuery(key="caseTagId"))
    if 'isPlan' in dataJson.keys() and dataJson['isPlan'] == 0:
        _must_not_tmp = ExistsQuery(key="ptpTime")
        _should_tmp = RangeQuery(key="ptpTime",
                                 to=dataJson['ignorePlanTime'],
                                 otherMap={"format": "yyyy-MM-dd HH:mm:ss"})
        _bool_tmp = BoolQuery(must_list=[], must_not_list=_must_not_tmp, should_list=_should_tmp)
        _must.append(_bool_tmp)
    if 'isAgent' in dataJson.keys():
        _must.append(TermQuery(key="isAgent", value=dataJson['isAgent']))
    # 债务人跟进次数查询
    if 'debtFollowCountStart' in dataJson.keys():
        _must.append(RangeQuery(key="debtFollowCount",
                                _from=dataJson['debtFollowCountStart']))
    if 'debtFollowCountEnd' in dataJson.keys():
        _must.append(RangeQuery(key="debtFollowCount",
                                to=dataJson['debtFollowCountEnd']))
    if 'ctrlIds' in dataJson.keys():
        _must.append(TermsQuery(key="ctrlId", values=dataJson['ctrlIds']))
    if 'endType' in dataJson.keys():
        _must.append(TermQuery(key="endType", value=dataJson['endType']))
    if 'endConfigId' in dataJson.keys():
        _must.append(TermQuery(key="endConfigId", value=dataJson['endConfigId']))
    if 'fieldSearch' in dataJson.keys() and len(dataJson['fieldSearch']) > 0:
        for dic in dataJson['fieldSearch']:
            key = dic['key']
            vals = dic['values']
            if vals and len(vals) == 1:
                _must.append(WildcardQuery(key="fieldSearch.keyword", value=key + "#" + vals[0] + "*"))
            elif vals and len(vals) > 1:
                tmp = [key + "#" + val for val in vals]
                _must.append(TermsQuery(key="fieldSearch.keyword", values=tmp))
            else:
                continue
    # 承诺还款信息
    if 'ptpTimeStart' in dataJson.keys() and 'ptpTimeEnd' in dataJson.keys():
        _must.append(RangeQuery(key="ptpTime", _from=str(dataJson['ptpTimeStart']), to=str(dataJson['ptpTimeEnd']),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=True,
                                include_upper=True))
    if 'minPtpAmount' in dataJson.keys():
        _must.append(RangeQuery(key="ptpAmount", _from=int(dataJson['minPtpAmount']),
                                include_lower=True))
    if 'maxPtpAmount' in dataJson.keys():
        _must.append(RangeQuery(key="ptpAmount", to=int(dataJson['maxPtpAmount']),
                                include_upper=True))
    if 'allotAgents' in dataJson.keys():
        _must.append(TermsQuery(key="allotAgent", values=dataJson['allotAgents'].split(",")))
    if 'allotAgent' in dataJson.keys():
        _must.append(TermQuery(key="allotAgent", value=dataJson['allotAgent']))
    if 'allotAgentState' in dataJson.keys():
        _must.append(TermQuery(key="allotAgentState", value=dataJson['allotAgentState']))
    if 'wayAllotState' in dataJson.keys():
        _must.append(TermQuery(key="wayAllotState", value=dataJson['wayAllotState']))
    if 'operationWays' in dataJson.keys():
        _must.append(TermsQuery(key="operationWay", values=dataJson['operationWays'].split(",")))
    if 'operationWayNot' in dataJson.keys():
        _must_not.append(TermsQuery(key="operationWay", values=[int(dataJson['operationWayNot'])]))
    if 'important' in dataJson.keys():
        _must.append(TermQuery(key="important", value=dataJson['important']))
    if 'recycleFlag' in dataJson.keys():
        _must.append(TermQuery(key="recycleFlag", value=dataJson['recycleFlag']))
    if 'preDepId' in dataJson.keys():
        _must.append(TermQuery(key="preDepId", value=dataJson['preDepId']))
    if 'preDepIds' in dataJson.keys():
        _must.append(TermsQuery(key="preDepId", values=dataJson['preDepIds'].split(",")))
    if 'caseListType' in dataJson.keys():
        if dataJson['caseListType'] == 1:
            _must.append(TermQuery(key="allotAgentState", value=0))
        if dataJson['caseListType'] == 2:
            _must.append(TermQuery(key="allotAgentState", value=1))
            _must.append(TermsQuery(key="allotStatus", values=[1, 2]))
        if dataJson['caseListType'] == 3:
            _must.append(TermQuery(key="wayAllotState", value=1))
            # 电催：分案完成、留案的案件
            if 'operationWays' not in dataJson.keys() or dataJson['operationWays'] == "1":
                _must.append(TermQuery(key="allotStatus", value=3))
                _must.append(TermsQuery(key="caseStatus", values=[0, 1]))
        if dataJson['caseListType'] == 4:
            _must.append(TermQuery(key="recycleFlag", value=1))
        if dataJson['caseListType'] == 5:
            _must.append(TermQuery(key="caseStatus", value=3))
        if dataJson['caseListType'] == 6:
            _must.append(TermQuery(key="important", value=1))
        if dataJson['caseListType'] == 7:
            _must.append(TermQuery(key="caseStatus", value=2))
    if 'wayUpdateDateStart' in dataJson.keys() and 'wayUpdateDateEnd' in dataJson.keys():
        _must.append(RangeQuery(key="wayUpdateDate", _from=str(dataJson['wayUpdateDateStart']), to=str(dataJson['wayUpdateDateEnd']),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=True,
                                include_upper=True))
    if 'stopDateStart' in dataJson.keys() and 'stopDateEnd' in dataJson.keys():
        _must.append(RangeQuery(key="stopDate", _from=str(dataJson['stopDateStart']), to=str(dataJson['stopDateEnd']),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=True,
                                include_upper=True))
    if 'recycleDateStart' in dataJson.keys() and 'recycleDateEnd' in dataJson.keys():
        _must.append(RangeQuery(key="recycleDate", _from=str(dataJson['recycleDateStart']), to=str(dataJson['recycleDateEnd']),
                                otherMap={"format": "yyyy-MM-dd HH:mm:ss"},
                                include_lower=True,
                                include_upper=True))
    if 'productTypes' in dataJson.keys():
        _must.append(TermsQuery(key="productType", values=dataJson['productTypes'].split(",")))
    if 'minOutsourceCount' in dataJson.keys():
        _must.append(RangeQuery(key="outsourceCount", _from=int(dataJson['minOutsourceCount']),
                                include_lower=True))
    if 'maxOutsourceCount' in dataJson.keys():
        _must.append(RangeQuery(key="outsourceCount", to=int(dataJson['maxOutsourceCount']),
                                include_upper=True))
    if 'isVisit' in dataJson.keys():
        _must.append(TermQuery(key="isVisit", value=dataJson['isVisit']))
    if 'visitorIds' in dataJson.keys():
        _must.append(TermsQuery(key="visitorId", values=dataJson['visitorIds']))

    orderSql = ""
    if dataJson['sortRule'] == 0 and dataJson['orderBy'] == 0:
        _sort_list.append(Sort(key="createTime", order="desc"))
        _sort_list.append(Sort(key="id", order="desc"))
        orderSql += " order by ca.create_time desc,ca.id desc "
    if 'orderBy' in dataJson.keys() and dataJson['sortRule'] != 0:
        orderSql += " order by "
        if dataJson['sortRule'] == 1:
            if dataJson['orderBy'] == 0:
                _sort_list.append(Sort(key="updateTime", order="asc"))
                orderSql += " ca.update_time asc "
            if dataJson['orderBy'] == 1:
                _sort_list.append(Sort(key="amount", order="asc"))
                orderSql += " ca.amount asc "
            if dataJson['orderBy'] == 2:
                _sort_list.append(Sort(key="overdueDate", order="asc"))
                orderSql += " ca.overdue_date asc "
            if dataJson['orderBy'] == 3:
                _sort_list.append(Sort(key="lastFollowTime", order="asc"))
                orderSql += " ca.last_follow_time asc "
            if dataJson['orderBy'] == 4:
                _sort_list.append(Sort(key="followCount", order="asc"))
                orderSql += " ca.follow_count asc "
            if dataJson['orderBy'] == 5:
                _sort_list.append(Sort(key="payAmount", order="asc"))
                orderSql += " ca.pay_amount asc "
            if dataJson['orderBy'] == 6:
                _sort_list.append(Sort(key="entrustEndTime", order="asc"))
                orderSql += " ca.entrust_end_time asc "
            if dataJson['orderBy'] == 7:
                _sort_list.append(Sort(key="name", order="asc"))
                _sort_list.append(Sort(key="idCard", order="asc"))
                orderSql += " ca.name asc, ca.id_card "
            if dataJson['orderBy'] == 8:
                _sort_list.append(Sort(key="divisionTime", order="asc"))
                orderSql += " ca.division_time asc "
            if dataJson['orderBy'] == 9:
                _sort_list.append(Sort(key="overdueDays", order="asc"))
                orderSql += " ca.overdue_days asc "
            if dataJson['orderBy'] == 10:
                _sort_list.append(Sort(key="debtFollowCount", order="asc"))
                orderSql += " cador.follow_count asc "
            if dataJson['orderBy'] == 11:
                _sort_list.append(Sort(key="debtLastFollowTime", order="asc"))
                orderSql += " cador.last_follow_time asc "
            if dataJson['orderBy'] == 12:
                _sort_list.append(Sort(key="createTime", order="asc"))
                orderSql += " ca.create_time asc "
            orderSql += ",ca.id asc "
            _sort_list.append(Sort(key="id", order="asc"))
        elif dataJson['sortRule'] == 2:
            if dataJson['orderBy'] == 0:
                _sort_list.append(Sort(key="updateTime", order="desc"))
                orderSql += " ca.update_time desc "
            if dataJson['orderBy'] == 1:
                _sort_list.append(Sort(key="amount", order="desc"))
                orderSql += " ca.amount desc "
            if dataJson['orderBy'] == 2:
                _sort_list.append(Sort(key="overdueDate", order="desc"))
                orderSql += " ca.overdue_date desc "
            if dataJson['orderBy'] == 3:
                _sort_list.append(Sort(key="lastFollowTime", order="desc"))
                orderSql += " ca.last_follow_time desc "
            if dataJson['orderBy'] == 4:
                _sort_list.append(Sort(key="followCount", order="desc"))
                orderSql += " ca.follow_count desc "
            if dataJson['orderBy'] == 5:
                _sort_list.append(Sort(key="payAmount", order="desc"))
                orderSql += " ca.pay_amount desc "
            if dataJson['orderBy'] == 6:
                _sort_list.append(Sort(key="entrustEndTime", order="desc"))
                orderSql += " ca.entrust_end_time desc "
            if dataJson['orderBy'] == 7:
                _sort_list.append(Sort(key="name", order="desc"))
                _sort_list.append(Sort(key="idCard", order="desc"))
                orderSql += " ca.name desc, ca.id_card "
            if dataJson['orderBy'] == 8:
                _sort_list.append(Sort(key="divisionTime", order="desc"))
                orderSql += " ca.division_time desc "
            if dataJson['orderBy'] == 9:
                _sort_list.append(Sort(key="overdueDays", order="desc"))
                orderSql += " ca.overdue_days desc "
            if dataJson['orderBy'] == 10:
                _sort_list.append(Sort(key="debtFollowCount", order="desc"))
                orderSql += " cador.follow_count desc "
            if dataJson['orderBy'] == 11:
                _sort_list.append(Sort(key="debtLastFollowTime", order="desc"))
                orderSql += " cador.last_follow_time desc "
            if dataJson['orderBy'] == 12:
                _sort_list.append(Sort(key="createTime", order="desc"))
                orderSql += " ca.create_time desc "
            orderSql += ",ca.id desc "
            _sort_list.append(Sort(key="id", order="desc"))
    _bool = BoolQuery(must_list=_must, should_list=_should, must_not_list=_must_not)
    _search = Search(query=_bool, order_list=_sort_list, limit=pageSize)
    order["value"] = orderSql
    es = Elasticsearch([{'host': config["anmi_es"]["host"], 'port': config["anmi_es"]["port"]}],
                       timeout=3600,
                       http_auth=(config["anmi_es"]["user"], config["anmi_es"]["password"]))
    logger.info("开始es搜索")
    logger.info(json.JSONEncoder().encode(o=_search.toMap()))
    # 根据条件查询es
    _result = es.search(index="bpw_case_info_query", body=_search.toMap(), _source_includes=["_id"], scroll="5m")
    if _result is None or _result["timed_out"]:
        raise RuntimeError("查询错误")

    if _result["hits"]["total"]["value"] is None or _result["hits"]["total"]["value"] == 0:
        raise RuntimeError("查询案件信息为空")
    total["value"] = _result["hits"]["total"]["value"]

    scroll_id = _result["_scroll_id"]
    while len(_result["hits"]["hits"]) != 0:
        case_ids = []
        for case_info in _result["hits"]["hits"]:
            case_ids.append(int(case_info["_id"]))
        yield case_ids
        _result = es.scroll(scroll_id=scroll_id, scroll='5m')
        if _result is None or _result["timed_out"]:
            raise RuntimeError("查询错误")


def get_case_ids_from_mysql(orgId, dataJson, order, total):
    whereSql = " where ca.recovery = 0 and ca.org_id=" + str(orgId)
    # 委案公司多选
    if dataJson.get('deltIds'):
        whereSql += " and ca.org_delt_id in (" + dataJson['deltIds'] + ") "
    # 案件颜色
    if dataJson.get('color'):
        whereSql += " and ca.color = '" + str(dataJson['color']) + "'"
    # 债务人姓名
    if dataJson.get('names'):
        namesStr = str(dataJson['names']).strip('[').strip(']')
        whereSql += " and ca.name in (" + namesStr + ") "
    # 债务人身份证
    if dataJson.get('idCards'):
        idCardsStr = str(dataJson['idCards']).strip('[').strip(']')
        whereSql += " and ca.id_card in (" + idCardsStr + ") "
    # 债务人手机号
    if dataJson.get('mobiles'):
        mobilesStr = str(dataJson['mobiles']).strip('[').strip(']')
        whereSql += " and ca.own_mobile in (" + mobilesStr + ") "
    # 债务人委案编号
    if dataJson.get('outSerialNos'):
        outSerialNosStr = str(dataJson['outSerialNos']).strip('[').strip(']')
        length = len(dataJson['outSerialNos'])
        if length > 1:
            whereSql += " and ca.out_serial_temp in (" + outSerialNosStr + ") "
        elif length == 1:
            whereSql += " and ca.out_serial_temp like '" + dataJson['outSerialNos'][0] + "%' "
    # 自定义标签 searchKeyParam是一个字典
    searchKeyParam = dataJson.get('searchKeyParam')
    if searchKeyParam and len(searchKeyParam) > 0:
        for key in searchKeyParam:
            whereSql += " and ("
            vals = searchKeyParam[key]
            insideIndex = 1
            for val in vals:
                if insideIndex == len(vals):
                    whereSql += " JSON_EXTRACT(ca.field_json, concat('$.','" + key + "')) = '" + val + "'"
                elif insideIndex < len(vals):
                    whereSql += " JSON_EXTRACT(ca.field_json, concat('$.','" + key + "')) = '" + val + "' or "
                insideIndex += 1
            whereSql += ") "
    # 委案时间查询
    if dataJson.get('deltStart') and dataJson.get('deltEnd'):
        whereSql += " and ca.entrust_start_time >= '%s' and ca.entrust_start_time < '%s'" % (str(dataJson['deltStart']), str(dataJson['deltEnd']))
    # 产品编号查询，支持多选
    if dataJson.get('products'):
        whereSql += " and ca.product_id in (" + dataJson['products'] + ") "
    # 催员ID查询，支持多选
    if dataJson.get('userIds'):
        whereSql += " and ca.user_id in (" + dataJson['userIds'] + ") "
    # 团队查询
    if dataJson.get('teamIds'):
        whereSql += " and ca.team_id in (" + dataJson['teamIds'] + ") "
    if dataJson.get('depIds'):
        whereSql += " and ca.dep_id in (" + dataJson['depIds'] + ") "
    if dataJson.get('teamId'):
        whereSql += " and ca.team_id = " + str(dataJson['teamId'])
    if dataJson.get('depId'):
        whereSql += " and ca.dep_id = " + str(dataJson['depId'])
    # 催收状态查询，支持多选
    if dataJson.get('actionTypes'):
        whereSql += " and ca.oper_status in (" + dataJson['actionTypes'] + ") "
    # 协催结果查询，支持多选
    if dataJson.get('callStatuses'):
        whereSql += " and ca.call_status in (" + dataJson['callStatuses'] + ") "
    # action判断
    if dataJson.get('action'):
        if dataJson['action'] == 1 or dataJson['action'] == 6 or dataJson['action'] == 9:
            whereSql += "  and ca.allot_status=3 and ca.case_status in (0,1) "
        if dataJson['action'] == 2:
            whereSql += " and ca.allot_status = 0 "
        if dataJson['action'] == 3:
            whereSql += " and ca.allot_status = 3 "
        if dataJson['action'] == 4:
            whereSql += " and ca.case_status = 4 "
        if dataJson['action'] == 5:
            whereSql += " and ca.case_status = 3 "
        if dataJson['action'] == 7:
            whereSql += " and ca.allot_status =2 "
        if dataJson['action'] == 8:
            whereSql += " and ca.allot_status = 1 "
    # status查询
    if dataJson.get('caseStatuses'):
        whereSql += " and ca.case_status in (" + dataJson['caseStatuses'] + ") "
    if dataJson.get('notInCaseStatuses'):
        whereSql += " and ca.case_status not in (" + dataJson['notInCaseStatuses'] + ") "
    if dataJson.get('allotStatues'):
        whereSql += " and ca.allot_status in (" + dataJson['allotStatues'] + ") "
    if dataJson.get('notInAllotStatues'):
        whereSql += " and ca.allot_status not in (" + dataJson['notInAllotStatues'] + ") "
    # 逾期日期搜索
    if dataJson.get('overdueDateStart'):
        whereSql += " and ca.overdue_date >= '%s'" % (str(dataJson['overdueDateStart']))
    if dataJson.get('overdueDateEnd'):
        whereSql += " and ca.overdue_date < '%s'" % (str(dataJson['overdueDateEnd']))
    # 上次跟进时间搜索
    if dataJson.get('lastFollowTimeStart'):
        whereSql += " and ca.last_follow_time >= '%s' " % str(dataJson['lastFollowTimeStart'])
    if dataJson.get('lastFollowTimeEnd'):
        whereSql += " and ca.last_follow_time <  '%s' " % str(dataJson['lastFollowTimeEnd'])
    # 逾期天数
    if dataJson.get('overdueDays'):
        whereSql += " and ca.overdue_days = " + str(dataJson['overdueDays'])
    # 逾期天数范围查询
    if dataJson.get('overdueDaysStart'):
        whereSql += " and ca.overdue_days >= " + str(dataJson['overdueDaysStart'])
    if dataJson.get('overdueDaysEnd'):
        whereSql += " and ca.overdue_days <= " + str(dataJson['overdueDaysEnd'])
    # 下次跟进时间搜索
    if dataJson.get('operationNextTimeStart') and dataJson.get('operationNextTimeEnd'):
        whereSql += " and ca.operation_next_time >= '%s' and ca.operation_next_time < '%s'" % (str(dataJson['operationNextTimeStart']), str(dataJson['operationNextTimeEnd']))
    # 分案日期搜索
    if dataJson.get('divisionTimeStart') and dataJson.get('divisionTimeEnd'):
        whereSql += " and ca.division_time >= '%s' and ca.division_time <= '%s'" % (str(dataJson['divisionTimeStart']), str(dataJson['divisionTimeEnd']))
    # 结案时间搜索
    if dataJson.get('endTimeStart') and dataJson.get('endTimeEnd'):
        whereSql += " and ca.end_time >= '%s' and ca.end_time <= '%s'" % (str(dataJson['endTimeStart']), str(dataJson['endTimeEnd']))
    # 委案时间查询
    if dataJson.get('deltStartStrOn') and dataJson.get('deltStartStrOff'):
        whereSql += " and ca.entrust_start_time >= '%s' and ca.entrust_start_time <= '%s'" % (str(dataJson['deltStartStrOn']), str(dataJson['deltStartStrOff']))
    if dataJson.get('deltEndStrOn') and dataJson.get('deltEndStrOff'):
        whereSql += " and ca.entrust_end_time >= '%s' and ca.entrust_end_time <= '%s'" % (str(dataJson['deltEndStrOn']), str(dataJson['deltEndStrOff']))
    # 催收进程查询，支持多选
    if dataJson.get('operationStates'):
        whereSql += " and ca.operation_state in (" + dataJson['operationStates'] + ") "
    # 催收结果状态查询，支持多选
    if dataJson.get('operStatus'):
        whereSql += " and ca.oper_status in (" + dataJson['operStatus'] + ") "
    # 案件id查询
    if dataJson.get('caseIds'):
        caseIdsStr = str(dataJson['caseIds']).strip('[').strip(']')
        whereSql += " and ca.id in (" + caseIdsStr + ") "
    # 委案批次号查询
    if dataJson.get('outBatchIds'):
        whereSql += " and ca.out_batch_id in (" + dataJson['outBatchIds'] + ") "
    # 内部批次号查询
    if dataJson.get('innerBatchIds'):
        whereSql += " and ca.inner_batch_id in (" + dataJson['innerBatchIds'] + ") "
    # 智能协催电话结果
    if dataJson.get('autoAssistResult'):
        whereSql += " and ca.auto_assist_result = '%s'" % dataJson['autoAssistResult']
    # autoAssistDateStart
    if dataJson.get('autoAssistDateStart') and dataJson.get('autoAssistDateEnd'):
        whereSql += " and ca.auto_assist_date >= '%s' and ca.auto_assist_date <= '%s'" % (str(dataJson['autoAssistDateStart']), str(dataJson['autoAssistDateEnd']))
    # tag
    if dataJson.get('tag'):
        whereSql += " and ca.tag = '%s'" % dataJson['tag']
    # ignorePlan
    if dataJson.get('ignorePlan'):
        whereSql += " and ca.ignore_plan = " + dataJson['ignorePlan']
    # 委案金额
    if dataJson.get('minAmount'):
        whereSql += " and ca.amount >= %d" % dataJson['minAmount']
    if dataJson.get('maxAmount'):
        whereSql += " and ca.amount <= %d" % dataJson['maxAmount']
    # 电话结果多选
    if dataJson.get('callTypes'):
        whereSql += " and ca.call_type in (" + dataJson['callTypes'] + ") "
    # 标签多选
    if dataJson.get('caseTagIds'):
        # whereSql += " and ca.case_tag_id in (" + dataJson['caseTagIds'] + ") "
        whereSql += " and ca.id in(select case_id from case_tag_rel ctr where ctr.tag_id in (" + dataJson['caseTagIds'] + "))"
    if dataJson.get('noTag') and dataJson.get('noTag'):
        whereSql += "  and not exists(select 1 from case_tag_rel ctr where ca.org_id=ctr.org_id and ca.id=ctr.case_id) "
    # 委外机构
    if dataJson.get('isAgent'):
        whereSql += " and ifnull(ca.dep_id,ca.team_id) in(select id from org_dep_team where is_agent = %s)" % dataJson['isAgent']
    if dataJson.get('debtFollowCountStart'):
        whereSql += " and ifnull(cador.follow_count,ca.follow_count) >= " + str(dataJson['debtFollowCountStart'])
    if dataJson.get('debtFollowCountEnd'):
        whereSql += " and ifnull(cador.follow_count,ca.follow_count) <= " + str(dataJson['debtFollowCountEnd'])
    if dataJson.get('ctrlIds'):
        whereSql += " and ca.ctrl_id in (" + ",".join(dataJson['ctrlIds']) + ") "
    if dataJson.get('endType'):
        whereSql += " and ca.end_type =" + str(dataJson['endType'])
    if dataJson.get('endConfigId'):
        whereSql += " and ca.end_config_id =" + str(dataJson['endConfigId'])
    if 'fieldSearch' in dataJson.keys() and len(dataJson['fieldSearch']) > 0:
        for dic in dataJson['fieldSearch']:
            key = dic['key']
            vals = dic['values']
            if vals and len(vals) == 1:
                whereSql += f" and ca.id in(select case_id from case_info_field where field_key='{key}' and field_value like '{vals[0]}%')"
            elif vals and len(vals) > 1:
                tmp = ",".join(["'" + val + "'" for val in vals])
                whereSql += f" and ca.id in(select case_id from case_info_field where field_key='{key}' and field_value in ({tmp}))"
            else:
                continue
    # 承诺还款信息
    if dataJson.get('ptpTimeStart') is not None and dataJson.get('ptpTimeEnd') is not None:
        whereSql += " and ca.ptp_time >= '%s' and ca.ptp_time <= '%s'" % (str(dataJson['ptpTimeStart']), str(dataJson['ptpTimeEnd']))
    if dataJson.get('minPtpAmount') is not None:
        whereSql += " and ca.ptp_amount >= %d" % int(dataJson['minPtpAmount'])
    if dataJson.get('maxPtpAmount') is not None:
        whereSql += " and ca.ptp_amount <= %d" % int(dataJson['maxPtpAmount'])
    if dataJson.get('allotAgents'):
        whereSql += " and ca.allot_agent in (" + dataJson['allotAgents'] + ")"
    if dataJson.get('allotAgent'):
        whereSql += " and ca.allot_agent = " + int(dataJson['allotAgent'])
    if dataJson.get('allotAgentState'):
        whereSql += " and ca.allot_agent_state = " + int(dataJson['allotAgentState'])
    if dataJson.get('wayAllotState'):
        whereSql += " and ca.way_allot_state = " + int(dataJson['wayAllotState'])
    if dataJson.get('operationWays') or dataJson.get('operationWayNot'):
        whereSql += " and ("
        if dataJson.get('operationWayNot'):
            whereSql += " ca.way_allot_state = 0 or"
        whereSql += " EXISTS (select 1 from case_operation_way_rel cowr where ca.id=cowr.case_id"
        if dataJson.get('operationWays'):
            whereSql += " and cowr.operation_way in (" + dataJson['operationWays'] + ")"
        if dataJson.get('operationWayNot'):
            whereSql += " and cowr.operation_way !=  " + int(dataJson['operationWayNot'])
        whereSql += " ))"
    if dataJson.get('important'):
        whereSql += " and ca.important = " + int(dataJson['important'])
    if dataJson.get('recycleFlag'):
        whereSql += " and ca.recycle_flag = " + int(dataJson['recycleFlag'])
    if dataJson.get('preDepId'):
        whereSql += " and ca.pre_dep_id = " + int(dataJson['preDepId'])
    if dataJson.get('preDepIds'):
        whereSql += " and ca.pre_dep_id in (" + dataJson['preDepIds'] + ") "
    if dataJson.get('caseListType'):
        if dataJson['caseListType'] == 1:
            whereSql += " and ca.allot_agent_state = 0"
        if dataJson['caseListType'] == 2:
            whereSql += " and ca.allot_agent_state = 1 and ca.allot_status in (1,2) "
        if dataJson['caseListType'] == 3:
            whereSql += " and ca.way_allot_state = 1"
            # 电催：分案完成、留案的案件
            if 'operationWays' not in dataJson.keys() or dataJson['operationWays'] == "1":
                whereSql += " and ca.allot_status = 3 and ca.case_status in (0,1)"
        if dataJson['caseListType'] == 4:
            whereSql += " and ca.recycle_flag = 1"
        if dataJson['caseListType'] == 5:
            whereSql += " and ca.case_status = 3 "
        if dataJson['caseListType'] == 6:
            whereSql += " and ca.important = 1"
        if dataJson['caseListType'] == 7:
            whereSql += " and ca.case_status = 2"
    if dataJson.get('wayUpdateDateStart') is not None and dataJson.get('wayUpdateDateEnd') is not None:
        whereSql += " and ca.way_update_date >= '%s' and ca.way_update_date <= '%s'" % (str(dataJson['wayUpdateDateStart']), str(dataJson['wayUpdateDateEnd']))
    if dataJson.get('stopDateStart') is not None and dataJson.get('stopDateEnd') is not None:
        whereSql += " and ca.stop_date >= '%s' and ca.stop_date <= '%s'" % (str(dataJson['stopDateStart']), str(dataJson['stopDateEnd']))
    if dataJson.get('recycleDateStart') is not None and dataJson.get('recycleDateEnd') is not None:
        whereSql += " and ca.recycle_date >= '%s' and ca.recycle_date <= '%s'" % (str(dataJson['recycleDateStart']), str(dataJson['recycleDateEnd']))
    if dataJson.get('productTypes'):
        whereSql += " and p.type in (" + dataJson['productTypes'] + ")"
    if dataJson.get('minOutsourceCount') is not None:
        whereSql += " and ca.outsource_count >= %d" % int(dataJson['minOutsourceCount'])
    if dataJson.get('maxOutsourceCount') is not None:
        whereSql += " and ca.outsource_count <= %d" % int(dataJson['maxOutsourceCount'])
    if dataJson.get('visitorIds'):
        whereSql += " and ca.visitor_id in (" + ",".join(dataJson['visitorIds']) + ") "
    if dataJson.get('isVisit'):
        whereSql += "and ca.is_visit = " + dataJson['isVisit']

    orderSql = ""
    # 排序规则
    if dataJson.get('sortRule') == 0 and dataJson.get('orderBy') == 0:
        orderSql += " order by ca.id desc "
    if dataJson.get('orderBy') and dataJson.get('sortRule') != 0:
        orderSql += " order by "
        if dataJson['sortRule'] == 1:
            if dataJson['orderBy'] == 0:
                orderSql += " ca.update_time asc "
            if dataJson['orderBy'] == 1:
                orderSql += " ca.amount asc "
            if dataJson['orderBy'] == 2:
                orderSql += " ca.overdue_date asc "
            if dataJson['orderBy'] == 3:
                orderSql += " ca.last_follow_time asc "
            if dataJson['orderBy'] == 4:
                orderSql += " ca.follow_count asc "
            if dataJson['orderBy'] == 5:
                orderSql += " ca.pay_amount asc "
            if dataJson['orderBy'] == 6:
                orderSql += " ca.entrust_end_time asc "
            if dataJson['orderBy'] == 7:
                orderSql += " ca.name asc, ca.id_card "
            if dataJson['orderBy'] == 8:
                orderSql += " ca.division_time asc "
            if dataJson['orderBy'] == 9:
                orderSql += " ca.overdue_days asc "
            if dataJson['orderBy'] == 10:
                orderSql += " cador.follow_count asc "
            if dataJson['orderBy'] == 11:
                orderSql += " cador.last_follow_time asc "
            if dataJson['orderBy'] == 12:
                orderSql += " ca.id asc "
            # 如果排序字段不是id，那么在当前排序字段后添加id逆序，解决排序字段值相同导致随机顺序返回结果分页查询错误问题
            if dataJson['orderBy'] != 12:
                orderSql += ",ca.id desc "
        elif dataJson['sortRule'] == 2:
            if dataJson['orderBy'] == 0:
                orderSql += " ca.update_time desc "
            if dataJson['orderBy'] == 1:
                orderSql += " ca.amount desc "
            if dataJson['orderBy'] == 2:
                orderSql += " ca.overdue_date desc "
            if dataJson['orderBy'] == 3:
                orderSql += " ca.last_follow_time desc "
            if dataJson['orderBy'] == 4:
                orderSql += " ca.follow_count desc "
            if dataJson['orderBy'] == 5:
                orderSql += " ca.pay_amount desc "
            if dataJson['orderBy'] == 6:
                orderSql += " ca.entrust_end_time desc "
            if dataJson['orderBy'] == 7:
                orderSql += " ca.name desc, ca.id_card "
            if dataJson['orderBy'] == 8:
                orderSql += " ca.division_time desc "
            if dataJson['orderBy'] == 9:
                orderSql += " ca.overdue_days desc "
            if dataJson['orderBy'] == 10:
                orderSql += " cador.follow_count desc "
            if dataJson['orderBy'] == 11:
                orderSql += " cador.last_follow_time desc "
            if dataJson['orderBy'] == 12:
                orderSql += " ca.id desc "
            # 如果排序字段不是id，那么在当前排序字段后添加id逆序，解决排序字段值相同导致随机顺序返回结果分页查询错误问题
            if dataJson['orderBy'] != 12:
                orderSql += ",ca.id desc "
    countSql = "select count(0) as cnt " + caseFromSql + leftJoinBaseSql + whereSql
    logger.info(countSql)
    count = mysql_pool.select_one(countSql)["cnt"]
    order["value"] = orderSql
    whereSql += orderSql
    total["value"] = count
    pages = int(count / pageSize) if count % pageSize == 0 else int(count / pageSize) + 1
    for page in range(0, pages):
        limitSql = "select ca.id " + caseFromSql + leftJoinBaseSql + whereSql + f" limit  {page * pageSize}, {pageSize}"
        logger.info(limitSql)
        case_ids = []
        caseList = mysql_pool.select(limitSql)
        for caseInfo in caseList:
            case_ids.append(caseInfo["id"])
        yield case_ids


def export_excel(taskId, caseEntity, dataJson, order, language, caseIdsList, total, operStatusDict, operStateDict, containsCaseNote):
    page = 1
    case_num = {"value": 1}
    repay_num = {"value": 1}
    reduce_number = {"value": 1}
    contact_number = {"value": 1}
    case_file_number = {"value": 1}
    # 是否导出还款信息
    hasRepayment = dataJson.get('hasRepayment')
    # 是否导出减免信息
    hasReduction = dataJson.get('hasReduction')
    # 是否导出联系人
    hasContacts = dataJson.get('hasContacts')
    # 是否导出案件标签
    hasTag = dataJson.get('hasTag')
    # 是否导出补充材料文件
    hasCaseFile = dataJson.get('hasCaseFile')
    isSeparate = dataJson.get("isSeparate") == 1
    wbs = {}
    deltNames = {}
    # 添加案件标签表头
    if hasTag:
        caseTagTemp = {str(len(caseEntity)): caseTagDict}
        caseEntity.update(caseTagTemp)
    # 添加案件催收手段表头
    operationWayTemp = {str(len(caseEntity)): operationWayDict}
    caseEntity.update(operationWayTemp)

    tmpFilePath = f"./case_export_{taskId}/"
    caseSql = "select " + caseBaseSql + caseFromSql + leftJoinOtherBaseSql + " where ca.id in %s "
    repaySql = "select " + caseBaseSql + repaymentBaseSql + repaymentFromSql + leftJoinOtherBaseSql \
               + " where ca.id in %s and rep.apply_status in (1,2) and rep.status = 0 "
    reduceSql = "select " + caseBaseSql + reductionBaseSql + reductionFromSql + leftJoinOtherBaseSql \
                + " where ca.id in %s and re.status = 1 "
    for caseIds in caseIdsList:
        orderSql = order.get("value")
        limitSql = caseSql + orderSql
        logger.info("我看看: " + limitSql)
        write_to_excel_sheet(wbs, deltNames, "案件信息", caseEntity, limitSql, caseIds,
                             line=case_num, languageType=language, operStatusDict=operStatusDict, operStateDict=operStateDict, isSeparate=isSeparate, containsCaseNote=containsCaseNote, containsCaseTag=hasTag)
        if hasRepayment:
            limitSqlRepay = repaySql + orderSql
            write_to_excel_sheet(wbs, deltNames, "还款记录", getRepaymentEntity(caseEntity), limitSqlRepay, caseIds,
                                 line=repay_num, languageType=language, operStatusDict=operStatusDict, operStateDict=operStateDict, isSeparate=isSeparate, containsCaseNote=containsCaseNote, containsCaseTag=hasTag)
        if hasReduction:
            limitSqlReduce = reduceSql + orderSql
            write_to_excel_sheet(wbs, deltNames, "减免记录", getReductionEntity(caseEntity), limitSqlReduce, caseIds,
                                 line=reduce_number, languageType=language, operStatusDict=operStatusDict, operStateDict=operStateDict, isSeparate=isSeparate, containsCaseNote=containsCaseNote, containsCaseTag=hasTag)
        if hasContacts:
            write_to_excel_sheet(wbs, deltNames, "联系人", getLanguageDictEntity(language), contactSql, caseIds,
                                 line=contact_number, languageType=language, operStatusDict=operStatusDict, operStateDict=operStateDict, isSeparate=isSeparate, containsCaseNote=containsCaseNote, containsCaseTag=hasTag)
        if hasCaseFile:
            write_to_excel_sheet(wbs, deltNames, "补充材料文件", caseFileEntityDict, caseFileSql, caseIds,
                                 line=case_file_number, languageType=language, operStatusDict=operStatusDict, operStateDict=operStateDict, isSeparate=isSeparate, containsCaseNote=containsCaseNote, containsCaseTag=hasTag)
            if isSeparate:
                saveCaseFile(caseFileSql, caseIds, tmpFilePath, 1, deltNames)
            else:
                saveCaseFile(caseFileSql, caseIds, tmpFilePath, 0, None)
        progress = page * pageSize / total.get("value") * 100
        taskUtil.updateProgress(taskId, progress)
        page += 1
    doSave(wbs, deltNames, taskId, hasCaseFile, tmpFilePath, case_num, isSeparate)


def doSave(wbs, deltNames, taskId, hasCaseFile, tmpFilePath, case_num, isSeparate=False):
    # 保存本地文件
    fileName = excelName = f"案件数据导出_{taskId}.xlsx"
    if isSeparate or hasCaseFile:
        fileName = f"案件导出_{taskId}.zip"
        if isSeparate:
            for org_delt_id in wbs:
                excelName = deltNames[org_delt_id] + ".xlsx"
                wb = wbs[org_delt_id]
                wb.save(excelName)
                # excel文件的临时目录 = 公共临时目录 + 任务id（分开不同任务）+ 任务id（按需求要求excel文件放到以任务id命名的文件夹里面，和录音文件同级）
                excelPath = tmpFilePath + str(taskId) + "/"
                fileUtil.move(excelName, excelPath)
        else:
            if not os.path.exists(tmpFilePath):
                os.makedirs(tmpFilePath)
            wbs[-1].save(tmpFilePath + excelName)
        fileUtil.zip(fileName, tmpFilePath)
        fileUtil.delete(tmpFilePath)
        logger.info("删除案件导出临时Excel文件成功: " + tmpFilePath)
    else:
        wbs[-1].save(excelName)
    downloadUrl, fileSize = base_utils.upload_file(ossSwitch, bucket, fileUtil, fileName)
    # 更新数据库
    taskUtil.updateSuccess(taskId, downloadUrl, case_num["value"] - 1)


def saveCaseFile(sql, args, tmpFilePath, isSeparate, deltNames):
    resultRows = mysql_pool.select_by_param(sql, [args])
    for j in range(len(resultRows)):
        resultRow = resultRows[j]
        url = resultRow["file_url"]
        fileName = resultRow["new_file_name"]
        if encryptSwitch:
            resultRow["case_name"] = encrypt_utils.decrypt(encryptMode, resultRow["case_name"])
        # 按委案公司分类存放
        if isSeparate == 1:
            orgDeltId = resultRow.get("org_delt_id")
            deltName = deltNames.get(orgDeltId)
            folderName = tmpFilePath + deltName + "/" + str(resultRow["out_serial_temp"]) + "-" + resultRow["case_name"] + "/"
        else:
            folderName = tmpFilePath + str(resultRow["out_serial_temp"]) + "-" + resultRow["case_name"] + "/"
        os.makedirs(name=folderName, exist_ok=True)
        tmpName = folderName + fileName
        urlFile = requests.get(url)
        if urlFile.status_code == 200:
            open(tmpName, 'wb').write(urlFile.content)


def write_to_excel_sheet(wbs, deltNames, sheetName, excelEntity, sql, args, line, languageType, operStatusDict, operStateDict, isSeparate, containsCaseNote, containsCaseTag):
    # 减免标识
    reduceKey = -1
    # 催收备注
    operationDescKey = -1
    # 判断是否存在
    if sheetName == '案件信息' or sheetName == '还款记录' or sheetName == '减免记录':
        for index in range(len(excelEntity)):
            if excelEntity[str(index)]["value"] == "reduction_total":
                reduceKey = index
                break
        for index in range(len(excelEntity)):
            if excelEntity[str(index)]["value"] == "operation_desc":
                operationDescKey = index
                break

    jsonNote = json.loads('{}')
    if containsCaseNote:
        logger.info(args)
        tmpNoteRows = mysql_pool.select_by_param(caseNoteSql, [args])
        for row in tmpNoteRows or []:
            if jsonNote.get(row["case_id"]) is None:
                jsonNote[row["case_id"]] = row["case_note"]
            else:
                jsonNote[row["case_id"]] = jsonNote[row["case_id"]] + "│" + row["case_note"]

    jsonReduce = json.loads('{}')
    if reduceKey != -1:
        logger.info("查询减免金额:" + reductionSumSql)
        tmpReduceRows = mysql_pool.select_by_param(reductionSumSql, [args])
        for row in tmpReduceRows or []:
            jsonReduce[row["case_id"]] = row["reduction_total"]

    jsonOperation = json.loads('{}')
    if operationDescKey != -1:
        logger.info("查询催收备注:" + operationDescSql)
        tmpOperationRows = mysql_pool.select_by_param(operationDescSql, [args])
        for row in tmpOperationRows or []:
            jsonOperation[row["case_id"]] = row["desc"]

    # 设置案件标签
    jsonTag = json.loads('{}')
    if containsCaseTag:
        tmpTagRows = mysql_pool.select_by_param(tagSql, [args])
        for row in tmpTagRows or []:
            if jsonTag.get(row["case_id"]) is None:
                jsonTag[row["case_id"]] = row["case_tag"]
            else:
                jsonTag[row["case_id"]] = jsonTag[row["case_id"]] + "," + row["case_tag"]

    # 设置催收手段
    jsonOperationWay = json.loads('{}')
    tmpOperationWayRows = mysql_pool.select_by_param(operationWaySql, [args])
    for row in tmpOperationWayRows or []:
        if jsonOperationWay.get(row["case_id"]) is None:
            jsonOperationWay[row["case_id"]] = row["operation_way_name"]
        else:
            jsonOperationWay[row["case_id"]] = jsonOperationWay[row["case_id"]] + "," + row["operation_way_name"]

    rows = len(excelEntity)
    # 组装数据
    logger.info(f"执行{sheetName}查询语句:{sql}")
    resultRows = mysql_pool.select_by_param(sql, [args])
    if len(resultRows) == 0:
        line["value"] = line["value"] + 1
    # 遍历数据集
    for resultRow in resultRows:
        # 根据委案公司处理excel数据
        orgDeltId = -1
        if isSeparate:
            orgDeltId = resultRow.get("org_delt_id")
            if deltNames.get(orgDeltId) is None:
                deltNames[orgDeltId] = resultRow.get("org_delt_name")
        # 判断当前委案公司是否创建过wb
        if wbs.get(orgDeltId) is None:
            workbook = openpyxl.Workbook(write_only=True)
            wbs[orgDeltId] = workbook
        wb = wbs[orgDeltId]
        # 判断当前sheetName是否已经创建过
        if sheetName in wb.sheetnames:
            sheet = wb[sheetName]
        else:
            sheet = wb.create_sheet(sheetName)
            # 设置表头
            data = []
            for k in range(rows):
                sheet.column_dimensions[get_column_letter(k + 1)].width = 20
                data.append(excelEntity[str(k)]['name'])
            sheet.append(data)
        if not resultRow:
            if line["value"] == 1:
                if sheetName == "案件信息":
                    errorMsg = "查询案件信息为空"
                    raise RuntimeError(errorMsg)
                else:
                    sheet.append([sheetName + "数据为空"])
                    return
            else:
                return
        data = []
        for j in range(rows):
            data.append("")
            typeVal = getTypeVal(resultRow, excelEntity[str(j)], languageType, operStatusDict, operStateDict)
            if typeVal is None:
                if excelEntity[str(j)]["value"] == 'case_note':
                    typeVal = jsonNote.get(resultRow["id"])
                    if typeVal is None:
                        continue
                elif excelEntity[str(j)]["value"] == 'case_tag':
                    typeVal = jsonTag.get(resultRow["id"])
                    if typeVal is None:
                        continue
                elif excelEntity[str(j)]["value"] == 'operation_way_name':
                    typeVal = jsonOperationWay.get(resultRow["id"])
                    if typeVal is None:
                        continue
                # 催收备注
                elif excelEntity[str(j)]["value"] == 'operation_desc':
                    typeVal = jsonOperation.get(resultRow["id"])
                    if typeVal is None:
                        continue
                # 减免
                elif excelEntity[str(j)]["value"] == 'reduction_total':
                    typeVal = jsonReduce.get(resultRow["id"])
                    if typeVal:
                        typeVal = str(float(typeVal) / 1000)
                    if typeVal is None:
                        typeVal = "0.0"
                # 剩余
                elif excelEntity[str(j)]["value"] == 'remain_amount':
                    if jsonReduce.get(resultRow["id"]) is None:
                        reduceTypeVal = "0.0"
                    else:
                        reduceTypeVal = jsonReduce.get(resultRow["id"])
                    if resultRow["amount"] is None:
                        amount = "0.0"
                    else:
                        amount = resultRow["amount"]
                    if resultRow["pay_amount"] is None:
                        payAmount = "0.0"
                    else:
                        payAmount = resultRow["pay_amount"]
                    typeVal = str((float(amount) - float(reduceTypeVal) - float(payAmount)) / 1000)
                else:
                    continue
            typeVal = ILLEGAL_CHARACTERS_RE.sub(r'', typeVal)
            data[len(data) - 1] = typeVal
        sheet.append(data)
        line["value"] = line["value"] + 1


# 重新排序json对象，确保case_note字段放在最后--蛋疼的需求。。
def resortJsonObj(jsonObject):
    key = -1
    for index in range(len(jsonObject)):
        if jsonObject[str(index)] == "case_note":
            key = index
            break
    if key != -1:
        jsonTmp = json.loads('{}')
        index = 0
        for p in range(len(jsonObject)):
            if p != key:
                jsonTmp[str(index)] = jsonObject[str(p)]
                index = index + 1
        jsonTmp[str(index)] = jsonObject[str(key)]
        return jsonTmp, True
    else:
        return jsonObject, False


def getLanguageDictEntity(language):
    # 联系人模板 多语言
    contactEntityLanguageDict = {
        "contactEntityDict": {
            '0': {'value': 'out_serial_no', 'name': '委案编号'},
            '1': {'value': 'delt_name', 'name': '委案公司'},
            '2': {'value': 'case_name', 'name': '债务人姓名'},
            '3': {'value': 'contact_name', 'name': '联系人'},
            '4': {'value': 'contact_status', 'name': '状态'},
            '5': {'value': 'relation_type', 'name': '关系'},
            '6': {'value': 'contact_type_name', 'name': '号码类型'},
            '7': {'value': 'contact_mobile', 'name': '手机号码'},
            '8': {'value': 'remark', 'name': '联系人备注'}},
        "contactEntityDict_tj": {
            '0': {'value': 'out_serial_no', 'name': '案件编号'},
            '1': {'value': 'delt_name', 'name': '申请方'},
            '2': {'value': 'case_name', 'name': '被申请人姓名'},
            '3': {'value': 'contact_name', 'name': '联系人'},
            '4': {'value': 'contact_status', 'name': '状态'},
            '5': {'value': 'relation_type', 'name': '关系'},
            '6': {'value': 'contact_type_name', 'name': '号码类型'},
            '7': {'value': 'contact_mobile', 'name': '手机号码'},
            '8': {'value': 'remark', 'name': '联系人备注'}}
    }
    key = "contactEntityDict"
    if language:
        key = key + "_" + language
    return contactEntityLanguageDict[key]


def getEndTypeLanguageDict(language):
    # 结案类型
    endTypeLanguageDict = {
        "endTypeDict": {0: '催收失败', 1: '催收成功'},
        "endTypeDict_tj": {0: '调解失败', 1: '调解成功'}
    }
    key = "endTypeDict"
    if language:
        key = key + "_" + language
    return endTypeLanguageDict[key]


def getCaseEntity(templateId, allfields, orgId):
    temFields = taskUtil.getTemplateFields(templateId)
    allFieldJson = json.loads(allfields)
    temFieldJson = json.loads(temFields)
    temFieldJson, containsCaseNote = resortJsonObj(temFieldJson)
    # 组装模板所有字段
    caseEntity = {}
    for k in range(len(temFieldJson)):
        fieldValue = temFieldJson[str(k)]
        for field in allFieldJson:
            if fieldValue == field['value']:
                caseEntity[str(k)] = field
                break
        else:
            # 自定义字段
            sql = f"select name from custom_field cuf where cuf.value='{fieldValue}'"
            fieldName = mysql_pool.select_one(sql)
            if fieldName is None:
                # 自定义搜索字段
                sql = f"select name from custom_search_field cef where cef.search_key='{fieldValue}' and org_id={orgId}"
                fieldName = mysql_pool.select_one(sql)
            if fieldName is None:
                fieldName = fieldValue
            else:
                fieldName = fieldName["name"]
            fieldEntity = {'name': fieldName, 'value': fieldValue}
            caseEntity[str(k)] = fieldEntity
    return caseEntity, containsCaseNote


def getRepaymentEntity(caseEntity):
    # 还款信息模板
    repaymentEntityDict = {'0': {'value': 'repayment_from', 'name': '还款人'},
                           '1': {'value': 'repayment_time', 'name': '还款时间'},
                           '2': {'value': 'repayment_amount', 'name': '还款金额', 'type': 'Money'},
                           '3': {'value': 'repayment_type', 'name': '还款类型'},
                           '4': {'value': 'repayment_card_no', 'name': '还款卡号'},
                           '5': {'value': 'repayment_style', 'name': '还款方式'},
                           '6': {'value': 'repayment_operator_name', 'name': '申请人'},
                           '7': {'value': 'repayment_create_time', 'name': '申请时间'},
                           '8': {'value': 'repayment_desc', 'name': '申请说明'}}

    repaymentEntity = caseEntity.copy()
    # 添加还款模板
    entityLen = len(caseEntity)
    for j in range(len(repaymentEntityDict)):
        repaymentEntity[str(entityLen + j)] = repaymentEntityDict[str(j)]
    return repaymentEntity


def getReductionEntity(caseEntity):
    reductionEntity = caseEntity.copy()
    # 减免信息模板
    reductionEntityDict = {'0': {'value': 'reduction_amount', 'name': '减免金额', 'type': 'Money'},
                           '1': {'value': 'reduction_after_amount', 'name': '减免后金额', 'type': 'Money'},
                           '2': {'value': 'apply_name', 'name': '申请人'}, '3': {'value': 'approval_name', 'name': '批准人'},
                           '4': {'value': 'apply_time', 'name': '申请时间'}, '5': {'value': 'apply_desc', 'name': '申请说明'},
                           '6': {'value': 'apply_status', 'name': '申请结果'}}
    # 添加减免模板
    entityLen = len(caseEntity)
    for j in range(len(reductionEntityDict)):
        reductionEntity[str(entityLen + j)] = reductionEntityDict[str(j)]
    return reductionEntity


def existsJsonKey(key, jsonObject):
    return jsonObject and key in jsonObject


def getTypeVal(dataResult, fieldEntry, languageType, operStatusDict, operationStateDict):
    if dataResult is None or fieldEntry is None:
        logger.debug("数据或者字段名称为空")
        return
    fieldValue = fieldEntry.get('value')
    fieldType = fieldEntry.get('type')
    if fieldValue is None:
        logger.debug("字段名称为空")
        return None
    typeVal = dataResult.get(fieldValue)
    if fieldValue == 'end_config_type':
        endTypeStr = ""
        endTypeDict = getEndTypeLanguageDict(languageType)
        if dataResult.get('end_type') and dataResult.get('end_type') in endTypeDict:
            endTypeStr = endTypeDict.get(dataResult.get('end_type'))
        if dataResult.get('attached_res'):
            endTypeStr = endTypeStr + "-" + dataResult.get('attached_res')
        typeVal = endTypeStr
    if fieldValue == 'reduction_after_amount':
        if dataResult.get('amount') and dataResult.get('reduction_amount'):
            typeVal = float(dataResult.get('amount')) - float(dataResult.get('reduction_amount'))
    if fieldValue == 'case_status':
        typeVal = taskUtil.getCaseStatus(dataResult.get('case_status'), languageType)
    if fieldValue == 'allot_status':
        typeVal = taskUtil.getCaseAllotStatus(dataResult.get('allot_status'))
    # 联系人状态
    if fieldValue == 'contact_status':
        return contact_status_dict.get(typeVal)
    if typeVal is None:
        # json字段里面
        caseJson = json.loads(dataResult['field_json'])
        typeVal = caseJson.get(fieldValue)
        if typeVal is None:
            return typeVal
    if type(typeVal).__name__ == 'unicode':
        typeVal = typeVal.encode("utf-8")
    else:
        typeVal = str(typeVal)
    if fieldValue == 'out_serial_no':
        return typeVal[0:typeVal.rindex("#")]
    # 枚举转化
    if fieldValue == 'apply_status':
        return applyStatusDict.get(typeVal)
    if fieldValue == 'operation_state':
        return operationStateDict.get(typeVal)
    if fieldValue == 'oper_status':
        return operStatusDict.get(typeVal)
    # 时间转化
    if fieldValue in ['entrust_start_time', 'entrust_end_time', 'overdue_date', 'repayment_time', 'apply_time', 'ptp_time']:
        return base_utils.formatDate(typeVal)
    # 金额转化
    if fieldType and fieldType == 'Money':
        return str(float(typeVal) / 1000)
    if (fieldValue == 'case_name' or fieldValue == 'id_card' or fieldValue == 'own_mobile'
        or fieldValue == "contact_name" or fieldValue == "contact_mobile") and encryptSwitch:
        typeVal = encrypt_utils.decrypt(encryptMode, typeVal)
    return typeVal


if __name__ == '__main__':
    # 初始化心跳
    init_heartbeat("export_case_info", logger)
    # consumer_task()
    background_schedulers = bg.BackgroundScheduler(timezone='Asia/Shanghai')
    background_schedulers.add_job(push_task, 'interval', seconds=10)
    background_schedulers.start()
    # consumer
    executors = {
        'default': ProcessPoolExecutor(multiprocessing.cpu_count())
    }
    blocking_schedulers = bl.BlockingScheduler(executors, timezone='Asia/Shanghai')
    for i in range(multiprocessing.cpu_count()):
        blocking_schedulers.add_job(consumer_task, 'interval', seconds=15)
    blocking_schedulers.start()
