# JH-Loan-Cash-Capital 系统改进计划

## 改进计划概述

基于日进件20万的目标，制定分阶段的系统改进计划，确保系统能够稳定支撑高并发业务场景。

## 第一阶段：紧急优化（1-2周）

### 1.1 立即实施项目

#### 1.1.1 数据库连接池优化
```yaml
# application.yml
spring:
  datasource:
    druid:
      initial-size: 20
      min-idle: 20
      max-active: 200
      max-wait: 60000
      validation-query: SELECT 1
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
```

#### 1.1.2 关键索引添加
```sql
-- 订单表优化
ALTER TABLE s03_order ADD INDEX idx_user_status_time (user_id, order_state, created_time);
ALTER TABLE s03_order ADD INDEX idx_flow_channel_time (flow_channel, created_time);

-- 授信表优化  
ALTER TABLE s03_credit ADD INDEX idx_bank_status (bank_channel, state);
ALTER TABLE s03_credit ADD INDEX idx_order_id (order_id);

-- 放款表优化
ALTER TABLE s03_loan ADD INDEX idx_loan_status_time (loan_state, created_time);
ALTER TABLE s03_loan ADD INDEX idx_credit_id (credit_id);
```

#### 1.1.3 Redis缓存热点数据
```java
@Service
public class CapitalConfigCacheService {
    
    @Cacheable(value = "capitalConfig", key = "#bankChannel", unless = "#result == null")
    public CapitalConfig getCapitalConfig(BankChannel bankChannel) {
        return capitalConfigRepository.findByBankChannel(bankChannel).orElse(null);
    }
    
    @CacheEvict(value = "capitalConfig", key = "#bankChannel")
    public void evictCapitalConfig(BankChannel bankChannel) {
        // 缓存失效
    }
}
```

#### 1.1.4 接口限流配置
```java
@RestController
@RequestMapping("lvxin")
public class LvxinController {
    
    @RateLimiter(name = "lvxin-global", fallbackMethod = "rateLimitFallback")
    @PostMapping("/api/partner/v1/confirmLoan")
    public LvxinResponse confirmLoan(@RequestBody LvxinLoanApplyRequest request) {
        return lvxinService.loanApply(request);
    }
    
    public LvxinResponse rateLimitFallback(LvxinLoanApplyRequest request, Exception ex) {
        return LvxinResponse.error("系统繁忙，请稍后重试");
    }
}
```

### 1.2 监控告警配置
```yaml
# 关键指标监控
management:
  endpoints:
    web:
      exposure:
        include: health,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true

# 告警规则
alerts:
  database:
    connection_pool_usage: 80%
    slow_query_threshold: 2s
  application:
    error_rate_threshold: 5%
    response_time_p99: 3s
  business:
    credit_success_rate: 90%
    loan_success_rate: 85%
```

## 第二阶段：架构优化（3-4周）

### 2.1 异步化改造

#### 2.1.1 授信流程异步化
```java
@Service
public class AsyncCreditService {
    
    @Async("creditExecutor")
    public CompletableFuture<CreditResult> processCreditAsync(String creditId) {
        return CompletableFuture.supplyAsync(() -> {
            Credit credit = creditRepository.findById(creditId).orElseThrow();
            // 异步处理授信逻辑
            return processCreditInternal(credit);
        });
    }
}

@Configuration
public class AsyncConfig {
    
    @Bean("creditExecutor")
    public TaskExecutor creditExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(50);
        executor.setMaxPoolSize(200);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("Credit-");
        executor.initialize();
        return executor;
    }
}
```

#### 2.1.2 MQ消息批处理
```java
@Component
public class BatchMessageProcessor {
    
    @RabbitListener(queues = "credit.apply.batch")
    public void processCreditBatch(List<CreditApplyMessage> messages) {
        // 批量处理，提高吞吐量
        List<CompletableFuture<Void>> futures = messages.stream()
            .map(msg -> CompletableFuture.runAsync(() -> processCredit(msg)))
            .collect(Collectors.toList());
            
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }
}
```

### 2.2 数据库读写分离
```yaml
# 主从数据源配置
spring:
  datasource:
    master:
      url: ***********************************
      username: ${DB_USER}
      password: ${DB_PASSWORD}
    slave:
      url: **********************************
      username: ${DB_USER}
      password: ${DB_PASSWORD}
```

```java
@Configuration
public class DataSourceConfig {
    
    @Bean
    @Primary
    public DataSource routingDataSource() {
        RoutingDataSource routingDataSource = new RoutingDataSource();
        routingDataSource.setTargetDataSources(Map.of(
            "master", masterDataSource(),
            "slave", slaveDataSource()
        ));
        routingDataSource.setDefaultTargetDataSource(masterDataSource());
        return routingDataSource;
    }
}
```

### 2.3 熔断降级机制
```java
@Component
public class CapitalServiceClient {
    
    @CircuitBreaker(name = "capital-service", fallbackMethod = "fallbackCredit")
    @Retry(name = "capital-service")
    public CreditResult callCapitalCredit(CreditRequest request) {
        return capitalService.credit(request);
    }
    
    public CreditResult fallbackCredit(CreditRequest request, Exception ex) {
        // 降级逻辑：返回默认结果或排队处理
        return CreditResult.builder()
            .status(CreditStatus.PENDING)
            .message("系统繁忙，已加入处理队列")
            .build();
    }
}
```

## 第三阶段：架构重构（5-8周）

### 3.1 微服务拆分

#### 3.1.1 服务拆分方案
```
原单体应用
├── jh-loan-cash-capital
└── 包含所有业务逻辑

新微服务架构
├── gateway-service (网关服务)
├── user-service (用户服务)
├── credit-service (授信服务)
├── loan-service (放款服务)
├── repay-service (还款服务)
├── risk-service (风控服务)
├── notification-service (通知服务)
└── config-service (配置服务)
```

#### 3.1.2 服务间通信
```java
// 使用OpenFeign进行服务间调用
@FeignClient(name = "credit-service")
public interface CreditServiceClient {
    
    @PostMapping("/internal/credit/apply")
    CreditResult applyCredit(@RequestBody CreditRequest request);
    
    @GetMapping("/internal/credit/{creditId}")
    Credit getCreditById(@PathVariable String creditId);
}
```

### 3.2 分库分表实施

#### 3.2.1 分表策略
```java
@Component
public class ShardingStrategy {
    
    public String getTableSuffix(String userId) {
        // 按用户ID hash分16张表
        int hash = Math.abs(userId.hashCode());
        return String.valueOf(hash % 16);
    }
    
    public String getOrderTable(String userId) {
        return "s03_order_" + getTableSuffix(userId);
    }
}
```

#### 3.2.2 分库配置
```yaml
# ShardingSphere配置
spring:
  shardingsphere:
    datasource:
      names: ds0,ds1,ds2,ds3
      ds0:
        type: com.alibaba.druid.pool.DruidDataSource
        url: *******************************
      ds1:
        type: com.alibaba.druid.pool.DruidDataSource
        url: *******************************
    rules:
      sharding:
        tables:
          s03_order:
            actual-data-nodes: ds$->{0..3}.s03_order_$->{0..15}
            table-strategy:
              standard:
                sharding-column: user_id
                sharding-algorithm-name: order_hash_mod
```

### 3.3 消息队列集群化
```yaml
# RabbitMQ集群配置
rabbitmq:
  cluster:
    nodes:
      - rabbit@node1
      - rabbit@node2  
      - rabbit@node3
  ha-policy: all
  ha-sync-mode: automatic
```

## 第四阶段：性能调优（9-12周）

### 4.1 JVM调优
```bash
# 生产环境JVM参数
JAVA_OPTS="-Xms8g -Xmx8g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+UnlockExperimentalVMOptions \
  -XX:+UseStringDeduplication \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -Xloggc:/var/log/gc.log"
```

### 4.2 应用层优化
```java
// 对象池化减少GC压力
@Component
public class ObjectPoolConfig {
    
    @Bean
    public GenericObjectPool<StringBuilder> stringBuilderPool() {
        return new GenericObjectPool<>(new BasePooledObjectFactory<StringBuilder>() {
            @Override
            public StringBuilder create() {
                return new StringBuilder(1024);
            }
            
            @Override
            public PooledObject<StringBuilder> wrap(StringBuilder obj) {
                return new DefaultPooledObject<>(obj);
            }
        });
    }
}
```

### 4.3 网络优化
```yaml
# Tomcat调优
server:
  tomcat:
    threads:
      max: 800
      min-spare: 100
    max-connections: 20000
    accept-count: 1000
    connection-timeout: 20000
    max-http-form-post-size: 10MB
```

## 实施时间表

| 阶段 | 时间 | 主要任务 | 预期效果 |
|------|------|----------|----------|
| 第一阶段 | 1-2周 | 紧急优化 | 支撑5万/天 |
| 第二阶段 | 3-4周 | 架构优化 | 支撑10万/天 |
| 第三阶段 | 5-8周 | 架构重构 | 支撑15万/天 |
| 第四阶段 | 9-12周 | 性能调优 | 支撑20万+/天 |

## 资源投入评估

### 人力资源
- **架构师**: 1人，全程参与
- **后端开发**: 4人，分阶段投入
- **DBA**: 1人，数据库优化
- **运维工程师**: 2人，基础设施
- **测试工程师**: 2人，性能测试

### 硬件资源
- **应用服务器**: 从2台扩容到8台
- **数据库服务器**: 从1主1从扩容到1主3从
- **Redis集群**: 从单机扩容到3主3从
- **RabbitMQ集群**: 从单机扩容到3节点集群

### 预算评估
- **硬件成本**: 约50万/年
- **人力成本**: 约200万（3个月）
- **第三方服务**: 约20万/年
- **总计**: 约270万

---

**制定人**: 技术架构组  
**审核人**: CTO  
**执行时间**: 2025年1月-4月  
**风险评估**: 中等风险，建议分阶段实施
