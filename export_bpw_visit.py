#!/usr/bin/python
# -*- coding: UTF-8 -*-
import json
import os
import time
import openpyxl
from docxtpl import DocxTemplate

from NacosHelper import NacosHelper
from utils import base_utils
from utils.base_utils import init_base_conf, init_heartbeat
from utils import encrypt_utils

# 导出excel

config = NacosHelper().init_config()
logger, fileUtil, ossSwitch, esSwitch, encryptSwitch, encryptMode, bucket, mysql_pool, taskUtil, redis_pool = init_base_conf("export_bpw_visit", config)
zipRootPath = "./export_visit_zip/"

# 地址类型字典
addressTypeDicts = {'0': '家庭地址', '1': '住宅地址', '2': '单位地址', '3': '户籍地址', '4': '对账单地址', '5': '其他地址'}

# 地址状态字典
addressStateDicts = {'0': '未知', '1': '有效', '-1': '无效', '-2': '删除'}


def doTasks():
    tasks = taskUtil.getUndoTasks(7)
    # tasks = mysql_pool.select(f"select org_id,dep_id,team_id,data,id,type from download_task where id=19659")
    for task in tasks or []:
        handleTask(task)


def handleTask(task):
    orgId, depId, teamId, data, taskId = task["org_id"], task["dep_id"], task["team_id"], task["data"], task["id"]
    logger.info("执行任务id:" + str(taskId))
    taskUtil.updateTaskIng(taskId)
    language = taskUtil.getOrgLanguage(orgId)
    # 获取导出数据
    fieldSql = "select v.id,v.case_id,va.address_id,v.state,v.visit_start_time,v.visit_end_time,v.visit_time, \
             v.user_id main_visitor,v.desc,v.sign_in_addr,v.sign_in_time, v.out_serial_temp out_serial_no, v.org_delt_id org_delt_name,\
        ci.name case_name,ci.amount,ifnull(ci.pay_amount,0) as pay_amount,ci.overdue_days,DATEDIFF(now(),ci.overdue_date) as overdue_days_new,ci.overdue_date,ci.entrust_end_time,ci.id_card,ci.own_mobile,ci.product_id product_name,ci.out_batch_id out_batch_no,\
        ca.address visit_address,ca.type address_type,ca.state address_state,\
        vd.apply_name,vd.apply_time,vd.instruction,vd.audit_name,vd.audit_time, \
        u.name as visitor_name,delt.name as org_delt_name, \
        v.ext1,v.ext2,v.ext3,v.ext4,v.ext5,v.ext6,v.ext7,v.ext8,v.ext9,v.ext10,v.ext11,v.ext12,v.ext13,v.ext14,v.ext15,v.ext16,v.ext17 "
    whereSql = " from visit v left join case_info ci on v.case_id = ci.id \
        left join visit_address va on v.id = va.visit_id \
        left join case_address ca on va.address_id = ca.id \
        left join visit_audit vd on vd.id=v.visit_audit_id \
        left join user u on v.user_id=u.id \
        left join org_delt delt on ci.org_delt_id=delt.id \
        where v.org_id=" + str(orgId)

    dataJson = json.loads(data)
    templateId = dataJson.get('templateId')
    if templateId is None:
        raise RuntimeError("外访导出任务找不到模版id")
    temFields = taskUtil.getTemplateFields(templateId)
    # 按照案件id查询
    if dataJson.get('caseId'):
        whereSql += " and v.`case_id`=%s " % str(dataJson.get('caseId'))
    # 按照allotAgent查询内催委外
    if dataJson.get('allotAgent'):
        whereSql += " and v.allot_agent=%s " % str(dataJson.get('allotAgent'))
    if dataJson.get('depIds'):
        whereSql += " and v.dep_id in (%s) " % str(dataJson.get('depIds'))
    if dataJson.get('teamIds'):
        whereSql += " and v.team_id in (%s) " % str(dataJson.get('teamIds'))
    # 分公司
    if dataJson.get('depId'):
        whereSql += " and v.dep_id=%s " % str(dataJson.get('depId'))
    # 地址类型
    if dataJson.get('types'):
        whereSql += " and ca.type in (%s) " % str(dataJson.get('types'))
    # 外访地址
    if dataJson.get('address'):
        whereSql += " and ca.address like '%" + str(dataJson.get('address')) + "%' "
    # 外访状态
    if dataJson.get('visitStates'):
        whereSql += " and v.state in (%s) " % str(dataJson.get('visitStates'))
    # 催员
    if dataJson.get('userIds'):
        whereSql += " and v.user_id in (%s) " % str(dataJson.get('userIds'))
    # 外访时间
    if dataJson.get('visitTimeStart'):
        whereSql += " and v.visit_time>='%s' " % str(dataJson.get('visitTimeStart'))
    if dataJson.get('visitTimeEnd'):
        whereSql += " and v.visit_time<='%s' " % str(dataJson.get('visitTimeEnd'))
    # 债务人姓名
    if dataJson.get('names'):
        whereSql += " and ci.name in (%s)" % (base_utils.arrayConvertSplitStr(dataJson.get('names')))
    # 委案编号
    if dataJson.get('outSerialTempList'):
        if len(dataJson['outSerialTempList']) > 1:
            whereSql += " and v.`out_serial_temp` in (%s)" % (base_utils.arrayConvertSplitStr(dataJson.get('outSerialTempList')))
        elif len(dataJson['outSerialTempList']) == 1:
            whereSql += " and v.out_serial_temp like '%s" % str(dataJson['outSerialTempList'][0]) + "%' "
    # 手机号
    if dataJson.get('mobiles'):
        whereSql += " and ci.own_mobile in (%s)" % (base_utils.arrayConvertSplitStr(dataJson.get('mobiles')))
    # 身份证号
    if dataJson.get('idCards'):
        whereSql += " and ci.id_card in (%s)" % (base_utils.arrayConvertSplitStr(dataJson.get('idCards')))
    # 委案公司
    if dataJson.get('deltIds'):
        whereSql += " and v.org_delt_id in (%s) " % str(dataJson.get('deltIds'))
    # 委案产品
    if dataJson.get('products'):
        whereSql += " and ci.product_id in (%s) " % str(dataJson.get('products'))
    # 委案批次号
    if dataJson.get('outBatchIds'):
        whereSql += " and ci.out_batch_id in (%s) " % str(dataJson.get('outBatchIds'))
    # 总数
    resultCount = selectVisitCount(whereSql)
    if resultCount and resultCount > 0:
        exportExcel(orgId, fieldSql + whereSql, resultCount, temFields, dataJson, taskId, language)
    else:
        logger.warn("任务id:" + str(taskId) + "查询外访记录为空")
        taskUtil.taskFail(taskId, "无外访信息")


def selectVisitCount(whereSql):
    sql = "select count(0) as cnt " + whereSql
    logger.debug("执行查询数据" + sql)
    result = mysql_pool.select_one(sql)
    return int(result["cnt"])


def exportExcel(orgId, sql, resultCount, temFields, dataJson, taskId, language):
    temFieldJson = json.loads(temFields)
    allfieldJson = getAllFieldJson(language)
    relFiles = dataJson.get('relFiles')
    relRecords = dataJson.get('relRecords')
    relReports = dataJson.get('relReports')
    autoUpdateOverdueDays = mysql_pool.select_one(f"select auto_update_overdue_days from org_switch where org_id={orgId}")["auto_update_overdue_days"] == "true"
    reportTpl = None
    # reportFileRenameTmpl = "caseName-mobile-visitorName-visitTime-outSerialNo-addressType"
    reportFileRenameTmpl = dataJson.get("reportFileRename")
    # recordFileRenameTmpl = "caseName-mobile-visitorName-visitTime-outSerialNo-addressType"
    recordFileRenameTmpl = dataJson.get("recordFileRename")
    customFieldDict = {}
    if relReports:
        reportTemplateId = dataJson.get("reportTemplateId")
        reportTemplateUrl = mysql_pool.select_one(f"select url from visit_report_template where id={reportTemplateId}")["url"]
        os.makedirs(zipRootPath + str(taskId) + "/外访报告", exist_ok=True)
        if ossSwitch:
            url = getFileUrl(reportTemplateUrl)
            if not base_utils.downloadFile(zipRootPath + f"template-{taskId}.docx", url, logger):
                logger.error(f"无法下载外访报告模板,地址：{url}")
                taskUtil.updateFail(taskId, "无法下载外访报告模板，请联系管理员")
                return
        else:
            fileUtil.exec(f"cp '/usr/local/duyansoft{reportTemplateUrl}' " + zipRootPath + f"template-{taskId}.docx")
        reportTpl = DocxTemplate(zipRootPath + f"template-{taskId}.docx")
        fieldMap = mysql_pool.select(f"select field,name from visit_report_field where org_id={orgId} and is_selected = 1")
        for row in fieldMap or []:
            customFieldDict[row["field"]] = row["name"]

    sheetNum = 1
    wb = openpyxl.Workbook()
    sheet = createNewSheet(wb, sheetNum, temFieldJson, allfieldJson, relFiles)
    maxRow = 1000002
    logger.info("表格最大行数:" + str(maxRow))
    # 分页
    limit = 500
    page = resultCount // limit
    # 填写表格
    num = 2
    columns = len(temFieldJson)
    for p in range(0, page + 1):
        results = selectVisitForLimit(sql, p, limit)
        if results is None:
            break
        resultSize = len(results)
        if encryptSwitch:
            for i in range(0, resultSize):
                results[i]["case_name"] = encrypt_utils.decrypt(encryptMode, results[i].get("case_name"))
                results[i]["own_mobile"] = encrypt_utils.decrypt(encryptMode, results[i].get("own_mobile"))
                results[i]["id_card"] = encrypt_utils.decrypt(encryptMode, results[i].get("id_card"))

        logger.info("第%s页数据是%s" % (str(p), str(resultSize)))
        if num + resultSize > maxRow:
            sheetNum = sheetNum + 1
            sheet = createNewSheet(wb, sheetNum, temFieldJson, allfieldJson, relFiles)
            num = 2
            logger.info("创建新的sheet%s" % str(sheetNum))
        # 外访员姓名
        mainVisitorDicts = {}
        if fieldNameExist("main_visitor", temFieldJson):
            mainVisitorKeys = getKeySets(results, "main_visitor")
            mainVisitorDicts = getUserDicts(mainVisitorKeys)
        # 委案公司
        orgDeltNameDicts = {}
        if fieldNameExist("org_delt_name", temFieldJson):
            orgDeltNameKeys = getKeySets(results, "org_delt_name")
            orgDeltNameDicts = getDeltDicts(orgDeltNameKeys)
        # 委案方批次号
        outBatchNoDicts = {}
        if fieldNameExist("out_batch_no", temFieldJson):
            outBatchNoKeys = getKeySets(results, "out_batch_no")
            outBatchNoDicts = getOutBatchNoDicts(outBatchNoKeys)
        # 委案产品
        productNameDicts = {}
        if fieldNameExist("product_name", temFieldJson):
            productNameKeys = getKeySets(results, "product_name")
            productNameDicts = getProductDicts(productNameKeys)
        # 协访员
        assistVisitorDicts = {}
        visitIdKeys = getKeySets(results, "id")
        if fieldNameExist("assist_visitor", temFieldJson):
            assistVisitorDicts = getAssistVisitorDicts(visitIdKeys)
        # 外访结果、外访小结
        visitResultDicts = {}
        visitSummaryDicts = {}
        if fieldNameExist("result", temFieldJson) or fieldNameExist("summary", temFieldJson):
            visitReports = selectVisitReports(visitIdKeys)
            if fieldNameExist("result", temFieldJson):
                visitResultDicts = getVisitResultDicts(visitReports)
            if fieldNameExist("summary", temFieldJson):
                visitSummaryDicts = getVisitSummaryDicts(visitReports)
        # 附件地址
        visitFileUrlDicts = {}
        if relFiles:
            logger.info("导出外访附件")
            os.makedirs(zipRootPath + f"{taskId}/附件", exist_ok=True)
            visitFileUrlDicts = getVisitFileUrlDicts(visitIdKeys)
        visitRecordDicts = {}
        if relRecords:
            logger.info("导出外访录音")
            os.makedirs(zipRootPath + f"{taskId}/通话录音", exist_ok=True)
            visitRecordDicts = getVisitFileUrlDicts(visitIdKeys)
        # visitSignDict = getVisitSignDicts(visitIdKeys)
        # 组装数据
        for j in range(0, resultSize):
            dataResult = results[j]
            visitId = dataResult["id"]
            # if relFiles:
            #     visitFileUrlList = visitFileUrlDicts.get(str(dataResult.get('id')))
            #     for y in range(0, len(visitFileUrlList or [])):
            #         sheet.cell(row=j + num, column=columns + y + 1, value=getFileUrl(visitFileUrlList[y]))
            for i in range(1, columns + 1):
                fieldName = temFieldJson[str(i - 1)]
                typeVal = getTypeVal(dataResult, fieldName, mainVisitorDicts, orgDeltNameDicts, outBatchNoDicts, productNameDicts, assistVisitorDicts, visitResultDicts, visitSummaryDicts)
                if typeVal is None:
                    continue
                sheet.cell(row=j + num, column=i, value=typeVal)
            if relFiles:
                for relFileInfo in visitFileUrlDicts.get(visitId) or []:
                    if ossSwitch:
                        base_utils.downloadFile(zipRootPath + f"{taskId}/附件/" + relFileInfo.get("wx_file_name"), getFileUrl(relFileInfo.get("url")), logger)
                    else:
                        url = relFileInfo.get("url")
                        fileUtil.exec(f"cp '/usr/local/duyansoft{url}' '" + zipRootPath + f"{taskId}/附件/" + relFileInfo.get("wx_file_name")+"'")
            if relRecords:
                for relRecordFile in visitRecordDicts.get(visitId) or []:
                    fileName = relRecordFile.get("wx_file_name")
                    extName = fileName[fileName.rfind("."):]
                    replaceName = get_file_name(dataResult, recordFileRenameTmpl)
                    fileName = replaceName + relRecordFile.get("create_time").strftime("%Y-%m-%d_%H-%M-%S") + extName
                    base_utils.downloadFile(zipRootPath + f"{taskId}/通话录音/{fileName}", getFileUrl(relRecordFile.get("url")), logger)
            if relReports:
                reportName = get_file_name(dataResult, reportFileRenameTmpl)
                fieldDictVal = get_visit_vars_dict(dataResult, customFieldDict, visitResultDicts, visitSummaryDicts, autoUpdateOverdueDays, productNameDicts, orgDeltNameDicts)
                reportTpl.render(fieldDictVal)
                reportTpl.save(zipRootPath + f"{taskId}/外访报告/{reportName}.docx")
        num = num + resultSize
        progress = p * 100 / (page + 1)
        taskUtil.updateProgress(taskId, progress)
    if relReports:
        fileUtil.delete(zipRootPath + f"template-{taskId}.docx")
    doSave(wb, resultCount, taskId)


def get_file_name(row, tmpl):
    return tmpl.replace("caseName", row.get("case_name") or "") \
        .replace("conMobile", row.get("own_mobile") or "") \
        .replace("operatorName", row.get("visitor_name") or "") \
        .replace("visitTime", row.get("visit_time").strftime("%Y-%m-%d_%H-%M-%S") if row.get("visit_time") else "") \
        .replace("outSerialNo", row.get("out_serial_no") or "") \
        .replace("type", addressTypeDicts.get(str(row.get("address_type"))))


def get_visit_vars_dict(row, customFieldDict, visitResultDicts, visitSummaryDicts, autoUpdateOverdueDays, productNameDicts, orgDeltNameDicts):
    result = {
        "caseNo": row.get("out_serial_no") if row.get("out_serial_no") else "",
        "name": row.get("case_name") if row.get("case_name") else "",
        "idCard": row.get("id_card") if row.get("id_card") else "",
        "amount": row.get("amount") / 1000 if row.get("amount") is not None else "",
        "payAmount": row.get("pay_amount") / 1000 if row.get("pay_amount") is not None else "",
        "entrustEndTime": row.get("entrust_end_time").strftime("%Y-%m-%d") if row.get("entrust_end_time") else "",
        "overdueDays": (row.get("overdue_days_new") if row.get("overdue_days_new") is not None else "") if autoUpdateOverdueDays else (row.get("overdue_days") if row.get("overdue_days") is not None else ""),
        "orgDeltName": orgDeltNameDicts.get(str(row.get("org_delt_name"))) if row.get("org_delt_name") else "",
        "productName": productNameDicts.get(str(row.get("product_name"))) if row.get("product_name") else "",
        "overdueDate": row.get("overdue_date").strftime("%Y-%m-%d") if row.get("overdue_date") else "",
        "ownMobile": row.get("own_mobile") if row.get("own_mobile") else "",
        "addressType": addressTypeDicts.get(str(row.get("address_type"))) if addressTypeDicts.get(str(row.get("address_type"))) else "",
        "visitResult": visitResultDicts.get(str(row.get("id"))) if visitResultDicts.get(str(row.get("id"))) else "",
        "visitSummary": visitSummaryDicts.get(str(row.get("id"))) if visitSummaryDicts.get(str(row.get("id"))) else "",
        "signAddress": row.get("sign_in_addr") if row.get("sign_in_addr") else "",
        "signTime": row.get("sign_in_time").strftime("%Y-%m-%d %H:%M:%S") if row.get("sign_in_time") else "",
        "visitName": row.get("visitor_name") if row.get("visitor_name") else "",
        "visitDate": row.get("visit_time").strftime("%Y-%m-%d") if row.get("visit_time") else "",
        "visitAddress": row.get("visit_address") if row.get("visit_address") else ""
    }

    option_ext_list = ['ext9', 'ext10', 'ext11']

    for i in range(0, 17):
        tmp = f"ext{(i + 1)}"
        if customFieldDict.get(tmp):
            if tmp in option_ext_list:
                if row.get(tmp):
                    json_data = json.loads(row.get(tmp))
                    result[customFieldDict.get(tmp)] = ','.join(json_data)
                else:
                    result[customFieldDict.get(tmp)] = ""
            else:
                result[customFieldDict.get(tmp)] = row.get(tmp) if row.get(tmp) is not None else ""
    return result


def getFileUrl(url):
    if ossSwitch:
        return url
    else:
        return "https://" + config["anmi_db"]["host"] + url


def getTypeVal(dataResult, fieldName, mainVisitorDicts, orgDeltNameDicts, outBatchNoDicts, productNameDicts, assistVisitorDicts, visitResultDicts, visitSummaryDicts):
    if dataResult is None or fieldName is None:
        logger.debug("外访记录或者字段名称为空")
        return
    # 外访排期
    if fieldName == 'visit_schedule':
        visitStartTime = dataResult.get('visit_start_time')
        visitEndTime = dataResult.get('visit_end_time')
        visitSchedule = ""
        if visitStartTime:
            visitSchedule = base_utils.formatDate(str(visitStartTime))
        visitSchedule = visitSchedule + "-"
        if visitEndTime:
            visitSchedule = visitSchedule + base_utils.formatDate(str(visitEndTime))
        return visitSchedule
    typeVal = dataResult.get(fieldName)
    if fieldName == 'assist_visitor' or fieldName == 'result' or fieldName == 'summary':
        typeVal = str(dataResult.get('id'))
    if typeVal is None:
        return typeVal
    if type(typeVal).__name__ == 'unicode':
        typeVal = typeVal.encode("utf-8")
    else:
        typeVal = str(typeVal)
    if fieldName == 'main_visitor':
        return mainVisitorDicts.get(typeVal)
    if fieldName == 'org_delt_name':
        return orgDeltNameDicts.get(typeVal)
    if fieldName == 'out_batch_no':
        return outBatchNoDicts.get(typeVal)
    if fieldName == 'product_name':
        return productNameDicts.get(typeVal)
    if fieldName == 'address_type':
        return addressTypeDicts.get(typeVal)
    if fieldName == 'address_state':
        return addressStateDicts.get(typeVal)
    if fieldName == 'assist_visitor':
        return assistVisitorDicts.get(typeVal)
    if fieldName == 'result':
        return visitResultDicts.get(typeVal)
    if fieldName == 'summary':
        return visitSummaryDicts.get(typeVal)
    if fieldName == 'amount':
        return str(float(typeVal) / 1000)
    # if (fieldName == 'case_name' or fieldName == 'id_card' or fieldName == 'own_mobile') and encryptSwitch:
    #     typeVal = encrypt_utils.decrypt(encryptMode, typeVal)
    return typeVal


def fieldNameExist(fieldName, temFieldJson):
    rows = len(temFieldJson)
    for i in range(0, rows):
        if fieldName == str(temFieldJson.get(str(i))):
            return True
    return False


def doSave(wbk, dataNums, taskId):
    logger.info("表格已生成，保存中")
    fileName = f"{taskId}/外访记录.xlsx"
    os.makedirs(zipRootPath + str(taskId), exist_ok=True)
    wbk.save(zipRootPath + fileName)
    zipFileName = f"外访_{taskId}.zip"
    fileUtil.zip(zipFileName, str(taskId), rate=0, cwd=zipRootPath)
    fileUtil.delete(zipRootPath + str(taskId))
    downloadurl, fileSize = base_utils.upload_file(ossSwitch, bucket, fileUtil, zipFileName, zipRootPath=zipRootPath)
    logger.info("下载地址:" + downloadurl)
    taskUtil.updateSuccess(taskId, downloadurl, dataNums, fileSize=fileSize)


def selectVisitForLimit(sql, page, limit):
    start = page * limit
    limitSql = sql + f" order by v.id desc limit {start},{limit}"
    logger.debug("执行查询数据" + limitSql)
    result = mysql_pool.select(limitSql)
    return result


def createNewSheet(wb, sheetNum, temFieldJson, allfieldJson, relFiles):
    if sheetNum == 1:
        ws = wb.active
        ws.title = "外访信息" + str(sheetNum)
    else:
        ws = wb.create_sheet("外访信息" + str(sheetNum))
    # 创建表头
    rows = len(temFieldJson)
    for i in range(1, rows + 1):
        ws.cell(row=1, column=i, value=getFieldName(temFieldJson[str(i - 1)], allfieldJson))
    # if relFiles and relFiles:
    #     ws.cell(row=1, column=rows + 1, value='附件地址')
    return ws


def getFieldName(keyName, allFields):
    for field in allFields:
        if keyName == field['value']:
            return field['name']


def getAllFieldJson(language):
    all_fields = taskUtil.getLanguageFieldDict("visitExportFields", language)
    fieldJson = json.loads(all_fields)
    return fieldJson


def getKeySets(results, keyName):
    keySets = set()
    for data in results:
        key = data.get(keyName)
        if key:
            keySets.add(key)
    return keySets


def getUserDicts(userIds):
    userDicts = {}
    if not userIds:
        return userDicts
    sql = "select id,name from user where id in %s"
    results = mysql_pool.select_by_param(sql, [userIds])
    for data in results:
        userDicts[str(data["id"])] = data["name"]
    return userDicts


def getDeltDicts(deltIds):
    deltDicts = {}
    if not deltIds:
        return deltDicts
    sql = "select id,name from org_delt where id in %s"
    results = mysql_pool.select_by_param(sql, [deltIds])
    for data in results:
        deltDicts[str(data["id"])] = data["name"]
    return deltDicts


def getOutBatchNoDicts(outBatchIds):
    outBatchNoDicts = {}
    if not outBatchIds:
        return outBatchNoDicts
    sql = "select id,name from out_batch where id in %s"
    results = mysql_pool.select_by_param(sql, [outBatchIds])
    for data in results:
        outBatchNoDicts[str(data["id"])] = data["name"]
    return outBatchNoDicts


def getProductDicts(productIds):
    productDicts = {}
    if productIds is None or len(productIds) == 0:
        return productDicts
    sql = "select id,name from product where id in %s"
    results = mysql_pool.select_by_param(sql, [productIds])
    for data in results:
        productDicts[str(data["id"])] = data["name"]
    return productDicts


def selectAssistVisitors(visitIds):
    if not visitIds:
        return None
    sql = "select vu.`visit_id`,vu.`user_id` from `visit_user` vu where vu.`visit_id` in %s and vu.`is_main`=1"
    logger.info("执行查询:" + sql)
    results = mysql_pool.select_by_param(sql, [visitIds])
    return results


def getAssistVisitorDicts(visitIds):
    assistVisitorDicts = {}
    results = selectAssistVisitors(visitIds)
    if results is None or len(results) == 0:
        return assistVisitorDicts
    # 所有外访员
    userIds = getKeySets(results, "user_id")
    userDicts = getUserDicts(userIds)
    for result in results:
        visitId = result.get('visit_id')
        userId = result.get('user_id')
        if visitId is None or userId is None:
            continue
        assistName = userDicts.get(str(userId))
        if assistName is None:
            continue
        names = assistVisitorDicts.get(str(visitId))
        if names is None:
            names = assistName + ","
        else:
            names = names + assistName + ","
        assistVisitorDicts[str(visitId)] = str(names)
    return assistVisitorDicts


def selectVisitReports(visitIds):
    if not visitIds:
        return None
    sql = "select vr.`visit_id`,vr.`result`,vr.`summary` from `visit_report` vr where vr.`visit_id` in %s order by vr.`create_time` asc"
    logger.info("执行查询:" + sql)
    results = mysql_pool.select_by_param(sql, [visitIds])
    return results


def getVisitResultDicts(results):
    visitResultDicts = {}
    if results is None or len(results) == 0:
        return visitResultDicts
    for result in results:
        visitId = result.get('visit_id')
        visitResult = result.get('result')
        if visitId is None or visitResult is None:
            continue
        visitResults = visitResultDicts.get(str(visitId))
        if visitResults is None:
            visitResults = visitResult + ";"
        else:
            visitResults = visitResults + visitResult + ";"
        visitResultDicts[str(visitId)] = str(visitResults)
    return visitResultDicts


def getVisitSummaryDicts(results):
    visitSummaryDicts = {}
    if results is None or len(results) == 0:
        return visitSummaryDicts
    for result in results:
        visitId = result.get('visit_id')
        summary = result.get('summary')
        if visitId is None or summary is None:
            continue
        visitSummaries = visitSummaryDicts.get(str(visitId))
        if visitSummaries is None:
            visitSummaries = summary + ";"
        else:
            visitSummaries = visitSummaries + summary + ";"
        visitSummaryDicts[str(visitId)] = str(visitSummaries)
    return visitSummaryDicts


def selectVisitFiles(visitIds):
    if not visitIds:
        return None
    sql = "select * from `visit_file` vf where vf.`visit_id` in %s order by vf.`visit_id`"
    logger.info("执行查询:" + sql)
    results = mysql_pool.select_by_param(sql, [visitIds])
    return results


def getVisitSignDicts(visitIds):
    visitSignDicts = {}
    results = mysql_pool.select_by_param("select visit_id,sign_in_time,address from visit_trail where visit_id in %s and type=1", [visitIds])
    for result in results or []:
        visitId = result.get('visit_id')
        visitSignDicts[visitId] = result
    return visitSignDicts


def getVisitFileUrlDicts(visitIds):
    visitFileUrlDicts = {}
    results = selectVisitFiles(visitIds)
    if not results:
        return visitFileUrlDicts
    for result in results:
        visitId = result.get('visit_id')
        visitFileUrlDicts[visitId] = visitFileUrlDicts.get(visitId) or []
        visitFileUrlDicts[visitId].append(result)
    return visitFileUrlDicts


if __name__ == '__main__':
    # 初始化心跳
    init_heartbeat("export_visit", logger)
    while True:
        try:
            doTasks()
        except Exception as e:
            logger.exception(e)
        # 休息10秒
        time.sleep(10)
