package com.jinghang.capital.core.banks.hxbk.dto.credit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * HXBK天枢系统授信申请查询响应
 *
 * @Author: Lior
 * @CreateTime: 2025/7/9 10:10
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKCreditQueryResponse {

    /**
     * 授信状态
     * 必填
     * 0-通过；1-不通过；2-处理中
     */
    @JsonProperty("status")
    private String status;

    /**
     * 拒绝原因
     * 必填
     */
    @JsonProperty("msg")
    private String msg;

    /**
     * 授信额度
     * 必填
     */
    @JsonProperty("credit_amt")
    private BigDecimal creditAmt;

    /**
     * 期数
     * 非必填
     */
    @JsonProperty("period")
    private Integer period;

    /**
     * 还款方式
     * 非必填
     */
    @JsonProperty("repay_type")
    private String repayType;

    /**
     * 额度信息
     * 必填
     */
    @JsonProperty("credit_info")
    private HXBKCreditAmount creditInfo;

    /**
     * 客户编号
     * 必填
     * 该客户的唯一标识，后续接口需要用到
     */
    @JsonProperty("custom_no")
    private String customNo;

    /**
     * 授信申请编号
     * 必填
     */
    @JsonProperty("apply_no")
    private String applyNo;

    /**
     * 资金方编号
     * 必填
     */
    @JsonProperty("fund_code")
    private String fundCode;

    /**
     * 冷静期结束日期
     * 非必填
     * 授信拒绝时返回
     */
    @JsonProperty("cooling_period")
    private Date coolingPeriod;

    /**
     * 资金源编码
     * 非必填
     * 下游有多个资金方时返回
     */
    @JsonProperty("loan_inst_code")
    private String loanInstCode;

    /**
     * 结果code
     * 必填
     * 详见附录
     */
    @JsonProperty("result_code")
    private String resultCode;

    /**
     * 结果信息
     * 必填
     */
    @JsonProperty("result_msg")
    private String resultMsg;

    /**
     * 请求唯一id
     * 必填
     */
    @JsonProperty("req_msg_id")
    private String reqMsgId;

    // Getter and Setter methods
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public BigDecimal getCreditAmt() {
        return creditAmt;
    }

    public void setCreditAmt(BigDecimal creditAmt) {
        this.creditAmt = creditAmt;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public HXBKCreditAmount getCreditInfo() {
        return creditInfo;
    }

    public void setCreditInfo(HXBKCreditAmount creditInfo) {
        this.creditInfo = creditInfo;
    }

    public String getCustomNo() {
        return customNo;
    }

    public void setCustomNo(String customNo) {
        this.customNo = customNo;
    }

    public String getApplyNo() {
        return applyNo;
    }

    public void setApplyNo(String applyNo) {
        this.applyNo = applyNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public Date getCoolingPeriod() {
        return coolingPeriod;
    }

    public void setCoolingPeriod(Date coolingPeriod) {
        this.coolingPeriod = coolingPeriod;
    }

    public String getLoanInstCode() {
        return loanInstCode;
    }

    public void setLoanInstCode(String loanInstCode) {
        this.loanInstCode = loanInstCode;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getReqMsgId() {
        return reqMsgId;
    }

    public void setReqMsgId(String reqMsgId) {
        this.reqMsgId = reqMsgId;
    }
}
