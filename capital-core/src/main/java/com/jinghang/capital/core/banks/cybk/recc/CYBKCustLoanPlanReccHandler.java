package com.jinghang.capital.core.banks.cybk.recc;


import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKLoanReplanDTO;
import com.jinghang.capital.core.entity.CYBKCreditFlow;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.vo.recc.ReccType;
import com.jinghang.common.util.DateUtil;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28 19:36
 */
@Component
public class CYBKCustLoanPlanReccHandler extends CYBKReccAbstractHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKCustLoanPlanReccHandler.class);

    @Autowired
    private WarningService warningService;

    @Override
    public void process(LocalDate reccDay) {
        List<CYBKLoanReplanDTO> loanReplanDTOList = findCustReccLoanReplans(reccDay);

        // 首次放款后的还款计划
        String dateStr = reccDay.format(DateTimeFormatter.ofPattern("yyyyMMdd"));


        if (CollectionUtils.isEmpty(loanReplanDTOList)) {
            logger.info("日期" + reccDay.toString() + "无放款订单");
        }

        File sourceFile = null;
        File tempDir = null;
        File okFile = null;
        try {
            Path path = Files.createTempDirectory("CYBK");
            tempDir = path.toFile();

            String filePre = DateFormatUtils.format(new Date(), "yyyyMMdd") + "对客还款计划文件";
            sourceFile = File.createTempFile(tempDir.getAbsolutePath() + filePre, ".csv");
            CSVPrinter printer = CSVFormat.DEFAULT.withSkipHeaderRecord()
                .withDelimiter(',').print(sourceFile, StandardCharsets.UTF_8);

            //表头
            // 合作机构贷款唯一编号,长银授信流水号,放款申请流水号,长银客户号,合作机构客户号,期次号,分期开始日期,分期结束日期,本金金额（单位分）,利息金额（单位分）");
            List<String> header = List.of(
                "loan_seq", "appl_seq", "out_appl_seq", "cust_id", "out_cust_id", "period", "start_date", "end_date", "prcp_amt", "int_amt");

            printer.printRecord(header);

            //按期次排序
            loanReplanDTOList = loanReplanDTOList.stream().sorted(Comparator.comparing(CYBKLoanReplanDTO::getPeriod)).toList();
            for (CYBKLoanReplanDTO loanReplanDTO : loanReplanDTOList) {
                CYBKCreditFlow creditFlow = getCybKCreditFlowRepository().findByCreditId(loanReplanDTO.getCreditId()).orElseThrow();
                Credit credit = getCreditRepository().findById(creditFlow.getCreditId()).orElseThrow();

                Date startDate;
                if (1 == loanReplanDTO.getPeriod()) {
                    startDate = loanReplanDTO.getLoanTime();
                } else {
                    startDate = loanReplanDTOList.stream().filter(item ->
                        item.getPeriod().equals(loanReplanDTO.getPeriod() - 1)).findFirst().orElseThrow().getRepayDate();

                }

                List<String> firstLoanReplanDTOList = new ArrayList<>();
                firstLoanReplanDTOList.add(loanReplanDTO.getLoanId());
                firstLoanReplanDTOList.add(credit.getCreditNo());
                firstLoanReplanDTOList.add(credit.getId());
                firstLoanReplanDTOList.add(creditFlow.getCustId());
                firstLoanReplanDTOList.add(loanReplanDTO.getAccountId());
                firstLoanReplanDTOList.add(loanReplanDTO.getPeriod().toString());
                firstLoanReplanDTOList.add(new SimpleDateFormat("yyyy-MM-dd").format(startDate));
                firstLoanReplanDTOList.add(DateUtil.formatWeb(new Date(loanReplanDTO.getRepayDate().getTime())));
                // 本金
                firstLoanReplanDTOList.add(loanReplanDTO.getPrincipalAmt().movePointRight(2).toPlainString());
                // 利息
                firstLoanReplanDTOList.add(loanReplanDTO.getInterestAmt().movePointRight(2).toPlainString());

                printer.printRecord(firstLoanReplanDTOList);
            }

            printer.close();

            String uploadPath = getCustReccFilePath(reccDay);

            // /download/cyxf/{产品编码}/out/files/{YYYYMMDD}/repay_plan_${yyyymmdd}.csv
            String fileName = "repay_plan_" + dateStr + ".csv";
            String okFileName = "repay_plan_" + dateStr + ".csv.ok";
            // 上传oss
            logger.info("长银直连还款计划文件上传:{}", uploadPath + fileName);
            getCybkSftpService().upload(uploadPath + fileName, sourceFile.getAbsolutePath());
            logger.info("长银直连还款计划文件上传结束:{}", uploadPath + fileName);

            // 生成 ok 文件
            Path localVerifyFilePath = Files.createTempFile("repay_plan_" + dateStr, ".csv.ok");
            OutputStream verifyOs = Files.newOutputStream(localVerifyFilePath);
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(String.valueOf(loanReplanDTOList.size()).getBytes(StandardCharsets.UTF_8));
            IOUtils.copy(byteArrayInputStream, verifyOs);

            getCybkSftpService().upload(uploadPath + okFileName, localVerifyFilePath.toAbsolutePath().toString());
            okFile = localVerifyFilePath.toFile();
        } catch (Exception e) {
            logger.error("长银直连消金还款计划文件上传异常", e);
            warningService.warn("长银直连还款计划文件上传异常");
        } finally {
            if (sourceFile != null) {
                sourceFile.delete();
            }
            if (okFile != null) {
                okFile.delete();
            }
            if (tempDir != null) {
                tempDir.delete();
            }
        }


    }

    @Override
    public ReccType getReccType() {
        return ReccType.CUST_LOAN_PLAN;
    }
}
