package com.jinghang.capital.core.vo.repay;

import java.math.BigDecimal;

public class RepayPlanItemVo {

    /**
     * 还款流水号
     */
    private String tranNo;

    /**
     * 资金放款id
     */
    private String loanId;

    /**
     * 期数
     */
    private Integer period;

    /**
     * 本金
     */
    private BigDecimal principalAmt;

    /**
     * 利息
     */
    private BigDecimal interestAmt;

    /**
     * 罚息/违约金
     */
    private BigDecimal penaltyAmt;

    /**
     * 融单费
     */
    private BigDecimal guaranteeFee;

    /**
     * 违约金
     */
    private BigDecimal breachAmt;

    /**
     * 总金额
     */
    private BigDecimal totalAmt;

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getTranNo() {
        return tranNo;
    }

    public void setTranNo(String tranNo) {
        this.tranNo = tranNo;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }
}
