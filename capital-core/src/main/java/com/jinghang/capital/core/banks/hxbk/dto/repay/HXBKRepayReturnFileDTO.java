package com.jinghang.capital.core.banks.hxbk.dto.repay;

import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 * @date 2025-07-15 14:04
 */
public class HXBKRepayReturnFileDTO {
    ByteArrayOutputStream fileOutputStream;
    String fileName;
    ByteArrayOutputStream CheckOutputStream;
    String checkFileName;
    Integer size;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getCheckFileName() {
        return checkFileName;
    }

    public void setCheckFileName(String checkFileName) {
        this.checkFileName = checkFileName;
    }

    public ByteArrayOutputStream getCheckOutputStream() {
        return CheckOutputStream;
    }

    public void setCheckOutputStream(ByteArrayOutputStream checkOutputStream) {
        CheckOutputStream = checkOutputStream;
    }

    public ByteArrayOutputStream getFileOutputStream() {
        return fileOutputStream;
    }

    public void setFileOutputStream(ByteArrayOutputStream fileOutputStream) {
        this.fileOutputStream = fileOutputStream;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}

