package com.jinghang.capital.core.banks.cybk.enums;


import com.jinghang.capital.core.enums.Education;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/27
 */
public enum CYBKEduLevel {
    MASTER("00", "硕士", Education.MASTER),
    COLLEGE("10", "大学本科", Education.COLLEGE),
    JUNIOR_COLLEGE("20", "大专", Education.JUNIOR_COLLEGE),
    HIGH_SCHOOL("30", "高中/中专", Education.HIGH_SCHOOL),
    JUNIOR_HIGH_SCHOOL("40", "初中及以下", Education.JUNIOR_HIGH_SCHOOL),
    UNKNOWN("99", "其他", Education.UNKNOWN);

    private final String code;
    private final String desc;
    private final Education education;

    CYBKEduLevel(String code, String desc, Education education) {
        this.code = code;
        this.desc = desc;
        this.education = education;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Education getEducation() {
        return education;
    }

    public static String getCodeByEducation(Education education) {
        return Arrays.stream(values()).filter(l -> education.equals(l.education)).findFirst().orElse(UNKNOWN).getCode();
    }
}
