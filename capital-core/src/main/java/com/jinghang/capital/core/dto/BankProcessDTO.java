package com.jinghang.capital.core.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.capital.core.enums.BankChannel;

import java.time.LocalDate;

/**
 * 资方通用处理dto
 */
public class BankProcessDTO {
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate processDate;

    private BankChannel channel;

    private String businessId;

    public LocalDate getProcessDate() {
        return processDate;
    }

    public void setProcessDate(LocalDate processDate) {
        this.processDate = processDate;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }
}
