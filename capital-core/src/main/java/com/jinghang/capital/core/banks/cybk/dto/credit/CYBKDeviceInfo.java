package com.jinghang.capital.core.banks.cybk.dto.credit;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/10/10 17:07
 **/
public class CYBKDeviceInfo {

    /**
     * 设备ip
     */
    private String deviceIp;
    /**
     * 设备指纹标识
     */
    private String deviceId;
    /**
     * GPS数据来源
     */
    private String coordinateType;
    /**
     * GPS经度
     */
    private String longitude;
    /**
     * GPS纬度
     */
    private String latitude;
    /**
     * GPS地址
     */
    private CYBKAddress gpsInfo;
    /**
     * IP
     */
    private String trueIp;
    /**
     * 系统名称
     */
    private String os;
    /**
     * 系统版本
     */
    private String osVersion;
    /**
     * 系统ID
     */
    private String androidId;
    /**
     * Android系统定制商
     */
    private String brand;
    /**
     * 手机制造商
     */
    private String product;
    /**
     * 硬件制造商
     */
    private String manufacture;
    /**
     * 手机型号
     */
    private String deviceModel;
    /**
     * 运营商名称
     */
    private String carrierOperator;
    /**
     * 系统ram（内存）大小
     */
    private String memoryTotal;
    /**
     * 网络类型
     */
    private String internetType;
    /**
     * IMEI编码(安卓)或IDFA(iphone)
     */
    private String imei;
    /**
     * SIM卡状态
     */
    private String simStatus;
    /**
     * SIM序列号
     */
    private String simId;
    /**
     * 分辨率
     */
    private String screenRatio;
    /**
     * 语言
     */
    private String language;
    /**
     * 时区
     */
    private String timeZone;
    /**
     * WFI_MAC地址
     */
    private String wifiMac;
    /**
     * 硬件名称
     */
    private String hardName;
    /**
     * Sdk版本
     */
    private String sdkVersion;
    /**
     * 电池电量
     */
    private String electricQuantity;
    /**
     * CPU类型
     */
    private String cpuType;
    /**
     * 申请业务所属省描述
     */
    private String applyProvinceDesc;

    public String getDeviceIp() {
        return deviceIp;
    }

    public void setDeviceIp(String deviceIp) {
        this.deviceIp = deviceIp;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getCoordinateType() {
        return coordinateType;
    }

    public void setCoordinateType(String coordinateType) {
        this.coordinateType = coordinateType;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public CYBKAddress getGpsInfo() {
        return gpsInfo;
    }

    public void setGpsInfo(CYBKAddress gpsInfo) {
        this.gpsInfo = gpsInfo;
    }

    public String getTrueIp() {
        return trueIp;
    }

    public void setTrueIp(String trueIp) {
        this.trueIp = trueIp;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getManufacture() {
        return manufacture;
    }

    public void setManufacture(String manufacture) {
        this.manufacture = manufacture;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getCarrierOperator() {
        return carrierOperator;
    }

    public void setCarrierOperator(String carrierOperator) {
        this.carrierOperator = carrierOperator;
    }

    public String getMemoryTotal() {
        return memoryTotal;
    }

    public void setMemoryTotal(String memoryTotal) {
        this.memoryTotal = memoryTotal;
    }

    public String getInternetType() {
        return internetType;
    }

    public void setInternetType(String internetType) {
        this.internetType = internetType;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getSimStatus() {
        return simStatus;
    }

    public void setSimStatus(String simStatus) {
        this.simStatus = simStatus;
    }

    public String getSimId() {
        return simId;
    }

    public void setSimId(String simId) {
        this.simId = simId;
    }

    public String getScreenRatio() {
        return screenRatio;
    }

    public void setScreenRatio(String screenRatio) {
        this.screenRatio = screenRatio;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getWifiMac() {
        return wifiMac;
    }

    public void setWifiMac(String wifiMac) {
        this.wifiMac = wifiMac;
    }

    public String getHardName() {
        return hardName;
    }

    public void setHardName(String hardName) {
        this.hardName = hardName;
    }

    public String getSdkVersion() {
        return sdkVersion;
    }

    public void setSdkVersion(String sdkVersion) {
        this.sdkVersion = sdkVersion;
    }

    public String getElectricQuantity() {
        return electricQuantity;
    }

    public void setElectricQuantity(String electricQuantity) {
        this.electricQuantity = electricQuantity;
    }

    public String getCpuType() {
        return cpuType;
    }

    public void setCpuType(String cpuType) {
        this.cpuType = cpuType;
    }

    public String getApplyProvinceDesc() {
        return applyProvinceDesc;
    }

    public void setApplyProvinceDesc(String applyProvinceDesc) {
        this.applyProvinceDesc = applyProvinceDesc;
    }
}
