package com.jinghang.capital.core.banks.cybk.recc;

import com.jinghang.capital.core.vo.recc.ReccResultVo;
import com.jinghang.capital.core.vo.recc.ReccType;

import java.time.LocalDate;

/**
 * @作者 Mr.sandman
 * @时间 2025/06/26 09:40
 */
public interface CYBKReccQueryHandler {

  /**
   * 查询 对账文件
   * @param date 对账时间
   * @return 对账结果
   */
  ReccResultVo query(LocalDate date);

  /**
   * 对账类型
   * @return 对账类型
   */
  ReccType getReccType();

}
