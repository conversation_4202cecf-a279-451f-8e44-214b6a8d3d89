package com.jinghang.capital.core.banks.hxbk.recc;

import com.jinghang.capital.core.banks.hxbk.enums.HXBKReccFileType;
import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.entity.hxbk.HXBKReccLoan;
import com.jinghang.capital.core.enums.ReccStateEnum;
import com.jinghang.capital.core.vo.recc.ReccType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 放款对账处理
 * @作者 Mr.sandman
 * @时间 2025/07/10 18:18
 */
@Component
public class HXBKReccLoanHandler extends HXBKReccAbstractHandler {

  private static final Logger logger = LoggerFactory.getLogger(HXBKReccLoanHandler.class);
  /**
   * 对账处理
   *
   * @param reccDay 对账文件日期
   */
  @Override
  public void process( LocalDate reccDay ) {
    List<Loan> reccLoans = findReccLoans(reccDay);
    CYBKReconcileFile reconcileFile = findReconcileFile(reccDay, HXBKReccFileType.LOAN_FILE);
    reconcileFile.setReccDate(LocalDate.now());
    String reccId = reconcileFile.getId();

    List<HXBKReccLoan> reccLoanFileRecords = findReccLoanFileRecords(reccId);
    if (reccLoans.size() == 0 && reccLoanFileRecords.size() == 0) {
      reconcileFile.setReccState(ReccStateEnum.S.name());
      updateCYBKReconcileFile(reconcileFile);
      return;
    }
    if (reccLoans.size() != reccLoanFileRecords.size()) {
      logger.warn("湖消放款明细对账成功条数不一致 reccType：{} reccDay：{} 业务方条数：{} 资方条数：{}", HXBKReccFileType.LOAN_FILE, reccDay, reccLoans.size(),
                  reccLoanFileRecords.size());
      getWarningService().warn("\n湖消放款明细对账:" + HXBKReccFileType.LOAN_FILE + "\n对账日:" + reccDay + "\n成功条数不一致 ");
    }

    List<HXBKReccLoan> successList = new ArrayList<>();
    reccLoanFileRecords.forEach(lf -> {
      String loanId = lf.getSysId();
      if ( StringUtils.isBlank(loanId)) {
        lf.setReccStatus(ReccStateEnum.F.name());
        lf.setRemark("湖消放款成功记录,系统未匹配到");
        updateReccLoan(lf);
        warningLog(lf, null, null);
        return;
      }
      Loan existRecord = filterLoanInter(reccLoans, loanId);
      boolean match = match(lf, existRecord);
      lf.setReccStatus(match ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
      if (match) {
        successList.add(lf);
      } else {
        //对账失败打印日志
        warningLog(lf, existRecord, loanId);
      }
      updateReccLoan(lf);
    });

    boolean allMatch = successList.size() == reccLoans.size() && successList.size() == reccLoanFileRecords.size();
    reconcileFile.setReccState(allMatch ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
    updateCYBKReconcileFile(reconcileFile);

    //对账失败，企业微信告警
    if (ReccStateEnum.F.name().equals(reconcileFile.getReccState())) {
      getWarningService().warn("\n湖消放款明细对账失败:" + HXBKReccFileType.LOAN_FILE + "\n对账日:" + reccDay + "\n，对账成功总数与系统放款成功总数不一致 ");
    }
    logger.info("湖消放款明细对账结束，reccType：{} reccDay：{} 结果：{}", HXBKReccFileType.LOAN_FILE, reccDay, reconcileFile.getReccState());
  }

  private void warningLog( HXBKReccLoan lf, Loan existRecord, String loanId ) {
    BigDecimal sysAmount = null;
    Integer sysPeriod = null;
    if ( Objects.nonNull(existRecord)) {
      sysAmount = existRecord.getLoanAmt();
      sysPeriod = existRecord.getPeriods();
    }
    logger.warn("湖消放款明细对账失败，reccType：{} 资方loanId：{}，金额:{},期数:{}; 业务方金额:{},期数:{}",
                HXBKReccFileType.LOAN_FILE, loanId, getEncashAmtInYuan(lf), lf.getTotalTerms(), sysAmount, sysPeriod);
  }

  private Loan filterLoanInter(List<Loan> loanList, String loanId) {
    return loanList.stream().filter(l -> loanId.equals(l.getId())).findAny().orElse(null);
  }

  private boolean match(HXBKReccLoan reccLoan, Loan loan) {
    if (loan == null) {
      return false;
    }
    return loan.getLoanAmt().compareTo(getEncashAmtInYuan(reccLoan)) == 0 && loan.getPeriods().equals(reccLoan.getTotalTerms());
  }

  private BigDecimal getEncashAmtInYuan(HXBKReccLoan reccLoan) {
    return reccLoan.getEncashAmt()
        .divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN); // 单位：分 -> 元
  }

  @Override
  public ReccType getReccType() {
    return ReccType.LOAN;
  }
}
