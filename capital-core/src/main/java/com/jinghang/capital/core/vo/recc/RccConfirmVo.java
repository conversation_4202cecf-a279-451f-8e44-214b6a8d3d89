package com.jinghang.capital.core.vo.recc;



import com.jinghang.capital.core.enums.BankChannel;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 通汇对账单确认Dto
 */
public class RccConfirmVo {
    /**
     * 资方
     */
    private BankChannel channel;
    /**
     * 对账单流水
     */
    private String cnNo;
    /**
     * 打款金额（单位分）
     */
    private BigDecimal transferAmount;
    /**
     * 银行流水
     */
    private String bankSerialNo;
    /**
     * 打款时间
     */
    private LocalDate payDate;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public String getCnNo() {
        return cnNo;
    }

    public void setCnNo(String cnNo) {
        this.cnNo = cnNo;
    }

    public BigDecimal getTransferAmount() {
        return transferAmount;
    }

    public void setTransferAmount(BigDecimal transferAmount) {
        this.transferAmount = transferAmount;
    }

    public String getBankSerialNo() {
        return bankSerialNo;
    }

    public void setBankSerialNo(String bankSerialNo) {
        this.bankSerialNo = bankSerialNo;
    }

    public LocalDate getPayDate() {
        return payDate;
    }

    public void setPayDate(LocalDate payDate) {
        this.payDate = payDate;
    }
}
