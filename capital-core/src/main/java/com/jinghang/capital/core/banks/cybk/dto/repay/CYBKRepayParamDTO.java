package com.jinghang.capital.core.banks.cybk.dto.repay;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/8/24 20:31
 */
public class CYBKRepayParamDTO {

    private String repayRecordId;
    private BigDecimal totalPrincipal;
    private BigDecimal totalInterest;
    private BigDecimal totalPenalty;
    private BigDecimal guaranteeAmt;
    private BigDecimal reduceGuaranteeAmt;
    private String period;
    private String repayMode;
    private String repayType;
    private LocalDate transferDate;

    /**
     * 应还违约金
     */
    private BigDecimal breachAmt;

    public BigDecimal getTotalPrincipal() {
        return totalPrincipal;
    }

    public void setTotalPrincipal(BigDecimal totalPrincipal) {
        this.totalPrincipal = totalPrincipal;
    }

    public BigDecimal getTotalInterest() {
        return totalInterest;
    }

    public void setTotalInterest(BigDecimal totalInterest) {
        this.totalInterest = totalInterest;
    }

    public BigDecimal getTotalPenalty() {
        return totalPenalty;
    }

    public void setTotalPenalty(BigDecimal totalPenalty) {
        this.totalPenalty = totalPenalty;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getReduceGuaranteeAmt() {
        return reduceGuaranteeAmt;
    }

    public void setReduceGuaranteeAmt(BigDecimal reduceGuaranteeAmt) {
        this.reduceGuaranteeAmt = reduceGuaranteeAmt;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(String repayMode) {
        this.repayMode = repayMode;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public LocalDate getTransferDate() {
        return transferDate;
    }

    public void setTransferDate(LocalDate transferDate) {
        this.transferDate = transferDate;
    }

    public String getRepayRecordId() {
        return repayRecordId;
    }

    public void setRepayRecordId(String repayRecordId) {
        this.repayRecordId = repayRecordId;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }
}
