package com.jinghang.capital.core.banks.cybk.dto.repay;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKRepayApplyResponse {


    /**
     * 外部还款流水号
     */
    private String outRepaymentSeq;
    /**
     * 长银还款流水号
     */
    private String setlSeq;
    /**
     * 长银授信流水号
     */
    private String applCde;
    /**
     * 长银借据号
     */
    private String loanNo;
    /**
     * 还款状态
     * 01-还款成功
     * 02-还款失败
     * 03-清算处理中
     */
    private String setlSts;
    /**
     * 归还担保费
     */
    private BigDecimal guaraFeeAmt;
    /**
     * 归还担保费罚息
     */
    private BigDecimal guaraFeeOdAmt;

    public String getOutRepaymentSeq() {
        return outRepaymentSeq;
    }

    public void setOutRepaymentSeq(String outRepaymentSeq) {
        this.outRepaymentSeq = outRepaymentSeq;
    }

    public String getSetlSeq() {
        return setlSeq;
    }

    public void setSetlSeq(String setlSeq) {
        this.setlSeq = setlSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getSetlSts() {
        return setlSts;
    }

    public void setSetlSts(String setlSts) {
        this.setlSts = setlSts;
    }

    public BigDecimal getGuaraFeeAmt() {
        return guaraFeeAmt;
    }

    public void setGuaraFeeAmt(BigDecimal guaraFeeAmt) {
        this.guaraFeeAmt = guaraFeeAmt;
    }

    public BigDecimal getGuaraFeeOdAmt() {
        return guaraFeeOdAmt;
    }

    public void setGuaraFeeOdAmt(BigDecimal guaraFeeOdAmt) {
        this.guaraFeeOdAmt = guaraFeeOdAmt;
    }

    public boolean isSuccess() {
        return Objects.equals(setlSts, "01");
    }

    public boolean isFail() {
        return Objects.equals(setlSts, "02");
    }

    public boolean isProcessing() {
        return Objects.equals(setlSts, "03");
    }
}
