package com.jinghang.capital.core.banks.hxbk.enums;


import com.jinghang.capital.core.banks.cybk.enums.CYBKIndustry;
import com.jinghang.capital.core.enums.Industry;
import com.jinghang.capital.core.enums.Position;

import java.util.Arrays;

/**
 * 码值映射
 * IN001 农、林、牧、渔业——15：农、林、牧、渔业
 * IN002 采掘业——14：采矿业
 * IN003 制造业——12：制造业
 * IN004 电力、燃气及水的生产和供应业——2：电力、热力、燃气及水生产和供应业
 * IN005 建筑业——1：建筑业
 * IN006 交通运输、仓储和邮政业——3：交通运输、仓储和邮政业
 * IN007 信息传输、计算机服务和软件业——9:信息传输、软件和信息技术服务业
 * IN008 批发和零售业——7:批发和零售业
 * IN009 住宿和餐饮业——6:住宿和餐饮业
 * IN010 金融业——10：金融业
 * IN011 房地产业——5:房地产业
 * IN012 租赁和商务服务业——8:租赁和商务服务业
 * IN013 科学研究、技术服务业和地质勘察业——18：科学研究和技术服务业
 * IN014 水利、环境和公共设施管理业——13：水利、环境和公共设施管理业
 * IN015 居民服务和其他服务业——16：居民服务、修理和其他服务业
 * IN016 教育——4：教育
 * IN017 卫生、社会保障和社会福利业——17：卫生和社会工作
 * IN018 文化、体育和娱乐业——11:文化、体育和娱乐业
 * IN019 公共管理和社会组织——19：公共管理、社会保障和社会组织
 * IN021 未知——20：其他
 *
 * <AUTHOR>
 * @date 2025/7/16
 */
public enum HXBKIndustry {
    FIFTEEN("IN001", "农、林、牧、渔业", Industry.FIFTEEN),
    FOURTEEN("IN002", "采矿业", Industry.FOURTEEN),
    TWELVE("IN003", "制造业", Industry.TWELVE),
    TWO("IN004", "电力、热力、燃气及水生产和供应业", Industry.TWO),
    ONE("IN005", "建筑业", Industry.ONE),
    THREE("IN006", "交通运输、仓储和邮政业", Industry.THREE),
    NINE("IN007", "信息传输、软件和信息技术服务业", Industry.NINE),
    SEVEN("IN008", "批发和零售业", Industry.SEVEN),
    SIX("IN009", "住宿和餐饮业", Industry.SIX),
    TEN("IN010", "金融业", Industry.TEN),
    FIVE("IN011", "房地产业", Industry.FIVE),
    EIGHT("IN012", "租赁和商务服务业", Industry.EIGHT),
    EIGHTEEN("IN013", "科学研究和技术服务业", Industry.EIGHTEEN),
    THIRTEEN("IN014", "水利、环境和公共设施管理业", Industry.THIRTEEN),
    SIXTEEN("IN015", "居民服务、修理和其他服务业", Industry.SIXTEEN),
    FOUR("IN016", "教育", Industry.FOUR),
    SEVENTEEN("IN017", "卫生和社会工作", Industry.SEVENTEEN),
    ELEVEN("IN018", "文化、体育和娱乐业", Industry.ELEVEN),
    NINETEEN("IN019", "公共管理、社会保障和社会组织", Industry.NINETEEN),
    TWENTY("IN021", "其他", Industry.TWENTY);

    private final String code;
    private final String desc;
    private final Industry industry;

    HXBKIndustry(String code, String desc, Industry industry) {
        this.code = code;
        this.desc = desc;
        this.industry = industry;
    }

    public static String getHXBKCodeByIndustry(String industry) {
        return Arrays.stream(values()).filter(l -> l.industry.name().equals(industry)).findFirst().orElse(TWENTY).getCode();
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Industry getIndustry() {
        return industry;
    }
}
