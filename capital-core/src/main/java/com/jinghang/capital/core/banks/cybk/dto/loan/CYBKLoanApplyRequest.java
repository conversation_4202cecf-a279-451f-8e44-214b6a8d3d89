package com.jinghang.capital.core.banks.cybk.dto.loan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKAccountInfo;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKFamilyInfo;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKImageInfo;
import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKRelationInfo;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;


import java.math.BigDecimal;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKLoanApplyRequest extends CYBKBaseRequest {
    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.LOAN_APPLY;


    /**
     * 外部放款流水号
     */
    private String outLoanSeq;
    /**
     * 外部合同号
     */
    private String outContractSeq;
    /**
     * 长银授信流水号
     */
    private String applCde;
    /**
     * 长银客户号
     */
    private String custId;
    /**
     * 商户码值
     */
    private String merchantNo;
    /**
     * 门店码值
     */
    private String storeCode;
    /**
     * 终端类型
     */
    private String terminalType;
    /**
     * 签约授权号（协议签约返回的签约编号）
     */
    private String agrSeq;
    /**
     * 放款金额
     */
    private BigDecimal dnAmt;
    /**
     * 申请期限
     */
    private BigDecimal applyTnr;
    /**
     * 还款方式
     */
    private String mtdCde;
    /**
     * 贷款用途
     */
    private String purpose;
    /**
     * 其他贷款用途
     */
    private String otherPurpose;
    /**
     * 利率
     */
    private BigDecimal priceIntRat;
    /**
     * 利率模式
     */
    private String mtdMode;
    /**
     * 利率调整方式
     */
    private String repcOpt;
    /**
     * 客户展示利率
     */
    private BigDecimal custDayRate;
    /**
     * 还款间隔
     */
    private String loanFreq;
    /**
     * 每期还款日
     */
    private String dueDayOpt;
    /**
     * 还款日
     */
    private String dueDay;
    /**
     * 合同签订日期
     */
    private String contSignDt;
    /**
     * 回调地址
     */
    private String callbackUrl;
    /**
     * 首次还款日
     */
    private String firstPayDt;

    private List<CYBKAccountInfo> accInfoList;

    private CYBKLoanGuaranteeInfo guaranteeInfo;

    private List<CYBKImageInfo> imageInfoList;

    private BasicInfoReq basicInfo;

    private List<CYBKRelationInfo> relationList;

    private CYBKFamilyInfo familyInfo;

    public List<CYBKAccountInfo> getAccInfoList() {
        return accInfoList;
    }

    public void setAccInfoList(List<CYBKAccountInfo> accInfoList) {
        this.accInfoList = accInfoList;
    }

    public CYBKLoanGuaranteeInfo getGuaranteeInfo() {
        return guaranteeInfo;
    }

    public void setGuaranteeInfo(CYBKLoanGuaranteeInfo guaranteeInfo) {
        this.guaranteeInfo = guaranteeInfo;
    }

    public List<CYBKImageInfo> getImageInfoList() {
        return imageInfoList;
    }

    public void setImageInfoList(List<CYBKImageInfo> imageInfoList) {
        this.imageInfoList = imageInfoList;
    }

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getOutContractSeq() {
        return outContractSeq;
    }

    public void setOutContractSeq(String outContractSeq) {
        this.outContractSeq = outContractSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(String terminalType) {
        this.terminalType = terminalType;
    }

    public String getAgrSeq() {
        return agrSeq;
    }

    public void setAgrSeq(String agrSeq) {
        this.agrSeq = agrSeq;
    }

    public BigDecimal getDnAmt() {
        return dnAmt;
    }

    public void setDnAmt(BigDecimal dnAmt) {
        this.dnAmt = dnAmt;
    }

    public BigDecimal getApplyTnr() {
        return applyTnr;
    }

    public void setApplyTnr(BigDecimal applyTnr) {
        this.applyTnr = applyTnr;
    }

    public String getMtdCde() {
        return mtdCde;
    }

    public void setMtdCde(String mtdCde) {
        this.mtdCde = mtdCde;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getOtherPurpose() {
        return otherPurpose;
    }

    public void setOtherPurpose(String otherPurpose) {
        this.otherPurpose = otherPurpose;
    }

    public BigDecimal getPriceIntRat() {
        return priceIntRat;
    }

    public void setPriceIntRat(BigDecimal priceIntRat) {
        this.priceIntRat = priceIntRat;
    }

    public String getMtdMode() {
        return mtdMode;
    }

    public void setMtdMode(String mtdMode) {
        this.mtdMode = mtdMode;
    }

    public String getRepcOpt() {
        return repcOpt;
    }

    public void setRepcOpt(String repcOpt) {
        this.repcOpt = repcOpt;
    }

    public BigDecimal getCustDayRate() {
        return custDayRate;
    }

    public void setCustDayRate(BigDecimal custDayRate) {
        this.custDayRate = custDayRate;
    }

    public String getLoanFreq() {
        return loanFreq;
    }

    public void setLoanFreq(String loanFreq) {
        this.loanFreq = loanFreq;
    }

    public String getDueDayOpt() {
        return dueDayOpt;
    }

    public void setDueDayOpt(String dueDayOpt) {
        this.dueDayOpt = dueDayOpt;
    }

    public String getDueDay() {
        return dueDay;
    }

    public void setDueDay(String dueDay) {
        this.dueDay = dueDay;
    }

    public String getContSignDt() {
        return contSignDt;
    }

    public void setContSignDt(String contSignDt) {
        this.contSignDt = contSignDt;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public String getFirstPayDt() {
        return firstPayDt;
    }

    public void setFirstPayDt(String firstPayDt) {
        this.firstPayDt = firstPayDt;
    }

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }

    public BasicInfoReq getBasicInfo() {
        return basicInfo;
    }

    public void setBasicInfo(BasicInfoReq basicInfo) {
        this.basicInfo = basicInfo;
    }

    public List<CYBKRelationInfo> getRelationList() {
        return relationList;
    }

    public void setRelationList(List<CYBKRelationInfo> relationList) {
        this.relationList = relationList;
    }

    public CYBKFamilyInfo getFamilyInfo() {
        return familyInfo;
    }

    public void setFamilyInfo(CYBKFamilyInfo familyInfo) {
        this.familyInfo = familyInfo;
    }
}
