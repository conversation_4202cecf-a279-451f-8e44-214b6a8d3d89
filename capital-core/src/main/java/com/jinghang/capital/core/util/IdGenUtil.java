package com.jinghang.capital.core.util;


import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.IdGen;

import java.time.LocalDate;
import java.util.UUID;

public class IdGenUtil {

    private static final int MAX_ID_LENGTH = 32;
    private static final int MAX_ID = 6;

    public static String genReqNo() {
        return IdGen.genId("RN", MAX_ID_LENGTH);
    }

    public static String genReqNo(String perfix) {
        return IdGen.genId(perfix, MAX_ID_LENGTH);
    }
    public static String genReqNo(String perfix, Integer length) {
        return IdGen.genId(perfix, length);
    }
    /**
     * 生成协议编号
     *
     * @param prefix:前缀
     * @param businessId：业务编号
     * @return
     */
    public static String genContractNo(String prefix, String businessId) {
        return prefix + LocalDate.now().format(DateUtil.SHORT_FORMATTER)
            + businessId.substring(Math.max(0, businessId.length() - MAX_ID));
    }



    public static String genUUID() {
        return "RN" + UUID.randomUUID().toString().replace("-", "");
    }

}
