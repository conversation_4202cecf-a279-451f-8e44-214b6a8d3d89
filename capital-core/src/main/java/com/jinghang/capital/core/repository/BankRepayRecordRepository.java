package com.jinghang.capital.core.repository;

import com.jinghang.capital.core.entity.BankRepayRecord;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FlowChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface BankRepayRecordRepository extends JpaRepository<BankRepayRecord, String> {

    List<BankRepayRecord> findByIdIn(List<String> ids);

    List<BankRepayRecord> findByRepayStatusAndChannelAndRepayTimeIsGreaterThanEqualAndRepayTimeIsLessThan(ProcessStatus status,
                                                                                                          BankChannel channel,
                                                                                                          LocalDateTime dayStart,
                                                                                                          LocalDateTime nextDayStart);

    List<BankRepayRecord> findByRepayStatusAndChannelAndRepayTimeIsGreaterThanEqualAndRepayTimeIsLessThanAndCouponAmtIsGreaterThan(ProcessStatus status,
                                                                                                                                   BankChannel channel,
                                                                                                                                   LocalDateTime dayStart,
                                                                                                                                   LocalDateTime nextDayStart,
                                                                                                                                   BigDecimal couponAmt);

    @Query("select b from BankRepayRecord b where b.repayStatus = 'SUCCESS' and b.repayType != 'CLAIM' "
        + "and b.loanId = ?1 and b.period = ?2 ")
    Optional<BankRepayRecord> findSuccessNotClaimByLoanIdAndPeriod(String loanId, Integer period);


    @Query("select b from BankRepayRecord b left join Loan l on b.loanId = l.id "
        + "where b.repayStatus = ?1 and b.channel = ?2 and b.repayTime >= ?3 and b.repayTime < ?4 and l.flowChannel = ?5")
    List<BankRepayRecord> findSuccessBankRepayRecord(ProcessStatus status,
                                                     BankChannel channel,
                                                     LocalDateTime dayStart,
                                                     LocalDateTime nextDayStart,
                                                     FlowChannel flowChannel);

    @Query("select b from BankRepayRecord b left join Loan l on b.loanId = l.id "
        + "where b.repayStatus = ?1 and b.channel = ?2 and b.repayTime >= ?3 and b.repayTime < ?4 ")
    List<BankRepayRecord> findSuccessBankRepayRecordLyx(ProcessStatus status,
                                                        BankChannel channel,
                                                        LocalDateTime dayStart,
                                                        LocalDateTime nextDayStart);

    @Query("select b from BankRepayRecord b left join Loan l on b.loanId = l.id "
        + "where b.repayStatus = ?1 and b.channel = ?2 and b.repayTime >= ?3 and b.repayTime < ?4 and b.couponAmt > ?5 ")
    List<BankRepayRecord> findSuccessAndHasCouponBankRepayRecord(ProcessStatus status,
                                                                 BankChannel channel,
                                                                 LocalDateTime dayStart,
                                                                 LocalDateTime nextDayStart,
                                                                 BigDecimal couponAmt);

    @Query("select b from BankRepayRecord b left join Loan l on b.loanId = l.id "
        + "where b.repayStatus = ?1 and b.channel = ?2 and b.repayTime >= ?3 and b.repayTime < ?4 and b.reduceAmount > ?5 ")
    List<BankRepayRecord> findSuccessAndHasReduceBankRepayRecord(ProcessStatus status,
                                                                 BankChannel channel,
                                                                 LocalDateTime dayStart,
                                                                 LocalDateTime nextDayStart,
                                                                 BigDecimal reduceAmount);

    Optional<BankRepayRecord> findByLoanIdAndPeriodAndRepayStatus(String loanId, Integer period, ProcessStatus status);

    List<BankRepayRecord> findListByLoanIdAndPeriodAndRepayStatus(String loanId, Integer period, ProcessStatus status);


    @Query("select l from BankRepayRecord l where l.channel = ?1 and l.repayStatus = ?2 and l.repayTime >= ?3 and l.repayTime < ?4")
    List<BankRepayRecord> findByChannelAndLoanStatusAndLoanTime(BankChannel channel,
                                                                ProcessStatus repayStatus,
                                                                LocalDateTime dayStart,
                                                                LocalDateTime nextDayStart);

    List<BankRepayRecord> findBankRepayRecordsByLoanIdAndPeriod(String loanId, Integer period);

    Optional<BankRepayRecord> findTopBankRepayRecordsByLoanIdAndPeriodAndRepayStatus(String loanId, Integer period, ProcessStatus processStatus);


    BankRepayRecord findFirstByLoanIdAndRepayStatusOrderByPeriodDesc(String loanId, ProcessStatus repayStatus);

    /**
     * 查询时间段内 结清的订单(含提前结清、正常结清、代偿结清）
     *
     * @param channel   资方类型
     * @param startTime 开始时间（条件表达式: >= startTime）
     * @param endTime   结束时间（条件表达式: < endTime）
     * @return List
     */
    @Query("select l from BankRepayRecord l"
        + " where l.channel = ?1 and l.repayStatus ='SUCCESS' and l.repayPurpose = 'CLEAR' and l.repayTime >= ?2 and l.repayTime < ?3 "
        + " union "
        + " select brr from BankRepayRecord brr"
        + " left join Loan loan on loan.id = brr.loanId and brr.period = loan.periods"
        + " where brr.channel = ?1 and brr.repayStatus ='SUCCESS' and brr.repayPurpose = 'CURRENT' and brr.repayTime >= ?2 and brr.repayTime < ?3")
    List<BankRepayRecord> findSuccessClearAndWithChannelAndRepayTime(BankChannel channel, LocalDateTime startTime, LocalDateTime endTime);

    List<BankRepayRecord> findByLoanIdAndRepayStatus(String loanId, ProcessStatus repayStatus);

    Optional<BankRepayRecord> findBySysIdAndRepayStatus(String sysId, ProcessStatus repayStatus);

    Optional<BankRepayRecord> findBySysIdAndRepayStatusIn(String sysId, List<ProcessStatus> repayStatus);

    Optional<BankRepayRecord> findBySysId(String sysId);

    List<BankRepayRecord> findByLoanIdAndRepayTypeAndRepayStatus(String loanId, RepayType repayType, ProcessStatus repayStatus);

    /**
     * 根据资方、还款模式、还款时间，查询还款成功数据
     *
     * @param channel
     * @param repayMode
     * @param dayStart
     * @param nextDayStart
     * @return
     */
    @Query("select l from BankRepayRecord l where l.repayStatus = 'SUCCESS' and l.channel = ?1 "
        + "and l.repayMode = ?2 and l.repayTime >= ?3 and l.repayTime < ?4")
    List<BankRepayRecord> findByChannelAndRepayModeAndLoanTime(BankChannel channel,
                                                               RepayMode repayMode,
                                                               LocalDateTime dayStart,
                                                               LocalDateTime nextDayStart);


    /**
     * 查询时间范围内 非代偿 全部的结清还款记录
     *
     * @param bankChannel
     * @param timeStart
     * @param timeEnd
     * @return
     */
    @Query("select brr from BankRepayRecord brr join Loan l on l.id = brr.loanId where brr.repayStatus = 'SUCCESS' "
        + "and brr.repayType != 'CLAIM' and (brr.period = l.periods or brr.repayPurpose = 'CLEAR') "
        + "and brr.channel = ?1 and brr.repayTime >= ?2  and brr.repayTime < ?3")
    List<BankRepayRecord> findListSettleSuccessNotClaim(BankChannel bankChannel, LocalDateTime timeStart, LocalDateTime timeEnd);

    /**
     * 查询时间范围内 代偿成功记录 包含回购
     *
     * @param bankChannel
     * @param timeStart
     * @param timeEnd
     * @return
     */
    @Query("select brr from BankRepayRecord brr where brr.repayStatus = 'SUCCESS' and brr.repayType = 'CLAIM' "
        + "and brr.channel = ?1 and brr.repayTime >= ?2  and brr.repayTime < ?3")
    List<BankRepayRecord> findListClaimSuccess(BankChannel bankChannel, LocalDateTime timeStart, LocalDateTime timeEnd);


    /**
     * 查询代偿成功记录
     *
     * @param loanId 借据编号
     * @param period 期数
     * @return
     */
    @Query("select brr from BankRepayRecord brr where brr.repayStatus = 'SUCCESS' and brr.loanId = ?1 and brr.period = ?2 and brr.repayType = 'CLAIM' ")
    BankRepayRecord findClaimSuccessByLoanIdAndPeriod(String loanId, Integer period);

    /**
     * 查询结清成功记录 (非回购记录或代偿结清记录)
     *
     * @param loanId 借据编号
     * @return
     */
    @Query("select brr from BankRepayRecord brr join Loan l on l.id = brr.loanId where brr.repayStatus = 'SUCCESS' and brr.loanId = ?1 "
        + "and brr.repayType != 'CLAIM' and (brr.period = l.periods or brr.repayPurpose = 'CLEAR')  ")
    BankRepayRecord findClearSuccessByLoanIdNotClaim(String loanId);

    /**
     * 查询时间范围内,回购结清
     *
     * @param bankChannel
     * @param timeStart
     * @param timeEnd
     * @return
     */
    @Query("select brr from BankRepayRecord brr where brr.repayStatus = 'SUCCESS' and brr.repayType = 'CLAIM' "
        + "and brr.repayPurpose = 'CLEAR' and brr.channel = ?1 and brr.repayTime >= ?2  and brr.repayTime < ?3")
    List<BankRepayRecord> findListClaimClearSuccess(BankChannel bankChannel, LocalDateTime timeStart, LocalDateTime timeEnd);

    @Query(value = "select brr.* from bank_repay_record brr where brr.repay_status = 'SUCCESS' "
        + "and brr.repay_type = 'CLAIM' and brr.channel = 'TL_SMB' and brr.repay_purpose = ?1 and brr.loan_id = ?2", nativeQuery = true)
    BankRepayRecord findBankSuccessClaim(String repayPurpose, String loanId);

    List<BankRepayRecord> findByLoanId(String loanId);

    List<BankRepayRecord> findBySysIdIn(List<String> sysIds);

    List<BankRepayRecord> findBySysIdInAndRepayTypeNot(List<String> sysIds, RepayType repayType);

    /**
     * 查询时间范围内 代偿成功记录 对客未还
     *
     * @param bankChannel
     * @param timeStart
     * @param timeEnd
     * @return
     */
    @Query(value = "select brr.* from bank_repay_record brr where brr.repay_status = 'SUCCESS' and brr.repay_type = 'CLAIM' and brr.channel = ?1 \n"
        + " and brr.repay_time >= ?2 and brr.repay_time < ?3\n"
        + " and not exists (\n"
        + "\tselect 1 from customer_repay_record crr where crr.loan_id = brr.loan_id and crr.period = brr.period and crr.repay_status = 'SUCCESS'\n"
        + "\tand crr.repay_time >= ?2 and crr.repay_time < ?3);", nativeQuery = true)
    List<BankRepayRecord> findListBankClaimSuccess(String bankChannel, LocalDateTime timeStart, LocalDateTime timeEnd);

    List<BankRepayRecord> findByRepayStatusAndChannelAndLoanIdIn(ProcessStatus processStatus, BankChannel backChannel, List<String> list);

    /**
     * 查询时间范围内 代偿成功记录 不包含回购
     *
     * @param bankChannel
     * @param timeStart
     * @param timeEnd
     * @return
     */
    @Query("select brr from BankRepayRecord brr where brr.repayStatus = 'SUCCESS' and brr.repayType = 'CLAIM' "
        + "and brr.repayPurpose = 'CURRENT' and brr.channel = ?1 and brr.repayTime >= ?2  and brr.repayTime < ?3")
    List<BankRepayRecord> findListCurrentSuccessClaim(BankChannel bankChannel, LocalDateTime timeStart, LocalDateTime timeEnd);

    /**
     * 查询回购结清成功记录
     *
     * @param loanId 借据编号
     * @return
     */
    @Query("select brr from BankRepayRecord brr where brr.repayStatus = 'SUCCESS' and "
        + "brr.loanId = ?1 and brr.repayPurpose = 'CLEAR' and brr.repayType = 'CLAIM' ")
    BankRepayRecord findClaimClearSuccessByLoanId(String loanId);
}
