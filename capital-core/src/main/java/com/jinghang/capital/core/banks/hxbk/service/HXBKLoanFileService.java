package com.jinghang.capital.core.banks.hxbk.service;

import com.jinghang.capital.api.dto.repay.RepayReturnUploadResultDto;
import com.jinghang.capital.core.banks.AbstractBankFileService;
import com.jinghang.capital.core.banks.hxbk.config.HXBKConfig;
import com.jinghang.capital.core.banks.hxbk.dto.contract.HXBKContractGetRequest;
import com.jinghang.capital.core.banks.hxbk.dto.contract.HXBKContractGetResponse;
import com.jinghang.capital.core.banks.hxbk.dto.file.HXBKSettlementCertificateRequest;
import com.jinghang.capital.core.banks.hxbk.dto.file.HXBKSettlementCertificateResponse;
import com.jinghang.capital.core.banks.hxbk.dto.repay.HXBKRepayReturnFileDTO;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKFileType;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKRepayAmtType;
import com.jinghang.capital.core.banks.hxbk.enums.HXBKResponseCode;
import com.jinghang.capital.core.banks.hxbk.remote.HXBKRequestService;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.*;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;
import com.jinghang.capital.core.enums.LoanStage;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.exception.BizErrorCode;
import com.jinghang.capital.core.exception.BizException;
import com.jinghang.capital.core.repository.BankLoanReplanRepository;
import com.jinghang.capital.core.repository.DownloadFileLogRepository;
import com.jinghang.capital.core.repository.HXBKCreditFlowRepository;
import com.jinghang.capital.core.repository.BankRepayRecordRepository;
import com.jinghang.capital.core.repository.LoanRepository;
import com.jinghang.capital.core.service.CommonService;
import com.jinghang.capital.core.service.FileService;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.util.IdGenUtil;
import com.jinghang.capital.core.vo.ProductVo;
import com.jinghang.capital.core.vo.file.*;
import com.jinghang.capital.core.vo.repay.RepayReturnUploadVo;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @作者 Mr.sandman
 * @时间 2025/07/12 10:03
 */
@Service
public class HXBKLoanFileService extends AbstractBankFileService {

  private static final Logger logger = LoggerFactory.getLogger(HXBKLoanFileService.class);

  public static final String DOT = ".";

  @Autowired
  private CommonService commonService;
  @Autowired
  private WarningService warningService;
  @Autowired
  private RedissonClient redissonClient;
  @Autowired
  private HXBKConfig hxbkConfig;
  @Autowired
  private HXBKRequestService requestService;
  @Autowired
  private FileService fileService;
  @Autowired
  private BankLoanReplanRepository bankLoanReplanRepository;
  @Autowired
  private DownloadFileLogRepository downloadFileLogRepository;
  @Autowired
  private HXBKCreditFlowRepository hxbkCreditFlowRepository;

  @Override
  public FileUploadResultVo upload( FileUploadVo uploadVo ) {
    return null;
  }

  @Override
  public FileDownloadResultVo download( FileDownloadVo downloadVo ) {
    FileType fileType = downloadVo.getType();

    return switch (fileType) {
      case LOAN_CONTRACT, ENTRUSTED_GUARANTEE_CONTRACT, ARBITRATION_AGREEMENT, PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT -> downloadLoanContract(downloadVo);
      case REPAYMENT_FILE, LOAN_FILE -> downloadReccFile(downloadVo);
      case CREDIT_SETTLE_VOUCHER_FILE -> downloadCreditSettleVoucher(downloadVo);
      default -> downloadForOutOrderId(downloadVo);
    };
  }

  @Override
  public ByteArrayOutputStream getCheckFileStream(String fileName, Integer size) {

    String[] headers = {"file_name", "row_count"};

    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
    BufferedWriter bw = new BufferedWriter(writer);

    try {
      // 表头
      bw.write(String.join(",", headers));
      bw.newLine();
      // 内容
      bw.write(String.join(",", Arrays.asList( fileName,size.toString())));
      bw.newLine();
      bw.flush();
    } catch (IOException e) {
      logger.error("线下还款回盘文件，check文件，生成异常，{}",e.getMessage());
      e.printStackTrace();
    }

    return outputStream;
  }


  private FileDownloadResultVo downloadLoanContract(FileDownloadVo downloadVo) {
    Loan loan = commonService.findLoanById(downloadVo.getLoanId());
    FileDownloadResultVo resultVo = new FileDownloadResultVo();
    try {
      LoanFile loanFile = downloadLoanContract(loan, downloadVo.getType());
      resultVo.setOssBucket(loanFile.getOssBucket());
      resultVo.setOssPath(loanFile.getOssKey());
      resultVo.setFileName(loanFile.getFileName());
      resultVo.setFileStatus(loanFile.getSignStatus());
      String ossUrl = fileService.getOssUrl(loanFile.getOssBucket(), loanFile.getOssKey());
      resultVo.setFileUrl(ossUrl);
    } catch (BizException e) {
      resultVo.setFileStatus(ProcessStatus.FAIL);
    } catch (IOException e) {
      logger.warn("下载湖消合同异常, loanId:{}, fileType:{}", loan.getId(), downloadVo.getType(), e);
      resultVo.setFileStatus(ProcessStatus.FAIL);
    }

    return resultVo;
  }

  /**
   * 业务端下载对账文件
   *
   * @param downloadVo
   * @return
   */
  private FileDownloadResultVo downloadReccFile(FileDownloadVo downloadVo) {
    LocalDate fileDate = downloadVo.getFileDate();
    ReconciliationFile reccFile =
        commonService.getRecFileRepository().findFirstByFileTypeAndProductAndFileDateAndBankChannelOrderByCreatedTimeDesc(downloadVo.getType(),
                                                                                                                          ProductVo.ZC_360, fileDate, downloadVo.getBankChannel());

    FileDownloadResultVo resultVo = new FileDownloadResultVo();
    if (reccFile == null) {
      return resultVo;
    }

    resultVo.setOssBucket(reccFile.getTargetOssBucket());
    resultVo.setOssPath(reccFile.getTargetOssKey());
    resultVo.setFileName(reccFile.getFileName());
    return resultVo;
  }

  private FileDownloadResultVo downloadForOutOrderId(FileDownloadVo downloadVo) {
    FileType fileType = downloadVo.getType();
    Loan loan = commonService.findLoanByOutId(downloadVo.getLoanOrderId());
    // 借据关联的合同
    List<LoanFile> loanFiles = commonService.getLoanFileRepository().findByRelatedIdAndFileType(loan.getId(), fileType);
    if (CollectionUtils.isEmpty(loanFiles)) {
      // 授信关联的合同
      loanFiles = commonService.getLoanFileRepository().findByCreditIdAndFileType(loan.getCreditId(), fileType);
    }

    FileDownloadResultVo resultVo = new FileDownloadResultVo();
    if (!CollectionUtils.isEmpty(loanFiles)) {
      LoanFile loanFile = loanFiles.get(0);
      resultVo.setOssBucket(loanFile.getOssBucket());
      resultVo.setOssPath(loanFile.getOssKey());
      resultVo.setFileName(loanFile.getFileName());
      resultVo.setSignStatus(loanFile.getSignStatus());
      resultVo.setFileStatus(ProcessStatus.SUCCESS);
      String ossUrl = fileService.getOssUrl(loanFile.getOssBucket(), loanFile.getOssKey());
      // 获取文件后缀名
      String suffixName = loanFile.getOssKey().substring(loanFile.getOssKey().lastIndexOf(DOT) + 1);
      resultVo.setFileUrl(ossUrl);
      resultVo.setFileName(loanFile.getFileName() + DOT + suffixName);
    }
    return resultVo;
  }

  @Override
  public void processDaily( FileDailyProcessVo processVo ) {
    LocalDate processDate = processVo.getProcessDate();
    if (processDate == null) {
      processDate = LocalDate.now();
    }
    FileType type = processVo.getType();
    final LocalDate workDate = processDate;

    switch (type) {
      // 借款合同、征信授权书、仲裁协议
      case LOAN_CONTRACT, ARBITRATION_AGREEMENT,PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT -> processLoanContract(workDate);
      // 结清证明申请 查询结果并下载
      case CREDIT_SETTLE_VOUCHER_FILE -> processCreditSettleVoucherQuery();
      default -> {
      }
    }
  }


  @Async("fileProcessThreadPool")
  public void processLoanContract(final LocalDate processDate) {
    logger.info("下载湖消借款合同, processDate:{}", processDate);

    List<Loan> loanList = commonService.findSuccessLoan(BankChannel.HXBK, processDate);

    loanList.forEach(l -> {
      // 征信授权书
      try {
        downloadLoanContract(l, FileType.PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT);
      } catch (Exception e) {
        warningService.warn("下载湖消征信授权书异常, loanId:" + l.getId(), msg -> logger.error(msg, e));
      }
      // 借款合同
      try {
        downloadLoanContract(l, FileType.LOAN_CONTRACT);
      } catch (Exception e) {
        warningService.warn("下载湖消借款合同异常, loanId:" + l.getId(), msg -> logger.error(msg, e));
      }
      // 仲裁协议
      try {
        downloadLoanContract(l, FileType.ARBITRATION_AGREEMENT);
      } catch (Exception e) {
        warningService.warn("下载湖消仲裁协议异常, loanId:" + l.getId(), msg -> logger.error(msg, e));
      }
    });

  }

  /**
   * 下载借款合同、征信授权书、仲裁协议
   * @param loan
   * @throws IOException
   */
  public LoanFile downloadLoanContract( Loan loan, FileType fileType ) throws IOException {
    logger.info("下载蚂蚁湖消最终签署的合同, loanId: {},fileType:{}", loan.getId(), fileType);

    List<LoanFile> loanFiles = commonService.getLoanFileRepository().findByRelatedIdAndFileType(loan.getId(), fileType);
    if (! CollectionUtils.isEmpty(loanFiles)) {
      LoanFile loanFile = loanFiles.get(0);
      if (loanFile.getSignStatus() == ProcessStatus.SUCCESS) {
        logger.warn("合同已下载, loanId: {}, fileType:{}", loan.getId(), fileType);
        return loanFile;
      }
    }

    // Credit credit = commonService.getCreditRepository().findById(loan.getCreditId()).orElseThrow();

    HXBKFileType cybkFileType = HXBKFileType.getEnumByFileType(fileType);

    HXBKContractGetRequest request = new HXBKContractGetRequest();
    request.setCustomerNo(loan.getAccountId());
    request.setContractType(cybkFileType.getCode());
    // 确定为授信订单号
    request.setOriginalOrderNo(loan.getCreditId());
    logger.info("蚂蚁湖消获取合同信息请求参数, request:{}", JsonUtil.toJsonString(request));
    HXBKContractGetResponse response = requestService.getContract(request);
    logger.info("蚂蚁湖消获取合同信息响应参数, response:{}", JsonUtil.toJsonString(response));
    String fileBase64 = null;
    try {
      fileBase64 = convertOssUrlToBase64(response);
    } catch (Exception e) {
      logger.error("从蚂蚁湖消下载合同出错", e);
      throw e;
    }
    String loanTimeStr = loan.getLoanTime().format(DateTimeFormatter.BASIC_ISO_DATE);
    String ossFilePath = "hxbk/contract/" + loanTimeStr + "/" + loan.getId() + "_" + loan.getLoanNo() + "_" + fileType + ".pdf";

    try {
      fileService.uploadOss(hxbkConfig.getHXBKOssBucket(), ossFilePath, fileBase64);
      LoanFile loanFile = new LoanFile();
      loanFile.setCreditId(loan.getCreditId());
      loanFile.setRelatedId(loan.getId());
      loanFile.setStage(LoanStage.LOAN.name());
      loanFile.setOssBucket(hxbkConfig.getHXBKOssBucket());
      loanFile.setOssKey(ossFilePath);
      loanFile.setChannel(BankChannel.HXBK);
      loanFile.setFileType(fileType);
      loanFile.setFileName(fileType.getDesc());
      loanFile.setSignStatus(ProcessStatus.SUCCESS);
      return commonService.getLoanFileRepository().save(loanFile);
    } catch (Exception e) {
      logger.error("上传借款合同到OSS异常, loanId: {}, ossBucket:{}, ossKey: {}", loan.getId(), hxbkConfig.getHXBKOssBucket(), ossFilePath, e);
      throw new BizException(BizErrorCode.FILE_UPLOAD_ERROR.getCode(), "湖消合同下载失败，Oss上传出错");
    }
  }

  // 获取合同base64
  public String convertOssUrlToBase64(HXBKContractGetResponse response) {
    String fileBase64 = null;
    if ( response == null ) {
      logger.error("HXBKContractGetResponse is null");
      throw new BizException(BizErrorCode.JSON_PROCESSING_ERROR);
    }

    if ( response.getResultCode().equals(HXBKResponseCode.OK.getCode()) && response.getContracts().size() > 0) {
      try {
        fileBase64  = FileService.convertOssUrlToBase64(response.getContracts().get(0).getSavePath());
      } catch ( Exception e ) {
        logger.error("HXBKContractGetResponse 转换base64异常", e);
        throw new RuntimeException(e);
      }

    }
    return fileBase64;
  }


  /**
   * 处理结清证明查询
   * 查询7天内处理中状态的结清证明申请记录，并调用接口查询结果
   */
  @Async("fileProcessThreadPool")
  public void processCreditSettleVoucherQuery() {
    logger.info("开始处理HXBK结清证明查询任务");

    // 查询7天内处理中状态的下载记录
    LocalDateTime startTime = LocalDateTime.now().minusDays(7);

    List<DownloadFileLog> logList = downloadFileLogRepository.findByBankChannelAndStatusAndCreateTime(
            BankChannel.HXBK, DownloadFileStatusEnum.P, startTime);

    logger.info("查询到{}条处理中的结清证明申请记录", logList.size());

    for (DownloadFileLog downloadFileLog : logList) {
      try {
        // 调用开具结清证明接口
        downloadCreditSettleVoucherQuery(downloadFileLog);

      } catch (Exception e) {
        logger.error("处理结清证明查询失败, logId: {}, loanId: {}",
                downloadFileLog.getId(), downloadFileLog.getBizId(), e);
      }
    }

    logger.info("HXBK结清证明查询任务处理完成");
  }

  private FileDownloadResultVo downloadCreditSettleVoucher(FileDownloadVo downloadVo) {
    FileDownloadResultVo resultVo = new FileDownloadResultVo();

    // 先查数据库是否已经下载过了
    LoanFile loanFile = commonService.getLoanFileRepository()
            .findFirstByRelatedIdAndFileTypeAndChannelOrderByCreatedTimeDesc(downloadVo.getLoanId(), downloadVo.getType(), BankChannel.HXBK);

    if (Objects.nonNull(loanFile)) {
      return processFileResult(loanFile);
    } else {
      try {
        // HXBK不需要申请步骤，直接查询结清证明
        logger.info("湖消直连，结清证明下载，直接查询结清证明，loanId:{}", downloadVo.getLoanId());

        // 创建或获取下载任务记录
        DownloadFileLog downloadFileLog = getOrCreateDownloadFileLog(downloadVo.getLoanId());

        // 直接查询结清证明
        downloadCreditSettleVoucherQuery(downloadFileLog);

        // 重新查询已下载文件记录并返回下载链接
        loanFile = commonService.getLoanFileRepository()
                .findFirstByRelatedIdAndFileTypeAndChannelOrderByCreatedTimeDesc(downloadVo.getLoanId(), downloadVo.getType(), BankChannel.HXBK);
        if (Objects.nonNull(loanFile)) {
          return processFileResult(loanFile);
        }

      } catch (Exception e) {
        logger.error("湖消直连结清证明下载失败", e);
      }
    }
    resultVo.setFileStatus(ProcessStatus.PROCESSING);
    return resultVo;
  }

  private DownloadFileLog getOrCreateDownloadFileLog(String loanId) {
    DownloadFileLog downloadFileLog = downloadFileLogRepository.findFirstByBizIdAndFileTypeOrderByCreateTimeDesc(loanId, FileType.CREDIT_SETTLE_VOUCHER_FILE);

    if (Objects.isNull(downloadFileLog) || DownloadFileStatusEnum.F == downloadFileLog.getStatus()) {
      logger.info("湖消直连创建结清证明下载任务记录，loanId:{}", loanId);
      downloadFileLog = new DownloadFileLog();
      downloadFileLog.setBizId(loanId);
      downloadFileLog.setBankChannel(BankChannel.HXBK);
      downloadFileLog.setFileType(FileType.CREDIT_SETTLE_VOUCHER_FILE);
      downloadFileLog.setCreateTime(LocalDateTime.now());
      downloadFileLog.setStatus(DownloadFileStatusEnum.P); // 直接设置为处理中状态
      downloadFileLogRepository.save(downloadFileLog);
    }

    return downloadFileLog;
  }

  /**
   * 下载结清证明查询
   *
   * @param downloadFileLog
   */
  public void downloadCreditSettleVoucherQuery(DownloadFileLog downloadFileLog) {
    String loanId = downloadFileLog.getBizId();
    try {
      Loan loan = commonService.findLoanById(loanId);

      HXBKSettlementCertificateRequest request = new HXBKSettlementCertificateRequest();
      // todo 待确定OrderNo填什么
      request.setOrderNo(loan.getId());
      request.setOriginalOrderNo(loan.getCreditId());
      // todo 待确定什么场景
      request.setScene("01");

      logger.info("湖消直连结清证明文件,查询下载 请求: [{}]", JsonUtil.toJsonString(request));
      // 请求湖消直连
      HXBKSettlementCertificateResponse response = requestService.settlementCertificateQuery(request);
      logger.info("湖消直连结清证明文件,查询下载 响应: [{}]", JsonUtil.toJsonString(response));

      if (response != null && HXBKResponseCode.isOk(response.getResultCode()) && !CollectionUtils.isEmpty(response.getCertificateInfoList())) {
        HXBKSettlementCertificateResponse.CertificateInfo certificateInfo = response.getCertificateInfoList().get(0);
        switch (certificateInfo.getStatus()) {
          case "0" -> processCertificateFile(certificateInfo, downloadFileLog, loan);
          case "1" -> updateDownloadLogFail(downloadFileLog, "无结清证明");
          case "2" -> logger.info("湖消直连结清证明申请处理中，20分钟后再试，本次结束，loanId: [{}]", loan.getId());
          case "3" -> updateDownloadLogFail(downloadFileLog, "暂不支持开具结清证明");
          default ->
                  logger.error("湖消直连结清证明,结果查询失败，loanId:[{}]，resp：[{}]", loanId, JsonUtil.toJsonString(response));
        }
      } else {
        updateDownloadLogFail(downloadFileLog, "接口调用失败或响应为空");
      }
    } catch (Exception e) {
      logger.error("湖消直连结清证明申请结果查询失败，loanId: [{}]", loanId, e);
    }
  }


  private void processCertificateFile(HXBKSettlementCertificateResponse.CertificateInfo certificateInfo,
                                      DownloadFileLog downloadFileLog, Loan loan) {
    try {
      if (StringUtils.hasText(certificateInfo.getCertificateBase64())) {
        // Base64方式保存文件
        logger.info("湖消直连通过Base64保存结清证明文件, loanId: {}", loan.getId());
        saveFromBase64(downloadFileLog, loan, certificateInfo.getCertificateBase64());
      } else if (StringUtil.isNotBlank(certificateInfo.getCertificateUrl())) {
        // URL方式下载文件
        logger.info("湖消直连通过URL下载结清证明文件, loanId: {}, url: {}", loan.getId(), certificateInfo.getCertificateUrl());
        downloadFromUrl(downloadFileLog, loan, certificateInfo.getCertificateUrl());
      } else {
        logger.warn("湖消直连结清证明文件信息为空, loanId: {}", loan.getId());
        updateDownloadLogFail(downloadFileLog, "证明文件信息为空");
      }
    } catch (Exception e) {
      logger.error("湖消直连处理结清证明文件失败, loanId: {}", loan.getId(), e);
      updateDownloadLogFail(downloadFileLog, "文件处理失败: " + e.getMessage());
    }
  }


  /**
   * 更新下载日志为失败状态
   */
  private void downloadFromUrl(DownloadFileLog downloadFileLog, Loan loan, String certificateUrl) throws IOException {
    // TODO: 实现从URL下载文件的逻辑
    // 1. 从URL下载文件到临时文件
    // 2. 上传到OSS
    // 3. 保存LoanFile记录
    // 4. 更新下载日志状态
    logger.warn("湖消直连URL下载功能待实现, loanId: {}, url: {}", loan.getId(), certificateUrl);
    updateDownloadLogFail(downloadFileLog, "URL下载功能待实现");
  }

  private void saveFromBase64(DownloadFileLog downloadFileLog, Loan loan, String certificateBase64) throws IOException {
    try {
      // 解码Base64数据
      byte[] fileData = java.util.Base64.getDecoder().decode(certificateBase64);

      // 生成OSS文件路径
      String dateTimeStr = LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE);
      String ossFilePath = "hxbk/voucher/" + dateTimeStr + "/" + loan.getId() + "_clear.pdf";

      // 上传到OSS
      try (InputStream is = new java.io.ByteArrayInputStream(fileData)) {
        fileService.uploadOss(hxbkConfig.getHXBKOssBucket(), ossFilePath, is);

        // 保存LoanFile记录
        saveLoanFile(downloadFileLog, loan.getCreditId(), FileType.CREDIT_SETTLE_VOUCHER_FILE, ossFilePath);

        logger.info("湖消直连Base64结清证明文件保存成功, loanId: {}, ossPath: {}", loan.getId(), ossFilePath);
      }
    } catch (Exception e) {
      logger.error("湖消直连Base64文件保存失败, loanId: {}", loan.getId(), e);
      updateDownloadLogFail(downloadFileLog, "Base64文件保存失败: " + e.getMessage());
      throw e;
    }
  }

  private void saveLoanFile(DownloadFileLog downloadFileLog, String creditId, FileType fileType, String ossFilePath) {
    String loanId = downloadFileLog.getBizId();

    LoanFile voucherFile = new LoanFile();
    voucherFile.setRelatedId(loanId);
    voucherFile.setCreditId(creditId);
    voucherFile.setChannel(BankChannel.HXBK);
    voucherFile.setOssBucket(hxbkConfig.getHXBKOssBucket());
    voucherFile.setOssKey(ossFilePath);
    voucherFile.setSignStatus(ProcessStatus.SUCCESS);
    voucherFile.setStage(getStage(fileType));
    voucherFile.setFileType(fileType);
    voucherFile.setFileName(fileType.getDesc());

    // 检查是否已存在文件记录
    LoanFile existingFile = commonService.getLoanFileRepository()
            .findFirstByRelatedIdAndFileTypeAndChannelOrderByCreatedTimeDesc(loanId, fileType, BankChannel.HXBK);

    if (existingFile == null) {
      commonService.getLoanFileRepository().save(voucherFile);
    }

    // 更新下载日志状态为成功
    downloadFileLog.setStatus(DownloadFileStatusEnum.S);
    downloadFileLog.setUpdateTime(LocalDateTime.now());
    downloadFileLogRepository.save(downloadFileLog);
  }

  private void updateDownloadLogFail(DownloadFileLog downloadFileLog, String errorMessage) {
    downloadFileLog.setStatus(DownloadFileStatusEnum.F);
    downloadFileLog.setRemark(errorMessage);
    downloadFileLog.setUpdateTime(LocalDateTime.now());
    downloadFileLogRepository.save(downloadFileLog);
  }

  /**
   * 处理文件下载结果
   *
   * @param loanFile
   * @return
   */
  private FileDownloadResultVo processFileResult(LoanFile loanFile) {
    FileDownloadResultVo resultVo = new FileDownloadResultVo();
    resultVo.setOssBucket(loanFile.getOssBucket());
    resultVo.setOssPath(loanFile.getOssKey());
    resultVo.setFileName(loanFile.getFileName());
    resultVo.setSignStatus(loanFile.getSignStatus());
    resultVo.setFileStatus(ProcessStatus.SUCCESS);
    String ossUrl = fileService.getOssUrl(loanFile.getOssBucket(), loanFile.getOssKey());
    // 获取文件后缀名
    String suffixName = loanFile.getOssKey().substring(loanFile.getOssKey().lastIndexOf(DOT) + 1);
    resultVo.setFileUrl(ossUrl);
    resultVo.setFileName(loanFile.getFileName() + DOT + suffixName);
    return resultVo;
  }

  /**
   * 是否支持该资方渠道
   * @param channel 资方渠道
   * @return 支持情况
   */
  @Override
  public boolean isSupport( BankChannel channel ) {
    return BankChannel.HXBK == channel;
  }

  @Autowired
  private BankRepayRecordRepository bankRepayRecordRepository;
  @Autowired
  private HXBKImageFileService hxbkImageFileService;
  @Autowired
  private LoanRepository loanRepository;

  @Override
  public RepayReturnUploadResultDto offlineRepayReturnFileUpload(RepayReturnUploadVo applyVo) {
    // 获取昨天还款成功的还款记录-db
    LocalDate actualRepayDate = applyVo.getActualRepayDate();
    LocalDateTime repayStartDate = actualRepayDate.atTime(0, 0, 0, 0);
    LocalDateTime repayEndDate = actualRepayDate.atTime(23, 59, 59, 999);
    ProcessStatus status = applyVo.getRepayStatus();
    RepayMode repayMode =  applyVo.getRepayMode() ;
    List<BankRepayRecord> bankRepayRecords = bankRepayRecordRepository
            .getSucceedRepayRecords(applyVo.getBankChannel(), repayMode,repayStartDate, repayEndDate,status );

    logger.info("回盘文件查询数据库条数：{}",bankRepayRecords.size());
    RepayReturnUploadResultDto resultDto = new RepayReturnUploadResultDto();
    HXBKRepayReturnFileDTO dto = new HXBKRepayReturnFileDTO();

    if (CollectionUtils.isEmpty(bankRepayRecords)){
      resultDto.setUploadNumber(0);
      resultDto.setStatus(com.jinghang.capital.api.dto.ProcessStatus.SUCCESS);
      return resultDto;
    }
    try {
      dto = generateOutputStream(bankRepayRecords);
      resultDto.setUploadNumber(dto.getSize());
      // 上传到sftp
      hxbkImageFileService.uploadOfflineRepayReturnFile(dto,actualRepayDate);
    } catch (Exception e) {
      logger.error("线下还款回盘文件 date:{},channel:{} 上传异常！{}",applyVo.getActualRepayDate(),applyVo.getBankChannel(),e.getMessage());
      resultDto.setStatus(com.jinghang.capital.api.dto.ProcessStatus.FAIL);
      resultDto.setFailMsg(e.getMessage());
      return resultDto;
    }
    resultDto.setStatus(com.jinghang.capital.api.dto.ProcessStatus.SUCCESS);
    return resultDto;

  }

  private HXBKRepayReturnFileDTO generateOutputStream(List<BankRepayRecord> bankRepayRecords) throws IOException {

    HXBKRepayReturnFileDTO dto = new HXBKRepayReturnFileDTO();

    String[] headers = {
            "contract_no", "seq_no", "term_no", "repay_amt_type", "repay_date", "repay_amt",
            "paid_prin_amt", "paid_int_amt", "paid_guar_int_amt", "paid_ovd_prin_pnlt_amt", "paid_ovd_int_pnlt_amt",
            "paid_ovd_guar_int_pnlt_amt", "paid_breach_amt", "other_data", "bsn_type"
    };

    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
    BufferedWriter bw = new BufferedWriter(writer);

    // 写表头
    bw.write(String.join(",", headers));
    bw.newLine();

    Integer count = 0;
    // 写数据（你替换的逻辑）
    for (BankRepayRecord bankRepayRecord : bankRepayRecords) {
      Loan loan = loanRepository.findById(bankRepayRecord.getLoanId()).orElse(new Loan());
      bw.write(String.join(",", Arrays.asList(
              safe(loan.getLoanContractNo()),
              safe(bankRepayRecord.getId()),
              safe(BigDecimal.valueOf(bankRepayRecord.getPeriod())),
              safe(HXBKRepayAmtType.OFFLINE.getCode()),
              safe(bankRepayRecord,bankRepayRecord.getRepayTime()),
              safeAndConvert(bankRepayRecord.getTotalAmt()),
              safeAndConvert(bankRepayRecord.getPrincipalAmt()),
              safeAndConvert(bankRepayRecord.getInterestAmt()),
              safeAndConvert(bankRepayRecord.getGuaranteeAmt()),
              safeAndConvert(bankRepayRecord.getPenaltyAmt()),
              BigDecimal.valueOf(0).toString(),
              "",
              safeAndConvert(bankRepayRecord.getBreachAmt()),
              "",
              "D301")));
      count++;
      bw.newLine();
    }
    dto.setSize(count);


    String fileName = "repay_instmnt_detail_" + IdGenUtil.genReqNo("ret", 20);
    String checkFileName = "check_repay_instmnt_detail_" + IdGenUtil.genReqNo("chk", 20);
    Integer size = dto.getSize();
    ByteArrayOutputStream checkStream = getCheckFileStream(fileName,size);
    dto.setCheckOutputStream(checkStream);
    dto.setFileName(fileName);
    dto.setCheckFileName(checkFileName);
    dto.setFileOutputStream(outputStream);

    bw.flush();
    return dto;
  }

  private String safe(BigDecimal valueOf) {
    return valueOf == null ? "" : valueOf.toString();
  }


  private String safe(BankRepayRecord repayRecord,LocalDateTime repayTime) {
    if (null == repayTime){
      return repayRecord.getCreatedTime().toLocalDate().toString();
    }else{
      return repayTime.toLocalDate().toString();
    }

  }
  private static String yuanToFen(BigDecimal yuan){
    return yuan.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toString();
  }

  // 处理 null 的字段
  private static String safe(String val) {
    return val == null ? "" : val;
  }


  private static String safeAndConvert( BigDecimal val ) {
    return null  == val ? "" : yuanToFen(val);
  }

}
