package com.jinghang.capital.core.banks.cybk.dto.credit;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

public class CYBKUserInfo {

    /**
     * 婚姻状况
     */
    private String marriage;

    /**
     * 国籍
     */
    private String country = "01";

    /**
     * 最高学历
     */
    private String eduLevel;
    /**
     * 最高学位
     */

    private String eduDegree;

    /**
     * 职业
     */
    private String occupation;


    /**
     * 居住状况
     */
    private String residCase;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private LocalDateTime applyTime;


    /**
     * 居住电话
     */
    private String homeTel;

    /**
     * 居住地址-省市区划代码
     */
    private String homeArea;

    /**
     * 居住地址-详细
     */
    private String homeAddr;

    /**
     * 户籍地址-省市区代码
     */
    private String registerArea;

    /**
     * 户籍地址-详细
     */
    private String registerAddr;

    /**
     * 通讯地址-省市区
     */
    private String commArea;

    /**
     * 通讯地址-详细
     */
    private String commAddr;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 毕业学校
     */
    private String graduateSchoolName;

    /**
     * 个人月收入
     * <p></p>
     * 1 <5000
     * 2 5000-7500
     * 3 7500-10000
     * 4 10000 -15000
     * 5 15000-20000
     * 6 20000-30000
     * 7 30000-40000
     * 8 40000-50000
     * 9 50000-100000
     * 10 >100000
     */
    private String monthIncome;

    /**
     * 个人年收入
     */
    private String yearIncome;
    /**
     * 家庭年收入
     */
    private String familyYearIncome;
    /**
     * 是否农户
     * 0 否 1是
     */
    private String isFarmer;
    /**
     * 是否农户放款
     * 0 否 1是
     */
    private String isFarmerLoan;


    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getEduLevel() {
        return eduLevel;
    }

    public void setEduLevel(String eduLevel) {
        this.eduLevel = eduLevel;
    }

    public String getEduDegree() {
        return eduDegree;
    }

    public void setEduDegree(String eduDegree) {
        this.eduDegree = eduDegree;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getResidCase() {
        return residCase;
    }

    public void setResidCase(String residCase) {
        this.residCase = residCase;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public String getHomeTel() {
        return homeTel;
    }

    public void setHomeTel(String homeTel) {
        this.homeTel = homeTel;
    }

    public String getHomeArea() {
        return homeArea;
    }

    public void setHomeArea(String homeArea) {
        this.homeArea = homeArea;
    }

    public String getHomeAddr() {
        return homeAddr;
    }

    public void setHomeAddr(String homeAddr) {
        this.homeAddr = homeAddr;
    }

    public String getRegisterArea() {
        return registerArea;
    }

    public void setRegisterArea(String registerArea) {
        this.registerArea = registerArea;
    }

    public String getRegisterAddr() {
        return registerAddr;
    }

    public void setRegisterAddr(String registerAddr) {
        this.registerAddr = registerAddr;
    }

    public String getCommArea() {
        return commArea;
    }

    public void setCommArea(String commArea) {
        this.commArea = commArea;
    }

    public String getCommAddr() {
        return commAddr;
    }

    public void setCommAddr(String commAddr) {
        this.commAddr = commAddr;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getGraduateSchoolName() {
        return graduateSchoolName;
    }

    public void setGraduateSchoolName(String graduateSchoolName) {
        this.graduateSchoolName = graduateSchoolName;
    }

    public String getMonthIncome() {
        return monthIncome;
    }

    public void setMonthIncome(String monthIncome) {
        this.monthIncome = monthIncome;
    }

    public String getYearIncome() {
        return yearIncome;
    }

    public void setYearIncome(String yearIncome) {
        this.yearIncome = yearIncome;
    }

    public String getFamilyYearIncome() {
        return familyYearIncome;
    }

    public void setFamilyYearIncome(String familyYearIncome) {
        this.familyYearIncome = familyYearIncome;
    }

    public String getIsFarmer() {
        return isFarmer;
    }

    public void setIsFarmer(String isFarmer) {
        this.isFarmer = isFarmer;
    }

    public String getIsFarmerLoan() {
        return isFarmerLoan;
    }

    public void setIsFarmerLoan(String isFarmerLoan) {
        this.isFarmerLoan = isFarmerLoan;
    }
}
