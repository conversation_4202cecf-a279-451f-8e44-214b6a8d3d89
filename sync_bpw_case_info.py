# 监听case_info，并同步数据到es
# 增删改 case_info 需要更新 case_info、case_repayment
# 增改 case_info 需要更新 case_operation、case_operation_use_less
import json
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from apscheduler.schedulers import background as bg

from datasync.base_data_to_es import DataToEs
from datasync.datetime_to_json import DateEncoder
from es_query_builder import *
from utils.logger_util import LOG
import decimal
import uuid


task_topic = "bpw_anmi_case_info"

es_index = "bpw_case_info_query"
es_index_repayment = "bpw_case_repayment_query"
es_index_operation = "bpw_case_operation_query"
es_index_operation_use_less = "bpw_case_operation_query_use_less"
es_type = "_doc"

logger = LOG.get_logger("sync_bpw_case_info")

sync_util = DataToEs(task_topic)

# 查询转移sql
transerDataSqlMeta = "SELECT ca.id,ca.id_card,ca.create_time,ca.update_time,ca.create_by,ca.update_by," \
                         "ca.out_serial_no,ca.serial_no,ca.NAME,ca.oper_status,ca.overdue_date,ca.overdue_days," \
                         "ca.entrust_start_time,ca.entrust_end_time,ca.amount,ca.DESC,ca.case_status,ca.field_json," \
                         "ca.call_status,ca.allot_status,ca.repair_status,ca.recovery,ca.operation_state," \
                         "ca.last_follow_time,ca.follow_count,ca.sync_status,ca.operation_next_time," \
                         "ca.out_serial_temp,ca.pay_amount,ca.return_time,ca.own_mobile,ca.division_time,ca.color," \
                         "ca.ignore_plan,ca.cooperation_status,ca.ptp_time,ca.ptp_amount,ca.delay_time,ca.product_id," \
                         "ca.org_delt_id,ca.user_id,ca.dep_id,ca.team_id,ca.store_id,ca.org_id,ca.out_batch_id," \
                         "ca.inner_batch_id,debt_id,ca.vsearch_key1,ca.vsearch_key2,ca.auto_assist_result," \
                         "ca.auto_assist_date,ca.tag,ca.auto_assist_record,ca.call_type,ca.case_tag_id," \
                         "ca.auto_recovery_date,ca.end_time,ca.ctrl_id,ca.warning,ca.end_type,ca.end_config_id," \
                         "ca.allot_agent,ca.allot_agent_state,ca.way_allot_state,ca.important," \
                         "ca.recycle_flag,ca.outsource_count,ca.way_update_date,ca.pre_dep_id,ca.stop_date,ca.recycle_date," \
                         "ca.is_visit,ca.visitor_id," \
                         "ifnull(deb.last_follow_time,ca.last_follow_time) AS debt_last_follow_time," \
                         "ifnull(deb.follow_count,ca.follow_count) AS debt_follow_count," \
                         "deb.type AS conjoint_type,p.type AS product_type,ur.NAME AS user_name," \
                         "ur.STATUS AS user_status," \
                         "ifnull(dept.is_agent,team.is_agent) AS is_agent " \
                         "FROM case_info AS ca " \
                         "INNER JOIN product p ON p.id=ca.product_id " \
                         "LEFT JOIN `user` AS ur ON ur.id=ca.user_id AND ur.STATUS=0 " \
                         "LEFT JOIN case_debtor deb ON deb.id=ca.debt_id AND deb.STATUS=0 " \
                         "LEFT JOIN org_dep_team dept ON dept.id=ca.dep_id " \
                         "LEFT JOIN org_dep_team team ON team.id=ca.team_id " \
                         "WHERE ca.id IN %s"

selectTagSqlMeta = "SELECT ctr.id,ctr.tag_id,ctr.case_id,ct.NAME,ct.color,ct.state " \
                   "FROM case_tag_rel ctr " \
                   "INNER JOIN case_info ca ON ctr.org_id=ca.org_id AND ctr.case_id=ca.id " \
                   "INNER JOIN case_tag ct ON ctr.tag_id=ct.id " \
                   "WHERE ca.id IN %s"

transerDataRepaymentSqlMeta = "SELECT rep.type,rep.id,rep.out_serial_no,rep.debt_name,rep.debt_id_card," \
                                  "rep.repayment_time,rep.repayment_amount,rep.repayment_type,rep.repayment_card_no," \
                                  "rep.repayment_mobile,rep.STATUS,rep.create_by,rep.update_by,rep.create_time," \
                                  "rep.update_time,rep.DESC,rep.case_operator,rep.case_operator_name," \
                                  "rep.apply_status,rep.voucher_url,rep.apply_desc,rep.repayment_from," \
                                  "rep.repayment_style,rep.case_id,rep.audit_by,rep.org_id,rep.dep_id,rep.team_id," \
                                  "rep.audit_time,ca.product_id,rep.out_serial_temp,ca.user_id,rep.org_delt_id," \
                                  "rep.allot_agent,rep.ext1,rep.ext2,rep.ext3,rep.ext4,rep.ext5,rep.ext6,rep.ext7," \
                                  "rep.ext8,rep.ext9,rep.ext10,rep.flow_id," \
                                  "rep.ext11,rep.ext12,rep.ext13,rep.ext14,rep.ext15,rep.ext16,rep.ext17," \
                                  "rep.ext18,rep.ext19,rep.ext20,rep.ext21,rep.ext22," \
                                  "ca.out_batch_id,ca.inner_batch_id,ca.amount,ca.entrust_start_time," \
                                  "ca.entrust_end_time,ca.case_status AS case_status,ca.allot_status AS case_allot_status,ca.recovery," \
                                  "ca.own_mobile " \
                                  "FROM case_repayment rep " \
                                  "LEFT JOIN case_info ca ON rep.case_id=ca.id AND ca.recovery<>-2 " \
                                  "WHERE rep.case_id IN %s"

transerDataOperationSqlMeta = "SELECT cao.create_time,cao.action_type,cao.call_type,cao.reduce_amount," \
                     "cao.DESC,cao.ptp_amount,cao.ptp_time,cao.next_time,cao.id,cao.create_by," \
                     "cao.update_by,cao.call_uuid,cao.call_style,cao.call_time,cao.ring_durtion," \
                     "cao.call_durtion,cao.outcome,cao.submit_type,cao.caller,cao.create_type," \
                     "cao.con_mobile,cao.con_name,cao.relation_type,cao.operator_name,cao.STATUS," \
                     "cao.tag,cao.operation_state,cao.is_hidden,cao.case_id,cao.org_id,cao.org_delt_id," \
                     "cao.callback_flag,cao.site_id,cao.site_name,cao.oper_time,cao.pool_id," \
                     "cao.out_serial_no,cao.out_serial_temp,cao.COMMENT,cao.admin_submitter,cao.client_label_id," \
                     "cao.total_oper_time,cao.is_push,cao.intention,cao.intention_name," \
                     "ca.product_id,ca.user_id,ca.team_id,ca.dep_id,ca.out_batch_id," \
                     "ca.inner_batch_id,ca.NAME,ca.id_card,ca.own_mobile," \
                     "batch.NAME AS out_batch_no " \
                     "FROM `case_operation` cao " \
                     "LEFT JOIN `case_info` ca ON cao.case_id=ca.id AND ca.recovery<>-2 " \
                     "LEFT JOIN `out_batch` batch ON ca.out_batch_id=batch.id WHERE cao.case_id in %s"

transerDataOperationUseLessSqlMeta = "SELECT caoul.create_time,caoul.action_type,caoul.call_type,caoul.reduce_amount," \
                     "caoul.DESC,caoul.ptp_amount,caoul.ptp_time,caoul.next_time,caoul.id,caoul.create_by," \
                     "caoul.update_by,caoul.call_uuid,caoul.call_style,caoul.call_time,caoul.ring_durtion," \
                     "caoul.call_durtion,caoul.outcome,caoul.submit_type,caoul.caller,caoul.create_type," \
                     "caoul.con_mobile,caoul.con_name,caoul.relation_type,caoul.operator_name,caoul.STATUS," \
                     "caoul.tag,caoul.operation_state,caoul.is_hidden,caoul.case_id,caoul.org_id,caoul.org_delt_id," \
                     "caoul.callback_flag,caoul.site_id,caoul.site_name,caoul.oper_time,caoul.pool_id," \
                     "caoul.out_serial_no,caoul.out_serial_temp,caoul.COMMENT,caoul.admin_submitter,caoul.client_label_id," \
                     "caoul.total_oper_time, caoul.is_push,caoul.intention,caoul.intention_name,"\
                     "ca.product_id,ca.user_id,ca.team_id,ca.dep_id,ca.out_batch_id," \
                     "ca.inner_batch_id,ca.NAME,ca.id_card,ca.own_mobile," \
                     "batch.NAME AS out_batch_no " \
                     "FROM `case_operation_use_less` caoul " \
                     "LEFT JOIN `case_info` ca ON caoul.case_id=ca.id AND ca.recovery<>-2 " \
                     "LEFT JOIN `out_batch` batch ON ca.out_batch_id=batch.id WHERE caoul.case_id in %s"

# 催收方式
operationWaySql = "SELECT case_id,operation_way FROM case_operation_way_rel WHERE case_id IN %s "

def main():
    partitions = sync_util.kafka_consumer.get_partitions()
    partitions_size = len(partitions)
    thread_pool = ThreadPoolExecutor(max_workers=partitions_size, thread_name_prefix="sync_bpw_case_info")
    futures = []
    for partition in partitions:
        future = thread_pool.submit(run, partition)
        futures.append(future)
    for future in futures:
        future.result()


def run(partition):
    while True:
        try:
            thread_name = threading.current_thread().name
            start_offset, end_offset, bin_log_datas = sync_util.get_bin_log_data_by_partition(partition)
            if bin_log_datas:
                logger.info(f'partition:{partition}消费到binlog数据 >>>')
                insert_or_update_list = []
                delete_list = []
                action_list = []
                for bin_log_data in bin_log_datas:
                    logger.info(f'{thread_name}消费到binlog数据 >>> {bin_log_data}')
                    json_bin_log_data = json.loads(bin_log_data)

                    log_type = json_bin_log_data.get('type')
                    if log_type == 'INSERT' or log_type == 'UPDATE':
                        case_info_list = json_bin_log_data.get('data')
                        for case_info in case_info_list:
                            case_info_id = int(case_info["id"])
                            insert_or_update_list.append(case_info_id)
                    elif log_type == 'DELETE':
                        case_info_list = json_bin_log_data.get('data')
                        for case_info in case_info_list:
                            case_info_id = int(case_info["id"])
                            delete_list.append(case_info_id)

                if len(insert_or_update_list) > 0:
                    action_list.extend(build_insert_or_update_action_list(insert_or_update_list))
                    action_list.extend(build_repayment(insert_or_update_list))
                    action_list.extend(build_operation(insert_or_update_list, False))
                    action_list.extend(build_operation(insert_or_update_list, True))

                if len(delete_list) > 0:
                    action_list.extend(build_delete_action_list(delete_list))
                    action_list.extend(build_repayment(delete_list))

                if len(action_list) > 0:
                    sync_util.es_bulk(action_list)
            else:
                time.sleep(1)
            #logger.info("<<<<<<<<<<<<<<<<<<<<" + thread_name + " == sync_case_info>>>>>>>>>>>>>>>>>>>>")
        except Exception as e:
            logger.exception(e)


def build_operation(insert_or_update_list, use_less):
    transerSql = transerDataOperationSqlMeta
    if use_less:
        transerSql = transerDataOperationUseLessSqlMeta

    case_operations = sync_util.db.select_by_param(transerSql, (insert_or_update_list,))

    result_list = []
    if case_operations:
        for case_operation in case_operations:
            result_list.append(build_source_operation(case_operation, use_less))
    return result_list


def build_source_operation(case_operation, use_less):
    case_operation_id = case_operation["id"]
    source = {}

    if case_operation_id is not None:
        source["id"] = case_operation_id

    create_time = case_operation["create_time"]
    if create_time is not None:
        source["createTime"] = create_time

    action_type = case_operation["action_type"]
    if action_type is not None:
        source["actionType"] = action_type

    call_type = case_operation["call_type"]
    if call_type is not None:
        source["callType"] = call_type

    reduce_amount = case_operation["reduce_amount"]
    if reduce_amount is not None:
        source["reduceAmount"] = reduce_amount

    desc = case_operation["DESC"]
    if desc is not None:
        source["desc"] = desc

    ptp_amount = case_operation["ptp_amount"]
    if ptp_amount is not None:
        source["ptpAmount"] = ptp_amount

    ptp_time = case_operation["ptp_time"]
    if ptp_time is not None:
        source["ptpTime"] = ptp_time

    next_time = case_operation["next_time"]
    if next_time is not None:
        source["nextTime"] = next_time

    id = case_operation["id"]
    if id is not None:
        source["id"] = id

    create_by = case_operation["create_by"]
    if create_by is not None:
        source["createBy"] = create_by

    update_by = case_operation["update_by"]
    if update_by is not None:
        source["updateBy"] = update_by

    call_uuid = case_operation["call_uuid"]
    if call_uuid is not None:
        source["callUuid"] = call_uuid

    call_style = case_operation["call_style"]
    if call_style is not None:
        source["callStyle"] = call_style

    call_time = case_operation["call_time"]
    if call_time is not None:
        source["callTime"] = call_time

    ring_durtion = case_operation["ring_durtion"]
    if ring_durtion is not None:
        source["ringDurtion"] = ring_durtion

    call_durtion = case_operation["call_durtion"]
    if call_durtion is not None:
        source["callDurtion"] = call_durtion

    outcome = case_operation["outcome"]
    if outcome is not None:
        source["outcome"] = outcome

    submit_type = case_operation["submit_type"]
    if submit_type is not None:
        source["submitType"] = submit_type

    caller = case_operation["caller"]
    if caller is not None:
        source["caller"] = caller

    create_type = case_operation["create_type"]
    if create_type is not None:
        source["createType"] = create_type

    con_mobile = case_operation["con_mobile"]
    if con_mobile is not None:
        source["conMobile"] = con_mobile

    con_name = case_operation["con_name"]
    if con_name is not None:
        source["conName"] = con_name

    relation_type = case_operation["relation_type"]
    if relation_type is not None:
        source["relationType"] = relation_type

    operator_name = case_operation["operator_name"]
    if operator_name is not None:
        source["operatorName"] = operator_name

    status = case_operation["STATUS"]
    if status is not None:
        source["status"] = status

    tag = case_operation["tag"]
    if tag is not None:
        source["tag"] = tag

    operation_state = case_operation["operation_state"]
    if operation_state is not None:
        source["operationState"] = operation_state

    is_hidden = case_operation["is_hidden"]
    if is_hidden is not None:
        source["isHidden"] = is_hidden

    case_id = case_operation["case_id"]
    if case_id is not None:
        source["caseId"] = case_id

    org_id = case_operation["org_id"]
    if org_id is not None:
        source["orgId"] = org_id

    org_delt_id = case_operation["org_delt_id"]
    if org_delt_id is not None:
        source["orgDeltId"] = org_delt_id

    out_serial_no = case_operation["out_serial_no"]
    if out_serial_no is not None:
        source["outSerialNo"] = out_serial_no

    out_serial_no_search = case_operation["out_serial_temp"]
    if out_serial_no_search is not None:
        source["outSerialNoSearch"] = out_serial_no_search

    comment = case_operation["COMMENT"]
    if comment is not None:
        source["comment"] = comment

    admin_submitter = case_operation["admin_submitter"]
    if admin_submitter is not None:
        source["adminSubmitter"] = admin_submitter

    client_label_id = case_operation["client_label_id"]
    if client_label_id is not None:
        source["clientLabelId"] = client_label_id

    product_id = case_operation["product_id"]
    if product_id is not None:
        source["productId"] = product_id

    user_id = case_operation["user_id"]
    if user_id is not None:
        source["userId"] = user_id

    team_id = case_operation["team_id"]
    if team_id is not None:
        source["teamId"] = team_id

    dep_id = case_operation["dep_id"]
    if dep_id is not None:
        source["depId"] = dep_id

    out_batch_id = case_operation["out_batch_id"]
    if out_batch_id is not None:
        source["outBatchId"] = out_batch_id

    inner_batch_id = case_operation["inner_batch_id"]
    if inner_batch_id is not None:
        source["innerBatchId"] = inner_batch_id

    name = case_operation["NAME"]
    if name is not None:
        source["name"] = name

    id_card = case_operation["id_card"]
    if id_card is not None:
        source["idCard"] = id_card

    own_mobile = case_operation["own_mobile"]
    if own_mobile is not None:
        source["ownMobile"] = own_mobile

    out_batch_no = case_operation["out_batch_no"]
    if out_batch_no is not None:
        source["outBatchNo"] = out_batch_no

    callback_flag = case_operation["callback_flag"]
    if callback_flag is not None:
        source["callbackFlag"] = callback_flag

    site_id = case_operation["site_id"]
    if site_id is not None:
        source["siteId"] = site_id

    site_name = case_operation["site_name"]
    if site_name is not None:
        source["siteName"] = site_name

    oper_time = case_operation["oper_time"]
    if oper_time is not None:
        source["operTime"] = oper_time

    total_oper_time = case_operation["total_oper_time"]
    if total_oper_time is not None:
        source["totalOperTime"] = total_oper_time

    pool_id = case_operation["pool_id"]
    if pool_id is not None:
        source["poolId"] = pool_id

    is_push = case_operation["is_push"]
    if is_push is not None:
        source["isPush"] = is_push

    intention = case_operation["intention"]
    if intention is not None:
        source["intention"] = intention

    intention_name = case_operation["intention_name"]
    if intention_name is not None:
        source["intentionName"] = intention_name

    source_json = json.dumps(source, cls=DateEncoder)
    logger.info(f'case_operation_source:{source_json} >>>')

    operation_es_index = es_index_operation
    if use_less:
        operation_es_index = es_index_operation_use_less

    return {
        "_index": operation_es_index,
        "_type": es_type,
        "_id": case_operation_id,
        "_source": source_json
    }


def build_repayment(insert_or_update_list):
    case_repayments = sync_util.db.select_by_param(transerDataRepaymentSqlMeta, (insert_or_update_list,))

    result_list = []
    if case_repayments:
        for case_repayment in case_repayments:
            result_list.append(build_source_repayment(case_repayment))
    return result_list


def build_source_repayment(case_repayment):
    case_repayment_id = case_repayment["id"]
    source = {}

    if case_repayment_id is not None:
        source["id"] = case_repayment_id

    type = case_repayment["type"]
    if type is not None:
        source["type"] = type

    out_serial_no = case_repayment["out_serial_no"]
    if out_serial_no is not None:
        source["outSerialNo"] = out_serial_no
        if "#" in out_serial_no:
            index = out_serial_no.index("#")
            source["outSerialNoSearch"] = out_serial_no[0:index]
        else:
            source["outSerialNoSearch"] = out_serial_no

    debt_name = case_repayment["debt_name"]
    if debt_name is not None:
        source["debtName"] = debt_name

    debt_id_card = case_repayment["debt_id_card"]
    if debt_id_card is not None:
        source["debtIdCard"] = debt_id_card

    repayment_time = case_repayment["repayment_time"]
    if repayment_time is not None:
        source["repaymentTime"] = repayment_time

    repayment_amount = case_repayment["repayment_amount"]
    if repayment_amount is not None:
        source["repaymentAmount"] = repayment_amount

    repayment_type = case_repayment["repayment_type"]
    if repayment_type is not None:
        source["repaymentType"] = repayment_type

    repayment_card_no = case_repayment["repayment_card_no"]
    if repayment_card_no is not None:
        source["repaymentCardNo"] = repayment_card_no

    repayment_mobile = case_repayment["repayment_mobile"]
    if repayment_mobile is not None:
        source["repaymentMobile"] = repayment_mobile

    status = case_repayment["STATUS"]
    if status is not None:
        source["status"] = status

    create_by = case_repayment["create_by"]
    if create_by is not None:
        source["createBy"] = int(create_by)

    update_by = case_repayment["update_by"]
    if update_by is not None:
        source["updateBy"] = int(update_by)

    create_time = case_repayment["create_time"]
    if create_time is not None:
        source["createTime"] = create_time

    update_time = case_repayment["update_time"]
    if update_time is not None:
        source["updateTime"] = update_time

    desc = case_repayment["DESC"]
    if desc is not None:
        source["desc"] = desc

    case_operator = case_repayment["case_operator"]
    if case_operator is not None:
        source["caseOperator"] = case_operator

    case_operator_name = case_repayment["case_operator_name"]
    if case_operator_name is not None:
        source["caseOperatorName"] = case_operator_name

    apply_status = case_repayment["apply_status"]
    if apply_status is not None:
        source["applyStatus"] = apply_status

    voucher_url = case_repayment["voucher_url"]
    if voucher_url is not None:
        source["voucherUrl"] = voucher_url

    apply_desc = case_repayment["apply_desc"]
    if apply_desc is not None:
        source["applyDesc"] = apply_desc

    repayment_from = case_repayment["repayment_from"]
    if repayment_from is not None:
        source["repaymentFrom"] = repayment_from

    repayment_style = case_repayment["repayment_style"]
    if repayment_style is not None:
        source["repaymentStyle"] = repayment_style

    case_id = case_repayment["case_id"]
    if case_id is not None:
        source["caseId"] = case_id

    audit_by = case_repayment["audit_by"]
    if audit_by is not None:
        source["auditBy"] = audit_by

    org_id = case_repayment["org_id"]
    if org_id is not None:
        source["orgId"] = org_id

    dep_id = case_repayment["dep_id"]
    if dep_id is not None:
        source["depId"] = dep_id

    team_id = case_repayment["team_id"]
    if team_id is not None:
        source["teamId"] = team_id

    audit_time = case_repayment["audit_time"]
    if audit_time is not None:
        source["auditTime"] = audit_time

    product_id = case_repayment["product_id"]
    if product_id is not None:
        source["productId"] = product_id

    out_serial_temp = case_repayment["out_serial_temp"]
    if out_serial_temp is not None:
        source["outSerialTemp"] = out_serial_temp

    user_id = case_repayment["user_id"]
    if user_id is not None:
        source["userId"] = user_id

    org_delt_id = case_repayment["org_delt_id"]
    if org_delt_id is not None:
        source["orgDeltId"] = org_delt_id

    out_batch_id = case_repayment["out_batch_id"]
    if out_batch_id is not None:
        source["outBatchId"] = out_batch_id

    inner_batch_id = case_repayment["inner_batch_id"]
    if inner_batch_id is not None:
        source["innerBatchId"] = inner_batch_id

    amount = case_repayment["amount"]
    if amount is not None:
        source["amount"] = int(amount)

    entrust_start_time = case_repayment["entrust_start_time"]
    if entrust_start_time is not None:
        source["entrustStartTime"] = entrust_start_time

    entrust_end_time = case_repayment["entrust_end_time"]
    if entrust_end_time is not None:
        source["entrustEndTime"] = entrust_end_time

    case_status = case_repayment["case_status"]
    if case_status is not None:
        source["caseStatus"] = case_status

    case_allot_status = case_repayment["case_allot_status"]
    if case_allot_status is not None:
        source["caseAllotStatus"] = case_allot_status

    recovery = case_repayment["recovery"]
    if recovery is not None:
        source["recovery"] = recovery

    own_mobile = case_repayment["own_mobile"]
    if own_mobile is not None:
        source["ownMobile"] = own_mobile
    allot_agent = case_repayment["allot_agent"]
    if allot_agent is not None:
        source["allotAgent"] = allot_agent

    ext1 = case_repayment["ext1"]
    if ext1 is not None:
        source["ext1"] = ext1

    ext2 = case_repayment["ext2"]
    if ext2 is not None:
        source["ext2"] = ext2

    ext3 = case_repayment["ext3"]
    if ext3 is not None:
        source["ext3"] = ext3

    ext4 = case_repayment["ext4"]
    if ext4 is not None:
        source["ext4"] = ext4

    ext5 = case_repayment["ext5"]
    if ext5 is not None:
        source["ext5"] = ext5

    ext6 = case_repayment["ext6"]
    if ext6 is not None:
        source["ext6"] = ext6

    ext7 = case_repayment["ext7"]
    if ext7 is not None:
        source["ext7"] = str(decimal.Decimal(ext7).quantize(decimal.Decimal('0.00')))

    ext8 = case_repayment["ext8"]
    if ext8 is not None:
        source["ext8"] = str(decimal.Decimal(ext8).quantize(decimal.Decimal('0.00')))

    ext9 = case_repayment["ext9"]
    if ext9 is not None:
        source["ext9"] = ext9

    ext10 = case_repayment["ext10"]
    if ext10 is not None:
        source["ext10"] = ext10

    flow_id = case_repayment["flow_id"]
    if flow_id is not None:
        source["flowId"] = flow_id

    ext11 = case_repayment["ext11"]
    if ext11 is not None:
        source["ext11"] = ext11

    ext12 = case_repayment["ext12"]
    if ext12 is not None:
        source["ext12"] = ext12

    ext13 = case_repayment["ext13"]
    if ext13 is not None:
        source["ext13"] = ext13

    ext14 = case_repayment["ext14"]
    if ext14 is not None:
        source["ext14"] = ext14

    ext15 = case_repayment["ext15"]
    if ext15 is not None:
        source["ext15"] = str(decimal.Decimal(ext15).quantize(decimal.Decimal('0.00')))

    ext16 = case_repayment["ext16"]
    if ext16 is not None:
        source["ext16"] = str(decimal.Decimal(ext16).quantize(decimal.Decimal('0.00')))

    ext17 = case_repayment["ext17"]
    if ext17 is not None:
        source["ext17"] = str(decimal.Decimal(ext17).quantize(decimal.Decimal('0.00')))

    ext18 = case_repayment["ext18"]
    if ext18 is not None:
        source["ext18"] = str(decimal.Decimal(ext18).quantize(decimal.Decimal('0.00')))

    ext19 = case_repayment["ext19"]
    if ext19 is not None:
        source["ext19"] = ext19

    ext20 = case_repayment["ext20"]
    if ext20 is not None:
        source["ext20"] = ext20

    ext21 = case_repayment["ext21"]
    if ext21 is not None:
        source["ext21"] = ext21

    ext22 = case_repayment["ext22"]
    if ext22 is not None:
        source["ext22"] = ext22

    source_json = json.dumps(source, cls=DateEncoder)
    logger.info(f'case_repayment_source:{source_json} >>>')

    return {
        "_index": es_index_repayment,
        "_type": es_type,
        "_id": case_repayment_id,
        "_source": source_json
    }


def build_insert_or_update_action_list(insert_or_update_list):
    case_infos = sync_util.db.select_by_param(transerDataSqlMeta, (insert_or_update_list,))

    result_list = []
    if case_infos:
        case_info_ids = list(map(lambda x: x["id"], filter(lambda x: x["id"] is not None, case_infos)))
        tag_rels = []
        operation_way_rels = []
        if case_info_ids:
            tag_rels = sync_util.db.select_by_param(selectTagSqlMeta, (case_info_ids,))
            operation_way_rels = sync_util.db.select_by_param(operationWaySql, (case_info_ids,))

        for case_info in case_infos:
            tags = list(filter(lambda x: x["case_id"] == case_info["id"], tag_rels))
            operation_ways = list(filter(lambda x: x["case_id"] == case_info["id"], operation_way_rels))
            result_list.append(build_common_source(case_info, tags, operation_ways))
    return result_list


def build_delete_action_list(delete_list):
    result_list = []
    for case_info_id in delete_list:
        result_list.append(
            {
                "_op_type": 'delete',
                "_index": es_index,
                "_type": es_type,
                "_id": case_info_id
            }
        )
    return result_list


def build_common_source(case_info, tags, operation_ways):
    case_info_id = case_info["id"]
    source = {}

    if case_info_id is not None:
        source["id"] = case_info_id

    id_card = case_info["id_card"]
    if id_card is not None:
        source["idCard"] = id_card

    out_serial_no = case_info["out_serial_no"]
    if out_serial_no is not None:
        source["outSerialNo"] = out_serial_no
        if "#" in out_serial_no:
            index = out_serial_no.index("#")
            source["outSerialNoSearch"] = out_serial_no[0:index]
        else:
            source["outSerialNoSearch"] = out_serial_no

    create_time = case_info["create_time"]
    if create_time is not None:
        source["createTime"] = create_time

    update_time = case_info["update_time"]
    if update_time is not None:
        source["updateTime"] = update_time

    create_by = case_info["create_by"]
    if create_by is not None:
        source["createBy"] = int(create_by)

    update_by = case_info["update_by"]
    if update_by is not None:
        source["updateBy"] = int(update_by)

    serial_no = case_info["serial_no"]
    if serial_no is not None:
        source["serialNo"] = serial_no

    name = case_info["NAME"]
    if name is not None:
        source["name"] = name

    oper_status = case_info["oper_status"]
    if oper_status is not None:
        source["operStatus"] = oper_status

    overdue_date = case_info["overdue_date"]
    if overdue_date is not None:
        source["overdueDate"] = overdue_date

    overdue_days = case_info["overdue_days"]
    if overdue_days is not None:
        source["overdueDays"] = overdue_days

    entrust_start_time = case_info["entrust_start_time"]
    if entrust_start_time is not None:
        source["entrustStartTime"] = entrust_start_time

    entrust_end_time = case_info["entrust_end_time"]
    if entrust_end_time is not None:
        source["entrustEndTime"] = entrust_end_time

    amount = case_info["amount"]
    if amount is not None:
        source["amount"] = int(amount)

    desc = case_info["DESC"]
    if desc is not None:
        source["desc"] = desc

    case_status = case_info["case_status"]
    if case_status is not None:
        source["caseStatus"] = case_status

    field_json_map = {}
    field_search = []
    field_json = case_info["field_json"]
    if field_json is not None:
        field_json_tem = json.loads(field_json)
        search_key1 = field_json_tem.get("search_key1")
        if search_key1 is not None:
            field_json_map["search_key1"] = search_key1
        search_key2 = field_json_tem.get("search_key2")
        if search_key2 is not None:
            field_json_map["search_key2"] = search_key2
        for key in field_json_tem:
            if key is not None and key != "":
                value = field_json_tem[key]
                if value is not None and value != "":
                    field_search.append(key+"#"+value)

    source["fieldJson"] = field_json_map
    source["fieldSearch"] = field_search

    call_status = case_info["call_status"]
    if call_status is not None:
        source["callStatus"] = call_status

    allot_status = case_info["allot_status"]
    if allot_status is not None:
        source["allotStatus"] = allot_status

    repair_status = case_info["repair_status"]
    if repair_status is not None:
        source["repairStatus"] = repair_status

    recovery = case_info["recovery"]
    if recovery is not None:
        source["recovery"] = recovery

    operation_state = case_info["operation_state"]
    if operation_state is not None:
        source["operationState"] = operation_state

    last_follow_time = case_info["last_follow_time"]
    if last_follow_time is not None:
        source["lastFollowTime"] = last_follow_time

    follow_count = case_info["follow_count"]
    if follow_count is not None:
        source["followCount"] = follow_count

    sync_status = case_info["sync_status"]
    if sync_status is not None:
        source["syncStatus"] = sync_status

    operation_next_time = case_info["operation_next_time"]
    if operation_next_time is not None:
        source["operationNextTime"] = operation_next_time

    out_serial_temp = case_info["out_serial_temp"]
    if out_serial_temp is not None:
        source["outSerialTemp"] = out_serial_temp

    pay_amount = case_info["pay_amount"]
    if pay_amount is not None:
        source["payAmount"] = int(pay_amount)

    return_time = case_info["return_time"]
    if return_time is not None:
        source["returnTime"] = return_time

    own_mobile = case_info["own_mobile"]
    if own_mobile is not None:
        source["ownMobile"] = own_mobile

    division_time = case_info["division_time"]
    if division_time is not None:
        source["divisionTime"] = division_time

    color = case_info["color"]
    if color is not None:
        source["color"] = color

    ignore_plan = case_info["ignore_plan"]
    if ignore_plan is not None:
        source["ignorePlan"] = ignore_plan

    cooperation_status = case_info["cooperation_status"]
    if cooperation_status is not None:
        source["cooperationStatus"] = cooperation_status

    ptp_time = case_info["ptp_time"]
    if ptp_time is not None:
        source["ptpTime"] = ptp_time

    ptp_amount = case_info["ptp_amount"]
    if ptp_amount is not None:
        source["ptpAmount"] = int(ptp_amount)

    delay_time = case_info["delay_time"]
    if delay_time is not None:
        source["delayTime"] = delay_time

    product_id = case_info["product_id"]
    if product_id is not None:
        source["productId"] = product_id

    org_delt_id = case_info["org_delt_id"]
    if org_delt_id is not None:
        source["orgDeltId"] = org_delt_id

    user_id = case_info["user_id"]
    if user_id is not None:
        source["userId"] = user_id

    dep_id = case_info["dep_id"]
    if dep_id is not None:
        source["depId"] = dep_id

    team_id = case_info["team_id"]
    if team_id is not None:
        source["teamId"] = team_id

    store_id = case_info["store_id"]
    if store_id is not None:
        source["storeId"] = store_id

    org_id = case_info["org_id"]
    if org_id is not None:
        source["orgId"] = org_id

    out_batch_id = case_info["out_batch_id"]
    if out_batch_id is not None:
        source["outBatchId"] = out_batch_id

    inner_batch_id = case_info["inner_batch_id"]
    if inner_batch_id is not None:
        source["innerBatchId"] = inner_batch_id

    debt_id = case_info["debt_id"]
    if debt_id is not None:
        source["debtId"] = debt_id

    vsearch_key1 = case_info["vsearch_key1"]
    if vsearch_key1 is not None:
        source["vsearchKey1"] = vsearch_key1

    vsearch_key2 = case_info["vsearch_key2"]
    if vsearch_key2 is not None:
        source["vsearchKey2"] = vsearch_key2

    auto_assist_result = case_info["auto_assist_result"]
    if auto_assist_result is not None:
        source["autoAssistResult"] = auto_assist_result

    auto_assist_date = case_info["auto_assist_date"]
    if auto_assist_date is not None:
        source["autoAssistDate"] = auto_assist_date

    tag = case_info["tag"]
    if tag is not None:
        source["tag"] = tag

    auto_assist_record = case_info["auto_assist_record"]
    if auto_assist_record is not None:
        source["autoAssistRecord"] = auto_assist_record

    call_type = case_info["call_type"]
    if call_type is not None:
        source["callType"] = call_type

    if tags:
        tag_ids = list(map(lambda x: x["tag_id"], filter(lambda x: x["tag_id"] is not None, tags)))
        if tag_ids:
            source["caseTagId"] = tag_ids

    auto_recovery_date = case_info["auto_recovery_date"]
    if auto_recovery_date is not None:
        source["autoRecoveryDate"] = auto_recovery_date

    end_time = case_info["end_time"]
    if end_time is not None:
        source["endTime"] = end_time

    ctrl_id = case_info["ctrl_id"]
    if ctrl_id is not None:
        source["ctrlId"] = ctrl_id

    warning = case_info["warning"]
    if warning is not None:
        source["warning"] = warning

    end_type = case_info["end_type"]
    if end_type is not None:
        source["endType"] = end_type

    end_config_id = case_info["end_config_id"]
    if end_config_id is not None:
        source["endConfigId"] = end_config_id

    debt_last_follow_time = case_info["debt_last_follow_time"]
    if debt_last_follow_time is not None:
        source["debtLastFollowTime"] = debt_last_follow_time

    debt_follow_count = case_info["debt_follow_count"]
    if debt_follow_count is not None:
        source["debtFollowCount"] = debt_follow_count

    conjoint_type = case_info["conjoint_type"]
    if conjoint_type is not None:
        source["conjointType"] = conjoint_type

    product_type = case_info["product_type"]
    if product_type is not None:
        source["productType"] = product_type

    user_name = case_info["user_name"]
    if user_name is not None:
        source["userName"] = user_name

    user_status = case_info["user_status"]
    if user_status is not None:
        source["userStatus"] = user_status

    is_agent = case_info["is_agent"]
    if is_agent is not None:
        source["isAgent"] = is_agent

    allot_agent = case_info["allot_agent"]
    if allot_agent is not None:
        source["allotAgent"] = allot_agent

    allot_agent_state = case_info["allot_agent_state"]
    if allot_agent_state is not None:
        source["allotAgentState"] = allot_agent_state

    way_allot_state = case_info["way_allot_state"]
    if way_allot_state is not None:
        source["wayAllotState"] = way_allot_state

    operation_way_values = list(map(lambda x: x["operation_way"], filter(lambda x: x["operation_way"] is not None, operation_ways)))
    source["operationWay"] = operation_way_values

    important = case_info["important"]
    if important is not None:
        source["important"] = important

    recycle_flag = case_info["recycle_flag"]
    if recycle_flag is not None:
        source["recycleFlag"] = recycle_flag

    outsource_count = case_info["outsource_count"]
    if outsource_count is not None:
        source["outsourceCount"] = outsource_count

    way_update_date = case_info["way_update_date"]
    if way_update_date is not None:
        source["wayUpdateDate"] = way_update_date

    pre_dep_id = case_info["pre_dep_id"]
    if pre_dep_id is not None:
        source["preDepId"] = pre_dep_id

    stop_date = case_info["stop_date"]
    if stop_date is not None:
        source["stopDate"] = stop_date

    recycle_date = case_info["recycle_date"]
    if recycle_date is not None:
        source["recycleDate"] = recycle_date
    is_visit = case_info["is_visit"]
    if is_visit is not None:
        source["isVisit"] = is_visit
    visitor_id = case_info["visitor_id"]
    if visitor_id is not None:
        source["visitorId"] = visitor_id

    source_json = json.dumps(source, cls=DateEncoder)
    logger.info(f'case_info_source:{source_json} >>>')

    return {
        "_index": es_index,
        "_type": es_type,
        "_id": case_info_id,
        "_source": source_json
    }


def heartbeat():
    logger.info("-------sync_case_info_heartbeat-------")


def data_compare():
    """
    数据比对
    """
    try:
        sql_result = sync_util.db.select_one('select count(id) as sql_count from case_info')
        sql_count = sql_result['sql_count']

        _bool = BoolQuery(must_list=[], should_list=[], must_not_list=[])
        _search = Search(query=_bool, limit=0, order_list=[])
        _result = sync_util.es_client.search(index=es_index, body=_search.toMap())
        es_count = _result['hits']['total']['value']

        if sql_count != es_count:
            logger.error(f"-------sync_case_info_data_compare-------sql_count:{sql_count};es_count:{es_count}")
        else:
            logger.info(f"-------sync_case_info_data_compare-------sql_count:{sql_count};es_count:{es_count}")
    except Exception as e:
        logger.exception(e)



#每隔十分钟修改一下某个案件的
def case_check():
    try:
        if not sync_util.conf_info.has_option("data_check", "case_id"):
            return
        case_id = sync_util.conf_info["data_check"]["case_id"]
        if case_id is None or case_id == "0":
            return
        sql_result = sync_util.db.select_one(f"select serial_no from case_info where id={case_id}")
        db_serial_no = sql_result['serial_no']

        _result = sync_util.es_client.get(index=es_index, id=case_id)
        es_serial_no = _result["_source"]["serialNo"]

        if db_serial_no != es_serial_no:
            logger.error(f"案件数据同步出现异常，数据库与es不一致！！！")
        randomUuid = uuid.uuid4()
        sync_util.db.execute(f"update case_info set serial_no='{randomUuid}' where id={case_id}")
    except Exception as ex:
        logger.error(f"案件数据同步出现异常，数据库与es不一致！！！AA")
        logger.exception(ex)



if __name__ == '__main__':
    try:
        background_schedulers = bg.BackgroundScheduler(timezone='Asia/Shanghai')
        background_schedulers.add_job(heartbeat, 'interval', seconds=10)
        background_schedulers.add_job(case_check, 'interval',seconds=900)
        background_schedulers.add_job(data_compare, 'cron', day_of_week='*', hour=23, minute=30)
        background_schedulers.start()
        main()
    except Exception as e:
        logger.exception(e)
