#!/usr/bin/python
# -*- coding: UTF-8 -*-
import time
import json
import openpyxl
from elasticsearch import Elasticsearch
from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE
from NacosHelper import <PERSON>cosHelper
from es_query_builder import *
from utils import base_utils
from utils.base_utils import init_base_conf, init_heartbeat
from utils import encrypt_utils

config = NacosHelper().init_config()
logger, fileUtil, ossSwitch, esSwitch, encryptSwitch, encryptMode, bucket, mysql_pool, taskUtil, redis_pool = init_base_conf("export_bpw_case_log", config)

selectFiledSql = "id, create_time, type, case_id, case_name, org_delt_name, org_delt_id, batch_id, " \
                 "product_name, product_id, create_by, id_card, create_by_name, field_json, dunner_id, " \
                 "dunner_name, batch_no, serial_no, out_serial_no, org_id, team_id, team_name, dep_id, " \
                 "task_id, own_mobile, out_batch_id, out_batch_name, case_amount, case_json, repayment_amount, " \
                 "out_serial_temp"

selectTableSql = " case_log as log "

zipRootPath = "../zip/"

pageSize = 500


def getTypeDIct(language):
    typeDictLanguage = {
        "typeDict": {"0": "案件导入",
                     "1": "结案",
                     "2": "停催",
                     "3": "分案",
                     "4": "停催恢复",
                     "5": "更新",
                     "6": "还款导入",
                     "7": "作废",
                     "8": "留案",
                     "9": "退案",
                     "10": "批量调整",
                     "11": "删除",
                     "12": "彻底删除",
                     "13": "案件恢复",
                     "14": "重新分配",
                     "15": "催记删除",
                     "16": "还款作废",
                     "17": "分配协催员,",
                     "18": "修改催收进程",
                     "19": "案件自动回收",
                     "20": "添加标签"},
        "typeDict_tj": {"0": "案件导入",
                        "1": "结案",
                        "2": "停调",
                        "3": "分案",
                        "4": "停调恢复",
                        "5": "更新",
                        "6": "还款导入",
                        "7": "作废",
                        "8": "留案",
                        "9": "退案",
                        "10": "批量调整",
                        "11": "删除",
                        "12": "彻底删除",
                        "13": "案件恢复",
                        "14": "重新分配",
                        "15": "调解删除",
                        "16": "还款作废",
                        "17": "分配协调员,",
                        "18": "修改调解进程",
                        "19": "案件自动回收",
                        "20": "添加标签"}
    }
    key = "typeDict"
    if language:
        key = key + "_" + language
    return typeDictLanguage[key]


def getFieldDict(language):
    fieldDictLanguage = {
        "fieldDict": {"0": {"name": "操作时间", "value": "create_time"},
                      "1": {"name": "操作人", "value": "create_by_name"},
                      "2": {"name": "操作", "value": "type"},
                      "3": {"name": "委案编号", "value": "out_serial_temp"},
                      "4": {"name": "姓名", "value": "case_name"},
                      "5": {"name": "身份证号", "value": "id_card"},
                      "6": {"name": "催员", "value": "dunner_name"},
                      "7": {"name": "委案公司", "value": "org_delt_name"},
                      "8": {"name": "委案产品", "value": "product_name"},
                      "9": {"name": "委案方批次号", "value": "out_batch_name"},
                      "10": {"name": "内部批次号", "value": "batch_no"}
                      },
        "fieldDict_tj": {"0": {"name": "操作时间", "value": "create_time"},
                         "1": {"name": "操作人", "value": "create_by_name"},
                         "2": {"name": "操作", "value": "type"},
                         "3": {"name": "案件编号", "value": "out_serial_temp"},
                         "4": {"name": "姓名", "value": "case_name"},
                         "5": {"name": "身份证号", "value": "id_card"},
                         "6": {"name": "调解员", "value": "dunner_name"},
                         "7": {"name": "申请方", "value": "org_delt_name"},
                         "8": {"name": "申请产品", "value": "product_name"},
                         "9": {"name": "申请方批次号", "value": "out_batch_name"},
                         "10": {"name": "内部批次号", "value": "batch_no"}
                         }
    }
    key = "fieldDict"
    if language:
        key = key + "_" + language
    return fieldDictLanguage[key]


def doTasks():
    tasks = taskUtil.getUndoTasks(10)
    for task in tasks or []:
        handleTask(task)


def handleTask(task):
    orgId, data, taskId = task["org_id"], task["data"], task["id"]
    try:
        language = taskUtil.getOrgLanguage(orgId)
        logger.info(f"执行任务id:{taskId}")
        taskUtil.updateTaskIng(taskId)
        dataJson = json.loads(data)
        total = {"value": 1}
        if esSwitch:
            caseLogIdsList = get_case_log_ids_from_es(dataJson, total)
        else:
            caseLogIdsList = get_case_log_ids_from_mysql(dataJson, total)
        exportExcel(caseLogIdsList, taskId, language, total)
    except Exception as ex:
        logger.error(f"任务{taskId}到处失败")
        logger.exception(ex)
        taskUtil.updateFail(taskId)


def get_case_log_ids_from_mysql(dataJson, total):
    queryParaMap = ()
    selectWhereSql = " log.org_id=%s "
    queryParaMap += (dataJson.get('orgId'),)
    # 分公司
    if dataJson.get('depId'):
        selectWhereSql += " and log.dep_id=%s "
        queryParaMap += (dataJson.get('depId'),)
    if dataJson.get('depIds'):
        selectWhereSql += " and log.dep_id in %s "
        queryParaMap += (dataJson.get('depIds').split(","),)
    # 小组
    if dataJson.get('teamIds'):
        selectWhereSql += " and log.team_id in %s "
        queryParaMap += (dataJson.get('teamIds').split(","),)
    if dataJson.get('taskId'):
        selectWhereSql += " and log.task_id = %s "
        queryParaMap += (dataJson.get('taskId'),)
    if dataJson.get('caseId'):
        selectWhereSql += " and log.case_id = %s "
        queryParaMap += (dataJson.get('caseId'),)
    if dataJson.get('orgDeltIds'):
        selectWhereSql += " and log.org_delt_id in %s "
        queryParaMap += (dataJson.get('orgDeltIds').split(","),)
    if dataJson.get('productIds'):
        selectWhereSql += " and log.product_id in %s "
        queryParaMap += (dataJson.get('productIds').split(","),)
    if dataJson.get('batchIds'):
        selectWhereSql += " and log.batch_id in %s "
        queryParaMap += (dataJson.get('batchIds').split(","),)
    if dataJson.get('userIds'):
        selectWhereSql += " and log.dunner_id in %s "
        queryParaMap += (dataJson.get('userIds').split(","),)
    if dataJson.get('types'):
        selectWhereSql += " and log.type in %s "
        queryParaMap += (dataJson.get('types').split(","),)
    if dataJson.get('notInTypes'):
        selectWhereSql += " and log.type not in %s "
        queryParaMap += (dataJson.get('notInTypes').split(","),)
    if dataJson.get('outSerialNos'):
        selectWhereSql += " and log.out_serial_temp in %s "
        queryParaMap += (dataJson.get('outSerialNos'),)
    if dataJson.get('outBatchIds'):
        selectWhereSql += " and log.out_batch_id in %s "
        queryParaMap += (dataJson.get('outBatchIds').split(","),)
    if dataJson.get('start'):
        selectWhereSql += " and log.create_time >= DATE(FROM_UNIXTIME(%s/1000)) "
        queryParaMap += (dataJson.get('start'),)
    if dataJson.get('end'):
        selectWhereSql += " and log.create_time < DATE_ADD(DATE(FROM_UNIXTIME(%s/1000)),INTERVAL 1 DAY) "
        queryParaMap += (dataJson.get('end'),)
    if dataJson.get('minCaseAmount'):
        selectWhereSql += " and log.case_amount >= %s "
        queryParaMap += (dataJson.get('minCaseAmount'),)
    if dataJson.get('maxCaseAmount'):
        selectWhereSql += " and log.case_amount <= %s "
        queryParaMap += (dataJson.get('maxCaseAmount'),)
    if dataJson.get('customerNames'):
        selectWhereSql += " and log.case_name in %s "
        queryParaMap += (dataJson.get('customerNames'),)
    if dataJson.get('idCards'):
        selectWhereSql += " and log.id_card in %s "
        queryParaMap += (dataJson.get('idCards'),)
    if dataJson.get('mobiles'):
        selectWhereSql += " and log.own_mobile in %s "
        queryParaMap += (dataJson.get('mobiles'),)
    if dataJson.get('allotAgent'):
        selectWhereSql += " and JSON_EXTRACT(log.case_json, '$.allot_agent') = %s "
        queryParaMap += (dataJson.get('allotAgent'),)
    if dataJson.get('operationWay'):
        selectWhereSql += " and JSON_EXTRACT(log.case_json, '$.operation_way') = %s "
        queryParaMap += (dataJson.get('operationWay'),)

    selectCountSql = "select count(1) as cnt from " + selectTableSql + " where " + selectWhereSql
    resultCount = mysql_pool.select_by_param(selectCountSql, queryParaMap)[0]["cnt"]
    total["value"] = resultCount
    selectSql = "select log.id from " + selectTableSql + " where " + selectWhereSql
    logger.info(f"sql查询：{selectSql}")
    startTime, endTime = dataJson.get('start'), dataJson.get('end')
    timeCursor = endTime
    while timeCursor > startTime:
        maxId = 99999999999
        while True:
            limitSql = selectSql + f" and log.create_time>=DATE(FROM_UNIXTIME({timeCursor}/1000)) and log.create_time<DATE_ADD(DATE(FROM_UNIXTIME({timeCursor}/1000)),INTERVAL 1 day) and log.id<{maxId} order by log.id desc limit {pageSize}"
            logger.info(f"sql查询：{limitSql}")
            results = mysql_pool.select_by_param(limitSql, queryParaMap)
            if not results:
                break
            case_log_ids = []
            for row in results:
                case_log_ids.append(row["id"])
                maxId = row["id"]
            yield case_log_ids
        timeCursor = timeCursor - 24 * 3600 * 1000


def get_case_log_ids_from_es(dataJson, total):
    must = [TermQuery(key="orgId", value=dataJson.get('orgId'))]
    must_not = []
    sort_list = []
    # 分公司
    if dataJson.get('depId'):
        must.append(TermQuery(key="depId", value=dataJson.get('depId')))
    if dataJson.get('depIds'):
        must.append(TermsQuery(key="depId", values=dataJson.get('depIds').split(",")))
    # 小组
    if dataJson.get('teamIds'):
        must.append(TermsQuery(key="teamId", values=dataJson.get('teamIds').split(",")))
    if dataJson.get('taskId'):
        must.append(TermQuery(key="taskId", value=dataJson.get('taskId')))
    if dataJson.get('caseId'):
        must.append(TermQuery(key="caseId", value=dataJson.get('caseId')))
    if dataJson.get('orgDeltIds'):
        must.append(TermsQuery(key="orgDeltId", values=dataJson.get('orgDeltIds').split(",")))
    if dataJson.get('productIds'):
        must.append(TermsQuery(key="productId", values=dataJson.get('productIds').split(",")))
    if dataJson.get('batchIds'):
        must.append(TermsQuery(key="batchId", values=dataJson.get('batchIds').split(",")))
    if dataJson.get('userIds'):
        must.append(TermsQuery(key="userId", values=dataJson.get('userIds').split(",")))
    if dataJson.get('types'):
        must.append(TermsQuery(key="type", values=dataJson.get('types').split(",")))
    if dataJson.get('notInTypes'):
        must_not.append(TermsQuery(key="type", values=dataJson.get('notInTypes').split(",")))
    if dataJson.get('outSerialNos'):
        must.append(TermsQuery(key="outSerialTemp", values=dataJson.get('outSerialNos')))
    if dataJson.get('outBatchIds'):
        must.append(TermsQuery(key="outBatchId", values=dataJson.get('outBatchIds').split(",")))
    if dataJson.get('start'):
        must.append(RangeQuery(key="createTime",
                               _from=time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(dataJson['start'] / 1000)),
                               otherMap={"format": "yyyy-MM-dd HH:mm:ss"}))
    if dataJson.get('end'):
        must.append(RangeQuery(key="createTime",
                               to=time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(dataJson['end'] / 1000)),
                               otherMap={"format": "yyyy-MM-dd HH:mm:ss"}))
    if 'minCaseAmount' in dataJson.keys():
        must.append(RangeQuery(key="caseAmount", _from=int(dataJson['minCaseAmount']),
                                include_lower=True))
    if 'maxCaseAmount' in dataJson.keys():
        must.append(RangeQuery(key="caseAmount", to=int(dataJson['maxCaseAmount']),
                                include_upper=True))
    if dataJson.get('customerNames'):
        must.append(TermsQuery(key="caseName", values=dataJson.get('customerNames')))
    if dataJson.get('idCards'):
        must.append(TermsQuery(key="idCard", values=dataJson.get('idCards')))
    if dataJson.get('mobiles'):
        must.append(TermsQuery(key="ownMobile", values=dataJson.get('mobiles')))
    if dataJson.get('allotAgent'):
        must.append(TermQuery(key="caseJsonQuery.allot_agent.keyword", value=dataJson.get('allotAgent')))
    if dataJson.get('operationWay'):
        must.append(TermQuery(key="caseJsonQuery.operation_way.keyword", value=dataJson.get('operationWay')))

    sort_list.append(Sort(key="id", order="desc"))
    bool_query = BoolQuery(must_list=must, should_list=[], must_not_list=must_not)
    search = Search(query=bool_query, limit=pageSize, order_list=sort_list, otherMap={"from": 0}, track_total_hits=True)

    es = Elasticsearch([{'host': config["anmi_es"]["host"], 'port': config["anmi_es"]["port"]}],
                       timeout=3600,
                       http_auth=(config["anmi_es"]["user"], config["anmi_es"]["password"]))

    logger.info("开始es搜索" + json.JSONEncoder().encode(o=search.toMap()))
    result = es.search(index="bpw_case_log_query", body=search.toMap(), scroll="5m", _source_includes=["_id"])
    total["value"] = result["hits"]["total"]["value"]
    scroll_id = result["_scroll_id"]
    while len(result["hits"]["hits"]) != 0:
        case_log_ids = []
        for case_log in result["hits"]["hits"]:
            case_log_ids.append(int(case_log["_id"]))
        yield case_log_ids
        result = es.scroll(scroll_id=scroll_id, scroll='5m')
        if result is None or result["timed_out"]:
            raise RuntimeError("查询错误")


def exportExcel(caseLogIdsList, taskId, language, total):
    sheetNum = 1
    wb = openpyxl.Workbook(write_only=True)
    sheet = createNewSheet(wb, sheetNum, language)
    maxRow = 1000002
    num = 2
    count = 0
    for caseLogIds in caseLogIdsList:
        if num + len(caseLogIds) > maxRow:
            sheetNum = sheetNum + 1
            sheet = createNewSheet(wb, sheetNum, language)
            num = 2
            logger.info(f"创建新的sheet{sheetNum}")
        dataResults = get_case_log_info(caseLogIds)
        for row in dataResults:
            data = getExcelRowData(row, language)
            sheet.append(data)
        count = count + len(caseLogIds)
        progress = count / total["value"] * 100
        taskUtil.updateProgress(taskId, progress)
    doSave(wb, count, taskId)


def get_case_log_info(caseLogIds):
    selectSql = "select " + selectFiledSql + " from " + selectTableSql + " where log.id in %s order by id desc"
    dataResults = mysql_pool.select_by_param(selectSql, [caseLogIds])
    return dataResults


def doSave(wbk, dataNums, taskId):
    logger.info("表格已生成，保存中")
    dateStr = time.strftime('%Y-%m-%d', time.localtime(int(time.time())))
    excelFileName = f"案件操作记录{dateStr}_{taskId}.xlsx"
    wbk.save(excelFileName)
    downloadurl, fileSize = base_utils.upload_file(ossSwitch, bucket, fileUtil, excelFileName)
    logger.info("下载地址:" + downloadurl)
    taskUtil.updateSuccess(taskId, downloadurl, dataNums)


def createNewSheet(wb, sheetNum, language):
    sheet = wb.create_sheet("案件操作记录" + str(sheetNum))
    # 创建表头
    columnCount = len(getFieldDict(language))
    data = []
    for i in range(0, columnCount):
        data.append(getFieldDict(language)[str(i)].get("name"))
    sheet.append(data)
    return sheet


def getExcelRowData(dataResult, language):
    data = []
    for i in range(len(getFieldDict(language))):
        field = getFieldDict(language)[str(i)]["value"]
        dataTmp = dataResult.get(field)
        if dataTmp is None:
            data.append("")
            continue
        if field == "type":
            dataTmp = getTypeDIct(language).get(str(dataTmp))
        if type(dataTmp).__name__ == 'unicode':
            dataTmp = dataTmp.encode("utf-8")
        else:
            dataTmp = str(dataTmp)
        if (field == 'case_name' or field == 'id_card' or field == 'own_mobile') and encryptSwitch:
            dataTmp = encrypt_utils.decrypt(encryptMode, dataTmp)
        dataTmp = ILLEGAL_CHARACTERS_RE.sub(r'', dataTmp)
        data.append(dataTmp)
    return data


if __name__ == '__main__':
    # 初始化心跳
    init_heartbeat("export_case_log", logger)
    while True:
        try:
            doTasks()
        except Exception as e:
            logger.exception(e)
        time.sleep(10)
