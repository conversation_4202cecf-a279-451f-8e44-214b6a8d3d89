#!/usr/bin/python
# -*- coding: UTF-8 -*-
import json
import os
import time

import openpyxl
import requests
from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE

from NacosHelper import <PERSON>cosHelper
from utils import base_utils
from utils.base_utils import init_base_conf, init_heartbeat

config = NacosHelper().init_config()
logger, fileUtil, ossSwitch, esSwitch, encryptSwitch, encryptMode, bucket, mysql_pool, taskUtil, redis_pool = init_base_conf("export_bpw_lawsuit_file", config)

zipRootPath = "../zip/"

selectFiledSql = " lf.name AS file_name,lc.name AS file_type_name, lf.lawsuit_id, delt.name AS org_delt_name,DATE_FORMAT(lf.create_time,'%%Y-%%m-%%d') AS create_date" \
                 ",u.name as user_name, lf.url "

selectTableSql = " lawsuit_file lf " \
                 "LEFT JOIN lawsuit l ON lf.lawsuit_id = l.id " \
                 "LEFT JOIN org_delt delt ON delt.id = l.org_delt_id " \
                 "LEFT JOIN `user` u ON u.id = lf.operator " \
                 "LEFT JOIN `lawsuit_config` lc on lc.id = lf.file_type "


def doTasks():
    tasks = taskUtil.getUndoTasks(22)
    # tasks = mysql_pool.select(f"select org_id,dep_id,team_id,data,id,type from download_task where id=19372")
    for task in tasks or []:
        handleTask(task)


def handleTask(task):
    orgId, data, taskId = task["org_id"], task["data"], task["id"]
    try:
        language = taskUtil.getOrgLanguage(orgId)
        logger.info(f"执行任务id:{taskId}")
        taskUtil.updateTaskIng(taskId)
        export_lawsuit_file_task(data, taskId, language)
    except Exception as ex:
        logger.exception(ex)
        taskUtil.updateFail(taskId, str(ex))


def export_lawsuit_file_task(data, taskId, language):
    dataJson = json.loads(data)
    queryParaMap = ()
    selectWhereSql = "lf.org_id=%s "
    queryParaMap += (dataJson.get('orgId'),)
    # 分公司
    if dataJson.get('depId') is not None and dataJson.get('depId') != '':
        selectWhereSql += " and l.dep_id = %s "
        queryParaMap += (dataJson.get('depId'),)
    # 小组
    if dataJson.get('teamId') is not None and dataJson.get('teamId') != '':
        selectWhereSql += " and l.team_id = %s "
        queryParaMap += (dataJson.get('teamId'),)
    # 委案公司
    if dataJson.get('orgDeltIds') is not None and dataJson.get('orgDeltIds') != '':
        selectWhereSql += " and l.org_delt_id in %s "
        queryParaMap += (dataJson.get('orgDeltIds').split(","),)
    # 文件名称
    if dataJson.get('fileName') is not None and dataJson.get('fileName') != '':
        selectWhereSql += " and lf.name like concat('%%', %s, '%%') "
        queryParaMap += (dataJson.get('fileName'),)
    # 提交月份
    if dataJson.get('submitDateStart') is not None and dataJson.get('submitDateEnd') is not None:
        startTimeStr = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(dataJson.get('submitDateStart') / 1000))
        endTimeStr = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(dataJson.get('submitDateEnd') / 1000))
        selectWhereSql += " and lf.create_time >= '" + startTimeStr + "' and lf.create_time <= '" + endTimeStr + "' "
    # 文件类型
    if dataJson.get('fileTypes') is not None and dataJson.get('fileTypes') != '':
        selectWhereSql += " and lf.file_type in %s "
        queryParaMap += (dataJson.get('fileTypes'),)
    # 诉讼案件编号
    if dataJson.get('lawsuitIds') is not None and dataJson.get('lawsuitIds') != '':
        selectWhereSql += " and lf.lawsuit_id in %s "
        queryParaMap += (dataJson.get('lawsuitIds'),)
    selectCountSql = "select count(1) as cnt from " + selectTableSql + " where " + selectWhereSql
    # 总数
    countResult = mysql_pool.select_by_param(selectCountSql, queryParaMap)[0]
    resultCount = countResult["cnt"]
    selectSql = "select " + selectFiledSql + " from " + selectTableSql + " where " + selectWhereSql
    if resultCount and resultCount > 0:
        exportExcel(selectSql, queryParaMap, resultCount, taskId, language)
    else:
        logger.warning(f"任务id:{taskId}查询诉讼材料文件为空")
        taskUtil.updateFail(taskId, "无诉讼材料文件信息")


def exportExcel(sql, sqlParam, allCount, taskId, language):
    sheetNum = 1
    wb = openpyxl.Workbook(write_only=True)
    tmpFilePath = f"./lawsuit_file_export_{taskId}/"
    # 如果存在目录，先清空
    fileUtil.delete(tmpFilePath)
    lawsuitFilePath = tmpFilePath + "file/"
    os.makedirs(lawsuitFilePath, exist_ok=True)
    sheet = createNewSheet(wb, sheetNum, language)
    maxRow = 1000002
    logger.info(f"表格最大行数:{maxRow}")
    limit = 500
    pageCount = int(allCount / limit)
    # 填写表格
    num = 2
    name = {}
    for page in range(0, pageCount + 1):
        results = selectLawsuitFileForLimit(sql, sqlParam, page, limit)
        if results is None:
            break
        resultSize = len(results)
        logger.info("第%s页数据是%s" % (page + 1, resultSize))
        if num + resultSize > maxRow:
            sheetNum = sheetNum + 1
            sheet = createNewSheet(wb, sheetNum, language)
            num = 2
            logger.info("创建新的sheet%s" % str(sheetNum))
        for row in results:
            fileName = str(row["file_name"])
            origin_file_name = fileName[fileName.rfind("/") + 1:]
            fileName = lawsuitFilePath
            if name.get(origin_file_name) is None:
                name[origin_file_name] = 0
                fileName = fileName + origin_file_name
            else:
                name[origin_file_name] = name[origin_file_name] + 1
                pos = origin_file_name.rfind(".")
                if pos == -1:
                    fileName = fileName + "(" + str(name[origin_file_name]) + ")"
                else:
                    fileName = fileName + origin_file_name[0:pos] + "(" + str(name[origin_file_name]) + ")" + origin_file_name[pos:]
            data = getExcelRowData(row, name, language)
            sheet.append(data)
            try:
                if ossSwitch:
                    urlFile = requests.get(row["url"])
                    if urlFile.status_code == 200:
                        open(fileName, 'wb').write(urlFile.content)
                else:
                    recordPath = "/usr/local/duyansoft" + str(row["url"])
                    fileUtil.exec(f"cp '{recordPath}' '{fileName}'")
            except Exception:
                logger.warning("文件下载失败:%s" % row["url"])

        progress = page / (pageCount + 1) * 100
        taskUtil.updateProgress(taskId, progress)
        num = num + resultSize
    doSave(wb, allCount, taskId, tmpFilePath)


def doSave(wbk, dataNums, taskId, tmpFilePath):
    logger.info("表格已生成，保存中")
    dateStr = time.strftime('%Y-%m-%d', time.localtime(int(time.time())))
    excelFileName = tmpFilePath + f"诉讼文件_{dateStr}_{taskId}.xlsx"
    wbk.save(excelFileName)
    zipName = f"诉讼文件导出_{taskId}.zip"
    fileUtil.zip(zipName, tmpFilePath)
    fileUtil.delete(tmpFilePath)
    downloadurl, fileSize = base_utils.upload_file(ossSwitch, bucket, fileUtil, zipName)
    taskUtil.updateSuccess(taskId, downloadurl, dataNums)


def selectLawsuitFileForLimit(sql, sqlParam, page, limit):
    start = page * limit
    limitSql = sql + f" limit {start},{limit}"
    logger.debug("执行查询数据" + limitSql)
    result = mysql_pool.select_by_param(limitSql, sqlParam)
    return result


def createNewSheet(wb, sheetNum, language):
    sheet = wb.create_sheet("诉讼文件" + str(sheetNum))
    # 创建表头
    columnCount = len(getFieldDictLanguage(language))
    data = []
    for i in range(0, columnCount):
        data.append(getFieldDictLanguage(language)[str(i)].get("name"))
    sheet.append(data)
    return sheet


def getExcelRowData(dataResult, file_name, language):
    data = []
    for i in range(len(getFieldDictLanguage(language))):
        field = getFieldDictLanguage(language)[str(i)]["value"]
        dataTmp = dataResult.get(field)
        if dataTmp is None:
            data.append("")
            continue
        if type(dataTmp).__name__ == 'unicode':
            dataTmp = dataTmp.encode("utf-8")
        else:
            dataTmp = str(dataTmp)
        dataTmp = ILLEGAL_CHARACTERS_RE.sub(r'', dataTmp)
        if field == "file_name":
            dataTmp = dataTmp[dataTmp.rfind("/") + 1:]
            if file_name.get(dataTmp) > 0:
                pos = dataTmp.rfind(".")
                if pos == -1:
                    dataTmp = dataTmp + "(" + str(file_name[dataTmp]) + ")"
                else:
                    dataTmp = dataTmp[0:pos] + "(" + str(file_name[dataTmp]) + ")" + dataTmp[pos:]
        data.append(dataTmp)
    return data


def getFieldDictLanguage(language):
    fieldDictLanguage = {
        "fieldDict": {"0": {"name": "文件名称", "value": "file_name"},
                      "1": {"name": "文件类型", "value": "file_type_name"},
                      "2": {"name": "诉讼案件编号", "value": "lawsuit_id"},
                      "3": {"name": "委案公司", "value": "org_delt_name"},
                      "4": {"name": "上传时间", "value": "create_date"},
                      "5": {"name": "操作人", "value": "user_name"}
                      },
        "fieldDict_tj": {"0": {"name": "文件名称", "value": "file_name"},
                         "1": {"name": "文件类型", "value": "file_type_name"},
                         "2": {"name": "诉讼案件编号", "value": "lawsuit_id"},
                         "3": {"name": "申请方", "value": "org_delt_name"},
                         "4": {"name": "上传时间", "value": "create_date"},
                         "5": {"name": "操作人", "value": "user_name"}
                         }
    }
    key = "fieldDict"
    if language is not None and language != "":
        key = key + "_" + language
    return fieldDictLanguage[key]


if __name__ == '__main__':
    # 初始化心跳
    init_heartbeat("export_lawsuit_file", logger)
    while True:
        try:
            doTasks()
        except Exception as e:
            logger.exception(e)
        time.sleep(10)
