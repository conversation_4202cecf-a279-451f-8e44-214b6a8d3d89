#!/usr/bin/python
# -*- coding: UTF-8 -*-
import datetime
import json
import multiprocessing
import os
import time
import openpyxl
import redis
import requests
from apscheduler.executors.pool import ProcessPoolExecutor
from apscheduler.schedulers import background as bg
from apscheduler.schedulers import blocking as bl
from elasticsearch import Elasticsearch
from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE

from NacosHelper import NacosHelper
from es_query_builder import *
from utils import base_utils
from utils.base_utils import init_base_conf, init_heartbeat
from utils import encrypt_utils

# 导出excel

config = NacosHelper().init_config()
logger, fileUtil, ossSwitch, esSwitch, encryptSwitch, encryptMode, bucket, mysql_pool, taskUtil, redis_pool = init_base_conf(
    "export_bpw_case_operation", config)
# redis pool


task_queue_key = "export_case_operation_es_task_queue"

task_queue_key_big_task = "export_case_operation_es_task_queue_big_task"

zipRootPath = "../zip/"
# init all record path
recordPaths = ['/old/recording/', '/dystore0/recording/', '/dystore1/recording/', '/vgw/vgw/recording/',
               '/vgw/vgw1/recording/', '/vgw/vgw2/recording/', '/vgw/vgw3/recording/',
               '/vgw/vgw4/recording/', '/vgw/vgw6/recording/', '/vgw/vgw8/recording/', '/vgw/vgw9/recording/',
               '/vgw/vgw7/recording/',
               '/vgw/vgw10/recording/', '/vgw/vip5/recording/', '/vgw/vip6/recording/', '/vgw/vip7/recording/',
               '/vgw/vip8/recording/', '/vgw/vip9/recording/', '/vgw/vgw11/recording/', '/vgw/vgw12/recording/',
               '/vgw/vgw13/recording/', '/vgw/vgw14/recording/', '/vgw/vgw15/recording/', '/vgw/vgw16/recording/',
               '/vgw/vgw17/recording/', '/vgw/vgw18/recording/', '/vgw/vgw19/recording/', '/vgw/vgw20/recording/',
               '/vgw/vgw21/recording/', '/vgw/vgw25/recording/', '/vgw/vgw29/recording/',
               '/vgw/vgw301/recording/', '/vgw/vgw303/recording/', '/nas/recording/']

localRecordPath = '/usr/local/duyansoft/ng/download/recording/'

case_operation = {"name": "催记", "name_tj": "调解"}

# 来源
createTypeDict = {'0': '系统录入', '1': '导入'}
# 呼叫类型
callStyleDict = {'0': '呼入', '1': '呼出'}
# 催记字段
operationFieldsSql = " co.id,co.action_type,co.call_type,co.reduce_amount,co.ptp_amount,co.ptp_time,co.operator_name,\
     co.con_name,co.con_mobile as mobile,co.relation_type,co.next_time,co.create_time case_ope_time,co.`desc`,co.operation_state,\
     co.call_time,co.call_durtion,co.call_uuid,co.ring_durtion,co.caller,co.tag,co.submit_type,co.create_type,call_style," \
                     " co.out_serial_no, co.comment, co.org_delt_id, co.auto_assist_record,"
# 用户字段
userBaseSql = "(CASE WHEN u.name is null THEN operator_name ELSE u.name END) as user_name,"
# 案件字段
caseBaseSql = " ca.id as case_id,ca.own_mobile,delt.name as delt_name,p.name as product_name,ca.name as case_name,ca.id_card,\
            ca.entrust_start_time,ca.entrust_end_time,ca.amount,inbat.name as batch_no,outbat.name as out_batch_no,\
            ca.case_status,ca.allot_status,ca.overdue_date,ca.overdue_days,ca.field_json"
# 关联sql
fromSql = " from case_operation co left join case_info ca on ca.id=co.case_id \
    left join org_delt delt on co.org_delt_id=delt.id \
    left join product p on ca.product_id=p.id \
    left join inner_batch inbat on ca.inner_batch_id = inbat.id \
    left join out_batch outbat on ca.out_batch_id = outbat.id \
    left join user u on co.create_by = u.id"
fromSqlCold = " from case_operation_use_less co left join case_info ca on ca.id=co.case_id \
    left join org_delt delt on co.org_delt_id=delt.id \
    left join product p on ca.product_id=p.id \
    left join inner_batch inbat on ca.inner_batch_id = inbat.id \
    left join out_batch outbat on ca.out_batch_id = outbat.id \
    left join user u on co.create_by = u.id"


def push_task():
    rs = redis.Redis(connection_pool=redis_pool)
    is_connected = rs.ping
    if is_connected is ConnectionError:
        return
    tasksList = mysql_pool.select("select id,predict_cnt from download_task where status=0 and type in (1,20)")
    if len(tasksList) <= 0:
        return
    task_queue = rs.smembers(task_queue_key)
    task_queue_big_task = rs.smembers(task_queue_key_big_task)
    num = 0
    for task in tasksList:
        if str(task["id"]) not in task_queue and int(task["predict_cnt"]) < 10000:
            rs.sadd(task_queue_key, task["id"])
            num += 1
        if str(task["id"]) not in task_queue_big_task and int(task["predict_cnt"]) >= 10000:
            rs.sadd(task_queue_key_big_task, task["id"])
            num += 1
    logger.info(f"新增了:{num}个等待任务")


def consumer_task():
    rs = redis.Redis(connection_pool=redis_pool)
    is_connected = rs.ping
    if is_connected is ConnectionError:
        return
    task_id = rs.spop(task_queue_key)
    if task_id is None:
        return
    doTask(task_id)


def consumer_task_big_task():
    rs = redis.Redis(connection_pool=redis_pool)
    is_connected = rs.ping
    if is_connected is ConnectionError:
        return
    task_id = rs.spop(task_queue_key_big_task)
    if task_id is None:
        return
    doTask(task_id)


def doTask(taskId):
    try:
        task = mysql_pool.select_one(f"select org_id,dep_id,team_id,rel_record,data,id,type from download_task where status = 0 and  id = {taskId}")
        # task = mysql_pool.select_one(f"select org_id,dep_id,team_id,rel_record,data,id,type from download_task where id = {taskId}")
        if task is None:
            return
        start = datetime.datetime.now()
        taskUtil.updateTaskIng(taskId)
        export_case_operation_task(task)
        end = datetime.datetime.now()
        logger.info(f"任务id:{taskId}开始时间:{start},结束时间:{end}")
    except Exception as ex:
        logger.exception(ex)
        taskUtil.updateFail(taskId, str(ex))


def export_case_operation_task(task):
    taskId, orgId, relRecord, taskData, exportType = task["id"], task["org_id"], task["rel_record"], task["data"], task["type"]
    language = taskUtil.getOrgLanguage(orgId)
    if taskData is None:
        raise RuntimeError("data为空，无法导出")
    if orgId is None:
        raise RuntimeError("公司id为空，无法导出")
    total = {"value": 1}
    task_data_json = json.loads(taskData)
    isSeparate = task_data_json.get("isSeparate") == 1
    templateId = task_data_json["templateId"]
    if 'templateId' not in task_data_json.keys():
        raise RuntimeError("催记导出任务找不到模版id")
    excelEntity = getExcelEntity(templateId, language, orgId)
    if esSwitch:
        caseOperationIdsList = get_case_operation_ids_list_from_es(task_data_json, orgId, exportType, total)
    else:
        caseOperationIdsList = get_case_operation_ids_list_from_mysql(task_data_json, orgId, exportType, total)
    exportExcel(orgId, caseOperationIdsList, excelEntity, relRecord, task_data_json, taskId, language, exportType, isSeparate, total)


def get_case_operation_ids_list_from_es(task_data_json, orgId, exportType, total):
    must = [TermQuery(key="status", value=0), TermQuery(key="orgId", value=orgId)]
    if 'productIds' in task_data_json.keys():
        must.append(TermsQuery(key="productId", values=task_data_json['productIds'].split(",")))
    if 'orgDeltIds' in task_data_json.keys():
        must.append(TermsQuery(key="orgDeltId", values=task_data_json['orgDeltIds'].split(",")))
    # 按照案件id查询
    if 'caseId' in task_data_json.keys():
        must.append(TermQuery(key="caseId", value=task_data_json['caseId']))
    if 'caseIds' in task_data_json.keys():
        must.append(TermsQuery(key="caseId", values=task_data_json['caseIds'].split(",")))
    # 按照案件id查询
    if 'teamId' in task_data_json.keys():
        must.append(TermQuery(key="teamId", value=task_data_json['teamId']))
    # 按照案件id查询
    if 'depId' in task_data_json.keys():
        must.append(TermQuery(key="depId", value=task_data_json['depId']))
    # 催收进程支持多选
    if 'operationStates' in task_data_json.keys():
        must.append(TermsQuery(key="operationState", values=task_data_json['operationStates'].split(",")))
    # 按照user_id查询
    if 'userIds' in task_data_json.keys():
        must.append(TermsQuery(key="createBy", values=task_data_json['userIds'].split(",")))
    if 'startTime' in task_data_json.keys() and 'endTime' in task_data_json.keys():
        startTimeStr = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(task_data_json['startTime'] / 1000))
        endTimeStr = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(task_data_json['endTime'] / 1000))
        must.append(RangeQuery(key="createTime", _from=startTimeStr, to=endTimeStr, otherMap={"format": "yyyy-MM-dd HH:mm:ss"}))
    # 催熟结果
    if 'actionTypes' in task_data_json.keys():
        must.append(TermsQuery(key="actionType", values=task_data_json['actionTypes'].split(",")))
    # 电话结果
    if 'callTypes' in task_data_json.keys():
        must.append(TermsQuery(key="callType", values=task_data_json['callTypes'].split(",")))
    # 债务人姓名
    if 'names' in task_data_json.keys():
        must.append(TermsQuery(key="name.keyword", values=task_data_json['names']))
    # 委案编号搜索
    if 'outSerialNos' in task_data_json.keys():
        if len(task_data_json['outSerialNos']) > 1:
            must.append(TermsQuery(key="outSerialNoSearch.keyword", values=task_data_json['outSerialNos']))
        elif len(task_data_json['outSerialNos']) == 1:
            must.append(WildcardQuery(key="outSerialNoSearch.keyword", value=task_data_json['outSerialNos'][0] + "*"))
    # 手机号
    if 'ownMobiles' in task_data_json.keys():
        must.append(TermsQuery(key="ownMobile.keyword", values=task_data_json['ownMobiles']))
    # 联系人手机号
    if 'conMobiles' in task_data_json.keys():
        must.append(TermsQuery(key="conMobile.keyword", values=task_data_json['conMobiles']))
    # 委案批次号搜索
    if 'outBatchIds' in task_data_json.keys():
        must.append(TermsQuery(key="outBatchId", values=task_data_json['outBatchIds'].split(",")))
    if 'tag' in task_data_json.keys():
        must.append(TermQuery(key="tag", value=task_data_json['tag']))
    if 'submitType' in task_data_json.keys():
        must.append(TermQuery(key="submitType", value=task_data_json['submitType']))
    # 催记来源
    if 'createType' in task_data_json.keys():
        must.append(TermQuery(key="createType", value=task_data_json['createType']))
    # 是否导出隐藏的催记
    if 'isHidden' in task_data_json.keys():
        must.append(TermQuery(key="isHidden", value=task_data_json['isHidden']))
    if 'depIds' in task_data_json.keys():
        must.append(TermsQuery(key="depId", values=task_data_json['depIds'].split(",")))
    if 'callbackFlag' in task_data_json.keys():
        must.append(TermQuery(key="callbackFlag", value=dataJson['callbackFlag']))
    if 'siteId' in task_data_json.keys():
        must.append(TermQuery(key="siteId", value=dataJson['siteId']))
    if 'operTime' in task_data_json.keys():
        must.append(TermQuery(key="operTime", value=dataJson['operTime']))
    if 'poolId' in task_data_json.keys():
        must.append(TermQuery(key="poolId", value=dataJson['poolId']))
    if 'isPush' in task_data_json.keys():
        must.append(TermQuery(key="isPush", value=task_data_json['isPush']))
    sort = Sort(key="createTime", order="desc")
    _bool = BoolQuery(must_list=must, should_list=[], must_not_list=[])
    _search = Search(query=_bool, limit=500, order_list=[sort], otherMap={"from": 0}, track_total_hits=True)

    es = Elasticsearch([{'host': config["anmi_es"]["host"], 'port': config["anmi_es"]["port"]}],
                       timeout=3600,
                       http_auth=(config["anmi_es"]["user"], config["anmi_es"]["password"]),
                       maxsize=10)
    index = "bpw_case_operation_query"
    if exportType == 20:
        index = "bpw_case_operation_query_use_less"

    logger.info("开始es搜索" + json.JSONEncoder().encode(o=_search.toMap()))
    result = es.search(index=index, body=_search.toMap(), scroll="5m", _source_includes=["_id"])
    total["value"] = result["hits"]["total"]["value"]
    scroll_id = result["_scroll_id"]
    while len(result["hits"]["hits"]) != 0:
        case_operation_ids = []
        for data in result["hits"]["hits"]:
            case_operation_ids.append(int(data["_id"]))
        yield case_operation_ids
        result = es.scroll(scroll_id=scroll_id, scroll='5m')
        if result is None or result["timed_out"]:
            raise RuntimeError("查询错误")


def get_case_operation_ids_list_from_mysql(task_data_json, orgId, exportType, total):
    sql = (fromSqlCold if exportType == 20 else fromSql) + " where co.status=0 and co.org_id=" + str(orgId)
    if task_data_json.get('productIds'):
        sql += " and ca.product_id in (" + task_data_json['productIds'] + ") "
    if task_data_json.get('orgDeltIds'):
        sql += " and co.org_delt_id in (" + task_data_json['orgDeltIds'] + ") "
    if task_data_json.get('caseId'):
        sql += " and co.case_id = " + str(task_data_json['caseId'])
    if task_data_json.get('caseIds'):
        sql += " and co.case_id in (" + str(task_data_json['caseIds']) + ") "
    if task_data_json.get('teamId'):
        sql += " and ca.team_id = " + str(task_data_json['teamId'])
    if task_data_json.get('depId'):
        sql += " and ca.dep_id = " + str(task_data_json['depId'])
    if task_data_json.get('depIds'):
        sql += " and ca.dep_id in (" + task_data_json.get('depIds') + ")"
    if task_data_json.get('operationStates'):
        sql += " and co.operation_state in (" + task_data_json['operationStates'] + ") "
    if task_data_json.get('userIds'):
        sql += " and co.create_by in (" + task_data_json['userIds'] + ") "
    if task_data_json.get('startTime') and task_data_json.get('endTime'):
        startTimeStr = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(task_data_json.get('startTime') / 1000))
        endTimeStr = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(task_data_json.get('endTime') / 1000))
        sql += " and co.create_time >= '" + startTimeStr + "' and co.create_time <= '" + endTimeStr + "' "
    if task_data_json.get('actionTypes'):
        sql += " and co.action_type in (" + task_data_json['actionTypes'] + ") "
    if task_data_json.get('names'):
        sql += " and ca.name in (" + base_utils.arrayConvertSplitStr(task_data_json.get('names')) + ") "
    if task_data_json.get('outSerialNos'):
        if len(task_data_json['outSerialNos']) > 1:
            sql += " and co.out_serial_temp in (" + base_utils.arrayConvertSplitStr(task_data_json.get('outSerialNos')) + ") "
        else:
            sql += " and co.out_serial_temp like '" + task_data_json['outSerialNos'][0] + "%' "
    # 手机号
    if task_data_json.get('ownMobiles'):
        sql += " and ca.own_mobile in (" + base_utils.arrayConvertSplitStr(task_data_json.get('ownMobiles')) + ") "
    # 联系人手机号
    if task_data_json.get('conMobiles'):
        sql += " and co.con_mobile in (" + base_utils.arrayConvertSplitStr(task_data_json.get('conMobiles')) + ") "
    # 委案批次号搜索
    if task_data_json.get('outBatchIds'):
        sql += " and ca.out_batch_id in (" + task_data_json['outBatchIds'] + ") "
    if task_data_json.get('tag'):
        sql += " and co.tag = " + str(task_data_json['tag'])
    if task_data_json.get('submitType'):
        sql += " and co.submit_type = " + str(task_data_json['submitType'])
    # 催记来源
    if task_data_json.get('createType'):
        sql += " and co.create_type = " + str(task_data_json['createType'])
    if task_data_json.get('isHidden'):
        sql += " and co.is_hidden = " + str(task_data_json['isHidden'])
    if task_data_json.get('callbackFlag'):
        sql += " and co.callback_flag = " + str(task_data_json['callbackFlag'])
    if task_data_json.get('siteId'):
        sql += " and co.site_id = " + str(task_data_json['siteId'])
    if task_data_json.get('operTime'):
        sql += " and co.oper_time = " + str(task_data_json['operTime'])
    if task_data_json.get('poolId'):
        sql += " and co.pool_id = " + str(task_data_json['poolId'])
    if task_data_json.get('isPush'):
        sql += " and co.is_push = " + str(task_data_json['isPush'])
    countSql = "select count(0) as cnt " + sql
    selectSql = "select co.id " + sql
    logger.info("执行查询数据" + countSql)
    operCount = mysql_pool.select_one(countSql)['cnt']
    logger.info(f"催记数量{operCount}")
    total["value"] = operCount
    limit = 500
    startTime, endTime = task_data_json.get('startTime'), task_data_json.get('endTime')
    timeCursor = endTime
    while timeCursor > startTime:
        maxId = 99999999999
        while True:
            limitSql = selectSql + f" and co.create_time>=date(FROM_UNIXTIME({timeCursor}/1000)) and co.create_time<DATE_ADD(date(FROM_UNIXTIME({timeCursor}/1000)),INTERVAL 1 day) and co.id<{maxId} order by co.id desc limit {limit}"
            logger.info(f"sql查询：{limitSql}")
            results = mysql_pool.select(limitSql)
            if not results:
                break
            case_operation_ids = []
            for row in results:
                case_operation_ids.append(row["id"])
                maxId = row["id"]
            yield case_operation_ids
        timeCursor = timeCursor - 24 * 3600 * 1000


def exportExcel(orgId, caseOperationIdsList, excelEntity, relRecord, task_data_json, taskId, language, exportType, isSeparate,
                total):
    operationStateDict = initOperationStateDict(orgId)
    callTypeDict = initCallTypeDict(orgId)
    actionTypeDict = initActionTypeDict(orgId)
    wbs, delt_nums, maxRow, dataNum, exist_name, wp_host, wp_apikey, delt_names = {}, {}, 1000002, 0, {}, None, None, {}
    orgWorkPhone = mysql_pool.select_one(f"select host, apikey from org_work_phone where org_id={orgId}")
    if orgWorkPhone:
        wp_host, wp_apikey = orgWorkPhone['host'], orgWorkPhone['apikey']
    for case_operation_ids in caseOperationIdsList:
        sql = "select " + operationFieldsSql + userBaseSql + caseBaseSql + (
            fromSqlCold if exportType == 20 else fromSql) + " where co.id in %s order by co.create_time desc "
        operation_infos = mysql_pool.select_by_param(sql, [case_operation_ids])
        # 组装数据
        wp_uuid_list, wp_record_list = [], []
        for operation_info in operation_infos or []:
            # 工作手机录音
            if relRecord is not None and int(relRecord) == 1 and operation_info.get("submit_type") == 3:
                wp_uuid_list.append(operation_info.get("call_uuid"))
        if wp_uuid_list:
            wp_record_list = getWorkPhoneVoiceUrl(wp_host, wp_apikey, wp_uuid_list)
        for operation_info in operation_infos:
            data = []
            for column in excelEntity:
                data.append("")
                fieldValue = excelEntity[str(column)]['value']
                # 用户导入的催记没有催收结果和催收进程
                if (fieldValue == 'action_type' or fieldValue == 'operation_state') and operation_info.get("create_type") == 1:
                    continue
                typeVal = getTypeVal(operation_info, excelEntity[str(column)], operationStateDict, callTypeDict, actionTypeDict,
                                     language)
                if typeVal is None:
                    continue
                typeVal = ILLEGAL_CHARACTERS_RE.sub(r'', typeVal)
                data[len(data) - 1] = typeVal
            # 度言录音
            if relRecord and int(relRecord) == 1:
                recordName = assembleRecord(operation_info, task_data_json, taskId, exist_name, wp_record_list)
                data.append(recordName)
            # 根据委案公司处理excel数据
            org_delt_id = operation_info["org_delt_id"]
            delt_names[org_delt_id] = operation_info["delt_name"]
            org_delt_id = org_delt_id if isSeparate else -1
            # 统计每个委案公司导出催记数量
            if delt_nums.get(org_delt_id) is None:
                delt_nums[org_delt_id] = 2
            # 计算excel的sheet数量，如果当前委案公司催记数量整除sheet最大数量，那么sheetNum+1
            sheetNum = int(delt_nums[org_delt_id] / maxRow) + 1
            # 根据委案公司id生成对应的excel工作簿wb
            if wbs.get(org_delt_id) is None:
                wb = openpyxl.Workbook(write_only=True)
                createNewSheet(wb, sheetNum, excelEntity, relRecord, language)
                wbs[org_delt_id] = wb
            wb = wbs[org_delt_id]
            # 如果当前委案公司催记数量整除sheet最大数量，此时需要创建新的sheet
            if delt_nums[org_delt_id] % maxRow == 0:
                sheet = createNewSheet(wb, sheetNum, excelEntity, relRecord, language)
            else:
                # 注意 这里sheetNum只是为了生存sheet的title名称，其索引顺序是从0开始，所以创建sheetNum=1的sheet的索引下标为0
                sheet = wb.worksheets[sheetNum - 1]
            # sheet表记录催记数据
            sheet.append(data)
            # 记录每个委案公司对应的催记数据
            delt_nums[org_delt_id] = delt_nums[org_delt_id] + 1
            # 统计已导出的催记总数，方便下面更新导出进度
            dataNum = dataNum + 1

        progress = dataNum / total["value"] * 100
        taskUtil.updateProgress(taskId, progress)
    # 按委案公司分类分别导出催记数据
    save_file(wbs, total["value"], taskId, delt_names, isSeparate)


def getWorkPhoneVoiceUrl(host, apikey, uuid_list):
    wp_record_list = []
    try:
        headers = {'Content-Type': 'application/json'}
        param = {'apiKey': apikey, 'uuidList': uuid_list}
        url = host + '/palm/api/open/userCall/getUserCallByUuids'
        data = json.dumps(param, ensure_ascii=False)
        resp = requests.post(url=url, data=data, headers=headers, timeout=5)
        if resp.status_code == 200 and resp.text != '':
            result = json.loads(resp.text)
            wp_record_list = result.get('data')
        else:
            logger.error("获取工作手机录音地址返回结果错误：" + resp.text)
    except Exception as e:
        logger.exception("获取工作手机录音地址失败: " + str(e))
    return wp_record_list


def save_file(wbs, dataNums, taskId, delt_names, isSeparate):
    # zip包文件名
    zipName = f"operations_{taskId}.zip"
    excelName = f"operation_{taskId}.xlsx"
    # 根据委案公司分类生成excel，excel文件名为委案公司名称
    if isSeparate:
        for org_delt_id in wbs:
            excelName = delt_names[org_delt_id] + ".xlsx"
            wb = wbs[org_delt_id]
            wb.save(excelName)
            # excel文件的临时目录 = 公共临时目录 + 任务id（分开不同任务）+ 任务id（按需求要求excel文件放到以任务id命名的文件夹里面，和录音文件同级）
            zipFinalPath = zipRootPath + str(taskId) + "/" + str(taskId) + "/"
            fileUtil.move(excelName, zipFinalPath)
    else:
        wbs[-1].save(excelName)
        zipFinalPath = zipRootPath + str(taskId) + "/"
        fileUtil.move(excelName, zipFinalPath)
    fileUtil.zip(zipName, str(taskId), 0, cwd=zipRootPath)
    # 上传导出文件
    downloadUrl, fileSize = base_utils.upload_file(ossSwitch, bucket, fileUtil, zipName, zipRootPath)
    fileUtil.delete(zipRootPath + str(taskId))
    taskUtil.updateSuccess(taskId, downloadUrl, dataNums, fileSize)


def createNewSheet(wb, sheetNum, excelEntity, relRecord, language):
    name = case_operation.get("name_" + language) if language else case_operation.get("name")
    ws = wb.create_sheet(name + "数据" + str(sheetNum))
    # 创建表头
    data = []
    for column in excelEntity:
        data.append(excelEntity[str(column)]['name'])
    if relRecord is not None and int(relRecord) == 1:
        data.append("录音名称")
    ws.append(data)
    return ws


def assembleRecord(operation_info, task_data_json, taskId, exist_name, wp_record_list):
    try:
        record_path = None
        if operation_info is None or task_data_json is None:
            logger.info("催记数据或者任务序列化数据为空")
            return
        if not existsJsonKey("duyanReferId", task_data_json):
            logger.info("度言公司id为空")
            return
        if not existsJsonKey("recordStyle", task_data_json):
            logger.info("下载录音的格式为空")
            return
        callTime = operation_info['call_time']
        if callTime is None:
            logger.info("电话时间为空")
            return
        callUuid = operation_info['call_uuid']
        if callUuid is None:
            logger.info("电话UUID为空")
            return
        duyanOrgId = task_data_json['duyanReferId']
        # 本地化标识
        local_deploy = task_data_json.get("localDeploy", None)
        # 本地化录音存储
        local_record_storage = task_data_json.get("localRecordStorage", None)
        # 是否按委案公司分开独立存储
        isSeparate = task_data_json.get("isSeparate", None)
        if operation_info.get("submit_type") == 3:
            wp_record = {}
            for k in range(0, len(wp_record_list)):
                if callUuid == wp_record_list[k].get("uuid"):
                    wp_record = wp_record_list[k]
                    break
            if wp_record is None or wp_record.get('recordingFile') is None or wp_record.get('recordingFile') == '':
                logger.info("工作手机录音 UUID:" + callUuid + ",数据不存在")
                return
        if isSeparate == 1:
            # 按委案公司生成路径
            deltName = operation_info.get("delt_name")
            planRecordPath = zipRootPath + str(taskId) + "/record/" + deltName + "/"
        else:
            planRecordPath = zipRootPath + str(taskId) + "/record/"
        if not os.path.exists(planRecordPath):
            os.makedirs(planRecordPath)
        record_name_rule = task_data_json.get('recordNameRule', None)
        record_name_keys = record_name_rule.split("-")
        # 录音文件的名称按照自定义规则生成
        record_name_keys_size = len(record_name_keys)
        planRecordName = None
        for k in range(0, record_name_keys_size):
            key = record_name_keys[k]
            if key == "callTime":
                call_time = operation_info.get("call_time")
                callTimeName = call_time.strftime('%Y-%m-%d_%H-%M-%S')
                if planRecordName is None:
                    planRecordName = callTimeName
                else:
                    planRecordName = planRecordName + "-" + callTimeName
            if key == "callUuid":
                value = operation_info.get("call_uuid")
                if planRecordName is None:
                    planRecordName = value
                else:
                    planRecordName = planRecordName + "-" + value
            if key == "operatorName":
                # 催记的催员姓名有可能为空
                value = operation_info.get("user_name")
                if planRecordName is None:
                    planRecordName = value
                else:
                    if value is not None:
                        planRecordName = planRecordName + "-" + value
            if key == "conMobile":
                value = operation_info.get("mobile")
                if encryptSwitch:
                    value = encrypt_utils.decrypt(encryptMode, value)
                if planRecordName is None:
                    planRecordName = value
                else:
                    if value is not None:
                        planRecordName = planRecordName + "-" + value
            if key == "caseName":
                value = operation_info.get("case_name")
                if encryptSwitch:
                    value = encrypt_utils.decrypt(encryptMode, value)
                if planRecordName is None:
                    planRecordName = value
                else:
                    if value is not None:
                        planRecordName = planRecordName + "-" + value
            if key == "outSerialNo":
                value = operation_info.get("out_serial_no")[0:operation_info.get("out_serial_no").rindex("#")]
                if planRecordName is None:
                    planRecordName = value
                else:
                    planRecordName = planRecordName + "-" + value
        # 如果单独用催员名称做文件名可能为空，此时按要求用催记id为文件名
        if planRecordName is None:
            planRecordName = str(operation_info.get("id"))
        planRecordName = planRecordName.replace(' ', '')
        temp = planRecordName
        if exist_name.get(planRecordName) is None:
            exist_name[planRecordName] = 1
        else:
            temp = planRecordName + "(" + str(operation_info.get("id")) + ")"
            planRecordName = planRecordName + f"({operation_info.get('id')})"
        planRecordName = planRecordName + ".wav"
        temp = temp + ".wav"
        if operation_info.get("submit_type") == 3:
            wp_file_url = wp_record.get('recordingFile')
            urlFile = requests.get(wp_file_url)
            if urlFile.status_code == 200:
                open(planRecordPath + planRecordName, 'wb').write(urlFile.content)
        else:
            callTime = time.mktime(time.strptime(str(callTime), '%Y-%m-%d %H:%M:%S'))
            nameCallTime = time.strftime('%Y-%m-%d', time.localtime(callTime))
            record_key = "recording/" + str(duyanOrgId) + "/" + nameCallTime + "/" + callUuid + ".wav"
            tmpFilePath = planRecordPath + callUuid + ".wav"
            if bucket.object_exists(record_key):
                bucket.get_object_to_file(record_key, tmpFilePath)
                os.replace(tmpFilePath, planRecordPath + planRecordName)
                return temp
    except Exception as ex:
        logger.exception(ex)


def getTypeVal(dataResult, fieldEntry, operationStateDict, callTypeDict, actionTypeDict, language):
    if dataResult is None or fieldEntry is None:
        logger.debug("数据或者字段名称为空")
        return
    fieldValue = fieldEntry.get('value')
    fieldType = fieldEntry.get('type')
    if fieldValue is None:
        logger.debug("字段名称为空")
        return None
    typeVal = dataResult.get(fieldValue)
    if fieldValue == 'case_status':
        typeVal = taskUtil.getCaseStatus(dataResult.get('case_status'), language)
    if fieldValue == 'allot_status':
        typeVal = taskUtil.getCaseAllotStatus(dataResult.get('allot_status'))
    if typeVal is None:
        # json字段里面
        # 查询相关案件字段值为空，并且自定义字段集合field_json为空，说明此案件已被物理删除
        if dataResult['field_json'] is None:
            return None
        caseJson = json.loads(dataResult['field_json'])
        # 虽然处理逻辑很恶心，但是没办法
        if fieldValue != 'desc':
            typeVal = caseJson.get(fieldValue)
            # 催记备注与案件备注都是desc,做了转换
            if fieldValue == 'case_desc':
                typeVal = caseJson.get('desc')
            if typeVal is None:
                return typeVal
        else:
            return typeVal
    if type(typeVal).__name__ == 'unicode':
        typeVal = typeVal.encode("utf-8")
    else:
        typeVal = str(typeVal)
    if fieldValue == 'out_serial_no':
        return typeVal[0:typeVal.rindex("#")]
    # 枚举转化
    if fieldValue == 'action_type':
        return actionTypeDict.get(typeVal)
    if fieldValue == 'call_type':
        return callTypeDict.get(typeVal)
    if fieldValue == 'operation_state':
        return operationStateDict.get(typeVal)
    if fieldValue == 'reduce_amount' or fieldValue == 'ptp_amount':
        return str(int(typeVal) / 1000)
    if fieldValue == 'submit_type':
        if typeVal == '0':
            return '坐席外呼'
        elif typeVal == '1':
            return '机器人智能协催'
        elif typeVal == '2':
            return '预测式外呼'
        elif typeVal == '3':
            return '工作手机外呼'
    if fieldValue == 'tag':
        # 客户标签
        if typeVal == 'notag':
            return '无标签'
        if typeVal == 'FAIL':
            return None
        # typeVal为空的话，默认返回None
        if typeVal == 'USER_BUSY':
            return None
    if fieldValue == 'create_type':
        return createTypeDict.get(typeVal)
    if fieldValue == 'call_style':
        return callStyleDict.get(typeVal)
    # 时间转化
    if fieldValue == 'entrust_start_time' or fieldValue == 'entrust_end_time' or fieldValue == 'overdue_date':
        return base_utils.formatDate(typeVal)
    # 金额转化
    if fieldType is not None and fieldType == 'Money':
        return str(float(typeVal) / 1000)
    if (fieldValue == 'case_name' or fieldValue == 'id_card' or fieldValue == 'own_mobile' or fieldValue == 'mobile'
        or fieldValue == 'con_name') and encryptSwitch:
        typeVal = encrypt_utils.decrypt(encryptMode, typeVal)
    return typeVal


def existsJsonKey(key, _json):
    return _json and key in _json


def getExcelEntity(templateId, language, orgId):
    operFields = taskUtil.getLanguageFieldDict('caseOperationFields', language)
    caseFields = taskUtil.getLanguageFieldDict('operationExportCaseFields', language)
    temFields = taskUtil.getTemplateFields(templateId)
    # 解析
    operFieldJson = json.loads(operFields)
    caseFieldJson = json.loads(caseFields)
    temFieldJson = json.loads(temFields)
    allFieldJson = getAllFieldJson(operFieldJson, caseFieldJson)
    # 组装模板所有字段
    caseEntity = {}
    for k in range(len(temFieldJson)):
        fieldValue = temFieldJson[str(k)]
        fieldEntry = allFieldJson.get(fieldValue)
        if fieldEntry is not None:
            caseEntity[str(k)] = fieldEntry
        else:
            # 自定义字段
            sql = f"select name from custom_field cuf where cuf.value='{fieldValue}'"
            logger.info("查询自定义字段:" + sql)
            fieldName = mysql_pool.select_one(sql)
            if fieldName is None:
                # 自定义搜索字段
                sql = f"select name from custom_search_field cef where cef.search_key='{fieldValue}' and org_id={orgId}"
                logger.info("查询自定义搜索字段:" + sql)
                fieldName = mysql_pool.select_one(sql)
            if fieldName is None:
                fieldName = fieldValue
            else:
                fieldName = fieldName["name"]
            fieldEntity = {'name': fieldName, 'value': fieldValue}
            caseEntity[str(k)] = fieldEntity
    return caseEntity


def getAllFieldJson(operFieldJson, caseFieldJson):
    allFieldJson = {}
    for fieldEntry in operFieldJson:
        allFieldJson[fieldEntry.get('value')] = fieldEntry
    for fieldEntry in caseFieldJson:
        allFieldJson[fieldEntry.get('value')] = fieldEntry
    return allFieldJson


def initOperationStateDict(orgId):
    operationStateDict = {'-2': '未知', '-1': '未联系上', '0': '承诺还款', '1': '筹款中', '2': '联系本人中', '3': '联系家人中', '4': '联系第三方中',
                          '5': '谈判中', '6': '要求减免', '7': '否认用款', '8': '已结清', '9': '已外访', '10': '准备外访', '11': '重点跟进'}
    sql = "select `code`,`name`,`rename`,`operation_no` from `org_config` where org_id=%d and type=1" % orgId
    logger.info("查询催收进程枚举:" + sql)
    results = mysql_pool.select(sql)
    for result in results or []:
        if result["rename"]:
            if result["operation_no"]:
                operationStateDict[str(result["code"])] = str(result["operation_no"]) + '-' + str(result["rename"])
            else:
                operationStateDict[str(result["code"])] = str(result["rename"])
        else:
            if result["operation_no"]:
                operationStateDict[str(result["code"])] = str(result["operation_no"]) + '-' + str(result["name"])
            else:
                operationStateDict[str(result["code"])] = str(result["name"])
    return operationStateDict


def initCallTypeDict(orgId):
    callTypeDict = {'-3': '未填写', '-1': '未呼叫', '0': '正常接通', '1': '关机', '2': '忙线中', '3': '无人接听', '4': '接听挂断', '5': '停机',
                    '6': '空号', '7': '传真机', '8': '呼叫异常', '9': '呼入限制', '10': '网查'}
    sql = "select `code`,`name`,`rename`,`operation_no` from `org_config` where org_id=%d and type=2" % orgId
    logger.info("查询电话结果枚举:" + sql)
    results = mysql_pool.select(sql)
    for result in results or []:
        if result["rename"]:
            if result["operation_no"]:
                callTypeDict[str(result["code"])] = str(result["operation_no"]) + '-' + str(result["rename"])
            else:
                callTypeDict[str(result["code"])] = str(result["rename"])
        else:
            if result["operation_no"]:
                callTypeDict[str(result["code"])] = str(result["operation_no"]) + '-' + str(result["name"])
            else:
                callTypeDict[str(result["code"])] = str(result["name"])
    return callTypeDict


def initActionTypeDict(orgId):
    actionTypeDict = {'-3': '未填写', '-2': '未处理', '-1': '未联系上', '0': '承诺还款', '1': '意愿还款', '2': '谈判中', '3': '已转告',
                      '4': '声称已还款', '5': '要求减免', '6': '否认用款', '7': '非本人', '8': '已结清', '9': '未知', '11': '声称要投诉',
                      '12': '分期还款', '13': '债务人失联', '14': '争议案件', '15': '查询案件'}
    sql = f"select `code`,`name`,`rename`,`operation_no` from `custom_operation_status` where org_id={orgId}"
    logger.info("查询催收结果枚举:" + sql)
    results = mysql_pool.select(sql)
    for result in results or []:
        if result["rename"]:
            if result["operation_no"]:
                actionTypeDict[str(result["code"])] = str(result["operation_no"]) + '-' + str(result["rename"])
            else:
                actionTypeDict[str(result["code"])] = str(result["rename"])
        else:
            if result["operation_no"]:
                actionTypeDict[str(result["code"])] = str(result["operation_no"]) + '-' + str(result["name"])
            else:
                actionTypeDict[str(result["code"])] = str(result["name"])
    return actionTypeDict


if __name__ == '__main__':
    # 初始化心跳
    init_heartbeat("export_case_operation", logger)
    background_schedulers = bg.BackgroundScheduler(timezone='Asia/Shanghai')
    background_schedulers.add_job(push_task, 'interval', seconds=10)
    background_schedulers.start()
    # consumer
    executors = {
        'default': ProcessPoolExecutor(multiprocessing.cpu_count())
    }
    blocking_schedulers = bl.BlockingScheduler(executors, timezone='Asia/Shanghai')
    for i in range(multiprocessing.cpu_count()):
        blocking_schedulers.add_job(consumer_task, 'interval', seconds=15)
    blocking_schedulers.add_job(consumer_task_big_task, 'interval', seconds=20)
    blocking_schedulers.start()
