package com.anmi.collection.constant;


/**
 * 阿里大鱼短信模板
 *
 */
public class SmsContants {
	private SmsContants() {
        throw new IllegalStateException("Utility class");
    }
	
   
    /**
     * 账户更新通知模板码
     * 模版内容 : 账户已更新，帐号：${account}，密码：${password}，请妥善保管账户信息。
     */
    public static final String INFORMATION_CHANGE_ADVICE = "SMS_132390818";
    /**
     * 账户开通通知模板码
     *  模版内容 : 账户已开通，帐号：${account}，密码：${password}，请妥善保管账户信息。
     */
    public static final String ACCOUNT_OPEN = "SMS_127157118";
    
    /**
     *  信息变更发送验证码模板码 SMS_126325356
     *  模版内容 : 验证码${code}，您正在尝试变更重要信息，请妥善保管账户信息。
     */
    public static final String INFORMATION_CHANGE = "SMS_126325356";
    
    public static final String SEND_SMS_FAILURE = "发送短信验证码失败";
    
    public static final String SEND_SMS_SUCCESS = "发送短信验证码成功";
    
    public static final String INVALID_PHONE_NUMBER = "无效的手机号！";

    /**
     * 安米发送短信模板
     */
    public static final String SMS_CODE_USER_ADD = "【安米智能】您在安米平台的密码为%s，请及时登录并修改密码。";
    public static final String SMS_CODE_RESET_PASSWORD = "【安米智能】您的登录密码已重置，密码是%s，请及时登录修改";
    public static final String SMS_CODE_CALLCENTER_AGENT = "【安米智能】您好，已成功将您添加为呼叫中心坐席，密码重置为%s，请及时登录修改";
    public static final String SMS_CODE_CALLCENTER_ADMIN = "【安米智能】您好，已成功将您添加为呼叫中心管理员，密码重置为%s，请及时登录修改";
    public static final String SMS_CODE_NORMAL = "【安米智能】您的短信验证码是%s，10分钟内有效，请尽快完成验证";
    public static final String SMS_CODE_MAIL_ORG = "【安米智能】尊敬的%s：您购买的安米智能催收系统将于%s到期，截止目前仅剩%d天，为避免影响业务，请尽快联系客户经理续费。";
    public static final String SMS_CODE_MAIL_SALES = "%s，你负责的公司%s使用的安米智能催收系统将于%s到期，截止目前仅剩%d天，请跟进处理";

    public static final String SMS_CODE_MAIL_SPACE_CAPACITY = "【安米智能】您（公司）的文件可用存储空间大小近期将会到期或不足，为避免影响数据上传，请及时联系客户经理进行充值。";
}
