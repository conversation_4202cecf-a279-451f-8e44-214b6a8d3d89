package com.anmi.collection.constant;

import java.util.Date;

/** 文件仓库记录 常量 */
public class FiletStoreConstants {
  private FiletStoreConstants() {
    throw new IllegalStateException("Utility class");
  }

  public static final String FILE_SOURCE_SYSTEM = "SYSTEM.BI";
  public static final String FILE_SOURCE_WEB = "WEB.UPLOAD";

  public static final Integer CASE_CGI = 1; // 1 CGI案件模版
  public static final Integer CASE_CP = 2; // 2 CP导入模版
  public static final Integer CASE_COMMENT = 3; // 3 案件评语模版

  public static final Long uploadSizeLimit = 100 * 1024 * 1024L;
  public static final Long contactsUploadSizeLimit = 2 * 1024 * 1024L;

  // ===================案件导入状态==========================
  public static final String FILE_STORE_NONE = "0"; // 未入库
  public static final String FILE_STORE_BEGIN = "1"; // 入库中
  public static final String FILE_STORE_SUCCESS = "2"; // 入库成功
  public static final String FILE_STORE_FIAL = "3"; // 入库失败
  public static final String FILE_STORE_REPAIR_BEGIN = "4"; // 文件导入记录表修复开始
  public static final String FILE_STORE_REPAIR_FINISH = "5"; // 文件导入记录表修复完成
  public static final String FILE_STORE_AUDIT_APPLY = "6"; // 作废申请中
  public static final String FILE_STORE_AUDIT_PASS = "7"; // 作废通过（已作废）

  public static final Integer REPAIR_TYPE_ALL = 1; // 1-全部修复

  public static final Integer FILE_STORE_RESULT_LENGTH = 19000; // 文件仓库 ，result字段的长度

  public static final Date beginTime = new Date(31507200000L); // 1971-01-01 00:00:00
  public static final Date endTime = new Date(4102416000000L); // 2100-01-01 00:00:00
}
