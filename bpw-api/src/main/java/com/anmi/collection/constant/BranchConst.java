package com.anmi.collection.constant;

public class BranchConst {
    private BranchConst() {
        throw new IllegalStateException("Utility class");
    }

    // ==================机构 节点类型========================

    /**
     * 总公司
     */
    public static final Integer OFFICE_HEAD = 1;
    /**
     * 分公司
     */
    public static final Integer OFFICE_BRANCH = 2;
    /**
     * 部门
     */
    public static final Integer DEPT = 3;
    /**
     * 合营公司
     */
    public static final Integer OFFICE_JOINT_VENTURE = 4;
    /**
     * 外部机构
     */
    public static final Integer EXTERNAL_ORG = 5;
    /**
     * 虚拟机构
     */
    public static final Integer VIRTUAL_ORG = 6;
    /**
     * 项目组
     */
    public static final Integer GROUP = 7;

}
