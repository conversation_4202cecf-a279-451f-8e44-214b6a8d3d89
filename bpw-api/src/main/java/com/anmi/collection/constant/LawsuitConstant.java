package com.anmi.collection.constant;

import com.google.common.collect.ImmutableList;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/22 16:00
 */
public interface LawsuitConstant {
    Integer AUDIT_STATUS_ING = 0;
    Integer AUDIT_STATUS_REFUSE = -1;  //拒绝
    Integer AUDIT_STATUS_PASS = 1; //通过

    Integer LAWSUIT_STATUS_RUNNING = 0; //办案中
    Integer LAWSUIT_STATUS_END = 1;  //结案

    Integer HANDLE_TYPE_MERGE = 0;   //共债案件合并成一个诉讼
    Integer HANDLE_TYPE_NOT_MERGE = 1; //不合并

    Integer LAWSUIT_SOURCE_ADMIN = 0; //管理员加入
    Integer LAWSUIT_SOURCE_AUDIT= 1;  //诉讼申请审批；

    Integer LAWSUIT_EXPORT_LIST = 0;
    Integer LAWSUIT_EXPORT_ALL = 1;


    List<String> LAWSUIT_UPDATE_EXCEL_HEAD_LIST = ImmutableList.of("诉讼案件id", "诉讼案号", "被告", "证件号", "手机号",
            "诉讼节点", "卡号", "联系地址", "联系电话", "档案号", "委托人", "被告二", "被告二身份证号码", "被告三", "被告三身份证号码",
            "标的", "案件类型", "案由", "还款情况", "代理律师", "律师联系方式", "立案日期", "受理法院", "办案法官", "法官联系方式",
            "诉讼费缴纳日期", "保全费缴纳日期", "保全资产清单", "首次开庭日期", "执行立案日期", "判决日期", "送达情况", "判决书",
            "争议处理方式", "一审判决结果", "是否有二审再审", "二审立案日期", "二审开庭日期", "二审判决日期", "再审立案日期",
            "再审开庭日期", "再审判决日期", "判决生效日期", "材料提交日期", "查封日期", "续封日期", "查封到期日期", "案件是否归档",
            "应收代保全费", "应收保底律师费", "应收风险律师费", "应收代垫诉讼费", "合同号", "执行承办律师", "申请执行案号", "执行终结日期",
            "费用", "执行结果","备注");

    List<String> LAWSUIT_EXPORT_LIST_HEAD = ImmutableList.of("诉讼案件id","诉讼案号", "案件状态", "诉讼进度", "被告",
            "身份证号", "标的", "案件类型", "代理律师", "受理法院");


}
