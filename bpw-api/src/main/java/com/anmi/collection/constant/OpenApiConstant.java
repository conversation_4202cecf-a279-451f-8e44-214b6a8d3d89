package com.anmi.collection.constant;

import com.anmi.collection.common.enums.CaseTemplateEnums;

import java.util.HashMap;
import java.util.Map;

public class OpenApiConstant {
    private OpenApiConstant() {
        throw new IllegalStateException("Utility class");
    }

    public static final String APIKEY_NOTNULL_CODE = "ERR_1000";
    public static final String APIKEY_NOTNULL_MESSAGE = "apikey不能为空";

    public static final String APIKEY_ILLEGAL_CODE = "ERR_1001";
    public static final String APIKEY_ILLEGAL_MESSAGE = "apikey不合法";

    public static final String TEMPLATE_NOTFOUND_CODE = "ERR_1002";
    public static final String TEMPLATE_NOTFOUND_MESSAGE = "未找到该模板";

    public static final String ORGDELT_NOTFOUND_CODE = "ERR_1003";
    public static final String ORGDELT_NOTFOUND_MESSAGE = "未找到该委案公司";

    public static final String FIELD_ILLEGAL_CODE = "ERR_1004";
    public static final String FIELD_ILLEGAL_MESSAGE = "传输字段与模板不符";

    public static final String OUT_OF_RANGE_CODE = "ERR_1005";
    public static final String OUT_OF_RANGE_MESSAGE = "超过传输数据范围";

    public static final String TASK_NOTFOUND_CODE = "ERR_1006";
    public static final String TASK_NOTFOUND_MESSAGE = "找不到该任务";

    public static final String ORG_NOTFOUND_CODE = "ERR_1007";
    public static final String ORG_NOTFOUND_MESSAGE = "找不到该总公司";

    public static final String PRODUCT_NOTFOUND_CODE = "ERR_1008";
    public static final String PRODUCT_NOTFOUND_MESSAGE = "当前委案公司下找不到该委案产品";

    public static final String OUT_BATCH_NO_NOTNULL_CODE = "ERR_1009";
    public static final String OUT_BATCH_NO_NOTNULL_MESSAGE = "委案方批次号不能为空";

    public static final String ENTRUST_TIME_ILLEGAL_CODE = "ERR_1010";
    public static final String ENTRUST_TIME_ILLEGAL_MESSAGE = "委案结束时间应大于委案开始时间";

    public static final Map<String, String> errorCodeMap = new HashMap<>();

    public static final Map<Long, Integer> tplMap = new HashMap<>();

    static {
        errorCodeMap.put("ERR_1000", "apikey不能为空");
        errorCodeMap.put("ERR_1001", "apikey不合法");
        errorCodeMap.put("ERR_1002", "未找到该模板");
        errorCodeMap.put("ERR_1003", "未找到该委案公司");
        errorCodeMap.put("ERR_1004", "传输字段与模板不符");
        errorCodeMap.put("ERR_1005", "超过传输数据范围");
        errorCodeMap.put("ERR_1006", "找不到该任务");
        errorCodeMap.put("ERR_1007", "找不到该总公司");
        errorCodeMap.put("ERR_1008", "当前委案公司下找不到该委案产品");
        errorCodeMap.put("ERR_1009", "委案方批次号不能为空");
        errorCodeMap.put("ERR_1010", "委案结束时间应大于委案开始时间");
        // 模版
        tplMap.put(13L, CaseTemplateEnums.TempType.CASE_UPDATE_IMPORT.getCode());
        tplMap.put(14L, CaseTemplateEnums.TempType.CASE_BASE_IMPORT.getCode());
        tplMap.put(18L, CaseTemplateEnums.TempType.CONTACTS_IMPORT.getCode());
        tplMap.put(19L, CaseTemplateEnums.TempType.CASE_BASE_IMPORT.getCode());
        tplMap.put(20L, CaseTemplateEnums.TempType.CASE_BASE_IMPORT.getCode());
        tplMap.put(21L, CaseTemplateEnums.TempType.CASE_BASE_IMPORT.getCode());
        tplMap.put(22L, CaseTemplateEnums.TempType.OPERATION_IMPORT.getCode());
    }
}
