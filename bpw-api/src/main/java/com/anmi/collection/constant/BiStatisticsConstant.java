package com.anmi.collection.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/22 09:59
 */
public interface BiStatisticsConstant {

    /**
     * 按催员统计
     */
    int STATISTICS_USER = 0;
    /**
     * 按小组统计
     */
    int STATISTICS_TEAM = 1;
    /**
     * 按分公司统计
     */
    int STATISTICS_DEP = 2;
    /**
     * 按委案方统计
     */
    int STATISTICS_DELT = 3;
    /**
     * 按委案方批次号统计
     */
    int STATISTICS_BATCH = 4;
    /**
     * 按委案产品统计
     */
    int STATISTICS_PRODUCT = 5;

    /**
     * 点呼（坐席外呼）
     */
    int CALL_CENTER_CALL = 0;
    /**
     * 机器人外呼
     */
    int CALL_CENTER_ROBOT = 1;
    /**
     * 预测式外呼
     */
    int CALL_CENTER_FIFO = 2;
}
