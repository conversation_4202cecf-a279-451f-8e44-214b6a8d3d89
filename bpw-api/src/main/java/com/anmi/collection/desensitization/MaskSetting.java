package com.anmi.collection.desensitization;

import com.anmi.collection.entity.requset.sys.DesensitizationRuleCreate;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/19 20:41
 * 脱敏配置
 */
@Data
public class MaskSetting implements Serializable {

    /**
     * 脱敏总开关
     */
    private Boolean enable;

    /**
     * 催员，外访员界面信息脱敏
     */
    private Boolean userLevelSwitch;

    /**
     * 管理员界面信息脱敏
     */
    private Boolean adminLevelSwitch;

    /**
     * 绑定的需要脱敏的机构或部门id
     */
    private List<Long> depTeamIds;

    /**
     * 脱敏规则
     */
    private List<DesensitizationRuleCreate> rules;



}
