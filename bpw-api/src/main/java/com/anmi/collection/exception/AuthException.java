package com.anmi.collection.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * findUserWithCache
 */
public class AuthException extends RuntimeException {

	private static final long serialVersionUID = -6440837707819985638L;
	private static final Logger logger = LoggerFactory.getLogger(AuthException.class);
    private int code;

    public AuthException(String message) {
        super(message);
    }

    public AuthException(String message, Throwable cause) {
        super(message, cause);
    }

    public AuthException(int code) {
        this("error,code:" + code);
        this.code = code;
    }

    public AuthException(int code, String message) {
        this(message);
        this.code = code;
    }

    public int getCode() {
        return this.code;
    }

    public synchronized Throwable fillInStackTrace() {
        if (logger.isDebugEnabled()) {
            return super.fillInStackTrace();
        } else {
        	return new Throwable(this.getMessage());
        }
    }
}
