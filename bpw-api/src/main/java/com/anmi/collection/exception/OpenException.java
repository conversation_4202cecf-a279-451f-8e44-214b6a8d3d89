package com.anmi.collection.exception;

import com.anmi.collection.common.enums.OpenErrorEnums;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * open接口异常
 */
public class OpenException extends RuntimeException {

    private static final long serialVersionUID = 5377048039820953465L;

    private static final Logger logger = LoggerFactory.getLogger(BaseException.class);

    private String errorCode;

    public OpenException(OpenErrorEnums.ErrorCode errorCode) {
        this(errorCode.getCode(), errorCode.getMessage());
    }

    public OpenException(String msg) {
        this(OpenErrorEnums.ErrorCode.OTHER_ERROR_CODE.getCode(), msg);
    }

    public OpenException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public OpenException(Throwable cause) {
        super(cause);
    }

    public OpenException(String status, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = status;
    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public void setErrorCode(String status) {
        this.errorCode = status;
    }

    public String getMsg() {
        return getMessage();
    }

    @Override
    public synchronized Throwable fillInStackTrace() {
        if (logger.isDebugEnabled()) {
            return super.fillInStackTrace();
        } else {
            return new Throwable(this.getMessage());
        }
    }

}
