package com.anmi.collection.entity.response.principal;

import com.anmi.collection.entity.response.Base;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel(description = "产品")
public class ProductVO extends Base
{
    @ApiModelProperty(value = "产品名称")
    private String name;
    @ApiModelProperty(value = "委托方id")
    private Long orgDeltId;
    @ApiModelProperty(value = "委托方名称")
    private String orgDeltName;
    @ApiModelProperty(value = "类型")
    private Integer type;
}
