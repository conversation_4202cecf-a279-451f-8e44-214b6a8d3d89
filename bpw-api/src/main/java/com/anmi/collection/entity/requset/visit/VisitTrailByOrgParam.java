package com.anmi.collection.entity.requset.visit;

import com.anmi.collection.entity.requset.Base;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> */
@ApiModel(description = "外访轨迹")
@Getter
@Setter
public class VisitTrailByOrgParam extends Base {
  @ApiModelProperty(value = "外访员ids")
  private String userIds;
  @ApiModelProperty(value = "机构类型  1：内催  2：委外")
  private Integer allotAgent;

}
