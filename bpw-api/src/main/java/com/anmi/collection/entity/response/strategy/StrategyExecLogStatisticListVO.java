package com.anmi.collection.entity.response.strategy;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StrategyExecLogStatisticListVO {
  /** 节点id */
  private String nodeId;
  /** 属性名称 */
  private String attrName;
  /**属性值**/
  private String attrValue;
  /** 总数量 */
  private Integer allCnt;
  /** 匹配成功数量 */
  private Integer successCnt;
  /** 匹配失败数量 */
  private Integer failCnt;

  private Integer type;
  private Integer isSys;

}
