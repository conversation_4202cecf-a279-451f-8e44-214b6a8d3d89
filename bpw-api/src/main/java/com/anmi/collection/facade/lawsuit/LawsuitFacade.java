package com.anmi.collection.facade.lawsuit;

import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.entity.requset.lawsuit.*;
import com.anmi.collection.entity.requset.query.lawsuit.LawsuitFileQuery;
import com.anmi.collection.entity.requset.query.lawsuit.LawsuitPayQuery;
import com.anmi.collection.entity.requset.query.lawsuit.LawsuitQuery;
import com.anmi.collection.entity.requset.sys.DownloadTaskQuery;
import com.anmi.collection.entity.response.cases.DownloadTaskVO;
import com.anmi.collection.entity.response.lawsuit.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/21 09:57
 */
@Api(tags = "诉讼案件管理")
@RequestMapping(value = "/v1/lawsuit", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public interface LawsuitFacade {

    @PostMapping("/list")
    @ApiOperation("诉讼列表")
    ResultMessage<PageOutput<LawsuitVO>> getList(@RequestBody LawsuitQuery query);

    @GetMapping("/{id}")
    @ApiOperation("诉讼案件详情")
    ResultMessage<LawsuitDetailVO> getLawsuitDetail(@PathVariable("id") Long id);

    @PostMapping("/process")
    @ApiOperation("添加诉讼进度")
    ResultMessage addLawsuitProcess(@RequestBody LawsuitProcessParam param);

    @PostMapping("/note")
    @ApiOperation("添加案件备忘")
    ResultMessage addLawsuitNote(@RequestBody LawsuitNoteParam param);

    @GetMapping("/note")
    @ApiOperation("案件备忘列表")
    ResultMessage<List<LawsuitNoteVO>> getLawsuitNoteList(@RequestParam("lawsuitId") Long lawsuitId);

    @PostMapping("/update/import")
    @ApiOperation("导入Excel文件更新诉讼")
    ResultMessage updateLawsuit(@RequestParam("file") MultipartFile file) throws IOException;

    @PostMapping("/export")
    @ApiOperation("导出诉讼列表")
    ResultMessage exportLawsuit(@RequestBody LawsuitQuery query);

    @GetMapping("/handler")
    @ApiOperation("查询办理记录")
    ResultMessage<PageOutput<LawsuitHandlerVO>> queryLawsuitHandlerList(@Validated LawsuitRecordParam query) throws Exception;

    @PostMapping("/handler/add")
    @ApiOperation("添加办理记录")
    ResultMessage addLawsuitHandler(@Validated @RequestBody LawsuitHandlerAddParam lawsuitHandlerAddParam);

    @PutMapping("/handler")
    @ApiOperation("修改办理记录")
    ResultMessage updateLawsuitHandler(@Validated @RequestBody LawsuitHandlerUpdateParam lawsuitHandlerUpdateParam);

    @DeleteMapping("/handler/{id}")
    @ApiOperation("删除办理记录")
    ResultMessage deleteLawsuitHandler(@PathVariable("id") Long id);

    @ApiOperation("收费记录列表")
    @GetMapping("/pay")
    ResultMessage<PageOutput<LawsuitPayVO>> queryLawsuitPayList(@Validated LawsuitRecordParam query) throws Exception;

    @ApiOperation("根据诉讼案件获取收费记录总和")
    @GetMapping("/paySum/{lawsuitId}")
    ResultMessage<BigDecimal> queryLawsuitPaySumById(@PathVariable("lawsuitId") Long lawsuitId);

    @ApiOperation("添加收费记录")
    @PostMapping("/pay/add")
    ResultMessage addLawsuitPay(@Validated @RequestBody LawsuitPayParam param);

    @PutMapping("/pay")
    @ApiOperation("修改收费记录")
    ResultMessage updateLawsuitPay(@Validated @RequestBody LawsuitPayParam param);

    @ApiOperation("删除收费记录")
    @DeleteMapping("/pay/{id}")
    ResultMessage deleteLawsuitPay(@PathVariable("id") Long id);

    @ApiOperation("查封资产记录")
    @GetMapping("/asset")
    ResultMessage<PageOutput<LawsuitAssetVO>> queryLawsuitAssetList(@Validated LawsuitRecordParam param) throws Exception;

    @ApiOperation("添加资产信息")
    @PostMapping("/asset/add")
    ResultMessage addLawsuitAsset(@RequestBody LawsuitAssetParam param) throws Exception;

    @ApiOperation("修改资产信息")
    @PutMapping("/asset")
    ResultMessage updateLawsuitAsset(@RequestBody LawsuitAssetParam param) throws Exception;

    @DeleteMapping("/asset/{id}")
    @ApiOperation("删除资产信息")
    ResultMessage deleteLawsuitAsset(@PathVariable("id") Long id);

    @ApiOperation("材料文件列表")
    @GetMapping("/file")
    ResultMessage<PageOutput<LawsuitFileVO>> queryLawsuitFileList(@Validated LawsuitRecordParam param) throws Exception;

    @PostMapping("/file/add")
    @ApiOperation("上传诉讼材料文件")
    ResultMessage addLawsuitFile(@Validated @ModelAttribute LawsuitFileParam param, @RequestParam(name = "files") MultipartFile[] files);

    @DeleteMapping("/file/{id}")
    @ApiOperation("删除诉讼材料文件(单个)")
    ResultMessage deleteLawsuitFile(@PathVariable("id") Long id);

    @ApiOperation("诉讼文件管理")
    @PostMapping("/file/list")
    ResultMessage<PageOutput<LawsuitFileVO>> queryLawsuitFileManager(@RequestBody LawsuitFileQuery query) throws Exception;

    @ApiOperation("批量删除诉讼文件")
    @DeleteMapping("/file/batch")
    ResultMessage deleteBatchLawsuitFile(@RequestBody LawsuitFileDeleteParam param);

    @ApiOperation("导出诉讼文件")
    @PostMapping("/exportLawsuitFile")
    ResultMessage exportLawsuitFile(@RequestBody LawsuitFileQuery query);

    @ApiOperation("更新诉讼案件状态或进度")
    @PutMapping
    ResultMessage updateLawsuit(@RequestBody LawsuitUpdateParam param);

    @ApiOperation("更新诉讼案件详情")
    @PutMapping("/{id}")
    ResultMessage updateLawsuitById(@RequestBody LawsuitUpdateEmptyParam param, @PathVariable("id") Long id) throws NoSuchFieldException, IllegalAccessException;

    @ApiOperation("诉讼案件导出列表接口")
    @GetMapping("/downloadTask")
    ResultMessage<PageOutput<DownloadTaskVO>> getExportList(DownloadTaskQuery query);

    @ApiOperation("诉讼文件删除操作记录")
    @GetMapping("/file/deleteRecord")
    ResultMessage<PageOutput<LawsuitAsyncTaskVO>> getLawsuitFileRecord(LawsuitAsyncTaskQuery query);

    @ApiOperation("甲方法诉对账列表")
    @PostMapping("/pay/list")
    ResultMessage<PageOutput<LawsuitPayVO>> getLawsuitPayList(@RequestBody LawsuitPayQuery query);

    @ApiOperation("甲方法诉对账列表导出")
    @PostMapping("/pay/export")
    ResultMessage exportLawsuitPay(@RequestBody LawsuitPayQuery query);



}
