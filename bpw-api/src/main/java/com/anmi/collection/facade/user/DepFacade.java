package com.anmi.collection.facade.user;

import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.entity.requset.sys.dep.*;
import com.anmi.collection.entity.response.sys.dep.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/** Created by dongwang on 2018-12-02. */
@Api(tags = "部门团队管理")
@RequestMapping(value = "/v1/dep", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public interface DepFacade {

  @ApiOperation("添加部门")
  @RequestMapping(value = "", method = RequestMethod.POST)
  @ResponseBody
  ResultMessage add(@Validated @RequestBody DepOperate create);


  @ApiOperation("添加部门文件")
  @RequestMapping(value = "/uploadDepFile", method = RequestMethod.POST)
  @ResponseBody
  ResultMessage<Long> uploadDepFile(@RequestParam(name = "file") MultipartFile file,@RequestParam(name = "depId",required = false) Long depId);

  @ApiOperation("删除部门文件")
  @RequestMapping(value = "/deleteDepFile/{fileId}", method = RequestMethod.DELETE)
  @ResponseBody
  ResultMessage<?> deleteDepFile(@PathVariable("fileId") Long fileId);

  @ApiOperation("修改部门")
  @RequestMapping(
      value = "",
      method = {RequestMethod.PUT})
  @ResponseBody
  ResultMessage update(@Validated @RequestBody DepUpdate update);

  @ApiOperation("删除部门，有子团队或者子员工时无法删除")
  @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
  @ResponseBody
  ResultMessage delete(@ApiParam(value = "id") @PathVariable Long id);

  @ApiOperation("部门详情")
  @RequestMapping(value = "details/{id}", method = RequestMethod.GET)
  @ResponseBody
  ResultMessage<DepVO> details(@ApiParam(value = "部门编号") @PathVariable Long id);

  @ApiOperation("单表分页")
  @RequestMapping(value = "", method = RequestMethod.GET)
  @ResponseBody
  ResultMessage<PageOutput<DepVO>> getList(
      @ModelAttribute @Validated DepParam param, @ModelAttribute @Validated PageParam pageParam);

  @ApiOperation("获取Map")
  @RequestMapping(value = "map/", method = RequestMethod.GET)
  @ResponseBody
  ResultMessage<Map<Long, String>> maps();

  @ApiOperation("总公司小组列表")
  @RequestMapping(value = "/teamList", method = RequestMethod.GET)
  @ResponseBody
  ResultMessage<List<DepVO>> getTeamList() throws Exception;

  @ApiOperation("分公司、直属小组列表")
  @RequestMapping(value = "/depTeamList", method = RequestMethod.GET)
  @ResponseBody
  ResultMessage<List<DepVO>> getDepTeamList() throws Exception;

  @ApiOperation("查询委外机构列表")
  @RequestMapping(value = "/agentList", method = RequestMethod.GET)
  @ResponseBody
  ResultMessage<PageOutput<AgentVO>> getAgentList(@ModelAttribute @Validated AgentParam param)  throws Exception;

  @ApiOperation("银行委外机构列表")
  @RequestMapping(value = "/bankAgentList", method = RequestMethod.GET)
  @ResponseBody
  ResultMessage<PageOutput<BankAgentVO>> getBankAgentList(@ModelAttribute @Validated BankAgentParam param);

  @ApiOperation("设置团队编号")
  @PutMapping("/teamNo/{depId}")
  @ResponseBody
  ResultMessage<?> updateTeamNo(@ApiParam(value = "分公司id") @PathVariable("depId") Long depId,  String teamNo);

  @ApiOperation("设置团队编号")
  @PostMapping("/teamNo")
  @ResponseBody
  ResultMessage<?> updateTeamNoList(@RequestBody List<DepNoParam> depNoParams);

  @ApiOperation("查询分公司/委外机构列表")
  @GetMapping("/depList")
  @ResponseBody
  ResultMessage<List<DepTeamVO>> getDepList();

  @ApiOperation("查询分公司/委外机构列表")
  @GetMapping("/allDepTreeList")
  @ResponseBody
  ResultMessage<OrgDepTeamTreeVO> getAllDepTreeList() throws Exception;

  @ApiOperation("查询未配置全局管控的委案公司")
  @GetMapping("/notRelTeam")
  @ResponseBody
  ResultMessage<List<DepTeamVO>> getNotRelCtrlTeam();

  @ApiOperation("新增自定义账龄")
  @PostMapping("/addAccountAge")
  @ResponseBody
  ResultMessage<?> addAccountAge(@Validated @RequestBody AccountAgeAddParam param);

  @ApiOperation("查询自定义账龄")
  @GetMapping("/accountAgeList/{depId}")
  ResultMessage<List<String>> queryAccountAgeList(@PathVariable("depId") Long depId);
}
