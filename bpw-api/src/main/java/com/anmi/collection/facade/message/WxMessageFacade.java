package com.anmi.collection.facade.message;

import com.anmi.collection.common.ResultMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Api(tags = "微信平台管理")
@RequestMapping(value = "/v1/wx", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public interface WxMessageFacade {
    @ApiOperation("微信发送")
    @PostMapping(value = "/send")
    @ResponseBody
    ResultMessage<?> sendMessage(String message);
}
