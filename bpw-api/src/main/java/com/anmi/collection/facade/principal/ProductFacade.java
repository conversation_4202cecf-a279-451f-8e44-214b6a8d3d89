package com.anmi.collection.facade.principal;

import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.entity.requset.principal.Product;
import com.anmi.collection.entity.requset.principal.ProductParam;
import com.anmi.collection.entity.response.principal.ProductVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Api(tags = "委托方产品")
@RequestMapping(value = "/v1/delt/product", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public interface ProductFacade
{
    @ApiOperation("添加委托方产品")
    @RequestMapping(method = RequestMethod.POST)
    @ResponseBody
    ResultMessage add(@RequestBody Product product);

    @ApiOperation("编辑委托方产品")
    @RequestMapping(method = RequestMethod.PUT)
    @ResponseBody
    ResultMessage update(@RequestBody Product product);

    @ApiOperation("删除委托方产品")
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @ResponseBody
    ResultMessage delete(@ApiParam(value = "委托方产品id") @PathVariable Long id);

    @ApiOperation("委托方产品列表")
    @RequestMapping(method = RequestMethod.GET)
    @ResponseBody
    ResultMessage<PageOutput<ProductVO>> list(@ModelAttribute ProductParam productParam);

    @ApiOperation("委托方产品详情")
    @RequestMapping(value = "details/{id}", method = RequestMethod.GET)
    @ResponseBody
    ResultMessage<ProductVO> details(@ApiParam(value = "委托方产品id") @PathVariable Long id);
}
