package com.anmi.collection.facade.lawsuit;

import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.entity.requset.lawsuit.LawsuitConfigParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@Api(tags = "诉讼自定义配置")
@RequestMapping(value = "/v1/lawsuit/config", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public interface LawsuitConfigFacade {
    @GetMapping("/lawsuitList/{type}")
    @ApiOperation("查看诉讼设置")
    ResultMessage queryLawsuitConfigList(@PathVariable("type") Integer type) throws Exception;

    @PostMapping("/add")
    @ApiOperation("添加诉讼设置")
    ResultMessage addLawsuitConfig(@RequestBody LawsuitConfigParam param) throws InterruptedException;

    @PutMapping("/update")
    @ApiOperation("修改诉讼设置")
    ResultMessage updateLawsuitConfig(@RequestBody LawsuitConfigParam param);

    @PutMapping("/updateSwitch")
    @ApiOperation("更新节点开关")
    ResultMessage updateLawsuitNodeSwitch(@RequestBody LawsuitConfigParam param);

    @PutMapping("/delete/{id}")
    @ApiOperation("删除诉讼节点")
    ResultMessage deleteLawsuitConfig(@PathVariable("id") Long id);
}
