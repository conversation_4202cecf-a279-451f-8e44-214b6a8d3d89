package com.anmi.collection.facade.message;

import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.PageParam;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.entity.requset.message.InnerMessageDetailParam;
import com.anmi.collection.entity.requset.message.InnerMessageSendParam;
import com.anmi.collection.entity.response.message.InnerMessageDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Api(tags = "站内信")
@RequestMapping(value = "/v1/inner/message", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public interface InnerMessageFacade {

    @ApiOperation("发送站内信")
    @PostMapping(value = "/send")
    @ResponseBody
    ResultMessage send(@RequestBody @Validated InnerMessageSendParam param);

    @ApiOperation("收件列表")
    @PostMapping(value = "/receive/list")
    @ResponseBody
    ResultMessage<PageOutput<InnerMessageDetailVO>> receiveList(@RequestBody @Validated PageParam param);

    @ApiOperation("发件列表")
    @PostMapping(value = "/send/list")
    @ResponseBody
    ResultMessage<PageOutput<InnerMessageDetailVO>> sendList(@RequestBody @Validated PageParam param);

    @ApiOperation("站内信详情")
    @PostMapping(value = "/detail")
    @ResponseBody
    ResultMessage<InnerMessageDetailVO> detail(@RequestBody @Validated InnerMessageDetailParam param);

    @ApiOperation("全部已读")
    @PostMapping(value = "/readAll")
    @ResponseBody
    ResultMessage readAll();
}
