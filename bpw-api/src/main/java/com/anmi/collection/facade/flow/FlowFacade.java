package com.anmi.collection.facade.flow;

import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.entity.requset.flow.*;
import com.anmi.collection.entity.response.flow.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Api(tags = "审批流")
@RequestMapping(value = "/v1/flow", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public interface FlowFacade {

    @PostMapping("/applyFormField/list")
    @ApiOperation("申请表单字段列表")
    ResultMessage<List<FlowApplyFormFieldVO>> getApplyFormFieldList(@RequestBody @Validated ApplyFormFieldListParam param);
    @PostMapping("/applyFormField/enable")
    @ApiOperation("开启/关闭自定义字段")
    ResultMessage<String> enableField(@RequestBody @Validated ApplyFormFieldEnableParam param);
    @PostMapping("/applyFormField/update")
    @ApiOperation("自定义字段更新")
    ResultMessage<String> updateField(@RequestBody @Validated ApplyFormFieldUpdateParam param);
    @PostMapping("/applyFormField/sort")
    @ApiOperation("申请表单字段排序")
    ResultMessage<String> sortField(@RequestBody @Validated List<ApplyFormFieldSortParam> sortParams);
    @PostMapping("/list")
    @ApiOperation("审批流配置列表")
    ResultMessage<List<FlowVO>> getFlowList(@RequestBody @Validated FlowListParam param);
    @PostMapping("/detail")
    @ApiOperation("审批流配置详情")
    ResultMessage<FlowVO> getFlowDetail(@RequestBody @Validated FlowDetailParam param);
    @PostMapping("/update")
    @ApiOperation("审批流配置修改")
    ResultMessage<String> updateFlow(@RequestBody @Validated FlowUpdateParam param);
    @PostMapping("/updateNodes")
    @ApiOperation("审批流节点配置修改")
    ResultMessage<String> updateNodes(@RequestBody @Validated FlowUpdateNodesParam param);
    @PostMapping("/approve/transmit")
    @ApiOperation("代办转交")
    ResultMessage<String> approveTransmit(@RequestBody @Validated ApproveTransmitParam param) throws Exception;
    @PostMapping("/apply/detail")
    @ApiOperation("申请信息详情")
    ResultMessage<ApplyDetailVO> applyDetail(@RequestBody @Validated ApplyDetailParam param);
    @PostMapping("/approve/detail")
    @ApiOperation("审批信息详情")
    ResultMessage<List<FlowNodeImageVO>> approveDetail(@RequestBody @Validated ApproveDetailParam param);
    @PostMapping("/approve/todoStatistics")
    @ApiOperation("审批待处理统计")
    ResultMessage<ApproveTodoStatisVO> todoStatistics(@RequestBody @Validated TodoStatisticsParam param);
    @PostMapping("/approve/list")
    @ApiOperation("我的审批列表")
    ResultMessage<PageOutput<ApproveListVO>> approveList(@RequestBody @Validated ApproveListParam param);
    @PostMapping("/approve/batch")
    @ApiOperation("批量审批")
    ResultMessage<String> approveBatch(@RequestBody @Validated ApproveBatchParam param) throws Exception;
}
