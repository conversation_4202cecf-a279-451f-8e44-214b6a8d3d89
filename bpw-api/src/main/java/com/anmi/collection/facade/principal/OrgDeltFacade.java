package com.anmi.collection.facade.principal;

import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.entity.requset.principal.Delt;
import com.anmi.collection.entity.requset.principal.DeltOperationParam;
import com.anmi.collection.entity.requset.principal.DeltParam;
import com.anmi.collection.entity.response.principal.DeltVO;
import com.anmi.collection.entity.response.sys.org.OrgConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "委托方")
@RequestMapping(value = "/v1/delt", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public interface OrgDeltFacade
{
    @ApiOperation("添加委托方")
    @RequestMapping(method = RequestMethod.POST)
    @ResponseBody
    ResultMessage add(@RequestBody Delt delt);

    @ApiOperation("编辑委托方")
    @RequestMapping(method = RequestMethod.PUT)
    @ResponseBody
    ResultMessage update(@RequestBody Delt delt);

    @ApiOperation("删除")
    @RequestMapping(value = "/{id}",method = RequestMethod.DELETE)
    @ResponseBody
    ResultMessage delete(@ApiParam(value = "委托方id") @PathVariable Long id);

    @ApiOperation("委托方列表")
    @RequestMapping(method = RequestMethod.GET)
    @ResponseBody
    ResultMessage<PageOutput<DeltVO>> list(@ModelAttribute DeltParam deltParam);

    @ApiOperation("所有有效的委托方列表(排除已删除、暂停合作、停止合作)")
    @RequestMapping(value ="validList",method = RequestMethod.GET)
    @ResponseBody
    ResultMessage<List<DeltVO>> selectValidDelts();

    @ApiOperation("委托方详情")
    @RequestMapping(value ="details/{id}" ,method = RequestMethod.GET)
    @ResponseBody
    ResultMessage<DeltVO> details(@ApiParam(value = "委托方id") @PathVariable Long id);

    @ApiOperation("委案公司绑定催记信息配置")
    @PostMapping("/operation")
    @ResponseBody
    ResultMessage addDeltOperation(@RequestBody @Validated DeltOperationParam param);

    @ApiOperation("查询委案公司绑定催记信息配置列表")
    @GetMapping ("/operation")
    @ResponseBody
    ResultMessage<List<OrgConfigVO>> getDeltOperationList(DeltOperationParam param);

    @ApiOperation("查询未配置全局管控的委案公司")
    @GetMapping("/notRelDelt")
    @ResponseBody
    ResultMessage<List<DeltVO>> getNotRelCtrlDelt();






}
