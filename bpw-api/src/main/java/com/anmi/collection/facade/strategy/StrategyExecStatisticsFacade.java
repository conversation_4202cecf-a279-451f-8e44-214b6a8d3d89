package com.anmi.collection.facade.strategy;

import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.entity.requset.strategy.StrategyExecStatisticsListParam;
import com.anmi.collection.entity.response.strategy.StrategyExecStatisticsListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "策略执行管理")
@RequestMapping(value = "/v1/strategyExecStatistics", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public interface StrategyExecStatisticsFacade {

  @ApiOperation("策略统计列表")
  @RequestMapping(value = "/list", method = RequestMethod.POST)
  @ResponseBody
   ResultMessage<List<StrategyExecStatisticsListVO>> list(@Valid @RequestBody StrategyExecStatisticsListParam param);
}
