package com.anmi.collection.facade.cases;

import com.anmi.collection.common.PageOutput;
import com.anmi.collection.common.ResultMessage;
import com.anmi.collection.entity.requset.cases.CaseLogParam;
import com.anmi.collection.entity.response.cases.CaseLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@Api(tags = "案件日志管理")
@RequestMapping(value = "/v1/cases/log", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public interface CaseLogFacade {
    @ApiOperation("案件日志")
    @RequestMapping(method = RequestMethod.POST)
    @ResponseBody
    ResultMessage<PageOutput<CaseLogVO>> logList(@RequestBody CaseLogParam caseLogParam);

    @ApiOperation("案件日志导出")
    @RequestMapping(path = "/export",method = RequestMethod.POST)
    @ResponseBody
    ResultMessage<?> export(@RequestBody CaseLogParam caseLogParam);
}
