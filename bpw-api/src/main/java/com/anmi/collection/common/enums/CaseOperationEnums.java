package com.anmi.collection.common.enums;

import cn.hutool.core.util.ObjectUtil;
import com.anmi.collection.utils.CodeEnum;
import lombok.Getter;
import lombok.Setter;

public class CaseOperationEnums {

  public enum ActionType {
    UNKNOWN(-4, "未知"),
    NOT_FILLED(-3, "未填写"),
    UNTREATED(-2, "未处理"),

    UNCONTACTED(-1, "未联系上"),
    // 有效联络催收结果
    PROMISE_REPAY(0, "承诺还款"),
    WANT_REPAY(1, "意愿还款"),
    CLAIM_REPAY(4, "声称已还款"),
    APPLY_REDUCTION(5, "要求减免"),
    HAVE_CLOSED(8, "已结清"),
    AMORTIZATION_LOAN(12, "分期还款"),

    // 非有效联络催收结果
    NEGOTIATION(2, "谈判中"),
    EFFECTIVE_NOTIFY(3, "已转告"),
    DENIAL(6, "否认用款"),
    NOT_ONESELF(7, "非本人"),
    CLAIM_COMPLAIN(11, "声称要投诉"),
    DEBTOR_LOSE_CONTACT(13, "债务人失联"),
    DISPUTED_CASE(14, "争议案件"),
    ENQUIRY_CASE(15, "查询案件");
    @Getter @Setter private Integer code;

    @Getter @Setter private String message;

    ActionType(Integer code, String message) {
      this.code = code;
      this.message = message;
    }

    public static String getMessageByCode(Integer code) {
      if (ObjectUtil.isNull(code)) {
        return null;
      }
      for (ActionType actionType : ActionType.values()) {
        if (actionType.code.equals(code)) {
          return actionType.message;
        }
      }
      // 如果未找到对应的code，返回null或者其他默认值
      return null;
    }

  }

  public enum State implements CodeEnum {
    UNKNOWN(-2, "未知"),
    UNCONTACTED(-1, "未联系上"),
    PROMISE_REPAY(0, "承诺还款"),
    IN_FUNDRAISING(1, "筹款中"),
    CONTACT_ONESELF(2, "联系本人中"),
    CONTACT_FAMILY(3, "联系家人中"),
    CONTACT_THIRD_PARTY(4, "联系第三方中"),
    NEGOTIATION(5, "谈判中"),
    APPLY_REDUCTION(6, "要求减免"),
    DENIAL(7, "否认用款"),
    HAVE_CLOSED(8, "已结清"),
    FOLLOW_UP(11, "重点跟进"),
    ALREADY_VISITED(9, "已外访"),
    PLAN_VISITE(10, "准备外访");

    @Getter @Setter private Integer code;

    @Getter @Setter private String message;

    State(Integer code, String message) {
      this.code = code;
      this.message = message;
    }

    public static String getMessageByCode(Integer code) {
      if (ObjectUtil.isNull(code)) {
        return null;
      }
      for (State state : State.values()) {
        if (state.code.equals(code)) {
          return state.message;
        }
      }
      // 如果未找到对应的code，返回null或者其他默认值
      return null;
    }
  }

  public enum CallType {
    UNKNOWN(-4, "未知"),
    NOT_FILLED(-3, "未填写"),
    NORMAL(0, "正常接通"),
    CALL_ERROR(8, "呼叫异常"),
    SHUTDOWN(1, "关机"),
    BUSY_LINE(2, "忙线中"),
    NO_ANSWER(3, "无人接听"),
    HANG_UP(4, "接听挂断"),
    STOP_SERVICE(5, "停机"),
    EMPTY_NUMBER(6, "空号"),
    FAX_MACHINE(7, "传真机"),
    CALL_LIMIT(9, "呼入限制"),
    WEB_SEARCH(10, "网查"),
    UNCONTACTED(-1, "未拨打");
    @Getter @Setter private Integer code;
    @Getter @Setter private String message;

    CallType(Integer code, String message) {
      this.code = code;
      this.message = message;
    }

    public static String getMessageByCode(Integer code) {
      if (ObjectUtil.isNull(code)) {
        return null;
      }
      for (CallType callType : CallType.values()) {
        if (callType.code.equals(code)) {
          return callType.message;
        }
      }
      // 如果未找到对应的code，返回null或者其他默认值
      return null;
    }
  }

  public enum Outcome {
    SUCCESS(0),
    FAIL(1),
    USER_BUSY(2),
    POWER_OFF(3),
    SUSPENDED(4),
    NOT_EXIST(5);

    @Getter @Setter private int code;

    Outcome(int code) {
      this.code = code;
    }
  }

  /**
   * 催记填写类型：0：人为呼叫 1：机器人计划 2:预测式外呼计划 3:工作手机 4:惠捷电话 5:机器人点呼
   *
   * <AUTHOR>
   * @date 2023/05/30
   */
  public enum SubmitType {
    MANUAL(0),
    ROBOT(1),
    FIFO(2),
    WORK_PHONE(3),
    WELL_PHONE(4),
    ROBOT_POINT(5);

    @Getter @Setter private int code;

    SubmitType(int code) {
      this.code = code;
    }
  }

  public enum CreateType {
    SYS(0),
    IMPORT(1);

    @Getter @Setter private Byte code;

    CreateType(Integer code) {
      this.code = code.byteValue();
    }
  }

  public enum Status {
    NORMAL(0),
    DELETE(-1),
    HANDLE(-11);
    @Getter @Setter private int code;

    Status(int code) {
      this.code = code;
    }
  }

  // ：0：呼入 1：呼出
  public enum CallStyle {
    COMEIN(0),
    OUT(1);
    @Getter @Setter private Byte code;

    CallStyle(Integer code) {
      this.code = code.byteValue();
    }
  }

  public enum IsHidden {
    NO(0),
    YES(1);
    @Getter @Setter private Byte code;

    IsHidden(Integer code) {
      this.code = code.byteValue();
    }
  }

  @Getter
  public enum DataType {
    COLD(0,"冷数据"),
    HEAT(1,"热数据");

    private Integer code;
    private String msg;

    DataType(Integer code,String msg) {
      this.code = code;
      this.msg = msg;
    }
  }
}
