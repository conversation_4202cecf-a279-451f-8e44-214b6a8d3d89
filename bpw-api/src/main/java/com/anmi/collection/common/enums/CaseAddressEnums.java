package com.anmi.collection.common.enums;

import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> */
public class CaseAddressEnums {

  // 地址类型
  public enum Type {
    FAMILY(0, "家庭地址"),
    HOUSE(1, "住宅地址"),
    COMPANY(2, "单位地址"),
    RESIDENCE(3, "户籍地址"),
    STATEMENT(4, "对账单地址"),
    OTHER(5, "其他地址");

        @Getter
        @Setter
        private Byte code;

    @Getter @Setter private String message;

        Type(Integer code, String message) {
            this.code = code.byteValue();
            this.message = message;
        }
  }

  // 地址状态
  public enum State {
    UNKNOWN(0, "未知"),
    VALID(1, "有效"),
    INVALID(-1, "无效"),
    DELETE(-2, "删除");

        @Getter
        @Setter
        private Byte code;

    @Getter @Setter private String message;

        State(Integer code, String message) {
            this.code = code.byteValue();
            this.message = message;
        }
  }

  // 地址来源
  public enum Source {
    IMPORT(0, "导入"),
    ADD(1, "添加"),
    OPEN(2, "open导入");

        @Getter
        @Setter
        private Byte code;

    @Getter @Setter private String message;

        Source(Integer code, String message) {
            this.code = code.byteValue();
            this.message = message;
        }
    }
}
