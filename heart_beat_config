{"beat_list": [{"name": "export_bpw_case_info.py", "log_path": "/usr/local/duyansoft/service/scripts/log/export_bpw_case_info.log", "service_ip": "***********2", "heart_beat": true}, {"name": "export_bpw_case_log.py", "log_path": "/usr/local/duyansoft/service/scripts/log/export_bpw_case_log.log", "service_ip": "***********", "heart_beat": true}, {"name": "export_bpw_case_operation.py", "log_path": "/usr/local/duyansoft/service/scripts/log/export_bpw_case_operation.log", "service_ip": "***********", "heart_beat": true}, {"name": "export_bpw_case_operation_voice_new.py", "log_path": "/usr/local/duyansoft/service/scripts/log/export_bpw_case_operation_voice_new.log", "service_ip": "***********", "heart_beat": true}, {"name": "export_bpw_lawsuit_file.py", "log_path": "/usr/local/duyansoft/service/scripts/log/export_bpw_lawsuit_file.log", "service_ip": "***********", "heart_beat": true}, {"name": "export_bpw_reduction.py", "log_path": "/usr/local/duyansoft/service/scripts/log/export_bpw_reduction.log", "service_ip": "***********", "heart_beat": true}, {"name": "export_bpw_repayment.py", "log_path": "/usr/local/duyansoft/service/scripts/log/export_bpw_repayment.log", "service_ip": "***********", "heart_beat": true}, {"name": "export_bpw_visit.py", "log_path": "/usr/local/duyansoft/service/scripts/log/export_bpw_visit.log", "service_ip": "***********", "heart_beat": true}, {"name": "export_bpw_visit_file.py", "log_path": "/usr/local/duyansoft/service/scripts/log/export_bpw_visit_file.log", "service_ip": "***********", "heart_beat": true}, {"name": "sync_case_info.py", "log_path": "/usr/local/duyansoft/service/scripts/log/sync_case_info.log", "service_ip": "************", "heart_beat": true}, {"name": "sync_case_log.py", "log_path": "/usr/local/duyansoft/service/scripts/log/sync_case_log.log", "service_ip": "************", "heart_beat": true}, {"name": "sync_case_operation.py", "log_path": "/usr/local/duyansoft/service/scripts/log/sync_case_operation.log", "service_ip": "************", "heart_beat": true}, {"name": "sync_other.py", "log_path": "/usr/local/duyansoft/service/scripts/log/sync_other.log", "service_ip": "************", "heart_beat": true}]}