package com.anmi.domain.cases;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CommissionCaseDetail {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Long id;
    /** 公司id */
    private Long orgId;
    /** 部门id */
    private Long depId;
    /** 委案方批次号 */
    private Long outBatchId;
    /** 案件id */
    private Long caseId;
    /** 配置id */
    private Long configId;
    /** 逾期天数 */
    private Integer overdueDays;
    /** 还款金额 */
    private BigDecimal repayAmount;
    /** 最近还款时间 */
    private Date lastestRepayTime;
    /** 对账状态 0：待对账 1：已对账 */
    private Integer checkStatus;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;
}
