import schedule
import time
import os
import re

tmpPath = '/usr/local/duyansoft/ng/download/downloadFile'

def rmJob():
	dirs = os.listdir(tmpPath)
	for file in dirs:
		match = re.match( r'(\d{4})-(\d{2})-(\d{2})$', file)

		#确认时间格式文件 
		if match:
			timeArray = time.strptime(file, "%Y-%m-%d")
			timeStamp = int(time.mktime(timeArray))
			gap = time.time() - timeStamp

			#如果大于3天
			if gap > 3 * 24 * 3600:
				removeFile = tmpPath + '/' + file
				os.system("rm -rf " + removeFile)

# 

if __name__ == '__main__':
	schedule.every().day.at("00:00").do(rmJob)

	while True:
		schedule.run_pending()
		time.sleep(10)