{"name": "anmi-parta-web", "version": "2.0.0", "author": "xsc", "private": false, "scripts": {"dev": "vite serve --mode user", "opendev": "vite serve --mode open", "build": "node --max_old_space_size=4096 ./node_modules/vite/bin/vite.js build --mode user", "buildOpen": "node --max_old_space_size=4096 ./node_modules/vite/bin/vite.js build --mode open"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/g6": "^4.6.4", "@antv/layout": "^0.3.25", "@antv/x6": "^2.13.1", "@antv/x6-vue-shape": "^2.1.2", "@vue/compat": "^3.2.36", "axios": "^0.18.0", "countup": "^1.8.2", "cron-parser": "^4.4.0", "crypto-js": "^4.2.0", "dayjs": "^1.7.7", "debounce": "^1.2.1", "dy-vite-vue-pdf": "^2.0.2", "echarts": "^4.0.4", "idcard": "^4.1.0", "js-cookie": "^2.2.0", "mqtt": "^4.3.7", "pinyin-pro": "^3.16.1", "sortablejs": "^1.7.0", "trtc-js-sdk": "^4.15.8", "view-ui-plus": "^1.3.15", "vue": "^3.2.36", "vue-clipboard3": "^2.0.0", "vue-i18n": "^9.2.2", "vue-property-decorator": "^9.1.2", "vue-router": "4.0.13", "vuedraggable": "^4.1.0", "vuex": "^4.0.0-0", "vxe-table": "^4.5.9", "wangeditor": "4.6.3", "wavesurfer.js": "^5.1.0", "xe-utils": "^3.5.2", "xlsx": "^0.13.3"}, "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@vitejs/plugin-legacy": "^1.8.2", "@vitejs/plugin-vue": "^2.3.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/babel-preset-app": "^5.0.8", "@vue/compiler-core": "^3.2.36", "@vue/compiler-sfc": "^3.2.36", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "^1.0.0-beta.10", "add-asset-html-webpack-plugin": "^3.1.3", "babel-eslint": "^10.0.1", "chai": "^4.1.2", "clean-webpack-plugin": "^3.0.0", "eslint": "^5.10.0", "eslint-config-standard": "^12.0.0", "eslint-plugin-cypress": "^2.0.1", "eslint-plugin-vue": "^5.0.0", "extract-text-webpack-plugin": "^4.0.0-beta.0", "husky": "^1.3.0", "less": "^2.7.3", "less-loader": "^4.0.5", "lint-staged": "^6.0.0", "sass": "1.52.1", "sass-loader": "7.1.0", "style-loader": "^1.2.1", "vite": "^4.4.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^0.13.3"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}