#!/usr/bin/python
# -*- coding: UTF-8 -*-
import time
import json
import os
import openpyxl
import requests
from openpyxl.cell.cell import ILLEGAL_CHARACTERS_RE
from NacosHelper import NacosHelper
from utils import base_utils

from utils.base_utils import init_base_conf, init_heartbeat

config = NacosHelper().init_config()
logger, fileUtil, ossSwitch, esSwitch, encryptSwitch, encryptMode, bucket, mysql_pool, taskUtil, redis_pool = init_base_conf("export_bpw_visit_file", config)

fieldDictLanguage = {
    "fieldDict": {"0": {"name": "文件名", "value": "file_name"},
                  "1": {"name": "外访日期", "value": "visit_date"},
                  "2": {"name": "委案编号", "value": "out_serial_no"},
                  "3": {"name": "委案公司", "value": "org_delt_name"},
                  "4": {"name": "委案产品", "value": "product_name"},
                  "5": {"name": "最近修改时间", "value": "update_time"},
                  "6": {"name": "操作人", "value": "user_name"}
                  },
    "fieldDict_tj": {"0": {"name": "文件名", "value": "file_name"},
                     "1": {"name": "外访日期", "value": "visit_date"},
                     "2": {"name": "案件编号", "value": "out_serial_no"},
                     "3": {"name": "申请方", "value": "org_delt_name"},
                     "4": {"name": "申请产品", "value": "product_name"},
                     "5": {"name": "最近修改时间", "value": "update_time"},
                     "6": {"name": "操作人", "value": "user_name"}
                     }
}

selectFiledSql = " vf.file_name,DATE_FORMAT(v.visit_time,'%%Y-%%m-%%d') AS visit_date,vf.out_serial_temp AS out_serial_no,delt.name AS org_delt_name,p.name AS product_name,DATE_FORMAT(vf.update_time,'%%Y-%%m-%%d %%H:%%i:%%s') as update_time" \
                 ",u.name as user_name, vf.url "

selectTableSql = " visit_file vf LEFT JOIN visit v ON vf.visit_id = v.id LEFT JOIN case_info ca ON v.case_id = ca.id LEFT JOIN org_delt delt ON delt.id = vf.org_delt_id LEFT JOIN product p ON p.id = ca.product_id LEFT JOIN `user` u ON u.id = " \
                 "vf.user_id "


def doTasks():
    tasks = taskUtil.getUndoTasks(8)
    for task in tasks or []:
        handleTask(task)


def handleTask(task):
    orgId, data, taskId = task["org_id"], task["data"], task["id"]
    language = taskUtil.getOrgLanguage(orgId)
    logger.info("执行任务id:" + str(taskId))
    taskUtil.updateTaskIng(taskId)
    dataJson = json.loads(data)

    queryParaMap = ()
    selectWhereSql = " vf.org_id=%s "
    queryParaMap += (dataJson.get('orgId'),)
    # 分公司
    if dataJson.get('depId'):
        selectWhereSql += " and v.dep_id=%s "
        queryParaMap += (dataJson.get('depId'),)
    # 分公司
    if dataJson.get('teamId'):
        selectWhereSql += " and v.team_id=%s "
        queryParaMap += (dataJson.get('teamId'),)
    if dataJson.get('allotAgent'):
        selectWhereSql += " and v.allot_agent=%s "
        queryParaMap += (dataJson.get('allotAgent'),)
    if dataJson.get('suffix') and len(dataJson.get('suffix')) > 0:
        selectWhereSql += " and SUBSTRING_INDEX(vf.file_name,'.',-1) in %s "
        queryParaMap += (dataJson.get('suffix'),)
    if dataJson.get('notInSuffix') and len(dataJson.get('notInSuffix')) > 0:
        selectWhereSql += " and SUBSTRING_INDEX(vf.file_name,'.',-1) not in %s "
        queryParaMap += (dataJson.get('notInSuffix'),)
    if dataJson.get('deltIds') and dataJson.get('deltIds') != '':
        selectWhereSql += " and vf.org_delt_id in %s "
        queryParaMap += (dataJson.get('deltIds').split(","),)
    if dataJson.get('depIds') and dataJson.get('depIds') != '':
        selectWhereSql += " and v.dep_id in %s "
        queryParaMap += (dataJson.get('depIds').split(","),)
    if dataJson.get('teamIds') and dataJson.get('teamIds') != '':
        selectWhereSql += " and v.team_id in %s "
        queryParaMap += (dataJson.get('teamIds').split(","),)
    if dataJson.get('visitDateStart'):
        selectWhereSql += " and v.visit_time >= date(FROM_UNIXTIME(%s/1000)) "
        queryParaMap += (dataJson.get('visitDateStart'),)
    if dataJson.get('visitDateEnd'):
        selectWhereSql += " and v.visit_time <= FROM_UNIXTIME(%s/1000) "
        queryParaMap += (dataJson.get('visitDateEnd'),)
    if dataJson.get('fileName') and dataJson.get('fileName') != '':
        selectWhereSql += " and vf.file_name like concat('%%', %s, '%%') "
        queryParaMap += (dataJson.get('fileName'),)
    if dataJson.get('outSerialNos'):
        if len(dataJson.get('outSerialNos')) == 1:
            selectWhereSql += " and vf.out_serial_temp like concat('%%', %s, '%%') "
            queryParaMap += (dataJson.get('outSerialNos')[0],)
        else:
            selectWhereSql += " and vf.out_serial_temp in %s "
            queryParaMap += (dataJson.get('outSerialNos'),)
    if dataJson.get('orderBy') is None and dataJson.get('sortRule') is None:
        selectWhereSql += " order by v.visit_time desc "
    if dataJson.get('orderBy') and dataJson.get('orderBy') == 1:
        selectWhereSql += " order by vf.update_time "
        if dataJson.get('sortRule') and dataJson.get('sortRule') == 2:
            selectWhereSql += " desc "
    selectCountSql = "select count(1) as cnt from " + selectTableSql + " where " + selectWhereSql
    # 总数
    resultCount = mysql_pool.select_by_param(selectCountSql, queryParaMap, )[0]["cnt"]
    selectSql = "select " + selectFiledSql + " from " + selectTableSql + " where " + selectWhereSql
    if resultCount and resultCount > 0:
        exportExcel(selectSql, queryParaMap, resultCount, taskId, language)
    else:
        logger.warning("任务id:" + str(taskId) + "查询外访记录为空")
        taskUtil.updateFail(taskId, "无外访信息")


def exportExcel(sql, sqlParam, allCount, taskId, language):
    sheetNum = 1
    wb = openpyxl.Workbook(write_only=True)
    tmpFilePath = "./visit_file_export_" + str(taskId) + "/"
    os.makedirs(name=tmpFilePath, exist_ok=True)
    # 如果存在目录，先清空
    fileUtil.delete(tmpFilePath)
    visitFilePath = tmpFilePath + "file/"
    os.makedirs(name=visitFilePath, exist_ok=True)
    sheet = createNewSheet(wb, sheetNum, language)
    maxRow = 1000002
    logger.info("表格最大行数:" + str(maxRow))
    # 分页
    limit = 500
    pageCount = int(allCount / limit)
    # 填写表格
    num = 2
    name = {}
    for page in range(0, pageCount + 1):
        results = selectVisitFileForLimit(sql, sqlParam, page, limit)
        if results is None:
            break
        resultSize = len(results)
        logger.info("第%s页数据是%s" % (page + 1, resultSize))
        if num + resultSize > maxRow:
            sheetNum = sheetNum + 1
            sheet = createNewSheet(wb, sheetNum, language)
            num = 2
            logger.info("创建新的sheet%s" % str(sheetNum))
        for row in range(0, resultSize):
            origin_file_name = str(results[row]["file_name"])[str(results[row]["file_name"]).rfind("/") + 1:]
            fileName = visitFilePath
            if name.get(origin_file_name) is None:
                name[origin_file_name] = 0
                fileName = fileName + origin_file_name
            else:
                name[origin_file_name] = name[origin_file_name] + 1
                pos = origin_file_name.rfind(".")
                if pos == -1:
                    fileName = fileName + "(" + str(name[origin_file_name]) + ")"
                else:
                    fileName = fileName + origin_file_name[0:pos] + "(" + str(name[origin_file_name]) + ")" + origin_file_name[pos:]
            data = getExcelRowData(results[row], name, language)
            sheet.append(data)
            try:
                # 本地化附件导出
                if ossSwitch:
                    # sass附件导出
                    urlFile = requests.get(results[row]["url"])
                    if urlFile.status_code == 200:
                        open(fileName, 'wb').write(urlFile.content)
                else:
                    recordPath = "/usr/local/duyansoft" + str(results[row]["url"])
                    fileUtil.exec(f"cp '{recordPath}' '{fileName}'")
            except Exception:
                logger.warning("文件下载失败:" + results[row]["url"])

        progress = page / (pageCount + 1) * 100
        taskUtil.updateProgress(taskId, progress)

        num = num + resultSize
    doSave(wb, allCount, taskId, tmpFilePath)


def doSave(wbk, dataNums, taskId, tmpFilePath):
    logger.info("表格已生成，保存中")
    dateStr = time.strftime('%Y-%m-%d', time.localtime(int(time.time())))
    excelFileName = tmpFilePath + "外访附件_" + str(dateStr) + "_" + str(taskId) + ".xlsx"
    wbk.save(excelFileName)
    zipName = "外访附件导出_%s" % taskId + ".zip"
    fileUtil.zip(zipName, tmpFilePath)
    fileUtil.delete(tmpFilePath)
    downloadurl, fileSize = base_utils.upload_file(ossSwitch, bucket, fileUtil, zipName)
    logger.info("下载地址:" + downloadurl)
    taskUtil.updateSuccess(taskId, downloadurl, dataNums, fileSize)


def selectVisitFileForLimit(sql, sqlParam, page, limit):
    start = page * limit
    limitSql = sql + " limit %s,%s" % (start, limit)
    logger.debug("执行查询数据" + limitSql)
    result = mysql_pool.select_by_param(limitSql, sqlParam)
    return result


def createNewSheet(wb, sheetNum, language):
    sheet = wb.create_sheet("外访附件" + str(sheetNum))
    # 创建表头
    columnCount = len(getFieldDictLanguage(language))
    data = []
    for i in range(0, columnCount):
        data.append(getFieldDictLanguage(language)[str(i)].get("name"))
    sheet.append(data)
    return sheet


def getExcelRowData(dataResult, file_name, language):
    data = []
    for i in range(len(getFieldDictLanguage(language))):
        field = getFieldDictLanguage(language)[str(i)]["value"]
        dataTmp = dataResult.get(field)
        if dataTmp is None:
            data.append("")
            continue
        if type(dataTmp).__name__ == 'unicode':
            dataTmp = dataTmp.encode("utf-8")
        else:
            dataTmp = str(dataTmp)
        dataTmp = ILLEGAL_CHARACTERS_RE.sub(r'', dataTmp)
        if field == "file_name":
            dataTmp = dataTmp[dataTmp.rfind("/") + 1:]
            if file_name.get(dataTmp) > 0:
                pos = dataTmp.rfind(".")
                if pos == -1:
                    dataTmp = dataTmp + "(" + str(file_name[dataTmp]) + ")"
                else:
                    dataTmp = dataTmp[0:pos] + "(" + str(file_name[dataTmp]) + ")" + dataTmp[pos:]
        data.append(dataTmp)
    return data


def getFieldDictLanguage(language):
    key = "fieldDict"
    if language:
        key = key + "_" + language
    return fieldDictLanguage[key]


if __name__ == '__main__':
    init_heartbeat("export_visit_file", logger)
    while True:
        try:
            doTasks()
        except Exception as e:
            logger.exception(e)
        # 休息10秒
        time.sleep(10)
